{"ast": null, "code": "import createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is greater than or equal to `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than or equal to\n *  `other`, else `false`.\n * @see _.lte\n * @example\n *\n * _.gte(3, 1);\n * // => true\n *\n * _.gte(3, 3);\n * // => true\n *\n * _.gte(1, 3);\n * // => false\n */\nvar gte = createRelationalOperation(function (value, other) {\n  return value >= other;\n});\nexport default gte;", "map": {"version": 3, "names": ["createRelationalOperation", "gte", "value", "other"], "sources": ["/home/<USER>/other/digi/digitorywebv4/node_modules/lodash-es/gte.js"], "sourcesContent": ["import createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is greater than or equal to `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than or equal to\n *  `other`, else `false`.\n * @see _.lte\n * @example\n *\n * _.gte(3, 1);\n * // => true\n *\n * _.gte(3, 3);\n * // => true\n *\n * _.gte(1, 3);\n * // => false\n */\nvar gte = createRelationalOperation(function(value, other) {\n  return value >= other;\n});\n\nexport default gte;\n"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,iCAAiC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAGD,yBAAyB,CAAC,UAASE,KAAK,EAAEC,KAAK,EAAE;EACzD,OAAOD,KAAK,IAAIC,KAAK;AACvB,CAAC,CAAC;AAEF,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}