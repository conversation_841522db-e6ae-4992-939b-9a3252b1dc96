{"ast": null, "code": "import { BehaviorSubject, map, startWith } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./responsive-service.service\";\nlet DashboardMenuService = /*#__PURE__*/(() => {\n  class DashboardMenuService {\n    constructor(responsiveService) {\n      this.responsiveService = responsiveService;\n      this.showMenu$ = new BehaviorSubject(true);\n      this.isSmallDevice = false;\n      this.responsiveService.isSmallDevice$.subscribe(isSmall => {\n        if (isSmall) {\n          this.isSmallDevice = true;\n          this.showMenu$.next(false);\n        } else {\n          this.isSmallDevice = false;\n          this.showMenu$.next(true);\n        }\n      });\n    }\n    toggleMenu() {\n      const showing = this.showMenu$.value;\n      this.showMenu$.next(!showing);\n    }\n    get sidenavType$() {\n      return this.responsiveService.isSmallDevice$.pipe(map(isSmall => {\n        if (isSmall) {\n          return 'over';\n        }\n        return 'side';\n      }));\n    }\n    get showSidenav$() {\n      return this.showMenu$.asObservable();\n    }\n    get showSmallDeviceMenuButton$() {\n      return this.responsiveService.isSmallDevice$.pipe(startWith(false));\n    }\n    toggleSidenavOnSmallDevice() {\n      if (this.isSmallDevice) {\n        this.toggleMenu();\n      }\n    }\n    static {\n      this.ɵfac = function DashboardMenuService_Factory(t) {\n        return new (t || DashboardMenuService)(i0.ɵɵinject(i1.ResponsiveService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DashboardMenuService,\n        factory: DashboardMenuService.ɵfac\n      });\n    }\n  }\n  return DashboardMenuService;\n})();\nexport { DashboardMenuService };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}