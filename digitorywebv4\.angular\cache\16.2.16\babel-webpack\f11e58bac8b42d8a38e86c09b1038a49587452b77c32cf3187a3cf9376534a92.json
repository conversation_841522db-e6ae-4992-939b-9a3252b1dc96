{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/radio\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/card\";\nimport * as i18 from \"@angular/material/progress-bar\";\nimport * as i19 from \"@angular/material/tabs\";\nfunction AccountSetupComponent_mat_error_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"apps\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_error_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 56);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"div\", 66)(3, \"h3\")(4, \"mat-icon\", 67);\n    i0.ɵɵtext(5, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 68)(14, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_122_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Get Started \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_4_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 80);\n    i0.ɵɵtext(1, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 81);\n    i0.ɵɵtext(3, \"Chat Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_4_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 80);\n    i0.ɵɵtext(1, \"dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 81);\n    i0.ɵɵtext(3, \"Generate Datasets\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"mat-tab-group\", 72);\n    i0.ɵɵlistener(\"selectedIndexChange\", function AccountSetupComponent_mat_card_122_div_4_Template_mat_tab_group_selectedIndexChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.selectedAITab = $event);\n    });\n    i0.ɵɵelementStart(3, \"mat-tab\");\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_122_div_4_ng_template_4_Template, 4, 0, \"ng-template\", 73);\n    i0.ɵɵelementStart(5, \"div\", 74)(6, \"p\", 75);\n    i0.ɵɵtext(7, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"app-chat-bot\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-tab\");\n    i0.ɵɵtemplate(10, AccountSetupComponent_mat_card_122_div_4_ng_template_10_Template, 4, 0, \"ng-template\", 73);\n    i0.ɵɵelementStart(11, \"div\", 77)(12, \"div\", 78)(13, \"h3\")(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" AI-Powered Dataset Generation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Our AI will analyze your restaurant information to create optimized inventory and packaging datasets.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" This process takes approximately 15 minutes to complete.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_122_div_4_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.startAIProcessing());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"play_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Generate Datasets \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selectedIndex\", ctx_r11.selectedAITab);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"tenantId\", ctx_r11.registrationForm.value.tenantId)(\"tenantName\", ctx_r11.registrationForm.value.tenantName);\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r22.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 102);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_122_div_5_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_122_div_5_div_16_div_3_Template, 3, 0, \"div\", 46);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 99)(6, \"div\", 100);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 101);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r26 = ctx.$implicit;\n    const i_r27 = ctx.index;\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, step_r26.completed, ctx_r25.activeStep === i_r27, !step_r26.completed && ctx_r25.activeStep !== i_r27));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r26.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r26.completed && ctx_r25.activeStep === i_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r26.completed && ctx_r25.activeStep !== i_r27);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r26.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r26.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"h3\", 83)(2, \"mat-icon\", 84);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 85);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 86);\n    i0.ɵɵelementStart(7, \"div\", 87);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 88)(10, \"mat-icon\", 89);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_122_div_5_span_12_Template, 3, 4, \"span\", 19);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_122_div_5_span_13_Template, 2, 0, \"span\", 19);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_122_div_5_span_14_Template, 2, 0, \"span\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 91);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_122_div_5_div_16_Template, 10, 10, \"div\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 93)(18, \"div\", 94)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 95);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r12.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining > 0 && ctx_r12.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104)(2, \"mat-icon\", 105);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 106);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 107)(9, \"div\", 108)(10, \"mat-card\", 109)(11, \"mat-card-header\")(12, \"div\", 110)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-card-title\");\n    i0.ɵɵtext(16, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-subtitle\");\n    i0.ɵɵtext(18, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_122_div_6_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.downloadInventory());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Download \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 108)(25, \"mat-card\", 109)(26, \"mat-card-header\")(27, \"div\", 112)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-card-title\");\n    i0.ɵɵtext(31, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-card-subtitle\");\n    i0.ɵɵtext(33, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card-actions\")(35, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_122_div_6_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.downloadPackaging());\n    });\n    i0.ɵɵelementStart(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Download \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114)(2, \"mat-icon\", 115);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 116);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 117)(9, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_122_div_7_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 57)(1, \"div\", 58);\n    i0.ɵɵtext(2, \"AI-Powered Data Generation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_122_div_3_Template, 18, 0, \"div\", 59);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_122_div_4_Template, 27, 3, \"div\", 60);\n    i0.ɵɵtemplate(5, AccountSetupComponent_mat_card_122_div_5_Template, 25, 6, \"div\", 61);\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_122_div_6_Template, 39, 0, \"div\", 62);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_122_div_7_Template, 13, 0, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed && !ctx_r9.showChatBot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showChatBot && !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadFailed);\n  }\n}\nconst _c1 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c2 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.selectedTabIndex = 0;\n    this.tenantCreated = false;\n    this.aiDataAvailable = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // AI Data Generation properties\n    this.showChatBot = false;\n    this.selectedAITab = 0; // 0 = Chat Agent tab, 1 = Dataset tab\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  onTabChange(index) {\n    this.selectedTabIndex = index;\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.selectedAITab = 0; // Select the Chat Agent tab\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  // Switch between Chat Agent and Dataset tabs\n  switchAITab(tabIndex) {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 123,\n      vars: 19,\n      consts: [[1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"status-header\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\", 1, \"compact-radio\"], [\"value\", \"no\", \"color\", \"primary\", 1, \"compact-radio\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-header\"], [1, \"logo-container\"], [1, \"logo-preview-container\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-placeholder\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"text-center\", \"p-2\", \"my-2\", \"bottomTitles\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"ai-intro-panel\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-8\"], [1, \"ai-icon\"], [1, \"col-md-4\", \"text-center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-ai-btn\", 3, \"click\"], [1, \"chat-bot-section\"], [1, \"ai-data-tabs\"], [\"animationDuration\", \"300ms\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"mat-tab-label\", \"\"], [1, \"tab-content\"], [1, \"chat-bot-description\"], [3, \"tenantId\", \"tenantName\"], [1, \"tab-content\", \"dataset-tab-content\"], [1, \"dataset-info\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-btn\", 3, \"click\"], [1, \"tab-icon\"], [1, \"tab-label\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"row\", \"download-options\"], [1, \"col-md-6\", \"mb-3\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r36 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 6)(16, \"mat-icon\", 4);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_21_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"mat-card\", 10)(27, \"mat-card-content\")(28, \"form\", 11)(29, \"h3\", 12);\n          i0.ɵɵtext(30, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"div\", 14)(33, \"mat-form-field\", 15)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 16);\n          i0.ɵɵelementStart(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 15)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 18);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_42_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-icon\", 17);\n          i0.ɵɵtext(44, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, AccountSetupComponent_mat_error_45_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 15)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 20);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_49_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-icon\", 17);\n          i0.ɵɵtext(51, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, AccountSetupComponent_mat_error_52_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 14)(54, \"mat-form-field\", 15)(55, \"mat-label\");\n          i0.ɵɵtext(56, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_57_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-icon\", 17);\n          i0.ɵɵtext(59, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, AccountSetupComponent_mat_error_60_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 15)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 22);\n          i0.ɵɵelementStart(65, \"mat-icon\", 17);\n          i0.ɵɵtext(66, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"mat-form-field\", 15)(68, \"mat-label\");\n          i0.ɵɵtext(69, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 23);\n          i0.ɵɵelementStart(71, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_71_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(72, \"mat-icon\");\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26)(76, \"div\", 27)(77, \"div\", 28)(78, \"h4\", 29);\n          i0.ɵɵtext(79, \"Account Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 30)(81, \"div\", 31)(82, \"label\", 32);\n          i0.ɵɵtext(83, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"mat-radio-group\", 33)(85, \"mat-radio-button\", 34);\n          i0.ɵɵtext(86, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-radio-button\", 35);\n          i0.ɵɵtext(88, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(89, \"div\", 31)(90, \"label\", 32);\n          i0.ɵɵtext(91, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"mat-radio-group\", 36)(93, \"mat-radio-button\", 34);\n          i0.ɵɵtext(94, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-radio-button\", 35);\n          i0.ɵɵtext(96, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(97, \"div\", 31)(98, \"label\", 32);\n          i0.ɵɵtext(99, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"mat-radio-group\", 37)(101, \"mat-radio-button\", 34);\n          i0.ɵɵtext(102, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"mat-radio-button\", 35);\n          i0.ɵɵtext(104, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(105, \"div\", 38)(106, \"div\", 39)(107, \"h4\", 29);\n          i0.ɵɵtext(108, \"Company Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 40)(110, \"div\", 41);\n          i0.ɵɵtemplate(111, AccountSetupComponent_div_111_Template, 2, 1, \"div\", 42);\n          i0.ɵɵtemplate(112, AccountSetupComponent_div_112_Template, 3, 0, \"div\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"div\", 44)(114, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_114_listener() {\n            i0.ɵɵrestoreView(_r36);\n            const _r7 = i0.ɵɵreference(120);\n            return i0.ɵɵresetView(_r7.click());\n          });\n          i0.ɵɵtemplate(115, AccountSetupComponent_mat_icon_115_Template, 2, 0, \"mat-icon\", 19);\n          i0.ɵɵtemplate(116, AccountSetupComponent_div_116_Template, 3, 0, \"div\", 46);\n          i0.ɵɵelementStart(117, \"span\");\n          i0.ɵɵtext(118, \"Upload Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"input\", 47, 48);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_119_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(121, AccountSetupComponent_mat_error_121_Template, 2, 0, \"mat-error\", 49);\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵtemplate(122, AccountSetupComponent_mat_card_122_Template, 8, 5, \"mat-card\", 50);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c2));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(38);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i15.MatRadioGroup, i15.MatRadioButton, MatButtonModule, i16.MatButton, i16.MatIconButton, MatCardModule, i17.MatCard, i17.MatCardActions, i17.MatCardAvatar, i17.MatCardContent, i17.MatCardHeader, i17.MatCardSubtitle, i17.MatCardTitle, MatSelectModule, MatProgressBarModule, i18.MatProgressBar, MatTabsModule, i19.MatTabLabel, i19.MatTab, i19.MatTabGroup, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 6px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  align-items: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 2;\\n  min-width: 250px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 8px;\\n  border-bottom: 1px solid #eee;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 0;\\n  color: #555;\\n  padding-bottom: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 2px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label {\\n  margin: 0;\\n  padding: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-container {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-outer-circle, .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-inner-circle {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label-content {\\n  padding-left: 4px;\\n  font-size: 13px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%] {\\n  border: 1px dashed #ccc;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  background-color: #f9f9f9;\\n  margin-bottom: 6px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #aaa;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 30px;\\n  width: 30px;\\n  height: 30px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  width: 100%;\\n  max-width: 140px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n  width: 100%;\\n  padding: 2px 8px;\\n  min-height: 32px;\\n  line-height: 1;\\n  font-size: 12px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  box-sizing: border-box;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 4px;\\n  text-align: center;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n\\n.ai-data-tabs[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n  .chat-bot-section .mat-mdc-tab-header {\\n  background-color: #f8a055; \\n\\n  border-radius: 8px 8px 0 0;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab {\\n  min-width: 160px;\\n  padding: 0 16px;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab-label-container {\\n  padding: 0 16px;\\n}\\n\\n  .chat-bot-section .mdc-tab__text-label,   .chat-bot-section .mat-mdc-tab .mdc-tab-indicator__content--underline {\\n  color: rgba(255, 255, 255, 0.7) !important;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {\\n  color: white !important;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron {\\n  border-color: white;\\n}\\n\\n.tab-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.tab-label[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background-color: white;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n  background-color: #f5f5f5;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 550px;\\n}\\n\\n\\n\\n.dataset-tab-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  min-height: 550px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  text-align: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n  color: #f8a055; \\n\\n  font-size: 24px;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #f8a055; \\n\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  color: #555;\\n}\\n\\n.generate-btn[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "MatTabsModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_122_div_3_Template_button_click_14_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "AccountSetupComponent_mat_card_122_div_4_Template_mat_tab_group_selectedIndexChange_2_listener", "$event", "_r20", "ctx_r19", "selectedAITab", "ɵɵtemplate", "AccountSetupComponent_mat_card_122_div_4_ng_template_4_Template", "AccountSetupComponent_mat_card_122_div_4_ng_template_10_Template", "AccountSetupComponent_mat_card_122_div_4_Template_button_click_23_listener", "ctx_r21", "startAIProcessing", "ctx_r11", "registrationForm", "value", "tenantId", "tenantName", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r22", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_122_div_5_div_16_div_3_Template", "AccountSetupComponent_mat_card_122_div_5_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c0", "step_r26", "completed", "ctx_r25", "activeStep", "i_r27", "ɵɵtextInterpolate", "name", "description", "AccountSetupComponent_mat_card_122_div_5_span_12_Template", "AccountSetupComponent_mat_card_122_div_5_span_13_Template", "AccountSetupComponent_mat_card_122_div_5_span_14_Template", "AccountSetupComponent_mat_card_122_div_5_div_16_Template", "ctx_r12", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_122_div_6_Template_button_click_20_listener", "_r32", "ctx_r31", "downloadInventory", "AccountSetupComponent_mat_card_122_div_6_Template_button_click_35_listener", "ctx_r33", "downloadPackaging", "AccountSetupComponent_mat_card_122_div_7_Template_button_click_9_listener", "_r35", "ctx_r34", "AccountSetupComponent_mat_card_122_div_3_Template", "AccountSetupComponent_mat_card_122_div_4_Template", "AccountSetupComponent_mat_card_122_div_5_Template", "AccountSetupComponent_mat_card_122_div_6_Template", "AccountSetupComponent_mat_card_122_div_7_Template", "ctx_r9", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "selectedTabIndex", "tenantCreated", "aiDataAvailable", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "onTabChange", "index", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "switchAITab", "tabIndex", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_21_listener", "AccountSetupComponent_Template_input_keyup_42_listener", "AccountSetupComponent_mat_error_45_Template", "AccountSetupComponent_Template_input_keyup_49_listener", "AccountSetupComponent_mat_error_52_Template", "AccountSetupComponent_Template_input_keyup_57_listener", "AccountSetupComponent_mat_error_60_Template", "AccountSetupComponent_Template_button_click_71_listener", "AccountSetupComponent_div_111_Template", "AccountSetupComponent_div_112_Template", "AccountSetupComponent_Template_button_click_114_listener", "_r36", "_r7", "ɵɵreference", "click", "AccountSetupComponent_mat_icon_115_Template", "AccountSetupComponent_div_116_Template", "AccountSetupComponent_Template_input_change_119_listener", "AccountSetupComponent_mat_error_121_Template", "AccountSetupComponent_mat_card_122_Template", "ɵɵpureFunction0", "_c1", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i15", "MatRadioGroup", "MatRadioButton", "i16", "MatButton", "MatIconButton", "i17", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i18", "MatProgressBar", "i19", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    MatTabsModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n  selectedTabIndex: number = 0;\n  tenantCreated: boolean = false;\n  aiDataAvailable: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // AI Data Generation properties\n  showChatBot: boolean = false;\n  selectedAITab: number = 0; // 0 = Chat Agent tab, 1 = Dataset tab\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  onTabChange(index: number) {\n    this.selectedTabIndex = index;\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.selectedAITab = 0; // Select the Chat Agent tab\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  // Switch between Chat Agent and Dataset tabs\n  switchAITab(tabIndex: number): void {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n          <h3 class=\"form-section-title\">Account Information</h3>\n          <div class=\"compact-form-grid\">\n            <!-- First row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant Name</mat-label>\n                <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                <mat-icon matSuffix>business</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant ID</mat-label>\n                <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                <mat-icon matSuffix>fingerprint</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                  Tenant ID already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Account Number</mat-label>\n                <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                <mat-icon matSuffix>account_balance</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                  Account number already exists\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Second row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>G-Sheet</mat-label>\n                <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                  (keyup)=\"checkGSheet($event)\">\n                <mat-icon matSuffix>table_chart</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                  G-Sheet number already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Email</mat-label>\n                <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                <mat-icon matSuffix>email</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Password</mat-label>\n                <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                  [type]=\"hidePassword ? 'password' : 'text'\" />\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n              </mat-form-field>\n            </div>\n\n            <!-- Settings section with two-column layout -->\n            <div class=\"settings-section\">\n              <div class=\"two-column-grid\">\n                <!-- Left column: Status options -->\n                <div class=\"left-column\">\n                  <div class=\"status-header\">\n                    <h4 class=\"section-label\">Account Status</h4>\n                  </div>\n                  <div class=\"status-options\">\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Account</label>\n                      <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Active</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Inactive</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Forecast</label>\n                      <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Sales</label>\n                      <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Right column: Logo upload -->\n                <div class=\"right-column\">\n                  <div class=\"logo-header\">\n                    <h4 class=\"section-label\">Company Logo</h4>\n                  </div>\n                  <div class=\"logo-container\">\n                    <div class=\"logo-preview-container\">\n                      <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                        <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                      </div>\n                      <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                        <mat-icon>apps</mat-icon>\n                      </div>\n                    </div>\n                    <div class=\"logo-actions\">\n                      <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                        <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                        <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                          <span class=\"sr-only\">Loading...</span>\n                        </div>\n                        <span>Upload Logo</span>\n                      </button>\n                      <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n                      <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                        Please upload a logo\n                      </mat-error>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- AI Data Download Section - Shows after tenant creation -->\n    <mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n      <div class=\"text-center p-2 my-2 bottomTitles\">AI-Powered Data Generation</div>\n\n      <!-- Initial state - before starting process -->\n      <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed && !showChatBot\" class=\"ai-intro-panel\">\n        <div class=\"row align-items-center\">\n          <div class=\"col-md-8\">\n            <h3><mat-icon class=\"ai-icon\">auto_awesome</mat-icon> Generate AI-Powered Datasets</h3>\n            <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will\n              analyze your business needs and create personalized recommendations.</p>\n            <p><strong>Note:</strong> This process takes approximately 15 minutes to complete. You can continue using\n              the system while processing runs in the background.</p>\n          </div>\n          <div class=\"col-md-4 text-center\">\n            <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"start-ai-btn\">\n              <mat-icon>play_arrow</mat-icon>\n              Get Started\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Data Generation Section with Tabs -->\n      <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n        <div class=\"ai-data-tabs\">\n          <mat-tab-group [(selectedIndex)]=\"selectedAITab\" animationDuration=\"300ms\">\n            <!-- Chat Agent Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">chat</mat-icon>\n                <span class=\"tab-label\">Chat Agent</span>\n              </ng-template>\n\n              <div class=\"tab-content\">\n                <p class=\"chat-bot-description\">\n                  Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n                </p>\n\n                <app-chat-bot [tenantId]=\"registrationForm.value.tenantId\"\n                  [tenantName]=\"registrationForm.value.tenantName\">\n                </app-chat-bot>\n              </div>\n            </mat-tab>\n\n            <!-- Dataset Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">dataset</mat-icon>\n                <span class=\"tab-label\">Generate Datasets</span>\n              </ng-template>\n\n              <div class=\"tab-content dataset-tab-content\">\n                <div class=\"dataset-info\">\n                  <h3><mat-icon>auto_awesome</mat-icon> AI-Powered Dataset Generation</h3>\n                  <p>Our AI will analyze your restaurant information to create optimized inventory and packaging datasets.</p>\n                  <p><strong>Note:</strong> This process takes approximately 15 minutes to complete.</p>\n\n                  <button mat-raised-button color=\"primary\" (click)=\"startAIProcessing()\" class=\"generate-btn\">\n                    <mat-icon>play_circle</mat-icon>\n                    Generate Datasets\n                  </button>\n                </div>\n              </div>\n            </mat-tab>\n          </mat-tab-group>\n        </div>\n      </div>\n\n      <!-- Processing state -->\n      <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n        <h3 class=\"processing-title\">\n          <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n          Processing Your Data\n        </h3>\n\n        <!-- Progress indicator -->\n        <div class=\"progress-container\">\n          <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n          <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n        </div>\n\n        <!-- Estimated time -->\n        <div class=\"estimated-time\">\n          <mat-icon class=\"icon\">access_time</mat-icon>\n          <span *ngIf=\"estimatedTimeRemaining > 60\">\n            Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n            Estimated time remaining: less than a minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n            Calculating...\n          </span>\n        </div>\n\n        <!-- Processing steps -->\n        <div class=\"processing-steps\">\n          <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n            [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n            <div class=\"step-status\">\n              <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n              <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                <span class=\"visually-hidden\">Loading...</span>\n              </div>\n              <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n            </div>\n\n            <div class=\"step-details\">\n              <div class=\"step-name\">{{step.name}}</div>\n              <div class=\"step-description\">{{step.description}}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Helpful tips section -->\n        <div class=\"tips-section\">\n          <div class=\"tip-header\">\n            <mat-icon>lightbulb</mat-icon>\n            <span>Did You Know?</span>\n          </div>\n          <div class=\"tip-content\">\n            AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n            insights\n          </div>\n        </div>\n      </div>\n\n      <!-- Download complete state -->\n      <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n        <div class=\"success-header\">\n          <mat-icon class=\"success-icon\">task_alt</mat-icon>\n          <h3>Processing Complete!</h3>\n        </div>\n\n        <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.\n        </p>\n\n        <div class=\"row download-options\">\n          <!-- Inventory Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"inventory-icon\">\n                  <mat-icon>inventory_2</mat-icon>\n                </div>\n                <mat-card-title>Inventory Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n\n          <!-- Packaging Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"packaging-icon\">\n                  <mat-icon>category</mat-icon>\n                </div>\n                <mat-card-title>Packaging Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n              </mat-card-header>\n\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n        </div>\n      </div>\n\n      <!-- Error state -->\n      <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n        <div class=\"error-header\">\n          <mat-icon class=\"error-icon\">error_outline</mat-icon>\n          <h3>Processing Failed</h3>\n        </div>\n\n        <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n          load or connection issues.</p>\n\n        <div class=\"error-actions\">\n          <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\">\n            <mat-icon>refresh</mat-icon> Try Again\n          </button>\n        </div>\n      </div>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICsBZC,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA6DNH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAEtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKzBH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7DH,EAAA,CAAAC,cAAA,cAAuF;IAC/DD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IAgB5BH,EAAA,CAAAC,cAAA,cAA2G;IAGvED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvFH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLACmE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,4IAC2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EAAA,CAAAC,cAAA,eAAkC;IACUD,EAAA,CAAAU,UAAA,mBAAAC,2EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYLH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBzCH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAzB1DH,EAAA,CAAAC,cAAA,cAA4G;IAEzFD,EAAA,CAAAU,UAAA,iCAAAQ,+FAAAC,MAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAK,OAAA,CAAAC,aAAA,GAAAH,MAAA;IAAA,EAAiC;IAE9CnB,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAuB,UAAA,IAAAC,+DAAA,0BAGc;IAEdxB,EAAA,CAAAC,cAAA,cAAyB;IAErBD,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAI,SAAA,uBAEe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAuB,UAAA,KAAAE,gEAAA,0BAGc;IAEdzB,EAAA,CAAAC,cAAA,eAA6C;IAE3BD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,sCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxEH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6GAAqG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5GH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iEAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEtFH,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAU,UAAA,mBAAAgB,2EAAA;MAAA1B,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAO,OAAA,GAAA3B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAW,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrE5B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAnCFH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,kBAAAuB,OAAA,CAAAP,aAAA,CAAiC;IAa5BtB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAM,UAAA,aAAAuB,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;IA8ChEjC,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkC,kBAAA,gCAAAlC,EAAA,CAAAmC,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACArC,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,cAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,cACuI;IAGnID,EAAA,CAAAuB,UAAA,IAAAe,mEAAA,uBAAwD;IACxDtC,EAAA,CAAAuB,UAAA,IAAAgB,8DAAA,kBAEM;IACNvC,EAAA,CAAAuB,UAAA,IAAAiB,mEAAA,uBAAuF;IACzFxC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvH/C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAqC,QAAA,CAAAC,SAAA,CAAoB;IACzB5C,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAqC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpC/C,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAqC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7B/C,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAgD,iBAAA,CAAAL,QAAA,CAAAM,IAAA,CAAa;IACNjD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgD,iBAAA,CAAAL,QAAA,CAAAO,WAAA,CAAoB;;;;;IAzC1DlD,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAuB,UAAA,KAAA4B,yDAAA,mBAEO;IACPnD,EAAA,CAAAuB,UAAA,KAAA6B,yDAAA,mBAEO;IACPpD,EAAA,CAAAuB,UAAA,KAAA8B,yDAAA,mBAEO;IACTrD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAuB,UAAA,KAAA+B,wDAAA,oBAeM;IACRtD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAiD,OAAA,CAAAC,gBAAA,CAA0B;IACnCxD,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAkC,kBAAA,KAAAqB,OAAA,CAAAC,gBAAA,eAA8B;IAMnDxD,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAiD,OAAA,CAAAlB,sBAAA,MAAiC;IAGjCrC,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAiD,OAAA,CAAAlB,sBAAA,QAAAkB,OAAA,CAAAlB,sBAAA,OAAgE;IAGhErC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAiD,OAAA,CAAAlB,sBAAA,OAAkC;IAOnBrC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAiD,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5CzD,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,6FAC3B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,eAAkC;IAMdD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAgD,2EAAA;MAAA1D,EAAA,CAAAY,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA4C,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrE7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,gBAA2B;IAITD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAoD,2EAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAA+C,IAAA;MAAA,MAAAI,OAAA,GAAA/D,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA+C,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEhE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQnBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAAuD,0EAAA;MAAAjE,EAAA,CAAAY,aAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmD,OAAA,CAAAlD,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA/LfH,EAAA,CAAAC,cAAA,mBAA2D;IACVD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/EH,EAAA,CAAAuB,UAAA,IAAA6C,iDAAA,mBAgBM;IAGNpE,EAAA,CAAAuB,UAAA,IAAA8C,iDAAA,mBA2CM;IAGNrE,EAAA,CAAAuB,UAAA,IAAA+C,iDAAA,mBAyDM;IAGNtE,EAAA,CAAAuB,UAAA,IAAAgD,iDAAA,mBA+CM;IAGNvE,EAAA,CAAAuB,UAAA,IAAAiD,iDAAA,mBAcM;IACRxE,EAAA,CAAAG,YAAA,EAAW;;;;IA9LHH,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAM,UAAA,UAAAmE,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAI,WAAA,CAA4E;IAmB5E7E,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAAmE,MAAA,CAAAI,WAAA,KAAAJ,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,CAA2E;IA8C3E5E,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAAmE,MAAA,CAAAC,aAAA,CAAmB;IA4DnB1E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAmE,MAAA,CAAAE,gBAAA,CAAsB;IAkDtB3E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAmE,MAAA,CAAAG,cAAA,CAAoB;;;;;;;;;ADzThC,MAwBaE,qBAAqB;EAwChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IAlDpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAzF,OAAO,GAAkB,IAAI;IAC7B,KAAA0F,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAGhC,KAAA7C,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA8C,gBAAgB,GAAY,IAAI;IAChC,KAAA7B,aAAa,GAAY,KAAK;IAC9B,KAAAlB,gBAAgB,GAAW,CAAC;IAC5B,KAAAmB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA9B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAwC,WAAW,GAAY,KAAK;IAC5B,KAAAvD,aAAa,GAAW,CAAC,CAAC,CAAC;IAkBzB,IAAI,CAACkF,IAAI,GAAG,IAAI,CAAChB,IAAI,CAACiB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACtB,UAAU,CAACuB,WAAW,EAAE,CAAC5E,KAAK;IAEnD;IACA,IAAI,IAAI,CAAC2D,UAAU,EAAE;MACnB,IAAI,CAACkB,WAAW,GAAG,IAAI,CAAClB,UAAU,CAACmB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC9E,gBAAgB,GAAG,IAAI,CAACqD,EAAE,CAAC2B,KAAK,CAAC;MACpC9E,QAAQ,EAAE,IAAItD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAE;MAC3D9E,UAAU,EAAE,IAAIvD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAItI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAIvI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAIxI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIzI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAI1I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAI3I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAI5I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAI7I,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN;IACA,IAAI,CAACrB,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACmB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACV,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACsB,WAAW,CAAC,IAAI,CAAC/B,UAAU,CAACgC,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAACxC,KAAK,CAACyC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAAC,CAAC;UAExB;UACA,IAAI,CAACZ,GAAG,CAACuC,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAAC/B,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAAC8C,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAACnC,UAAU,GAAG,KAAK,CAAC,CAAC;cACzBoC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAACjD,MAAM,CAAC8C,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAACpG,gBAAgB,CAAC0G,UAAU,CAAC;MAC/BvG,UAAU,EAAEiG,IAAI,CAACjG,UAAU;MAC3BD,QAAQ,EAAEkG,IAAI,CAAClG,QAAQ;MACvBgF,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAC/G,OAAO,GAAG0H,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAACtB,aAAa,GAAG,CAAC;QACpB0C,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAAC9B,EAAE,CAACmD,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAC5D,MAAM,CAACmD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAAChH,gBAAgB,CAACiH,OAAO,EAAE;MACjC,IAAI,CAACjH,gBAAgB,CAACkH,gBAAgB,EAAE;MACxC,IAAI,CAAC3D,MAAM,CAAC8C,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAACnH,gBAAgB,CAACC,KAAK;MACxC,IAAImH,GAAG,GAAQ;QACTlH,QAAQ,EAAEiH,MAAM,CAACjH,QAAQ;QACzBC,UAAU,EAAEgH,MAAM,CAAChH,UAAU;QAC7BiF,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAACtB,aAAa,CAACkD,MAAM,GAAG,CAAC,GAAG,IAAI,CAAClD,aAAa,CAAC,CAAC,CAAC,CAAC0C,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACpD,GAAG,CAAC6D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAAC5C,MAAM,CAACgE,mBAAmB,CAAC,IAAI,CAAClD,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAACE,aAAa,GAAG,IAAI,CAAC,CAAC;YAC3B,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;YAC7B,IAAI,CAACrB,MAAM,CAACmD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAC9C,iBAAiB,CAACgE,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAACjE,MAAM,CAAC8C,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACrD,gBAAgB,GAAGqD,KAAK;EAC/B;EAEAC,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAImG,IAAI,GAAG,IAAI,CAAC9C,UAAU,CAACuB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC/H,QAAQ,KAAM2H,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAImG,IAAI,GAAG,IAAI,CAAC9C,UAAU,CAACuB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7C,SAAS,KAAMyC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAImG,IAAI,GAAG,IAAI,CAAC9C,UAAU,CAACuB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9C,MAAM,KAAM0C,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACtE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9ByE,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACrL,OAAO,GAAGoL,MAAM;YACrB,IAAI,CAAC9J,gBAAgB,CAAC0G,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAC/G;YAAO,CAAE,CAAC;YACxD,IAAI,CAACyF,aAAa,CAAC6F,IAAI,CAAC;cAAEnD,GAAG,EAAEiD;YAAM,CAAE,CAAC;YACxC,IAAI,CAAC7F,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACmD,aAAa,EAAE;UACzB,CAAC;UACDsC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACzI,aAAa,CAACiH,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACvJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEC7B,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACa,gBAAgB,CAACiH,OAAO,EAAE;MACjC,IAAI,CAAC1D,MAAM,CAAC8C,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACtD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACvD,aAAa,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACmE,EAAE,CAACmD,aAAa,EAAE;IAEvB;IACAwD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5K,iBAAiBA,CAAA;IACf,IAAI,CAAC8C,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACnB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACsC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACsH,UAAU,EAAE;IACjB,MAAMlK,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACuD,GAAG,CAACkH,eAAe,CAACzK,QAAQ,CAAC,CAAC0K,IAAI,CACrChN,UAAU,CAACiN,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAO/M,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAAC+H,SAAS,CAAEiF,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC5E,OAAO,EAAE;QAChC,IAAI,CAAC6E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAAC/K,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAAC4K,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEA;EACAI,WAAWA,CAACC,QAAgB;IAC1B,IAAI,CAAC3L,aAAa,GAAG2L,QAAQ;IAC7B,IAAI,CAACxH,EAAE,CAACmD,aAAa,EAAE;EACzB;EAEAmE,kBAAkBA,CAAC/K,QAAgB;IACjC,IAAI,CAACkL,aAAa,GAAGpN,QAAQ,CAAC,KAAK,CAAC,CAAC4M,IAAI,CACvC/M,SAAS,CAAC,MAAM,IAAI,CAAC4F,GAAG,CAAC4H,SAAS,CAACnL,QAAQ,CAAC,CAAC,EAC7CpC,SAAS,CAAEiN,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAACpE,MAAM,KAAK,UAAU,IAAIoE,QAAQ,CAACpE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAEiF,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAACpE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACmE,mBAAmB,CAACC,QAAQ,CAACO,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAAC5J,gBAAgB,GAAGqJ,QAAQ,CAACQ,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAAChL,sBAAsB,GAAGwK,QAAQ,CAACS,wBAAwB,IAAI,CAAC;MAEpE,IAAIT,QAAQ,CAACU,WAAW,KAAKC,SAAS,IAAIX,QAAQ,CAACU,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACzK,UAAU,GAAG+J,QAAQ,CAACU,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIZ,QAAQ,CAACU,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAAChK,aAAa,CAAC0F,MAAM,EAAE;YACjC,IAAI,CAAC1F,aAAa,CAACgK,CAAC,CAAC,CAAC7K,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAAC6C,EAAE,CAACmD,aAAa,EAAE;MAEvB,IAAIiE,QAAQ,CAACpE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAACiF,gBAAgB,EAAE;;IAE3B,CAAC,EAAEf,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAc,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAACjJ,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACc,EAAE,CAACmD,aAAa,EAAE;IACvB,IAAI,CAACjD,QAAQ,CAACiI,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAnB,mBAAmBA,CAACQ,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAACjJ,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACe,QAAQ,CAACiI,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAACpG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC3G,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGA4C,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,GAAG,CAAC0I,YAAY,CAAC,WAAW,EAAE,IAAI,CAACnM,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAAC4F,SAAS,CAC/EsG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGxO,IAAI,CAACyO,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EjC,IAAI,CAAC2O,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOpG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC1C,QAAQ,CAACiI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAxF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC1C,QAAQ,CAACiI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGA7J,iBAAiBA,CAAA;IACf,IAAI,CAACuB,GAAG,CAAC0I,YAAY,CAAC,SAAS,EAAE,IAAI,CAACnM,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAAC4F,SAAS,CAC7EsG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGxO,IAAI,CAACyO,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EjC,IAAI,CAAC2O,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOpG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC1C,QAAQ,CAACiI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAxF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC1C,QAAQ,CAACiI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBA1bW7I,qBAAqB,EAAA9E,EAAA,CAAA4O,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA9O,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhP,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAjP,EAAA,CAAA4O,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAnP,EAAA,CAAA4O,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAArP,EAAA,CAAA4O,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAvP,EAAA,CAAA4O,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAzP,EAAA,CAAA4O,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAA3P,EAAA,CAAA4O,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAA4O,iBAAA,CAAA5O,EAAA,CAAA8P,iBAAA,GAAA9P,EAAA,CAAA4O,iBAAA,CAmDV3P,eAAe,MAAAe,EAAA,CAAA4O,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAAhQ,EAAA,CAAA4O,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAnD1BpL,qBAAqB;MAAAqL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArQ,EAAA,CAAAsQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAArF,GAAA;QAAA,IAAAqF,EAAA;;UCpDlC5Q,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAmQ,wDAAA;YAAA,OAAStF,GAAA,CAAAzC,IAAA,EAAM;UAAA,EAAC;UACtB9I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA6B;UAIUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAoQ,uDAAA3P,MAAA;YAAA,OAASoK,GAAA,CAAA7B,aAAA,CAAAvI,MAAA,CAAqB;UAAA,EAAC;UADjFnB,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAuB,UAAA,KAAAwP,2CAAA,wBAEY;UACd/Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAsQ,uDAAA7P,MAAA;YAAA,OAASoK,GAAA,CAAArB,cAAA,CAAA/I,MAAA,CAAsB;UAAA,EAAC;UADlFnB,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAuB,UAAA,KAAA0P,2CAAA,wBAEY;UACdjR,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAwQ,uDAAA/P,MAAA;YAAA,OAASoK,GAAA,CAAApB,WAAA,CAAAhJ,MAAA,CAAmB;UAAA,EAAC;UAD/BnB,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAuB,UAAA,KAAA4P,2CAAA,wBAEY;UACdnR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAA0Q,wDAAA;YAAA,OAAA7F,GAAA,CAAArF,YAAA,IAAAqF,GAAA,CAAArF,YAAA;UAAA,EAAsC;UACtElG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UAKID,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UACvCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC7FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UACzCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACnCD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,6BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOtGH,EAAA,CAAAC,cAAA,gBAA0B;UAEID,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,gBAA4B;UAExBD,EAAA,CAAAuB,UAAA,MAAA8P,sCAAA,kBAEM;UACNrR,EAAA,CAAAuB,UAAA,MAAA+P,sCAAA,kBAEM;UACRtR,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA6Q,yDAAA;YAAAvR,EAAA,CAAAY,aAAA,CAAA4Q,IAAA;YAAA,MAAAC,GAAA,GAAAzR,EAAA,CAAA0R,WAAA;YAAA,OAAS1R,EAAA,CAAAgB,WAAA,CAAAyQ,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnE3R,EAAA,CAAAuB,UAAA,MAAAqQ,2CAAA,uBAA6D;UAC7D5R,EAAA,CAAAuB,UAAA,MAAAsQ,sCAAA,kBAEM;UACN7R,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAAoR,yDAAA3Q,MAAA;YAAA,OAAUoK,GAAA,CAAAnB,cAAA,CAAAjJ,MAAA,CAAsB;UAAA,EAAC;UAA/DnB,EAAA,CAAAG,YAAA,EAAwG;UACxGH,EAAA,CAAAuB,UAAA,MAAAwQ,4CAAA,wBAEY;UACd/R,EAAA,CAAAG,YAAA,EAAM;UAWtBH,EAAA,CAAAuB,UAAA,MAAAyQ,2CAAA,uBAkMW;UACbhS,EAAA,CAAAG,YAAA,EAAM;;;UAjWCH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAiS,eAAA,KAAAC,GAAA,EAAkC;UAIlClS,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAiS,eAAA,KAAAE,GAAA,EAAqC;UAMJnS,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAgD,iBAAA,CAAAuI,GAAA,CAAApF,UAAA,yBAAsC;UAClEnG,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAgD,iBAAA,CAAAuI,GAAA,CAAApF,UAAA,kCAA+C;UAOrDnG,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAkC,kBAAA,MAAAqJ,GAAA,CAAApF,UAAA,4BACF;UAO6BnG,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAiL,GAAA,CAAAzJ,gBAAA,CAA8B;UAgBrC9B,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,aAAAoI,QAAA,mBAAiE;UAUjEpS,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,cAAAoI,QAAA,oBAAmE;UAanEpS,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,WAAAoI,QAAA,iBAA6D;UAcvEpS,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAArF,YAAA,uBAA2C;UAEjClG,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAgD,iBAAA,CAAAuI,GAAA,CAAArF,YAAA,mCAAkD;UA+C7BlG,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAA/K,OAAA,CAAa;UAGTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAAiL,GAAA,CAAA/K,OAAA,CAAc;UAMhCR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAAiL,GAAA,CAAAxF,kBAAA,CAAyB;UAC9B/F,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAxF,kBAAA,CAAwB;UAMpB/F,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,SAAAjB,OAAA,IAAAwC,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,SAAAqI,OAAA,CAAkF;UAcrGrS,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAiL,GAAA,CAAAhF,gBAAA,CAAsB;;;qBD/HjC9H,YAAY,EAAA6T,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZ5T,aAAa,EAAA6T,GAAA,CAAAC,OAAA,EACb7T,cAAc,EAAA8T,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACdnU,gBAAgB,EAChBL,WAAW,EAAAuQ,EAAA,CAAAkE,aAAA,EAAAlE,EAAA,CAAAmE,oBAAA,EAAAnE,EAAA,CAAAoE,eAAA,EAAApE,EAAA,CAAAqE,oBAAA,EACX3U,mBAAmB,EAAAsQ,EAAA,CAAAsE,kBAAA,EAAAtE,EAAA,CAAAuE,eAAA,EACnBpU,cAAc,EAAAqU,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdtU,eAAe,EAAAuU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfxU,aAAa,EAAAyU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACb/U,eAAe,EACfN,oBAAoB,EAAAsV,GAAA,CAAAC,cAAA,EACpBtV,aAAa,EAAAuV,GAAA,CAAAC,WAAA,EAAAD,GAAA,CAAAE,MAAA,EAAAF,GAAA,CAAAG,WAAA,EACbpV,gBAAgB,EAChBL,YAAY,EAAA2P,EAAA,CAAA+F,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHlQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}