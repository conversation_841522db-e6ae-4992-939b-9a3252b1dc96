.smart-dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;

  // ===== SMART DASHBOARD SPECIFIC MATERIAL OVERRIDES =====
  // Compact form fields with orange theme
  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px !important;
      min-height: 36px !important;

      .mat-mdc-form-field-infix {
        padding: 6px 12px !important;
        min-height: 24px !important;
        border-top: none !important;
      }

      .mat-mdc-form-field-flex {
        align-items: center !important;
        height: 36px !important;
      }
    }

    // Remove subscript wrapper to save space
    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
    }

    // Orange theme for outlines
    .mat-mdc-form-field-outline {
      color: #dee2e6 !important;
    }

    .mat-mdc-form-field-outline-thick {
      color: #ff6b35 !important;
    }

    // Orange theme for labels
    .mat-mdc-form-field-label {
      color: #666 !important;
      font-size: 13px !important;
      top: 18px !important;
    }

    &.mat-focused {
      .mat-mdc-form-field-label {
        color: #ff6b35 !important;
      }
    }

    // Floating label adjustments
    &.mat-form-field-should-float {
      .mat-mdc-form-field-label {
        transform: translateY(-12px) scale(0.75) !important;
      }
    }
  }

  // Compact select dropdowns
  ::ng-deep .mat-mdc-select {
    .mat-mdc-select-trigger {
      height: 36px !important;
      display: flex !important;
      align-items: center !important;
    }

    .mat-mdc-select-value {
      font-size: 13px !important;
      line-height: 24px !important;
    }

    .mat-mdc-select-arrow {
      color: #ff6b35 !important;
    }
  }

  // Select panel styling
  ::ng-deep .mat-mdc-select-panel {
    .mat-mdc-option {
      height: 32px !important;
      line-height: 32px !important;
      font-size: 13px !important;
      padding: 0 16px !important;

      &.mat-mdc-option-active {
        background: rgba(255, 107, 53, 0.1) !important;
        color: #ff6b35 !important;
      }

      &:hover {
        background: rgba(255, 107, 53, 0.05) !important;
      }
    }
  }

  // Compact date pickers
  ::ng-deep .mat-mdc-input-element {
    font-size: 13px !important;
    height: 24px !important;
    line-height: 24px !important;
  }

  ::ng-deep .mat-datepicker-toggle {
    .mat-icon {
      color: #ff6b35 !important;
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }
  }

  // Date picker panel orange theme
  ::ng-deep .mat-datepicker-content {
    .mat-calendar-header {
      background: #ff6b35 !important;
      color: white !important;
    }

    .mat-calendar-body-selected {
      background-color: #ff6b35 !important;
      color: white !important;
    }

    .mat-calendar-body-today:not(.mat-calendar-body-selected) {
      border-color: #ff6b35 !important;
    }
  }

  // Compact buttons
  ::ng-deep .mat-mdc-raised-button,
  ::ng-deep .mat-mdc-outlined-button {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;

    &.mat-primary {
      background-color: #ff6b35 !important;
      color: white !important;

      &:hover {
        background-color: #ff5722 !important;
      }
    }
  }

  ::ng-deep .mat-mdc-outlined-button {
    &.mat-primary {
      border-color: #ff6b35 !important;
      color: #ff6b35 !important;
      background-color: transparent !important;

      &:hover {
        background-color: rgba(255, 107, 53, 0.05) !important;
      }
    }
  }

  // Main Layout Container
  .main-layout {
    flex: 1;
    display: flex;
    overflow: hidden;
    height: 100vh;

    // Left Sidebar
    .left-sidebar {
      width: 240px;
      background: white;
      border-right: 1px solid #e9ecef;
      padding: 16px;
      overflow: hidden; // Remove sidebar scrollbar
      height: 100%;
      box-shadow: 2px 0 4px rgba(0,0,0,0.08);

      // Dashboard Selection within sidebar
      .dashboard-selection {
        margin: -16px -16px 16px -16px; // Extend to full sidebar width
        padding: 8px 16px;
        background: linear-gradient(135deg, #ffb088 0%, #ffc4a3 100%);
        border-radius: 0;
        position: relative;

        .dashboard-dropdown {
          width: 100%;

          ::ng-deep .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }

          ::ng-deep .mat-mdc-text-field-wrapper {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.2s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.25);
              border-color: rgba(255, 255, 255, 0.4);
            }
          }

          ::ng-deep .mat-mdc-form-field-outline {
            display: none;
          }

          ::ng-deep .mat-mdc-form-field-outline-thick {
            display: none;
          }

          ::ng-deep .mat-mdc-form-field-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 12px;
          }

          ::ng-deep .mat-mdc-select-value {
            color: white;
            font-weight: 600;
            font-size: 14px;
          }

          ::ng-deep .mat-mdc-select-arrow {
            color: rgba(255, 255, 255, 0.9);
          }

          ::ng-deep .mat-mdc-form-field-infix {
            padding: 6px 12px;
            min-height: 32px;
          }

          ::ng-deep .mat-mdc-select-trigger {
            height: 32px;
            display: flex;
            align-items: center;
          }

          ::ng-deep .mat-mdc-form-field-flex {
            align-items: center;
          }
        }
      }

      // Filters Section
      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 16px 0;
          font-size: 15px;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ff6b35;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          .filter-count {
            background: #ff6b35;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            margin-left: auto;
          }
        }

        .filter-group {
          margin-bottom: 20px;

          .filter-label {
            display: flex;
            align-items: center;
            gap: 6px;
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: 500;
            color: #333;

            mat-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
              color: #ff6b35;
            }
          }

          .filter-field {
            width: 100%;
            margin-bottom: 6px;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 4px;
            }

            ::ng-deep .mat-mdc-form-field-outline {
              color: #dee2e6;
            }

            ::ng-deep .mat-mdc-form-field-outline-thick {
              color: #ff6b35;
            }

            ::ng-deep .mat-mdc-form-field-label {
              font-size: 13px;
            }
          }
        }

        .filter-actions {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #e9ecef;

          .reset-filters-btn {
            width: 100%;
            color: #6c757d;
            border-color: #dee2e6;
            font-weight: 500;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }
          }
        }
      }
    }

    // Right Content Area
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f8f9fa;
      height: 100%;
      overflow: hidden;

      // Top Search Header
      .search-header {
        background: white;
        border-bottom: 1px solid #e9ecef;
        padding: 12px 24px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 60px;

        .assistant-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .assistant-icon {
            color: #ff6b35;
            font-size: 20px;
            width: 20px;
            height: 20px;
          }

          .assistant-text {
            display: flex;
            flex-direction: column;

            .assistant-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              margin-bottom: 1px;
            }

            .assistant-status {
              font-size: 10px;
              color: #28a745;
              font-weight: 500;
              background: #e8f5e8;
              padding: 2px 6px;
              border-radius: 10px;
              display: inline-block;
              width: fit-content;
            }
          }
        }

        .search-container {
          flex: 1;
          max-width: 500px;
          margin-left: 24px;

          .search-field {
            width: 100%;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: #f8f9fa;
              border-radius: 24px;
              height: 40px;
            }

            ::ng-deep .mat-mdc-form-field-outline {
              color: #dee2e6;
              border-radius: 24px;
            }

            ::ng-deep .mat-mdc-form-field-outline-thick {
              color: #ff6b35;
              border-radius: 24px;
            }

            ::ng-deep .mat-mdc-form-field-infix {
              padding: 8px 16px;
              border-top: none;
            }

            ::ng-deep .mat-mdc-form-field-flex {
              align-items: center;
              height: 40px;
            }

            ::ng-deep input {
              font-size: 14px;
              color: #666;
            }

            ::ng-deep input::placeholder {
              color: #999;
              font-size: 14px;
            }

            .search-icon {
              color: #999;
              cursor: pointer;
              font-size: 20px;

              &:hover {
                color: #ff6b35;
              }
            }
          }
        }
      }

      // Dashboard Content Area
      .dashboard-content-area {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;

        // Custom scrollbar styling for right content only
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #ff6b35;
          border-radius: 3px;

          &:hover {
            background: #ff5722;
          }
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          gap: 16px;

          p {
            color: #6c757d;
            font-size: 16px;
          }
        }

        .dashboard-grid {
          .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 20px;

            .summary-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              position: relative;

              &:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              }

              // Color indicators for each card type
              &:nth-child(1) {
                border-left: 4px solid #ff6b35;
              }
              &:nth-child(2) {
                border-left: 4px solid #ffa66f;
              }
              &:nth-child(3) {
                border-left: 4px solid #ff8b4d;
              }
              &:nth-child(4) {
                border-left: 4px solid #ff9966;
              }

              ::ng-deep .mat-mdc-card-content {
                padding: 16px !important;
              }

              .card-content {
                display: flex;
                align-items: center;
                gap: 12px;

                .card-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  mat-icon {
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                  }
                }

                .card-info {
                  flex: 1;

                  .card-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    line-height: 1.2;
                  }

                  .card-label {
                    font-size: 12px;
                    color: #6c757d;
                    font-weight: 500;
                    line-height: 1.3;
                  }
                }
              }
            }
          }

          .charts-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px;

            .chart-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;

              &:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }

              &.full-width {
                grid-column: span 12;
              }

              &.half-width {
                grid-column: span 6;
              }

              &.third-width {
                grid-column: span 4;
              }

              ::ng-deep .mat-mdc-card-header {
                padding: 16px 16px 0 16px;
                border-bottom: 1px solid #f1f3f4;
              }

              ::ng-deep .mat-mdc-card-content {
                padding: 16px !important;
              }

              .chart-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
              }

              .chart-container {
                height: 280px;
                position: relative;

                canvas {
                  max-height: 100%;
                }

                .no-data-message {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100%;
                  color: #adb5bd;
                  text-align: center;

                  mat-icon {
                    font-size: 40px;
                    width: 40px;
                    height: 40px;
                    margin-bottom: 8px;
                    opacity: 0.5;
                    color: #ff6b35;
                  }

                  p {
                    margin: 0;
                    font-size: 13px;
                    font-weight: 500;
                  }
                }

                &[data-chart-type="line"] {
                  height: 300px;
                }

                &[data-chart-type="doughnut"],
                &[data-chart-type="pie"] {
                  height: 260px;
                }
              }
            }
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          color: #666;

          .empty-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1400px) {
  .smart-dashboard-container .main-layout .right-content .dashboard-content-area .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid .chart-card.half-width,
    .charts-grid .chart-card.third-width {
      grid-column: span 12;
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .main-layout .right-content .search-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .search-container .search-field {
        width: 100%;
      }
    }

    .main-layout {
      .left-sidebar {
        width: 220px;
      }
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container {
    .main-layout {
      flex-direction: column;
      height: 100vh;

      .left-sidebar {
        width: 100%;
        height: auto;
        max-height: 250px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        padding: 12px;

        .dashboard-selection {
          margin-bottom: 12px;
          padding-bottom: 12px;
        }
      }

      .right-content {
        .search-header {
          flex-direction: column;
          gap: 12px;
          padding: 12px 16px;

          .search-container .search-field {
            width: 100%;
          }
        }

        .dashboard-content-area {
          padding: 12px;

          .dashboard-grid {
            .summary-cards-row {
              grid-template-columns: 1fr;
              gap: 12px;
            }

            .charts-grid {
              gap: 12px;

              .chart-card {
                grid-column: span 12 !important;
              }
            }
          }
        }
      }
    }
  }
}

// Chart specific styles
::ng-deep {
  .chart-container canvas {
    max-width: 100% !important;
    height: auto !important;
  }
}
