.smart-dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;

  .dashboard-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 16px 24px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        .assistant-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .assistant-icon {
            color: #ff6b35;
            font-size: 24px;
            width: 24px;
            height: 24px;
          }

          .assistant-text {
            display: flex;
            flex-direction: column;

            .assistant-title {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin-bottom: 2px;
            }

            .assistant-status {
              font-size: 12px;
              color: #ff6b35;
              font-weight: 500;
            }
          }
        }
      }

      .header-right {
        .search-container {
          .search-field {
            width: 400px;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: #f8f9fa;
              border-radius: 8px;
            }

            ::ng-deep .mat-mdc-form-field-outline {
              color: #dee2e6;
            }

            ::ng-deep .mat-mdc-form-field-focus-overlay {
              background-color: transparent;
            }

            .search-icon {
              color: #6c757d;
            }
          }
        }
      }
    }
  }

  .dashboard-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    height: calc(100vh - 80px); // Subtract header height

    .sidebar {
      width: 280px;
      background: white;
      border-right: 1px solid #e9ecef;
      padding: 20px;
      overflow-y: auto;
      height: 100%;
      box-shadow: 1px 0 3px rgba(0,0,0,0.1);

      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 20px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ff6b35;
            font-size: 20px;
            width: 20px;
            height: 20px;
          }

          .filter-count {
            background: #ff6b35;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            margin-left: auto;
          }
        }

        .filter-group {
          margin-bottom: 24px;

          .filter-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 500;
            color: #333;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
              color: #ff6b35;
            }
          }

          .filter-field {
            width: 100%;
            margin-bottom: 8px;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 4px;
            }

            ::ng-deep .mat-mdc-form-field-outline {
              color: #dee2e6;
            }

            ::ng-deep .mat-mdc-form-field-outline-thick {
              color: #ff6b35;
            }
          }
        }

        .filter-actions {
          margin-top: 24px;
          padding-top: 20px;
          border-top: 1px solid #e9ecef;

          .reset-filters-btn {
            width: 100%;
            color: #6c757d;
            border-color: #dee2e6;
            font-weight: 500;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }
          }
        }
      }
    }

    .main-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      background-color: #f8f9fa;
      height: 100%;

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        gap: 16px;

        p {
          color: #6c757d;
          font-size: 16px;
        }
      }

      .dashboard-grid {
        .summary-cards-row {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 16px;
          margin-bottom: 24px;

          .summary-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: box-shadow 0.2s ease;
            position: relative;

            &:hover {
              box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }

            // Color indicators for each card type
            &:nth-child(1) {
              border-left: 4px solid #ff6b35;
            }
            &:nth-child(2) {
              border-left: 4px solid #ffa66f;
            }
            &:nth-child(3) {
              border-left: 4px solid #ff8b4d;
            }
            &:nth-child(4) {
              border-left: 4px solid #ff9966;
            }

            ::ng-deep .mat-mdc-card-content {
              padding: 16px !important;
            }

            .card-content {
              display: flex;
              align-items: center;
              gap: 12px;

              .card-icon {
                width: 40px;
                height: 40px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;

                mat-icon {
                  font-size: 20px;
                  width: 20px;
                  height: 20px;
                }
              }

              .card-info {
                flex: 1;

                .card-value {
                  font-size: 20px;
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 4px;
                  line-height: 1.2;
                }

                .card-label {
                  font-size: 12px;
                  color: #6c757d;
                  font-weight: 500;
                  line-height: 1.3;
                }
              }
            }
          }
        }

        .charts-grid {
          display: grid;
          grid-template-columns: repeat(12, 1fr);
          gap: 16px;

          .chart-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: box-shadow 0.2s ease;

            &:hover {
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            &.full-width {
              grid-column: span 12;
            }

            &.half-width {
              grid-column: span 6;
            }

            &.third-width {
              grid-column: span 4;
            }

            ::ng-deep .mat-mdc-card-header {
              padding: 16px 16px 0 16px;
              border-bottom: 1px solid #f1f3f4;
            }

            ::ng-deep .mat-mdc-card-content {
              padding: 16px !important;
            }

            .chart-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              margin: 0;
            }

            .chart-container {
              height: 280px;
              position: relative;

              canvas {
                max-height: 100%;
              }

              .no-data-message {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #adb5bd;
                text-align: center;

                mat-icon {
                  font-size: 40px;
                  width: 40px;
                  height: 40px;
                  margin-bottom: 8px;
                  opacity: 0.5;
                  color: #ff6b35;
                }

                p {
                  margin: 0;
                  font-size: 13px;
                  font-weight: 500;
                }
              }

              &[data-chart-type="line"] {
                height: 300px;
              }

              &[data-chart-type="doughnut"],
              &[data-chart-type="pie"] {
                height: 260px;
              }
            }
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        text-align: center;
        color: #666;

        .empty-icon {
          font-size: 64px;
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 20px;
        }

        p {
          margin: 0 0 24px 0;
          font-size: 14px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1400px) {
  .smart-dashboard-container .dashboard-content .main-content .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid .chart-card.half-width,
    .charts-grid .chart-card.third-width {
      grid-column: span 12;
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .dashboard-header .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-right .search-container .search-field {
        width: 100%;
      }
    }

    .dashboard-content {
      height: calc(100vh - 120px);

      .sidebar {
        width: 260px;
      }
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container {
    .dashboard-content {
      flex-direction: column;
      height: auto;

      .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
      }

      .main-content {
        height: auto;

        .dashboard-grid {
          .summary-cards-row {
            grid-template-columns: 1fr;
          }

          .charts-grid .chart-card {
            grid-column: span 12 !important;
          }
        }
      }
    }
  }
}

// Chart specific styles
::ng-deep {
  .chart-container canvas {
    max-width: 100% !important;
    height: auto !important;
  }
}
