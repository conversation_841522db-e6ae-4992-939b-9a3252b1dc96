{"ast": null, "code": "import _asyncToGenerator from \"/home/<USER>/other/digi/digitorywebv4/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatNativeDateModule, MatOption } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { Subject, first, map, startWith, takeUntil } from 'rxjs';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { ReplaySubject } from 'rxjs';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSort } from '@angular/material/sort';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/inventory.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/services/share-data.service\";\nimport * as i6 from \"src/app/services/session-cache.service\";\nimport * as i7 from \"src/app/services/notification.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"src/app/services/master-data.service\";\nimport * as i10 from \"@angular/material/chips\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/button\";\nimport * as i15 from \"@angular/material/icon\";\nimport * as i16 from \"@angular/material/select\";\nimport * as i17 from \"@angular/material/core\";\nimport * as i18 from \"@angular/material/radio\";\nimport * as i19 from \"@angular/material/autocomplete\";\nimport * as i20 from \"ngx-mat-select-search\";\nimport * as i21 from \"@angular/material/table\";\nimport * as i22 from \"@angular/material/tooltip\";\nimport * as i23 from \"ngx-skeleton-loader\";\nimport * as i24 from \"@angular/material/paginator\";\nimport * as i25 from \"@angular/cdk/a11y\";\nimport * as i26 from \"@angular/material/slide-toggle\";\nconst _c0 = [\"stepper\"];\nconst _c1 = [\"openPortionDialog\"];\nconst _c2 = [\"openUnitCostDialog\"];\nconst _c3 = [\"openDeleteDialog\"];\nconst _c4 = [\"widgetsContent\"];\nconst _c5 = [\"discontinuedSelectDialog\"];\nfunction ActionComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\", 20);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_0_Template_mat_icon_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.close());\n    });\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-form-field\", 24)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 25);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_div_3_Template_input_keyup_4_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.filterDialog($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-icon\", 26);\n    i0.ɵɵtext(6, \"search\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActionComponent_div_4_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"uppercase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r32);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, item_r32));\n  }\n}\nfunction ActionComponent_div_4_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_div_4_mat_icon_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"library_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_4_mat_icon_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"update\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_4_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Update\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_4_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"span\");\n    i0.ɵɵtext(4, \"SubRecipe Master Form\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-form-field\", 24)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Search SubRecipe ..\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 30);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_div_4_Template_input_keyup_8_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.checkItem($event));\n    })(\"keyup.enter\", function ActionComponent_div_4_Template_input_keyup_enter_8_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.addOption(\"Subrecipe Master\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-autocomplete\", 31, 32);\n    i0.ɵɵlistener(\"optionSelected\", function ActionComponent_div_4_Template_mat_autocomplete_optionSelected_9_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.optionSelected(\"Subrecipe Master\", $event.option));\n    });\n    i0.ɵɵtemplate(11, ActionComponent_div_4_mat_option_11_Template, 4, 4, \"mat-option\", 33);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-icon\", 26);\n    i0.ɵɵtext(14, \"search\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 34)(16, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_4_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.addOption(\"Subrecipe Master\"));\n    });\n    i0.ɵɵtemplate(17, ActionComponent_div_4_div_17_Template, 3, 0, \"div\", 2);\n    i0.ɵɵtemplate(18, ActionComponent_div_4_mat_icon_18_Template, 2, 0, \"mat-icon\", 12);\n    i0.ɵɵtemplate(19, ActionComponent_div_4_mat_icon_19_Template, 2, 0, \"mat-icon\", 12);\n    i0.ɵɵtemplate(20, ActionComponent_div_4_span_20_Template, 2, 0, \"span\", 12);\n    i0.ɵɵtemplate(21, ActionComponent_div_4_span_21_Template, 2, 0, \"span\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r25 = i0.ɵɵreference(10);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"matAutocomplete\", _r25)(\"formControl\", ctx_r3.itemNameControl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 9, ctx_r3.itemNameOptions));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.itemNameControl.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loadBtn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.updateBtnActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.updateBtnActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.updateBtnActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.updateBtnActive);\n  }\n}\nfunction ActionComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ActionComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.printOption());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"print\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Print\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_button_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ActionComponent_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.update());\n    });\n    i0.ɵɵtemplate(1, ActionComponent_button_7_div_1_Template, 3, 0, \"div\", 2);\n    i0.ɵɵtext(2, \" Update \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r5.loadSrmBtn || ctx_r5.loadSpinnerForApi);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadSpinnerForApi);\n  }\n}\nfunction ActionComponent_button_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ActionComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      ctx_r44.submit();\n      return i0.ɵɵresetView(ctx_r44.isButtonDisabled = true);\n    });\n    i0.ɵɵtemplate(1, ActionComponent_button_8_div_1_Template, 3, 0, \"div\", 2);\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"add_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Create \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r6.dataSource.data.length === 0 || ctx_r6.registrationForm.invalid || ctx_r6.loadSpinnerForApi);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.loadSpinnerForApi);\n  }\n}\nfunction ActionComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ActionComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.close());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Close \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1, \" Sub-Recipe Master \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_mat_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const uom_r73 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", uom_r73);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", uom_r73, \" \");\n  }\n}\nfunction ActionComponent_div_12_mat_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const uom_r74 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", uom_r74);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", uom_r74, \" \");\n  }\n}\nfunction ActionComponent_div_12_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const cat_r75 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", cat_r75);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(cat_r75);\n  }\n}\nfunction ActionComponent_div_12_mat_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const sub_r76 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sub_r76);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sub_r76);\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"clickable\": a0\n  };\n};\nfunction ActionComponent_div_12_mat_option_59_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 100);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_option_59_mat_icon_4_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const loc_r77 = i0.ɵɵnextContext().$implicit;\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.onDelete(loc_r77, $event, \"preparatoryLocation\", \"null\"));\n    });\n    i0.ɵɵtext(1, \" delete \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loc_r77 = i0.ɵɵnextContext().$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, ctx_r78.discontinuedPreLocData.includes(loc_r77)));\n  }\n}\nfunction ActionComponent_div_12_mat_option_59_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 101);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_option_59_mat_icon_5_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r86);\n      const loc_r77 = i0.ɵɵnextContext().$implicit;\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.onRestore(loc_r77, $event, \"preparatoryLocation\", \"null\"));\n    });\n    i0.ɵɵtext(1, \" settings_backup_restore\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    \"disabled-option\": a0\n  };\n};\nconst _c8 = function (a0) {\n  return {\n    \"disabledSelect\": a0\n  };\n};\nfunction ActionComponent_div_12_mat_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 96)(1, \"span\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"uppercase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ActionComponent_div_12_mat_option_59_mat_icon_4_Template, 2, 3, \"mat-icon\", 98);\n    i0.ɵɵtemplate(5, ActionComponent_div_12_mat_option_59_mat_icon_5_Template, 2, 0, \"mat-icon\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loc_r77 = ctx.$implicit;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", loc_r77)(\"disabled\", ctx_r54.discontinuedPreLocData.includes(loc_r77))(\"ngClass\", i0.ɵɵpureFunction1(9, _c7, ctx_r54.defaultPreLocData.includes(loc_r77) || ctx_r54.discontinuedPreLocData.includes(loc_r77)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c8, ctx_r54.discontinuedPreLocData.includes(loc_r77)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, loc_r77));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r54.defaultPreLocData.includes(loc_r77) && !ctx_r54.discontinuedPreLocData.includes(loc_r77));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r54.discontinuedPreLocData.includes(loc_r77));\n  }\n}\nfunction ActionComponent_div_12_mat_option_72_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 100);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_option_72_mat_icon_4_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r92);\n      const loc_r87 = i0.ɵɵnextContext().$implicit;\n      const ctx_r90 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r90.onDelete(loc_r87, $event, \"usedOutlet\", \"null\"));\n    });\n    i0.ɵɵtext(1, \" delete \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loc_r87 = i0.ɵɵnextContext().$implicit;\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, ctx_r88.discontinuedOutletData.includes(loc_r87)));\n  }\n}\nfunction ActionComponent_div_12_mat_option_72_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 101);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_option_72_mat_icon_5_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const loc_r87 = i0.ɵɵnextContext().$implicit;\n      const ctx_r94 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r94.onRestore(loc_r87, $event, \"usedOutlet\", \"null\"));\n    });\n    i0.ɵɵtext(1, \" settings_backup_restore\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_mat_option_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 96)(1, \"span\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"uppercase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ActionComponent_div_12_mat_option_72_mat_icon_4_Template, 2, 3, \"mat-icon\", 98);\n    i0.ɵɵtemplate(5, ActionComponent_div_12_mat_option_72_mat_icon_5_Template, 2, 0, \"mat-icon\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const loc_r87 = ctx.$implicit;\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", loc_r87)(\"disabled\", ctx_r55.discontinuedOutletData.includes(loc_r87))(\"ngClass\", i0.ɵɵpureFunction1(9, _c7, ctx_r55.defaultOutletData.includes(loc_r87) || ctx_r55.discontinuedOutletData.includes(loc_r87)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c8, ctx_r55.discontinuedOutletData.includes(loc_r87)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, loc_r87));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.defaultOutletData.includes(loc_r87) && !ctx_r55.discontinuedOutletData.includes(loc_r87));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.discontinuedOutletData.includes(loc_r87));\n  }\n}\nfunction ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 100);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_4_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r104);\n      const data_r99 = i0.ɵɵnextContext().$implicit;\n      const group_r97 = i0.ɵɵnextContext().$implicit;\n      const ctx_r102 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r102.onDelete(data_r99, $event, \"issuedTo\", group_r97));\n    });\n    i0.ɵɵtext(1, \" delete \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r99 = i0.ɵɵnextContext().$implicit;\n    const ctx_r100 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, ctx_r100.discontinuedIssuedToData.includes(data_r99)));\n  }\n}\nfunction ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r109 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-icon\", 101);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_5_Template_mat_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r109);\n      const data_r99 = i0.ɵɵnextContext().$implicit;\n      const group_r97 = i0.ɵɵnextContext().$implicit;\n      const ctx_r107 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r107.onRestore(data_r99, $event, \"issuedTo\", group_r97));\n    });\n    i0.ɵɵtext(1, \" settings_backup_restore \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_mat_optgroup_81_mat_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 96)(1, \"span\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"uppercase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_4_Template, 2, 3, \"mat-icon\", 98);\n    i0.ɵɵtemplate(5, ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_5_Template, 2, 0, \"mat-icon\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r99 = ctx.$implicit;\n    const group_r97 = i0.ɵɵnextContext().$implicit;\n    const ctx_r98 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", data_r99)(\"disabled\", ctx_r98.isOptionDisabled(data_r99, group_r97))(\"ngClass\", i0.ɵɵpureFunction1(9, _c7, ctx_r98.isCheckOptionDisabled(data_r99, group_r97) || ctx_r98.defaultIssuedToData.includes(data_r99)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c8, ctx_r98.isOptionDisabled(data_r99, group_r97) || group_r97.disabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, data_r99));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r98.discontinuedOutletData.includes(group_r97.abbreviatedRestaurantId) && ctx_r98.defaultIssuedToData.includes(data_r99) && !ctx_r98.isOptionDisabled(data_r99, group_r97));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r98.isOptionDisabled(data_r99, group_r97) && !group_r97.disabled);\n  }\n}\nfunction ActionComponent_div_12_mat_optgroup_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-optgroup\", 102);\n    i0.ɵɵtemplate(1, ActionComponent_div_12_mat_optgroup_81_mat_option_1_Template, 6, 13, \"mat-option\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r97 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", group_r97.restaurantIdOld.split(\"@\")[1])(\"disabled\", group_r97.disabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", group_r97.workAreas);\n  }\n}\nfunction ActionComponent_div_12_mat_error_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 103);\n    i0.ɵɵtext(1, \" * select at least one workarea in every branch \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_mat_error_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Yield should be more than 0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_mat_option_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const unit_r112 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", unit_r112);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(unit_r112);\n  }\n}\nfunction ActionComponent_div_12_mat_error_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Portion should be more than 0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 28)(2, \"label\");\n    i0.ɵɵtext(3, \"Do you want to discontinue?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-radio-group\", 104)(5, \"mat-radio-button\", 105);\n    i0.ɵɵtext(6, \"Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-radio-button\", 106);\n    i0.ɵɵtext(8, \"No\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nconst _c9 = function (a0) {\n  return {\n    \"text-warning\": a0\n  };\n};\nfunction ActionComponent_div_12_mat_option_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 107)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"uppercase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const name_r113 = ctx.$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", name_r113)(\"ngClass\", i0.ɵɵpureFunction1(6, _c9, ctx_r63.subData.includes(name_r113)))(\"disabled\", ctx_r63.updateSRR && ctx_r63.subRecipeRecipeForm.value.ingredientName || name_r113 === \"No Item Found\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, name_r113));\n  }\n}\nfunction ActionComponent_div_12_option_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 108);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r114 = ctx.$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", data_r114)(\"disabled\", !ctx_r64.isOptionAccessible(data_r114));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r114, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_150_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"label\", 109);\n    i0.ɵɵtext(2, \"Portion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 110);\n    i0.ɵɵlistener(\"focus\", function ActionComponent_div_12_div_150_Template_input_focus_3_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r115.focusFunctionSRR(\"portionCount\"));\n    })(\"focusout\", function ActionComponent_div_12_div_150_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r117 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r117.focusOutFunctionSRR(\"portionCount\"));\n    })(\"keyup\", function ActionComponent_div_12_div_150_Template_input_keyup_3_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r118 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r118.convertPortionToUOM());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_div_12_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtext(1, \" * weight should be greater than 0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_164_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtext(1, \" Yield should be more than 0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 154);\n    i0.ɵɵtext(1, \" S.No. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 154);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r168 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i_r168 + 1);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 154);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 155);\n    i0.ɵɵtext(1, \" Action \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r171 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 155)(1, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_div_176_mat_cell_8_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r171);\n      const element_r169 = restoredCtx.$implicit;\n      const ctx_r170 = i0.ɵɵnextContext(3);\n      const _r10 = i0.ɵɵreference(14);\n      return i0.ɵɵresetView(ctx_r170.editFun(element_r169, _r10));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 157);\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_div_176_mat_cell_8_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r171);\n      const element_r169 = restoredCtx.$implicit;\n      const ctx_r172 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r172.deleteSRR(element_r169));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\", 157);\n    i0.ɵɵtext(6, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 155);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"mat-icon\", 162);\n    i0.ɵɵtext(2, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u00A0 Discontinued \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"mat-icon\", 163);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u00A0 Active \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_12_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"mat-icon\", 163);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u00A0 Active \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 160);\n    i0.ɵɵtemplate(1, ActionComponent_div_12_div_176_mat_cell_12_div_1_Template, 4, 0, \"div\", 161);\n    i0.ɵɵtemplate(2, ActionComponent_div_12_div_176_mat_cell_12_div_2_Template, 4, 0, \"div\", 161);\n    i0.ɵɵtemplate(3, ActionComponent_div_12_div_176_mat_cell_12_div_3_Template, 4, 0, \"div\", 161);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r173 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r173.Discontinued == \"yes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r173.Discontinued == \"no\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r173.Discontinued != \"no\" && element_r173.Discontinued != \"yes\");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 164);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 165);\n    i0.ɵɵtext(1, \" Modified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-chip\", 166);\n    i0.ɵɵtext(2, \"NOT SYNCED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" - \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 165);\n    i0.ɵɵtemplate(1, ActionComponent_div_12_div_176_mat_cell_16_div_1_Template, 3, 0, \"div\", 12);\n    i0.ɵɵtemplate(2, ActionComponent_div_12_div_176_mat_cell_16_div_2_Template, 2, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r177 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r177.modified == \"yes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r177.modified == \"no\" || element_r177.modified == \"-\");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 165);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 167);\n    i0.ɵɵtext(1, \" Ingredient Name \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_20_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 171);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_20_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 171);\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r184 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 168)(1, \"div\", 169);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_div_176_mat_cell_20_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r184);\n      const element_r180 = restoredCtx.$implicit;\n      const ctx_r183 = i0.ɵɵnextContext(3);\n      const _r10 = i0.ɵɵreference(14);\n      return i0.ɵɵresetView(ctx_r183.editFun(element_r180, _r10));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActionComponent_div_12_div_176_mat_cell_20_mat_icon_3_Template, 2, 0, \"mat-icon\", 170);\n    i0.ɵɵtemplate(4, ActionComponent_div_12_div_176_mat_cell_20_mat_icon_4_Template, 2, 0, \"mat-icon\", 170);\n    i0.ɵɵelementStart(5, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_div_176_mat_cell_20_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r184);\n      const element_r180 = restoredCtx.$implicit;\n      const ctx_r185 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r185.deleteSRR(element_r180));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\", 157);\n    i0.ɵɵtext(7, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const element_r180 = ctx.$implicit;\n    const ctx_r132 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, ctx_r132.subData.includes(element_r180.ingredientName)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r180.ingredientName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r180.Discontinued == \"no\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r180.Discontinued != \"no\" && element_r180.Discontinued != \"yes\");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\", 172);\n    i0.ɵɵtext(1, \" Total \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Ingredient Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r186 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r186.ingredientCode, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 164);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" SubRecipe Name \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r187 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r187.subRecipeName, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 164);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" SubRecipe Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r188 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r188.subRecipeCode, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 164);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 174);\n    i0.ɵɵtext(1, \" UOM \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r189 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r189.UOM, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 174);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Initial Weight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r190 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r190.Initialweight || 0, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\", 164);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r148 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r148.getTotal(\"Initialweight\"), \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 175);\n    i0.ɵɵtext(1, \" Yield \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r191 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r191.yield || 0, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 175);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Loss \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r192 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r192.loss || 0, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 164);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Weight in Use \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r193 = ctx.$implicit;\n    const ctx_r156 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r156.notify.truncateAndFloor(element_r193.weightInUse || 0), \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\", 164);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r157 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r157.getTotal(\"weightInUse\"), \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 174);\n    i0.ɵɵtext(1, \" WAC(incl.tax,etc)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r194 = ctx.$implicit;\n    const ctx_r159 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r159.notify.truncateAndFloor(element_r194.rate || 0), \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\", 174);\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_cell_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 159);\n    i0.ɵɵtext(1, \" Final Rate\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_cell_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 173);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r195 = ctx.$implicit;\n    const ctx_r162 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r162.notify.truncateAndFloor(element_r195.finalRate, 2) || 0, \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_cell_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\", 164);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r163 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r163.notify.truncateAndFloor(ctx_r163.getSRRTotal(\"finalRate\"), 2), \" \");\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_header_row_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nconst _c10 = function (a0) {\n  return {\n    \"highlighted-row\": a0\n  };\n};\nfunction ActionComponent_div_12_div_176_mat_row_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 97);\n  }\n  if (rf & 2) {\n    const row_r196 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c10, row_r196.Discontinued === \"yes\"));\n  }\n}\nfunction ActionComponent_div_12_div_176_mat_footer_row_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-row\");\n  }\n}\nconst _c11 = function () {\n  return [5, 10, 25, 50, 100];\n};\nfunction ActionComponent_div_12_div_176_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"mat-table\", 112);\n    i0.ɵɵelementContainerStart(2, 113);\n    i0.ɵɵtemplate(3, ActionComponent_div_12_div_176_mat_header_cell_3_Template, 2, 0, \"mat-header-cell\", 114);\n    i0.ɵɵtemplate(4, ActionComponent_div_12_div_176_mat_cell_4_Template, 2, 1, \"mat-cell\", 115);\n    i0.ɵɵtemplate(5, ActionComponent_div_12_div_176_mat_footer_cell_5_Template, 1, 0, \"mat-footer-cell\", 116);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(6, 117);\n    i0.ɵɵtemplate(7, ActionComponent_div_12_div_176_mat_header_cell_7_Template, 2, 0, \"mat-header-cell\", 118);\n    i0.ɵɵtemplate(8, ActionComponent_div_12_div_176_mat_cell_8_Template, 7, 0, \"mat-cell\", 119);\n    i0.ɵɵtemplate(9, ActionComponent_div_12_div_176_mat_footer_cell_9_Template, 1, 0, \"mat-footer-cell\", 120);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 121);\n    i0.ɵɵtemplate(11, ActionComponent_div_12_div_176_mat_header_cell_11_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(12, ActionComponent_div_12_div_176_mat_cell_12_Template, 4, 3, \"mat-cell\", 123);\n    i0.ɵɵtemplate(13, ActionComponent_div_12_div_176_mat_footer_cell_13_Template, 1, 0, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 125);\n    i0.ɵɵtemplate(15, ActionComponent_div_12_div_176_mat_header_cell_15_Template, 2, 0, \"mat-header-cell\", 126);\n    i0.ɵɵtemplate(16, ActionComponent_div_12_div_176_mat_cell_16_Template, 3, 2, \"mat-cell\", 127);\n    i0.ɵɵtemplate(17, ActionComponent_div_12_div_176_mat_footer_cell_17_Template, 1, 0, \"mat-footer-cell\", 128);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 129);\n    i0.ɵɵtemplate(19, ActionComponent_div_12_div_176_mat_header_cell_19_Template, 2, 0, \"mat-header-cell\", 130);\n    i0.ɵɵtemplate(20, ActionComponent_div_12_div_176_mat_cell_20_Template, 8, 6, \"mat-cell\", 131);\n    i0.ɵɵtemplate(21, ActionComponent_div_12_div_176_mat_footer_cell_21_Template, 2, 0, \"mat-footer-cell\", 132);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(22, 133);\n    i0.ɵɵtemplate(23, ActionComponent_div_12_div_176_mat_header_cell_23_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(24, ActionComponent_div_12_div_176_mat_cell_24_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(25, ActionComponent_div_12_div_176_mat_footer_cell_25_Template, 1, 0, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(26, 135);\n    i0.ɵɵtemplate(27, ActionComponent_div_12_div_176_mat_header_cell_27_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(28, ActionComponent_div_12_div_176_mat_cell_28_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(29, ActionComponent_div_12_div_176_mat_footer_cell_29_Template, 1, 0, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(30, 136);\n    i0.ɵɵtemplate(31, ActionComponent_div_12_div_176_mat_header_cell_31_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(32, ActionComponent_div_12_div_176_mat_cell_32_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(33, ActionComponent_div_12_div_176_mat_footer_cell_33_Template, 1, 0, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(34, 137);\n    i0.ɵɵtemplate(35, ActionComponent_div_12_div_176_mat_header_cell_35_Template, 2, 0, \"mat-header-cell\", 138);\n    i0.ɵɵtemplate(36, ActionComponent_div_12_div_176_mat_cell_36_Template, 2, 1, \"mat-cell\", 139);\n    i0.ɵɵtemplate(37, ActionComponent_div_12_div_176_mat_footer_cell_37_Template, 1, 0, \"mat-footer-cell\", 140);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(38, 141);\n    i0.ɵɵtemplate(39, ActionComponent_div_12_div_176_mat_header_cell_39_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(40, ActionComponent_div_12_div_176_mat_cell_40_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(41, ActionComponent_div_12_div_176_mat_footer_cell_41_Template, 2, 1, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(42, 142);\n    i0.ɵɵtemplate(43, ActionComponent_div_12_div_176_mat_header_cell_43_Template, 2, 0, \"mat-header-cell\", 143);\n    i0.ɵɵtemplate(44, ActionComponent_div_12_div_176_mat_cell_44_Template, 2, 1, \"mat-cell\", 144);\n    i0.ɵɵtemplate(45, ActionComponent_div_12_div_176_mat_footer_cell_45_Template, 1, 0, \"mat-footer-cell\", 145);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(46, 146);\n    i0.ɵɵtemplate(47, ActionComponent_div_12_div_176_mat_header_cell_47_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(48, ActionComponent_div_12_div_176_mat_cell_48_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(49, ActionComponent_div_12_div_176_mat_footer_cell_49_Template, 1, 0, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(50, 147);\n    i0.ɵɵtemplate(51, ActionComponent_div_12_div_176_mat_header_cell_51_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(52, ActionComponent_div_12_div_176_mat_cell_52_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(53, ActionComponent_div_12_div_176_mat_footer_cell_53_Template, 2, 1, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(54, 148);\n    i0.ɵɵtemplate(55, ActionComponent_div_12_div_176_mat_header_cell_55_Template, 2, 0, \"mat-header-cell\", 138);\n    i0.ɵɵtemplate(56, ActionComponent_div_12_div_176_mat_cell_56_Template, 2, 1, \"mat-cell\", 139);\n    i0.ɵɵtemplate(57, ActionComponent_div_12_div_176_mat_footer_cell_57_Template, 1, 0, \"mat-footer-cell\", 140);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(58, 149);\n    i0.ɵɵtemplate(59, ActionComponent_div_12_div_176_mat_header_cell_59_Template, 2, 0, \"mat-header-cell\", 122);\n    i0.ɵɵtemplate(60, ActionComponent_div_12_div_176_mat_cell_60_Template, 2, 1, \"mat-cell\", 134);\n    i0.ɵɵtemplate(61, ActionComponent_div_12_div_176_mat_footer_cell_61_Template, 2, 1, \"mat-footer-cell\", 124);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(62, ActionComponent_div_12_div_176_mat_header_row_62_Template, 1, 0, \"mat-header-row\", 150);\n    i0.ɵɵtemplate(63, ActionComponent_div_12_div_176_mat_row_63_Template, 1, 3, \"mat-row\", 151);\n    i0.ɵɵtemplate(64, ActionComponent_div_12_div_176_mat_footer_row_64_Template, 1, 0, \"mat-footer-row\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"mat-paginator\", 153);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r70 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r70.dataSource);\n    i0.ɵɵadvance(61);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r70.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r70.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matFooterRowDef\", ctx_r70.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(6, _c11));\n  }\n}\nconst _c12 = function () {\n  return {\n    \"border-radius\": \"5px\",\n    height: \"30px\"\n  };\n};\nfunction ActionComponent_div_12_div_177_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 176);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c12));\n  }\n}\nfunction ActionComponent_div_12_div_178_app_empty_state_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-empty-state\", 179);\n  }\n}\nfunction ActionComponent_div_12_div_178_app_empty_state_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-empty-state\", 180);\n  }\n}\nfunction ActionComponent_div_12_div_178_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ActionComponent_div_12_div_178_app_empty_state_1_Template, 1, 0, \"app-empty-state\", 177);\n    i0.ɵɵtemplate(2, ActionComponent_div_12_div_178_app_empty_state_2_Template, 1, 0, \"app-empty-state\", 178);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r72 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r72.showDeleteItems == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r72.showDeleteItems == true);\n  }\n}\nconst _c13 = function (a0) {\n  return {\n    \"readonly-field\": a0\n  };\n};\nconst _c14 = function () {\n  return [\"GM\", \"ML\", \"NOS\"];\n};\nconst _c15 = function () {\n  return [\"KG\", \"LITRE\", \"NOS\"];\n};\nfunction ActionComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r200 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 42)(2, \"form\", 43)(3, \"div\", 44)(4, \"div\", 45)(5, \"mat-form-field\", 24)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Menu Item code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 46);\n    i0.ɵɵelementStart(9, \"mat-icon\", 26);\n    i0.ɵɵtext(10, \"receipt\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 45)(12, \"mat-form-field\", 24)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Menu Item Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 47);\n    i0.ɵɵelementStart(16, \"mat-icon\", 26);\n    i0.ɵɵtext(17, \"restaurant_menu\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"mat-form-field\", 24)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"UOM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-select\", 48);\n    i0.ɵɵtemplate(23, ActionComponent_div_12_mat_option_23_Template, 2, 2, \"mat-option\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 45)(25, \"mat-form-field\", 24)(26, \"mat-label\");\n    i0.ɵɵtext(27, \"Closing UOM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"mat-select\", 49);\n    i0.ɵɵtemplate(29, ActionComponent_div_12_mat_option_29_Template, 2, 2, \"mat-option\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 45)(31, \"mat-form-field\", 24)(32, \"mat-label\");\n    i0.ɵɵtext(33, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"input\", 50);\n    i0.ɵɵlistener(\"keyup.enter\", function ActionComponent_div_12_Template_input_keyup_enter_34_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r199 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r199.addOptionCat());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"mat-autocomplete\", 31, 32);\n    i0.ɵɵlistener(\"optionSelected\", function ActionComponent_div_12_Template_mat_autocomplete_optionSelected_35_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r201 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r201.optionSelectedCat($event.option));\n    });\n    i0.ɵɵtemplate(37, ActionComponent_div_12_mat_option_37_Template, 3, 2, \"mat-option\", 33);\n    i0.ɵɵpipe(38, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 45)(40, \"mat-form-field\", 24)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Sub Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"input\", 51);\n    i0.ɵɵlistener(\"keyup.enter\", function ActionComponent_div_12_Template_input_keyup_enter_43_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r202 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r202.addOptionSubCat());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-autocomplete\", 31, 52);\n    i0.ɵɵlistener(\"optionSelected\", function ActionComponent_div_12_Template_mat_autocomplete_optionSelected_44_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r203 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r203.optionSelectedSubCat($event.option));\n    });\n    i0.ɵɵtemplate(46, ActionComponent_div_12_mat_option_46_Template, 3, 2, \"mat-option\", 33);\n    i0.ɵɵpipe(47, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 45)(49, \"mat-form-field\", 24)(50, \"mat-label\");\n    i0.ɵɵtext(51, \"Prepared At\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"mat-select\", 53)(53, \"mat-option\");\n    i0.ɵɵelement(54, \"ngx-mat-select-search\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"mat-option\", 55);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_Template_mat_option_click_55_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r204 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r204.preparedToggleSelectAll());\n    });\n    i0.ɵɵelementStart(56, \"mat-icon\", 26);\n    i0.ɵɵtext(57, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(59, ActionComponent_div_12_mat_option_59_Template, 6, 13, \"mat-option\", 56);\n    i0.ɵɵpipe(60, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(61, \"div\", 45)(62, \"mat-form-field\", 24)(63, \"mat-label\");\n    i0.ɵɵtext(64, \"Sales Outlet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"mat-select\", 57);\n    i0.ɵɵlistener(\"selectionChange\", function ActionComponent_div_12_Template_mat_select_selectionChange_65_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r205 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r205.locationChange($event.value));\n    });\n    i0.ɵɵelementStart(66, \"mat-option\");\n    i0.ɵɵelement(67, \"ngx-mat-select-search\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"mat-option\", 55);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_Template_mat_option_click_68_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r206 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r206.usedOutLetToggleSelectAll());\n    });\n    i0.ɵɵelementStart(69, \"mat-icon\", 26);\n    i0.ɵɵtext(70, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(71, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(72, ActionComponent_div_12_mat_option_72_Template, 6, 13, \"mat-option\", 56);\n    i0.ɵɵpipe(73, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 45)(75, \"mat-form-field\", 24)(76, \"mat-label\");\n    i0.ɵɵtext(77, \"Issued To\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"mat-select\", 58)(79, \"mat-option\");\n    i0.ɵɵelement(80, \"ngx-mat-select-search\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(81, ActionComponent_div_12_mat_optgroup_81_Template, 2, 3, \"mat-optgroup\", 59);\n    i0.ɵɵpipe(82, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(83, ActionComponent_div_12_mat_error_83_Template, 2, 0, \"mat-error\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 45)(85, \"div\", 61)(86, \"label\");\n    i0.ɵɵtext(87, \"Initial Weight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\");\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 45)(91, \"div\", 61)(92, \"label\");\n    i0.ɵɵtext(93, \"Final Weight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\");\n    i0.ɵɵtext(95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(96, \"div\", 45)(97, \"div\", 61)(98, \"label\");\n    i0.ɵɵtext(99, \"Preparation Cost \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"div\", 62)(101, \"span\", 63);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_Template_span_click_101_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r207 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r207.openUnitDialog());\n    });\n    i0.ɵɵtext(102);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(103, \"div\", 45)(104, \"div\", 61)(105, \"label\");\n    i0.ɵɵtext(106, \"Usage Cost per UOM \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"div\", 62)(108, \"span\");\n    i0.ɵɵtext(109);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(110, \"div\", 45)(111, \"mat-form-field\", 24)(112, \"mat-label\");\n    i0.ɵɵtext(113, \"Final Yield\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"input\", 64);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_div_12_Template_input_keyup_114_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r208 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r208.setZeroSRM($event, \"yield\"));\n    })(\"focus\", function ActionComponent_div_12_Template_input_focus_114_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r209 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r209.focusFunction(\"yield\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_114_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r210 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r210.focusOutFunction(\"yield\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(115, ActionComponent_div_12_mat_error_115_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 45)(117, \"div\", 65)(118, \"mat-form-field\", 66)(119, \"mat-label\");\n    i0.ɵɵtext(120, \"Batch\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"mat-select\", 67);\n    i0.ɵɵlistener(\"selectionChange\", function ActionComponent_div_12_Template_mat_select_selectionChange_121_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r211 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r211.onUnitChange($event.value));\n    });\n    i0.ɵɵtemplate(122, ActionComponent_div_12_mat_option_122_Template, 2, 2, \"mat-option\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(123, \"mat-form-field\", 66)(124, \"input\", 68);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_div_12_Template_input_keyup_124_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r212 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r212.setPortionData());\n    })(\"focus\", function ActionComponent_div_12_Template_input_focus_124_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r213 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r213.focusFunction(\"portion\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_124_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r214 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r214.focusOutFunction(\"portion\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(125, ActionComponent_div_12_mat_error_125_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵelementStart(126, \"mat-icon\", 69);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_Template_mat_icon_click_126_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r215 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r215.openPortionData());\n    });\n    i0.ɵɵtext(127, \"info\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(128, ActionComponent_div_12_div_128_Template, 9, 0, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(129, \"div\")(130, \"div\", 70);\n    i0.ɵɵtext(131, \" Sub-Recipe Recipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(132, \"div\", 71)(133, \"div\")(134, \"div\", 72)(135, \"form\", 43)(136, \"div\", 73)(137, \"div\", 74)(138, \"label\", 75);\n    i0.ɵɵtext(139, \"Ingredient Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(140, \"input\", 76);\n    i0.ɵɵelementStart(141, \"mat-autocomplete\", 31, 77);\n    i0.ɵɵlistener(\"optionSelected\", function ActionComponent_div_12_Template_mat_autocomplete_optionSelected_141_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r216 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r216.selectIngredientsName(ctx_r216.subRecipeRecipeForm.value.ingredientName));\n    });\n    i0.ɵɵtemplate(143, ActionComponent_div_12_mat_option_143_Template, 4, 8, \"mat-option\", 78);\n    i0.ɵɵpipe(144, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(145, \"div\", 74)(146, \"label\", 79);\n    i0.ɵɵtext(147, \"UOM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(148, \"select\", 80);\n    i0.ɵɵlistener(\"change\", function ActionComponent_div_12_Template_select_change_148_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r217 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r217.uomChange());\n    });\n    i0.ɵɵtemplate(149, ActionComponent_div_12_option_149_Template, 2, 3, \"option\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(150, ActionComponent_div_12_div_150_Template, 4, 0, \"div\", 82);\n    i0.ɵɵelementStart(151, \"div\", 74)(152, \"label\", 83);\n    i0.ɵɵtext(153, \"WAC(incl.tax,etc)/UOM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(154, \"input\", 84);\n    i0.ɵɵlistener(\"focus\", function ActionComponent_div_12_Template_input_focus_154_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r218 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r218.focusFunctionSRR(\"rate\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_154_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r219 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r219.focusOutFunctionSRR(\"rate\"));\n    })(\"keyup\", function ActionComponent_div_12_Template_input_keyup_154_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r220 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r220.sumForFinalRateSRR($event));\n    })(\"focus\", function ActionComponent_div_12_Template_input_focus_154_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r221 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r221.focusFunction(\"rate\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_154_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r222 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r222.focusOutFunction(\"rate\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(155, \"div\", 74)(156, \"label\", 85);\n    i0.ɵɵtext(157, \"Weight in Use\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(158, \"input\", 86);\n    i0.ɵɵlistener(\"focus\", function ActionComponent_div_12_Template_input_focus_158_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r223 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r223.focusFunctionSRR(\"weightInUse\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_158_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r224 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r224.focusOutFunctionSRR(\"weightInUse\"));\n    })(\"keyup\", function ActionComponent_div_12_Template_input_keyup_158_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r225 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r225.sumForFinalRateSRR($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(159, ActionComponent_div_12_div_159_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(160, \"div\", 74)(161, \"label\", 87);\n    i0.ɵɵtext(162, \"Yield\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(163, \"input\", 88);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_div_12_Template_input_keyup_163_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r226 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r226.setZeroSRR($event, \"yield\"));\n    })(\"focus\", function ActionComponent_div_12_Template_input_focus_163_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r227 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r227.focusFunctionSRR(\"yield\"));\n    })(\"focusout\", function ActionComponent_div_12_Template_input_focusout_163_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r228 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r228.focusOutFunctionSRR(\"yield\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(164, ActionComponent_div_12_div_164_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(165, \"div\", 89)(166, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ActionComponent_div_12_Template_button_click_166_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r229 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r229.addNewSubRecipeRecipe());\n    });\n    i0.ɵɵelementStart(167, \"i\", 91);\n    i0.ɵɵtext(168, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(169, \" Add \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(170, \"div\")(171, \"mat-slide-toggle\", 92);\n    i0.ɵɵlistener(\"ngModelChange\", function ActionComponent_div_12_Template_mat_slide_toggle_ngModelChange_171_listener($event) {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r230 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r230.showDeleteItems = $event);\n    })(\"change\", function ActionComponent_div_12_Template_mat_slide_toggle_change_171_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r231 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r231.showItems());\n    });\n    i0.ɵɵtext(172, \"Show Discontinued\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(173, \"div\", 93, 94);\n    i0.ɵɵtemplate(176, ActionComponent_div_12_div_176_Template, 66, 7, \"div\", 95);\n    i0.ɵɵtemplate(177, ActionComponent_div_12_div_177_Template, 2, 2, \"div\", 12);\n    i0.ɵɵtemplate(178, ActionComponent_div_12_div_178_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r50 = i0.ɵɵreference(36);\n    const _r52 = i0.ɵɵreference(45);\n    const _r62 = i0.ɵɵreference(142);\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r9.registrationForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readonly\", ctx_r9.isReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(50, _c13, ctx_r9.isReadOnly));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"readonly\", ctx_r9.isReadOnly)(\"ngClass\", i0.ɵɵpureFunction1(52, _c13, ctx_r9.isReadOnly));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(54, _c14));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(55, _c15));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matAutocomplete\", _r50);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(38, 38, ctx_r9.catBank));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"matAutocomplete\", _r52);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(47, 40, ctx_r9.subCatBank));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formControl\", ctx_r9.preparedFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(60, 42, ctx_r9.preparedLocationNames));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formControl\", ctx_r9.outletFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(73, 44, ctx_r9.outletLocationNames));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formControl\", ctx_r9.usedInWorkAreaFilterCtrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(82, 46, ctx_r9.usedInWorkAreaNames));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showWorkAreaError);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r9.registrationForm.get(\"weightInUse\").value);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r9.registrationForm.get(\"recovery\").value);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.notify.truncateAndFloor(ctx_r9.registrationForm.get(\"rate\").value, 2), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.notify.truncateAndFloor(ctx_r9.registrationForm.value.rate / ctx_r9.registrationForm.value.recovery * 1000, 2), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.registrationForm.get(\"yield\").errors == null ? null : ctx_r9.registrationForm.get(\"yield\").errors[\"yieldInvalid\"]);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.units);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.registrationForm.get(\"portion\").errors == null ? null : ctx_r9.registrationForm.get(\"portion\").errors[\"portionInvalid\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isUpdateActive);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formGroup\", ctx_r9.subRecipeRecipeForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matAutocomplete\", _r62);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(144, 48, ctx_r9.ingredientNamesOptions));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.ingredientUOM);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.subRecipeRecipeForm.value.uom === \"PORTION\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"readonly\", ctx_r9.subRecipeRecipeForm.value.uom === \"PORTION\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showWeightError);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.subRecipeRecipeForm.get(\"yield\").errors == null ? null : ctx_r9.subRecipeRecipeForm.get(\"yield\").errors[\"yieldInvalid\"]);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r9.showDeleteItems);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isSRRDataReady);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isSRRDataReady);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.dataSource.data.length == 0 && ctx_r9.isSRRDataReady);\n  }\n}\nfunction ActionComponent_ng_template_13_button_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"span\", 22);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionComponent_ng_template_13_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r239 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 189);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_13_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r239);\n      const ctx_r238 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r238.editExistingSubRecipeRecipe());\n    });\n    i0.ɵɵtemplate(1, ActionComponent_ng_template_13_button_8_div_1_Template, 3, 0, \"div\", 2);\n    i0.ɵɵtext(2, \" Update \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r232 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r232.loadSpinnerForApiSRR);\n  }\n}\nfunction ActionComponent_ng_template_13_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 108);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const name_r240 = ctx.$implicit;\n    const ctx_r233 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", name_r240)(\"disabled\", ctx_r233.updateSRR);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", name_r240, \" \");\n  }\n}\nfunction ActionComponent_ng_template_13_mat_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 108);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r241 = ctx.$implicit;\n    const ctx_r234 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", data_r241)(\"disabled\", ctx_r234.updateSRR);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r241, \" \");\n  }\n}\nfunction ActionComponent_ng_template_13_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r243 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 24)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Portion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 190);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_ng_template_13_div_33_Template_input_keyup_4_listener() {\n      i0.ɵɵrestoreView(_r243);\n      const ctx_r242 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r242.convertPortionToUOM());\n    })(\"focus\", function ActionComponent_ng_template_13_div_33_Template_input_focus_4_listener() {\n      i0.ɵɵrestoreView(_r243);\n      const ctx_r244 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r244.focusFunctionSRR(\"portionCount\"));\n    })(\"focusout\", function ActionComponent_ng_template_13_div_33_Template_input_focusout_4_listener() {\n      i0.ɵɵrestoreView(_r243);\n      const ctx_r245 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r245.focusOutFunction(\"portionCount\"));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActionComponent_ng_template_13_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 191)(2, \"label\");\n    i0.ɵɵtext(3, \"Do you want to discontinue?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-radio-group\", 192)(5, \"mat-radio-button\", 105);\n    i0.ɵɵtext(6, \"Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-radio-button\", 106);\n    i0.ɵɵtext(8, \"No\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nconst _c16 = function (a0) {\n  return {\n    \"highlighted-input\": a0\n  };\n};\nfunction ActionComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r247 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"mat-icon\", 20);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_13_Template_mat_icon_click_1_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r246 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r246.closeSRRDialog());\n    });\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 29)(5, \"span\");\n    i0.ɵɵtext(6, \"SubRecipe Recipe Form\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 181);\n    i0.ɵɵtemplate(8, ActionComponent_ng_template_13_button_8_Template, 3, 1, \"button\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"form\", 43)(10, \"div\", 44)(11, \"div\")(12, \"mat-form-field\", 183)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"SubRecipe Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 184);\n    i0.ɵɵelementStart(16, \"mat-icon\", 26);\n    i0.ɵɵtext(17, \"restaurant_menu\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\")(19, \"mat-form-field\", 183)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Ingredient Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-select\", 185);\n    i0.ɵɵlistener(\"selectionChange\", function ActionComponent_ng_template_13_Template_mat_select_selectionChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r248 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r248.selectIngredientsName($event.value));\n    });\n    i0.ɵɵelementStart(23, \"mat-option\", 186);\n    i0.ɵɵelement(24, \"ngx-mat-select-search\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ActionComponent_ng_template_13_mat_option_25_Template, 2, 3, \"mat-option\", 81);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\")(28, \"mat-form-field\", 183)(29, \"mat-label\");\n    i0.ɵɵtext(30, \"UOM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-select\", 48);\n    i0.ɵɵtemplate(32, ActionComponent_ng_template_13_mat_option_32_Template, 2, 3, \"mat-option\", 81);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ActionComponent_ng_template_13_div_33_Template, 5, 0, \"div\", 12);\n    i0.ɵɵelementStart(34, \"div\")(35, \"mat-form-field\", 24)(36, \"mat-label\");\n    i0.ɵɵtext(37, \"Weight In Use\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"input\", 187);\n    i0.ɵɵlistener(\"focus\", function ActionComponent_ng_template_13_Template_input_focus_38_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r249 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r249.focusFunctionSRR(\"weightInUse\"));\n    })(\"focusout\", function ActionComponent_ng_template_13_Template_input_focusout_38_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r250 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r250.focusOutFunctionSRR(\"weightInUse\"));\n    })(\"keyup\", function ActionComponent_ng_template_13_Template_input_keyup_38_listener($event) {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r251 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r251.sumForFinalRateSRR($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\")(40, \"mat-form-field\", 24)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Yield\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"input\", 188);\n    i0.ɵɵlistener(\"keyup\", function ActionComponent_ng_template_13_Template_input_keyup_43_listener($event) {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r252 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r252.setZeroSRR($event, \"yield\"));\n    })(\"focus\", function ActionComponent_ng_template_13_Template_input_focus_43_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r253 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r253.focusFunctionSRR(\"yield\"));\n    })(\"focusout\", function ActionComponent_ng_template_13_Template_input_focusout_43_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r254 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r254.focusOutFunctionSRR(\"yield\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(44, ActionComponent_ng_template_13_div_44_Template, 9, 0, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.updateSRR);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r11.subRecipeRecipeForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c16, ctx_r11.isReadOnly));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"readonly\", ctx_r11.isReadOnly);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c16, ctx_r11.isReadOnly));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.updateSRR);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formControl\", ctx_r11.IngredientFilterCtrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(26, 13, ctx_r11.ingredientNames));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c16, ctx_r11.isReadOnly));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.ingredientUOM);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.subRecipeRecipeForm.value.uom === \"PORTION\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"readonly\", ctx_r11.subRecipeRecipeForm.value.uom === \"PORTION\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.updateSRR);\n  }\n}\nfunction ActionComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r257 = ctx.$implicit;\n    const i_r258 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i_r258 + 1, \". \", data_r257, \" \");\n  }\n}\nfunction ActionComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-empty-state\", 196);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 193);\n    i0.ɵɵtemplate(1, ActionComponent_div_15_div_1_Template, 2, 2, \"div\", 194);\n    i0.ɵɵtemplate(2, ActionComponent_div_15_div_2_Template, 2, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.filteredData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.filteredData.length == 0);\n  }\n}\nfunction ActionComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r260 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 197)(1, \"div\", 198)(2, \"button\", 199);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_16_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r260);\n      const ctx_r259 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r259.closeInfoDialog());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 200)(6, \"div\", 201)(7, \"span\");\n    i0.ɵɵtext(8, \"Detailed Info\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 202)(10, \"table\", 203)(11, \"tbody\")(12, \"tr\")(13, \"td\", 204)(14, \"strong\");\n    i0.ɵɵtext(15, \"Gross Weight\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 205);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 206);\n    i0.ɵɵtext(19, \"GM/ML\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"tr\")(21, \"td\", 204)(22, \"strong\");\n    i0.ɵɵtext(23, \"Portion Weight\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\", 205);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 206);\n    i0.ɵɵtext(27, \"GM/ML\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(28, \"br\");\n    i0.ɵɵelementStart(29, \"tr\")(30, \"td\", 204)(31, \"strong\");\n    i0.ɵɵtext(32, \"Gross Cost\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"td\", 205);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 206);\n    i0.ɵɵtext(36, \"Rs\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"tr\")(38, \"td\", 204)(39, \"strong\");\n    i0.ɵɵtext(40, \"Portion Cost\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"td\", 205);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 206);\n    i0.ɵɵtext(44, \"Rs\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate(ctx_r14.notify.truncateAndFloor(ctx_r14.registrationForm.value.weightInUse));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r14.notify.truncateAndFloor(ctx_r14.getWeightPerPortion()));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r14.notify.truncateAndFloor(ctx_r14.registrationForm.value.rate));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r14.notify.truncateAndFloor(ctx_r14.getCostPerPortion()));\n  }\n}\nfunction ActionComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r262 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 197)(1, \"div\", 198)(2, \"button\", 199);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_18_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r262);\n      const ctx_r261 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r261.closeInfoDialog());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 200)(6, \"div\", 201)(7, \"span\");\n    i0.ɵɵtext(8, \"Detailed Info\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 202)(10, \"table\", 203)(11, \"tbody\")(12, \"tr\")(13, \"td\", 204)(14, \"strong\");\n    i0.ɵɵtext(15, \"WAC Per KG\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 205);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 206);\n    i0.ɵɵtext(19, \"Rs\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate(ctx_r16.notify.truncateAndFloor(ctx_r16.getPerKGCost(), 2));\n  }\n}\nfunction ActionComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r264 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 197)(1, \"div\", 198)(2, \"button\", 199);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_20_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r264);\n      const ctx_r263 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r263.closeInfoDialog());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 1)(6, \"div\", 201)(7, \"span\");\n    i0.ɵɵtext(8, \"Confirm Ingredient Deletion\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 207)(10, \"div\", 208);\n    i0.ɵɵtext(11, \" Deleting this ingredient is permanent and will impact past records. Alternatively, you can choose to discontinue it \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 209)(13, \"button\", 210);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_20_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r264);\n      const ctx_r265 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r265.deleteData());\n    });\n    i0.ɵɵtext(14, \" Yes, Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 211);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_20_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r264);\n      const ctx_r266 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r266.discontinueData());\n    });\n    i0.ɵɵtext(16, \" Just, Discontinue \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction ActionComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r268 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 29)(2, \"span\");\n    i0.ɵɵtext(3, \"Discontinued Location\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 212);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 213)(7, \"button\", 214);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r268);\n      const ctx_r267 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r267.discontinuedSelectData());\n    });\n    i0.ɵɵtext(8, \" Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 215);\n    i0.ɵɵlistener(\"click\", function ActionComponent_ng_template_22_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r268);\n      const ctx_r269 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r269.closeDiscontinuedDialog());\n    });\n    i0.ɵɵtext(10, \" No\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Would you like to discontinue \", ctx_r20.selectedData, \" ? \");\n  }\n}\nclass ActionComponent {\n  constructor(data, fb, api, activatedRoute, router, dialog, sharedData, cache, dialogData, notify, renderer, el, auth, cd, masterDataService) {\n    this.data = data;\n    this.fb = fb;\n    this.api = api;\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.dialog = dialog;\n    this.sharedData = sharedData;\n    this.cache = cache;\n    this.dialogData = dialogData;\n    this.notify = notify;\n    this.renderer = renderer;\n    this.el = el;\n    this.auth = auth;\n    this.cd = cd;\n    this.masterDataService = masterDataService;\n    this.question = 'Would you like to add \"';\n    this.itemNameControl = new FormControl('');\n    this.isUpdateActive = false;\n    this.costDialogkey = false;\n    this.dataSource = new MatTableDataSource([]);\n    this.subRecipeData = [];\n    this.showSRR = false;\n    this.updateSRR = false;\n    this.isReadOnly = true;\n    this.loadBtn = false;\n    this.AccessibleUOM = [];\n    this.ingredientUOM = ['GM', 'ML', 'NOS', 'MM', 'PORTION'];\n    this.Bank = [];\n    this.IngredientFilterCtrl = new FormControl();\n    this.ingredientNames = new ReplaySubject(1);\n    this.usedInWorkAreaBank = [];\n    this.usedInWorkAreaFilterCtrl = new FormControl();\n    this.usedInWorkAreaNames = new ReplaySubject(1);\n    this.preparedBank = [];\n    this.preparedFilterCtrl = new FormControl();\n    this.preparedLocationNames = new ReplaySubject(1);\n    this.outletBank = [];\n    this.outletFilterCtrl = new FormControl();\n    this.outletLocationNames = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.baseData = {};\n    this.updateBtnActive = false;\n    this.loadSpinnerForApi = false;\n    this.loadSpinnerForApiSRR = false;\n    this.isEditable = false;\n    this.isSRRDataReady = false;\n    this.units = ['Portion', 'GM/ML'];\n    this.ratio = 1;\n    this.loadSrmBtn = true;\n    this.loadSrrBtn = true;\n    this.isButtonDisabled = false;\n    this.showWeightError = false;\n    this.showDeleteItems = false;\n    this.discontinueDatas = [];\n    this.removedItem = [];\n    this.discData = [];\n    this.disableOption = false;\n    this.discontinuedPreLocData = [];\n    this.defaultPreLocData = [];\n    this.discontinuedOutletData = [];\n    this.defaultOutletData = [];\n    this.discontinuedIssuedToData = [];\n    this.defaultIssuedToData = [];\n    this.globalLocation = this.sharedData.getGlLocation().value;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    this.newCategory = this.baseData['Subrecipe Master'].map(cat => cat.category.toUpperCase());\n    let tenantId = this.user.tenantId;\n    this.api.getRolesListDiscontinuedLocations(tenantId).subscribe(res => {\n      if (res['result'] == 'success' && res['discontinuedLocations']) {\n        this.discontinuedLocations = res['discontinuedLocations'];\n      }\n      this.cd.detectChanges();\n    });\n    this.registrationForm = this.fb.group({\n      category: ['', Validators.required],\n      subCategory: ['', Validators.required],\n      menuItemCode: ['', Validators.required],\n      menuItemName: ['', Validators.required],\n      closingUOM: ['', Validators.required],\n      itemType: ['SubRecipe', Validators.required],\n      preparedAt: ['', Validators.required],\n      usedAtOutlet: ['', Validators.required],\n      usedInWorkArea: ['', Validators.required],\n      uom: ['', Validators.required],\n      weightInUse: [0, Validators.required],\n      yield: [1, [Validators.required, this.yieldValidator()]],\n      portion: [1, [Validators.required, this.portionValidator()]],\n      unit: ['Portion', [Validators.required]],\n      recovery: [0, Validators.required],\n      rate: [0, Validators.required],\n      finalRate: [0],\n      discontinued: ['no', Validators.required],\n      row_uuid: ['']\n    });\n    this.registrationForm.get('yield').markAsTouched();\n    this.registrationForm.get('yield').markAsDirty();\n    this.subRecipeRecipeForm = this.fb.group({\n      subRecipeCode: ['', Validators.required],\n      subRecipeName: ['', Validators.required],\n      ingredientCode: ['', Validators.required],\n      ingredientName: ['', Validators.required],\n      uom: ['', Validators.required],\n      initialWeight: [0],\n      defaultUOM: ['', Validators.required],\n      portionCount: [0],\n      yield: [1, [Validators.required, this.yieldValidator()]],\n      loss: [0],\n      weightInUse: [0, Validators.required],\n      rate: [0, Validators.required],\n      finalRate: [0],\n      discontinued: ['no'],\n      row_uuid: ['']\n    });\n    this.isDuplicate = this.dialogData.key;\n    this.costDialogkey = this.dialogData.costDialogkey;\n    this.sharedData.getItemNames.subscribe(obj => {\n      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(startWith(''), map(value => this._filter(value || '', obj.menuItemName, '')));\n      this.preparedAtLocation = obj.locations;\n      this.usedAtOutletLocation = obj.workAreas;\n    });\n    this.getMenuRecipes();\n    this.getCategories();\n    this.readIPConfig();\n  }\n  yieldValidator() {\n    return control => {\n      const isValid = control.value > 0;\n      return isValid ? null : {\n        yieldInvalid: {\n          value: control.value\n        }\n      };\n    };\n  }\n  portionValidator() {\n    return control => {\n      const isValid = control.value > 0;\n      return isValid ? null : {\n        portionInvalid: {\n          value: control.value\n        }\n      };\n    };\n  }\n  reflectSubRecipe() {\n    let weightInUse = this.getTotal('Initialweight');\n    this.registrationForm.get('weightInUse').setValue(weightInUse);\n    let recovery = this.registrationForm.value['yield'] * weightInUse;\n    this.registrationForm.get('recovery').setValue(this.notify.truncateAndFloor(recovery));\n    let rate = this.getSRRTotal('finalRate');\n    this.registrationForm.get('rate').setValue(rate);\n    let finalRate = rate / recovery;\n    this.registrationForm.get('finalRate').setValue(finalRate);\n    this.cd.detectChanges();\n  }\n  getMenuRecipes() {\n    let currentInvItems = this.cache.getInvItems().value;\n    if (currentInvItems.hasOwnProperty(this.globalLocation['restaurantIdOld'])) {\n      this.invItems = currentInvItems[this.globalLocation['restaurantIdOld']];\n      // this.Bank = this.invItems.map((item) => item.itemName);\n      this.subData = this.invItems.filter(item => item.itemName && item.ItemType === \"SubRecipe\").map(item => item.itemName);\n      let invData = this.invItems.filter(item => item.itemName && item.ItemType === \"Inventory\").map(item => item.itemName);\n      this.Bank = [...this.subData, ...invData];\n      this.ingredientData = [...new Set(this.Bank)];\n      this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.ingredientData, 'ingredients')));\n      this.ingredientNames.next(this.Bank.slice());\n      this.IngredientFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n        this.Filter(this.Bank, this.IngredientFilterCtrl, this.ingredientNames);\n      });\n      this.getLocationCall();\n    } else {\n      let obj = {};\n      obj['tenantId'] = this.user.tenantId;\n      obj['restaurantId'] = this.globalLocation['restaurantIdOld'];\n      this.api.getInventoryListForSubrecipeMD(obj).subscribe({\n        next: res => {\n          if (res['success']) {\n            // currentInvItems[this.globalLocation['restaurantIdOld']] =\n            //   res['invList'];\n            // this.cache.setInvItems(currentInvItems);\n            this.invItems = res['invList'];\n            this.subData = this.invItems.filter(item => item.itemName && item.ItemType === \"SubRecipe\").map(item => item.itemName);\n            let invData = this.invItems.filter(item => item.itemName && item.ItemType === \"Inventory\").map(item => item.itemName);\n            this.Bank = [...this.subData, ...invData];\n            this.ingredientData = [...new Set(this.Bank)];\n            this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.ingredientData, 'ingredients')));\n            this.ingredientNames.next(this.Bank.slice());\n            this.IngredientFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n              this.Filter(this.Bank, this.IngredientFilterCtrl, this.ingredientNames);\n            });\n            this.getLocationCall();\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n  Filter(bank, form, data) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    data.next(bank.filter(data => data.toLowerCase().indexOf(search) > -1));\n  }\n  FilterIssued(bank, form, data) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    const filteredBank = bank.map(item => {\n      const filteredWorkAreas = item.workAreas.filter(workArea => workArea.toLowerCase().indexOf(search) > -1);\n      return {\n        ...item,\n        workAreas: filteredWorkAreas\n      };\n    });\n    data.next(filteredBank);\n  }\n  submit() {\n    this.loadSpinnerForApi = true;\n    this.showWorkAreaError = false;\n    this.baseData = this.sharedData.getBaseData().value;\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      // this.checkWorkArea();\n      if (this.showWorkAreaError) {\n        this.registrationForm.markAllAsTouched();\n        this.notify.snackBarShowError('Please fill out all required fields');\n        this.loadSpinnerForApi = false;\n        this.cd.detectChanges();\n      } else {\n        let updatedSRMData = this.convertSRMKeys();\n        updatedSRMData['modified'] = 'yes';\n        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');\n        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');\n        updatedSRMData['usedInWorkArea'] = updatedSRMData['usedInWorkArea'].join(',');\n        let data = this.baseData;\n        if (Object.keys(this.baseData).length > 0) {\n          let tempObj = {};\n          tempObj['Subrecipe Master'] = data['Subrecipe Master'];\n          let requiredVendor = tempObj['Subrecipe Master'].find(el => el.menuItemCode == updatedSRMData['menuItemCode']);\n          if (requiredVendor) {\n            this.notify.snackBarShowInfo('Item code already used');\n            this.loadSpinnerForApi = false;\n            this.cd.detectChanges();\n          } else {\n            this.dataSource.data.forEach(el => {\n              el['Initialweight'] = el['weightInUse'] / el['yield'];\n            });\n            tempObj['Subrecipe Master'].unshift(updatedSRMData);\n            tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(item => item.modified === 'yes');\n            tempObj['Subrecipe Recipe'] = this.dataSource.data;\n            let obj = {};\n            obj['tenantId'] = this.user.tenantId;\n            obj['userEmail'] = this.user.email;\n            obj['data'] = tempObj;\n            obj['type'] = 'recipe';\n            obj['category'] = 'subRecipe';\n            this.api.updateData(obj).pipe(first()).subscribe({\n              next: res => {\n                if (res['success']) {\n                  this.updateBaseDataForSRR();\n                  this.loadSpinnerForApi = false;\n                  this.close();\n                  this.cd.detectChanges();\n                  this.notify.snackBarShowSuccess('Sub-recipe added successfully');\n                }\n              },\n              error: err => {\n                console.log(err);\n              }\n            });\n          }\n        } else {\n          this.loadSpinnerForApi = false;\n          this.cd.detectChanges();\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: updatedSRMData['menuItemCode'],\n          subRecipeName: updatedSRMData['menuItemName']\n        });\n      }\n    }\n  }\n  selectIngredientsName(itemName) {\n    const item = this.invItems.find(item => {\n      return item.itemName === itemName;\n    });\n    let uom;\n    if (item['uom'] == 'KG' || item['uom'] == 'kg' || item['uom'] == 'Kg') {\n      uom = 'GM';\n    } else if (item['uom'] == 'LITRE' || item['uom'] == 'litre' || item['uom'] == 'Litre') {\n      uom = 'ML';\n    } else if (item['uom'] == 'NOS' || item['uom'] == 'nos' || item['uom'] == 'Nos') {\n      uom = 'NOS';\n    } else {\n      uom = 'MM';\n    }\n    this.AccessibleUOM = item['ItemType'] === 'SubRecipe' && item['portionWeight'] != 0 ? [uom, \"PORTION\"] : [uom];\n    let conversionCoefficient;\n    conversionCoefficient = item['uom'] == 'NOS' ? 1 : 1000;\n    let rate = item ? (item.hasOwnProperty('packageQty') ? item['packageQty'] : 1) / conversionCoefficient * item['withTaxPrice'] : 0;\n    this.subRecipeRecipeForm.patchValue({\n      weightInUse: 0,\n      ingredientCode: item['itemCode'],\n      uom: uom,\n      defaultUOM: uom,\n      yield: item['yield'] ?? 1,\n      rate: this.notify.truncateAndFloor(rate),\n      finalRate: 0\n    });\n  }\n  isOptionAccessible(option) {\n    return this.AccessibleUOM.includes(option);\n  }\n  uomChange() {\n    this.subRecipeRecipeForm.get('portionCount').setValue(1);\n    this.getPortionWeightForSubRecipe();\n  }\n  getPortionWeightForSubRecipe() {\n    let requiredItem = this.invItems.find(item => item.itemCode == this.subRecipeRecipeForm.value.ingredientCode);\n    let portionWeight = this.notify.truncateAndFloor(requiredItem['portionWeight']) * this.subRecipeRecipeForm.value.portionCount;\n    this.subRecipeRecipeForm.get('weightInUse').setValue(portionWeight);\n    this.sumForFinalRateSRR('Total');\n  }\n  convertPortionToUOM() {\n    this.getPortionWeightForSubRecipe();\n    this.cd.detectChanges();\n  }\n  update() {\n    this.deleteDataFormDB();\n    this.loadSpinnerForApi = true;\n    this.showWorkAreaError = false;\n    this.baseData = this.sharedData.getBaseData().value;\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      // this.checkWorkArea();\n      if (this.showWorkAreaError) {\n        this.registrationForm.markAllAsTouched();\n        this.notify.snackBarShowError('Please fill out all required fields');\n        this.loadSpinnerForApi = false;\n        this.cd.detectChanges();\n      } else {\n        this.setDiscontinuedDataInRolopos();\n        let updatedSRMData = this.convertSRMKeys();\n        updatedSRMData['modified'] = 'yes';\n        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');\n        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');\n        updatedSRMData['usedInWorkArea'] = updatedSRMData['usedInWorkArea'].join(',');\n        updatedSRMData['preparedAtDiscontinued'] = this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData.join(',') : '';\n        updatedSRMData['usedAtOutletDiscontinued'] = this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData.join(',') : '';\n        if (this.discontinuedIssuedToData.length > 0) {\n          const workAreas = this.discontinuedIssuedToData.flatMap(item => item.workAreas);\n          // updated['issuedToDiscontinued'] = workAreas.length > 0 ? workAreas.join(',') : '';\n          updatedSRMData['issuedToDiscontinued'] = workAreas > 0 ? workAreas.join(',') : '';\n        }\n        if (Object.keys(this.baseData).length > 0) {\n          let tempObj = {};\n          tempObj['Subrecipe Master'] = this.baseData['Subrecipe Master'];\n          let requiredVendor = tempObj['Subrecipe Master'].find(el => el.menuItemCode == updatedSRMData['menuItemCode']);\n          let index = tempObj['Subrecipe Master'].indexOf(requiredVendor);\n          this.dataSource.data.forEach(el => {\n            el.UOM = el.defaultUOM;\n            el['Initialweight'] = el['weightInUse'] / el['yield'];\n          });\n          tempObj['Subrecipe Master'][index] = updatedSRMData;\n          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(item => item.modified === 'yes');\n          this.dataSource.data = [...this.dataSource.data, ...this.removedItem, ...this.discData];\n          tempObj['Subrecipe Recipe'] = this.dataSource.data;\n          this.dataSource.data = this.dataSource.data.filter(item => item.delete !== true);\n          this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes');\n          this.baseData['Subrecipe Recipe'].filter(el => el['ingredientCode'] === updatedSRMData['menuItemCode'] && !['', null, undefined].includes(el.portionCount)).forEach(item => {\n            let portionWeight = this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion);\n            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight);\n            item['Initialweight'] = this.notify.truncateAndFloor(item['weightInUse'] / this.notify.truncateAndFloor(item['yield']));\n            item['modified'] = 'yes';\n            this.updateSubRecipeMaster(item['subRecipeCode']);\n          });\n          this.baseData['menu recipes'].filter(el => el['ingredientCode'] === updatedSRMData['menuItemCode'] && !['', null, undefined].includes(el.portionCount)).forEach(item => {\n            let portionWeight = this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion);\n            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight);\n            item['InitialWeight'] = this.notify.truncateAndFloor(item['weightInUse'] / this.notify.truncateAndFloor(item['Yield']));\n            item['modified'] = 'yes';\n            this.updateMenuMaster(item['menuItemCode']);\n          });\n          let updatedMenuMaster, updatedMenuRecipe, updatedSubRecipeMaster, updatedSubRecipeRecipe;\n          updatedMenuRecipe = this.baseData['menu recipes'].filter(el => el['modified'] === 'yes');\n          updatedMenuMaster = this.baseData['menu master'].filter(el => el['modified'] === 'yes');\n          updatedSubRecipeMaster = this.baseData['Subrecipe Master'].filter(el => el['modified'] === 'yes' && el['menuItemCode'] != updatedSRMData['menuItemCode']);\n          updatedSubRecipeRecipe = this.baseData['Subrecipe Recipe'].filter(el => el['modified'] === 'yes');\n          tempObj['menu recipes'] = updatedMenuRecipe;\n          tempObj['menu master'] = updatedMenuMaster;\n          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].concat(updatedSubRecipeMaster);\n          tempObj['Subrecipe Recipe'] = tempObj['Subrecipe Recipe'].concat(updatedSubRecipeRecipe);\n          let obj = {};\n          obj['tenantId'] = this.user.tenantId;\n          obj['userEmail'] = this.user.email;\n          obj['data'] = tempObj;\n          obj['type'] = 'recipe';\n          obj['category'] = 'subRecipe';\n          this.api.updateData(obj).pipe(first()).subscribe({\n            next: res => {\n              if (res['success']) {\n                this.loadSpinnerForApi = false;\n                this.cd.detectChanges();\n                this.updateBaseData(tempObj);\n                this.updateBaseDataForSRR();\n                this.notify.snackBarShowSuccess('Sub-recipe updated successfully');\n                this.close();\n              }\n            },\n            error: err => {\n              console.log(err);\n            }\n          });\n        } else {\n          this.loadSpinnerForApi = false;\n        }\n      }\n    }\n  }\n  updateBaseDataForSRR() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['userEmail'] = this.user.email;\n    obj['type'] = 'recipe';\n    obj['specific'] = 'Subrecipe Recipe';\n    if (this.registrationForm.value) {\n      obj['itemCode'] = this.registrationForm.value.menuItemCode;\n    }\n    this.api.getPresentData(obj).subscribe({\n      next: res => {\n        if (res['success']) {\n          let latestSRR = res['data'][0] ?? res['data'];\n          let currentSRR = this.baseData['Subrecipe Recipe'];\n          latestSRR['Subrecipe Recipe'].forEach(item => {\n            const exist = currentSRR.findIndex(el => el.ingredientCode == item['ingredientCode'] && el['subRecipeCode'] === item['subRecipeCode']);\n            if (exist !== -1) {\n              currentSRR[exist] = item;\n            } else {\n              currentSRR.push(item);\n            }\n          });\n          this.baseData['Subrecipe Recipe'] = currentSRR;\n          this.sharedData.setBaseData(this.baseData);\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  updateMenuMaster(itemCode) {\n    const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === itemCode);\n    let revisedTotalWeight, requiredItems;\n    requiredItems = this.baseData['menu recipes'].filter(item => item['menuItemCode'] === itemCode);\n    revisedTotalWeight = requiredItems.reduce((sum, item) => {\n      return sum + this.notify.truncateAndFloor(item.weightInUse);\n    }, 0);\n    let requiredData = this.baseData['menu master'].find(el => el['menuItemCode'] == itemCode);\n    if (requiredData) {\n      requiredData['weight'] = revisedTotalWeight;\n      requiredData['modified'] = 'yes';\n      if (index !== -1) {\n        this.baseData['menu master'][index] = requiredData;\n      }\n    }\n  }\n  updateSubRecipeMaster(itemCode) {\n    const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === itemCode);\n    let revisedTotalWeight, requiredItems;\n    requiredItems = this.baseData['Subrecipe Recipe'].filter(item => item['subRecipeCode'] === itemCode);\n    revisedTotalWeight = requiredItems.reduce((sum, item) => {\n      return sum + this.notify.truncateAndFloor(item.weightInUse);\n    }, 0);\n    let requiredData = this.baseData['Subrecipe Master'].find(el => el['menuItemCode'] == itemCode);\n    if (requiredData) {\n      requiredData['weightInUse'] = revisedTotalWeight;\n      requiredData['recovery'] = revisedTotalWeight * requiredData['yield'];\n      requiredData['modified'] = 'yes';\n      if (index !== -1) {\n        this.baseData['Subrecipe Master'][index] = requiredData;\n      }\n    }\n  }\n  // ######################## AUTO COMPLETION ########################\n  _filter(value, input, data) {\n    let filterValue = value.toLowerCase();\n    this.filtered = input.filter(option => option.toLowerCase().includes(filterValue));\n    if (this.filtered.length == 0) {\n      if (data == 'ingredients') {\n        // this.disableOption = true;\n        this.filtered = ['No Item Found'];\n      } else {\n        this.filtered = [this.question + value + '\"'];\n      }\n    }\n    return this.filtered;\n  }\n  checkItem(event) {\n    let invItem = this.sharedData.getDataForFillTheForm(event.target.value, 'Subrecipe Master');\n    if (invItem) {\n      this.updateBtnActive = true;\n    } else {\n      this.updateBtnActive = false;\n    }\n  }\n  optionSelected(type, option) {\n    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'Subrecipe Master');\n    if (invItem) {\n      this.updateBtnActive = true;\n    } else {\n      this.updateBtnActive = false;\n    }\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOption(type);\n    }\n  }\n  addOption(type) {\n    this.loadBtn = true;\n    if (type == 'package') {\n      this.itemNameControl.reset();\n    } else if (type == 'Subrecipe Master') {\n      let subRecipeMasterItem = this.sharedData.getDataForFillTheForm(this.itemNameControl.value, 'Subrecipe Master');\n      if (subRecipeMasterItem) {\n        this.isUpdateActive = true;\n        this.setValuesForForm(subRecipeMasterItem);\n      } else {\n        this.generateCode('subrecipeMasterCode');\n      }\n      this.registrationForm.controls['menuItemName'].patchValue(this.removePromptFromOption(this.itemNameControl.value));\n      this.itemNameControl.reset();\n      this.isDuplicate = false;\n    }\n    this.dataSource.data = [];\n    this.loadBtn = false;\n    this.getBaseData();\n  }\n  generateCode(code) {\n    var _this = this;\n    let obj = {};\n    let data;\n    obj['tenantId'] = this.user.tenantId;\n    obj['code'] = code;\n    this.api.getCode(obj).pipe(first()).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (res) {\n          if (res['success']) {\n            data = res['data'];\n            _this.registrationForm.get('menuItemCode').setValue(data);\n          }\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()\n    });\n  }\n  setValuesForForm(subRecipeMasterItem) {\n    if (!Array.isArray(subRecipeMasterItem['usedInWorkArea']) && !Array.isArray(subRecipeMasterItem['usedAtOutlet']) && !Array.isArray(subRecipeMasterItem['preparedAt'])) {\n      subRecipeMasterItem['usedInWorkArea'] = subRecipeMasterItem['usedInWorkArea'].split(',');\n      subRecipeMasterItem['usedAtOutlet'] = subRecipeMasterItem['usedAtOutlet'].split(',');\n      subRecipeMasterItem['preparedAt'] = subRecipeMasterItem['preparedAt'].split(',');\n    }\n    let recovery = this.notify.truncateAndFloor(subRecipeMasterItem['recovery']);\n    let portion = subRecipeMasterItem.hasOwnProperty('portion') && subRecipeMasterItem['portion'] ? subRecipeMasterItem['portion'] : 1;\n    this.defaultPreLocData = subRecipeMasterItem['preparedAt'];\n    this.defaultOutletData = subRecipeMasterItem['usedAtOutlet'];\n    this.defaultIssuedToData = subRecipeMasterItem['usedInWorkArea'];\n    if (this.discontinuedLocations && this.discontinuedLocations != undefined && this.discontinuedLocations.subRecipeLocations) {\n      this.discontinuedOutletData.push(...this.discontinuedLocations.subRecipeLocations.outLetDiscontinued);\n      this.discontinuedIssuedToData.push(...this.discontinuedLocations.subRecipeLocations.issuedToDiscontinued);\n      this.discontinuedPreLocData.push(...this.discontinuedLocations.subRecipeLocations.procuredAtDiscontinued);\n    }\n    this.registrationForm.patchValue({\n      category: subRecipeMasterItem['category'],\n      subCategory: subRecipeMasterItem['subCategory'],\n      menuItemCode: subRecipeMasterItem['menuItemCode'],\n      menuItemName: subRecipeMasterItem['menuItemName'],\n      closingUOM: subRecipeMasterItem['closingUOM'],\n      itemType: 'SubRecipe',\n      preparedAt: subRecipeMasterItem['preparedAt'],\n      usedAtOutlet: subRecipeMasterItem['usedAtOutlet'],\n      uom: subRecipeMasterItem['UOM'],\n      weightInUse: subRecipeMasterItem['weightInUse'],\n      portion: portion,\n      yield: subRecipeMasterItem['yield'],\n      recovery: recovery,\n      rate: this.notify.truncateAndFloor(subRecipeMasterItem['rate']),\n      finalRate: this.notify.truncateAndFloor(subRecipeMasterItem['finalRate']),\n      discontinued: ['no', 'NO', 'No', 'N', null, ''].includes(subRecipeMasterItem['Discontinued']) ? 'no' : 'yes',\n      row_uuid: subRecipeMasterItem['row_uuid']\n    });\n    this.loadSrmBtn = false;\n    this.locationChange(subRecipeMasterItem['usedAtOutlet']);\n    this.registrationForm.get('usedInWorkArea').patchValue(subRecipeMasterItem['usedInWorkArea']);\n  }\n  removePromptFromOption(option) {\n    if (option.startsWith(this.question)) {\n      option = option.substring(this.question.length, option.length - 1);\n    }\n    return option;\n  }\n  close() {\n    this.dataSource.data = [];\n    this.masterDataService.setNavigation('Subrecipe Master');\n    this.router.navigate(['/dashboard/home']);\n    this.dialog.closeAll();\n  }\n  editFun(element, addSRR) {\n    const item = this.invItems.find(item => item.itemName.toLowerCase() === element.ingredientName.toLowerCase());\n    this.updateSRR = true;\n    this.subRecipeRecipeForm.patchValue({\n      ingredientCode: element['ingredientCode'],\n      // ingredientName: item ? item.ingredientName : '',\n      ingredientName: element['ingredientName'],\n      subRecipeCode: element['subRecipeCode'],\n      subRecipeName: element['subRecipeName'],\n      uom: element['UOM'],\n      defaultUOM: element['defaultUOM'],\n      yield: element['yield'],\n      rate: this.notify.truncateAndFloor(element['rate']),\n      finalRate: this.notify.truncateAndFloor(element['finalRate']),\n      initialWeight: element['Initialweight'],\n      loss: element['loss'],\n      weightInUse: element['weightInUse'],\n      discontinued: ['no', 'NO', 'No', 'N', null, ''].includes(element['Discontinued']) ? 'no' : 'yes',\n      row_uuid: element['row_uuid']\n    });\n    if (element.hasOwnProperty('portionCount')) {\n      this.subRecipeRecipeForm.get('portionCount').setValue(element['portionCount']);\n    }\n    if (!element['Initialweight']) {\n      this.subRecipeRecipeForm.get('initialWeight').setValue(0);\n    }\n    if (!element['loss']) {\n      this.subRecipeRecipeForm.get('loss').setValue(0);\n    }\n    let dialogRef = this.dialog.open(addSRR, {\n      maxHeight: '95vh',\n      maxWidth: '50vw'\n    });\n    dialogRef.afterClosed().subscribe(result => {});\n    this.closeSRRRef = dialogRef;\n    this.loadSrrBtn = false;\n  }\n  closeSRRDialog() {\n    this.updateSRR = false;\n    // this.subRecipeRecipeForm.reset();\n    this.closeSRRRef.close();\n  }\n  addNewSubRecipeRecipe() {\n    this.subRecipeRecipeForm.patchValue({\n      subRecipeCode: this.registrationForm.value.menuItemCode,\n      subRecipeName: this.registrationForm.value.menuItemName\n    });\n    if (this.subRecipeRecipeForm.invalid) {\n      this.subRecipeRecipeForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.cd.detectChanges();\n    } else {\n      if (this.subRecipeRecipeForm.value.weightInUse > 0) {\n        let update = this.convertSRRKeys();\n        update['modified'] = 'yes';\n        let existingItem = this.dataSource.data.find(el => el.ingredientCode === update['ingredientCode']);\n        if (!existingItem) {\n          Object.entries(update).forEach(([key, value]) => {\n            if (value === null || value === undefined || value === '') {\n              return;\n            }\n            if (typeof value === 'number') {\n              update[key] = this.notify.truncateAndFloor(value);\n            }\n          });\n          this.dataSource.data.unshift(update);\n          this.dataSource.paginator = this.paginator;\n          this.updateSRR = false;\n          this.reflectSubRecipe();\n          // this.notify.snackBarShowSuccess('Created successfully');\n          this.subRecipeRecipeForm.reset();\n          this.subRecipeRecipeForm.patchValue({\n            subRecipeCode: this.registrationForm.value.menuItemCode,\n            subRecipeName: this.registrationForm.value.menuItemName,\n            yield: 1\n          });\n          this.tempData = this.dataSource.data;\n          this.clearForm();\n        } else {\n          this.notify.snackBarShowWarning('Ingredient already exists!');\n        }\n      } else {\n        this.subRecipeRecipeForm.markAllAsTouched();\n        this.showWeightError = true;\n        // this.notify.snackBarShowError('Please fill out all required fields');\n        this.cd.detectChanges();\n      }\n      this.cd.detectChanges();\n    }\n    this.clearForm();\n  }\n  clearForm() {\n    Object.keys(this.subRecipeRecipeForm.controls).forEach(key => {\n      this.subRecipeRecipeForm.get(key)?.setErrors(null);\n    });\n  }\n  editExistingSubRecipeRecipe() {\n    if (this.subRecipeRecipeForm.invalid) {\n      this.subRecipeRecipeForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.cd.detectChanges();\n    } else {\n      let updatedSRRData = this.convertSRRKeys();\n      updatedSRRData['modified'] = 'yes';\n      let requiredPackage = this.dataSource.data.find(el => el.ingredientCode == updatedSRRData['ingredientCode'] && el.subRecipeCode == updatedSRRData['subRecipeCode']);\n      if (requiredPackage) {\n        let data = this.tempData.filter(item => item.Discontinued !== 'yes');\n        let tempDiscData = [];\n        tempDiscData = this.discData.filter(el => el.ingredientCode === updatedSRRData['ingredientCode'] && el.subRecipeCode === updatedSRRData['subRecipeCode']);\n        if (tempDiscData.length > 0) {\n          tempDiscData[0].Discontinued = 'no';\n          tempDiscData[0].ingredientName = tempDiscData[0].ingredientName.toUpperCase();\n          this.discData.pop(tempDiscData);\n        }\n        this.dataSource.data = [...data, ...tempDiscData];\n        let index = this.dataSource.data.indexOf(requiredPackage);\n        this.dataSource.data[index] = updatedSRRData;\n        let items = this.dataSource.data;\n        // this.dataSource.data = items.filter(item => item.Discontinued !== 'yes')\n        this.dataSource.data = items.filter(item => !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n        this.tempData = this.dataSource.data;\n        let disData = items.filter(item => ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n        // let disData = items.filter(item => item.Discontinued === 'yes')\n        this.discData.push(...disData);\n        this.dataSource.paginator = this.paginator;\n        this.reflectSubRecipe();\n        this.notify.snackBarShowSuccess('Updated successfully');\n        this.subRecipeRecipeForm.reset();\n        this.clearForm();\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: this.registrationForm.value.menuItemCode,\n          subRecipeName: this.registrationForm.value.menuItemName,\n          yield: 1\n        });\n        this.closeSRRDialog();\n        this.showDeleteItems = false;\n        this.cd.detectChanges();\n      }\n    }\n  }\n  goBack() {\n    this.isDuplicate = false;\n    this.showSRR = false;\n    this.subRecipeRecipeForm.reset();\n    setTimeout(() => {\n      const dataSourceTable = this.el.nativeElement.querySelector('.section');\n      if (dataSourceTable) {\n        dataSourceTable.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n      }\n    }, 100);\n  }\n  convertSRMKeys() {\n    const keyData = [['category', 'category'], ['subCategory', 'subCategory'], ['menuItemCode', 'menuItemCode'], ['menuItemName', 'menuItemName'], ['closingUOM', 'closingUOM'], ['itemType', 'itemType'], ['preparedAt', 'preparedAt'], ['usedAtOutlet', 'usedAtOutlet'], ['usedInWorkArea', 'usedInWorkArea'], ['UOM', 'uom'], ['weightInUse', 'weightInUse'], ['yield', 'yield'], ['recovery', 'recovery'], ['rate', 'rate'], ['finalRate', 'finalRate'], ['Discontinued', 'discontinued'], ['closingConversion', 'discontinued'], ['modified', 'modified'], ['portion', 'portion'], ['row_uuid', 'row_uuid']];\n    const updatedRecipeData = {};\n    keyData.forEach(key => {\n      let value = this.registrationForm.value[key[1]];\n      if (key[0] == 'taxRate') {\n        updatedRecipeData[key[0]] = value || 0;\n      } else if (key[0] == 'portion') {\n        if (this.registrationForm.value.unit === 'GM/ML') {\n          value = this.registrationForm.value.recovery / value;\n        }\n        updatedRecipeData[key[0]] = this.notify.truncateAndFloor(value) || 1;\n      } else {\n        updatedRecipeData[key[0]] = value || '';\n      }\n    });\n    return updatedRecipeData;\n  }\n  convertSRRKeys() {\n    const keyData = [['subRecipeCode', 'subRecipeCode'], ['subRecipeName', 'subRecipeName'], ['ingredientCode', 'ingredientCode'], ['ingredientName', 'ingredientName'], ['UOM', 'uom'], ['Initialweight', 'initialWeight'], ['defaultUOM', 'defaultUOM'], ['yield', 'yield'], ['weightInUse', 'weightInUse'], ['rate', 'rate'], ['finalRate', 'finalRate'], ['Discontinued', 'discontinued'], ['row_uuid', 'row_uuid'], ['modified', 'modified'], ['portionCount', 'portionCount']];\n    const updatedSubRecipeData = {};\n    keyData.forEach(key => {\n      let value = this.subRecipeRecipeForm.value[key[1]];\n      if (key[0] === 'Initialweight') {\n        let formattedRec = this.notify.truncateAndFloor(this.subRecipeRecipeForm.value['weightInUse'] / this.subRecipeRecipeForm.value['yield']);\n        updatedSubRecipeData[key[0]] = formattedRec;\n      } else {\n        updatedSubRecipeData[key[0]] = value || '';\n      }\n    });\n    return updatedSubRecipeData;\n  }\n  applyFilter(filterValue) {\n    filterValue = filterValue.target.value;\n    filterValue = filterValue.trim();\n    filterValue = filterValue.toLowerCase();\n    this.dataSource.filter = filterValue;\n  }\n  toggleSelectAll() {\n    const control = this.registrationForm.controls['usedInWorkArea'];\n    let data = [...this.usedInWorkAreaBank.map(location => location.workAreas)];\n    const flattenedArray = [].concat(...data);\n    if (control.value.length - 1 === flattenedArray.length) {\n      control.setValue([]);\n    } else {\n      control.setValue(flattenedArray);\n    }\n  }\n  usedOutLetToggleSelectAll() {\n    const control = this.registrationForm.controls['usedAtOutlet'];\n    if (control.value.length - 1 === this.outletBank.length) {\n      control.setValue(this.defaultOutletData);\n    } else {\n      control.setValue(this.outletBank);\n    }\n    this.locationChange(this.registrationForm.value.usedAtOutlet);\n  }\n  preparedToggleSelectAll() {\n    const control = this.registrationForm.controls['preparedAt'];\n    if (control.value.length - 1 === this.outletBank.length) {\n      control.setValue(this.defaultPreLocData);\n    } else {\n      control.setValue(this.outletBank);\n    }\n  }\n  filterDialog(filterValue) {\n    filterValue = filterValue.target.value;\n    filterValue = filterValue.trim().toLowerCase();\n    this.filteredData = this.dropDownData.filter(item => item.toLowerCase().includes(filterValue));\n  }\n  setZeroSRM(val, formData) {\n    if (this.registrationForm.value.yield < 0) {\n      this.registrationForm.get(formData).setValue(1);\n      this.notify.snackBarShowInfo('Please enter a value greater than 0');\n    } else if (this.registrationForm.value.yield > 0) {\n      let formattedRec = this.notify.truncateAndFloor(this.registrationForm.value.yield * this.registrationForm.value.weightInUse);\n      this.registrationForm.get('recovery').setValue(formattedRec);\n    }\n  }\n  setZeroSRR(val, formData) {\n    if (this.subRecipeRecipeForm.value.yield < 0) {\n      this.subRecipeRecipeForm.get(formData).setValue(1);\n      this.notify.snackBarShowInfo('Please enter a value greater than 0');\n    } else if (this.subRecipeRecipeForm.value.yield > 0) {\n      let initialWeight = this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield;\n      this.subRecipeRecipeForm.get('initialWeight').setValue(this.notify.truncateAndFloor(initialWeight));\n      let sum = this.subRecipeRecipeForm.value.initialWeight * this.subRecipeRecipeForm.value.rate;\n      this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));\n    }\n  }\n  sumForFinalRateSRR(val) {\n    this.showWeightError = false;\n    this.subRecipeRecipeForm.get('initialWeight').setValue(this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield);\n    let sum = this.subRecipeRecipeForm.value.initialWeight * this.subRecipeRecipeForm.value.rate;\n    this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));\n  }\n  closeInfoDialog() {\n    if (this.costDialogkey == true) {\n      this.close();\n      if (this.dialogRef) {\n        this.dialogRef.close();\n      }\n    } else {\n      if (this.dialogRef) {\n        this.dialogRef.close();\n      }\n    }\n  }\n  getCategories() {\n    // this.api.getCategories({ tenantId: this.user.tenantId, type: 'subRecipe' }).pipe(first()).subscribe({\n    //   next: (res) => {\n    //     if (res['success']) {\n    this.catAndsubCat = this.sharedData.getCategories().value;\n    let categoryData = Object.keys(this.sharedData.getCategories().value).map(category => category.toUpperCase());\n    let newCat = [...this.newCategory, ...categoryData];\n    this.categories = [...new Set(newCat)];\n    this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.categories, '')));\n    // }\n    //   },\n    //   error: (err) => { console.log(err); }\n    // });\n  }\n\n  getSubCategories(val) {\n    this.registrationForm.get('subCategory').setValue('');\n    let data = this.baseData['Subrecipe Recipe'].filter(item => item.category === val);\n    this.newSubCategory = data.map(subCat => subCat.subCategory);\n    if (!(val in this.catAndsubCat)) {\n      this.catAndsubCat[val] = [];\n    }\n    let newSubCat = [...this.newSubCategory, ...this.catAndsubCat[val]];\n    this.subCategories = [...new Set(newSubCat)];\n    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.subCategories, '')));\n  }\n  optionSelectedCat(option) {\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOptionCat();\n    } else {\n      this.getSubCategories(this.registrationForm.value.category);\n    }\n    this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.categories, '')));\n  }\n  addOptionCat() {\n    this.registrationForm.controls['category'].patchValue(this.removePromptFromOption(this.registrationForm.value.category));\n    this.getSubCategories(this.registrationForm.value.category);\n  }\n  optionSelectedSubCat(option) {\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOptionSubCat();\n    }\n    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter(value || '', this.subCategories, '')));\n  }\n  addOptionSubCat() {\n    this.registrationForm.controls['subCategory'].patchValue(this.removePromptFromOption(this.registrationForm.value.subCategory));\n  }\n  focusOutFunction(formKey) {\n    if (this.registrationForm.get(formKey)) {\n      if (this.registrationForm.get(formKey).value === null) {\n        this.registrationForm.get(formKey).setValue(0);\n      }\n    }\n  }\n  focusOutFunctionSRR(formKey) {\n    if (this.subRecipeRecipeForm.get(formKey).value === null) {\n      this.subRecipeRecipeForm.get(formKey).setValue(0);\n    }\n  }\n  focusFunction(formKey) {\n    if (this.notify.truncateAndFloor(this.registrationForm.get(formKey).value) === 0) {\n      this.registrationForm.get(formKey).setValue(null);\n    }\n  }\n  focusFunctionSRR(formKey) {\n    if (this.notify.truncateAndFloor(this.subRecipeRecipeForm.get(formKey).value) === 0) {\n      this.subRecipeRecipeForm.get(formKey).setValue(null);\n    }\n  }\n  scrollRight() {\n    this.widgetsContent.nativeElement.scrollTo({\n      left: this.widgetsContent.nativeElement.scrollLeft + 150,\n      behavior: 'smooth'\n    });\n  }\n  scrollLeft() {\n    this.widgetsContent.nativeElement.scrollTo({\n      left: this.widgetsContent.nativeElement.scrollLeft - 150,\n      behavior: 'smooth'\n    });\n  }\n  getBaseData() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['userEmail'] = this.user.email;\n    obj['type'] = 'recipe';\n    obj['specific'] = 'Subrecipe Recipe';\n    if (this.registrationForm.value) {\n      obj['itemCode'] = this.registrationForm.value.menuItemCode;\n    }\n    this.api.getPresentData(obj).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.baseData = res['data'][0] ?? res['data'];\n          this.displayedColumns = ['sNo',\n          // 'discontinued',\n          'ingredientName',\n          // 'ingredientCode',\n          // 'subRecipeName',\n          // 'subRecipeCode',\n          'uom', 'weightInUse', 'yield', 'initialWeight', 'rate', 'finalRate'];\n          this.isSRRDataReady = true;\n          this.baseData['Subrecipe Recipe'].sort((a, b) => {\n            if (a.modified === 'yes' && b.modified !== 'yes') {\n              return -1;\n            }\n            if (b.modified === 'yes' && a.modified !== 'yes') {\n              return 1;\n            }\n            return 0;\n          });\n          this.dataSource.data = this.baseData['Subrecipe Recipe'] ? this.baseData['Subrecipe Recipe'] : [];\n          this.dataSource.data.forEach(el => {\n            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight']);\n            let portion = 1;\n            let requiredItem = this.invItems.find(item => item.itemCode == el['ingredientCode']);\n            let conversionCoefficient;\n            conversionCoefficient = requiredItem ? requiredItem['uom'] == 'NOS' ? 1 : 1000 : 0;\n            let rate = requiredItem ? (requiredItem.hasOwnProperty('packageQty') ? requiredItem['packageQty'] : 1) / conversionCoefficient * requiredItem['withTaxPrice'] : 0;\n            el['defaultUOM'] = el['UOM'];\n            if (requiredItem && el.hasOwnProperty('portionCount') && !['', null, undefined].includes(el.portionCount)) {\n              el['UOM'] = 'PORTION';\n              el['portionCount'] = this.notify.truncateAndFloor(el['portionCount']);\n            }\n            el['rate'] = rate;\n            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight'] * portion);\n            el['weightInUse'] = this.notify.truncateAndFloor(el['weightInUse'] * portion);\n            // el['finalRate'] = this.notify.truncateAndFloor(rate * el['Initialweight']);\n            el['finalRate'] = rate * el['Initialweight'];\n          });\n          this.tempData = this.dataSource.data;\n          // this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes')\n          this.dataSource.data = this.dataSource.data.filter(item => !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n          // this.discData = this.tempData.filter(item => item.Discontinued === 'yes')\n          this.discData = this.tempData.filter(item => ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n          this.cd.detectChanges();\n          this.dataSource.paginator = this.paginator;\n          this.dataSource.sort = this.sort;\n          this.reflectSubRecipe();\n          this.cd.detectChanges();\n        } else {\n          this.baseData = [];\n        }\n        if (this.costDialogkey == true) {\n          this.openPortionData();\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  nextTab() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      this.stepper.next();\n    }\n  }\n  getTotal(key) {\n    let total = this.dataSource.data.reduce((total, item) => {\n      const value = this.notify.truncateAndFloor(item[key]) || 0;\n      return total + value;\n    }, 0);\n    return this.notify.truncateAndFloor(total * this.ratio);\n  }\n  getSRRTotal(key) {\n    let total = this.dataSource.data.reduce((total, item) => {\n      const value = item[key] || 0;\n      return total + value;\n    }, 0);\n    return total * this.ratio;\n  }\n  getLocationCall() {\n    this.locationList = this.sharedData.getLocation().value;\n    this.preparedBank = this.locationList.map(branch => branch.abbreviatedRestaurantId);\n    if (this.preparedBank.length === 1) {\n      this.registrationForm.get('preparedAt').setValue(this.preparedBank);\n    }\n    this.preparedLocationNames.next(this.preparedBank.slice());\n    this.preparedFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this.preparedBank, this.preparedFilterCtrl, this.preparedLocationNames);\n    });\n    this.outletBank = this.locationList.map(branch => branch.abbreviatedRestaurantId);\n    if (this.outletBank.length === 1) {\n      this.registrationForm.get('usedAtOutlet').setValue(this.outletBank);\n      this.locationChange(this.registrationForm.value.usedAtOutlet);\n    }\n    this.outletLocationNames.next(this.outletBank.slice());\n    this.outletFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this.outletBank, this.outletFilterCtrl, this.outletLocationNames);\n    });\n    if (this.dialogData.key == false || this.dialogData.costDialogkey == true) {\n      this.isUpdateActive = true;\n      this.setValuesForForm(this.dialogData.elements);\n    } else if (this.dialogData.key == null && this.costDialogkey == false) {\n      this.dropDownData = this.dialogData.dropDownData;\n      this.filteredData = [...this.dropDownData];\n    }\n    this.getBaseData();\n  }\n  readIPConfig() {\n    this.api.readIPConfig(this.user.tenantId).subscribe({\n      next: res => {\n        if (res?.['success']) {\n          const data = res['data'];\n          this.logo = data.tenantDetails.logo;\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  printOption() {\n    const discontinuedValue = this.registrationForm.get('discontinued').value;\n    this.status = discontinuedValue === 'yes' ? 'active' : 'Discontinued';\n    const tableData = this.dataSource.data.map(element => [element.ingredientName, element.ingredientCode, element.UOM, element.Initialweight, element.yield, element.weightInUse, this.notify.truncateAndFloor(element.rate), this.notify.truncateAndFloor(element.finalRate)]);\n    let obj = {\n      factory_name: this.user.name,\n      recipe_details: {\n        'Sub Recipe Name': this.registrationForm.get('menuItemName').value,\n        'Sub Recipe Code': this.registrationForm.get('menuItemCode').value,\n        'Category': this.registrationForm.get('category').value,\n        'Sub Category': this.registrationForm.get('subCategory').value,\n        'Status': this.status,\n        'UOM': this.registrationForm.get('uom').value,\n        'Closing UOM': this.registrationForm.get('closingUOM').value,\n        'Prepared At': this.registrationForm.get('preparedAt').value.join(', '),\n        'Sales Outlet': this.registrationForm.get('usedAtOutlet').value.join(', '),\n        'Issued To': this.registrationForm.get('usedInWorkArea').value.join(', ')\n      },\n      logo: this.logo,\n      table_headers: ['Ingredient Name', 'Ingredient Code', 'UOM', 'Initial Weight', 'Yield', 'Weight In Use', 'Unit Cost', 'Final Rate'],\n      table_data: tableData,\n      summary: {\n        'Weight In Use': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,\n        'Yield': this.registrationForm.get('yield').value,\n        'Recovery': `${this.registrationForm.get('recovery').value} (${this.registrationForm.get('uom').value})`,\n        'Unit Cost': `${this.registrationForm.get('rate').value} (Rs)`,\n        'Gross Weight': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,\n        'Preparation Cost': `${this.registrationForm.get('rate').value} (Rs)`,\n        'Weight / Portion': `${this.getWeightPerPortion()} (${this.registrationForm.get('uom').value})`,\n        'Cost / Portion': `${this.notify.truncateAndFloor(this.getCostPerPortion())} (Rs)`\n      }\n    };\n    this.api.printInvoice(obj).subscribe({\n      next: data => {\n        this.api.globalPrintPdf(data.pdf_base64);\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  locationChange(event) {\n    if (event) {\n      const selectedWorkAreasArray = this.locationList.filter(branch => event.includes(branch.abbreviatedRestaurantId));\n      this.usedInWorkAreaBank = selectedWorkAreasArray;\n      if (this.usedInWorkAreaBank.length === 1) {\n        this.registrationForm.get('usedInWorkArea').setValue(this.usedInWorkAreaBank);\n      }\n      if (this.discontinuedOutletData.length > 0) {\n        this.discontinuedOutletData.forEach(val => {\n          this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {\n            if (item.branchName === val) {\n              item.disabled = true;\n            }\n            return item;\n          });\n        });\n      }\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n        this.FilterIssued(this.usedInWorkAreaBank, this.usedInWorkAreaFilterCtrl, this.usedInWorkAreaNames);\n      });\n    } else {\n      this.usedInWorkAreaBank = [];\n    }\n  }\n  openPortionData() {\n    this.dialogRef = this.dialog.open(this.openPortionDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog',\n      autoFocus: false,\n      disableClose: true\n    });\n  }\n  openUnitDialog() {\n    this.dialogRef = this.dialog.open(this.openUnitCostDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog'\n    });\n  }\n  deleteSRR(element) {\n    const item = this.invItems.find(item => item.itemName.toLowerCase() === element.ingredientName.toLowerCase());\n    this.updateSRR = true;\n    this.subRecipeRecipeForm.patchValue({\n      ingredientCode: element['ingredientCode'],\n      ingredientName: item ? item.itemName : '',\n      subRecipeCode: element['subRecipeCode'],\n      subRecipeName: element['subRecipeName'],\n      uom: element['UOM'],\n      yield: element['yield'],\n      rate: this.notify.truncateAndFloor(element['rate']),\n      finalRate: this.notify.truncateAndFloor(element['finalRate']),\n      initialWeight: element['Initialweight'],\n      loss: element['loss'],\n      weightInUse: element['weightInUse'],\n      discontinued: ['no', 'NO', 'No', 'N', null, ''].includes(element['Discontinued']) ? 'no' : 'yes',\n      row_uuid: element['row_uuid']\n    });\n    if (!element['Initialweight']) {\n      this.subRecipeRecipeForm.get('initialWeight').setValue(0);\n    }\n    if (!element['loss']) {\n      this.subRecipeRecipeForm.get('loss').setValue(0);\n    }\n    this.dialogRef = this.dialog.open(this.openDeleteDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog'\n    });\n  }\n  deleteData() {\n    const indexToRemove = this.dataSource.data.findIndex(recipe => recipe.ingredientCode === this.subRecipeRecipeForm.value.ingredientCode);\n    if (indexToRemove !== -1) {\n      let item = this.dataSource.data[indexToRemove]; // Store the removed item\n      this.removedItem.push(item);\n      this.dataSource.data = this.dataSource.data.slice(0, indexToRemove).concat(this.dataSource.data.slice(indexToRemove + 1));\n      // this.deleteDataFormDB()\n    }\n\n    this.tempData = this.dataSource.data;\n    this.subRecipeRecipeForm.reset();\n    this.subRecipeRecipeForm.patchValue({\n      subRecipeCode: this.registrationForm.value.menuItemCode,\n      subRecipeName: this.registrationForm.value.menuItemName\n    });\n    this.clearForm();\n    this.reflectSubRecipe();\n    this.closeInfoDialog();\n  }\n  deleteDataFormDB() {\n    let obj = {};\n    if (this.removedItem.length > 0) {\n      this.removedItem.forEach(item => {\n        item['delete'] = true;\n        // item['Discontinued'] = 'yes';\n        item['InvUom'] = item['Inv. UOM'];\n        delete item['Inv. UOM'];\n      });\n      obj['tenantId'] = this.user.tenantId;\n      obj['userEmail'] = this.user.email;\n      obj['data'] = this.removedItem;\n      obj['type'] = 'recipe';\n      obj['category'] = 'subRecipe';\n      this.api.removeData(obj).pipe(first()).subscribe({\n        next: res => {\n          if (res['success']) {\n            this.cd.detectChanges();\n            this.notify.snackBarShowSuccess('Data Deleted successfully');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  discontinueData() {\n    let updatedSRRData = this.convertSRRKeys();\n    updatedSRRData['modified'] = 'yes';\n    updatedSRRData['Discontinued'] = 'yes';\n    let requiredPackage = this.dataSource.data.find(el => el.ingredientCode == updatedSRRData['ingredientCode'] && el.subRecipeCode == updatedSRRData['subRecipeCode']\n    // el.ingredientName.toLowerCase() == updatedSRRData['ingredientName'].toLowerCase()\n    );\n\n    if (requiredPackage) {\n      requiredPackage['modified'] = 'yes';\n      requiredPackage['Discontinued'] = 'yes';\n      let index = this.dataSource.data.indexOf(requiredPackage);\n      this.discData.push(updatedSRRData);\n      this.dataSource.data[index] = updatedSRRData;\n      // let data = this.dataSource.data.filter(item => item.Discontinued !== 'yes' )\n      let data = this.dataSource.data.filter(item => !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n      this.dataSource.data = data;\n      // this.dataSource.paginator = this.paginator;\n      this.cd.detectChanges();\n      this.reflectSubRecipe();\n      this.notify.snackBarShowSuccess('Updated successfully');\n      this.subRecipeRecipeForm.reset();\n      this.subRecipeRecipeForm.patchValue({\n        subRecipeCode: this.registrationForm.value.menuItemCode,\n        subRecipeName: this.registrationForm.value.menuItemName\n      });\n      this.closeInfoDialog();\n    }\n  }\n  showItems() {\n    if (this.showDeleteItems == true) {\n      this.dataSource.data = this.discData;\n    } else if (this.showDeleteItems == false) {\n      // this.dataSource.data = this.tempData.filter(item => item.Discontinued !== 'yes' )\n      this.dataSource.data = this.tempData.filter(item => !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase()));\n    }\n  }\n  openSelect(select) {\n    select.open();\n  }\n  onUnitChange(el) {\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      let reqWeight = this.registrationForm.value.recovery / this.registrationForm.value.portion;\n      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqWeight));\n    } else {\n      let reqPortion = this.registrationForm.value.recovery / this.registrationForm.value.portion;\n      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqPortion));\n    }\n  }\n  getWeightPerPortion() {\n    let portion = this.registrationForm.value.portion;\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      portion = this.registrationForm.value.recovery / this.registrationForm.value.portion;\n    }\n    return this.registrationForm.value.recovery * (1 / portion);\n  }\n  getCostPerPortion() {\n    let portion = this.registrationForm.value.portion;\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      portion = this.registrationForm.value.recovery / this.registrationForm.value.portion;\n    }\n    return this.registrationForm.value.rate * (1 / portion);\n  }\n  setPortionData() {\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(this.registrationForm.value.recovery) : undefined;\n    } else {\n      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(1) : undefined;\n    }\n  }\n  updateBaseData(data) {\n    data['menu master'].forEach(el => {\n      const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);\n      el['modified'] = 'no';\n      if (index !== -1) {\n        this.baseData['menu master'][index] = el;\n      }\n    });\n    data['menu recipes'].forEach(el => {\n      const recipeIndex = this.baseData['menu recipes'].findIndex(item => item['menuItemCode'] === el['menuItemCode'] && item['ingredientCode'] === el['ingredientCode']);\n      el['modified'] = 'no';\n      if (recipeIndex !== -1) {\n        this.baseData['menu recipes'][recipeIndex] = el;\n      }\n    });\n    data['Subrecipe Master'].forEach(el => {\n      const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);\n      el['modified'] = 'no';\n      if (index !== -1) {\n        this.baseData['Subrecipe Master'][index] = el;\n      }\n    });\n    data['Subrecipe Recipe'].forEach(el => {\n      const recipeIndex = this.baseData['Subrecipe Recipe'].findIndex(item => item['subRecipeCode'] === el['subRecipeCode'] && item['ingredientCode'] === el['ingredientCode']);\n      el['modified'] = 'no';\n      if (recipeIndex !== -1) {\n        this.baseData['Subrecipe Recipe'][recipeIndex] = el;\n      }\n    });\n    this.sharedData.setBaseData(this.baseData);\n  }\n  onDelete(location, event, select, group) {\n    event.stopPropagation();\n    this.selectedDropDown = select;\n    this.selectedData = location;\n    this.groupData = group;\n    this.dialogRef = this.dialog.open(this.discontinuedSelectDialog, {\n      width: '600px',\n      panelClass: 'mediumCustomDialog'\n    });\n    this.dialogRef.afterClosed().subscribe(result => {});\n  }\n  onRestore(location, event, select, group) {\n    event.stopPropagation();\n    if (select === 'usedOutlet') {\n      this.discontinuedOutletData = this.discontinuedOutletData.filter(item => item !== location);\n      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item.abbreviatedRestaurantId !== location);\n      this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {\n        if (item.abbreviatedRestaurantId === location && item.hasOwnProperty('disabled')) {\n          delete item.disabled;\n        }\n        return item;\n      });\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n        this.FilterIssued(this.usedInWorkAreaBank, this.usedInWorkAreaFilterCtrl, this.usedInWorkAreaNames);\n      });\n    } else if (select === 'issuedTo') {\n      this.discontinuedIssuedToData.forEach(item => {\n        if (item.abbreviatedRestaurantId === group.abbreviatedRestaurantId) {\n          item.workAreas = item.workAreas.filter(workArea => workArea !== location);\n        }\n      });\n      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item.workAreas.length > 0);\n    } else if (select === 'preparatoryLocation') {\n      this.discontinuedPreLocData = this.discontinuedPreLocData.filter(item => item !== location);\n    }\n    this.cd.detectChanges();\n  }\n  discontinuedSelectData() {\n    if (this.selectedDropDown === 'usedOutlet') {\n      this.discontinuedOutletData.push(this.selectedData);\n      const selectedWorkAreasArray = this.locationList.filter(branch => this.selectedData.includes(branch.abbreviatedRestaurantId));\n      this.discontinuedIssuedToData.push(selectedWorkAreasArray[0]);\n      this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {\n        if (item.abbreviatedRestaurantId === this.selectedData) {\n          item.disabled = true;\n        }\n        return item;\n      });\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n        this.FilterIssued(this.usedInWorkAreaBank, this.usedInWorkAreaFilterCtrl, this.usedInWorkAreaNames);\n      });\n    } else if (this.selectedDropDown === 'issuedTo') {\n      [this.groupData].forEach(item => {\n        const matchingIssued = this.discontinuedIssuedToData.find(issuedItem => issuedItem.abbreviatedRestaurantId === item.abbreviatedRestaurantId);\n        if (matchingIssued) {\n          matchingIssued.workAreas.push(this.selectedData);\n        } else {\n          const newObject = {\n            abbreviatedRestaurantId: item.abbreviatedRestaurantId,\n            workAreas: [this.selectedData]\n          };\n          this.discontinuedIssuedToData.push(newObject);\n        }\n      });\n      const newArray = [this.groupData].map(item => {\n        return {\n          abbreviatedRestaurantId: item.abbreviatedRestaurantId,\n          workAreas: item.workAreas.filter(workArea => workArea === this.selectedData)\n        };\n      });\n      this.discontinuedIssuedToData.push(...newArray);\n    } else if (this.selectedDropDown === 'preparatoryLocation') {\n      this.discontinuedPreLocData.push(this.selectedData);\n    }\n    this.closeDiscontinuedDialog();\n    this.cd.detectChanges();\n  }\n  closeDiscontinuedDialog() {\n    this.dialogRef.close();\n  }\n  getPerKGCost() {\n    return this.registrationForm.value.rate / this.registrationForm.value.recovery * 1000;\n  }\n  isOptionDisabled(data, group) {\n    return this.discontinuedIssuedToData.some(item => item.abbreviatedRestaurantId === group.abbreviatedRestaurantId && item.workAreas.includes(data));\n  }\n  isCheckOptionDisabled(data, group) {\n    return this.discontinuedIssuedToData.some(item => item.abbreviatedRestaurantId === group.abbreviatedRestaurantId && item.workAreas.includes(data));\n  }\n  setDiscontinuedDataInRolopos() {\n    this.api.dicontinuedData({\n      'tenantId': this.user.tenantId,\n      'userEmail': this.user.email,\n      'type': 'subRecipeLocations',\n      'discontinuedLocations': {\n        'subRecipeLocations': {\n          'procuredAtDiscontinued': this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData : [],\n          'outLetDiscontinued': this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData : [],\n          'issuedToDiscontinued': this.discontinuedIssuedToData.length > 0 ? this.discontinuedIssuedToData : []\n        }\n      }\n    }).pipe(first()).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.cd.detectChanges();\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ActionComponent_Factory(t) {\n      return new (t || ActionComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.ShareDataService), i0.ɵɵdirectiveInject(i6.SessionCacheService), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i7.NotificationService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i9.MasterDataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionComponent,\n      selectors: [[\"app-action\"]],\n      viewQuery: function ActionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5, ElementRef);\n          i0.ɵɵviewQuery(MatSort, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(MatOption, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.openPortionDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.openUnitCostDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.openDeleteDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.widgetsContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.discontinuedSelectDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.matOptions = _t);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 11,\n      consts: [[\"class\", \"closeBtn\", 4, \"ngIf\"], [1, \"registration-form\", \"py-2\", \"px-3\"], [\"class\", \"spinner-border\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"m-1\", 4, \"ngIf\"], [\"class\", \"mt-3 smallDialog\", 4, \"ngIf\"], [1, \"mb-2\", \"topCreateAndUpdateBtn\", 2, \"float\", \"right\", \"padding-top\", \"1rem\"], [\"mat-raised-button\", \"\", \"class\", \"mappingBtn discButton\", \"style\", \"margin-right: 5px;\", \"matTooltip\", \"print\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-right: 5px;\", \"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"update\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"style\", \"margin-right: 5px;\", \"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Create\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Close\", \"style\", \"margin-right: 7px;\", 3, \"click\", 4, \"ngIf\"], [1, \"pb-2\"], [\"class\", \" my-2 p-3 bottomTitles\", 4, \"ngIf\"], [4, \"ngIf\"], [\"addSRR\", \"\"], [\"class\", \"mt-3 smallDialog dropDndDialog\", 4, \"ngIf\"], [\"openPortionDialog\", \"\"], [\"openUnitCostDialog\", \"\"], [\"openDeleteDialog\", \"\"], [\"discontinuedSelectDialog\", \"\"], [1, \"closeBtn\"], [\"matTooltip\", \"close\", 1, \"closeBtnIcon\", 3, \"click\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"m-1\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 3, \"keyup\"], [\"matSuffix\", \"\"], [1, \"mt-3\", \"smallDialog\"], [1, \"col-md-12\"], [1, \"text-center\", \"my-2\", \"p-2\", \"bottomTitles\"], [\"matInput\", \"\", \"placeholder\", \"SubRecipe..\", \"aria-label\", \"Inventory\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"matAutocomplete\", \"formControl\", \"keyup\", \"keyup.enter\"], [3, \"optionSelected\"], [\"auto1\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-end\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [3, \"value\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"print\", \"type\", \"button\", 1, \"mappingBtn\", \"discButton\", 2, \"margin-right\", \"5px\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"update\", 2, \"margin-right\", \"5px\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Create\", 2, \"margin-right\", \"5px\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Close\", 2, \"margin-right\", \"7px\", 3, \"click\"], [1, \"my-2\", \"p-3\", \"bottomTitles\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"formGroup\"], [1, \"row\"], [1, \"col-md-3\"], [\"formControlName\", \"menuItemCode\", \"matInput\", \"\", \"placeholder\", \"Menu Item Code\", \"autocomplete\", \"off\", 3, \"readonly\", \"ngClass\"], [\"formControlName\", \"menuItemName\", \"matInput\", \"\", \"placeholder\", \"Menu Item Name\", 3, \"readonly\", \"ngClass\"], [\"formControlName\", \"uom\"], [\"formControlName\", \"closingUOM\"], [\"matInput\", \"\", \"placeholder\", \"Category Name\", \"aria-label\", \"Category\", \"formControlName\", \"category\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"matAutocomplete\", \"keyup.enter\"], [\"matInput\", \"\", \"placeholder\", \"sub Category\", \"aria-label\", \"SubCategory\", \"formControlName\", \"subCategory\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"matAutocomplete\", \"keyup.enter\"], [\"auto2\", \"matAutocomplete\"], [\"formControlName\", \"preparedAt\", \"multiple\", \"\"], [\"placeholderLabel\", \"search...\", \"noEntriesFoundLabel\", \"'not found'\", 3, \"formControl\"], [1, \"hide-checkbox\", 3, \"click\"], [3, \"value\", \"disabled\", \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"usedAtOutlet\", \"multiple\", \"\", 3, \"selectionChange\"], [\"formControlName\", \"usedInWorkArea\", \"multiple\", \"\"], [3, \"label\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"formError\", 4, \"ngIf\"], [1, \"non-editable-field\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [3, \"click\"], [\"formControlName\", \"yield\", \"type\", \"number\", \"matInput\", \"\", \"placeholder\", \"Yield\", \"autocomplete\", \"off\", 3, \"keyup\", \"focus\", \"focusout\"], [1, \"d-flex\", \"align-items-center\"], [\"appearance\", \"outline\", 1, \"flex-fill\", \"me-2\"], [\"placeholder\", \"Select Unit\", \"formControlName\", \"unit\", 3, \"selectionChange\"], [\"formControlName\", \"portion\", \"type\", \"number\", \"matInput\", \"\", \"placeholder\", \"Batch\", \"autocomplete\", \"off\", 3, \"keyup\", \"focus\", \"focusout\"], [\"matSuffix\", \"\", \"matTooltip\", \"Portion Details\", 1, \"custom-outline-button\", 3, \"click\"], [1, \"my-2\", \"p-2\", \"bottomTitles\"], [1, \"searchInputParentClass\", \"my-2\"], [\"cdkTrapFocus\", \"\"], [1, \"d-flex\", \"gap-3\"], [1, \"form-group\", \"customHeightfield\"], [\"for\", \"ingredientSelect\"], [\"matInput\", \"\", \"placeholder\", \"ingredient Name\", \"formControlName\", \"ingredientName\", \"oninput\", \"this.value = this.value.toUpperCase()\", 1, \"form-control\", 3, \"matAutocomplete\"], [\"autoIngredients\", \"matAutocomplete\"], [3, \"value\", \"ngClass\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"modifierSelect\"], [\"id\", \"modifierSelect\", \"formControlName\", \"uom\", 1, \"form-select\", 2, \"width\", \"80px !important\", 3, \"change\"], [3, \"value\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-group customHeightfield\", 4, \"ngIf\"], [\"for\", \"unitCostSelect\"], [\"formControlName\", \"rate\", \"type\", \"number\", \"placeholder\", \"Rate\", \"autocomplete\", \"off\", \"readonly\", \"\", 1, \"highlighted-input\", \"form-control\", 3, \"focus\", \"focusout\", \"keyup\"], [\"for\", \"portionCountInput\"], [\"formControlName\", \"weightInUse\", \"type\", \"number\", \"placeholder\", \"Weight In Use\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"readonly\", \"focus\", \"focusout\", \"keyup\"], [\"for\", \"yieldInput\"], [\"formControlName\", \"yield\", \"type\", \"number\", \"placeholder\", \"Yield\", \"autocomplete\", \"off\", 1, \"form-control\", 3, \"keyup\", \"focus\", \"focusout\"], [1, \"form-group\", \"flex-shrink-0\", \"d-flex\", \"align-items-end\", \"justify-content-end\", 2, \"margin-bottom\", \"0.1px\"], [\"type\", \"submit\", \"matTooltip\", \"Add\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"px-3\", 2, \"height\", \"2.3rem\", 3, \"click\"], [1, \"material-icons\", \"align-middle\"], [1, \"mb-2\", \"floatRightBtn\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"section\"], [\"section\", \"\", \"widgetsContent\", \"\"], [\"class\", \"tableDiv\", 4, \"ngIf\"], [3, \"value\", \"disabled\", \"ngClass\"], [3, \"ngClass\"], [\"class\", \"deleteIconForMatSelect\", \"matTooltip\", \"discontinue\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"deleteIconForMatSelect\", \"matTooltip\", \"restore\", 3, \"click\", 4, \"ngIf\"], [\"matTooltip\", \"discontinue\", 1, \"deleteIconForMatSelect\", 3, \"ngClass\", \"click\"], [\"matTooltip\", \"restore\", 1, \"deleteIconForMatSelect\", 3, \"click\"], [3, \"label\", \"disabled\"], [1, \"formError\"], [\"formControlName\", \"discontinued\"], [\"value\", \"yes\"], [\"value\", \"no\"], [3, \"value\", \"ngClass\", \"disabled\"], [3, \"value\", \"disabled\"], [\"for\", \"portionSelect\"], [\"formControlName\", \"portionCount\", \"type\", \"number\", \"placeholder\", \"Rate\", \"autocomplete\", \"off\", 1, \"highlighted-input\", \"form-control\", 3, \"focus\", \"focusout\", \"keyup\"], [1, \"tableDiv\"], [\"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"sNo\"], [\"class\", \"tableSnoCol\", 4, \"matHeaderCellDef\"], [\"class\", \"tableSnoCol\", 4, \"matCellDef\"], [\"class\", \"custom-footer\", \"class\", \"tableSnoCol\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"action\"], [\"class\", \"tableActionCol\", 4, \"matHeaderCellDef\"], [\"class\", \"tableActionCol\", 4, \"matCellDef\"], [\"class\", \"tableActionCol\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"discontinued\"], [\"class\", \"custom-header\", 4, \"matHeaderCellDef\"], [\"class\", \"custom-cell justify-content-start\", 4, \"matCellDef\"], [\"class\", \"custom-footer\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"modified\"], [\"class\", \"tableModCol\", 4, \"matHeaderCellDef\"], [\"class\", \"tableModCol\", 4, \"matCellDef\"], [\"class\", \"custom-footer\", \"class\", \"tableModCol\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"ingredientName\"], [\"class\", \"custom-header\", \"style\", \"min-width: 300px !important;\", 4, \"matHeaderCellDef\"], [\"class\", \"custom-cell\", \"style\", \"min-width: 300px !important;\", 4, \"matCellDef\"], [\"class\", \"custom-footer\", \"style\", \"min-width: 300px !important;\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"ingredientCode\"], [\"class\", \"custom-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"subRecipeName\"], [\"matColumnDef\", \"subRecipeCode\"], [\"matColumnDef\", \"uom\"], [\"style\", \"min-width: 125px !important;\", 4, \"matHeaderCellDef\"], [\"style\", \"min-width: 125px !important;\", 4, \"matCellDef\"], [\"style\", \"min-width: 125px !important;\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"initialWeight\"], [\"matColumnDef\", \"yield\"], [\"style\", \"min-width: 100px !important;\", 4, \"matHeaderCellDef\"], [\"style\", \"min-width: 100px !important;\", 4, \"matCellDef\"], [\"style\", \"min-width: 100px !important;\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"loss\"], [\"matColumnDef\", \"weightInUse\"], [\"matColumnDef\", \"rate\"], [\"matColumnDef\", \"finalRate\"], [4, \"matHeaderRowDef\"], [3, \"ngClass\", 4, \"matRowDef\", \"matRowDefColumns\"], [4, \"matFooterRowDef\"], [1, \"mat-paginator-sticky\", 3, \"pageSize\", \"pageSizeOptions\"], [1, \"tableSnoCol\"], [1, \"tableActionCol\"], [\"backgroundColor\", \"primary\", \"matTooltip\", \"Edit\", 1, \"mx-2\", \"editIconBtn\", 3, \"click\"], [1, \"mt-1\"], [\"backgroundColor\", \"primary\", \"matTooltip\", \"Delete\", 1, \"mx-2\", \"editIconBtn\", 3, \"click\"], [1, \"custom-header\"], [1, \"custom-cell\", \"justify-content-start\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [1, \"cancelIcon\"], [1, \"checkIcon\"], [1, \"custom-footer\"], [1, \"tableModCol\"], [\"color\", \"primary\"], [1, \"custom-header\", 2, \"min-width\", \"300px !important\"], [1, \"custom-cell\", 2, \"min-width\", \"300px !important\"], [\"matTooltip\", \"Edit\", 1, \"link\", \"mr-2\", 2, \"width\", \"200px !important\", 3, \"ngClass\", \"click\"], [\"class\", \"checkIcon tableIcons\", 4, \"ngIf\"], [1, \"checkIcon\", \"tableIcons\"], [1, \"custom-footer\", 2, \"min-width\", \"300px !important\"], [1, \"custom-cell\"], [2, \"min-width\", \"125px !important\"], [2, \"min-width\", \"100px !important\"], [\"count\", \"5\", \"animation\", \"progress-dark\", 3, \"theme\"], [\"icon\", \"soup_kitchen\", \"title\", \"No Subrecipe Recipes Found\", \"message\", \"Time to get creative! Add ingredients to create your subrecipe.\", 4, \"ngIf\"], [\"icon\", \"check_circle\", \"title\", \"No Discontinued Items\", \"message\", \"There are no discontinued ingredients to display.\", 4, \"ngIf\"], [\"icon\", \"soup_kitchen\", \"title\", \"No Subrecipe Recipes Found\", \"message\", \"Time to get creative! Add ingredients to create your subrecipe.\"], [\"icon\", \"check_circle\", \"title\", \"No Discontinued Items\", \"message\", \"There are no discontinued ingredients to display.\"], [1, \"d-flex\", \"justify-content-end\", \"flex-wrap\", \"mb-3\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"update\", 3, \"click\", 4, \"ngIf\"], [\"appearance\", \"outline\", 3, \"ngClass\"], [\"formControlName\", \"subRecipeName\", \"matInput\", \"\", \"placeholder\", \"SubRecipe Name\", 3, \"readonly\"], [\"placeholder\", \"Ingredient Name\", \"formControlName\", \"ingredientName\", 3, \"selectionChange\"], [3, \"disabled\"], [\"formControlName\", \"weightInUse\", \"type\", \"number\", \"matInput\", \"\", \"placeholder\", \"Weight In Use\", \"autocomplete\", \"off\", 3, \"readonly\", \"focus\", \"focusout\", \"keyup\"], [\"formControlName\", \"yield\", \"type\", \"number\", \"matInput\", \"\", \"placeholder\", \"yield\", \"autocomplete\", \"off\", 3, \"keyup\", \"focus\", \"focusout\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"update\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"number\", \"placeholder\", \"Portion\", \"formControlName\", \"portionCount\", 1, \"outline\", 3, \"keyup\", \"focus\", \"focusout\"], [1, \"col\"], [\"formControlName\", \"discontinued\", \"aria-labelledby\", \"example-radio-group-label\"], [1, \"mt-3\", \"smallDialog\", \"dropDndDialog\"], [\"class\", \"my-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-2\"], [\"icon\", \"search_off\", \"title\", \"No Results Found\", \"message\", \"No matching data found for your search criteria.\", \"customClass\", \"dialog-empty-state\"], [1, \"dialog-container\"], [1, \"close-btn\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Close\", \"matTooltip\", \"close\", 1, \"close-btn-icon\", 3, \"click\"], [1, \"mx-1\", \"py-2\", \"px-3\"], [1, \"text-center\", \"my-2\", \"p-2\", \"bottom-titles\"], [1, \"portion-info\"], [1, \"info-table\"], [1, \"info-key\"], [1, \"info-value\"], [1, \"info-unit\"], [1, \"portion-info1\"], [1, \"mb-3\"], [1, \"d-flex\", \"justify-content-center\", \"gap-3\", \"m-2\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"delete\", 1, \"deleteBtn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"discontinue\", 1, \"discBtn\", 3, \"click\"], [1, \"m-3\", \"infoText\", \"text-center\"], [1, \"text-end\", \"m-2\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Update\", 1, \"m-1\", 3, \"click\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"close\", 1, \"m-1\", 3, \"click\"]],\n      template: function ActionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActionComponent_div_0_Template, 3, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ActionComponent_div_2_Template, 3, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, ActionComponent_div_3_Template, 7, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, ActionComponent_div_4_Template, 22, 11, \"div\", 4);\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtemplate(6, ActionComponent_button_6_Template, 4, 0, \"button\", 6);\n          i0.ɵɵtemplate(7, ActionComponent_button_7_Template, 3, 2, \"button\", 7);\n          i0.ɵɵtemplate(8, ActionComponent_button_8_Template, 5, 2, \"button\", 8);\n          i0.ɵɵtemplate(9, ActionComponent_button_9_Template, 4, 0, \"button\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 10);\n          i0.ɵɵtemplate(11, ActionComponent_div_11_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, ActionComponent_div_12_Template, 179, 56, \"div\", 12);\n          i0.ɵɵtemplate(13, ActionComponent_ng_template_13_Template, 45, 21, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(15, ActionComponent_div_15_Template, 3, 2, \"div\", 14);\n          i0.ɵɵtemplate(16, ActionComponent_ng_template_16_Template, 45, 4, \"ng-template\", null, 15, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(18, ActionComponent_ng_template_18_Template, 20, 1, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(20, ActionComponent_ng_template_20_Template, 17, 0, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(22, ActionComponent_ng_template_22_Template, 11, 1, \"ng-template\", null, 18, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate == true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.costDialogkey == true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate === null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate == true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdateActive && ctx.isDuplicate == false && !ctx.showSRR);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdateActive && ctx.isDuplicate == false && !ctx.showSRR);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUpdateActive && ctx.isDuplicate == false && !ctx.showSRR);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate == false && !ctx.showSRR);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate == false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate == false && !ctx.showSRR);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDuplicate === null);\n        }\n      },\n      dependencies: [FormsModule, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, MatDialogModule, MatChipsModule, i10.MatChip, CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.AsyncPipe, i11.UpperCasePipe, ReactiveFormsModule, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, MatFormFieldModule, i12.MatFormField, i12.MatLabel, i12.MatError, i12.MatSuffix, MatNativeDateModule, MatDatepickerModule, MatInputModule, i13.MatInput, MatSliderModule, MatButtonModule, i14.MatButton, i14.MatIconButton, MatIconModule, i15.MatIcon, MatCardModule, MatSelectModule, i16.MatSelect, i17.MatOption, i17.MatOptgroup, MatRadioModule, i18.MatRadioGroup, i18.MatRadioButton, MatAutocompleteModule, i19.MatAutocomplete, i19.MatAutocompleteTrigger, MatDividerModule, NgxMatSelectSearchModule, i20.MatSelectSearchComponent, MatTableModule, i21.MatTable, i21.MatHeaderCellDef, i21.MatHeaderRowDef, i21.MatColumnDef, i21.MatCellDef, i21.MatRowDef, i21.MatFooterCellDef, i21.MatFooterRowDef, i21.MatHeaderCell, i21.MatCell, i21.MatFooterCell, i21.MatHeaderRow, i21.MatRow, i21.MatFooterRow, MatTooltipModule, i22.MatTooltip, MatToolbarModule, MatStepperModule, NgxSkeletonLoaderModule, i23.NgxSkeletonLoaderComponent, MatPaginatorModule, i24.MatPaginator, A11yModule, i25.CdkTrapFocus, MatSlideToggleModule, i26.MatSlideToggle, EmptyStateComponent],\n      styles: [\".section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow-y: auto;\\n}\\n\\n.no-outline[_ngcontent-%COMP%]:disabled {\\n  outline: none;\\n}\\n\\n  .mat-step-icon-selected {\\n  background-color: rgba(211, 211, 211, 0.3607843137) !important;\\n}\\n\\n  .mat-step-icon-state-done {\\n  background-color: rgba(0, 101, 129, 0.8) !important;\\n}\\n\\n.disabledBtn[_ngcontent-%COMP%] {\\n  color: grey;\\n}\\n\\n.createClass[_ngcontent-%COMP%] {\\n  margin: 2.5rem auto;\\n  text-align: center;\\n}\\n\\n.createTextClass[_ngcontent-%COMP%] {\\n  font-size: large;\\n}\\n\\n.createBtnClass[_ngcontent-%COMP%] {\\n  margin: 15px auto;\\n  display: table;\\n}\\n\\n.createBtn[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n}\\n\\nmat-error[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.material-symbols-outlined[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  line-height: 48px;\\n  color: green;\\n  cursor: pointer;\\n}\\n\\n.bottom-titles[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: larger;\\n  font-weight: bolder;\\n  background-color: #e5e5e5;\\n}\\n\\n.portion-info[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 10px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.portion-info1[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 10px;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  padding: 10px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 4px;\\n}\\n\\n.info-content[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1em;\\n  color: #004175; \\n\\n}\\n\\n.info-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #004175; \\n\\n}\\n\\n.golden-yellow-chip[_ngcontent-%COMP%] {\\n  background-color: #ffc107 !important;\\n  color: rgba(0, 0, 0, 0.87) !important;\\n  max-height: 1.2rem;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.close-btn-icon[_ngcontent-%COMP%] {\\n  color: #ff0000; \\n\\n}\\n\\n.registration-form[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9; \\n\\n  border-radius: 8px; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); \\n\\n}\\n\\n.portion-info[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.info-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.info-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  border-bottom: 1px solid #ddd; \\n\\n}\\n\\n.info-key[_ngcontent-%COMP%] {\\n  text-align: left;\\n  font-weight: bold;\\n}\\n\\n.info-value[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: #333; \\n\\n}\\n\\n.info-unit[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: #666; \\n\\n}\\n\\n.non-editable-field[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n  margin-bottom: 10px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.non-editable-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n}\\n\\n.readonly-field[_ngcontent-%COMP%] {\\n  background-color: #e0e0e0;\\n}\\n\\n.mappingBtn[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n.discButton[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  color: black !important;\\n  border: 1px solid rgba(8, 87, 151, 0.685) !important;\\n}\\n\\n.sub-data-class[_ngcontent-%COMP%] {\\n  color: rgb(119, 12, 12); \\n\\n  \\n\\n  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaW52ZW50b3J5LW1hbmFnZW1lbnQvc3VicmVjaXBlLW1hc3Rlci9hY3Rpb24vYWN0aW9uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFFO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0FBQ0o7O0FBRUU7RUFDRSxhQUFBO0FBQ0o7O0FBRUU7RUFDRSw4REFBQTtBQUNKOztBQUVFO0VBQ0UsbURBQUE7QUFDSjs7QUFFRTtFQUNFLFdBQUE7QUFDSjs7QUFFQTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7QUFDRjs7QUFFQTtFQUNFLFVBQUE7QUFDRjs7QUFFQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFNRSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQUpGOztBQU9BO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsNkJBQUE7QUFKRjs7QUFPQTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtBQUpGOztBQU9BO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQUpGOztBQU9BO0VBQ0UsU0FBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQSxFQUFBLGNBQUE7QUFKRjs7QUFPQTtFQUNFLGNBQUEsRUFBQSxjQUFBO0FBSkY7O0FBT0E7RUFDRSxvQ0FBQTtFQUNBLHFDQUFBO0VBQ0Esa0JBQUE7QUFKRjs7QUFPQTtFQUNFLGlCQUFBO0FBSkY7O0FBT0E7RUFDRSxjQUFBLEVBQUEsc0NBQUE7QUFKRjs7QUFPQTtFQUNFLHlCQUFBLEVBQUEsa0NBQUE7RUFDQSxrQkFBQSxFQUFBLG9CQUFBO0VBQ0Esd0NBQUEsRUFBQSxpQ0FBQTtBQUpGOztBQVlBO0VBQ0UsZ0JBQUE7QUFURjs7QUFZQTtFQUNFLFdBQUE7RUFDQSx5QkFBQTtBQVRGOztBQVlBO0VBQ0UsWUFBQTtFQUNBLDZCQUFBLEVBQUEsK0JBQUE7QUFURjs7QUFZQTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7QUFURjs7QUFZQTtFQUNFLGlCQUFBO0VBQ0EsV0FBQSxFQUFBLDRCQUFBO0FBVEY7O0FBWUE7RUFDRSxpQkFBQTtFQUNBLFdBQUEsRUFBQSw0QkFBQTtBQVRGOztBQWFBO0VBQ0UsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFWRjs7QUFhQTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7QUFWRjs7QUFhQTtFQUNFLHlCQUFBO0FBVkY7O0FBWUE7RUFDRSxpQkFBQTtBQVRGOztBQVlBO0VBQ0Usa0NBQUE7RUFDQSx1QkFBQTtFQUNBLG9EQUFBO0FBVEY7O0FBWUE7RUFDRSx1QkFBQSxFQUFBLHNDQUFBO0VBQ0MsaUNBQUE7RUFFQSxxRUFBQTtBQVZIIiwic291cmNlc0NvbnRlbnQiOlsiICAuc2VjdGlvbntcbiAgICB3aWR0aDogMTAwJTtcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xuICB9XG5cbiAgLm5vLW91dGxpbmU6ZGlzYWJsZWQge1xuICAgIG91dGxpbmU6IG5vbmU7XG4gIH1cblxuICA6Om5nLWRlZXAgLm1hdC1zdGVwLWljb24tc2VsZWN0ZWQge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjExLCAyMTEsIDIxMSwgMC4zNjA3ODQzMTM3KSAhaW1wb3J0YW50O1xuICAgfVxuICBcbiAgOjpuZy1kZWVwIC5tYXQtc3RlcC1pY29uLXN0YXRlLWRvbmUge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMTAxLCAxMjksIDAuOCkgIWltcG9ydGFudDtcbiAgfVxuICBcbiAgLmRpc2FibGVkQnRue1xuICAgIGNvbG9yOiBncmV5O1xuICB9XG5cbi5jcmVhdGVDbGFzc3tcbiAgbWFyZ2luOiAyLjVyZW0gYXV0bztcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uY3JlYXRlVGV4dENsYXNze1xuICBmb250LXNpemU6IGxhcmdlO1xufVxuXG4uY3JlYXRlQnRuQ2xhc3N7XG4gIG1hcmdpbjogMTVweCBhdXRvO1xuICBkaXNwbGF5OiB0YWJsZTtcbn1cblxuLmNyZWF0ZUJ0bntcbiAgbWFyZ2luOiAwIGF1dG87XG59XG5cbm1hdC1lcnJvciB7XG4gIGNvbG9yOiByZWQ7IFxufVxuXG4ubWF0ZXJpYWwtc3ltYm9scy1vdXRsaW5lZCB7XG4gIGZvbnQtc2l6ZTogNDBweDsgXG4gIGxpbmUtaGVpZ2h0OiA0OHB4O1xuICBjb2xvcjogZ3JlZW47XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cblxuLmJvdHRvbS10aXRsZXMge1xuICAvLyBmb250LXNpemU6IDEuNWVtO1xuICAvLyBmb250LXdlaWdodDogYm9sZDtcbiAgLy8gY29sb3I6ICM0NDQ7XG4gIC8vIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMzQzYTQwOyAvKiBEYXJrIEdyYXkgKi9cbiAgLy8gcGFkZGluZy1ib3R0b206IDEwcHg7XG4gIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNik7XG4gIGZvbnQtc2l6ZTogbGFyZ2VyO1xuICBmb250LXdlaWdodDogYm9sZGVyO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTVlNWU1O1xufVxuXG4ucG9ydGlvbi1pbmZvIHtcbiAgbWFyZ2luLXRvcDogMjBweDtcbiAgcGFkZGluZzogMTBweDtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMGUwZTA7XG59XG5cbi5wb3J0aW9uLWluZm8xe1xuICBtYXJnaW4tdG9wOiAyMHB4O1xuICBwYWRkaW5nOiAxMHB4O1xufVxuXG4uaW5mby1pdGVtIHtcbiAgbWFyZ2luLWJvdHRvbTogMTBweDtcbiAgcGFkZGluZzogMTBweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xufVxuXG4uaW5mby1jb250ZW50IHtcbiAgbWFyZ2luOiAwO1xuICBmb250LXNpemU6IDEuMWVtO1xuICBjb2xvcjogIzAwNDE3NTsgLyogTmF2eSBCbHVlICovXG59XG5cbi5pbmZvLWNvbnRlbnQgc3Ryb25nIHtcbiAgY29sb3I6ICMwMDQxNzU7IC8qIE5hdnkgQmx1ZSAqL1xufVxuXG4uZ29sZGVuLXllbGxvdy1jaGlwIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmYzEwNyAhaW1wb3J0YW50OyBcbiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC44NykgIWltcG9ydGFudDsgXG4gIG1heC1oZWlnaHQ6IDEuMnJlbTtcbn1cblxuLmNsb3NlLWJ0biB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xufVxuXG4uY2xvc2UtYnRuLWljb24ge1xuICBjb2xvcjogI2ZmMDAwMDsgLyogb3IgYW55IGNvbG9yIGZvciB0aGUgY2xvc2UgYnV0dG9uICovXG59XG5cbi5yZWdpc3RyYXRpb24tZm9ybSB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmOWY5Zjk7IC8qIEJhY2tncm91bmQgY29sb3IgZm9yIHRoZSBmb3JtICovXG4gIGJvcmRlci1yYWRpdXM6IDhweDsgLyogUm91bmRlZCBjb3JuZXJzICovXG4gIGJveC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7IC8qIFNoYWRvdyBmb3IgYmV0dGVyIGFwcGVhcmFuY2UgKi9cbn1cblxuLy8gLmJvdHRvbS10aXRsZXMgc3BhbiB7XG4vLyAgIGZvbnQtc2l6ZTogMS4yZW07IC8qIEluY3JlYXNlIHRoZSBmb250IHNpemUgZm9yIHRpdGxlcyAqL1xuLy8gICBmb250LXdlaWdodDogYm9sZDsgLyogTWFrZSB0aXRsZSBib2xkICovXG4vLyB9XG5cbi5wb3J0aW9uLWluZm8ge1xuICBtYXJnaW4tdG9wOiAxMHB4O1xufVxuXG4uaW5mby10YWJsZSB7XG4gIHdpZHRoOiAxMDAlO1xuICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlO1xufVxuXG4uaW5mby10YWJsZSB0ZCB7XG4gIHBhZGRpbmc6IDhweDtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNkZGQ7IC8qIExpZ2h0IGdyYXkgYm9yZGVyIGZvciByb3dzICovXG59XG5cbi5pbmZvLWtleSB7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuXG4uaW5mby12YWx1ZSB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBjb2xvcjogIzMzMzsgLyogRGFya2VyIGNvbG9yIGZvciB2YWx1ZXMgKi9cbn1cblxuLmluZm8tdW5pdCB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBjb2xvcjogIzY2NjsgLyogTGlnaHRlciBjb2xvciBmb3IgdW5pdHMgKi9cbn1cblxuXG4ubm9uLWVkaXRhYmxlLWZpZWxkIHtcbiAgcGFkZGluZzogOHB4IDEycHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgbWFyZ2luLWJvdHRvbTogMTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLm5vbi1lZGl0YWJsZS1maWVsZCBsYWJlbCB7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBtYXJnaW4tYm90dG9tOiA0cHg7XG59XG5cbi5yZWFkb25seS1maWVsZCB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNlMGUwZTA7XG59XG4ubWFwcGluZ0J0biB7XG4gIG1hcmdpbi1yaWdodDogNXB4O1xufVxuXG4uZGlzY0J1dHRvbntcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGUgIWltcG9ydGFudDtcbiAgY29sb3I6IGJsYWNrICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoOCwgODcsIDE1MSwgMC42ODUpICFpbXBvcnRhbnQ7XG59XG5cbi5zdWItZGF0YS1jbGFzcyB7XG4gIGNvbG9yOiByZ2IoMTE5LCAxMiwgMTIpOyAvKiBDaGFuZ2UgdGhpcyB0byB5b3VyIGRlc2lyZWQgY29sb3IgKi9cbiAgIC8qIE9wdGlvbmFsOiBtYWtlIHRoZSB0ZXh0IGJvbGQgKi9cblxuICAgY29sb3I6IHJnYmEodmFyKC0tYnMtd2FybmluZy1yZ2IpLCB2YXIoLS1icy10ZXh0LW9wYWNpdHkpKSAhaW1wb3J0YW50O1xuXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ActionComponent };", "map": {"version": 3, "names": ["ElementRef", "CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatButtonModule", "MatCardModule", "MatNativeDateModule", "MatOption", "MatDatepickerModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSliderModule", "MatSelectModule", "MatRadioModule", "Subject", "first", "map", "startWith", "takeUntil", "MatAutocompleteModule", "MatDividerModule", "MAT_DIALOG_DATA", "MatDialogModule", "ReplaySubject", "NgxMatSelectSearchModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "MatToolbarModule", "MatSort", "MatStepperModule", "NgxSkeletonLoaderModule", "MatPaginator", "MatPaginatorModule", "MatChipsModule", "A11yModule", "MatSlideToggleModule", "EmptyStateComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ActionComponent_div_0_Template_mat_icon_click_1_listener", "ɵɵrestoreView", "_r22", "ctx_r21", "ɵɵnextContext", "ɵɵresetView", "close", "ɵɵtext", "ɵɵelementEnd", "ActionComponent_div_3_Template_input_keyup_4_listener", "$event", "_r24", "ctx_r23", "filterDialog", "ɵɵproperty", "item_r32", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ActionComponent_div_4_Template_input_keyup_8_listener", "_r34", "ctx_r33", "checkItem", "ActionComponent_div_4_Template_input_keyup_enter_8_listener", "ctx_r35", "addOption", "ActionComponent_div_4_Template_mat_autocomplete_optionSelected_9_listener", "ctx_r36", "optionSelected", "option", "ɵɵtemplate", "ActionComponent_div_4_mat_option_11_Template", "ActionComponent_div_4_Template_button_click_16_listener", "ctx_r37", "ActionComponent_div_4_div_17_Template", "ActionComponent_div_4_mat_icon_18_Template", "ActionComponent_div_4_mat_icon_19_Template", "ActionComponent_div_4_span_20_Template", "ActionComponent_div_4_span_21_Template", "_r25", "ctx_r3", "itemNameControl", "itemNameOptions", "value", "loadBtn", "updateBtnActive", "ActionComponent_button_6_Template_button_click_0_listener", "_r39", "ctx_r38", "printOption", "ActionComponent_button_7_Template_button_click_0_listener", "_r42", "ctx_r41", "update", "ActionComponent_button_7_div_1_Template", "ctx_r5", "loadSrmBtn", "loadSpinnerForApi", "ActionComponent_button_8_Template_button_click_0_listener", "_r45", "ctx_r44", "submit", "isButtonDisabled", "ActionComponent_button_8_div_1_Template", "ctx_r6", "dataSource", "data", "length", "registrationForm", "invalid", "ActionComponent_button_9_Template_button_click_0_listener", "_r47", "ctx_r46", "uom_r73", "ɵɵtextInterpolate1", "uom_r74", "cat_r75", "sub_r76", "ActionComponent_div_12_mat_option_59_mat_icon_4_Template_mat_icon_click_0_listener", "_r82", "loc_r77", "$implicit", "ctx_r80", "onDelete", "ɵɵpureFunction1", "_c6", "ctx_r78", "discontinuedPreLocData", "includes", "ActionComponent_div_12_mat_option_59_mat_icon_5_Template_mat_icon_click_0_listener", "_r86", "ctx_r84", "onRestore", "ActionComponent_div_12_mat_option_59_mat_icon_4_Template", "ActionComponent_div_12_mat_option_59_mat_icon_5_Template", "ctx_r54", "_c7", "defaultPreLocData", "_c8", "ActionComponent_div_12_mat_option_72_mat_icon_4_Template_mat_icon_click_0_listener", "_r92", "loc_r87", "ctx_r90", "ctx_r88", "discontinuedOutletData", "ActionComponent_div_12_mat_option_72_mat_icon_5_Template_mat_icon_click_0_listener", "_r96", "ctx_r94", "ActionComponent_div_12_mat_option_72_mat_icon_4_Template", "ActionComponent_div_12_mat_option_72_mat_icon_5_Template", "ctx_r55", "defaultOutletData", "ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_4_Template_mat_icon_click_0_listener", "_r104", "data_r99", "group_r97", "ctx_r102", "ctx_r100", "discontinuedIssuedToData", "ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_5_Template_mat_icon_click_0_listener", "_r109", "ctx_r107", "ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_4_Template", "ActionComponent_div_12_mat_optgroup_81_mat_option_1_mat_icon_5_Template", "ctx_r98", "isOptionDisabled", "isCheckOptionDisabled", "defaultIssuedToData", "disabled", "abbreviatedRestaurantId", "ActionComponent_div_12_mat_optgroup_81_mat_option_1_Template", "restaurantIdOld", "split", "work<PERSON><PERSON><PERSON>", "unit_r112", "name_r113", "_c9", "ctx_r63", "subData", "updateSRR", "subRecipeRecipeForm", "ingredientName", "data_r114", "ctx_r64", "isOptionAccessible", "ActionComponent_div_12_div_150_Template_input_focus_3_listener", "_r116", "ctx_r115", "focusFunctionSRR", "ActionComponent_div_12_div_150_Template_input_focusout_3_listener", "ctx_r117", "focusOutFunctionSRR", "ActionComponent_div_12_div_150_Template_input_keyup_3_listener", "ctx_r118", "convertPortionToUOM", "i_r168", "ɵɵelement", "ActionComponent_div_12_div_176_mat_cell_8_Template_button_click_1_listener", "restoredCtx", "_r171", "element_r169", "ctx_r170", "_r10", "ɵɵreference", "editFun", "ActionComponent_div_12_div_176_mat_cell_8_Template_button_click_4_listener", "ctx_r172", "deleteSRR", "ActionComponent_div_12_div_176_mat_cell_12_div_1_Template", "ActionComponent_div_12_div_176_mat_cell_12_div_2_Template", "ActionComponent_div_12_div_176_mat_cell_12_div_3_Template", "element_r173", "Discontinued", "ActionComponent_div_12_div_176_mat_cell_16_div_1_Template", "ActionComponent_div_12_div_176_mat_cell_16_div_2_Template", "element_r177", "modified", "ActionComponent_div_12_div_176_mat_cell_20_Template_div_click_1_listener", "_r184", "element_r180", "ctx_r183", "ActionComponent_div_12_div_176_mat_cell_20_mat_icon_3_Template", "ActionComponent_div_12_div_176_mat_cell_20_mat_icon_4_Template", "ActionComponent_div_12_div_176_mat_cell_20_Template_button_click_5_listener", "ctx_r185", "ctx_r132", "element_r186", "ingredientCode", "element_r187", "subRecipeName", "element_r188", "subRecipeCode", "element_r189", "UOM", "element_r190", "Initialweight", "ctx_r148", "getTotal", "element_r191", "yield", "element_r192", "loss", "ctx_r156", "notify", "truncateAndFloor", "element_r193", "weightInUse", "ctx_r157", "ctx_r159", "element_r194", "rate", "ctx_r162", "element_r195", "finalRate", "ctx_r163", "getSRRTotal", "_c10", "row_r196", "ɵɵelementContainerStart", "ActionComponent_div_12_div_176_mat_header_cell_3_Template", "ActionComponent_div_12_div_176_mat_cell_4_Template", "ActionComponent_div_12_div_176_mat_footer_cell_5_Template", "ɵɵelementContainerEnd", "ActionComponent_div_12_div_176_mat_header_cell_7_Template", "ActionComponent_div_12_div_176_mat_cell_8_Template", "ActionComponent_div_12_div_176_mat_footer_cell_9_Template", "ActionComponent_div_12_div_176_mat_header_cell_11_Template", "ActionComponent_div_12_div_176_mat_cell_12_Template", "ActionComponent_div_12_div_176_mat_footer_cell_13_Template", "ActionComponent_div_12_div_176_mat_header_cell_15_Template", "ActionComponent_div_12_div_176_mat_cell_16_Template", "ActionComponent_div_12_div_176_mat_footer_cell_17_Template", "ActionComponent_div_12_div_176_mat_header_cell_19_Template", "ActionComponent_div_12_div_176_mat_cell_20_Template", "ActionComponent_div_12_div_176_mat_footer_cell_21_Template", "ActionComponent_div_12_div_176_mat_header_cell_23_Template", "ActionComponent_div_12_div_176_mat_cell_24_Template", "ActionComponent_div_12_div_176_mat_footer_cell_25_Template", "ActionComponent_div_12_div_176_mat_header_cell_27_Template", "ActionComponent_div_12_div_176_mat_cell_28_Template", "ActionComponent_div_12_div_176_mat_footer_cell_29_Template", "ActionComponent_div_12_div_176_mat_header_cell_31_Template", "ActionComponent_div_12_div_176_mat_cell_32_Template", "ActionComponent_div_12_div_176_mat_footer_cell_33_Template", "ActionComponent_div_12_div_176_mat_header_cell_35_Template", "ActionComponent_div_12_div_176_mat_cell_36_Template", "ActionComponent_div_12_div_176_mat_footer_cell_37_Template", "ActionComponent_div_12_div_176_mat_header_cell_39_Template", "ActionComponent_div_12_div_176_mat_cell_40_Template", "ActionComponent_div_12_div_176_mat_footer_cell_41_Template", "ActionComponent_div_12_div_176_mat_header_cell_43_Template", "ActionComponent_div_12_div_176_mat_cell_44_Template", "ActionComponent_div_12_div_176_mat_footer_cell_45_Template", "ActionComponent_div_12_div_176_mat_header_cell_47_Template", "ActionComponent_div_12_div_176_mat_cell_48_Template", "ActionComponent_div_12_div_176_mat_footer_cell_49_Template", "ActionComponent_div_12_div_176_mat_header_cell_51_Template", "ActionComponent_div_12_div_176_mat_cell_52_Template", "ActionComponent_div_12_div_176_mat_footer_cell_53_Template", "ActionComponent_div_12_div_176_mat_header_cell_55_Template", "ActionComponent_div_12_div_176_mat_cell_56_Template", "ActionComponent_div_12_div_176_mat_footer_cell_57_Template", "ActionComponent_div_12_div_176_mat_header_cell_59_Template", "ActionComponent_div_12_div_176_mat_cell_60_Template", "ActionComponent_div_12_div_176_mat_footer_cell_61_Template", "ActionComponent_div_12_div_176_mat_header_row_62_Template", "ActionComponent_div_12_div_176_mat_row_63_Template", "ActionComponent_div_12_div_176_mat_footer_row_64_Template", "ctx_r70", "displayedColumns", "ɵɵpureFunction0", "_c11", "_c12", "ActionComponent_div_12_div_178_app_empty_state_1_Template", "ActionComponent_div_12_div_178_app_empty_state_2_Template", "ctx_r72", "showDeleteItems", "ActionComponent_div_12_mat_option_23_Template", "ActionComponent_div_12_mat_option_29_Template", "ActionComponent_div_12_Template_input_keyup_enter_34_listener", "_r200", "ctx_r199", "addOptionCat", "ActionComponent_div_12_Template_mat_autocomplete_optionSelected_35_listener", "ctx_r201", "optionSelectedCat", "ActionComponent_div_12_mat_option_37_Template", "ActionComponent_div_12_Template_input_keyup_enter_43_listener", "ctx_r202", "addOptionSubCat", "ActionComponent_div_12_Template_mat_autocomplete_optionSelected_44_listener", "ctx_r203", "optionSelectedSubCat", "ActionComponent_div_12_mat_option_46_Template", "ActionComponent_div_12_Template_mat_option_click_55_listener", "ctx_r204", "preparedToggleSelectAll", "ActionComponent_div_12_mat_option_59_Template", "ActionComponent_div_12_Template_mat_select_selectionC<PERSON>e_65_listener", "ctx_r205", "locationChange", "ActionComponent_div_12_Template_mat_option_click_68_listener", "ctx_r206", "usedOutLetToggleSelectAll", "ActionComponent_div_12_mat_option_72_Template", "ActionComponent_div_12_mat_optgroup_81_Template", "ActionComponent_div_12_mat_error_83_Template", "ActionComponent_div_12_Template_span_click_101_listener", "ctx_r207", "openUnitDialog", "ActionComponent_div_12_Template_input_keyup_114_listener", "ctx_r208", "setZeroSRM", "ActionComponent_div_12_Template_input_focus_114_listener", "ctx_r209", "focusFunction", "ActionComponent_div_12_Template_input_focusout_114_listener", "ctx_r210", "focusOutFunction", "ActionComponent_div_12_mat_error_115_Template", "ActionComponent_div_12_Template_mat_select_selectionChange_121_listener", "ctx_r211", "onUnitChange", "ActionComponent_div_12_mat_option_122_Template", "ActionComponent_div_12_Template_input_keyup_124_listener", "ctx_r212", "setPortionData", "ActionComponent_div_12_Template_input_focus_124_listener", "ctx_r213", "ActionComponent_div_12_Template_input_focusout_124_listener", "ctx_r214", "ActionComponent_div_12_mat_error_125_Template", "ActionComponent_div_12_Template_mat_icon_click_126_listener", "ctx_r215", "openPortionData", "ActionComponent_div_12_div_128_Template", "ActionComponent_div_12_Template_mat_autocomplete_optionSelected_141_listener", "ctx_r216", "selectIngredientsName", "ActionComponent_div_12_mat_option_143_Template", "ActionComponent_div_12_Template_select_change_148_listener", "ctx_r217", "uomChange", "ActionComponent_div_12_option_149_Template", "ActionComponent_div_12_div_150_Template", "ActionComponent_div_12_Template_input_focus_154_listener", "ctx_r218", "ActionComponent_div_12_Template_input_focusout_154_listener", "ctx_r219", "ActionComponent_div_12_Template_input_keyup_154_listener", "ctx_r220", "sumForFinalRateSRR", "ctx_r221", "ctx_r222", "ActionComponent_div_12_Template_input_focus_158_listener", "ctx_r223", "ActionComponent_div_12_Template_input_focusout_158_listener", "ctx_r224", "ActionComponent_div_12_Template_input_keyup_158_listener", "ctx_r225", "ActionComponent_div_12_div_159_Template", "ActionComponent_div_12_Template_input_keyup_163_listener", "ctx_r226", "setZeroSRR", "ActionComponent_div_12_Template_input_focus_163_listener", "ctx_r227", "ActionComponent_div_12_Template_input_focusout_163_listener", "ctx_r228", "ActionComponent_div_12_div_164_Template", "ActionComponent_div_12_Template_button_click_166_listener", "ctx_r229", "addNewSubRecipeRecipe", "ActionComponent_div_12_Template_mat_slide_toggle_ngModelChange_171_listener", "ctx_r230", "ActionComponent_div_12_Template_mat_slide_toggle_change_171_listener", "ctx_r231", "showItems", "ActionComponent_div_12_div_176_Template", "ActionComponent_div_12_div_177_Template", "ActionComponent_div_12_div_178_Template", "ctx_r9", "isReadOnly", "_c13", "_c14", "_c15", "_r50", "catBank", "_r52", "subCatBank", "preparedFilterCtrl", "preparedLocationNames", "outletFilterCtrl", "outletLocationNames", "usedInWorkAreaFilterCtrl", "usedInWorkAreaNames", "showWorkAreaError", "get", "recovery", "errors", "units", "isUpdateActive", "_r62", "ingredientNamesOptions", "ingredientUOM", "uom", "showWeightError", "isSRRDataReady", "ActionComponent_ng_template_13_button_8_Template_button_click_0_listener", "_r239", "ctx_r238", "editExistingSubRecipeRecipe", "ActionComponent_ng_template_13_button_8_div_1_Template", "ctx_r232", "loadSpinnerForApiSRR", "name_r240", "ctx_r233", "data_r241", "ctx_r234", "ActionComponent_ng_template_13_div_33_Template_input_keyup_4_listener", "_r243", "ctx_r242", "ActionComponent_ng_template_13_div_33_Template_input_focus_4_listener", "ctx_r244", "ActionComponent_ng_template_13_div_33_Template_input_focusout_4_listener", "ctx_r245", "ActionComponent_ng_template_13_Template_mat_icon_click_1_listener", "_r247", "ctx_r246", "closeSRRDialog", "ActionComponent_ng_template_13_button_8_Template", "ActionComponent_ng_template_13_Template_mat_select_selectionChange_22_listener", "ctx_r248", "ActionComponent_ng_template_13_mat_option_25_Template", "ActionComponent_ng_template_13_mat_option_32_Template", "ActionComponent_ng_template_13_div_33_Template", "ActionComponent_ng_template_13_Template_input_focus_38_listener", "ctx_r249", "ActionComponent_ng_template_13_Template_input_focusout_38_listener", "ctx_r250", "ActionComponent_ng_template_13_Template_input_keyup_38_listener", "ctx_r251", "ActionComponent_ng_template_13_Template_input_keyup_43_listener", "ctx_r252", "ActionComponent_ng_template_13_Template_input_focus_43_listener", "ctx_r253", "ActionComponent_ng_template_13_Template_input_focusout_43_listener", "ctx_r254", "ActionComponent_ng_template_13_div_44_Template", "ctx_r11", "_c16", "IngredientFilterCtrl", "ingredientNames", "ɵɵtextInterpolate2", "i_r258", "data_r257", "ActionComponent_div_15_div_1_Template", "ActionComponent_div_15_div_2_Template", "ctx_r12", "filteredData", "ActionComponent_ng_template_16_Template_button_click_2_listener", "_r260", "ctx_r259", "closeInfoDialog", "ctx_r14", "getWeightPerPortion", "getCostPerPortion", "ActionComponent_ng_template_18_Template_button_click_2_listener", "_r262", "ctx_r261", "ctx_r16", "getPerKGCost", "ActionComponent_ng_template_20_Template_button_click_2_listener", "_r264", "ctx_r263", "ActionComponent_ng_template_20_Template_button_click_13_listener", "ctx_r265", "deleteData", "ActionComponent_ng_template_20_Template_button_click_15_listener", "ctx_r266", "discontinueData", "ActionComponent_ng_template_22_Template_button_click_7_listener", "_r268", "ctx_r267", "discontinuedSelectData", "ActionComponent_ng_template_22_Template_button_click_9_listener", "ctx_r269", "closeDiscontinuedDialog", "ctx_r20", "selectedData", "ActionComponent", "constructor", "fb", "api", "activatedRoute", "router", "dialog", "sharedData", "cache", "dialogData", "renderer", "el", "auth", "cd", "masterDataService", "question", "costDialogkey", "subRecipeData", "showSRR", "AccessibleUOM", "Bank", "usedInWorkAreaBank", "preparedBank", "outletBank", "_onD<PERSON>roy", "baseData", "isEditable", "ratio", "loadSrrBtn", "discontinueDatas", "removedItem", "discData", "disableOption", "globalLocation", "getGlLocation", "user", "getCurrentUser", "getBaseData", "newCategory", "cat", "category", "toUpperCase", "tenantId", "getRolesListDiscontinuedLocations", "subscribe", "res", "discontinuedLocations", "detectChanges", "group", "required", "subCategory", "menuItemCode", "menuItemName", "closingUOM", "itemType", "preparedAt", "usedAtOutlet", "usedInWorkArea", "yieldValidator", "portion", "portionValidator", "unit", "discontinued", "row_uuid", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialWeight", "defaultUOM", "portionCount", "isDuplicate", "key", "getItemNames", "obj", "valueChanges", "pipe", "_filter", "preparedAtLocation", "locations", "usedAtOutletLocation", "getMenuRecipes", "getCategories", "readIPConfig", "control", "<PERSON><PERSON><PERSON><PERSON>", "yieldInvalid", "portionInvalid", "reflectSubRecipe", "setValue", "currentInvItems", "getInvItems", "hasOwnProperty", "invItems", "filter", "item", "itemName", "ItemType", "invData", "ingredientData", "Set", "next", "slice", "Filter", "getLocationCall", "getInventoryListForSubrecipeMD", "error", "err", "console", "log", "ngOnDestroy", "complete", "ngAfterViewInit", "paginator", "sort", "bank", "form", "search", "toLowerCase", "indexOf", "FilterIssued", "filteredBank", "filteredWorkAreas", "workArea", "mark<PERSON>llAsTouched", "snackBarShowError", "updatedSRMData", "convertSRMKeys", "join", "Object", "keys", "tempObj", "requiredVendor", "find", "snackBarShowInfo", "for<PERSON>ach", "unshift", "email", "updateData", "updateBaseDataForSRR", "snackBarShowSuccess", "patchValue", "conversionCoefficient", "getPortionWeightForSubRecipe", "requiredItem", "itemCode", "portionWeight", "deleteDataFormDB", "setDiscontinuedDataInRolopos", "flatMap", "index", "delete", "undefined", "updateSubRecipeMaster", "updateMenuMaster", "updatedMenuMaster", "updatedMenuRecipe", "updatedSubRecipeMaster", "updatedSubRecipeRecipe", "concat", "updateBaseData", "getPresentData", "latestSRR", "currentSRR", "exist", "findIndex", "push", "setBaseData", "revisedTotalWeight", "requiredItems", "reduce", "sum", "requiredData", "input", "filterValue", "filtered", "event", "invItem", "getDataForFillTheForm", "target", "type", "reset", "subRecipeMasterItem", "setValuesForForm", "generateCode", "controls", "removePromptFromOption", "code", "_this", "getCode", "_ref", "_asyncToGenerator", "_x", "apply", "arguments", "Array", "isArray", "subRecipeLocations", "outLetDiscontinued", "issuedToDiscontinued", "procuredAtDiscontinued", "startsWith", "substring", "setNavigation", "navigate", "closeAll", "element", "addSRR", "dialogRef", "open", "maxHeight", "max<PERSON><PERSON><PERSON>", "afterClosed", "result", "closeSRRRef", "convertSRRKeys", "existingItem", "entries", "tempData", "clearForm", "snackBarShowWarning", "setErrors", "updatedSRRData", "requiredPackage", "tempDiscData", "pop", "items", "disData", "goBack", "setTimeout", "dataSourceTable", "nativeElement", "querySelector", "scrollIntoView", "behavior", "block", "keyData", "updatedRecipeData", "updatedSubRecipeData", "formattedRec", "applyFilter", "trim", "toggleSelectAll", "location", "flattenedArray", "dropDownData", "val", "formData", "catAndsubCat", "categoryData", "newCat", "categories", "getSubCategories", "newSubCategory", "subCat", "newSubCat", "subCategories", "formKey", "scrollRight", "widgetsContent", "scrollTo", "left", "scrollLeft", "a", "b", "nextTab", "stepper", "total", "locationList", "getLocation", "branch", "elements", "logo", "tenantDetails", "discontinuedValue", "status", "tableData", "factory_name", "name", "recipe_details", "table_headers", "table_data", "summary", "printInvoice", "globalPrintPdf", "pdf_base64", "selectedWorkAreasArray", "branchName", "openPortionDialog", "min<PERSON><PERSON><PERSON>", "panelClass", "autoFocus", "disableClose", "openUnitCostDialog", "openDeleteDialog", "indexToRemove", "recipe", "removeData", "openSelect", "select", "reqWeight", "reqPortion", "recipeIndex", "stopPropagation", "selectedDropDown", "groupData", "discontinuedSelectDialog", "width", "matchingIssued", "issuedItem", "newObject", "newArray", "some", "dicontinuedData", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "InventoryService", "i3", "ActivatedRoute", "Router", "i4", "MatDialog", "i5", "ShareDataService", "i6", "SessionCacheService", "i7", "NotificationService", "Renderer2", "i8", "AuthService", "ChangeDetectorRef", "i9", "MasterDataService", "selectors", "viewQuery", "ActionComponent_Query", "rf", "ctx", "ActionComponent_div_0_Template", "ActionComponent_div_2_Template", "ActionComponent_div_3_Template", "ActionComponent_div_4_Template", "ActionComponent_button_6_Template", "ActionComponent_button_7_Template", "ActionComponent_button_8_Template", "ActionComponent_button_9_Template", "ActionComponent_div_11_Template", "ActionComponent_div_12_Template", "ActionComponent_ng_template_13_Template", "ɵɵtemplateRefExtractor", "ActionComponent_div_15_Template", "ActionComponent_ng_template_16_Template", "ActionComponent_ng_template_18_Template", "ActionComponent_ng_template_20_Template", "ActionComponent_ng_template_22_Template", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "i10", "MatChip", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "UpperCasePipe", "FormControlDirective", "FormGroupDirective", "FormControlName", "i12", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i13", "MatInput", "i14", "MatButton", "MatIconButton", "i15", "MatIcon", "i16", "MatSelect", "i17", "MatOptgroup", "i18", "MatRadioGroup", "MatRadioButton", "i19", "MatAutocomplete", "MatAutocompleteTrigger", "i20", "MatSelectSearchComponent", "i21", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatFooterCellDef", "MatFooterRowDef", "MatHeaderCell", "Mat<PERSON>ell", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatHeaderRow", "MatRow", "MatFooterRow", "i22", "MatTooltip", "i23", "NgxSkeletonLoaderComponent", "i24", "i25", "CdkTrapFocus", "i26", "MatSlideToggle", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/inventory-management/subrecipe-master/action/action.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/inventory-management/subrecipe-master/action/action.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Inject,\n  QueryList,\n  TemplateRef,\n  ViewChild,\n  ViewChildren,\n} from '@angular/core';\nimport { Renderer2, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport {\n  AbstractControl,\n  FormBuilder,\n  FormControl,\n  FormGroup,\n  FormsModule,\n  ReactiveFormsModule,\n  ValidatorFn,\n  Validators,\n} from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatNativeDateModule, MatOption } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatSelect, MatSelectChange, MatSelectModule } from '@angular/material/select';\nimport { MatRadioModule } from '@angular/material/radio';\nimport {\n  Observable,\n  Subject,\n  first,\n  map,\n  of,\n  startWith,\n  takeUntil,\n} from 'rxjs';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatDividerModule } from '@angular/material/divider';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialog,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { ActionComponent as ActionComponentVendor } from '../../vendor/action/action.component';\nimport { AutocompleteComponent } from '../../../../components/autocomplete/autocomplete.component';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { ReplaySubject } from 'rxjs';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { TooltipPosition, MatTooltipModule } from '@angular/material/tooltip';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { MatSort } from '@angular/material/sort';\nimport { MatStepper, MatStepperModule } from '@angular/material/stepper';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport { SessionCacheService } from 'src/app/services/session-cache.service';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';\n\n@Component({\n  selector: 'app-action',\n  standalone: true,\n  templateUrl: './action.component.html',\n  styleUrls: ['./action.component.scss'],\n  imports: [\n    FormsModule,\n    MatDialogModule,\n    MatChipsModule,\n    CommonModule,\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatNativeDateModule,\n    MatDatepickerModule,\n    MatInputModule,\n    MatSliderModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatSelectModule,\n    MatRadioModule,\n    MatAutocompleteModule,\n    MatDividerModule,\n    AutocompleteComponent,\n    NgxMatSelectSearchModule,\n    MatTableModule,\n    MatTooltipModule,\n    MatToolbarModule,\n    MatStepperModule,\n    NgxSkeletonLoaderModule,\n    MatPaginatorModule,\n    A11yModule,\n    MatSlideToggleModule,\n    EmptyStateComponent\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ActionComponent {\n  @ViewChild(MatPaginator) paginator: MatPaginator;\n  @ViewChild('stepper', { static: false }) private stepper: MatStepper;\n  @ViewChild('openPortionDialog') openPortionDialog: TemplateRef<any>;\n  @ViewChild('openUnitCostDialog') openUnitCostDialog: TemplateRef<any>;\n  @ViewChild('openDeleteDialog') openDeleteDialog: TemplateRef<any>;\n  @ViewChildren(MatOption) matOptions: QueryList<MatOption>;\n  @ViewChild('widgetsContent', { read: ElementRef }) public widgetsContent;\n  @ViewChild(MatSort) sort!: MatSort;\n  question = 'Would you like to add \"';\n  itemNameOptions: Observable<string[]>;\n  itemNameControl = new FormControl('');\n  registrationForm!: FormGroup;\n  subRecipeRecipeForm!: FormGroup;\n  public isUpdateActive: boolean = false;\n  categories: any;\n  subCategories: any;\n  isDuplicate: boolean;\n  costDialogkey: boolean = false;\n  preparedAtLocation: any[];\n  usedAtOutletLocation: any[];\n  dataSource = new MatTableDataSource<any>([]);\n  displayedColumns: string[];\n  subRecipeData = [];\n  showSRR: boolean = false;\n  updateSRR: boolean = false;\n  isReadOnly: boolean = true;\n  loadBtn: boolean = false;\n  dropDownData: any;\n  AccessibleUOM: any[] = [];\n  ingredientUOM = ['GM', 'ML', 'NOS','MM', 'PORTION'] ;\n  filteredData: any[];\n  subcategories: any;\n  ingredientData: any;\n  public Bank: any[] = [];\n  public IngredientFilterCtrl: FormControl = new FormControl();\n  public ingredientNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public usedInWorkAreaBank: any[] = [];\n  public usedInWorkAreaFilterCtrl: FormControl = new FormControl();\n  public usedInWorkAreaNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(\n    1\n  );\n  public preparedBank: any[] = [];\n  public preparedFilterCtrl: FormControl = new FormControl();\n  public preparedLocationNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(\n    1\n  );\n  public outletBank: any[] = [];\n  public outletFilterCtrl: FormControl = new FormControl();\n  public outletLocationNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(\n    1\n  );\n  catBank: Observable<string[]>;\n  subCatBank: Observable<string[]>;\n  ingredientNamesOptions: Observable<string[]>;\n  protected _onDestroy = new Subject<void>();\n  dialogRef: MatDialogRef<any>;\n  baseData: object = {};\n  user: any;\n  updateBtnActive: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  loadSpinnerForApiSRR: boolean = false;\n  newCategory: any;\n  catAndsubCat: any;\n  newSubCategory: any;\n  closeSRRRef: MatDialogRef<unknown, any>;\n  isEditable = false;\n  isSRRDataReady: boolean = false;\n  units: string[] = ['Portion', 'GM/ML'];\n  invItems: any;\n  ratio: number = 1;\n  locationList: any;\n  globalLocation: any;\n  loadSrmBtn: boolean = true;\n  loadSrrBtn: boolean = true;\n  isButtonDisabled = false;\n  showWeightError: boolean = false;\n  showDeleteItems: boolean = false;\n  discontinueDatas: any[] = [];\n  tempData: any[];\n  removedItem: any = [];\n  discData: any=[];\n  showWorkAreaError: boolean;\n  logo: any;\n  status: string;\n  subData: any;\n  filtered: string[];\n  disableOption: boolean = false;\n  @ViewChild('discontinuedSelectDialog') discontinuedSelectDialog: TemplateRef<any>;\n  selectedDropDown: any;\n  selectedData: string;\n  discontinuedPreLocData: any = [];\n  defaultPreLocData: any = [];\n  discontinuedOutletData: any = [];\n  defaultOutletData: any = [];\n  discontinuedIssuedToData: any = [];\n  defaultIssuedToData: any = [];\n  discontinuedLocations: any;\n  groupData: any;\n\n  constructor(\n    @Inject(MAT_DIALOG_DATA) public data: any,\n    private fb: FormBuilder,\n    private api: InventoryService,\n    private activatedRoute: ActivatedRoute,\n    private router: Router,\n    public dialog: MatDialog,\n    private sharedData: ShareDataService,\n    private cache: SessionCacheService,\n    @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    public notify: NotificationService,\n    private renderer: Renderer2,\n    private el: ElementRef,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    private masterDataService: MasterDataService\n  ) {\n    this.globalLocation = this.sharedData.getGlLocation().value;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    this.newCategory = this.baseData['Subrecipe Master'].map(cat => cat.category.toUpperCase());\n    let tenantId = this.user.tenantId\n      this.api.getRolesListDiscontinuedLocations(tenantId)\n      .subscribe((res) => {\n        if(res['result'] == 'success' && res['discontinuedLocations']){\n          this.discontinuedLocations = res['discontinuedLocations'];\n        }\n        this.cd.detectChanges();\n      });\n    this.registrationForm = this.fb.group({\n      category: ['', Validators.required],\n      subCategory: ['', Validators.required],\n      menuItemCode: ['', Validators.required],\n      menuItemName: ['', Validators.required],\n      closingUOM: ['', Validators.required],\n      itemType: ['SubRecipe', Validators.required],\n      preparedAt: ['', Validators.required],\n      usedAtOutlet: ['', Validators.required],\n      usedInWorkArea: ['', Validators.required],\n      uom: ['', Validators.required],\n      weightInUse: [0, Validators.required],\n      yield: [1, [Validators.required, this.yieldValidator()]],\n      portion: [1, [Validators.required, this.portionValidator()]],\n      unit: ['Portion', [Validators.required]],\n      recovery: [0, Validators.required],\n      rate: [0, Validators.required],\n      finalRate: [0],\n      discontinued: ['no', Validators.required],\n      row_uuid: [''],\n    });\n    this.registrationForm.get('yield').markAsTouched();\n    this.registrationForm.get('yield').markAsDirty();\n    this.subRecipeRecipeForm = this.fb.group({\n      subRecipeCode: ['', Validators.required],\n      subRecipeName: ['', Validators.required],\n      ingredientCode: ['', Validators.required],\n      ingredientName: ['', Validators.required],\n      uom: ['', Validators.required],\n      initialWeight: [0],\n      defaultUOM: ['', Validators.required],\n      portionCount: [0],\n      yield: [1, [Validators.required, this.yieldValidator()]],\n      loss: [0],\n      weightInUse: [0, Validators.required],\n      rate: [0, Validators.required],\n      finalRate: [0],\n      discontinued: ['no'],\n      row_uuid: [''],\n    });\n\n    this.isDuplicate = this.dialogData.key;\n    this.costDialogkey = this.dialogData.costDialogkey;\n    this.sharedData.getItemNames.subscribe((obj) => {\n      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(\n        startWith(''),\n        map((value) => this._filter(value || '', obj.menuItemName , ''))\n      );\n      this.preparedAtLocation = obj.locations;\n      this.usedAtOutletLocation = obj.workAreas;\n    });\n    this.getMenuRecipes();\n    this.getCategories();\n    this.readIPConfig();\n  }\n\n  yieldValidator(): ValidatorFn {\n    return (control: AbstractControl): { [key: string]: any } | null => {\n      const isValid = control.value > 0;\n      return isValid ? null : { yieldInvalid: { value: control.value } };\n    };\n  }\n\n  portionValidator(): ValidatorFn {\n    return (control: AbstractControl): { [key: string]: any } | null => {\n      const isValid = control.value > 0;\n      return isValid ? null : { portionInvalid: { value: control.value } };\n    };\n  }\n\n  reflectSubRecipe() {\n    let weightInUse = this.getTotal('Initialweight');\n    this.registrationForm.get('weightInUse').setValue(weightInUse);\n    let recovery = this.registrationForm.value['yield'] * weightInUse;\n    this.registrationForm.get('recovery').setValue(this.notify.truncateAndFloor(recovery));\n    let rate = this.getSRRTotal('finalRate');\n    this.registrationForm.get('rate').setValue(rate);\n    let finalRate = rate / recovery\n    this.registrationForm.get('finalRate').setValue(finalRate);\n    this.cd.detectChanges();\n  }\n\n  getMenuRecipes() {\n    let currentInvItems = this.cache.getInvItems().value;\n    if (\n      currentInvItems.hasOwnProperty(this.globalLocation['restaurantIdOld'])\n    ) {\n      this.invItems = currentInvItems[this.globalLocation['restaurantIdOld']];\n      // this.Bank = this.invItems.map((item) => item.itemName);\n      this.subData = this.invItems\n        .filter((item) => item.itemName && item.ItemType === \"SubRecipe\")\n        .map((item) => item.itemName);\n      let invData = this.invItems\n        .filter((item) => item.itemName && item.ItemType === \"Inventory\")\n        .map((item) => item.itemName);\n\n      this.Bank = [...this.subData, ...invData]\n      this.ingredientData = [...new Set(this.Bank)];\n      this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.ingredientData , 'ingredients')));\n      this.ingredientNames.next(this.Bank.slice());\n      this.IngredientFilterCtrl.valueChanges\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(() => {\n          this.Filter(\n            this.Bank,\n            this.IngredientFilterCtrl,\n            this.ingredientNames\n          );\n        });\n        this.getLocationCall();\n    } else {\n      let obj = {};\n      obj['tenantId'] = this.user.tenantId;\n      obj['restaurantId'] = this.globalLocation['restaurantIdOld'];\n      this.api.getInventoryListForSubrecipeMD(obj).subscribe({\n        next: (res) => {\n          if (res['success']) {\n            // currentInvItems[this.globalLocation['restaurantIdOld']] =\n            //   res['invList'];\n            // this.cache.setInvItems(currentInvItems);\n            this.invItems = res['invList'];\n            this.subData = this.invItems\n              .filter((item) => item.itemName && item.ItemType === \"SubRecipe\")\n              .map((item) => item.itemName);\n\n            let invData = this.invItems\n              .filter((item) => item.itemName && item.ItemType === \"Inventory\")\n              .map((item) => item.itemName);\n\n            this.Bank = [...this.subData, ...invData]\n            this.ingredientData = [...new Set(this.Bank)];\n            this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.ingredientData , 'ingredients')));\n            this.ingredientNames.next(this.Bank.slice());\n            this.IngredientFilterCtrl.valueChanges\n              .pipe(takeUntil(this._onDestroy))\n              .subscribe(() => {\n                this.Filter(\n                  this.Bank,\n                  this.IngredientFilterCtrl,\n                  this.ingredientNames\n                );\n              });\n           this.getLocationCall();\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        },\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  protected Filter(bank, form, data) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    data.next(bank.filter((data) => data.toLowerCase().indexOf(search) > -1));\n  }\n\n  protected FilterIssued(bank, form, data) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    const filteredBank = bank.map((item) => {\n      const filteredWorkAreas = item.workAreas.filter(\n        (workArea) => workArea.toLowerCase().indexOf(search) > -1\n      );\n      return { ...item, workAreas: filteredWorkAreas };\n    });\n    data.next(filteredBank);\n  }\n\n  submit() {\n    this.loadSpinnerForApi = true;\n    this.showWorkAreaError = false;\n    this.baseData = this.sharedData.getBaseData().value;\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      // this.checkWorkArea();\n      if(this.showWorkAreaError){\n        this.registrationForm.markAllAsTouched();\n        this.notify.snackBarShowError('Please fill out all required fields')\n        this.loadSpinnerForApi = false;\n        this.cd.detectChanges();\n      }else{\n        let updatedSRMData = this.convertSRMKeys();\n        updatedSRMData['modified'] = 'yes';\n        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');\n        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');\n        updatedSRMData['usedInWorkArea'] =\n          updatedSRMData['usedInWorkArea'].join(',');\n        let data = this.baseData;\n        if (Object.keys(this.baseData).length > 0) {\n          let tempObj = {};\n          tempObj['Subrecipe Master'] = data['Subrecipe Master'];\n          let requiredVendor = tempObj['Subrecipe Master'].find(\n            (el) => el.menuItemCode == updatedSRMData['menuItemCode']\n          );\n          if (requiredVendor) {\n            this.notify.snackBarShowInfo('Item code already used');\n            this.loadSpinnerForApi = false;\n            this.cd.detectChanges();\n          } else {\n            this.dataSource.data.forEach((el) => {\n              el['Initialweight'] = el['weightInUse'] / el['yield'];\n            });\n            tempObj['Subrecipe Master'].unshift(updatedSRMData);\n            tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(\n              (item) => item.modified === 'yes'\n            );\n            tempObj['Subrecipe Recipe'] = this.dataSource.data;\n            let obj = {};\n            obj['tenantId'] = this.user.tenantId;\n            obj['userEmail'] = this.user.email;\n            obj['data'] = tempObj;\n            obj['type'] = 'recipe';\n            obj['category'] = 'subRecipe';\n            this.api\n              .updateData(obj)\n              .pipe(first())\n              .subscribe({\n                next: (res) => {\n                  if (res['success']) {\n                    this.updateBaseDataForSRR() ;\n                    this.loadSpinnerForApi = false;\n                    this.close();\n                    this.cd.detectChanges();\n                    this.notify.snackBarShowSuccess(\n                      'Sub-recipe added successfully'\n                    );\n                  }\n                },\n                error: (err) => {\n                  console.log(err);\n                },\n              });\n          }\n        } else {\n          this.loadSpinnerForApi = false;\n          this.cd.detectChanges();\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: updatedSRMData['menuItemCode'],\n          subRecipeName: updatedSRMData['menuItemName'],\n        });\n      }\n\n    }\n  }\n\n  selectIngredientsName(itemName) {\n    const item = this.invItems.find((item) => {\n      return item.itemName === itemName;\n    });\n    let uom;\n    if (item['uom'] == 'KG' || item['uom'] == 'kg' || item['uom'] == 'Kg') {\n      uom = 'GM';\n    } else if (\n      item['uom'] == 'LITRE' ||\n      item['uom'] == 'litre' ||\n      item['uom'] == 'Litre'\n    ) {\n      uom = 'ML';\n    } else if (\n      item['uom'] == 'NOS' ||\n      item['uom'] == 'nos' ||\n      item['uom'] == 'Nos'\n    ) {\n      uom = 'NOS';\n    } else {\n      uom = 'MM';\n    }\n    this.AccessibleUOM = item['ItemType'] === 'SubRecipe' && item['portionWeight'] != 0 ? [uom,\"PORTION\"] : [uom]\n    let conversionCoefficient;\n    conversionCoefficient = item['uom'] == 'NOS' ? 1 : 1000;\n    let rate = item\n      ? ((item.hasOwnProperty('packageQty') ? item['packageQty'] : 1) /\n          conversionCoefficient) *\n        item['withTaxPrice']\n      : 0;\n    this.subRecipeRecipeForm.patchValue({\n      weightInUse: 0,\n      ingredientCode: item['itemCode'],\n      uom: uom,\n      defaultUOM: uom,\n      yield: item['yield'] ?? 1,\n      rate: this.notify.truncateAndFloor(rate),\n      finalRate: 0,\n    });\n  }\n\n  isOptionAccessible(option: string): boolean {\n    return this.AccessibleUOM.includes(option);\n  }\n\n  uomChange(){\n    this.subRecipeRecipeForm.get('portionCount').setValue(1) ;\n    this.getPortionWeightForSubRecipe()\n  }\n\n  getPortionWeightForSubRecipe(){\n    let requiredItem = this.invItems.find(\n      (item) => item.itemCode == this.subRecipeRecipeForm.value.ingredientCode\n    );\n    let portionWeight = this.notify.truncateAndFloor(requiredItem['portionWeight']) * this.subRecipeRecipeForm.value.portionCount\n    this.subRecipeRecipeForm.get('weightInUse').setValue(portionWeight);\n    this.sumForFinalRateSRR('Total');\n  }\n\n  convertPortionToUOM(){\n    this.getPortionWeightForSubRecipe() ;\n    this.cd.detectChanges() ;\n  }\n\n  update() {\n    this.deleteDataFormDB();\n    this.loadSpinnerForApi = true;\n    this.showWorkAreaError = false;\n    this.baseData = this.sharedData.getBaseData().value;\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      // this.checkWorkArea();\n      if(this.showWorkAreaError){\n        this.registrationForm.markAllAsTouched();\n        this.notify.snackBarShowError('Please fill out all required fields')\n        this.loadSpinnerForApi = false;\n        this.cd.detectChanges();\n      }else{\n        this.setDiscontinuedDataInRolopos();\n        let updatedSRMData = this.convertSRMKeys();\n        updatedSRMData['modified'] = 'yes';\n        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');\n        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');\n        updatedSRMData['usedInWorkArea'] =\n        updatedSRMData['usedInWorkArea'].join(',');\n        updatedSRMData['preparedAtDiscontinued'] = this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData.join(',') : '';\n        updatedSRMData['usedAtOutletDiscontinued'] = this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData.join(',') : '';\n        if(this.discontinuedIssuedToData.length > 0){\n          const workAreas = this.discontinuedIssuedToData.flatMap(item => item.workAreas);\n          // updated['issuedToDiscontinued'] = workAreas.length > 0 ? workAreas.join(',') : '';\n          updatedSRMData['issuedToDiscontinued'] = workAreas > 0 ? workAreas.join(',') : '';\n        }\n        if (Object.keys(this.baseData).length > 0) {\n          let tempObj = {};\n          tempObj['Subrecipe Master'] = this.baseData['Subrecipe Master'];\n          let requiredVendor = tempObj['Subrecipe Master'].find(\n            (el) => el.menuItemCode == updatedSRMData['menuItemCode']\n          );\n          let index = tempObj['Subrecipe Master'].indexOf(requiredVendor);\n          this.dataSource.data.forEach((el) => {\n            el.UOM = el.defaultUOM ;\n            el['Initialweight'] = el['weightInUse'] / el['yield'];\n          });\n          tempObj['Subrecipe Master'][index] = updatedSRMData;\n          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(\n            (item) => item.modified === 'yes'\n          );\n          this.dataSource.data = [...this.dataSource.data, ...this.removedItem, ...this.discData];\n          tempObj['Subrecipe Recipe'] = this.dataSource.data;\n          this.dataSource.data = this.dataSource.data.filter(item => item.delete !== true);\n          this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes');\n          this.baseData['Subrecipe Recipe'].filter((el) => el['ingredientCode'] === updatedSRMData['menuItemCode'] && (!['', null,undefined].includes(el.portionCount))).forEach((item) => {\n            let portionWeight = (this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion))\n            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight) ;\n            item['Initialweight'] = this.notify.truncateAndFloor(item['weightInUse'] /this.notify.truncateAndFloor( item['yield']));\n            item['modified'] = 'yes';\n            this.updateSubRecipeMaster(item['subRecipeCode']) ;\n          })\n\n          this.baseData['menu recipes'].filter((el) => el['ingredientCode'] === updatedSRMData['menuItemCode'] && (!['', null,undefined].includes(el.portionCount))).forEach((item) => {\n            let portionWeight = (this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion))\n            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight) ;\n            item['InitialWeight'] = this.notify.truncateAndFloor(item['weightInUse'] /this.notify.truncateAndFloor( item['Yield']));\n            item['modified'] = 'yes';\n            this.updateMenuMaster(item['menuItemCode']) ;\n          })\n          let updatedMenuMaster,updatedMenuRecipe,updatedSubRecipeMaster,updatedSubRecipeRecipe\n          updatedMenuRecipe = this.baseData['menu recipes'].filter((el) => el['modified'] === 'yes' )\n          updatedMenuMaster = this.baseData['menu master'].filter((el) => el['modified'] === 'yes' )\n          updatedSubRecipeMaster = this.baseData['Subrecipe Master'].filter((el) => el['modified'] === 'yes' && el['menuItemCode'] != updatedSRMData['menuItemCode'])\n          updatedSubRecipeRecipe = this.baseData['Subrecipe Recipe'].filter((el) => el['modified'] === 'yes' )\n          tempObj['menu recipes'] = updatedMenuRecipe;\n          tempObj['menu master'] = updatedMenuMaster;\n          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].concat(updatedSubRecipeMaster);\n          tempObj['Subrecipe Recipe'] = tempObj['Subrecipe Recipe'].concat(updatedSubRecipeRecipe);\n          let obj = {};\n          obj['tenantId'] = this.user.tenantId;\n          obj['userEmail'] = this.user.email;\n          obj['data'] = tempObj;\n          obj['type'] = 'recipe';\n          obj['category'] = 'subRecipe';\n          this.api\n            .updateData(obj)\n            .pipe(first())\n            .subscribe({\n              next: (res) => {\n                if (res['success']) {\n                  this.loadSpinnerForApi = false;\n                  this.cd.detectChanges();\n                  this.updateBaseData(tempObj) ;\n                  this.updateBaseDataForSRR() ;\n                  this.notify.snackBarShowSuccess(\n                    'Sub-recipe updated successfully'\n                  );\n                  this.close();\n                }\n              },\n              error: (err) => {\n                console.log(err);\n              },\n            });\n        } else {\n          this.loadSpinnerForApi = false;\n        }\n      }\n\n    }\n  }\n\n  updateBaseDataForSRR() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['userEmail'] = this.user.email;\n    obj['type'] = 'recipe';\n    obj['specific'] = 'Subrecipe Recipe';\n    if (this.registrationForm.value) {\n      obj['itemCode'] = this.registrationForm.value.menuItemCode;\n    }\n    this.api.getPresentData(obj).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          let latestSRR = res['data'][0] ?? res['data'];\n          let currentSRR = this.baseData['Subrecipe Recipe']\n          latestSRR['Subrecipe Recipe'].forEach(item => {\n            const exist = currentSRR.findIndex(el => el.ingredientCode == item['ingredientCode'] && el['subRecipeCode'] === item['subRecipeCode']\n            );\n            if (exist !== -1) {\n              currentSRR[exist] = item;\n            } else {\n              currentSRR.push(item);\n            }\n          })\n          this.baseData['Subrecipe Recipe'] = currentSRR;\n          this.sharedData.setBaseData(this.baseData) ;\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  updateMenuMaster(itemCode){\n    const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === itemCode);\n    let revisedTotalWeight,requiredItems\n    requiredItems = this.baseData['menu recipes'].filter((item) => item['menuItemCode'] === itemCode) ;\n    revisedTotalWeight = requiredItems.reduce((sum, item) => {\n      return sum + this.notify.truncateAndFloor(item.weightInUse);\n    }, 0);\n    let requiredData = this.baseData['menu master'].find((el) => el['menuItemCode'] == itemCode)\n    if(requiredData){\n      requiredData['weight'] = revisedTotalWeight ;\n      requiredData['modified'] = 'yes'\n      if (index !== -1) {\n        this.baseData['menu master'][index] = requiredData\n      }\n    }\n  }\n\n  updateSubRecipeMaster(itemCode){\n    const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === itemCode);\n    let revisedTotalWeight,requiredItems\n    requiredItems = this.baseData['Subrecipe Recipe'].filter((item) => item['subRecipeCode'] === itemCode) ;\n    revisedTotalWeight = requiredItems.reduce((sum, item) => {\n      return sum + this.notify.truncateAndFloor(item.weightInUse);\n    }, 0);\n    let requiredData = this.baseData['Subrecipe Master'].find((el) => el['menuItemCode'] == itemCode)\n    if(requiredData){\n      requiredData['weightInUse'] = revisedTotalWeight ;\n      requiredData['recovery'] = revisedTotalWeight * requiredData['yield'];\n      requiredData['modified'] = 'yes'\n      if (index !== -1) {\n        this.baseData['Subrecipe Master'][index] = requiredData\n      }\n    }\n  }\n\n\n  // ######################## AUTO COMPLETION ########################\n  _filter(value: string, input: string[] , data): string[] {\n    let filterValue = value.toLowerCase();\n    this.filtered = input.filter((option) =>\n      option.toLowerCase().includes(filterValue)\n    );\n    if (this.filtered.length == 0) {\n      if(data == 'ingredients'){\n        // this.disableOption = true;\n        this.filtered = ['No Item Found'];\n      }else{\n        this.filtered = [this.question + value + '\"'];\n      }\n    }\n    return this.filtered;\n  }\n\n  checkItem(event) {\n    let invItem = this.sharedData.getDataForFillTheForm(\n      event.target.value,\n      'Subrecipe Master'\n    );\n    if (invItem) {\n      this.updateBtnActive = true;\n    } else {\n      this.updateBtnActive = false;\n    }\n  }\n\n  optionSelected(type: string, option: any) {\n    let invItem = this.sharedData.getDataForFillTheForm(\n      option.value,\n      'Subrecipe Master'\n    );\n    if (invItem) {\n      this.updateBtnActive = true;\n    } else {\n      this.updateBtnActive = false;\n    }\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOption(type);\n    }\n  }\n\n  addOption(type: string) {\n    this.loadBtn = true;\n    if (type == 'package') {\n      this.itemNameControl.reset();\n    } else if (type == 'Subrecipe Master') {\n      let subRecipeMasterItem = this.sharedData.getDataForFillTheForm(\n        this.itemNameControl.value,\n        'Subrecipe Master'\n      );\n      if (subRecipeMasterItem) {\n        this.isUpdateActive = true;\n        this.setValuesForForm(subRecipeMasterItem);\n      } else {\n        this.generateCode('subrecipeMasterCode');\n      }\n      this.registrationForm.controls['menuItemName'].patchValue(\n        this.removePromptFromOption(this.itemNameControl.value)\n      );\n      this.itemNameControl.reset();\n      this.isDuplicate = false;\n    }\n    this.dataSource.data = [];\n    this.loadBtn = false;\n    this.getBaseData()\n  }\n\n  generateCode(code) {\n    let obj = {};\n    let data;\n    obj['tenantId'] = this.user.tenantId;\n    obj['code'] = code;\n    this.api\n      .getCode(obj)\n      .pipe(first())\n      .subscribe({\n        next: async (res) => {\n          if (res['success']) {\n            data = res['data'];\n            this.registrationForm.get('menuItemCode').setValue(data);\n          }\n        },\n      });\n  }\n\n  setValuesForForm(subRecipeMasterItem) {\n    if (\n      !Array.isArray(subRecipeMasterItem['usedInWorkArea']) &&\n      !Array.isArray(subRecipeMasterItem['usedAtOutlet']) &&\n      !Array.isArray(subRecipeMasterItem['preparedAt'])\n    ) {\n      subRecipeMasterItem['usedInWorkArea'] =\n        subRecipeMasterItem['usedInWorkArea'].split(',');\n      subRecipeMasterItem['usedAtOutlet'] =\n        subRecipeMasterItem['usedAtOutlet'].split(',');\n      subRecipeMasterItem['preparedAt'] =\n        subRecipeMasterItem['preparedAt'].split(',');\n    }\n    let recovery = this.notify.truncateAndFloor(subRecipeMasterItem['recovery'])\n    let portion = subRecipeMasterItem.hasOwnProperty('portion') && subRecipeMasterItem['portion'] ? subRecipeMasterItem['portion'] : 1 ;\n\n    this.defaultPreLocData = subRecipeMasterItem['preparedAt']\n    this.defaultOutletData = subRecipeMasterItem['usedAtOutlet']\n    this.defaultIssuedToData = subRecipeMasterItem['usedInWorkArea']\n    if((this.discontinuedLocations && this.discontinuedLocations != undefined ) && this.discontinuedLocations.subRecipeLocations){\n      this.discontinuedOutletData.push(...this.discontinuedLocations.subRecipeLocations.outLetDiscontinued)\n      this.discontinuedIssuedToData.push(...this.discontinuedLocations.subRecipeLocations.issuedToDiscontinued)\n      this.discontinuedPreLocData.push(...this.discontinuedLocations.subRecipeLocations.procuredAtDiscontinued)\n    }\n    this.registrationForm.patchValue({\n      category: subRecipeMasterItem['category'],\n      subCategory: subRecipeMasterItem['subCategory'],\n      menuItemCode: subRecipeMasterItem['menuItemCode'],\n      menuItemName: subRecipeMasterItem['menuItemName'],\n      closingUOM: subRecipeMasterItem['closingUOM'],\n      itemType: 'SubRecipe',\n      preparedAt: subRecipeMasterItem['preparedAt'],\n      usedAtOutlet: subRecipeMasterItem['usedAtOutlet'],\n      uom: subRecipeMasterItem['UOM'],\n      weightInUse: subRecipeMasterItem['weightInUse'],\n      portion: portion,\n      yield: subRecipeMasterItem['yield'],\n      recovery: recovery,\n      rate: this.notify.truncateAndFloor(subRecipeMasterItem['rate']),\n      finalRate: this.notify.truncateAndFloor(subRecipeMasterItem['finalRate']),\n      discontinued: ['no','NO' ,'No', 'N', null,''].includes(subRecipeMasterItem['Discontinued']) ? 'no' : 'yes',\n      row_uuid: subRecipeMasterItem['row_uuid'],\n    });\n\n    this.loadSrmBtn = false;\n    this.locationChange(subRecipeMasterItem['usedAtOutlet']);\n    this.registrationForm\n      .get('usedInWorkArea')\n      .patchValue(subRecipeMasterItem['usedInWorkArea']);\n  }\n\n  removePromptFromOption(option) {\n    if (option.startsWith(this.question)) {\n      option = option.substring(this.question.length, option.length - 1);\n    }\n    return option;\n  }\n\n  close() {\n    this.dataSource.data = [];\n    this.masterDataService.setNavigation('Subrecipe Master');\n    this.router.navigate(['/dashboard/home']);\n    this.dialog.closeAll();\n  }\n\n  editFun(element, addSRR) {\n    const item = this.invItems.find(\n      (item) =>\n        item.itemName.toLowerCase() === element.ingredientName.toLowerCase()\n    );\n    this.updateSRR = true;\n    this.subRecipeRecipeForm.patchValue({\n      ingredientCode: element['ingredientCode'],\n      // ingredientName: item ? item.ingredientName : '',\n      ingredientName: element['ingredientName'],\n      subRecipeCode: element['subRecipeCode'],\n      subRecipeName: element['subRecipeName'],\n      uom: element['UOM'],\n      defaultUOM: element['defaultUOM'],\n      yield: element['yield'],\n      rate: this.notify.truncateAndFloor(element['rate']) ,\n      finalRate: this.notify.truncateAndFloor(element['finalRate']),\n      initialWeight: element['Initialweight'],\n      loss: element['loss'],\n      weightInUse: element['weightInUse'],\n      discontinued:['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',\n      row_uuid: element['row_uuid'],\n    });\n\n    if (element.hasOwnProperty('portionCount')) {\n      this.subRecipeRecipeForm.get('portionCount').setValue(element['portionCount']);\n    }\n\n     if (!element['Initialweight']) {\n      this.subRecipeRecipeForm.get('initialWeight').setValue(0);\n    }\n\n    if (!element['loss']) {\n      this.subRecipeRecipeForm.get('loss').setValue(0);\n    }\n\n    let dialogRef = this.dialog.open(addSRR, {\n      maxHeight: '95vh',\n      maxWidth: '50vw',\n    });\n    dialogRef.afterClosed().subscribe((result) => {});\n    this.closeSRRRef = dialogRef;\n    this.loadSrrBtn = false;\n  }\n\n  closeSRRDialog() {\n    this.updateSRR = false;\n    // this.subRecipeRecipeForm.reset();\n    this.closeSRRRef.close();\n  }\n\n  addNewSubRecipeRecipe() {\n    this.subRecipeRecipeForm.patchValue({\n      subRecipeCode: this.registrationForm.value.menuItemCode,\n      subRecipeName: this.registrationForm.value.menuItemName,\n    });\n    if (this.subRecipeRecipeForm.invalid) {\n      this.subRecipeRecipeForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.cd.detectChanges();\n    } else {\n      if (this.subRecipeRecipeForm.value.weightInUse > 0) {\n        let update = this.convertSRRKeys();\n        update['modified'] = 'yes';\n        let existingItem = this.dataSource.data.find(\n          (el) => el.ingredientCode === update['ingredientCode']\n        );\n        if (!existingItem) {\n          Object.entries(update).forEach(([key, value]) => {\n            if (value === null || value === undefined || value === '') {\n                return;\n            }\n            if (typeof value === 'number') {\n              update[key] = this.notify.truncateAndFloor(value);\n            }\n          });\n          this.dataSource.data.unshift(update);\n          this.dataSource.paginator = this.paginator;\n          this.updateSRR = false;\n          this.reflectSubRecipe();\n          // this.notify.snackBarShowSuccess('Created successfully');\n          this.subRecipeRecipeForm.reset();\n          this.subRecipeRecipeForm.patchValue({\n            subRecipeCode: this.registrationForm.value.menuItemCode,\n            subRecipeName: this.registrationForm.value.menuItemName,\n            yield: 1\n          });\n          this.tempData = this.dataSource.data\n          this.clearForm();\n        } else {\n          this.notify.snackBarShowWarning('Ingredient already exists!');\n        }\n      } else {\n        this.subRecipeRecipeForm.markAllAsTouched();\n        this.showWeightError = true;\n        // this.notify.snackBarShowError('Please fill out all required fields');\n        this.cd.detectChanges();\n      }\n      this.cd.detectChanges();\n    }\n    this.clearForm();\n  }\n\n  clearForm() {\n    Object.keys(this.subRecipeRecipeForm.controls).forEach(key => {\n      this.subRecipeRecipeForm.get(key)?.setErrors(null);\n    });\n  }\n\n  editExistingSubRecipeRecipe() {\n    if (this.subRecipeRecipeForm.invalid) {\n      this.subRecipeRecipeForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.cd.detectChanges();\n    } else {\n      let updatedSRRData = this.convertSRRKeys();\n      updatedSRRData['modified'] = 'yes';\n      let requiredPackage = this.dataSource.data.find(\n        (el) =>\n          el.ingredientCode == updatedSRRData['ingredientCode'] &&\n          el.subRecipeCode == updatedSRRData['subRecipeCode']\n      );\n      if (requiredPackage) {\n        let data = this.tempData.filter(item => item.Discontinued !== 'yes' )\n        let tempDiscData : any = []\n        tempDiscData = this.discData.filter(el =>\n          el.ingredientCode === updatedSRRData['ingredientCode'] &&\n          el.subRecipeCode === updatedSRRData['subRecipeCode']\n        );\n        if (tempDiscData.length > 0) {\n          tempDiscData[0].Discontinued = 'no'\n          tempDiscData[0].ingredientName = tempDiscData[0].ingredientName.toUpperCase();\n          this.discData.pop(tempDiscData);\n        }\n        this.dataSource.data = [...data, ...tempDiscData];\n        let index = this.dataSource.data.indexOf(requiredPackage);\n        this.dataSource.data[index] = updatedSRRData;\n        let items = this.dataSource.data\n        // this.dataSource.data = items.filter(item => item.Discontinued !== 'yes')\n        this.dataSource.data = items.filter(item =>\n          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n        );\n        this.tempData = this.dataSource.data;\n        let disData = items.filter(item =>\n          ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n        );\n        // let disData = items.filter(item => item.Discontinued === 'yes')\n        this.discData.push(...disData)\n        this.dataSource.paginator = this.paginator;\n        this.reflectSubRecipe();\n        this.notify.snackBarShowSuccess('Updated successfully');\n        this.subRecipeRecipeForm.reset();\n        this.clearForm();\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: this.registrationForm.value.menuItemCode,\n          subRecipeName: this.registrationForm.value.menuItemName,\n          yield: 1\n        });\n        this.closeSRRDialog();\n        this.showDeleteItems = false\n        this.cd.detectChanges();\n      }\n    }\n  }\n\n  goBack() {\n    this.isDuplicate = false;\n    this.showSRR = false;\n    this.subRecipeRecipeForm.reset();\n    setTimeout(() => {\n      const dataSourceTable = this.el.nativeElement.querySelector('.section');\n      if (dataSourceTable) {\n        dataSourceTable.scrollIntoView({ behavior: 'smooth', block: 'start' });\n      }\n    }, 100);\n  }\n\n  convertSRMKeys() {\n    const keyData = [\n      ['category', 'category'],\n      ['subCategory', 'subCategory'],\n      ['menuItemCode', 'menuItemCode'],\n      ['menuItemName', 'menuItemName'],\n      ['closingUOM', 'closingUOM'],\n      ['itemType', 'itemType'],\n      ['preparedAt', 'preparedAt'],\n      ['usedAtOutlet', 'usedAtOutlet'],\n      ['usedInWorkArea', 'usedInWorkArea'],\n      ['UOM', 'uom'],\n      ['weightInUse', 'weightInUse'],\n      ['yield', 'yield'],\n      ['recovery', 'recovery'],\n      ['rate', 'rate'],\n      ['finalRate', 'finalRate'],\n      ['Discontinued', 'discontinued'],\n      ['closingConversion', 'discontinued'],\n      ['modified', 'modified'],\n      ['portion', 'portion'],\n      ['row_uuid', 'row_uuid'],\n    ];\n\n    const updatedRecipeData = {};\n    keyData.forEach((key) => {\n      let value = this.registrationForm.value[key[1]];\n      if (key[0] == 'taxRate') {\n        updatedRecipeData[key[0]] = value || 0;\n      } else if (key[0] == 'portion'){\n        if (this.registrationForm.value.unit === 'GM/ML') {\n          value = this.registrationForm.value.recovery/value\n        }\n        updatedRecipeData[key[0]] =  this.notify.truncateAndFloor(value) || 1;\n      } else {\n        updatedRecipeData[key[0]] = value || '';\n      }\n    });\n    return updatedRecipeData;\n  }\n\n  convertSRRKeys() {\n    const keyData = [\n      ['subRecipeCode', 'subRecipeCode'],\n      ['subRecipeName', 'subRecipeName'],\n      ['ingredientCode', 'ingredientCode'],\n      ['ingredientName', 'ingredientName'],\n      ['UOM', 'uom'],\n      ['Initialweight', 'initialWeight'],\n      ['defaultUOM', 'defaultUOM'],\n      ['yield', 'yield'],\n      ['weightInUse', 'weightInUse'],\n      ['rate', 'rate'],\n      ['finalRate', 'finalRate'],\n      ['Discontinued', 'discontinued'],\n      ['row_uuid', 'row_uuid'],\n      ['modified', 'modified'],\n      ['portionCount', 'portionCount']\n    ];\n    const updatedSubRecipeData = {};\n    keyData.forEach((key) => {\n      let value = this.subRecipeRecipeForm.value[key[1]];\n      if (key[0] === 'Initialweight') {\n        let formattedRec = this.notify.truncateAndFloor(this.subRecipeRecipeForm.value['weightInUse'] / this.subRecipeRecipeForm.value['yield'] )\n        updatedSubRecipeData[key[0]] = formattedRec;\n      } else {\n        updatedSubRecipeData[key[0]] = value || '';\n      }\n    });\n    return updatedSubRecipeData;\n  }\n\n  applyFilter(filterValue: any) {\n    filterValue = filterValue.target.value;\n    filterValue = filterValue.trim();\n    filterValue = filterValue.toLowerCase();\n    this.dataSource.filter = filterValue;\n  }\n\n  toggleSelectAll() {\n    const control = this.registrationForm.controls['usedInWorkArea'];\n    let data = [\n      ...this.usedInWorkAreaBank.map((location) => location.workAreas),\n    ];\n    const flattenedArray = [].concat(...data);\n    if (control.value.length - 1 === flattenedArray.length) {\n      control.setValue([]);\n    } else {\n      control.setValue(flattenedArray);\n    }\n  }\n\n  usedOutLetToggleSelectAll() {\n    const control = this.registrationForm.controls['usedAtOutlet'];\n    if (control.value.length - 1 === this.outletBank.length) {\n      control.setValue(this.defaultOutletData);\n    } else {\n      control.setValue(this.outletBank);\n    }\n    this.locationChange(this.registrationForm.value.usedAtOutlet);\n  }\n\n  preparedToggleSelectAll() {\n    const control = this.registrationForm.controls['preparedAt'];\n    if (control.value.length - 1 === this.outletBank.length) {\n      control.setValue(this.defaultPreLocData);\n    } else {\n      control.setValue(this.outletBank);\n    }\n  }\n\n  filterDialog(filterValue) {\n    filterValue = filterValue.target.value;\n    filterValue = filterValue.trim().toLowerCase();\n    this.filteredData = this.dropDownData.filter((item) =>\n      item.toLowerCase().includes(filterValue)\n    );\n  }\n\n  setZeroSRM(val, formData) {\n    if (this.registrationForm.value.yield < 0) {\n      this.registrationForm.get(formData).setValue(1);\n      this.notify.snackBarShowInfo('Please enter a value greater than 0');\n    } else if (this.registrationForm.value.yield > 0) {\n      let formattedRec = this.notify.truncateAndFloor(this.registrationForm.value.yield * this.registrationForm.value.weightInUse)\n      this.registrationForm.get('recovery').setValue(formattedRec);\n    }\n  }\n\n  setZeroSRR(val, formData) {\n    if (this.subRecipeRecipeForm.value.yield < 0) {\n      this.subRecipeRecipeForm.get(formData).setValue(1);\n      this.notify.snackBarShowInfo('Please enter a value greater than 0');\n    } else if (this.subRecipeRecipeForm.value.yield > 0) {\n      let initialWeight =  this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield\n      this.subRecipeRecipeForm.get('initialWeight').setValue(this.notify.truncateAndFloor((initialWeight)));\n      let sum = (\n        this.subRecipeRecipeForm.value.initialWeight *\n        this.subRecipeRecipeForm.value.rate\n      );\n      this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));\n    }\n  }\n\n  sumForFinalRateSRR(val) {\n    this.showWeightError = false;\n      this.subRecipeRecipeForm.get('initialWeight').setValue(this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield);\n      let sum = (\n        this.subRecipeRecipeForm.value.initialWeight *\n        this.subRecipeRecipeForm.value.rate\n      );\n      this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));\n  }\n\n  closeInfoDialog() {\n    if(this.costDialogkey == true){\n      this.close()\n      if (this.dialogRef) {\n        this.dialogRef.close();\n      }\n    }else{\n      if (this.dialogRef) {\n        this.dialogRef.close();\n      }\n    }\n  }\n\n  getCategories() {\n    // this.api.getCategories({ tenantId: this.user.tenantId, type: 'subRecipe' }).pipe(first()).subscribe({\n    //   next: (res) => {\n    //     if (res['success']) {\n          this.catAndsubCat = this.sharedData.getCategories().value;\n          let categoryData = Object.keys(this.sharedData.getCategories().value).map(category => category.toUpperCase());\n          let newCat = [...this.newCategory, ...categoryData];\n          this.categories = [...new Set(newCat)];\n          this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories, '')));\n        // }\n    //   },\n    //   error: (err) => { console.log(err); }\n    // });\n  }\n\n  getSubCategories(val) {\n    this.registrationForm.get('subCategory').setValue('');\n    let data = this.baseData['Subrecipe Recipe'].filter(item => item.category === val);\n    this.newSubCategory = data.map(subCat => subCat.subCategory)\n    if (!(val in this.catAndsubCat)) {\n      this.catAndsubCat[val] = []\n    }\n    let newSubCat = [...this.newSubCategory, ...this.catAndsubCat[val]]\n    this.subCategories = [...new Set(newSubCat)];\n    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories , '')));\n  }\n\n  optionSelectedCat(option: any) {\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOptionCat();\n    } else {\n      this.getSubCategories(this.registrationForm.value.category);\n    }\n    this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories , '')));\n  }\n\n  addOptionCat() {\n    this.registrationForm.controls['category'].patchValue(this.removePromptFromOption(this.registrationForm.value.category));\n    this.getSubCategories(this.registrationForm.value.category);\n  }\n\n  optionSelectedSubCat(option: any) {\n    if (option.value.indexOf(this.question) === 0) {\n      this.addOptionSubCat();\n    }\n    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories, '')));\n  }\n\n  addOptionSubCat() {\n    this.registrationForm.controls['subCategory'].patchValue(this.removePromptFromOption(this.registrationForm.value.subCategory));\n  }\n\n  focusOutFunction(formKey) {\n    if (this.registrationForm.get(formKey)) {\n      if (this.registrationForm.get(formKey).value === null) {\n        this.registrationForm.get(formKey).setValue(0);\n      }\n    }\n  }\n\n  focusOutFunctionSRR(formKey) {\n    if (this.subRecipeRecipeForm.get(formKey).value === null) {\n      this.subRecipeRecipeForm.get(formKey).setValue(0);\n    }\n  }\n\n  focusFunction(formKey) {\n    if (this.notify.truncateAndFloor(this.registrationForm.get(formKey).value) === 0) {\n      this.registrationForm.get(formKey).setValue(null);\n    }\n  }\n\n  focusFunctionSRR(formKey) {\n    if (this.notify.truncateAndFloor(this.subRecipeRecipeForm.get(formKey).value) === 0) {\n      this.subRecipeRecipeForm.get(formKey).setValue(null);\n    }\n  }\n\n  public scrollRight(): void {\n    this.widgetsContent.nativeElement.scrollTo({\n      left: this.widgetsContent.nativeElement.scrollLeft + 150,\n      behavior: 'smooth',\n    });\n  }\n\n  public scrollLeft(): void {\n    this.widgetsContent.nativeElement.scrollTo({\n      left: this.widgetsContent.nativeElement.scrollLeft - 150,\n      behavior: 'smooth',\n    });\n  }\n\n  getBaseData() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['userEmail'] = this.user.email;\n    obj['type'] = 'recipe';\n    obj['specific'] = 'Subrecipe Recipe';\n    if (this.registrationForm.value) {\n      obj['itemCode'] = this.registrationForm.value.menuItemCode;\n    }\n    this.api.getPresentData(obj).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.baseData = res['data'][0] ?? res['data'];\n          this.displayedColumns = [\n            'sNo',\n            // 'discontinued',\n            'ingredientName',\n            // 'ingredientCode',\n            // 'subRecipeName',\n            // 'subRecipeCode',\n            'uom',\n            'weightInUse',\n            'yield',\n            'initialWeight',\n            'rate',\n            'finalRate',\n          ];\n          this.isSRRDataReady = true;\n          this.baseData['Subrecipe Recipe'].sort((a, b) => {\n            if (a.modified === 'yes' && b.modified !== 'yes') {\n              return -1;\n            }\n            if (b.modified === 'yes' && a.modified !== 'yes') {\n              return 1;\n            }\n            return 0;\n          });\n          this.dataSource.data = this.baseData['Subrecipe Recipe']\n            ? this.baseData['Subrecipe Recipe']\n            : [];\n          this.dataSource.data.forEach((el) => {\n            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight']);\n            let portion = 1\n            let requiredItem = this.invItems.find(\n              (item) => item.itemCode == el['ingredientCode']\n            );\n            let conversionCoefficient;\n            conversionCoefficient = requiredItem\n              ? requiredItem['uom'] == 'NOS'\n                ? 1\n                : 1000\n              : 0;\n            let rate = requiredItem\n            ? ((requiredItem.hasOwnProperty('packageQty') ? requiredItem['packageQty'] : 1) / conversionCoefficient )* requiredItem['withTaxPrice']\n            : 0;\n\n            el['defaultUOM'] = el['UOM'] ;\n            if (requiredItem && el.hasOwnProperty('portionCount') && (!['', null,undefined].includes(el.portionCount)) ){\n              el['UOM'] = 'PORTION'\n              el['portionCount'] = this.notify.truncateAndFloor(el['portionCount'])\n            }\n            el['rate'] = rate ;\n            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight'] * portion);\n            el['weightInUse'] = this.notify.truncateAndFloor(el['weightInUse'] * portion) ;\n            // el['finalRate'] = this.notify.truncateAndFloor(rate * el['Initialweight']);\n            el['finalRate'] = rate * el['Initialweight'];\n          });\n          this.tempData = this.dataSource.data\n          // this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes')\n          this.dataSource.data = this.dataSource.data.filter(item =>\n            !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n          );\n          // this.discData = this.tempData.filter(item => item.Discontinued === 'yes')\n          this.discData = this.tempData.filter(item =>\n            ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n          );\n          this.cd.detectChanges();\n          this.dataSource.paginator = this.paginator;\n          this.dataSource.sort = this.sort;\n          this.reflectSubRecipe();\n          this.cd.detectChanges();\n        } else {\n          this.baseData = [];\n        }\n\n        if(this.costDialogkey == true){\n          this.openPortionData();\n        }\n\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  nextTab() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n      this.loadSpinnerForApi = false;\n      this.cd.detectChanges();\n    } else {\n      this.stepper.next();\n    }\n  }\n\n  getTotal(key: string) {\n    let total = this.dataSource.data.reduce((total, item) => {\n      const value = this.notify.truncateAndFloor(item[key]) || 0;\n      return total + value;\n    }, 0);\n    return this.notify.truncateAndFloor((total * this.ratio));\n  }\n\n  getSRRTotal(key: string) {\n    let total = this.dataSource.data.reduce((total, item) => {\n      const value = (item[key]) || 0;\n      return total + value;\n    }, 0);\n    return ((total * this.ratio));\n  }\n\n  getLocationCall() {\n    this.locationList = this.sharedData.getLocation().value;\n    this.preparedBank = this.locationList.map(\n      (branch) => branch.abbreviatedRestaurantId\n    );\n    if (this.preparedBank.length === 1) {\n      this.registrationForm.get('preparedAt').setValue(this.preparedBank);\n    }\n    this.preparedLocationNames.next(this.preparedBank.slice());\n    this.preparedFilterCtrl.valueChanges\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(() => {\n        this.Filter(\n          this.preparedBank,\n          this.preparedFilterCtrl,\n          this.preparedLocationNames\n        );\n      });\n\n    this.outletBank = this.locationList.map(\n      (branch) => branch.abbreviatedRestaurantId\n    );\n    if (this.outletBank.length === 1) {\n      this.registrationForm.get('usedAtOutlet').setValue(this.outletBank);\n      this.locationChange(this.registrationForm.value.usedAtOutlet)\n    }\n    this.outletLocationNames.next(this.outletBank.slice());\n    this.outletFilterCtrl.valueChanges\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(() => {\n        this.Filter(\n          this.outletBank,\n          this.outletFilterCtrl,\n          this.outletLocationNames\n        );\n      });\n\n    if (this.dialogData.key == false || this.dialogData.costDialogkey == true ) {\n      this.isUpdateActive = true;\n      this.setValuesForForm(this.dialogData.elements);\n    } else if (this.dialogData.key == null && this.costDialogkey == false) {\n      this.dropDownData = this.dialogData.dropDownData;\n      this.filteredData = [...this.dropDownData];\n    }\n    this.getBaseData();\n  }\n\n  readIPConfig() {\n    this.api.readIPConfig(this.user.tenantId).subscribe({\n      next: (res) => {\n        if (res?.['success']) {\n          const data = res['data'];\n          this.logo = data.tenantDetails.logo\n        }\n      },\n      error: (err) => { console.log(err) }\n    });\n  }\n\n  printOption() {\n    const discontinuedValue = this.registrationForm.get('discontinued').value;\n    this.status = discontinuedValue === 'yes' ? 'active' : 'Discontinued';\n    const tableData = this.dataSource.data.map((element: any) => [\n      element.ingredientName,\n      element.ingredientCode,\n      element.UOM,\n      element.Initialweight,\n      element.yield,\n      element.weightInUse,\n      this.notify.truncateAndFloor(element.rate),\n      this.notify.truncateAndFloor(element.finalRate),\n    ]);\n\n    let obj = {\n      factory_name: this.user.name,\n      recipe_details: {\n        'Sub Recipe Name': this.registrationForm.get('menuItemName').value,\n        'Sub Recipe Code': this.registrationForm.get('menuItemCode').value,\n        'Category': this.registrationForm.get('category').value,\n        'Sub Category': this.registrationForm.get('subCategory').value,\n        'Status': this.status,\n        'UOM': this.registrationForm.get('uom').value,\n        'Closing UOM': this.registrationForm.get('closingUOM').value,\n        'Prepared At': this.registrationForm.get('preparedAt').value.join(', '),\n        'Sales Outlet': this.registrationForm.get('usedAtOutlet').value.join(', '),\n        'Issued To': this.registrationForm.get('usedInWorkArea').value.join(', ')\n      },\n      logo: this.logo,\n      table_headers : ['Ingredient Name','Ingredient Code','UOM','Initial Weight','Yield','Weight In Use','Unit Cost','Final Rate'],\n      table_data: tableData,\n      summary: {\n        'Weight In Use': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,\n        'Yield': this.registrationForm.get('yield').value,\n        'Recovery': `${this.registrationForm.get('recovery').value} (${this.registrationForm.get('uom').value})`,\n        'Unit Cost': `${this.registrationForm.get('rate').value} (Rs)`,\n        'Gross Weight': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,\n        'Preparation Cost': `${this.registrationForm.get('rate').value} (Rs)`,\n        'Weight / Portion': `${this.getWeightPerPortion()} (${this.registrationForm.get('uom').value})`,\n        'Cost / Portion': `${this.notify.truncateAndFloor(this.getCostPerPortion())} (Rs)`,\n      },\n    };\n    this.api.printInvoice(obj).subscribe({\n      next: (data) => {\n        this.api.globalPrintPdf(data.pdf_base64);\n      },\n      error: (err) => {\n        console.error(err);\n      }\n    });\n  }\n\n  locationChange(event) {\n    if (event) {\n      const selectedWorkAreasArray = this.locationList.filter((branch) =>\n        event.includes(branch.abbreviatedRestaurantId)\n      );\n      this.usedInWorkAreaBank = selectedWorkAreasArray;\n      if (this.usedInWorkAreaBank.length === 1) {\n        this.registrationForm\n          .get('usedInWorkArea')\n          .setValue(this.usedInWorkAreaBank);\n      }\n      if(this.discontinuedOutletData.length > 0){\n        this.discontinuedOutletData.forEach(val => {\n          this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {\n            if (item.branchName === val) {\n              item.disabled = true;\n            }\n            return item;\n          });\n        })\n      }\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(() => {\n          this.FilterIssued(\n            this.usedInWorkAreaBank,\n            this.usedInWorkAreaFilterCtrl,\n            this.usedInWorkAreaNames\n          );\n        });\n    } else {\n      this.usedInWorkAreaBank = [];\n    }\n  }\n\n  openPortionData() {\n    this.dialogRef = this.dialog.open(this.openPortionDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog',\n      autoFocus: false,\n      disableClose: true,\n    });\n  }\n\n  openUnitDialog() {\n    this.dialogRef = this.dialog.open(this.openUnitCostDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog',\n    });\n  }\n\n  deleteSRR(element) {\n    const item = this.invItems.find(\n      (item) =>\n        item.itemName.toLowerCase() === element.ingredientName.toLowerCase()\n    );\n    this.updateSRR = true;\n    this.subRecipeRecipeForm.patchValue({\n      ingredientCode: element['ingredientCode'],\n      ingredientName: item ? item.itemName : '',\n      subRecipeCode: element['subRecipeCode'],\n      subRecipeName: element['subRecipeName'],\n      uom: element['UOM'],\n      yield: element['yield'],\n      rate: this.notify.truncateAndFloor(element['rate']) ,\n      finalRate: this.notify.truncateAndFloor(element['finalRate']),\n      initialWeight: element['Initialweight'],\n      loss: element['loss'],\n      weightInUse: element['weightInUse'],\n      discontinued: ['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',\n      row_uuid: element['row_uuid'],\n    });\n\n    if (!element['Initialweight']) {\n      this.subRecipeRecipeForm.get('initialWeight').setValue(0);\n    }\n\n    if (!element['loss']) {\n      this.subRecipeRecipeForm.get('loss').setValue(0);\n    }\n    this.dialogRef = this.dialog.open(this.openDeleteDialog, {\n      minWidth: '25vw',\n      panelClass: 'mediumCustomDialog',\n    });\n  }\n\n  deleteData(){\n    const indexToRemove = this.dataSource.data.findIndex(\n        (recipe) => recipe.ingredientCode === this.subRecipeRecipeForm.value.ingredientCode\n      );\n      if (indexToRemove !== -1) {\n        let item = this.dataSource.data[indexToRemove];  // Store the removed item\n        this.removedItem.push(item)\n        this.dataSource.data = this.dataSource.data\n          .slice(0, indexToRemove)\n          .concat(this.dataSource.data.slice(indexToRemove + 1));\n          // this.deleteDataFormDB()\n      }\n    this.tempData = this.dataSource.data\n    this.subRecipeRecipeForm.reset();\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: this.registrationForm.value.menuItemCode,\n          subRecipeName: this.registrationForm.value.menuItemName,\n    });\n    this.clearForm();\n    this.reflectSubRecipe();\n    this.closeInfoDialog();\n  }\n\n  deleteDataFormDB(){\n    let obj = {};\n    if(this.removedItem.length > 0){\n      this.removedItem.forEach(item => {\n        item['delete'] = true;\n        // item['Discontinued'] = 'yes';\n        item['InvUom'] = item['Inv. UOM'];\n        delete item['Inv. UOM']\n      });\n      obj['tenantId'] = this.user.tenantId;\n      obj['userEmail'] = this.user.email;\n      obj['data'] = this.removedItem;\n      obj['type'] = 'recipe';\n      obj['category'] = 'subRecipe';\n      this.api\n        .removeData(obj)\n        .pipe(first())\n        .subscribe({\n          next: (res) => {\n            if (res['success']) {\n              this.cd.detectChanges();\n              this.notify.snackBarShowSuccess(\n                'Data Deleted successfully'\n              );\n            }\n          },\n          error: (err) => {\n            console.log(err);\n          },\n        });\n    }\n  }\n\n  discontinueData(){\n      let updatedSRRData = this.convertSRRKeys();\n      updatedSRRData['modified'] = 'yes';\n      updatedSRRData['Discontinued'] = 'yes';\n      let requiredPackage = this.dataSource.data.find(\n        (el) =>\n          el.ingredientCode == updatedSRRData['ingredientCode'] &&\n          el.subRecipeCode == updatedSRRData['subRecipeCode']\n          // el.ingredientName.toLowerCase() == updatedSRRData['ingredientName'].toLowerCase()\n      );\n      if (requiredPackage) {\n        requiredPackage['modified'] = 'yes';\n        requiredPackage['Discontinued'] = 'yes';\n        let index = this.dataSource.data.indexOf(requiredPackage);\n        this.discData.push(updatedSRRData)\n        this.dataSource.data[index] = updatedSRRData;\n        // let data = this.dataSource.data.filter(item => item.Discontinued !== 'yes' )\n        let data = this.dataSource.data.filter(item =>\n          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n        );\n        this.dataSource.data = data\n        // this.dataSource.paginator = this.paginator;\n        this.cd.detectChanges();\n        this.reflectSubRecipe();\n        this.notify.snackBarShowSuccess('Updated successfully');\n        this.subRecipeRecipeForm.reset();\n        this.subRecipeRecipeForm.patchValue({\n          subRecipeCode: this.registrationForm.value.menuItemCode,\n          subRecipeName: this.registrationForm.value.menuItemName,\n        });\n        this.closeInfoDialog();\n      }\n  }\n\n  showItems(){\n    if(this.showDeleteItems == true){\n      this.dataSource.data = this.discData\n    }else if(this.showDeleteItems == false){\n      // this.dataSource.data = this.tempData.filter(item => item.Discontinued !== 'yes' )\n      this.dataSource.data = this.tempData.filter(item =>\n        !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())\n      );\n    }\n  }\n\n  openSelect(select: MatSelect) {\n    select.open();\n  }\n\n  onUnitChange(el) {\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      let reqWeight = this.registrationForm.value.recovery / this.registrationForm.value.portion\n      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqWeight));\n    } else {\n      let reqPortion = this.registrationForm.value.recovery / this.registrationForm.value.portion\n      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqPortion));\n    }\n  }\n\n  getWeightPerPortion() {\n    let portion = this.registrationForm.value.portion\n    if (this.registrationForm.value.unit === 'GM/ML') {\n       portion = this.registrationForm.value.recovery / this.registrationForm.value.portion\n    }\n    return (this.registrationForm.value.recovery * (1 / portion))\n  }\n\n  getCostPerPortion() {\n    let portion = this.registrationForm.value.portion\n    if (this.registrationForm.value.unit === 'GM/ML') {\n       portion = this.registrationForm.value.recovery / this.registrationForm.value.portion\n    }\n    return (this.registrationForm.value.rate * (1 / portion))\n  }\n\n  setPortionData() {\n    if (this.registrationForm.value.unit === 'GM/ML') {\n      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(this.registrationForm.value.recovery) : undefined ;\n    } else {\n      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(1) : undefined ;\n    }\n  }\n\n  updateBaseData(data){\n    data['menu master'].forEach((el)=> {\n      const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);\n      el['modified'] = 'no'\n      if (index !== -1) {\n        this.baseData['menu master'][index] = el\n      }\n    })\n    data['menu recipes'].forEach((el)=> {\n      const recipeIndex = this.baseData['menu recipes'].findIndex(item => item['menuItemCode'] === el['menuItemCode'] && item['ingredientCode'] === el['ingredientCode']);\n      el['modified'] = 'no'\n      if (recipeIndex !== -1) {\n        this.baseData['menu recipes'][recipeIndex] = el\n      }\n    })\n\n\n    data['Subrecipe Master'].forEach((el)=> {\n      const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);\n      el['modified'] = 'no'\n      if (index !== -1) {\n        this.baseData['Subrecipe Master'][index] = el\n      }\n    })\n    data['Subrecipe Recipe'].forEach((el)=> {\n      const recipeIndex = this.baseData['Subrecipe Recipe'].findIndex(item => item['subRecipeCode'] === el['subRecipeCode'] && item['ingredientCode'] === el['ingredientCode']);\n      el['modified'] = 'no'\n      if (recipeIndex !== -1) {\n        this.baseData['Subrecipe Recipe'][recipeIndex] = el\n      }\n    })\n    this.sharedData.setBaseData(this.baseData)\n  }\n\n  onDelete(location: string, event: Event, select ,group) {\n    event.stopPropagation();\n    this.selectedDropDown = select\n    this.selectedData = location\n    this.groupData = group\n    this.dialogRef = this.dialog.open(this.discontinuedSelectDialog, {\n      width: '600px',\n      panelClass: 'mediumCustomDialog',\n    });\n    this.dialogRef.afterClosed().subscribe(result => {\n    });\n  }\n\n  onRestore(location: string, event: Event, select ,group){\n    event.stopPropagation();\n    if(select === 'usedOutlet'){\n      this.discontinuedOutletData = this.discontinuedOutletData.filter(item => item !== location);\n      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item =>\n        item.abbreviatedRestaurantId !== location\n      );\n      this.usedInWorkAreaBank =  this.usedInWorkAreaBank.map(item => {\n        if (item.abbreviatedRestaurantId === location && item.hasOwnProperty('disabled')) {\n          delete item.disabled;\n        }\n        return item;\n      });\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(() => {\n          this.FilterIssued(\n            this.usedInWorkAreaBank,\n            this.usedInWorkAreaFilterCtrl,\n            this.usedInWorkAreaNames\n          );\n        });\n    }else if(select === 'issuedTo'){\n      this.discontinuedIssuedToData.forEach(item => {\n        if (item.abbreviatedRestaurantId === group.abbreviatedRestaurantId) {\n          item.workAreas = item.workAreas.filter(workArea => workArea !== location);\n        }\n      });\n      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item.workAreas.length > 0);\n    }else if(select === 'preparatoryLocation'){\n      this.discontinuedPreLocData = this.discontinuedPreLocData.filter(item => item !== location);\n    }\n    this.cd.detectChanges();\n  }\n\n  discontinuedSelectData(){\n    if(this.selectedDropDown === 'usedOutlet'){\n      this.discontinuedOutletData.push(this.selectedData)\n      const selectedWorkAreasArray = this.locationList.filter(branch => this.selectedData.includes(branch.abbreviatedRestaurantId))\n      this.discontinuedIssuedToData.push(selectedWorkAreasArray[0])\n      this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {\n        if (item.abbreviatedRestaurantId === this.selectedData) {\n          item.disabled = true;\n        }\n        return item;\n      });\n      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());\n      this.usedInWorkAreaFilterCtrl.valueChanges\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(() => {\n          this.FilterIssued(\n            this.usedInWorkAreaBank,\n            this.usedInWorkAreaFilterCtrl,\n            this.usedInWorkAreaNames\n          );\n        });\n\n    }else if(this.selectedDropDown === 'issuedTo'){\n      [this.groupData].forEach(item => {\n        const matchingIssued = this.discontinuedIssuedToData.find(issuedItem => issuedItem.abbreviatedRestaurantId === item.abbreviatedRestaurantId);\n        if (matchingIssued) {\n            matchingIssued.workAreas.push(this.selectedData);\n        } else {\n            const newObject = {\n                abbreviatedRestaurantId: item.abbreviatedRestaurantId,\n                workAreas: [this.selectedData]\n            };\n            this.discontinuedIssuedToData.push(newObject);\n        }\n      });\n      const newArray = [this.groupData].map(item => {\n          return {\n              abbreviatedRestaurantId: item.abbreviatedRestaurantId,\n              workAreas: item.workAreas.filter(workArea => workArea === this.selectedData)\n          };\n      });\n      this.discontinuedIssuedToData.push(...newArray);\n    }else if(this.selectedDropDown === 'preparatoryLocation'){\n      this.discontinuedPreLocData.push(this.selectedData)\n    }\n    this.closeDiscontinuedDialog();\n    this.cd.detectChanges();\n  }\n\n  closeDiscontinuedDialog(){\n    this.dialogRef.close();\n  }\n\n  getPerKGCost() {\n    return ((this.registrationForm.value.rate ) / this.registrationForm.value.recovery) * 1000\n  }\n\n  isOptionDisabled(data: string , group): boolean {\n    return this.discontinuedIssuedToData.some(item =>\n      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&\n      item.workAreas.includes(data)\n    );\n  }\n\n  isCheckOptionDisabled(data: string , group): boolean {\n    return this.discontinuedIssuedToData.some(item =>\n      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&\n      item.workAreas.includes(data)\n    );\n  }\n\n  setDiscontinuedDataInRolopos(){\n    this.api.dicontinuedData({\n      'tenantId' : this.user.tenantId,\n      'userEmail' : this.user.email,\n      'type' : 'subRecipeLocations',\n      'discontinuedLocations' : {\n        'subRecipeLocations' : {\n          'procuredAtDiscontinued' : this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData : [],\n          'outLetDiscontinued' : this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData : [],\n          'issuedToDiscontinued' : this.discontinuedIssuedToData.length > 0 ? this.discontinuedIssuedToData : []\n        }\n      }\n    }).pipe(first()).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.cd.detectChanges();\n        }\n      },\n      error: (err) => { console.log(err) }\n    });\n  }\n\n}\n", "<div class=\"closeBtn\" *ngIf=\"isDuplicate == true\">\n  <mat-icon (click)=\"close()\" matTooltip=\"close\" class=\"closeBtnIcon\">close</mat-icon>\n</div>\n<div class=\"registration-form py-2 px-3\">\n  <div *ngIf=\"this.costDialogkey == true\" class=\"spinner-border\" role=\"status\">\n    <span class=\"sr-only\">Loading...</span>\n  </div>\n  <div class=\"m-1\" *ngIf=\"isDuplicate === null\">\n    <mat-form-field appearance=\"outline\">\n      <mat-label>Search</mat-label>\n      <input matInput placeholder=\"Search\" (keyup)=\"filterDialog($event)\" aria-label=\"Search\">\n      <mat-icon matSuffix>search</mat-icon>\n    </mat-form-field>\n  </div>\n\n  <div *ngIf=\"isDuplicate == true\" class=\"mt-3 smallDialog\">\n    <div class=\"col-md-12\">\n      <div class=\"text-center my-2 p-2 bottomTitles\">\n        <span>SubRecipe Master Form</span>\n      </div>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Search SubRecipe ..</mat-label>\n        <input matInput placeholder=\"SubRecipe..\" aria-label=\"Inventory\" [matAutocomplete]=\"auto1\"\n          (keyup)=\"checkItem($event)\" [formControl]=\"itemNameControl\" (keyup.enter)=\"addOption('Subrecipe Master')\"\n          oninput=\"this.value = this.value.toUpperCase()\">\n        <mat-autocomplete #auto1=\"matAutocomplete\" (optionSelected)=\"optionSelected('Subrecipe Master', $event.option)\">\n          <mat-option *ngFor=\"let item of itemNameOptions | async\" [value]=\"item\">\n            <span>{{ item | uppercase }}</span>\n          </mat-option>\n        </mat-autocomplete>\n        <mat-icon matSuffix>search</mat-icon>\n      </mat-form-field>\n      <div class=\"text-end\">\n        <button (click)=\"addOption('Subrecipe Master')\" mat-raised-button color=\"accent\"\n          [disabled]=\"!itemNameControl.value\">\n          <div *ngIf=\"loadBtn\" class=\"spinner-border\" role=\"status\">\n            <span class=\"sr-only\">Loading...</span>\n          </div>\n          <mat-icon *ngIf=\"!updateBtnActive\">library_add</mat-icon>\n          <mat-icon *ngIf=\"updateBtnActive\">update</mat-icon>\n          <span *ngIf=\"updateBtnActive\">Update</span>\n          <span *ngIf=\"!updateBtnActive\">Add</span>\n        </button>\n      </div>\n    </div>\n  </div>\n  <div class=\"mb-2 topCreateAndUpdateBtn\" style=\"float: right; padding-top: 1rem;\">\n    <button *ngIf=\"isUpdateActive && (isDuplicate == false && !showSRR)\" mat-raised-button class=\"mappingBtn discButton\" style=\"margin-right: 5px;\" matTooltip=\"print\" (click)=\"printOption()\" type=\"button\">\n      <mat-icon>print</mat-icon>\n      Print</button>\n    <button *ngIf=\"isUpdateActive && (isDuplicate == false && !showSRR)\" style=\"margin-right: 5px;\" (click)=\"update()\" mat-raised-button\n      color=\"accent\" matTooltip=\"update\" [disabled]=\"loadSrmBtn || loadSpinnerForApi\">\n      <div *ngIf=\"loadSpinnerForApi\" class=\"spinner-border\" role=\"status\">\n        <span class=\"sr-only\">Loading...</span>\n      </div>\n      Update\n    </button>\n\n    <button *ngIf=\"!isUpdateActive && (isDuplicate == false && !showSRR)\" (click)=\"submit(); isButtonDisabled = true\" style=\"margin-right: 5px;\" mat-raised-button color=\"accent\" matTooltip=\"Create\"\n    [disabled]=\"dataSource.data.length === 0 || this.registrationForm.invalid || loadSpinnerForApi\">\n      <div *ngIf=\"loadSpinnerForApi\" class=\"spinner-border\" role=\"status\">\n        <span class=\"sr-only\">Loading...</span>\n      </div>\n      <mat-icon>add_circle</mat-icon> Create\n    </button>\n\n    <button *ngIf=\"isDuplicate == false && !showSRR\" mat-raised-button color=\"warn\" (click)=\"close()\" matTooltip=\"Close\" style=\"margin-right: 7px;\">\n      <mat-icon>close</mat-icon>\n      Close\n    </button>\n  </div>\n  <div class=\"pb-2\">\n    <div class=\" my-2 p-3 bottomTitles\" *ngIf=\"isDuplicate == false\">\n      Sub-Recipe Master\n    </div>\n  </div>\n\n  <div *ngIf=\"isDuplicate == false && !showSRR\">\n    <div class=\"d-flex justify-content-center\">\n        <form [formGroup]=\"registrationForm\">\n          <div class=\"row\">\n            <!-- Editable Fields -->\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Menu Item code</mat-label>\n                <input formControlName=\"menuItemCode\" matInput placeholder=\"Menu Item Code\" autocomplete=\"off\"\n                  [readonly]=\"isReadOnly\" [ngClass]=\"{'readonly-field': isReadOnly}\">\n                <mat-icon matSuffix>receipt</mat-icon>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Menu Item Name</mat-label>\n                <input formControlName=\"menuItemName\" matInput placeholder=\"Menu Item Name\" [readonly]=\"isReadOnly\"\n                  [ngClass]=\"{'readonly-field': isReadOnly}\">\n                <mat-icon matSuffix>restaurant_menu</mat-icon>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>UOM</mat-label>\n                <mat-select formControlName=\"uom\">\n                  <mat-option *ngFor=\"let uom of ['GM','ML','NOS']\" [value]=\"uom\">\n                    {{uom}}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Closing UOM</mat-label>\n                <mat-select formControlName=\"closingUOM\">\n                  <mat-option *ngFor=\"let uom of ['KG', 'LITRE', 'NOS']\" [value]=\"uom\">\n                    {{uom}}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Category</mat-label>\n                <input matInput placeholder=\"Category Name\" aria-label=\"Category\" [matAutocomplete]=\"auto1\"\n                  formControlName=\"category\" (keyup.enter)=\"addOptionCat()\" oninput=\"this.value = this.value.toUpperCase()\">\n                <mat-autocomplete #auto1=\"matAutocomplete\" (optionSelected)=\"optionSelectedCat($event.option)\">\n                  <mat-option *ngFor=\"let cat of catBank | async\" [value]=\"cat\">\n                    <span>{{ cat }}</span>\n                  </mat-option>\n                </mat-autocomplete>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Sub Category</mat-label>\n                <input matInput placeholder=\"sub Category\" aria-label=\"SubCategory\" [matAutocomplete]=\"auto2\"\n                  formControlName=\"subCategory\" (keyup.enter)=\"addOptionSubCat()\"\n                  oninput=\"this.value = this.value.toUpperCase()\">\n                <mat-autocomplete #auto2=\"matAutocomplete\" (optionSelected)=\"optionSelectedSubCat($event.option)\">\n                  <mat-option *ngFor=\"let sub of subCatBank | async\" [value]=\"sub\">\n                    <span>{{ sub }}</span>\n                  </mat-option>\n                </mat-autocomplete>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Prepared At</mat-label>\n                <mat-select formControlName=\"preparedAt\" multiple>\n                  <mat-option>\n                    <ngx-mat-select-search placeholderLabel=\"search...\" noEntriesFoundLabel=\"'not found'\"\n                      [formControl]=\"preparedFilterCtrl\"></ngx-mat-select-search>\n                  </mat-option>\n                  <mat-option class=\"hide-checkbox\" (click)=\"preparedToggleSelectAll()\">\n                    <mat-icon matSuffix>check_circle</mat-icon>\n                    Select All / Deselect All\n                  </mat-option>\n                  <!-- <mat-option *ngFor=\"let loc of preparedLocationNames | async\" [value]=\"loc\">\n                    {{loc}}\n                  </mat-option> -->\n                  <mat-option *ngFor=\"let loc of preparedLocationNames | async\" [value]=\"loc\" [disabled]=\"discontinuedPreLocData.includes(loc)\"\n                    [ngClass]=\"{'disabled-option': this.defaultPreLocData.includes(loc) || discontinuedPreLocData.includes(loc)}\">\n                    <span [ngClass]=\"{'disabledSelect': discontinuedPreLocData.includes(loc)}\">{{ loc | uppercase }}</span>\n                    <mat-icon *ngIf=\"this.defaultPreLocData.includes(loc) && !this.discontinuedPreLocData.includes(loc)\"\n                      class=\"deleteIconForMatSelect\" matTooltip=\"discontinue\"\n                      (click)=\"onDelete(loc, $event, 'preparatoryLocation' , 'null')\"\n                      [ngClass]=\"{'clickable': discontinuedPreLocData.includes(loc)}\">\n                      delete\n                    </mat-icon>\n                    <mat-icon *ngIf=\"this.discontinuedPreLocData.includes(loc)\"\n                      class=\"deleteIconForMatSelect\" matTooltip=\"restore\"\n                      (click)=\"onRestore(loc, $event, 'preparatoryLocation','null')\">\n                      settings_backup_restore</mat-icon>\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Sales Outlet</mat-label>\n                <mat-select formControlName=\"usedAtOutlet\" (selectionChange)=\"locationChange($event.value)\" multiple>\n                  <mat-option>\n                    <ngx-mat-select-search placeholderLabel=\"search...\" noEntriesFoundLabel=\"'not found'\"\n                      [formControl]=\"outletFilterCtrl\"></ngx-mat-select-search>\n                  </mat-option>\n                  <mat-option class=\"hide-checkbox\" (click)=\"usedOutLetToggleSelectAll()\">\n                    <mat-icon matSuffix>check_circle</mat-icon>\n                    Select All / Deselect All\n                  </mat-option>\n                  <!-- <mat-option *ngFor=\"let loc of outletLocationNames | async\" [value]=\"loc\">\n                    {{loc}}\n                  </mat-option> -->\n                  <mat-option *ngFor=\"let loc of outletLocationNames | async\" [value]=\"loc\" [disabled]=\"discontinuedOutletData.includes(loc)\"\n                    [ngClass]=\"{'disabled-option': this.defaultOutletData.includes(loc) || discontinuedOutletData.includes(loc)}\">\n                    <span [ngClass]=\"{'disabledSelect': discontinuedOutletData.includes(loc)}\">{{ loc | uppercase }}</span>\n                    <mat-icon *ngIf=\"this.defaultOutletData.includes(loc) && !this.discontinuedOutletData.includes(loc)\"\n                      class=\"deleteIconForMatSelect\" matTooltip=\"discontinue\"\n                      (click)=\"onDelete(loc, $event, 'usedOutlet', 'null')\"\n                      [ngClass]=\"{'clickable': discontinuedOutletData.includes(loc)}\">\n                      delete\n                    </mat-icon>\n                    <mat-icon *ngIf=\"this.discontinuedOutletData.includes(loc)\"\n                      class=\"deleteIconForMatSelect\" matTooltip=\"restore\"\n                      (click)=\"onRestore(loc, $event, 'usedOutlet','null')\">\n                      settings_backup_restore</mat-icon>\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Issued To</mat-label>\n                <mat-select formControlName=\"usedInWorkArea\" multiple>\n                  <mat-option>\n                    <ngx-mat-select-search placeholderLabel=\"search...\" noEntriesFoundLabel=\"'not found'\"\n                      [formControl]=\"usedInWorkAreaFilterCtrl\"></ngx-mat-select-search>\n                  </mat-option>\n                  <mat-optgroup *ngFor=\"let group of usedInWorkAreaNames | async\"\n                    [label]=\"group.restaurantIdOld.split('@')[1]\" [disabled]=\"group.disabled\">\n                    <!-- <mat-option *ngFor=\"let data of group.workAreas\" [value]=\"data\">\n                      {{data}}\n                    </mat-option> -->\n                    <mat-option *ngFor=\"let data of group.workAreas\" [value]=\"data\" [disabled]=\"isOptionDisabled(data , group)\"\n                    [ngClass]=\"{'disabled-option': isCheckOptionDisabled(data , group) || this.defaultIssuedToData.includes(data)}\">\n                    <span [ngClass]=\"{'disabledSelect': isOptionDisabled(data , group) || group.disabled}\">{{ data | uppercase }}</span>\n                    <mat-icon *ngIf=\"!discontinuedOutletData.includes(group.abbreviatedRestaurantId) && this.defaultIssuedToData.includes(data) && !isOptionDisabled(data , group)\"\n                        class=\"deleteIconForMatSelect\" matTooltip=\"discontinue\"\n                        (click)=\"onDelete(data, $event, 'issuedTo' , group)\"\n                        [ngClass]=\"{'clickable': discontinuedIssuedToData.includes(data)}\">\n                        delete\n                      </mat-icon>\n                      <mat-icon *ngIf=\"isOptionDisabled(data , group) && !group.disabled\"\n                        class=\"deleteIconForMatSelect\" matTooltip=\"restore\" (click)=\"onRestore(data, $event, 'issuedTo',group)\">\n                        settings_backup_restore\n                      </mat-icon>\n                  </mat-option>\n\n                  </mat-optgroup>\n                </mat-select>\n              </mat-form-field>\n              <mat-error class=\"formError\" *ngIf=\"showWorkAreaError\">\n                * select at least one workarea in every branch\n              </mat-error>\n            </div>\n\n            <!-- Non-Editable Fields with Better Design -->\n            <div class=\"col-md-3\">\n              <div class=\"non-editable-field\">\n                <label>Initial Weight</label>\n                <span>{{ registrationForm.get('weightInUse').value }}</span>\n              </div>\n            </div>\n\n            <div class=\"col-md-3\">\n              <div class=\"non-editable-field\">\n                <label>Final Weight</label>\n                <span>{{ registrationForm.get('recovery').value }}</span>\n              </div>\n            </div>\n\n            <div class=\"col-md-3\">\n              <div class=\"non-editable-field\">\n                  <label>Preparation Cost </label>\n                  <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n                    <span (click)=\"openUnitDialog()\"> {{ this.notify.truncateAndFloor(registrationForm.get('rate').value,2) }}</span>\n                  </div>\n              </div>\n            </div>\n\n            <div class=\"col-md-3\">\n              <div class=\"non-editable-field\">\n                  <label>Usage Cost per UOM </label>\n                  <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n                    <span > {{ this.notify.truncateAndFloor(((this.registrationForm.value.rate ) / this.registrationForm.value.recovery) * 1000,2)}}</span>\n                  </div>\n              </div>\n            </div>\n\n            <div class=\"col-md-3\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Final Yield</mat-label>\n                <input\n                  formControlName=\"yield\"\n                  type=\"number\"\n                  matInput\n                  placeholder=\"Yield\"\n                  (keyup)=\"setZeroSRM($event, 'yield')\"\n                  autocomplete=\"off\"\n                  (focus)=\"focusFunction('yield')\"\n                  (focusout)=\"focusOutFunction('yield')\"\n                >\n                <mat-error *ngIf=\"registrationForm.get('yield').errors?.['yieldInvalid']\">\n                  Yield should be more than 0\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Batch Section -->\n            <div class=\"col-md-3\">\n              <div class=\"d-flex align-items-center\">\n                <mat-form-field appearance=\"outline\" class=\"flex-fill me-2\">\n                  <mat-label>Batch</mat-label>\n                  <mat-select placeholder=\"Select Unit\" formControlName=\"unit\" (selectionChange)=\"onUnitChange($event.value)\">\n                    <mat-option *ngFor=\"let unit of units\" [value]=\"unit\">{{unit}}</mat-option>\n                  </mat-select>\n                </mat-form-field>\n                <mat-form-field appearance=\"outline\" class=\"flex-fill me-2\">\n                  <input formControlName=\"portion\" type=\"number\" matInput placeholder=\"Batch\" (keyup)=\"setPortionData()\"\n                    autocomplete=\"off\" (focus)=\"focusFunction('portion')\" (focusout)=\"focusOutFunction('portion')\">\n                  <mat-error *ngIf=\"registrationForm.get('portion').errors?.['portionInvalid']\">\n                    Portion should be more than 0\n                  </mat-error>\n                  <mat-icon matSuffix (click)=\"openPortionData()\" class=\"custom-outline-button\"\n                    matTooltip=\"Portion Details\">info</mat-icon>\n                </mat-form-field>\n              </div>\n            </div>\n\n            <!-- Additional Fields -->\n            <div *ngIf=\"isUpdateActive\">\n              <div class=\"col-md-12\">\n                <label>Do you want to discontinue?</label>\n                <mat-radio-group formControlName=\"discontinued\">\n                  <mat-radio-button value=\"yes\">Yes</mat-radio-button>\n                  <mat-radio-button value=\"no\">No</mat-radio-button>\n                </mat-radio-group>\n              </div>\n            </div>\n          </div>\n        </form>\n    </div>\n\n    <div>\n      <div class=\"my-2 p-2 bottomTitles\">\n        Sub-Recipe Recipe\n      </div>\n      <!-- <br> -->\n      <div class=\"searchInputParentClass my-2\">\n      <div>\n        <div cdkTrapFocus>\n          <form [formGroup]=\"subRecipeRecipeForm\">\n            <div class=\"d-flex gap-3\">\n              <div class=\"form-group customHeightfield\">\n                <label for=\"ingredientSelect\">Ingredient Name</label>\n                <input matInput placeholder=\"ingredient Name\" [matAutocomplete]=\"autoIngredients\" class=\"form-control\"\n                    formControlName=\"ingredientName\" oninput=\"this.value = this.value.toUpperCase()\">\n                  <mat-autocomplete #autoIngredients=\"matAutocomplete\"\n                  (optionSelected)=\"selectIngredientsName(subRecipeRecipeForm.value.ingredientName)\">\n                    <mat-option *ngFor=\"let name of ingredientNamesOptions | async\" [value]=\"name\" [ngClass]=\"{'text-warning': subData.includes(name)}\" [disabled]=\"(updateSRR && subRecipeRecipeForm.value.ingredientName) || name === 'No Item Found' \">\n                      <span>{{ name | uppercase }}</span>\n                    </mat-option>\n                  </mat-autocomplete>\n              </div>\n\n              <div class=\"form-group customHeightfield\">\n                <label for=\"modifierSelect\">UOM</label>\n                <select class=\"form-select\" id=\"modifierSelect\" formControlName=\"uom\" style=\"width: 80px !important;\"\n                  (change)=\"uomChange()\">\n                  <option *ngFor=\"let data of ingredientUOM\" [value]=\"data\" [disabled]=\"!isOptionAccessible(data)\">\n                    {{ data }}\n                  </option>\n                </select>\n              </div>\n\n              <div class=\"form-group customHeightfield\" *ngIf=\"this.subRecipeRecipeForm.value.uom  === 'PORTION'\">\n                <label for=\"portionSelect\">Portion</label>\n                <input formControlName=\"portionCount\" type=\"number\" class = \"highlighted-input form-control\" (focus)=\"focusFunctionSRR('portionCount')\"\n                (focusout)=\"focusOutFunctionSRR('portionCount')\" (keyup)=\" convertPortionToUOM()\" placeholder=\"Rate\" autocomplete=\"off\">\n              </div>\n\n              <div class=\"form-group customHeightfield\">\n                <label for=\"unitCostSelect\">WAC(incl.tax,etc)/UOM</label>\n                <input formControlName=\"rate\" type=\"number\" class = \"highlighted-input form-control\" (focus)=\"focusFunctionSRR('rate')\"\n                (focusout)=\"focusOutFunctionSRR('rate')\" (keyup)=\" sumForFinalRateSRR($event)\" placeholder=\"Rate\"\n                autocomplete=\"off\" (focus)=\"focusFunction('rate')\" (focusout)=\"focusOutFunction('rate')\" readonly>\n              </div>\n\n              <div class=\"form-group customHeightfield\">\n                <label for=\"portionCountInput\">Weight in Use</label>\n                <input formControlName=\"weightInUse\" type=\"number\" (focus)=\"focusFunctionSRR('weightInUse')\"\n                (focusout)=\"focusOutFunctionSRR('weightInUse')\" (keyup)=\"sumForFinalRateSRR($event)\"\n                placeholder=\"Weight In Use\" autocomplete=\"off\" class=\"form-control\" [readonly]=\"this.subRecipeRecipeForm.value.uom === 'PORTION'\">\n                <div class=\"formError\" *ngIf=\"this.showWeightError\">\n                  * weight should be greater than 0\n                </div>\n              </div>\n\n              <div class=\"form-group customHeightfield\">\n                <label for=\"yieldInput\">Yield</label>\n                <input formControlName=\"yield\" type=\"number\" (keyup)=\"setZeroSRR($event , 'yield')\"\n                placeholder=\"Yield\" autocomplete=\"off\" (focus)=\"focusFunctionSRR('yield')\"\n                (focusout)=\"focusOutFunctionSRR('yield')\" class=\"form-control\">\n                <div *ngIf=\"subRecipeRecipeForm.get('yield').errors?.['yieldInvalid']\" class=\"formError\">\n                  Yield should be more than 0\n                </div>\n              </div>\n\n              <div class=\"form-group flex-shrink-0 d-flex align-items-end justify-content-end\"\n                style=\"margin-bottom: 0.1px;\">\n                <button type=\"submit\" style=\"height: 2.3rem;\" class=\"btn btn-secondary btn-sm px-3\"\n                  (click)=\"addNewSubRecipeRecipe()\" matTooltip=\"Add\">\n                  <i class=\"material-icons align-middle\">add</i> Add\n                </button>\n\n              </div>\n            </div>\n          </form>\n        </div>\n\n      </div>\n    </div>\n\n      <div>\n        <mat-slide-toggle [(ngModel)]=\"showDeleteItems\" class=\"mb-2 floatRightBtn\"\n        (change)=\"showItems()\">Show Discontinued</mat-slide-toggle>\n      </div>\n      <div class=\"section\" #section #widgetsContent>\n        <div class=\"tableDiv\" *ngIf=\"isSRRDataReady\">\n            <mat-table [dataSource]=\"dataSource\" matSort>\n              <ng-container matColumnDef=\"sNo\">\n                <mat-header-cell *matHeaderCellDef class=\"tableSnoCol\"> S.No. </mat-header-cell>\n                <mat-cell *matCellDef=\"let element; let i = index;\"\n                  class=\"tableSnoCol\">{{i+1}}</mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef class=\"tableSnoCol\">\n                </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"action\">\n                <mat-header-cell *matHeaderCellDef class=\"tableActionCol\"> Action </mat-header-cell>\n                <mat-cell class=\"tableActionCol\" *matCellDef=\"let element\">\n                  <button (click)=\"editFun(element,addSRR)\" backgroundColor=\"primary\" class=\"mx-2 editIconBtn\"\n                    matTooltip=\"Edit\"><mat-icon class=\"mt-1\">edit</mat-icon></button>\n                  <button (click)=\"deleteSRR(element)\" backgroundColor=\"primary\" class=\"mx-2 editIconBtn\"\n                    matTooltip=\"Delete\"><mat-icon class=\"mt-1\">delete</mat-icon></button>\n                </mat-cell>\n                <mat-footer-cell class=\"tableActionCol\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"discontinued\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Status </mat-header-cell>\n                <mat-cell class=\"custom-cell justify-content-start\" *matCellDef=\"let element\">\n                  <div *ngIf=\"element.Discontinued == 'yes'\" class=\"d-flex align-items-center\">\n                    <mat-icon class=\"cancelIcon\">cancel</mat-icon> &nbsp; Discontinued\n                  </div>\n                  <div *ngIf=\"element.Discontinued == 'no'\" class=\"d-flex align-items-center\">\n                    <mat-icon class=\"checkIcon\">check_circle</mat-icon> &nbsp; Active\n                  </div>\n                  <div *ngIf=\"element.Discontinued != 'no' && element.Discontinued != 'yes'\"\n                    class=\"d-flex align-items-center\">\n                    <mat-icon class=\"checkIcon\">check_circle</mat-icon> &nbsp; Active\n                  </div>\n                </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"modified\">\n                <mat-header-cell *matHeaderCellDef class=\"tableModCol\"> Modified </mat-header-cell>\n                <mat-cell *matCellDef=\"let element\" class=\"tableModCol\">\n                  <div *ngIf=\"element.modified == 'yes'\">\n                    <mat-chip color=\"primary\">NOT SYNCED</mat-chip>\n                  </div>\n                  <div *ngIf=\"element.modified == 'no' || element.modified == '-'\">\n                    -\n                  </div>\n                </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef class=\"tableModCol\"> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"ingredientName\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef style=\"min-width: 300px !important;\"> Ingredient Name </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\" style=\"min-width: 300px !important;\">\n                  <div matTooltip=\"Edit\" class=\"link mr-2\" (click)=\"editFun(element,addSRR)\"\n                  [ngClass]=\"{'text-warning': subData.includes(element.ingredientName)}\" style=\"width: 200px !important;\">\n                    {{element.ingredientName}}\n                  </div>\n                  <mat-icon *ngIf=\"element.Discontinued == 'no'\" class=\"checkIcon tableIcons\">check_circle</mat-icon>\n                  <mat-icon *ngIf=\"element.Discontinued != 'no' && element.Discontinued != 'yes'\" class=\"checkIcon tableIcons\">check_circle</mat-icon>\n                  <button (click)=\"deleteSRR(element)\" backgroundColor=\"primary\" class=\"mx-2 editIconBtn\"\n                    matTooltip=\"Delete\"><mat-icon class=\"mt-1\">delete</mat-icon></button>\n                </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef style=\"min-width: 300px !important;\"> Total </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"ingredientCode\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Ingredient Code </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.ingredientCode}} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"subRecipeName\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> SubRecipe Name </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.subRecipeName}} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"subRecipeCode\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> SubRecipe Code </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.subRecipeCode}} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"uom\">\n                <mat-header-cell style=\"min-width: 125px !important;\" *matHeaderCellDef> UOM </mat-header-cell>\n                <mat-cell style=\"min-width: 125px !important;\" *matCellDef=\"let element\"> {{element.UOM}} </mat-cell>\n                <mat-footer-cell style=\"min-width: 125px !important;\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"initialWeight\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Initial Weight </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.Initialweight || 0 }} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> {{this.getTotal('Initialweight')}}\n                </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"yield\">\n                <mat-header-cell style=\"min-width: 100px !important;\" *matHeaderCellDef> Yield </mat-header-cell>\n                <mat-cell style=\"min-width: 100px !important;\" *matCellDef=\"let element\"> {{element.yield || 0 }} </mat-cell>\n                <mat-footer-cell style=\"min-width: 100px !important;\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"loss\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Loss </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.loss || 0 }} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"weightInUse\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Weight in Use </mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{ this.notify.truncateAndFloor(element.weightInUse || 0) }} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> {{ getTotal('weightInUse') }} </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"rate\">\n                <mat-header-cell style=\"min-width: 125px !important;\"  *matHeaderCellDef> WAC(incl.tax,etc)</mat-header-cell>\n                <mat-cell style=\"min-width: 125px !important;\"  *matCellDef=\"let element\"> {{this.notify.truncateAndFloor(element.rate || 0) }} </mat-cell>\n                <mat-footer-cell style=\"min-width: 125px !important;\"  *matFooterCellDef> </mat-footer-cell>\n              </ng-container>\n\n              <ng-container matColumnDef=\"finalRate\">\n                <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Final Rate</mat-header-cell>\n                <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{this.notify.truncateAndFloor(element.finalRate,2 )|| 0 }} </mat-cell>\n                <mat-footer-cell class=\"custom-footer\" *matFooterCellDef> {{this.notify.truncateAndFloor(this.getSRRTotal('finalRate'),2)}}\n                </mat-footer-cell>\n              </ng-container>\n\n              <mat-header-row *matHeaderRowDef=\"displayedColumns\"></mat-header-row>\n              <mat-row *matRowDef=\"let row; columns: displayedColumns;\"\n                [ngClass]=\"{'highlighted-row': row.Discontinued === 'yes'}\"></mat-row>\n              <mat-footer-row *matFooterRowDef=\"displayedColumns\"></mat-footer-row>\n            </mat-table>\n            <mat-paginator class=\"mat-paginator-sticky\" [pageSize]=\"10\" [pageSizeOptions]=\"[5, 10, 25, 50, 100]\"></mat-paginator>\n        </div>\n        <div *ngIf=\"!isSRRDataReady\">\n          <ngx-skeleton-loader count=\"5\" animation=\"progress-dark\"\n            [theme]=\"{ 'border-radius': '5px', height: '30px' }\"></ngx-skeleton-loader>\n        </div>\n        <div *ngIf=\"dataSource.data.length == 0 && isSRRDataReady\">\n          <app-empty-state\n            *ngIf=\"this.showDeleteItems == false\"\n            icon=\"soup_kitchen\"\n            title=\"No Subrecipe Recipes Found\"\n            message=\"Time to get creative! Add ingredients to create your subrecipe.\"\n          ></app-empty-state>\n\n          <app-empty-state\n            *ngIf=\"this.showDeleteItems == true\"\n            icon=\"check_circle\"\n            title=\"No Discontinued Items\"\n            message=\"There are no discontinued ingredients to display.\"\n          ></app-empty-state>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- ----------------------------------   SUBRECIPE RECIPE FORM    ---------------------------------- -->\n  <ng-template #addSRR>\n    <div class=\"closeBtn\">\n      <mat-icon (click)=\"closeSRRDialog()\" matTooltip=\"close\" class=\"closeBtnIcon\">close</mat-icon>\n    </div>\n    <div class=\"registration-form py-2 px-3\">\n      <div class=\"text-center my-2 p-2 bottomTitles\">\n        <span>SubRecipe Recipe Form</span>\n      </div>\n      <div class=\"d-flex justify-content-end flex-wrap mb-3\">\n        <button *ngIf=\"updateSRR\" (click)=\"editExistingSubRecipeRecipe()\" mat-raised-button color=\"accent\"\n          matTooltip=\"update\">\n          <div *ngIf=\"loadSpinnerForApiSRR\" class=\"spinner-border\" role=\"status\">\n            <span class=\"sr-only\">Loading...</span>\n          </div>\n          Update\n        </button>\n      </div>\n\n      <form [formGroup]=\"subRecipeRecipeForm\">\n        <div class=\"row\">\n          <div>\n            <mat-form-field appearance=\"outline\" [ngClass]=\"{'highlighted-input': isReadOnly}\">\n              <mat-label>SubRecipe Name</mat-label>\n              <input formControlName=\"subRecipeName\" matInput placeholder=\"SubRecipe Name\" [readonly]=\"isReadOnly\">\n              <mat-icon matSuffix>restaurant_menu</mat-icon>\n            </mat-form-field>\n          </div>\n\n          <div>\n            <mat-form-field appearance=\"outline\" [ngClass]=\"{'highlighted-input': isReadOnly}\">\n              <mat-label>Ingredient Name</mat-label>\n              <mat-select placeholder=\"Ingredient Name\" formControlName=\"ingredientName\"\n                (selectionChange)=\"selectIngredientsName($event.value)\" >\n                <mat-option [disabled]=\"updateSRR\">\n                  <ngx-mat-select-search placeholderLabel=\"search...\" noEntriesFoundLabel=\"'not found'\"\n                    [formControl]=\"IngredientFilterCtrl\"></ngx-mat-select-search>\n                </mat-option>\n                <mat-option *ngFor=\"let name of ingredientNames | async\" [value]=\"name\" [disabled]=\"updateSRR\">\n                  {{ name }}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n\n          <div>\n            <mat-form-field appearance=\"outline\" [ngClass]=\"{'highlighted-input': isReadOnly}\" >\n              <mat-label>UOM</mat-label>\n              <mat-select formControlName=\"uom\">\n                <mat-option *ngFor=\"let data of ingredientUOM\" [value]=\"data\" [disabled]=\"updateSRR\">\n                  {{data}}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n\n          <div  *ngIf=\"this.subRecipeRecipeForm.value.uom  === 'PORTION'\">\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Portion</mat-label>\n              <input matInput type=\"number\" placeholder=\"Portion\" class=\"outline\" formControlName=\"portionCount\"\n                (keyup)=\"convertPortionToUOM()\" (focus)=\"focusFunctionSRR('portionCount')\"\n                (focusout)=\"focusOutFunction('portionCount')\" />\n            </mat-form-field>\n          </div>\n\n          <div >\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Weight In Use</mat-label>\n              <input formControlName=\"weightInUse\" type=\"number\" matInput (focus)=\"focusFunctionSRR('weightInUse')\"\n                (focusout)=\"focusOutFunctionSRR('weightInUse')\" (keyup)=\"sumForFinalRateSRR($event)\"\n                placeholder=\"Weight In Use\" autocomplete=\"off\" [readonly]=\"this.subRecipeRecipeForm.value.uom === 'PORTION'\" >\n            </mat-form-field>\n          </div>\n\n          <div>\n            <mat-form-field appearance=\"outline\" >\n              <mat-label>Yield</mat-label>\n              <input formControlName=\"yield\" type=\"number\" matInput (keyup)=\"setZeroSRR($event , 'yield')\"\n                placeholder=\"yield\" autocomplete=\"off\" (focus)=\"focusFunctionSRR('yield')\"\n                (focusout)=\"focusOutFunctionSRR('yield')\" >\n            </mat-form-field>\n          </div>\n\n          <div *ngIf=\"updateSRR\">\n            <div class=\"col\">\n              <label>Do you want to discontinue?</label>\n              <mat-radio-group formControlName=\"discontinued\" aria-labelledby=\"example-radio-group-label\">\n                <mat-radio-button value=\"yes\">Yes</mat-radio-button>\n                <mat-radio-button value=\"no\">No</mat-radio-button>\n              </mat-radio-group>\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  </ng-template>\n\n  <div *ngIf=\"isDuplicate === null\" class=\"mt-3 smallDialog dropDndDialog\">\n    <div *ngFor=\"let data of filteredData;let i = index\" class=\"my-2\">\n      {{i + 1}}. {{data}}\n    </div>\n    <div *ngIf=\"filteredData.length == 0\">\n      <app-empty-state\n        icon=\"search_off\"\n        title=\"No Results Found\"\n        message=\"No matching data found for your search criteria.\"\n        customClass=\"dialog-empty-state\"\n      ></app-empty-state>\n    </div>\n  </div>\n\n  <ng-template #openPortionDialog>\n    <div class=\"dialog-container\">\n      <div class=\"close-btn\">\n        <button mat-icon-button class=\"close-btn-icon\" aria-label=\"Close\" matTooltip=\"close\" (click)=\"closeInfoDialog()\">\n          <mat-icon>close</mat-icon>\n        </button>\n      </div>\n      <div class=\"mx-1 py-2 px-3\">\n        <div class=\"text-center my-2 p-2 bottom-titles\">\n          <span>Detailed Info</span>\n        </div>\n        <div class=\"portion-info\">\n          <table class=\"info-table\">\n            <tbody>\n              <tr>\n                <td class=\"info-key\"><strong>Gross Weight</strong></td>\n                <td class=\"info-value\">{{ this.notify.truncateAndFloor(this.registrationForm.value.weightInUse)  }}</td>\n                <td class=\"info-unit\">GM/ML</td>\n              </tr>\n              <tr>\n                <td class=\"info-key\"><strong>Portion Weight</strong></td>\n                <td class=\"info-value\">{{ this.notify.truncateAndFloor(getWeightPerPortion())  }}</td>\n                <td class=\"info-unit\">GM/ML</td>\n              </tr>\n              <br>\n              <tr>\n                <td class=\"info-key\"><strong>Gross Cost</strong></td>\n                <td class=\"info-value\">{{this.notify.truncateAndFloor( this.registrationForm.value.rate) }}</td>\n                <td class=\"info-unit\">Rs</td>\n              </tr>\n              <tr>\n                <td class=\"info-key\"><strong>Portion Cost</strong></td>\n                <td class=\"info-value\">{{ this.notify.truncateAndFloor(getCostPerPortion()) }}</td>\n                <td class=\"info-unit\">Rs</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </ng-template>\n\n  <ng-template #openUnitCostDialog>\n    <div class=\"dialog-container\">\n      <div class=\"close-btn\">\n        <button mat-icon-button class=\"close-btn-icon\" aria-label=\"Close\" matTooltip=\"close\" (click)=\"closeInfoDialog()\">\n          <mat-icon>close</mat-icon>\n        </button>\n      </div>\n      <div class=\"mx-1 py-2 px-3\">\n        <div class=\"text-center my-2 p-2 bottom-titles\">\n          <span>Detailed Info</span>\n        </div>\n        <div class=\"portion-info\">\n          <table class=\"info-table\">\n            <tbody>\n              <tr>\n                <td class=\"info-key\"><strong>WAC Per KG</strong></td>\n                <!-- <td class=\"info-value\">{{ this.notify.truncateAndFloor((this.registrationForm.value.rate / this.registrationForm.value.weightInUse) * 1000) }}</td> -->\n                <td class=\"info-value\">{{ this.notify.truncateAndFloor(getPerKGCost(),2) }}</td>\n                <td class=\"info-unit\">Rs</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </ng-template>\n\n  <ng-template #openDeleteDialog>\n    <div class=\"dialog-container\">\n      <div class=\"close-btn\">\n        <button mat-icon-button class=\"close-btn-icon\" aria-label=\"Close\" matTooltip=\"close\"\n        (click)=\"closeInfoDialog()\">\n          <mat-icon>close</mat-icon>\n        </button>\n      </div>\n      <div class=\"registration-form py-2 px-3\">\n        <div class=\"text-center my-2 p-2 bottom-titles\">\n          <span>Confirm Ingredient Deletion</span>\n        </div>\n        <div class=\"portion-info1\">\n\n          <div class=\"mb-3\" >\n            Deleting this ingredient is permanent and will impact past records. Alternatively, you can choose to discontinue it\n                   </div>\n          <div class=\"d-flex justify-content-center gap-3 m-2\">\n            <button mat-raised-button class=\"deleteBtn\" matTooltip=\"delete\" (click)=\"deleteData()\">\n              Yes, Delete\n            </button>\n            <button mat-raised-button class=\"discBtn\" matTooltip=\"discontinue\" (click)=\"discontinueData()\">\n              Just, Discontinue\n            </button>\n          </div>\n\n        </div>\n      </div>\n    </div>\n  </ng-template>\n\n  <ng-template #discontinuedSelectDialog>\n    <div class=\"registration-form py-2 px-3\">\n      <div class=\"text-center my-2 p-2 bottomTitles\">\n        <span>Discontinued Location</span>\n      </div>\n      <div class=\"m-3 infoText text-center\">\n        Would you like to discontinue {{selectedData}} ?\n      </div>\n      <div class=\"text-end m-2\">\n        <button mat-raised-button (click)=\"discontinuedSelectData()\" color=\"accent\" matTooltip=\"Update\" class=\"m-1\">\n          Yes</button>\n        <button (click)=\"closeDiscontinuedDialog()\" mat-raised-button matTooltip=\"close\" class=\"m-1\">\n          No</button>\n      </div>\n    </div>\n  </ng-template>\n\n</div>\n\n"], "mappings": ";AAUA,SAAoBA,UAAU,QAAQ,eAAe;AACrD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAGEC,WAAW,EAEXC,WAAW,EACXC,mBAAmB,EAEnBC,UAAU,QACL,gBAAgB;AAGvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,mBAAmB,EAAEC,SAAS,QAAQ,wBAAwB;AACvE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAAqCC,eAAe,QAAQ,0BAA0B;AACtF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAEEC,OAAO,EACPC,KAAK,EACLC,GAAG,EAEHC,SAAS,EACTC,SAAS,QACJ,MAAM;AACb,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SACEC,eAAe,EAEfC,eAAe,QAEV,0BAA0B;AAIjC,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAA0BC,gBAAgB,QAAQ,2BAA2B;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAAqBC,gBAAgB,QAAQ,2BAA2B;AACxE,SAASC,uBAAuB,QAAQ,qBAAqB;AAE7D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,mBAAmB,QAAQ,sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtE1FC,EAAA,CAAAC,cAAA,cAAkD;IACtCD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IAAyCT,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAGpFX,EAAA,CAAAC,cAAA,cAA6E;IACrDD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAEzCX,EAAA,CAAAC,cAAA,cAA8C;IAE/BD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC7BX,EAAA,CAAAC,cAAA,gBAAwF;IAAnDD,EAAA,CAAAE,UAAA,mBAAAU,sDAAAC,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,IAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAO,OAAA,CAAAC,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAAnEb,EAAA,CAAAW,YAAA,EAAwF;IACxFX,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAejCX,EAAA,CAAAC,cAAA,qBAAwE;IAChED,EAAA,CAAAU,MAAA,GAAsB;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADoBX,EAAA,CAAAiB,UAAA,UAAAC,QAAA,CAAc;IAC/DlB,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAH,QAAA,EAAsB;;;;;IAQ9BlB,EAAA,CAAAC,cAAA,cAA0D;IAClCD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAEzCX,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IACzDX,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IACnDX,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAC3CX,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IA1BjDX,EAAA,CAAAC,cAAA,cAA0D;IAG9CD,EAAA,CAAAU,MAAA,4BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEpCX,EAAA,CAAAC,cAAA,yBAAqC;IACxBD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC1CX,EAAA,CAAAC,cAAA,gBAEkD;IADhDD,EAAA,CAAAE,UAAA,mBAAAoB,sDAAAT,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgB,OAAA,CAAAC,SAAA,CAAAZ,MAAA,CAAiB;IAAA,EAAC,yBAAAa,4DAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAI,OAAA,GAAA3B,EAAA,CAAAO,aAAA;MAAA,OAAgDP,EAAA,CAAAQ,WAAA,CAAAmB,OAAA,CAAAC,SAAA,CAAU,kBAAkB,CAAC;IAAA,EAA7E;IAD7B5B,EAAA,CAAAW,YAAA,EAEkD;IAClDX,EAAA,CAAAC,cAAA,+BAAgH;IAArED,EAAA,CAAAE,UAAA,4BAAA2B,0EAAAhB,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAO,OAAA,GAAA9B,EAAA,CAAAO,aAAA;MAAA,OAAkBP,EAAA,CAAAQ,WAAA,CAAAsB,OAAA,CAAAC,cAAA,CAAe,kBAAkB,EAAAlB,MAAA,CAAAmB,MAAA,CAAgB;IAAA,EAAC;IAC7GhC,EAAA,CAAAiC,UAAA,KAAAC,4CAAA,yBAEa;;IACflC,EAAA,CAAAW,YAAA,EAAmB;IACnBX,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAEvCX,EAAA,CAAAC,cAAA,eAAsB;IACZD,EAAA,CAAAE,UAAA,mBAAAiC,wDAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAa,OAAA,GAAApC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4B,OAAA,CAAAR,SAAA,CAAU,kBAAkB,CAAC;IAAA,EAAC;IAE7C5B,EAAA,CAAAiC,UAAA,KAAAI,qCAAA,iBAEM;IACNrC,EAAA,CAAAiC,UAAA,KAAAK,0CAAA,uBAAyD;IACzDtC,EAAA,CAAAiC,UAAA,KAAAM,0CAAA,uBAAmD;IACnDvC,EAAA,CAAAiC,UAAA,KAAAO,sCAAA,mBAA2C;IAC3CxC,EAAA,CAAAiC,UAAA,KAAAQ,sCAAA,mBAAyC;IAC3CzC,EAAA,CAAAW,YAAA,EAAS;;;;;IApBwDX,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAiB,UAAA,oBAAAyB,IAAA,CAAyB,gBAAAC,MAAA,CAAAC,eAAA;IAI3D5C,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,QAAAsB,MAAA,CAAAE,eAAA,EAA0B;IAQvD7C,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAiB,UAAA,cAAA0B,MAAA,CAAAC,eAAA,CAAAE,KAAA,CAAmC;IAC7B9C,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAiB,UAAA,SAAA0B,MAAA,CAAAI,OAAA,CAAa;IAGR/C,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAiB,UAAA,UAAA0B,MAAA,CAAAK,eAAA,CAAsB;IACtBhD,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAiB,UAAA,SAAA0B,MAAA,CAAAK,eAAA,CAAqB;IACzBhD,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAiB,UAAA,SAAA0B,MAAA,CAAAK,eAAA,CAAqB;IACrBhD,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAiB,UAAA,UAAA0B,MAAA,CAAAK,eAAA,CAAsB;;;;;;IAMnChD,EAAA,CAAAC,cAAA,iBAAyM;IAAtCD,EAAA,CAAAE,UAAA,mBAAA+C,0DAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2C,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACxLpD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC1BX,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAGdX,EAAA,CAAAC,cAAA,cAAoE;IAC5CD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAH3CX,EAAA,CAAAC,cAAA,iBACkF;IADcD,EAAA,CAAAE,UAAA,mBAAAmD,0DAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAEhHxD,EAAA,CAAAiC,UAAA,IAAAwB,uCAAA,iBAEM;IACNzD,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAL4BX,EAAA,CAAAiB,UAAA,aAAAyC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAA4C;IACzE5D,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,SAAAyC,MAAA,CAAAE,iBAAA,CAAuB;;;;;IAQ7B5D,EAAA,CAAAC,cAAA,cAAoE;IAC5CD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAH3CX,EAAA,CAAAC,cAAA,iBACgG;IAD1BD,EAAA,CAAAE,UAAA,mBAAA2D,0DAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAO,aAAA;MAASwD,OAAA,CAAAC,MAAA,EAAQ;MAAA,OAAAhE,EAAA,CAAAQ,WAAA,CAAAuD,OAAA,CAAAE,gBAAA,GAAqB,IAAI;IAAA,EAAC;IAE/GjE,EAAA,CAAAiC,UAAA,IAAAiC,uCAAA,iBAEM;IACNlE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAACX,EAAA,CAAAU,MAAA,eAClC;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IALTX,EAAA,CAAAiB,UAAA,aAAAkD,MAAA,CAAAC,UAAA,CAAAC,IAAA,CAAAC,MAAA,UAAAH,MAAA,CAAAI,gBAAA,CAAAC,OAAA,IAAAL,MAAA,CAAAP,iBAAA,CAA+F;IACvF5D,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,SAAAkD,MAAA,CAAAP,iBAAA,CAAuB;;;;;;IAM/B5D,EAAA,CAAAC,cAAA,iBAAgJ;IAAhED,EAAA,CAAAE,UAAA,mBAAAuE,0DAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmE,OAAA,CAAAlE,KAAA,EAAO;IAAA,EAAC;IAC/FT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC1BX,EAAA,CAAAU,MAAA,cACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAGTX,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,0BACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IA8BQX,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAFqCX,EAAA,CAAAiB,UAAA,UAAA2D,OAAA,CAAa;IAC7D5E,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAD,OAAA,MACF;;;;;IASA5E,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAF0CX,EAAA,CAAAiB,UAAA,UAAA6D,OAAA,CAAa;IAClE9E,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAC,OAAA,MACF;;;;;IAWA9E,EAAA,CAAAC,cAAA,qBAA8D;IACtDD,EAAA,CAAAU,MAAA,GAAS;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADwBX,EAAA,CAAAiB,UAAA,UAAA8D,OAAA,CAAa;IACrD/E,EAAA,CAAAmB,SAAA,GAAS;IAATnB,EAAA,CAAAoB,iBAAA,CAAA2D,OAAA,CAAS;;;;;IAajB/E,EAAA,CAAAC,cAAA,qBAAiE;IACzDD,EAAA,CAAAU,MAAA,GAAS;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAD2BX,EAAA,CAAAiB,UAAA,UAAA+D,OAAA,CAAa;IACxDhF,EAAA,CAAAmB,SAAA,GAAS;IAATnB,EAAA,CAAAoB,iBAAA,CAAA4D,OAAA,CAAS;;;;;;;;;;;IAwBfhF,EAAA,CAAAC,cAAA,oBAGkE;IADhED,EAAA,CAAAE,UAAA,mBAAA+E,mFAAApE,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAA8E,IAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6E,OAAA,CAAAC,QAAA,CAAAH,OAAA,EAAAtE,MAAA,EAAsB,qBAAqB,EAAG,MAAM,CAAC;IAAA,EAAC;IAE/Db,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAFTX,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,sBAAA,CAAAC,QAAA,CAAAR,OAAA,GAA+D;;;;;;IAGjEnF,EAAA,CAAAC,cAAA,oBAEiE;IAA/DD,EAAA,CAAAE,UAAA,mBAAA0F,mFAAA/E,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAyF,IAAA;MAAA,MAAAV,OAAA,GAAAnF,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAU,OAAA,GAAA9F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsF,OAAA,CAAAC,SAAA,CAAAZ,OAAA,EAAAtE,MAAA,EAAuB,qBAAqB,EAAC,MAAM,CAAC;IAAA,EAAC;IAC9Db,EAAA,CAAAU,MAAA,+BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;;;;;;;;;;;IAZtCX,EAAA,CAAAC,cAAA,qBACgH;IACnCD,EAAA,CAAAU,MAAA,GAAqB;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvGX,EAAA,CAAAiC,UAAA,IAAA+D,wDAAA,uBAKW;IACXhG,EAAA,CAAAiC,UAAA,IAAAgE,wDAAA,uBAGoC;IACtCjG,EAAA,CAAAW,YAAA,EAAa;;;;;IAbiDX,EAAA,CAAAiB,UAAA,UAAAkE,OAAA,CAAa,aAAAe,OAAA,CAAAR,sBAAA,CAAAC,QAAA,CAAAR,OAAA,cAAAnF,EAAA,CAAAuF,eAAA,IAAAY,GAAA,EAAAD,OAAA,CAAAE,iBAAA,CAAAT,QAAA,CAAAR,OAAA,KAAAe,OAAA,CAAAR,sBAAA,CAAAC,QAAA,CAAAR,OAAA;IAEnEnF,EAAA,CAAAmB,SAAA,GAAoE;IAApEnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAAc,GAAA,EAAAH,OAAA,CAAAR,sBAAA,CAAAC,QAAA,CAAAR,OAAA,GAAoE;IAACnF,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAA8D,OAAA,EAAqB;IACrFnF,EAAA,CAAAmB,SAAA,GAAwF;IAAxFnB,EAAA,CAAAiB,UAAA,SAAAiF,OAAA,CAAAE,iBAAA,CAAAT,QAAA,CAAAR,OAAA,MAAAe,OAAA,CAAAR,sBAAA,CAAAC,QAAA,CAAAR,OAAA,EAAwF;IAMxFnF,EAAA,CAAAmB,SAAA,GAA+C;IAA/CnB,EAAA,CAAAiB,UAAA,SAAAiF,OAAA,CAAAR,sBAAA,CAAAC,QAAA,CAAAR,OAAA,EAA+C;;;;;;IA2B1DnF,EAAA,CAAAC,cAAA,oBAGkE;IADhED,EAAA,CAAAE,UAAA,mBAAAoG,mFAAAzF,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAmG,IAAA;MAAA,MAAAC,OAAA,GAAAxG,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAqB,OAAA,GAAAzG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiG,OAAA,CAAAnB,QAAA,CAAAkB,OAAA,EAAA3F,MAAA,EAAsB,YAAY,EAAE,MAAM,CAAC;IAAA,EAAC;IAErDb,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAFTX,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,IAAAC,GAAA,EAAAkB,OAAA,CAAAC,sBAAA,CAAAhB,QAAA,CAAAa,OAAA,GAA+D;;;;;;IAGjExG,EAAA,CAAAC,cAAA,oBAEwD;IAAtDD,EAAA,CAAAE,UAAA,mBAAA0G,mFAAA/F,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAL,OAAA,GAAAxG,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAA0B,OAAA,GAAA9G,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsG,OAAA,CAAAf,SAAA,CAAAS,OAAA,EAAA3F,MAAA,EAAuB,YAAY,EAAC,MAAM,CAAC;IAAA,EAAC;IACrDb,EAAA,CAAAU,MAAA,+BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAZtCX,EAAA,CAAAC,cAAA,qBACgH;IACnCD,EAAA,CAAAU,MAAA,GAAqB;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvGX,EAAA,CAAAiC,UAAA,IAAA8E,wDAAA,uBAKW;IACX/G,EAAA,CAAAiC,UAAA,IAAA+E,wDAAA,uBAGoC;IACtChH,EAAA,CAAAW,YAAA,EAAa;;;;;IAb+CX,EAAA,CAAAiB,UAAA,UAAAuF,OAAA,CAAa,aAAAS,OAAA,CAAAN,sBAAA,CAAAhB,QAAA,CAAAa,OAAA,cAAAxG,EAAA,CAAAuF,eAAA,IAAAY,GAAA,EAAAc,OAAA,CAAAC,iBAAA,CAAAvB,QAAA,CAAAa,OAAA,KAAAS,OAAA,CAAAN,sBAAA,CAAAhB,QAAA,CAAAa,OAAA;IAEjExG,EAAA,CAAAmB,SAAA,GAAoE;IAApEnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAAc,GAAA,EAAAY,OAAA,CAAAN,sBAAA,CAAAhB,QAAA,CAAAa,OAAA,GAAoE;IAACxG,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAmF,OAAA,EAAqB;IACrFxG,EAAA,CAAAmB,SAAA,GAAwF;IAAxFnB,EAAA,CAAAiB,UAAA,SAAAgG,OAAA,CAAAC,iBAAA,CAAAvB,QAAA,CAAAa,OAAA,MAAAS,OAAA,CAAAN,sBAAA,CAAAhB,QAAA,CAAAa,OAAA,EAAwF;IAMxFxG,EAAA,CAAAmB,SAAA,GAA+C;IAA/CnB,EAAA,CAAAiB,UAAA,SAAAgG,OAAA,CAAAN,sBAAA,CAAAhB,QAAA,CAAAa,OAAA,EAA+C;;;;;;IAyB1DxG,EAAA,CAAAC,cAAA,oBAGuE;IADnED,EAAA,CAAAE,UAAA,mBAAAiH,kGAAAtG,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAgH,KAAA;MAAA,MAAAC,QAAA,GAAArH,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAkC,SAAA,GAAAtH,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAmC,QAAA,GAAAvH,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+G,QAAA,CAAAjC,QAAA,CAAA+B,QAAA,EAAAxG,MAAA,EAAuB,UAAU,EAAAyG,SAAA,CAAS;IAAA,EAAC;IAEpDtH,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAFTX,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,IAAAC,GAAA,EAAAgC,QAAA,CAAAC,wBAAA,CAAA9B,QAAA,CAAA0B,QAAA,GAAkE;;;;;;IAGpErH,EAAA,CAAAC,cAAA,oBAC0G;IAApDD,EAAA,CAAAE,UAAA,mBAAAwH,kGAAA7G,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuH,KAAA;MAAA,MAAAN,QAAA,GAAArH,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAkC,SAAA,GAAAtH,EAAA,CAAAO,aAAA,GAAA6E,SAAA;MAAA,MAAAwC,QAAA,GAAA5H,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoH,QAAA,CAAA7B,SAAA,CAAAsB,QAAA,EAAAxG,MAAA,EAAwB,UAAU,EAAAyG,SAAA,CAAO;IAAA,EAAC;IACvGtH,EAAA,CAAAU,MAAA,gCACF;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAZbX,EAAA,CAAAC,cAAA,qBACgH;IACzBD,EAAA,CAAAU,MAAA,GAAsB;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACpHX,EAAA,CAAAiC,UAAA,IAAA4F,uEAAA,uBAKa;IACX7H,EAAA,CAAAiC,UAAA,IAAA6F,uEAAA,uBAGW;IACf9H,EAAA,CAAAW,YAAA,EAAa;;;;;;IAbsCX,EAAA,CAAAiB,UAAA,UAAAoG,QAAA,CAAc,aAAAU,OAAA,CAAAC,gBAAA,CAAAX,QAAA,EAAAC,SAAA,cAAAtH,EAAA,CAAAuF,eAAA,IAAAY,GAAA,EAAA4B,OAAA,CAAAE,qBAAA,CAAAZ,QAAA,EAAAC,SAAA,KAAAS,OAAA,CAAAG,mBAAA,CAAAvC,QAAA,CAAA0B,QAAA;IAEzDrH,EAAA,CAAAmB,SAAA,GAAgF;IAAhFnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAAc,GAAA,EAAA0B,OAAA,CAAAC,gBAAA,CAAAX,QAAA,EAAAC,SAAA,KAAAA,SAAA,CAAAa,QAAA,EAAgF;IAACnI,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAgG,QAAA,EAAsB;IAClGrH,EAAA,CAAAmB,SAAA,GAAmJ;IAAnJnB,EAAA,CAAAiB,UAAA,UAAA8G,OAAA,CAAApB,sBAAA,CAAAhB,QAAA,CAAA2B,SAAA,CAAAc,uBAAA,KAAAL,OAAA,CAAAG,mBAAA,CAAAvC,QAAA,CAAA0B,QAAA,MAAAU,OAAA,CAAAC,gBAAA,CAAAX,QAAA,EAAAC,SAAA,EAAmJ;IAMjJtH,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAiB,UAAA,SAAA8G,OAAA,CAAAC,gBAAA,CAAAX,QAAA,EAAAC,SAAA,MAAAA,SAAA,CAAAa,QAAA,CAAuD;;;;;IAdtEnI,EAAA,CAAAC,cAAA,wBAC4E;IAI1ED,EAAA,CAAAiC,UAAA,IAAAoG,4DAAA,0BAaW;IAEbrI,EAAA,CAAAW,YAAA,EAAe;;;;IAnBbX,EAAA,CAAAiB,UAAA,UAAAqG,SAAA,CAAAgB,eAAA,CAAAC,KAAA,SAA6C,aAAAjB,SAAA,CAAAa,QAAA;IAIhBnI,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAiB,UAAA,YAAAqG,SAAA,CAAAkB,SAAA,CAAkB;;;;;IAkBrDxI,EAAA,CAAAC,cAAA,qBAAuD;IACrDD,EAAA,CAAAU,MAAA,uDACF;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAiDVX,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAU,MAAA,oCACF;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAURX,EAAA,CAAAC,cAAA,qBAAsD;IAAAD,EAAA,CAAAU,MAAA,GAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAApCX,EAAA,CAAAiB,UAAA,UAAAwH,SAAA,CAAc;IAACzI,EAAA,CAAAmB,SAAA,GAAQ;IAARnB,EAAA,CAAAoB,iBAAA,CAAAqH,SAAA,CAAQ;;;;;IAMhEzI,EAAA,CAAAC,cAAA,gBAA8E;IAC5ED,EAAA,CAAAU,MAAA,sCACF;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAQlBX,EAAA,CAAAC,cAAA,UAA4B;IAEjBD,EAAA,CAAAU,MAAA,kCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC1CX,EAAA,CAAAC,cAAA,2BAAgD;IAChBD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAmB;IACpDX,EAAA,CAAAC,cAAA,4BAA6B;IAAAD,EAAA,CAAAU,MAAA,SAAE;IAAAV,EAAA,CAAAW,YAAA,EAAmB;;;;;;;;;;IAwBhDX,EAAA,CAAAC,cAAA,sBAAsO;IAC9ND,EAAA,CAAAU,MAAA,GAAsB;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAD2BX,EAAA,CAAAiB,UAAA,UAAAyH,SAAA,CAAc,YAAA1I,EAAA,CAAAuF,eAAA,IAAAoD,GAAA,EAAAC,OAAA,CAAAC,OAAA,CAAAlD,QAAA,CAAA+C,SAAA,gBAAAE,OAAA,CAAAE,SAAA,IAAAF,OAAA,CAAAG,mBAAA,CAAAjG,KAAA,CAAAkG,cAAA,IAAAN,SAAA;IACtE1I,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAqH,SAAA,EAAsB;;;;;IAShC1I,EAAA,CAAAC,cAAA,kBAAiG;IAC/FD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAFkCX,EAAA,CAAAiB,UAAA,UAAAgI,SAAA,CAAc,cAAAC,OAAA,CAAAC,kBAAA,CAAAF,SAAA;IACvDjJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAoE,SAAA,MACF;;;;;;IAIJjJ,EAAA,CAAAC,cAAA,cAAoG;IACvED,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC1CX,EAAA,CAAAC,cAAA,iBACwH;IAD3BD,EAAA,CAAAE,UAAA,mBAAAkJ,+DAAA;MAAApJ,EAAA,CAAAI,aAAA,CAAAiJ,KAAA;MAAA,MAAAC,QAAA,GAAAtJ,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8I,QAAA,CAAAC,gBAAA,CAAiB,cAAc,CAAC;IAAA,EAAC,sBAAAC,kEAAA;MAAAxJ,EAAA,CAAAI,aAAA,CAAAiJ,KAAA;MAAA,MAAAI,QAAA,GAAAzJ,EAAA,CAAAO,aAAA;MAAA,OAC3HP,EAAA,CAAAQ,WAAA,CAAAiJ,QAAA,CAAAC,mBAAA,CAAoB,cAAc,CAAC;IAAA,EADwF,mBAAAC,+DAAA;MAAA3J,EAAA,CAAAI,aAAA,CAAAiJ,KAAA;MAAA,MAAAO,QAAA,GAAA5J,EAAA,CAAAO,aAAA;MAAA,OAC3EP,EAAA,CAAAQ,WAAA,CAAAoJ,QAAA,CAAAC,mBAAA,EAAqB;IAAA,EADsD;IAAvI7J,EAAA,CAAAW,YAAA,EACwH;;;;;IAexHX,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAU,MAAA,0CACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IAQNX,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,MAAA,oCACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IA0BNX,EAAA,CAAAC,cAAA,2BAAuD;IAACD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAChFX,EAAA,CAAAC,cAAA,oBACsB;IAAAD,EAAA,CAAAU,MAAA,GAAO;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAAlBX,EAAA,CAAAmB,SAAA,GAAO;IAAPnB,EAAA,CAAAoB,iBAAA,CAAA0I,MAAA,KAAO;;;;;IAC7B9J,EAAA,CAAA+J,SAAA,2BACkB;;;;;IAIlB/J,EAAA,CAAAC,cAAA,2BAA0D;IAACD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;;IACpFX,EAAA,CAAAC,cAAA,oBAA2D;IACjDD,EAAA,CAAAE,UAAA,mBAAA8J,2EAAA;MAAA,MAAAC,WAAA,GAAAjK,EAAA,CAAAI,aAAA,CAAA8J,KAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAA7E,SAAA;MAAA,MAAAgF,QAAA,GAAApK,EAAA,CAAAO,aAAA;MAAA,MAAA8J,IAAA,GAAArK,EAAA,CAAAsK,WAAA;MAAA,OAAStK,EAAA,CAAAQ,WAAA,CAAA4J,QAAA,CAAAG,OAAA,CAAAJ,YAAA,EAAAE,IAAA,CAAuB;IAAA,EAAC;IACrBrK,EAAA,CAAAC,cAAA,oBAAuB;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC1DX,EAAA,CAAAC,cAAA,kBACsB;IADdD,EAAA,CAAAE,UAAA,mBAAAsK,2EAAA;MAAA,MAAAP,WAAA,GAAAjK,EAAA,CAAAI,aAAA,CAAA8J,KAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAA7E,SAAA;MAAA,MAAAqF,QAAA,GAAAzK,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiK,QAAA,CAAAC,SAAA,CAAAP,YAAA,CAAkB;IAAA,EAAC;IACdnK,EAAA,CAAAC,cAAA,oBAAuB;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAEhEX,EAAA,CAAA+J,SAAA,2BAA6E;;;;;IAI7E/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAEjFX,EAAA,CAAAC,cAAA,cAA6E;IAC9CD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAACX,EAAA,CAAAU,MAAA,4BACjD;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IACNX,EAAA,CAAAC,cAAA,cAA4E;IAC9CD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAACX,EAAA,CAAAU,MAAA,sBACtD;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IACNX,EAAA,CAAAC,cAAA,cACoC;IACND,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAACX,EAAA,CAAAU,MAAA,sBACtD;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IAVRX,EAAA,CAAAC,cAAA,oBAA8E;IAC5ED,EAAA,CAAAiC,UAAA,IAAA0I,yDAAA,mBAEM;IACN3K,EAAA,CAAAiC,UAAA,IAAA2I,yDAAA,mBAEM;IACN5K,EAAA,CAAAiC,UAAA,IAAA4I,yDAAA,mBAGM;IACR7K,EAAA,CAAAW,YAAA,EAAW;;;;IAVHX,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAiB,UAAA,SAAA6J,YAAA,CAAAC,YAAA,UAAmC;IAGnC/K,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAiB,UAAA,SAAA6J,YAAA,CAAAC,YAAA,SAAkC;IAGlC/K,EAAA,CAAAmB,SAAA,GAAmE;IAAnEnB,EAAA,CAAAiB,UAAA,SAAA6J,YAAA,CAAAC,YAAA,YAAAD,YAAA,CAAAC,YAAA,UAAmE;;;;;IAK3E/K,EAAA,CAAA+J,SAAA,2BAA4E;;;;;IAI5E/J,EAAA,CAAAC,cAAA,2BAAuD;IAACD,EAAA,CAAAU,MAAA,iBAAS;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAEjFX,EAAA,CAAAC,cAAA,UAAuC;IACXD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAEjDX,EAAA,CAAAC,cAAA,UAAiE;IAC/DD,EAAA,CAAAU,MAAA,UACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IANRX,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAiC,UAAA,IAAA+I,yDAAA,kBAEM;IACNhL,EAAA,CAAAiC,UAAA,IAAAgJ,yDAAA,kBAEM;IACRjL,EAAA,CAAAW,YAAA,EAAW;;;;IANHX,EAAA,CAAAmB,SAAA,GAA+B;IAA/BnB,EAAA,CAAAiB,UAAA,SAAAiK,YAAA,CAAAC,QAAA,UAA+B;IAG/BnL,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAiB,UAAA,SAAAiK,YAAA,CAAAC,QAAA,YAAAD,YAAA,CAAAC,QAAA,QAAyD;;;;;IAIjEnL,EAAA,CAAA+J,SAAA,2BAAgG;;;;;IAIhG/J,EAAA,CAAAC,cAAA,2BAA8F;IAACD,EAAA,CAAAU,MAAA,wBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAM/HX,EAAA,CAAAC,cAAA,oBAA4E;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IACnGX,EAAA,CAAAC,cAAA,oBAA6G;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;;IANtIX,EAAA,CAAAC,cAAA,oBAA6F;IAClDD,EAAA,CAAAE,UAAA,mBAAAkL,yEAAA;MAAA,MAAAnB,WAAA,GAAAjK,EAAA,CAAAI,aAAA,CAAAiL,KAAA;MAAA,MAAAC,YAAA,GAAArB,WAAA,CAAA7E,SAAA;MAAA,MAAAmG,QAAA,GAAAvL,EAAA,CAAAO,aAAA;MAAA,MAAA8J,IAAA,GAAArK,EAAA,CAAAsK,WAAA;MAAA,OAAStK,EAAA,CAAAQ,WAAA,CAAA+K,QAAA,CAAAhB,OAAA,CAAAe,YAAA,EAAAjB,IAAA,CAAuB;IAAA,EAAC;IAExErK,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAiC,UAAA,IAAAuJ,8DAAA,wBAAmG;IACnGxL,EAAA,CAAAiC,UAAA,IAAAwJ,8DAAA,wBAAoI;IACpIzL,EAAA,CAAAC,cAAA,kBACsB;IADdD,EAAA,CAAAE,UAAA,mBAAAwL,4EAAA;MAAA,MAAAzB,WAAA,GAAAjK,EAAA,CAAAI,aAAA,CAAAiL,KAAA;MAAA,MAAAC,YAAA,GAAArB,WAAA,CAAA7E,SAAA;MAAA,MAAAuG,QAAA,GAAA3L,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmL,QAAA,CAAAjB,SAAA,CAAAY,YAAA,CAAkB;IAAA,EAAC;IACdtL,EAAA,CAAAC,cAAA,oBAAuB;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAN9DX,EAAA,CAAAmB,SAAA,GAAsE;IAAtEnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,IAAAoD,GAAA,EAAAiD,QAAA,CAAA/C,OAAA,CAAAlD,QAAA,CAAA2F,YAAA,CAAAtC,cAAA,GAAsE;IACpEhJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAyG,YAAA,CAAAtC,cAAA,MACF;IACWhJ,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAiB,UAAA,SAAAqK,YAAA,CAAAP,YAAA,SAAkC;IAClC/K,EAAA,CAAAmB,SAAA,GAAmE;IAAnEnB,EAAA,CAAAiB,UAAA,SAAAqK,YAAA,CAAAP,YAAA,YAAAO,YAAA,CAAAP,YAAA,UAAmE;;;;;IAIhF/K,EAAA,CAAAC,cAAA,2BAA8F;IAACD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAIvHX,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,wBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC5FX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAAtCX,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA6E,kBAAA,MAAAgH,YAAA,CAAAC,cAAA,MAA2B;;;;;IACpF9L,EAAA,CAAA+J,SAAA,2BAA4E;;;;;IAI5E/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC3FX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAArCX,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAA6E,kBAAA,MAAAkH,YAAA,CAAAC,aAAA,MAA0B;;;;;IACnFhM,EAAA,CAAA+J,SAAA,2BAA4E;;;;;IAI5E/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC3FX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAArCX,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAA6E,kBAAA,MAAAoH,YAAA,CAAAC,aAAA,MAA0B;;;;;IACnFlM,EAAA,CAAA+J,SAAA,2BAA4E;;;;;IAI5E/J,EAAA,CAAAC,cAAA,2BAAwE;IAACD,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC/FX,EAAA,CAAAC,cAAA,oBAAyE;IAACD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAA3BX,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAA6E,kBAAA,MAAAsH,YAAA,CAAAC,GAAA,MAAgB;;;;;IAC1FpM,EAAA,CAAA+J,SAAA,2BAA2F;;;;;IAI3F/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC3FX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAA3CX,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAA6E,kBAAA,MAAAwH,YAAA,CAAAC,aAAA,WAAgC;;;;;IACzFtM,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,GAC1D;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;IADwCX,EAAA,CAAAmB,SAAA,GAC1D;IAD0DnB,EAAA,CAAA6E,kBAAA,MAAA0H,QAAA,CAAAC,QAAA,uBAC1D;;;;;IAIAxM,EAAA,CAAAC,cAAA,2BAAwE;IAACD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IACjGX,EAAA,CAAAC,cAAA,oBAAyE;IAACD,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAAnCX,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAA6E,kBAAA,MAAA4H,YAAA,CAAAC,KAAA,WAAwB;;;;;IAClG1M,EAAA,CAAA+J,SAAA,2BAA2F;;;;;IAI3F/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IACjFX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;IAAlCX,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAA6E,kBAAA,MAAA8H,YAAA,CAAAC,IAAA,WAAuB;;;;;IAChF5M,EAAA,CAAA+J,SAAA,2BAA4E;;;;;IAI5E/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC1FX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAAxEX,EAAA,CAAAmB,SAAA,GAA6D;IAA7DnB,EAAA,CAAA6E,kBAAA,MAAAgI,QAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAC,YAAA,CAAAC,WAAA,YAA6D;;;;;IACtHjN,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;IAAhDX,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAA6E,kBAAA,MAAAqI,QAAA,CAAAV,QAAA,qBAA8B;;;;;IAIxFxM,EAAA,CAAAC,cAAA,2BAAyE;IAACD,EAAA,CAAAU,MAAA,yBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC7GX,EAAA,CAAAC,cAAA,oBAA0E;IAACD,EAAA,CAAAU,MAAA,GAAqD;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAAhEX,EAAA,CAAAmB,SAAA,GAAqD;IAArDnB,EAAA,CAAA6E,kBAAA,MAAAsI,QAAA,CAAAL,MAAA,CAAAC,gBAAA,CAAAK,YAAA,CAAAC,IAAA,YAAqD;;;;;IAChIrN,EAAA,CAAA+J,SAAA,2BAA4F;;;;;IAI5F/J,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;;IACtFX,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAU,MAAA,GAA4D;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAAvEX,EAAA,CAAAmB,SAAA,GAA4D;IAA5DnB,EAAA,CAAA6E,kBAAA,MAAAyI,QAAA,CAAAR,MAAA,CAAAC,gBAAA,CAAAQ,YAAA,CAAAC,SAAA,eAA4D;;;;;IACrHxN,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAU,MAAA,GAC1D;IAAAV,EAAA,CAAAW,YAAA,EAAkB;;;;IADwCX,EAAA,CAAAmB,SAAA,GAC1D;IAD0DnB,EAAA,CAAA6E,kBAAA,MAAA4I,QAAA,CAAAX,MAAA,CAAAC,gBAAA,CAAAU,QAAA,CAAAC,WAAA,uBAC1D;;;;;IAGF1N,EAAA,CAAA+J,SAAA,qBAAqE;;;;;;;;;;IACrE/J,EAAA,CAAA+J,SAAA,kBACwE;;;;IAAtE/J,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,IAAAoI,IAAA,EAAAC,QAAA,CAAA7C,YAAA,YAA2D;;;;;IAC7D/K,EAAA,CAAA+J,SAAA,qBAAqE;;;;;;;;IAnI3E/J,EAAA,CAAAC,cAAA,eAA6C;IAEvCD,EAAA,CAAA6N,uBAAA,QAAiC;IAC/B7N,EAAA,CAAAiC,UAAA,IAAA6L,yDAAA,+BAAgF;IAChF9N,EAAA,CAAAiC,UAAA,IAAA8L,kDAAA,wBACwC;IACxC/N,EAAA,CAAAiC,UAAA,IAAA+L,yDAAA,+BACkB;IACpBhO,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,QAAoC;IAClC7N,EAAA,CAAAiC,UAAA,IAAAiM,yDAAA,+BAAoF;IACpFlO,EAAA,CAAAiC,UAAA,IAAAkM,kDAAA,wBAKW;IACXnO,EAAA,CAAAiC,UAAA,IAAAmM,yDAAA,+BAA6E;IAC/EpO,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA0C;IACxC7N,EAAA,CAAAiC,UAAA,KAAAoM,0DAAA,+BAAmF;IACnFrO,EAAA,CAAAiC,UAAA,KAAAqM,mDAAA,wBAWW;IACXtO,EAAA,CAAAiC,UAAA,KAAAsM,0DAAA,+BAA4E;IAC9EvO,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAsC;IACpC7N,EAAA,CAAAiC,UAAA,KAAAuM,0DAAA,+BAAmF;IACnFxO,EAAA,CAAAiC,UAAA,KAAAwM,mDAAA,wBAOW;IACXzO,EAAA,CAAAiC,UAAA,KAAAyM,0DAAA,+BAAgG;IAClG1O,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA4C;IAC1C7N,EAAA,CAAAiC,UAAA,KAAA0M,0DAAA,+BAAiI;IACjI3O,EAAA,CAAAiC,UAAA,KAAA2M,mDAAA,wBASW;IACX5O,EAAA,CAAAiC,UAAA,KAAA4M,0DAAA,+BAAuH;IACzH7O,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA4C;IAC1C7N,EAAA,CAAAiC,UAAA,KAAA6M,0DAAA,+BAA4F;IAC5F9O,EAAA,CAAAiC,UAAA,KAAA8M,mDAAA,wBAA+F;IAC/F/O,EAAA,CAAAiC,UAAA,KAAA+M,0DAAA,+BAA4E;IAC9EhP,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA2C;IACzC7N,EAAA,CAAAiC,UAAA,KAAAgN,0DAAA,+BAA2F;IAC3FjP,EAAA,CAAAiC,UAAA,KAAAiN,mDAAA,wBAA8F;IAC9FlP,EAAA,CAAAiC,UAAA,KAAAkN,0DAAA,+BAA4E;IAC9EnP,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA2C;IACzC7N,EAAA,CAAAiC,UAAA,KAAAmN,0DAAA,+BAA2F;IAC3FpP,EAAA,CAAAiC,UAAA,KAAAoN,mDAAA,wBAA8F;IAC9FrP,EAAA,CAAAiC,UAAA,KAAAqN,0DAAA,+BAA4E;IAC9EtP,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAiC;IAC/B7N,EAAA,CAAAiC,UAAA,KAAAsN,0DAAA,+BAA+F;IAC/FvP,EAAA,CAAAiC,UAAA,KAAAuN,mDAAA,wBAAqG;IACrGxP,EAAA,CAAAiC,UAAA,KAAAwN,0DAAA,+BAA2F;IAC7FzP,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAA2C;IACzC7N,EAAA,CAAAiC,UAAA,KAAAyN,0DAAA,+BAA2F;IAC3F1P,EAAA,CAAAiC,UAAA,KAAA0N,mDAAA,wBAAoG;IACpG3P,EAAA,CAAAiC,UAAA,KAAA2N,0DAAA,+BACkB;IACpB5P,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAmC;IACjC7N,EAAA,CAAAiC,UAAA,KAAA4N,0DAAA,+BAAiG;IACjG7P,EAAA,CAAAiC,UAAA,KAAA6N,mDAAA,wBAA6G;IAC7G9P,EAAA,CAAAiC,UAAA,KAAA8N,0DAAA,+BAA2F;IAC7F/P,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAkC;IAChC7N,EAAA,CAAAiC,UAAA,KAAA+N,0DAAA,+BAAiF;IACjFhQ,EAAA,CAAAiC,UAAA,KAAAgO,mDAAA,wBAA2F;IAC3FjQ,EAAA,CAAAiC,UAAA,KAAAiO,0DAAA,+BAA4E;IAC9ElQ,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAyC;IACvC7N,EAAA,CAAAiC,UAAA,KAAAkO,0DAAA,+BAA0F;IAC1FnQ,EAAA,CAAAiC,UAAA,KAAAmO,mDAAA,wBAAiI;IACjIpQ,EAAA,CAAAiC,UAAA,KAAAoO,0DAAA,+BAA0G;IAC5GrQ,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAkC;IAChC7N,EAAA,CAAAiC,UAAA,KAAAqO,0DAAA,+BAA6G;IAC7GtQ,EAAA,CAAAiC,UAAA,KAAAsO,mDAAA,wBAA2I;IAC3IvQ,EAAA,CAAAiC,UAAA,KAAAuO,0DAAA,+BAA4F;IAC9FxQ,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAA6N,uBAAA,SAAuC;IACrC7N,EAAA,CAAAiC,UAAA,KAAAwO,0DAAA,+BAAsF;IACtFzQ,EAAA,CAAAiC,UAAA,KAAAyO,mDAAA,wBAAgI;IAChI1Q,EAAA,CAAAiC,UAAA,KAAA0O,0DAAA,+BACkB;IACpB3Q,EAAA,CAAAiO,qBAAA,EAAe;IAEfjO,EAAA,CAAAiC,UAAA,KAAA2O,yDAAA,8BAAqE;IACrE5Q,EAAA,CAAAiC,UAAA,KAAA4O,kDAAA,uBACwE;IACxE7Q,EAAA,CAAAiC,UAAA,KAAA6O,yDAAA,8BAAqE;IACvE9Q,EAAA,CAAAW,YAAA,EAAY;IACZX,EAAA,CAAA+J,SAAA,0BAAqH;IACzH/J,EAAA,CAAAW,YAAA,EAAM;;;;IArISX,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAiB,UAAA,eAAA8P,OAAA,CAAA3M,UAAA,CAAyB;IA+HjBpE,EAAA,CAAAmB,SAAA,IAAiC;IAAjCnB,EAAA,CAAAiB,UAAA,oBAAA8P,OAAA,CAAAC,gBAAA,CAAiC;IACpBhR,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,qBAAA8P,OAAA,CAAAC,gBAAA,CAA0B;IAEvChR,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAiB,UAAA,oBAAA8P,OAAA,CAAAC,gBAAA,CAAiC;IAERhR,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAiB,UAAA,gBAAe,oBAAAjB,EAAA,CAAAiR,eAAA,IAAAC,IAAA;;;;;;;;;;;IAE/DlR,EAAA,CAAAC,cAAA,UAA6B;IAC3BD,EAAA,CAAA+J,SAAA,+BAC6E;IAC/E/J,EAAA,CAAAW,YAAA,EAAM;;;IADFX,EAAA,CAAAmB,SAAA,GAAoD;IAApDnB,EAAA,CAAAiB,UAAA,UAAAjB,EAAA,CAAAiR,eAAA,IAAAE,IAAA,EAAoD;;;;;IAGtDnR,EAAA,CAAA+J,SAAA,2BAKmB;;;;;IAEnB/J,EAAA,CAAA+J,SAAA,2BAKmB;;;;;IAbrB/J,EAAA,CAAAC,cAAA,UAA2D;IACzDD,EAAA,CAAAiC,UAAA,IAAAmP,yDAAA,+BAKmB;IAEnBpR,EAAA,CAAAiC,UAAA,IAAAoP,yDAAA,+BAKmB;IACrBrR,EAAA,CAAAW,YAAA,EAAM;;;;IAZDX,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAiB,UAAA,SAAAqQ,OAAA,CAAAC,eAAA,UAAmC;IAOnCvR,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAiB,UAAA,SAAAqQ,OAAA,CAAAC,eAAA,SAAkC;;;;;;;;;;;;;;;;;IA9e7CvR,EAAA,CAAAC,cAAA,UAA8C;IAOrBD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACrCX,EAAA,CAAA+J,SAAA,gBACqE;IACrE/J,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAI1CX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACrCX,EAAA,CAAA+J,SAAA,iBAC6C;IAC7C/J,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAIlDX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC1BX,EAAA,CAAAC,cAAA,sBAAkC;IAChCD,EAAA,CAAAiC,UAAA,KAAAuP,6CAAA,yBAEa;IACfxR,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAClCX,EAAA,CAAAC,cAAA,sBAAyC;IACvCD,EAAA,CAAAiC,UAAA,KAAAwP,6CAAA,yBAEa;IACfzR,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,gBAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC/BX,EAAA,CAAAC,cAAA,iBAC4G;IAA/ED,EAAA,CAAAE,UAAA,yBAAAwR,8DAAA;MAAA1R,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAC,QAAA,GAAA5R,EAAA,CAAAO,aAAA;MAAA,OAAeP,EAAA,CAAAQ,WAAA,CAAAoR,QAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAD3D7R,EAAA,CAAAW,YAAA,EAC4G;IAC5GX,EAAA,CAAAC,cAAA,gCAA+F;IAApDD,EAAA,CAAAE,UAAA,4BAAA4R,4EAAAjR,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAI,QAAA,GAAA/R,EAAA,CAAAO,aAAA;MAAA,OAAkBP,EAAA,CAAAQ,WAAA,CAAAuR,QAAA,CAAAC,iBAAA,CAAAnR,MAAA,CAAAmB,MAAA,CAAgC;IAAA,EAAC;IAC5FhC,EAAA,CAAAiC,UAAA,KAAAgQ,6CAAA,yBAEa;;IACfjS,EAAA,CAAAW,YAAA,EAAmB;IAIvBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACnCX,EAAA,CAAAC,cAAA,iBAEkD;IADlBD,EAAA,CAAAE,UAAA,yBAAAgS,8DAAA;MAAAlS,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAQ,QAAA,GAAAnS,EAAA,CAAAO,aAAA;MAAA,OAAeP,EAAA,CAAAQ,WAAA,CAAA2R,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IADjEpS,EAAA,CAAAW,YAAA,EAEkD;IAClDX,EAAA,CAAAC,cAAA,gCAAkG;IAAvDD,EAAA,CAAAE,UAAA,4BAAAmS,4EAAAxR,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAW,QAAA,GAAAtS,EAAA,CAAAO,aAAA;MAAA,OAAkBP,EAAA,CAAAQ,WAAA,CAAA8R,QAAA,CAAAC,oBAAA,CAAA1R,MAAA,CAAAmB,MAAA,CAAmC;IAAA,EAAC;IAC/FhC,EAAA,CAAAiC,UAAA,KAAAuQ,6CAAA,yBAEa;;IACfxS,EAAA,CAAAW,YAAA,EAAmB;IAIvBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAClCX,EAAA,CAAAC,cAAA,sBAAkD;IAE9CD,EAAA,CAAA+J,SAAA,iCAC6D;IAC/D/J,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAAC,cAAA,sBAAsE;IAApCD,EAAA,CAAAE,UAAA,mBAAAuS,6DAAA;MAAAzS,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAe,QAAA,GAAA1S,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkS,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACnE3S,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3CX,EAAA,CAAAU,MAAA,mCACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;IAIbX,EAAA,CAAAiC,UAAA,KAAA2Q,6CAAA,0BAaa;;IACf5S,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACnCX,EAAA,CAAAC,cAAA,sBAAqG;IAA1DD,EAAA,CAAAE,UAAA,6BAAA2S,uEAAAhS,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAmB,QAAA,GAAA9S,EAAA,CAAAO,aAAA;MAAA,OAAmBP,EAAA,CAAAQ,WAAA,CAAAsS,QAAA,CAAAC,cAAA,CAAAlS,MAAA,CAAAiC,KAAA,CAA4B;IAAA,EAAC;IACzF9C,EAAA,CAAAC,cAAA,kBAAY;IACVD,EAAA,CAAA+J,SAAA,iCAC2D;IAC7D/J,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAAC,cAAA,sBAAwE;IAAtCD,EAAA,CAAAE,UAAA,mBAAA8S,6DAAA;MAAAhT,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAsB,QAAA,GAAAjT,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAyS,QAAA,CAAAC,yBAAA,EAA2B;IAAA,EAAC;IACrElT,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3CX,EAAA,CAAAU,MAAA,mCACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;IAIbX,EAAA,CAAAiC,UAAA,KAAAkR,6CAAA,0BAaa;;IACfnT,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,iBAAS;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAChCX,EAAA,CAAAC,cAAA,sBAAsD;IAElDD,EAAA,CAAA+J,SAAA,iCACmE;IACrE/J,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAAiC,UAAA,KAAAmR,+CAAA,2BAoBe;;IACjBpT,EAAA,CAAAW,YAAA,EAAa;IAEfX,EAAA,CAAAiC,UAAA,KAAAoR,4CAAA,wBAEY;IACdrT,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAAsB;IAEXD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC7BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA+C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIhEX,EAAA,CAAAC,cAAA,eAAsB;IAEXD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC3BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA4C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAI7DX,EAAA,CAAAC,cAAA,eAAsB;IAETD,EAAA,CAAAU,MAAA,yBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAChCX,EAAA,CAAAC,cAAA,gBAAiF;IACzED,EAAA,CAAAE,UAAA,mBAAAoT,wDAAA;MAAAtT,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA4B,QAAA,GAAAvT,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+S,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAAExT,EAAA,CAAAU,MAAA,KAAwE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAKzHX,EAAA,CAAAC,cAAA,gBAAsB;IAETD,EAAA,CAAAU,MAAA,4BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAClCX,EAAA,CAAAC,cAAA,gBAAiF;IACvED,EAAA,CAAAU,MAAA,KAAwH;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAK/IX,EAAA,CAAAC,cAAA,gBAAsB;IAEPD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAClCX,EAAA,CAAAC,cAAA,kBASC;IAJCD,EAAA,CAAAE,UAAA,mBAAAuT,yDAAA5S,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA+B,QAAA,GAAA1T,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkT,QAAA,CAAAC,UAAA,CAAA9S,MAAA,EAAmB,OAAO,CAAC;IAAA,EAAC,mBAAA+S,yDAAA;MAAA5T,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAkC,QAAA,GAAA7T,EAAA,CAAAO,aAAA;MAAA,OAE5BP,EAAA,CAAAQ,WAAA,CAAAqT,QAAA,CAAAC,aAAA,CAAc,OAAO,CAAC;IAAA,EAFM,sBAAAC,4DAAA;MAAA/T,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAqC,QAAA,GAAAhU,EAAA,CAAAO,aAAA;MAAA,OAGzBP,EAAA,CAAAQ,WAAA,CAAAwT,QAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAHA;IALvCjU,EAAA,CAAAW,YAAA,EASC;IACDX,EAAA,CAAAiC,UAAA,MAAAiS,6CAAA,wBAEY;IACdlU,EAAA,CAAAW,YAAA,EAAiB;IAInBX,EAAA,CAAAC,cAAA,gBAAsB;IAGLD,EAAA,CAAAU,MAAA,cAAK;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC5BX,EAAA,CAAAC,cAAA,uBAA4G;IAA/CD,EAAA,CAAAE,UAAA,6BAAAiU,wEAAAtT,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAyC,QAAA,GAAApU,EAAA,CAAAO,aAAA;MAAA,OAAmBP,EAAA,CAAAQ,WAAA,CAAA4T,QAAA,CAAAC,YAAA,CAAAxT,MAAA,CAAAiC,KAAA,CAA0B;IAAA,EAAC;IACzG9C,EAAA,CAAAiC,UAAA,MAAAqS,8CAAA,yBAA2E;IAC7EtU,EAAA,CAAAW,YAAA,EAAa;IAEfX,EAAA,CAAAC,cAAA,2BAA4D;IACkBD,EAAA,CAAAE,UAAA,mBAAAqU,yDAAA;MAAAvU,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA6C,QAAA,GAAAxU,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgU,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC,mBAAAC,yDAAA;MAAA1U,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAgD,QAAA,GAAA3U,EAAA,CAAAO,aAAA;MAAA,OACxEP,EAAA,CAAAQ,WAAA,CAAAmU,QAAA,CAAAb,aAAA,CAAc,SAAS,CAAC;IAAA,EADgD,sBAAAc,4DAAA;MAAA5U,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAkD,QAAA,GAAA7U,EAAA,CAAAO,aAAA;MAAA,OAClCP,EAAA,CAAAQ,WAAA,CAAAqU,QAAA,CAAAZ,gBAAA,CAAiB,SAAS,CAAC;IAAA,EADO;IAAtGjU,EAAA,CAAAW,YAAA,EACiG;IACjGX,EAAA,CAAAiC,UAAA,MAAA6S,6CAAA,wBAEY;IACZ9U,EAAA,CAAAC,cAAA,qBAC+B;IADXD,EAAA,CAAAE,UAAA,mBAAA6U,4DAAA;MAAA/U,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAqD,QAAA,GAAAhV,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwU,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAChBjV,EAAA,CAAAU,MAAA,aAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAMpDX,EAAA,CAAAiC,UAAA,MAAAiT,uCAAA,kBAQM;IACRlV,EAAA,CAAAW,YAAA,EAAM;IAIZX,EAAA,CAAAC,cAAA,YAAK;IAEDD,EAAA,CAAAU,MAAA,4BACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,gBAAyC;IAMDD,EAAA,CAAAU,MAAA,wBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACrDX,EAAA,CAAA+J,SAAA,kBACqF;IACnF/J,EAAA,CAAAC,cAAA,iCACmF;IAAnFD,EAAA,CAAAE,UAAA,4BAAAiV,6EAAA;MAAAnV,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAyD,QAAA,GAAApV,EAAA,CAAAO,aAAA;MAAA,OAAkBP,EAAA,CAAAQ,WAAA,CAAA4U,QAAA,CAAAC,qBAAA,CAAAD,QAAA,CAAArM,mBAAA,CAAAjG,KAAA,CAAAkG,cAAA,CAA+D;IAAA,EAAC;IAChFhJ,EAAA,CAAAiC,UAAA,MAAAqT,8CAAA,yBAEa;;IACftV,EAAA,CAAAW,YAAA,EAAmB;IAGvBX,EAAA,CAAAC,cAAA,gBAA0C;IACZD,EAAA,CAAAU,MAAA,YAAG;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACvCX,EAAA,CAAAC,cAAA,mBACyB;IAAvBD,EAAA,CAAAE,UAAA,oBAAAqV,2DAAA;MAAAvV,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA6D,QAAA,GAAAxV,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAgV,QAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACtBzV,EAAA,CAAAiC,UAAA,MAAAyT,0CAAA,qBAES;IACX1V,EAAA,CAAAW,YAAA,EAAS;IAGXX,EAAA,CAAAiC,UAAA,MAAA0T,uCAAA,kBAIM;IAEN3V,EAAA,CAAAC,cAAA,gBAA0C;IACZD,EAAA,CAAAU,MAAA,8BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACzDX,EAAA,CAAAC,cAAA,kBAEkG;IAFbD,EAAA,CAAAE,UAAA,mBAAA0V,yDAAA;MAAA5V,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAkE,QAAA,GAAA7V,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAqV,QAAA,CAAAtM,gBAAA,CAAiB,MAAM,CAAC;IAAA,EAAC,sBAAAuM,4DAAA;MAAA9V,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAoE,QAAA,GAAA/V,EAAA,CAAAO,aAAA;MAAA,OAC3GP,EAAA,CAAAQ,WAAA,CAAAuV,QAAA,CAAArM,mBAAA,CAAoB,MAAM,CAAC;IAAA,EADgF,mBAAAsM,yDAAAnV,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAsE,QAAA,GAAAjW,EAAA,CAAAO,aAAA;MAAA,OACnEP,EAAA,CAAAQ,WAAA,CAAAyV,QAAA,CAAAC,kBAAA,CAAArV,MAAA,CAA0B;IAAA,EADyC,mBAAA+U,yDAAA;MAAA5V,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAwE,QAAA,GAAAnW,EAAA,CAAAO,aAAA;MAAA,OAE3FP,EAAA,CAAAQ,WAAA,CAAA2V,QAAA,CAAArC,aAAA,CAAc,MAAM,CAAC;IAAA,EAFsE,sBAAAgC,4DAAA;MAAA9V,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAyE,QAAA,GAAApW,EAAA,CAAAO,aAAA;MAAA,OAExDP,EAAA,CAAAQ,WAAA,CAAA4V,QAAA,CAAAnC,gBAAA,CAAiB,MAAM,CAAC;IAAA,EAFgC;IAAvHjU,EAAA,CAAAW,YAAA,EAEkG;IAGpGX,EAAA,CAAAC,cAAA,gBAA0C;IACTD,EAAA,CAAAU,MAAA,sBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACpDX,EAAA,CAAAC,cAAA,kBAEkI;IAF/ED,EAAA,CAAAE,UAAA,mBAAAmW,yDAAA;MAAArW,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA2E,QAAA,GAAAtW,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8V,QAAA,CAAA/M,gBAAA,CAAiB,aAAa,CAAC;IAAA,EAAC,sBAAAgN,4DAAA;MAAAvW,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA6E,QAAA,GAAAxW,EAAA,CAAAO,aAAA;MAAA,OAChFP,EAAA,CAAAQ,WAAA,CAAAgW,QAAA,CAAA9M,mBAAA,CAAoB,aAAa,CAAC;IAAA,EAD8C,mBAAA+M,yDAAA5V,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA+E,QAAA,GAAA1W,EAAA,CAAAO,aAAA;MAAA,OACnCP,EAAA,CAAAQ,WAAA,CAAAkW,QAAA,CAAAR,kBAAA,CAAArV,MAAA,CAA0B;IAAA,EADS;IAA5Fb,EAAA,CAAAW,YAAA,EAEkI;IAClIX,EAAA,CAAAiC,UAAA,MAAA0U,uCAAA,kBAEM;IACR3W,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,gBAA0C;IAChBD,EAAA,CAAAU,MAAA,cAAK;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACrCX,EAAA,CAAAC,cAAA,kBAE+D;IAFlBD,EAAA,CAAAE,UAAA,mBAAA0W,yDAAA/V,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAkF,QAAA,GAAA7W,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAqW,QAAA,CAAAC,UAAA,CAAAjW,MAAA,EAAoB,OAAO,CAAC;IAAA,EAAC,mBAAAkW,yDAAA;MAAA/W,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAqF,QAAA,GAAAhX,EAAA,CAAAO,aAAA;MAAA,OACnCP,EAAA,CAAAQ,WAAA,CAAAwW,QAAA,CAAAzN,gBAAA,CAAiB,OAAO,CAAC;IAAA,EADU,sBAAA0N,4DAAA;MAAAjX,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAAuF,QAAA,GAAAlX,EAAA,CAAAO,aAAA;MAAA,OAEvEP,EAAA,CAAAQ,WAAA,CAAA0W,QAAA,CAAAxN,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAF2C;IAAnF1J,EAAA,CAAAW,YAAA,EAE+D;IAC/DX,EAAA,CAAAiC,UAAA,MAAAkV,uCAAA,kBAEM;IACRnX,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,gBACgC;IAE5BD,EAAA,CAAAE,UAAA,mBAAAkX,0DAAA;MAAApX,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA0F,QAAA,GAAArX,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6W,QAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IACjCtX,EAAA,CAAAC,cAAA,cAAuC;IAAAD,EAAA,CAAAU,MAAA,YAAG;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAACX,EAAA,CAAAU,MAAA,cACjD;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAUnBX,EAAA,CAAAC,cAAA,YAAK;IACeD,EAAA,CAAAE,UAAA,2BAAAqX,4EAAA1W,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA6F,QAAA,GAAAxX,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAgX,QAAA,CAAAjG,eAAA,GAAA1Q,MAAA;IAAA,EAA6B,oBAAA4W,qEAAA;MAAAzX,EAAA,CAAAI,aAAA,CAAAuR,KAAA;MAAA,MAAA+F,QAAA,GAAA1X,EAAA,CAAAO,aAAA;MAAA,OACrCP,EAAA,CAAAQ,WAAA,CAAAkX,QAAA,CAAAC,SAAA,EAAW;IAAA,EAD0B;IACxB3X,EAAA,CAAAU,MAAA,0BAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAmB;IAE7DX,EAAA,CAAAC,cAAA,oBAA8C;IAC5CD,EAAA,CAAAiC,UAAA,MAAA2V,uCAAA,mBAsIM;IACN5X,EAAA,CAAAiC,UAAA,MAAA4V,uCAAA,kBAGM;IACN7X,EAAA,CAAAiC,UAAA,MAAA6V,uCAAA,kBAcM;IACR9X,EAAA,CAAAW,YAAA,EAAM;;;;;;;IAlfEX,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAiB,UAAA,cAAA8W,MAAA,CAAAxT,gBAAA,CAA8B;IAO1BvE,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,aAAA8W,MAAA,CAAAC,UAAA,CAAuB,YAAAhY,EAAA,CAAAuF,eAAA,KAAA0S,IAAA,EAAAF,MAAA,CAAAC,UAAA;IAQmDhY,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,aAAA8W,MAAA,CAAAC,UAAA,CAAuB,YAAAhY,EAAA,CAAAuF,eAAA,KAAA0S,IAAA,EAAAF,MAAA,CAAAC,UAAA;IAUrEhY,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAiR,eAAA,KAAAiH,IAAA,EAAoB;IAWpBlY,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAiR,eAAA,KAAAkH,IAAA,EAAyB;IAUWnY,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAiB,UAAA,oBAAAmX,IAAA,CAAyB;IAG7DpY,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA0W,MAAA,CAAAM,OAAA,EAAkB;IAUoBrY,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAiB,UAAA,oBAAAqX,IAAA,CAAyB;IAI/DtY,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA0W,MAAA,CAAAQ,UAAA,EAAqB;IAa7CvY,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAiB,UAAA,gBAAA8W,MAAA,CAAAS,kBAAA,CAAkC;IASVxY,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA0W,MAAA,CAAAU,qBAAA,EAAgC;IAwBxDzY,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAAiB,UAAA,gBAAA8W,MAAA,CAAAW,gBAAA,CAAgC;IASR1Y,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA0W,MAAA,CAAAY,mBAAA,EAA8B;IAwBtD3Y,EAAA,CAAAmB,SAAA,GAAwC;IAAxCnB,EAAA,CAAAiB,UAAA,gBAAA8W,MAAA,CAAAa,wBAAA,CAAwC;IAEZ5Y,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA0W,MAAA,CAAAc,mBAAA,EAA8B;IAuBpC7Y,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAe,iBAAA,CAAuB;IAS7C9Y,EAAA,CAAAmB,SAAA,GAA+C;IAA/CnB,EAAA,CAAAoB,iBAAA,CAAA2W,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,gBAAAjW,KAAA,CAA+C;IAO/C9C,EAAA,CAAAmB,SAAA,GAA4C;IAA5CnB,EAAA,CAAAoB,iBAAA,CAAA2W,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,aAAAjW,KAAA,CAA4C;IAQZ9C,EAAA,CAAAmB,SAAA,GAAwE;IAAxEnB,EAAA,CAAA6E,kBAAA,MAAAkT,MAAA,CAAAjL,MAAA,CAAAC,gBAAA,CAAAgL,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,SAAAjW,KAAA,SAAwE;IASlG9C,EAAA,CAAAmB,SAAA,GAAwH;IAAxHnB,EAAA,CAAA6E,kBAAA,MAAAkT,MAAA,CAAAjL,MAAA,CAAAC,gBAAA,CAAAgL,MAAA,CAAAxT,gBAAA,CAAAzB,KAAA,CAAAuK,IAAA,GAAA0K,MAAA,CAAAxT,gBAAA,CAAAzB,KAAA,CAAAkW,QAAA,gBAAwH;IAkBxHhZ,EAAA,CAAAmB,SAAA,GAA4D;IAA5DnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,UAAAE,MAAA,kBAAAlB,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,UAAAE,MAAA,iBAA4D;IAYvCjZ,EAAA,CAAAmB,SAAA,GAAQ;IAARnB,EAAA,CAAAiB,UAAA,YAAA8W,MAAA,CAAAmB,KAAA,CAAQ;IAM3BlZ,EAAA,CAAAmB,SAAA,GAAgE;IAAhEnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,YAAAE,MAAA,kBAAAlB,MAAA,CAAAxT,gBAAA,CAAAwU,GAAA,YAAAE,MAAA,mBAAgE;IAU5EjZ,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAoB,cAAA,CAAoB;IAqBtBnZ,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAiB,UAAA,cAAA8W,MAAA,CAAAhP,mBAAA,CAAiC;IAIa/I,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAiB,UAAA,oBAAAmY,IAAA,CAAmC;IAIhDpZ,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,UAAA0W,MAAA,CAAAsB,sBAAA,EAAiC;IAUvCrZ,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAiB,UAAA,YAAA8W,MAAA,CAAAuB,aAAA,CAAgB;IAMFtZ,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAhP,mBAAA,CAAAjG,KAAA,CAAAyW,GAAA,eAAuD;IAiB5BvZ,EAAA,CAAAmB,SAAA,GAA6D;IAA7DnB,EAAA,CAAAiB,UAAA,aAAA8W,MAAA,CAAAhP,mBAAA,CAAAjG,KAAA,CAAAyW,GAAA,eAA6D;IACzGvZ,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAyB,eAAA,CAA0B;IAU5CxZ,EAAA,CAAAmB,SAAA,GAA+D;IAA/DnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAAhP,mBAAA,CAAAgQ,GAAA,UAAAE,MAAA,kBAAAlB,MAAA,CAAAhP,mBAAA,CAAAgQ,GAAA,UAAAE,MAAA,iBAA+D;IAqB3DjZ,EAAA,CAAAmB,SAAA,GAA6B;IAA7BnB,EAAA,CAAAiB,UAAA,YAAA8W,MAAA,CAAAxG,eAAA,CAA6B;IAIxBvR,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAA0B,cAAA,CAAoB;IAuIrCzZ,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAiB,UAAA,UAAA8W,MAAA,CAAA0B,cAAA,CAAqB;IAIrBzZ,EAAA,CAAAmB,SAAA,GAAmD;IAAnDnB,EAAA,CAAAiB,UAAA,SAAA8W,MAAA,CAAA3T,UAAA,CAAAC,IAAA,CAAAC,MAAA,SAAAyT,MAAA,CAAA0B,cAAA,CAAmD;;;;;IA+BvDzZ,EAAA,CAAAC,cAAA,cAAuE;IAC/CD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAH3CX,EAAA,CAAAC,cAAA,kBACsB;IADID,EAAA,CAAAE,UAAA,mBAAAwZ,yEAAA;MAAA1Z,EAAA,CAAAI,aAAA,CAAAuZ,KAAA;MAAA,MAAAC,QAAA,GAAA5Z,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoZ,QAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAE/D7Z,EAAA,CAAAiC,UAAA,IAAA6X,sDAAA,iBAEM;IACN9Z,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAJDX,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,SAAA8Y,QAAA,CAAAC,oBAAA,CAA0B;;;;;IA0B1Bha,EAAA,CAAAC,cAAA,sBAA+F;IAC7FD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;;IAF4CX,EAAA,CAAAiB,UAAA,UAAAgZ,SAAA,CAAc,aAAAC,QAAA,CAAApR,SAAA;IACrE9I,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAoV,SAAA,MACF;;;;;IASAja,EAAA,CAAAC,cAAA,sBAAqF;IACnFD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;;IAFkCX,EAAA,CAAAiB,UAAA,UAAAkZ,SAAA,CAAc,aAAAC,QAAA,CAAAtR,SAAA;IAC3D9I,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,MAAAsV,SAAA,MACF;;;;;;IAKNna,EAAA,CAAAC,cAAA,UAAgE;IAEjDD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC9BX,EAAA,CAAAC,cAAA,iBAEkD;IADhDD,EAAA,CAAAE,UAAA,mBAAAma,sEAAA;MAAAra,EAAA,CAAAI,aAAA,CAAAka,KAAA;MAAA,MAAAC,QAAA,GAAAva,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+Z,QAAA,CAAA1Q,mBAAA,EAAqB;IAAA,EAAC,mBAAA2Q,sEAAA;MAAAxa,EAAA,CAAAI,aAAA,CAAAka,KAAA;MAAA,MAAAG,QAAA,GAAAza,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAia,QAAA,CAAAlR,gBAAA,CAAiB,cAAc,CAAC;IAAA,EAA1C,sBAAAmR,yEAAA;MAAA1a,EAAA,CAAAI,aAAA,CAAAka,KAAA;MAAA,MAAAK,QAAA,GAAA3a,EAAA,CAAAO,aAAA;MAAA,OACnBP,EAAA,CAAAQ,WAAA,CAAAma,QAAA,CAAA1G,gBAAA,CAAiB,cAAc,CAAC;IAAA,EADb;IADjCjU,EAAA,CAAAW,YAAA,EAEkD;;;;;IAsBtDX,EAAA,CAAAC,cAAA,UAAuB;IAEZD,EAAA,CAAAU,MAAA,kCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC1CX,EAAA,CAAAC,cAAA,2BAA4F;IAC5DD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAmB;IACpDX,EAAA,CAAAC,cAAA,4BAA6B;IAAAD,EAAA,CAAAU,MAAA,SAAE;IAAAV,EAAA,CAAAW,YAAA,EAAmB;;;;;;;;;;;IAtF9DX,EAAA,CAAAC,cAAA,cAAsB;IACVD,EAAA,CAAAE,UAAA,mBAAA0a,kEAAA;MAAA5a,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAC,QAAA,GAAA9a,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsa,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAAyC/a,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAE/FX,EAAA,CAAAC,cAAA,aAAyC;IAE/BD,EAAA,CAAAU,MAAA,4BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEpCX,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAiC,UAAA,IAAA+Y,gDAAA,sBAMS;IACXhb,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAwC;IAIrBD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACrCX,EAAA,CAAA+J,SAAA,kBAAqG;IACrG/J,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAIlDX,EAAA,CAAAC,cAAA,WAAK;IAEUD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACtCX,EAAA,CAAAC,cAAA,uBAC2D;IAAzDD,EAAA,CAAAE,UAAA,6BAAA+a,+EAAApa,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAK,QAAA,GAAAlb,EAAA,CAAAO,aAAA;MAAA,OAAmBP,EAAA,CAAAQ,WAAA,CAAA0a,QAAA,CAAA7F,qBAAA,CAAAxU,MAAA,CAAAiC,KAAA,CAAmC;IAAA,EAAC;IACvD9C,EAAA,CAAAC,cAAA,uBAAmC;IACjCD,EAAA,CAAA+J,SAAA,iCAC+D;IACjE/J,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAAiC,UAAA,KAAAkZ,qDAAA,yBAEa;;IACfnb,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAC,cAAA,WAAK;IAEUD,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC1BX,EAAA,CAAAC,cAAA,sBAAkC;IAChCD,EAAA,CAAAiC,UAAA,KAAAmZ,qDAAA,yBAEa;IACfpb,EAAA,CAAAW,YAAA,EAAa;IAIjBX,EAAA,CAAAiC,UAAA,KAAAoZ,8CAAA,kBAOM;IAENrb,EAAA,CAAAC,cAAA,WAAM;IAESD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACpCX,EAAA,CAAAC,cAAA,kBAEgH;IAFpDD,EAAA,CAAAE,UAAA,mBAAAob,gEAAA;MAAAtb,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAU,QAAA,GAAAvb,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+a,QAAA,CAAAhS,gBAAA,CAAiB,aAAa,CAAC;IAAA,EAAC,sBAAAiS,mEAAA;MAAAxb,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAY,QAAA,GAAAzb,EAAA,CAAAO,aAAA;MAAA,OACvFP,EAAA,CAAAQ,WAAA,CAAAib,QAAA,CAAA/R,mBAAA,CAAoB,aAAa,CAAC;IAAA,EADqD,mBAAAgS,gEAAA7a,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAc,QAAA,GAAA3b,EAAA,CAAAO,aAAA;MAAA,OAC1CP,EAAA,CAAAQ,WAAA,CAAAmb,QAAA,CAAAzF,kBAAA,CAAArV,MAAA,CAA0B;IAAA,EADgB;IAArGb,EAAA,CAAAW,YAAA,EAEgH;IAIpHX,EAAA,CAAAC,cAAA,WAAK;IAEUD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC5BX,EAAA,CAAAC,cAAA,kBAE6C;IAFSD,EAAA,CAAAE,UAAA,mBAAA0b,gEAAA/a,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAgB,QAAA,GAAA7b,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAqb,QAAA,CAAA/E,UAAA,CAAAjW,MAAA,EAAoB,OAAO,CAAC;IAAA,EAAC,mBAAAib,gEAAA;MAAA9b,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAkB,QAAA,GAAA/b,EAAA,CAAAO,aAAA;MAAA,OAC1CP,EAAA,CAAAQ,WAAA,CAAAub,QAAA,CAAAxS,gBAAA,CAAiB,OAAO,CAAC;IAAA,EADiB,sBAAAyS,mEAAA;MAAAhc,EAAA,CAAAI,aAAA,CAAAya,KAAA;MAAA,MAAAoB,QAAA,GAAAjc,EAAA,CAAAO,aAAA;MAAA,OAE9EP,EAAA,CAAAQ,WAAA,CAAAyb,QAAA,CAAAvS,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAFkD;IAA5F1J,EAAA,CAAAW,YAAA,EAE6C;IAIjDX,EAAA,CAAAiC,UAAA,KAAAia,8CAAA,kBAQM;IACRlc,EAAA,CAAAW,YAAA,EAAM;;;;IAlFGX,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAiB,UAAA,SAAAkb,OAAA,CAAArT,SAAA,CAAe;IASpB9I,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAiB,UAAA,cAAAkb,OAAA,CAAApT,mBAAA,CAAiC;IAGI/I,EAAA,CAAAmB,SAAA,GAA6C;IAA7CnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAA6W,IAAA,EAAAD,OAAA,CAAAnE,UAAA,EAA6C;IAEHhY,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,aAAAkb,OAAA,CAAAnE,UAAA,CAAuB;IAMjEhY,EAAA,CAAAmB,SAAA,GAA6C;IAA7CnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAA6W,IAAA,EAAAD,OAAA,CAAAnE,UAAA,EAA6C;IAIlEhY,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAiB,UAAA,aAAAkb,OAAA,CAAArT,SAAA,CAAsB;IAE9B9I,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAAiB,UAAA,gBAAAkb,OAAA,CAAAE,oBAAA,CAAoC;IAEXrc,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAqB,WAAA,SAAA8a,OAAA,CAAAG,eAAA,EAA0B;IAQtBtc,EAAA,CAAAmB,SAAA,GAA6C;IAA7CnB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAuF,eAAA,KAAA6W,IAAA,EAAAD,OAAA,CAAAnE,UAAA,EAA6C;IAGjDhY,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAiB,UAAA,YAAAkb,OAAA,CAAA7C,aAAA,CAAgB;IAO5CtZ,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAiB,UAAA,SAAAkb,OAAA,CAAApT,mBAAA,CAAAjG,KAAA,CAAAyW,GAAA,eAAuD;IAcTvZ,EAAA,CAAAmB,SAAA,GAA6D;IAA7DnB,EAAA,CAAAiB,UAAA,aAAAkb,OAAA,CAAApT,mBAAA,CAAAjG,KAAA,CAAAyW,GAAA,eAA6D;IAa5GvZ,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAiB,UAAA,SAAAkb,OAAA,CAAArT,SAAA,CAAe;;;;;IAe3B9I,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IADJX,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuc,kBAAA,MAAAC,MAAA,YAAAC,SAAA,MACF;;;;;IACAzc,EAAA,CAAAC,cAAA,UAAsC;IACpCD,EAAA,CAAA+J,SAAA,2BAKmB;IACrB/J,EAAA,CAAAW,YAAA,EAAM;;;;;IAXRX,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAiC,UAAA,IAAAya,qCAAA,mBAEM;IACN1c,EAAA,CAAAiC,UAAA,IAAA0a,qCAAA,kBAOM;IACR3c,EAAA,CAAAW,YAAA,EAAM;;;;IAXkBX,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAiB,UAAA,YAAA2b,OAAA,CAAAC,YAAA,CAAgB;IAGhC7c,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAiB,UAAA,SAAA2b,OAAA,CAAAC,YAAA,CAAAvY,MAAA,MAA8B;;;;;;IAWpCtE,EAAA,CAAAC,cAAA,eAA8B;IAE2DD,EAAA,CAAAE,UAAA,mBAAA4c,gEAAA;MAAA9c,EAAA,CAAAI,aAAA,CAAA2c,KAAA;MAAA,MAAAC,QAAA,GAAAhd,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwc,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAC9Gjd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAG9BX,EAAA,CAAAC,cAAA,eAA4B;IAElBD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE5BX,EAAA,CAAAC,cAAA,eAA0B;IAIWD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,IAA4E;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxGX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAElCX,EAAA,CAAAC,cAAA,UAAI;IAC2BD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACpDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,IAA0D;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACtFX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAElCX,EAAA,CAAA+J,SAAA,UAAI;IACJ/J,EAAA,CAAAC,cAAA,UAAI;IAC2BD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAChDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,IAAoE;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChGX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,UAAE;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAE/BX,EAAA,CAAAC,cAAA,UAAI;IAC2BD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,IAAuD;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnFX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,UAAE;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IAjBNX,EAAA,CAAAmB,SAAA,IAA4E;IAA5EnB,EAAA,CAAAoB,iBAAA,CAAA8b,OAAA,CAAApQ,MAAA,CAAAC,gBAAA,CAAAmQ,OAAA,CAAA3Y,gBAAA,CAAAzB,KAAA,CAAAmK,WAAA,EAA4E;IAK5EjN,EAAA,CAAAmB,SAAA,GAA0D;IAA1DnB,EAAA,CAAAoB,iBAAA,CAAA8b,OAAA,CAAApQ,MAAA,CAAAC,gBAAA,CAAAmQ,OAAA,CAAAC,mBAAA,IAA0D;IAM1Dnd,EAAA,CAAAmB,SAAA,GAAoE;IAApEnB,EAAA,CAAAoB,iBAAA,CAAA8b,OAAA,CAAApQ,MAAA,CAAAC,gBAAA,CAAAmQ,OAAA,CAAA3Y,gBAAA,CAAAzB,KAAA,CAAAuK,IAAA,EAAoE;IAKpErN,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,iBAAA,CAAA8b,OAAA,CAAApQ,MAAA,CAAAC,gBAAA,CAAAmQ,OAAA,CAAAE,iBAAA,IAAuD;;;;;;IAW1Fpd,EAAA,CAAAC,cAAA,eAA8B;IAE2DD,EAAA,CAAAE,UAAA,mBAAAmd,gEAAA;MAAArd,EAAA,CAAAI,aAAA,CAAAkd,KAAA;MAAA,MAAAC,QAAA,GAAAvd,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+c,QAAA,CAAAN,eAAA,EAAiB;IAAA,EAAC;IAC9Gjd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAG9BX,EAAA,CAAAC,cAAA,eAA4B;IAElBD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE5BX,EAAA,CAAAC,cAAA,eAA0B;IAIWD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAEhDX,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAU,MAAA,IAAoD;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChFX,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAU,MAAA,UAAE;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADNX,EAAA,CAAAmB,SAAA,IAAoD;IAApDnB,EAAA,CAAAoB,iBAAA,CAAAoc,OAAA,CAAA1Q,MAAA,CAAAC,gBAAA,CAAAyQ,OAAA,CAAAC,YAAA,OAAoD;;;;;;IAWvFzd,EAAA,CAAAC,cAAA,eAA8B;IAG1BD,EAAA,CAAAE,UAAA,mBAAAwd,gEAAA;MAAA1d,EAAA,CAAAI,aAAA,CAAAud,KAAA;MAAA,MAAAC,QAAA,GAAA5d,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAod,QAAA,CAAAX,eAAA,EAAiB;IAAA,EAAC;IACzBjd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAG9BX,EAAA,CAAAC,cAAA,aAAyC;IAE/BD,EAAA,CAAAU,MAAA,kCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE1CX,EAAA,CAAAC,cAAA,eAA2B;IAGvBD,EAAA,CAAAU,MAAA,6HACO;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACfX,EAAA,CAAAC,cAAA,gBAAqD;IACaD,EAAA,CAAAE,UAAA,mBAAA2d,iEAAA;MAAA7d,EAAA,CAAAI,aAAA,CAAAud,KAAA;MAAA,MAAAG,QAAA,GAAA9d,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsd,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACpF/d,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,mBAA+F;IAA5BD,EAAA,CAAAE,UAAA,mBAAA8d,iEAAA;MAAAhe,EAAA,CAAAI,aAAA,CAAAud,KAAA;MAAA,MAAAM,QAAA,GAAAje,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAyd,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAC5Fle,EAAA,CAAAU,MAAA,2BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;;IASjBX,EAAA,CAAAC,cAAA,aAAyC;IAE/BD,EAAA,CAAAU,MAAA,4BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEpCX,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA0B;IACED,EAAA,CAAAE,UAAA,mBAAAie,gEAAA;MAAAne,EAAA,CAAAI,aAAA,CAAAge,KAAA;MAAA,MAAAC,QAAA,GAAAre,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6d,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAC1Dte,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACdX,EAAA,CAAAC,cAAA,kBAA6F;IAArFD,EAAA,CAAAE,UAAA,mBAAAqe,gEAAA;MAAAve,EAAA,CAAAI,aAAA,CAAAge,KAAA;MAAA,MAAAI,QAAA,GAAAxe,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAge,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACzCze,EAAA,CAAAU,MAAA,WAAE;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IANbX,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAA6E,kBAAA,oCAAA6Z,OAAA,CAAAC,YAAA,QACF;;;ADvtBN,MAqCaC,eAAe;EAoG1BC,YACkCxa,IAAS,EACjCya,EAAe,EACfC,GAAqB,EACrBC,cAA8B,EAC9BC,MAAc,EACfC,MAAiB,EAChBC,UAA4B,EAC5BC,KAA0B,EACFC,UAAe,EACxCvS,MAA2B,EAC1BwS,QAAmB,EACnBC,EAAc,EACdC,IAAiB,EACjBC,EAAqB,EACrBC,iBAAoC;IAdZ,KAAArb,IAAI,GAAJA,IAAI;IAC5B,KAAAya,EAAE,GAAFA,EAAE;IACF,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,KAAK,GAALA,KAAK;IACmB,KAAAC,UAAU,GAAVA,UAAU;IACnC,KAAAvS,MAAM,GAANA,MAAM;IACL,KAAAwS,QAAQ,GAARA,QAAQ;IACR,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IA1G3B,KAAAC,QAAQ,GAAG,yBAAyB;IAEpC,KAAA/c,eAAe,GAAG,IAAInF,WAAW,CAAC,EAAE,CAAC;IAG9B,KAAA0b,cAAc,GAAY,KAAK;IAItC,KAAAyG,aAAa,GAAY,KAAK;IAG9B,KAAAxb,UAAU,GAAG,IAAIjF,kBAAkB,CAAM,EAAE,CAAC;IAE5C,KAAA0gB,aAAa,GAAG,EAAE;IAClB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAhX,SAAS,GAAY,KAAK;IAC1B,KAAAkP,UAAU,GAAY,IAAI;IAC1B,KAAAjV,OAAO,GAAY,KAAK;IAExB,KAAAgd,aAAa,GAAU,EAAE;IACzB,KAAAzG,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,IAAI,EAAE,SAAS,CAAC;IAI5C,KAAA0G,IAAI,GAAU,EAAE;IAChB,KAAA3D,oBAAoB,GAAgB,IAAI5e,WAAW,EAAE;IACrD,KAAA6e,eAAe,GAAyB,IAAIrd,aAAa,CAAQ,CAAC,CAAC;IACnE,KAAAghB,kBAAkB,GAAU,EAAE;IAC9B,KAAArH,wBAAwB,GAAgB,IAAInb,WAAW,EAAE;IACzD,KAAAob,mBAAmB,GAAyB,IAAI5Z,aAAa,CAClE,CAAC,CACF;IACM,KAAAihB,YAAY,GAAU,EAAE;IACxB,KAAA1H,kBAAkB,GAAgB,IAAI/a,WAAW,EAAE;IACnD,KAAAgb,qBAAqB,GAAyB,IAAIxZ,aAAa,CACpE,CAAC,CACF;IACM,KAAAkhB,UAAU,GAAU,EAAE;IACtB,KAAAzH,gBAAgB,GAAgB,IAAIjb,WAAW,EAAE;IACjD,KAAAkb,mBAAmB,GAAyB,IAAI1Z,aAAa,CAClE,CAAC,CACF;IAIS,KAAAmhB,UAAU,GAAG,IAAI5hB,OAAO,EAAQ;IAE1C,KAAA6hB,QAAQ,GAAW,EAAE;IAErB,KAAArd,eAAe,GAAY,KAAK;IAChC,KAAAY,iBAAiB,GAAY,KAAK;IAClC,KAAAoW,oBAAoB,GAAY,KAAK;IAKrC,KAAAsG,UAAU,GAAG,KAAK;IAClB,KAAA7G,cAAc,GAAY,KAAK;IAC/B,KAAAP,KAAK,GAAa,CAAC,SAAS,EAAE,OAAO,CAAC;IAEtC,KAAAqH,KAAK,GAAW,CAAC;IAGjB,KAAA5c,UAAU,GAAY,IAAI;IAC1B,KAAA6c,UAAU,GAAY,IAAI;IAC1B,KAAAvc,gBAAgB,GAAG,KAAK;IACxB,KAAAuV,eAAe,GAAY,KAAK;IAChC,KAAAjI,eAAe,GAAY,KAAK;IAChC,KAAAkP,gBAAgB,GAAU,EAAE;IAE5B,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,QAAQ,GAAM,EAAE;IAMhB,KAAAC,aAAa,GAAY,KAAK;IAI9B,KAAAlb,sBAAsB,GAAQ,EAAE;IAChC,KAAAU,iBAAiB,GAAQ,EAAE;IAC3B,KAAAO,sBAAsB,GAAQ,EAAE;IAChC,KAAAO,iBAAiB,GAAQ,EAAE;IAC3B,KAAAO,wBAAwB,GAAQ,EAAE;IAClC,KAAAS,mBAAmB,GAAQ,EAAE;IAqB3B,IAAI,CAAC2Y,cAAc,GAAG,IAAI,CAAC1B,UAAU,CAAC2B,aAAa,EAAE,CAAChe,KAAK;IAC3D,IAAI,CAACie,IAAI,GAAG,IAAI,CAACvB,IAAI,CAACwB,cAAc,EAAE;IACtC,IAAI,CAACX,QAAQ,GAAG,IAAI,CAAClB,UAAU,CAAC8B,WAAW,EAAE,CAACne,KAAK;IACnD,IAAI,CAACoe,WAAW,GAAG,IAAI,CAACb,QAAQ,CAAC,kBAAkB,CAAC,CAAC3hB,GAAG,CAACyiB,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAACC,WAAW,EAAE,CAAC;IAC3F,IAAIC,QAAQ,GAAG,IAAI,CAACP,IAAI,CAACO,QAAQ;IAC/B,IAAI,CAACvC,GAAG,CAACwC,iCAAiC,CAACD,QAAQ,CAAC,CACnDE,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAGA,GAAG,CAAC,QAAQ,CAAC,IAAI,SAAS,IAAIA,GAAG,CAAC,uBAAuB,CAAC,EAAC;QAC5D,IAAI,CAACC,qBAAqB,GAAGD,GAAG,CAAC,uBAAuB,CAAC;;MAE3D,IAAI,CAAChC,EAAE,CAACkC,aAAa,EAAE;IACzB,CAAC,CAAC;IACJ,IAAI,CAACpd,gBAAgB,GAAG,IAAI,CAACua,EAAE,CAAC8C,KAAK,CAAC;MACpCR,QAAQ,EAAE,CAAC,EAAE,EAAExjB,UAAU,CAACikB,QAAQ,CAAC;MACnCC,WAAW,EAAE,CAAC,EAAE,EAAElkB,UAAU,CAACikB,QAAQ,CAAC;MACtCE,YAAY,EAAE,CAAC,EAAE,EAAEnkB,UAAU,CAACikB,QAAQ,CAAC;MACvCG,YAAY,EAAE,CAAC,EAAE,EAAEpkB,UAAU,CAACikB,QAAQ,CAAC;MACvCI,UAAU,EAAE,CAAC,EAAE,EAAErkB,UAAU,CAACikB,QAAQ,CAAC;MACrCK,QAAQ,EAAE,CAAC,WAAW,EAAEtkB,UAAU,CAACikB,QAAQ,CAAC;MAC5CM,UAAU,EAAE,CAAC,EAAE,EAAEvkB,UAAU,CAACikB,QAAQ,CAAC;MACrCO,YAAY,EAAE,CAAC,EAAE,EAAExkB,UAAU,CAACikB,QAAQ,CAAC;MACvCQ,cAAc,EAAE,CAAC,EAAE,EAAEzkB,UAAU,CAACikB,QAAQ,CAAC;MACzCtI,GAAG,EAAE,CAAC,EAAE,EAAE3b,UAAU,CAACikB,QAAQ,CAAC;MAC9B5U,WAAW,EAAE,CAAC,CAAC,EAAErP,UAAU,CAACikB,QAAQ,CAAC;MACrCnV,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC9O,UAAU,CAACikB,QAAQ,EAAE,IAAI,CAACS,cAAc,EAAE,CAAC,CAAC;MACxDC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC3kB,UAAU,CAACikB,QAAQ,EAAE,IAAI,CAACW,gBAAgB,EAAE,CAAC,CAAC;MAC5DC,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC7kB,UAAU,CAACikB,QAAQ,CAAC,CAAC;MACxC7I,QAAQ,EAAE,CAAC,CAAC,EAAEpb,UAAU,CAACikB,QAAQ,CAAC;MAClCxU,IAAI,EAAE,CAAC,CAAC,EAAEzP,UAAU,CAACikB,QAAQ,CAAC;MAC9BrU,SAAS,EAAE,CAAC,CAAC,CAAC;MACdkV,YAAY,EAAE,CAAC,IAAI,EAAE9kB,UAAU,CAACikB,QAAQ,CAAC;MACzCc,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;IACF,IAAI,CAACpe,gBAAgB,CAACwU,GAAG,CAAC,OAAO,CAAC,CAAC6J,aAAa,EAAE;IAClD,IAAI,CAACre,gBAAgB,CAACwU,GAAG,CAAC,OAAO,CAAC,CAAC8J,WAAW,EAAE;IAChD,IAAI,CAAC9Z,mBAAmB,GAAG,IAAI,CAAC+V,EAAE,CAAC8C,KAAK,CAAC;MACvC1V,aAAa,EAAE,CAAC,EAAE,EAAEtO,UAAU,CAACikB,QAAQ,CAAC;MACxC7V,aAAa,EAAE,CAAC,EAAE,EAAEpO,UAAU,CAACikB,QAAQ,CAAC;MACxC/V,cAAc,EAAE,CAAC,EAAE,EAAElO,UAAU,CAACikB,QAAQ,CAAC;MACzC7Y,cAAc,EAAE,CAAC,EAAE,EAAEpL,UAAU,CAACikB,QAAQ,CAAC;MACzCtI,GAAG,EAAE,CAAC,EAAE,EAAE3b,UAAU,CAACikB,QAAQ,CAAC;MAC9BiB,aAAa,EAAE,CAAC,CAAC,CAAC;MAClBC,UAAU,EAAE,CAAC,EAAE,EAAEnlB,UAAU,CAACikB,QAAQ,CAAC;MACrCmB,YAAY,EAAE,CAAC,CAAC,CAAC;MACjBtW,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC9O,UAAU,CAACikB,QAAQ,EAAE,IAAI,CAACS,cAAc,EAAE,CAAC,CAAC;MACxD1V,IAAI,EAAE,CAAC,CAAC,CAAC;MACTK,WAAW,EAAE,CAAC,CAAC,EAAErP,UAAU,CAACikB,QAAQ,CAAC;MACrCxU,IAAI,EAAE,CAAC,CAAC,EAAEzP,UAAU,CAACikB,QAAQ,CAAC;MAC9BrU,SAAS,EAAE,CAAC,CAAC,CAAC;MACdkV,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;IAEF,IAAI,CAACM,WAAW,GAAG,IAAI,CAAC5D,UAAU,CAAC6D,GAAG;IACtC,IAAI,CAACtD,aAAa,GAAG,IAAI,CAACP,UAAU,CAACO,aAAa;IAClD,IAAI,CAACT,UAAU,CAACgE,YAAY,CAAC3B,SAAS,CAAE4B,GAAG,IAAI;MAC7C,IAAI,CAACvgB,eAAe,GAAG,IAAI,CAACD,eAAe,CAACygB,YAAY,CAACC,IAAI,CAC3D3kB,SAAS,CAAC,EAAE,CAAC,EACbD,GAAG,CAAEoE,KAAK,IAAK,IAAI,CAACygB,OAAO,CAACzgB,KAAK,IAAI,EAAE,EAAEsgB,GAAG,CAACpB,YAAY,EAAG,EAAE,CAAC,CAAC,CACjE;MACD,IAAI,CAACwB,kBAAkB,GAAGJ,GAAG,CAACK,SAAS;MACvC,IAAI,CAACC,oBAAoB,GAAGN,GAAG,CAAC5a,SAAS;IAC3C,CAAC,CAAC;IACF,IAAI,CAACmb,cAAc,EAAE;IACrB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAvB,cAAcA,CAAA;IACZ,OAAQwB,OAAwB,IAAmC;MACjE,MAAMC,OAAO,GAAGD,OAAO,CAAChhB,KAAK,GAAG,CAAC;MACjC,OAAOihB,OAAO,GAAG,IAAI,GAAG;QAAEC,YAAY,EAAE;UAAElhB,KAAK,EAAEghB,OAAO,CAAChhB;QAAK;MAAE,CAAE;IACpE,CAAC;EACH;EAEA0f,gBAAgBA,CAAA;IACd,OAAQsB,OAAwB,IAAmC;MACjE,MAAMC,OAAO,GAAGD,OAAO,CAAChhB,KAAK,GAAG,CAAC;MACjC,OAAOihB,OAAO,GAAG,IAAI,GAAG;QAAEE,cAAc,EAAE;UAAEnhB,KAAK,EAAEghB,OAAO,CAAChhB;QAAK;MAAE,CAAE;IACtE,CAAC;EACH;EAEAohB,gBAAgBA,CAAA;IACd,IAAIjX,WAAW,GAAG,IAAI,CAACT,QAAQ,CAAC,eAAe,CAAC;IAChD,IAAI,CAACjI,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACoL,QAAQ,CAAClX,WAAW,CAAC;IAC9D,IAAI+L,QAAQ,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAAC,OAAO,CAAC,GAAGmK,WAAW;IACjE,IAAI,CAAC1I,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAACiM,QAAQ,CAAC,CAAC;IACtF,IAAI3L,IAAI,GAAG,IAAI,CAACK,WAAW,CAAC,WAAW,CAAC;IACxC,IAAI,CAACnJ,gBAAgB,CAACwU,GAAG,CAAC,MAAM,CAAC,CAACoL,QAAQ,CAAC9W,IAAI,CAAC;IAChD,IAAIG,SAAS,GAAGH,IAAI,GAAG2L,QAAQ;IAC/B,IAAI,CAACzU,gBAAgB,CAACwU,GAAG,CAAC,WAAW,CAAC,CAACoL,QAAQ,CAAC3W,SAAS,CAAC;IAC1D,IAAI,CAACiS,EAAE,CAACkC,aAAa,EAAE;EACzB;EAEAgC,cAAcA,CAAA;IACZ,IAAIS,eAAe,GAAG,IAAI,CAAChF,KAAK,CAACiF,WAAW,EAAE,CAACvhB,KAAK;IACpD,IACEshB,eAAe,CAACE,cAAc,CAAC,IAAI,CAACzD,cAAc,CAAC,iBAAiB,CAAC,CAAC,EACtE;MACA,IAAI,CAAC0D,QAAQ,GAAGH,eAAe,CAAC,IAAI,CAACvD,cAAc,CAAC,iBAAiB,CAAC,CAAC;MACvE;MACA,IAAI,CAAChY,OAAO,GAAG,IAAI,CAAC0b,QAAQ,CACzBC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,QAAQ,KAAK,WAAW,CAAC,CAChEjmB,GAAG,CAAE+lB,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;MAC/B,IAAIE,OAAO,GAAG,IAAI,CAACL,QAAQ,CACxBC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,QAAQ,KAAK,WAAW,CAAC,CAChEjmB,GAAG,CAAE+lB,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;MAE/B,IAAI,CAAC1E,IAAI,GAAG,CAAC,GAAG,IAAI,CAACnX,OAAO,EAAE,GAAG+b,OAAO,CAAC;MACzC,IAAI,CAACC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC9E,IAAI,CAAC,CAAC;MAC7C,IAAI,CAAC3G,sBAAsB,GAAG,IAAI,CAACtQ,mBAAmB,CAACgQ,GAAG,CAAC,gBAAgB,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAAC+hB,cAAc,EAAG,aAAa,CAAC,CAAC,CAAC;MAC7L,IAAI,CAACvI,eAAe,CAACyI,IAAI,CAAC,IAAI,CAAC/E,IAAI,CAACgF,KAAK,EAAE,CAAC;MAC5C,IAAI,CAAC3I,oBAAoB,CAACgH,YAAY,CACnCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;QACd,IAAI,CAACyD,MAAM,CACT,IAAI,CAACjF,IAAI,EACT,IAAI,CAAC3D,oBAAoB,EACzB,IAAI,CAACC,eAAe,CACrB;MACH,CAAC,CAAC;MACF,IAAI,CAAC4I,eAAe,EAAE;KACzB,MAAM;MACL,IAAI9B,GAAG,GAAG,EAAE;MACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;MACpC8B,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,CAACvC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAI,CAAC9B,GAAG,CAACoG,8BAA8B,CAAC/B,GAAG,CAAC,CAAC5B,SAAS,CAAC;QACrDuD,IAAI,EAAGtD,GAAG,IAAI;UACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAAC8C,QAAQ,GAAG9C,GAAG,CAAC,SAAS,CAAC;YAC9B,IAAI,CAAC5Y,OAAO,GAAG,IAAI,CAAC0b,QAAQ,CACzBC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,QAAQ,KAAK,WAAW,CAAC,CAChEjmB,GAAG,CAAE+lB,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;YAE/B,IAAIE,OAAO,GAAG,IAAI,CAACL,QAAQ,CACxBC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,QAAQ,KAAK,WAAW,CAAC,CAChEjmB,GAAG,CAAE+lB,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;YAE/B,IAAI,CAAC1E,IAAI,GAAG,CAAC,GAAG,IAAI,CAACnX,OAAO,EAAE,GAAG+b,OAAO,CAAC;YACzC,IAAI,CAACC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC9E,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC3G,sBAAsB,GAAG,IAAI,CAACtQ,mBAAmB,CAACgQ,GAAG,CAAC,gBAAgB,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAAC+hB,cAAc,EAAG,aAAa,CAAC,CAAC,CAAC;YAC7L,IAAI,CAACvI,eAAe,CAACyI,IAAI,CAAC,IAAI,CAAC/E,IAAI,CAACgF,KAAK,EAAE,CAAC;YAC5C,IAAI,CAAC3I,oBAAoB,CAACgH,YAAY,CACnCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;cACd,IAAI,CAACyD,MAAM,CACT,IAAI,CAACjF,IAAI,EACT,IAAI,CAAC3D,oBAAoB,EACzB,IAAI,CAACC,eAAe,CACrB;YACH,CAAC,CAAC;YACL,IAAI,CAAC4I,eAAe,EAAE;;QAEzB,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACpF,UAAU,CAAC2E,IAAI,EAAE;IACtB,IAAI,CAAC3E,UAAU,CAACqF,QAAQ,EAAE;EAC5B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACthB,UAAU,CAACuhB,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1C,IAAI,CAACvhB,UAAU,CAACwhB,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEUX,MAAMA,CAACY,IAAI,EAAEC,IAAI,EAAEzhB,IAAI;IAC/B,IAAI,CAACwhB,IAAI,EAAE;MACT;;IAEF,IAAIE,MAAM,GAAGD,IAAI,CAAChjB,KAAK;IACvB,IAAI,CAACijB,MAAM,EAAE;MACX1hB,IAAI,CAAC0gB,IAAI,CAACc,IAAI,CAACb,KAAK,EAAE,CAAC;MACvB;KACD,MAAM;MACLe,MAAM,GAAGA,MAAM,CAACC,WAAW,EAAE;;IAE/B3hB,IAAI,CAAC0gB,IAAI,CAACc,IAAI,CAACrB,MAAM,CAAEngB,IAAI,IAAKA,IAAI,CAAC2hB,WAAW,EAAE,CAACC,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3E;EAEUG,YAAYA,CAACL,IAAI,EAAEC,IAAI,EAAEzhB,IAAI;IACrC,IAAI,CAACwhB,IAAI,EAAE;MACT;;IAEF,IAAIE,MAAM,GAAGD,IAAI,CAAChjB,KAAK;IAEvB,IAAI,CAACijB,MAAM,EAAE;MACX1hB,IAAI,CAAC0gB,IAAI,CAACc,IAAI,CAACb,KAAK,EAAE,CAAC;MACvB;KACD,MAAM;MACLe,MAAM,GAAGA,MAAM,CAACC,WAAW,EAAE;;IAE/B,MAAMG,YAAY,GAAGN,IAAI,CAACnnB,GAAG,CAAE+lB,IAAI,IAAI;MACrC,MAAM2B,iBAAiB,GAAG3B,IAAI,CAACjc,SAAS,CAACgc,MAAM,CAC5C6B,QAAQ,IAAKA,QAAQ,CAACL,WAAW,EAAE,CAACC,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC,CAC1D;MACD,OAAO;QAAE,GAAGtB,IAAI;QAAEjc,SAAS,EAAE4d;MAAiB,CAAE;IAClD,CAAC,CAAC;IACF/hB,IAAI,CAAC0gB,IAAI,CAACoB,YAAY,CAAC;EACzB;EAEAniB,MAAMA,CAAA;IACJ,IAAI,CAACJ,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACkV,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACuH,QAAQ,GAAG,IAAI,CAAClB,UAAU,CAAC8B,WAAW,EAAE,CAACne,KAAK;IACnD,IAAI,IAAI,CAACyB,gBAAgB,CAACC,OAAO,EAAE;MACjC,IAAI,CAACD,gBAAgB,CAAC+hB,gBAAgB,EAAE;MACxC,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;MACpE,IAAI,CAAC3iB,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;KACxB,MAAM;MACL;MACA,IAAG,IAAI,CAAC7I,iBAAiB,EAAC;QACxB,IAAI,CAACvU,gBAAgB,CAAC+hB,gBAAgB,EAAE;QACxC,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;QACpE,IAAI,CAAC3iB,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;OACxB,MAAI;QACH,IAAI6E,cAAc,GAAG,IAAI,CAACC,cAAc,EAAE;QAC1CD,cAAc,CAAC,UAAU,CAAC,GAAG,KAAK;QAClCA,cAAc,CAAC,YAAY,CAAC,GAAGA,cAAc,CAAC,YAAY,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QACrEF,cAAc,CAAC,cAAc,CAAC,GAAGA,cAAc,CAAC,cAAc,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QACzEF,cAAc,CAAC,gBAAgB,CAAC,GAC9BA,cAAc,CAAC,gBAAgB,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QAC5C,IAAIriB,IAAI,GAAG,IAAI,CAACgc,QAAQ;QACxB,IAAIsG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC,CAAC/b,MAAM,GAAG,CAAC,EAAE;UACzC,IAAIuiB,OAAO,GAAG,EAAE;UAChBA,OAAO,CAAC,kBAAkB,CAAC,GAAGxiB,IAAI,CAAC,kBAAkB,CAAC;UACtD,IAAIyiB,cAAc,GAAGD,OAAO,CAAC,kBAAkB,CAAC,CAACE,IAAI,CAClDxH,EAAE,IAAKA,EAAE,CAACwC,YAAY,IAAIyE,cAAc,CAAC,cAAc,CAAC,CAC1D;UACD,IAAIM,cAAc,EAAE;YAClB,IAAI,CAACha,MAAM,CAACka,gBAAgB,CAAC,wBAAwB,CAAC;YACtD,IAAI,CAACpjB,iBAAiB,GAAG,KAAK;YAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;WACxB,MAAM;YACL,IAAI,CAACvd,UAAU,CAACC,IAAI,CAAC4iB,OAAO,CAAE1H,EAAE,IAAI;cAClCA,EAAE,CAAC,eAAe,CAAC,GAAGA,EAAE,CAAC,aAAa,CAAC,GAAGA,EAAE,CAAC,OAAO,CAAC;YACvD,CAAC,CAAC;YACFsH,OAAO,CAAC,kBAAkB,CAAC,CAACK,OAAO,CAACV,cAAc,CAAC;YACnDK,OAAO,CAAC,kBAAkB,CAAC,GAAGA,OAAO,CAAC,kBAAkB,CAAC,CAACrC,MAAM,CAC7DC,IAAI,IAAKA,IAAI,CAACtZ,QAAQ,KAAK,KAAK,CAClC;YACD0b,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACziB,UAAU,CAACC,IAAI;YAClD,IAAI+e,GAAG,GAAG,EAAE;YACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;YACpC8B,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACoG,KAAK;YAClC/D,GAAG,CAAC,MAAM,CAAC,GAAGyD,OAAO;YACrBzD,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;YACtBA,GAAG,CAAC,UAAU,CAAC,GAAG,WAAW;YAC7B,IAAI,CAACrE,GAAG,CACLqI,UAAU,CAAChE,GAAG,CAAC,CACfE,IAAI,CAAC7kB,KAAK,EAAE,CAAC,CACb+iB,SAAS,CAAC;cACTuD,IAAI,EAAGtD,GAAG,IAAI;gBACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;kBAClB,IAAI,CAAC4F,oBAAoB,EAAE;kBAC3B,IAAI,CAACzjB,iBAAiB,GAAG,KAAK;kBAC9B,IAAI,CAACnD,KAAK,EAAE;kBACZ,IAAI,CAACgf,EAAE,CAACkC,aAAa,EAAE;kBACvB,IAAI,CAAC7U,MAAM,CAACwa,mBAAmB,CAC7B,+BAA+B,CAChC;;cAEL,CAAC;cACDlC,KAAK,EAAGC,GAAG,IAAI;gBACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;cAClB;aACD,CAAC;;SAEP,MAAM;UACL,IAAI,CAACzhB,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;UACvB,IAAI,CAAC7U,MAAM,CAACyZ,iBAAiB,CAAC,uBAAuB,CAAC;;QAExD,IAAI,CAACxd,mBAAmB,CAACwe,UAAU,CAAC;UAClCrb,aAAa,EAAEsa,cAAc,CAAC,cAAc,CAAC;UAC7Cxa,aAAa,EAAEwa,cAAc,CAAC,cAAc;SAC7C,CAAC;;;EAIR;EAEAnR,qBAAqBA,CAACqP,QAAQ;IAC5B,MAAMD,IAAI,GAAG,IAAI,CAACF,QAAQ,CAACwC,IAAI,CAAEtC,IAAI,IAAI;MACvC,OAAOA,IAAI,CAACC,QAAQ,KAAKA,QAAQ;IACnC,CAAC,CAAC;IACF,IAAInL,GAAG;IACP,IAAIkL,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAIA,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAIA,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;MACrElL,GAAG,GAAG,IAAI;KACX,MAAM,IACLkL,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,IACtBA,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,IACtBA,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,EACtB;MACAlL,GAAG,GAAG,IAAI;KACX,MAAM,IACLkL,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IACpBA,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IACpBA,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,EACpB;MACAlL,GAAG,GAAG,KAAK;KACZ,MAAM;MACLA,GAAG,GAAG,IAAI;;IAEZ,IAAI,CAACwG,aAAa,GAAG0E,IAAI,CAAC,UAAU,CAAC,KAAK,WAAW,IAAIA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAClL,GAAG,EAAC,SAAS,CAAC,GAAG,CAACA,GAAG,CAAC;IAC7G,IAAIiO,qBAAqB;IACzBA,qBAAqB,GAAG/C,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI;IACvD,IAAIpX,IAAI,GAAGoX,IAAI,GACV,CAACA,IAAI,CAACH,cAAc,CAAC,YAAY,CAAC,GAAGG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAC1D+C,qBAAqB,GACvB/C,IAAI,CAAC,cAAc,CAAC,GACpB,CAAC;IACL,IAAI,CAAC1b,mBAAmB,CAACwe,UAAU,CAAC;MAClCta,WAAW,EAAE,CAAC;MACdnB,cAAc,EAAE2Y,IAAI,CAAC,UAAU,CAAC;MAChClL,GAAG,EAAEA,GAAG;MACRwJ,UAAU,EAAExJ,GAAG;MACf7M,KAAK,EAAE+X,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;MACzBpX,IAAI,EAAE,IAAI,CAACP,MAAM,CAACC,gBAAgB,CAACM,IAAI,CAAC;MACxCG,SAAS,EAAE;KACZ,CAAC;EACJ;EAEArE,kBAAkBA,CAACnH,MAAc;IAC/B,OAAO,IAAI,CAAC+d,aAAa,CAACpa,QAAQ,CAAC3D,MAAM,CAAC;EAC5C;EAEAyT,SAASA,CAAA;IACP,IAAI,CAAC1M,mBAAmB,CAACgQ,GAAG,CAAC,cAAc,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC;IACxD,IAAI,CAACsD,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAIC,YAAY,GAAG,IAAI,CAACnD,QAAQ,CAACwC,IAAI,CAClCtC,IAAI,IAAKA,IAAI,CAACkD,QAAQ,IAAI,IAAI,CAAC5e,mBAAmB,CAACjG,KAAK,CAACgJ,cAAc,CACzE;IACD,IAAI8b,aAAa,GAAG,IAAI,CAAC9a,MAAM,CAACC,gBAAgB,CAAC2a,YAAY,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC3e,mBAAmB,CAACjG,KAAK,CAACkgB,YAAY;IAC7H,IAAI,CAACja,mBAAmB,CAACgQ,GAAG,CAAC,aAAa,CAAC,CAACoL,QAAQ,CAACyD,aAAa,CAAC;IACnE,IAAI,CAAC1R,kBAAkB,CAAC,OAAO,CAAC;EAClC;EAEArM,mBAAmBA,CAAA;IACjB,IAAI,CAAC4d,4BAA4B,EAAE;IACnC,IAAI,CAAChI,EAAE,CAACkC,aAAa,EAAE;EACzB;EAEAne,MAAMA,CAAA;IACJ,IAAI,CAACqkB,gBAAgB,EAAE;IACvB,IAAI,CAACjkB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACkV,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACuH,QAAQ,GAAG,IAAI,CAAClB,UAAU,CAAC8B,WAAW,EAAE,CAACne,KAAK;IACnD,IAAI,IAAI,CAACyB,gBAAgB,CAACC,OAAO,EAAE;MACjC,IAAI,CAACD,gBAAgB,CAAC+hB,gBAAgB,EAAE;MACxC,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;MACpE,IAAI,CAAC3iB,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;KACxB,MAAM;MACL;MACA,IAAG,IAAI,CAAC7I,iBAAiB,EAAC;QACxB,IAAI,CAACvU,gBAAgB,CAAC+hB,gBAAgB,EAAE;QACxC,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;QACpE,IAAI,CAAC3iB,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;OACxB,MAAI;QACH,IAAI,CAACmG,4BAA4B,EAAE;QACnC,IAAItB,cAAc,GAAG,IAAI,CAACC,cAAc,EAAE;QAC1CD,cAAc,CAAC,UAAU,CAAC,GAAG,KAAK;QAClCA,cAAc,CAAC,YAAY,CAAC,GAAGA,cAAc,CAAC,YAAY,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QACrEF,cAAc,CAAC,cAAc,CAAC,GAAGA,cAAc,CAAC,cAAc,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QACzEF,cAAc,CAAC,gBAAgB,CAAC,GAChCA,cAAc,CAAC,gBAAgB,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;QAC1CF,cAAc,CAAC,wBAAwB,CAAC,GAAG,IAAI,CAAC9gB,sBAAsB,CAACpB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoB,sBAAsB,CAACghB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;QAC9HF,cAAc,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC7f,sBAAsB,CAACrC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqC,sBAAsB,CAAC+f,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;QAChI,IAAG,IAAI,CAACjf,wBAAwB,CAACnD,MAAM,GAAG,CAAC,EAAC;UAC1C,MAAMkE,SAAS,GAAG,IAAI,CAACf,wBAAwB,CAACsgB,OAAO,CAACtD,IAAI,IAAIA,IAAI,CAACjc,SAAS,CAAC;UAC/E;UACAge,cAAc,CAAC,sBAAsB,CAAC,GAAGhe,SAAS,GAAG,CAAC,GAAGA,SAAS,CAACke,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;;QAEnF,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC,CAAC/b,MAAM,GAAG,CAAC,EAAE;UACzC,IAAIuiB,OAAO,GAAG,EAAE;UAChBA,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACxG,QAAQ,CAAC,kBAAkB,CAAC;UAC/D,IAAIyG,cAAc,GAAGD,OAAO,CAAC,kBAAkB,CAAC,CAACE,IAAI,CAClDxH,EAAE,IAAKA,EAAE,CAACwC,YAAY,IAAIyE,cAAc,CAAC,cAAc,CAAC,CAC1D;UACD,IAAIwB,KAAK,GAAGnB,OAAO,CAAC,kBAAkB,CAAC,CAACZ,OAAO,CAACa,cAAc,CAAC;UAC/D,IAAI,CAAC1iB,UAAU,CAACC,IAAI,CAAC4iB,OAAO,CAAE1H,EAAE,IAAI;YAClCA,EAAE,CAACnT,GAAG,GAAGmT,EAAE,CAACwD,UAAU;YACtBxD,EAAE,CAAC,eAAe,CAAC,GAAGA,EAAE,CAAC,aAAa,CAAC,GAAGA,EAAE,CAAC,OAAO,CAAC;UACvD,CAAC,CAAC;UACFsH,OAAO,CAAC,kBAAkB,CAAC,CAACmB,KAAK,CAAC,GAAGxB,cAAc;UACnDK,OAAO,CAAC,kBAAkB,CAAC,GAAGA,OAAO,CAAC,kBAAkB,CAAC,CAACrC,MAAM,CAC7DC,IAAI,IAAKA,IAAI,CAACtZ,QAAQ,KAAK,KAAK,CAClC;UACD,IAAI,CAAC/G,UAAU,CAACC,IAAI,GAAG,CAAC,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,EAAE,GAAG,IAAI,CAACqc,WAAW,EAAE,GAAG,IAAI,CAACC,QAAQ,CAAC;UACvFkG,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACziB,UAAU,CAACC,IAAI;UAClD,IAAI,CAACD,UAAU,CAACC,IAAI,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,CAACmgB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACwD,MAAM,KAAK,IAAI,CAAC;UAChF,IAAI,CAAC7jB,UAAU,CAACC,IAAI,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,CAACmgB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC1Z,YAAY,KAAK,KAAK,CAAC;UACvF,IAAI,CAACsV,QAAQ,CAAC,kBAAkB,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,gBAAgB,CAAC,KAAKiH,cAAc,CAAC,cAAc,CAAC,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,EAAC0B,SAAS,CAAC,CAACviB,QAAQ,CAAC4Z,EAAE,CAACyD,YAAY,CAAE,CAAC,CAACiE,OAAO,CAAExC,IAAI,IAAI;YAC9K,IAAImD,aAAa,GAAI,IAAI,CAACrjB,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,IAAI,CAAC,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO,CAAE;YACtGkC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAAC,cAAc,CAAC,GAAGmD,aAAa,CAAC;YACxFnD,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAAC,aAAa,CAAC,GAAE,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAE0X,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACvHA,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK;YACxB,IAAI,CAAC0D,qBAAqB,CAAC1D,IAAI,CAAC,eAAe,CAAC,CAAC;UACnD,CAAC,CAAC;UAEF,IAAI,CAACpE,QAAQ,CAAC,cAAc,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,gBAAgB,CAAC,KAAKiH,cAAc,CAAC,cAAc,CAAC,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,EAAC0B,SAAS,CAAC,CAACviB,QAAQ,CAAC4Z,EAAE,CAACyD,YAAY,CAAE,CAAC,CAACiE,OAAO,CAAExC,IAAI,IAAI;YAC1K,IAAImD,aAAa,GAAI,IAAI,CAACrjB,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,IAAI,CAAC,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO,CAAE;YACtGkC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAAC,cAAc,CAAC,GAAGmD,aAAa,CAAC;YACxFnD,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAAC,aAAa,CAAC,GAAE,IAAI,CAAC3X,MAAM,CAACC,gBAAgB,CAAE0X,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACvHA,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK;YACxB,IAAI,CAAC2D,gBAAgB,CAAC3D,IAAI,CAAC,cAAc,CAAC,CAAC;UAC7C,CAAC,CAAC;UACF,IAAI4D,iBAAiB,EAACC,iBAAiB,EAACC,sBAAsB,EAACC,sBAAsB;UACrFF,iBAAiB,GAAG,IAAI,CAACjI,QAAQ,CAAC,cAAc,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,CAAE;UAC3F8I,iBAAiB,GAAG,IAAI,CAAChI,QAAQ,CAAC,aAAa,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,CAAE;UAC1FgJ,sBAAsB,GAAG,IAAI,CAAClI,QAAQ,CAAC,kBAAkB,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,IAAIA,EAAE,CAAC,cAAc,CAAC,IAAIiH,cAAc,CAAC,cAAc,CAAC,CAAC;UAC3JgC,sBAAsB,GAAG,IAAI,CAACnI,QAAQ,CAAC,kBAAkB,CAAC,CAACmE,MAAM,CAAEjF,EAAE,IAAKA,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,CAAE;UACpGsH,OAAO,CAAC,cAAc,CAAC,GAAGyB,iBAAiB;UAC3CzB,OAAO,CAAC,aAAa,CAAC,GAAGwB,iBAAiB;UAC1CxB,OAAO,CAAC,kBAAkB,CAAC,GAAGA,OAAO,CAAC,kBAAkB,CAAC,CAAC4B,MAAM,CAACF,sBAAsB,CAAC;UACxF1B,OAAO,CAAC,kBAAkB,CAAC,GAAGA,OAAO,CAAC,kBAAkB,CAAC,CAAC4B,MAAM,CAACD,sBAAsB,CAAC;UACxF,IAAIpF,GAAG,GAAG,EAAE;UACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;UACpC8B,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACoG,KAAK;UAClC/D,GAAG,CAAC,MAAM,CAAC,GAAGyD,OAAO;UACrBzD,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;UACtBA,GAAG,CAAC,UAAU,CAAC,GAAG,WAAW;UAC7B,IAAI,CAACrE,GAAG,CACLqI,UAAU,CAAChE,GAAG,CAAC,CACfE,IAAI,CAAC7kB,KAAK,EAAE,CAAC,CACb+iB,SAAS,CAAC;YACTuD,IAAI,EAAGtD,GAAG,IAAI;cACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;gBAClB,IAAI,CAAC7d,iBAAiB,GAAG,KAAK;gBAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;gBACvB,IAAI,CAAC+G,cAAc,CAAC7B,OAAO,CAAC;gBAC5B,IAAI,CAACQ,oBAAoB,EAAE;gBAC3B,IAAI,CAACva,MAAM,CAACwa,mBAAmB,CAC7B,iCAAiC,CAClC;gBACD,IAAI,CAAC7mB,KAAK,EAAE;;YAEhB,CAAC;YACD2kB,KAAK,EAAGC,GAAG,IAAI;cACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;YAClB;WACD,CAAC;SACL,MAAM;UACL,IAAI,CAACzhB,iBAAiB,GAAG,KAAK;;;;EAKtC;EAEAyjB,oBAAoBA,CAAA;IAClB,IAAI,CAAChH,QAAQ,GAAG,IAAI,CAAClB,UAAU,CAAC8B,WAAW,EAAE,CAACne,KAAK;IACnD,IAAIsgB,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;IACpC8B,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACoG,KAAK;IAClC/D,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;IACtBA,GAAG,CAAC,UAAU,CAAC,GAAG,kBAAkB;IACpC,IAAI,IAAI,CAAC7e,gBAAgB,CAACzB,KAAK,EAAE;MAC/BsgB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC7e,gBAAgB,CAACzB,KAAK,CAACif,YAAY;;IAE5D,IAAI,CAAChD,GAAG,CAAC4J,cAAc,CAACvF,GAAG,CAAC,CAAC5B,SAAS,CAAC;MACrCuD,IAAI,EAAGtD,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAImH,SAAS,GAAGnH,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC;UAC7C,IAAIoH,UAAU,GAAG,IAAI,CAACxI,QAAQ,CAAC,kBAAkB,CAAC;UAClDuI,SAAS,CAAC,kBAAkB,CAAC,CAAC3B,OAAO,CAACxC,IAAI,IAAG;YAC3C,MAAMqE,KAAK,GAAGD,UAAU,CAACE,SAAS,CAACxJ,EAAE,IAAIA,EAAE,CAACzT,cAAc,IAAI2Y,IAAI,CAAC,gBAAgB,CAAC,IAAIlF,EAAE,CAAC,eAAe,CAAC,KAAKkF,IAAI,CAAC,eAAe,CAAC,CACpI;YACD,IAAIqE,KAAK,KAAK,CAAC,CAAC,EAAE;cAChBD,UAAU,CAACC,KAAK,CAAC,GAAGrE,IAAI;aACzB,MAAM;cACLoE,UAAU,CAACG,IAAI,CAACvE,IAAI,CAAC;;UAEzB,CAAC,CAAC;UACF,IAAI,CAACpE,QAAQ,CAAC,kBAAkB,CAAC,GAAGwI,UAAU;UAC9C,IAAI,CAAC1J,UAAU,CAAC8J,WAAW,CAAC,IAAI,CAAC5I,QAAQ,CAAC;;MAE9C,CAAC;MACD+E,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA+C,gBAAgBA,CAACT,QAAQ;IACvB,MAAMK,KAAK,GAAG,IAAI,CAAC3H,QAAQ,CAAC,aAAa,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,cAAc,CAAC,KAAKkD,QAAQ,CAAC;IAC/F,IAAIuB,kBAAkB,EAACC,aAAa;IACpCA,aAAa,GAAG,IAAI,CAAC9I,QAAQ,CAAC,cAAc,CAAC,CAACmE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC,cAAc,CAAC,KAAKkD,QAAQ,CAAC;IACjGuB,kBAAkB,GAAGC,aAAa,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE5E,IAAI,KAAI;MACtD,OAAO4E,GAAG,GAAG,IAAI,CAACvc,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAACxX,WAAW,CAAC;IAC7D,CAAC,EAAE,CAAC,CAAC;IACL,IAAIqc,YAAY,GAAG,IAAI,CAACjJ,QAAQ,CAAC,aAAa,CAAC,CAAC0G,IAAI,CAAExH,EAAE,IAAKA,EAAE,CAAC,cAAc,CAAC,IAAIoI,QAAQ,CAAC;IAC5F,IAAG2B,YAAY,EAAC;MACdA,YAAY,CAAC,QAAQ,CAAC,GAAGJ,kBAAkB;MAC3CI,YAAY,CAAC,UAAU,CAAC,GAAG,KAAK;MAChC,IAAItB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC3H,QAAQ,CAAC,aAAa,CAAC,CAAC2H,KAAK,CAAC,GAAGsB,YAAY;;;EAGxD;EAEAnB,qBAAqBA,CAACR,QAAQ;IAC5B,MAAMK,KAAK,GAAG,IAAI,CAAC3H,QAAQ,CAAC,kBAAkB,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,cAAc,CAAC,KAAKkD,QAAQ,CAAC;IACpG,IAAIuB,kBAAkB,EAACC,aAAa;IACpCA,aAAa,GAAG,IAAI,CAAC9I,QAAQ,CAAC,kBAAkB,CAAC,CAACmE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC,eAAe,CAAC,KAAKkD,QAAQ,CAAC;IACtGuB,kBAAkB,GAAGC,aAAa,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE5E,IAAI,KAAI;MACtD,OAAO4E,GAAG,GAAG,IAAI,CAACvc,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAACxX,WAAW,CAAC;IAC7D,CAAC,EAAE,CAAC,CAAC;IACL,IAAIqc,YAAY,GAAG,IAAI,CAACjJ,QAAQ,CAAC,kBAAkB,CAAC,CAAC0G,IAAI,CAAExH,EAAE,IAAKA,EAAE,CAAC,cAAc,CAAC,IAAIoI,QAAQ,CAAC;IACjG,IAAG2B,YAAY,EAAC;MACdA,YAAY,CAAC,aAAa,CAAC,GAAGJ,kBAAkB;MAChDI,YAAY,CAAC,UAAU,CAAC,GAAGJ,kBAAkB,GAAGI,YAAY,CAAC,OAAO,CAAC;MACrEA,YAAY,CAAC,UAAU,CAAC,GAAG,KAAK;MAChC,IAAItB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC3H,QAAQ,CAAC,kBAAkB,CAAC,CAAC2H,KAAK,CAAC,GAAGsB,YAAY;;;EAG7D;EAGA;EACA/F,OAAOA,CAACzgB,KAAa,EAAEymB,KAAe,EAAGllB,IAAI;IAC3C,IAAImlB,WAAW,GAAG1mB,KAAK,CAACkjB,WAAW,EAAE;IACrC,IAAI,CAACyD,QAAQ,GAAGF,KAAK,CAAC/E,MAAM,CAAExiB,MAAM,IAClCA,MAAM,CAACgkB,WAAW,EAAE,CAACrgB,QAAQ,CAAC6jB,WAAW,CAAC,CAC3C;IACD,IAAI,IAAI,CAACC,QAAQ,CAACnlB,MAAM,IAAI,CAAC,EAAE;MAC7B,IAAGD,IAAI,IAAI,aAAa,EAAC;QACvB;QACA,IAAI,CAAColB,QAAQ,GAAG,CAAC,eAAe,CAAC;OAClC,MAAI;QACH,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAI,CAAC9J,QAAQ,GAAG7c,KAAK,GAAG,GAAG,CAAC;;;IAGjD,OAAO,IAAI,CAAC2mB,QAAQ;EACtB;EAEAhoB,SAASA,CAACioB,KAAK;IACb,IAAIC,OAAO,GAAG,IAAI,CAACxK,UAAU,CAACyK,qBAAqB,CACjDF,KAAK,CAACG,MAAM,CAAC/mB,KAAK,EAClB,kBAAkB,CACnB;IACD,IAAI6mB,OAAO,EAAE;MACX,IAAI,CAAC3mB,eAAe,GAAG,IAAI;KAC5B,MAAM;MACL,IAAI,CAACA,eAAe,GAAG,KAAK;;EAEhC;EAEAjB,cAAcA,CAAC+nB,IAAY,EAAE9nB,MAAW;IACtC,IAAI2nB,OAAO,GAAG,IAAI,CAACxK,UAAU,CAACyK,qBAAqB,CACjD5nB,MAAM,CAACc,KAAK,EACZ,kBAAkB,CACnB;IACD,IAAI6mB,OAAO,EAAE;MACX,IAAI,CAAC3mB,eAAe,GAAG,IAAI;KAC5B,MAAM;MACL,IAAI,CAACA,eAAe,GAAG,KAAK;;IAE9B,IAAIhB,MAAM,CAACc,KAAK,CAACmjB,OAAO,CAAC,IAAI,CAACtG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,IAAI,CAAC/d,SAAS,CAACkoB,IAAI,CAAC;;EAExB;EAEAloB,SAASA,CAACkoB,IAAY;IACpB,IAAI,CAAC/mB,OAAO,GAAG,IAAI;IACnB,IAAI+mB,IAAI,IAAI,SAAS,EAAE;MACrB,IAAI,CAAClnB,eAAe,CAACmnB,KAAK,EAAE;KAC7B,MAAM,IAAID,IAAI,IAAI,kBAAkB,EAAE;MACrC,IAAIE,mBAAmB,GAAG,IAAI,CAAC7K,UAAU,CAACyK,qBAAqB,CAC7D,IAAI,CAAChnB,eAAe,CAACE,KAAK,EAC1B,kBAAkB,CACnB;MACD,IAAIknB,mBAAmB,EAAE;QACvB,IAAI,CAAC7Q,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC8Q,gBAAgB,CAACD,mBAAmB,CAAC;OAC3C,MAAM;QACL,IAAI,CAACE,YAAY,CAAC,qBAAqB,CAAC;;MAE1C,IAAI,CAAC3lB,gBAAgB,CAAC4lB,QAAQ,CAAC,cAAc,CAAC,CAAC5C,UAAU,CACvD,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAACxnB,eAAe,CAACE,KAAK,CAAC,CACxD;MACD,IAAI,CAACF,eAAe,CAACmnB,KAAK,EAAE;MAC5B,IAAI,CAAC9G,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC7e,UAAU,CAACC,IAAI,GAAG,EAAE;IACzB,IAAI,CAACtB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACke,WAAW,EAAE;EACpB;EAEAiJ,YAAYA,CAACG,IAAI;IAAA,IAAAC,KAAA;IACf,IAAIlH,GAAG,GAAG,EAAE;IACZ,IAAI/e,IAAI;IACR+e,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;IACpC8B,GAAG,CAAC,MAAM,CAAC,GAAGiH,IAAI;IAClB,IAAI,CAACtL,GAAG,CACLwL,OAAO,CAACnH,GAAG,CAAC,CACZE,IAAI,CAAC7kB,KAAK,EAAE,CAAC,CACb+iB,SAAS,CAAC;MACTuD,IAAI;QAAA,IAAAyF,IAAA,GAAAC,iBAAA,CAAE,WAAOhJ,GAAG,EAAI;UAClB,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;YAClBpd,IAAI,GAAGod,GAAG,CAAC,MAAM,CAAC;YAClB6I,KAAI,CAAC/lB,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACoL,QAAQ,CAAC9f,IAAI,CAAC;;QAE5D,CAAC;QAAA,gBALD0gB,IAAIA,CAAA2F,EAAA;UAAA,OAAAF,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA;KAML,CAAC;EACN;EAEAX,gBAAgBA,CAACD,mBAAmB;IAClC,IACE,CAACa,KAAK,CAACC,OAAO,CAACd,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,IACrD,CAACa,KAAK,CAACC,OAAO,CAACd,mBAAmB,CAAC,cAAc,CAAC,CAAC,IACnD,CAACa,KAAK,CAACC,OAAO,CAACd,mBAAmB,CAAC,YAAY,CAAC,CAAC,EACjD;MACAA,mBAAmB,CAAC,gBAAgB,CAAC,GACnCA,mBAAmB,CAAC,gBAAgB,CAAC,CAACzhB,KAAK,CAAC,GAAG,CAAC;MAClDyhB,mBAAmB,CAAC,cAAc,CAAC,GACjCA,mBAAmB,CAAC,cAAc,CAAC,CAACzhB,KAAK,CAAC,GAAG,CAAC;MAChDyhB,mBAAmB,CAAC,YAAY,CAAC,GAC/BA,mBAAmB,CAAC,YAAY,CAAC,CAACzhB,KAAK,CAAC,GAAG,CAAC;;IAEhD,IAAIyQ,QAAQ,GAAG,IAAI,CAAClM,MAAM,CAACC,gBAAgB,CAACid,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC5E,IAAIzH,OAAO,GAAGyH,mBAAmB,CAAC1F,cAAc,CAAC,SAAS,CAAC,IAAI0F,mBAAmB,CAAC,SAAS,CAAC,GAAGA,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC;IAElI,IAAI,CAAC5jB,iBAAiB,GAAG4jB,mBAAmB,CAAC,YAAY,CAAC;IAC1D,IAAI,CAAC9iB,iBAAiB,GAAG8iB,mBAAmB,CAAC,cAAc,CAAC;IAC5D,IAAI,CAAC9hB,mBAAmB,GAAG8hB,mBAAmB,CAAC,gBAAgB,CAAC;IAChE,IAAI,IAAI,CAACtI,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,IAAIwG,SAAS,IAAM,IAAI,CAACxG,qBAAqB,CAACqJ,kBAAkB,EAAC;MAC3H,IAAI,CAACpkB,sBAAsB,CAACqiB,IAAI,CAAC,GAAG,IAAI,CAACtH,qBAAqB,CAACqJ,kBAAkB,CAACC,kBAAkB,CAAC;MACrG,IAAI,CAACvjB,wBAAwB,CAACuhB,IAAI,CAAC,GAAG,IAAI,CAACtH,qBAAqB,CAACqJ,kBAAkB,CAACE,oBAAoB,CAAC;MACzG,IAAI,CAACvlB,sBAAsB,CAACsjB,IAAI,CAAC,GAAG,IAAI,CAACtH,qBAAqB,CAACqJ,kBAAkB,CAACG,sBAAsB,CAAC;;IAE3G,IAAI,CAAC3mB,gBAAgB,CAACgjB,UAAU,CAAC;MAC/BnG,QAAQ,EAAE4I,mBAAmB,CAAC,UAAU,CAAC;MACzClI,WAAW,EAAEkI,mBAAmB,CAAC,aAAa,CAAC;MAC/CjI,YAAY,EAAEiI,mBAAmB,CAAC,cAAc,CAAC;MACjDhI,YAAY,EAAEgI,mBAAmB,CAAC,cAAc,CAAC;MACjD/H,UAAU,EAAE+H,mBAAmB,CAAC,YAAY,CAAC;MAC7C9H,QAAQ,EAAE,WAAW;MACrBC,UAAU,EAAE6H,mBAAmB,CAAC,YAAY,CAAC;MAC7C5H,YAAY,EAAE4H,mBAAmB,CAAC,cAAc,CAAC;MACjDzQ,GAAG,EAAEyQ,mBAAmB,CAAC,KAAK,CAAC;MAC/B/c,WAAW,EAAE+c,mBAAmB,CAAC,aAAa,CAAC;MAC/CzH,OAAO,EAAEA,OAAO;MAChB7V,KAAK,EAAEsd,mBAAmB,CAAC,OAAO,CAAC;MACnChR,QAAQ,EAAEA,QAAQ;MAClB3L,IAAI,EAAE,IAAI,CAACP,MAAM,CAACC,gBAAgB,CAACid,mBAAmB,CAAC,MAAM,CAAC,CAAC;MAC/Dxc,SAAS,EAAE,IAAI,CAACV,MAAM,CAACC,gBAAgB,CAACid,mBAAmB,CAAC,WAAW,CAAC,CAAC;MACzEtH,YAAY,EAAE,CAAC,IAAI,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC/c,QAAQ,CAACqkB,mBAAmB,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;MAC1GrH,QAAQ,EAAEqH,mBAAmB,CAAC,UAAU;KACzC,CAAC;IAEF,IAAI,CAACrmB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACoP,cAAc,CAACiX,mBAAmB,CAAC,cAAc,CAAC,CAAC;IACxD,IAAI,CAACzlB,gBAAgB,CAClBwU,GAAG,CAAC,gBAAgB,CAAC,CACrBwO,UAAU,CAACyC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;EACtD;EAEAI,sBAAsBA,CAACpoB,MAAM;IAC3B,IAAIA,MAAM,CAACmpB,UAAU,CAAC,IAAI,CAACxL,QAAQ,CAAC,EAAE;MACpC3d,MAAM,GAAGA,MAAM,CAACopB,SAAS,CAAC,IAAI,CAACzL,QAAQ,CAACrb,MAAM,EAAEtC,MAAM,CAACsC,MAAM,GAAG,CAAC,CAAC;;IAEpE,OAAOtC,MAAM;EACf;EAEAvB,KAAKA,CAAA;IACH,IAAI,CAAC2D,UAAU,CAACC,IAAI,GAAG,EAAE;IACzB,IAAI,CAACqb,iBAAiB,CAAC2L,aAAa,CAAC,kBAAkB,CAAC;IACxD,IAAI,CAACpM,MAAM,CAACqM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;IACzC,IAAI,CAACpM,MAAM,CAACqM,QAAQ,EAAE;EACxB;EAEAhhB,OAAOA,CAACihB,OAAO,EAAEC,MAAM;IACrB,MAAMhH,IAAI,GAAG,IAAI,CAACF,QAAQ,CAACwC,IAAI,CAC5BtC,IAAI,IACHA,IAAI,CAACC,QAAQ,CAACsB,WAAW,EAAE,KAAKwF,OAAO,CAACxiB,cAAc,CAACgd,WAAW,EAAE,CACvE;IACD,IAAI,CAACld,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,mBAAmB,CAACwe,UAAU,CAAC;MAClCzb,cAAc,EAAE0f,OAAO,CAAC,gBAAgB,CAAC;MACzC;MACAxiB,cAAc,EAAEwiB,OAAO,CAAC,gBAAgB,CAAC;MACzCtf,aAAa,EAAEsf,OAAO,CAAC,eAAe,CAAC;MACvCxf,aAAa,EAAEwf,OAAO,CAAC,eAAe,CAAC;MACvCjS,GAAG,EAAEiS,OAAO,CAAC,KAAK,CAAC;MACnBzI,UAAU,EAAEyI,OAAO,CAAC,YAAY,CAAC;MACjC9e,KAAK,EAAE8e,OAAO,CAAC,OAAO,CAAC;MACvBne,IAAI,EAAE,IAAI,CAACP,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAAC,MAAM,CAAC,CAAC;MACnDhe,SAAS,EAAE,IAAI,CAACV,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAAC,WAAW,CAAC,CAAC;MAC7D1I,aAAa,EAAE0I,OAAO,CAAC,eAAe,CAAC;MACvC5e,IAAI,EAAE4e,OAAO,CAAC,MAAM,CAAC;MACrBve,WAAW,EAAEue,OAAO,CAAC,aAAa,CAAC;MACnC9I,YAAY,EAAC,CAAC,IAAI,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC/c,QAAQ,CAAC6lB,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;MAC7F7I,QAAQ,EAAE6I,OAAO,CAAC,UAAU;KAC7B,CAAC;IAEF,IAAIA,OAAO,CAAClH,cAAc,CAAC,cAAc,CAAC,EAAE;MAC1C,IAAI,CAACvb,mBAAmB,CAACgQ,GAAG,CAAC,cAAc,CAAC,CAACoL,QAAQ,CAACqH,OAAO,CAAC,cAAc,CAAC,CAAC;;IAG/E,IAAI,CAACA,OAAO,CAAC,eAAe,CAAC,EAAE;MAC9B,IAAI,CAACziB,mBAAmB,CAACgQ,GAAG,CAAC,eAAe,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC;;IAG3D,IAAI,CAACqH,OAAO,CAAC,MAAM,CAAC,EAAE;MACpB,IAAI,CAACziB,mBAAmB,CAACgQ,GAAG,CAAC,MAAM,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC;;IAGlD,IAAIuH,SAAS,GAAG,IAAI,CAACxM,MAAM,CAACyM,IAAI,CAACF,MAAM,EAAE;MACvCG,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE;KACX,CAAC;IACFH,SAAS,CAACI,WAAW,EAAE,CAACtK,SAAS,CAAEuK,MAAM,IAAI,CAAE,CAAC,CAAC;IACjD,IAAI,CAACC,WAAW,GAAGN,SAAS;IAC5B,IAAI,CAAClL,UAAU,GAAG,KAAK;EACzB;EAEAzF,cAAcA,CAAA;IACZ,IAAI,CAACjS,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACkjB,WAAW,CAACvrB,KAAK,EAAE;EAC1B;EAEA6W,qBAAqBA,CAAA;IACnB,IAAI,CAACvO,mBAAmB,CAACwe,UAAU,CAAC;MAClCrb,aAAa,EAAE,IAAI,CAAC3H,gBAAgB,CAACzB,KAAK,CAACif,YAAY;MACvD/V,aAAa,EAAE,IAAI,CAACzH,gBAAgB,CAACzB,KAAK,CAACkf;KAC5C,CAAC;IACF,IAAI,IAAI,CAACjZ,mBAAmB,CAACvE,OAAO,EAAE;MACpC,IAAI,CAACuE,mBAAmB,CAACud,gBAAgB,EAAE;MAC3C,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;MACpE,IAAI,CAAC9G,EAAE,CAACkC,aAAa,EAAE;KACxB,MAAM;MACL,IAAI,IAAI,CAAC5Y,mBAAmB,CAACjG,KAAK,CAACmK,WAAW,GAAG,CAAC,EAAE;QAClD,IAAIzJ,MAAM,GAAG,IAAI,CAACyoB,cAAc,EAAE;QAClCzoB,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK;QAC1B,IAAI0oB,YAAY,GAAG,IAAI,CAAC9nB,UAAU,CAACC,IAAI,CAAC0iB,IAAI,CACzCxH,EAAE,IAAKA,EAAE,CAACzT,cAAc,KAAKtI,MAAM,CAAC,gBAAgB,CAAC,CACvD;QACD,IAAI,CAAC0oB,YAAY,EAAE;UACjBvF,MAAM,CAACwF,OAAO,CAAC3oB,MAAM,CAAC,CAACyjB,OAAO,CAAC,CAAC,CAAC/D,GAAG,EAAEpgB,KAAK,CAAC,KAAI;YAC9C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKolB,SAAS,IAAIplB,KAAK,KAAK,EAAE,EAAE;cACvD;;YAEJ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;cAC7BU,MAAM,CAAC0f,GAAG,CAAC,GAAG,IAAI,CAACpW,MAAM,CAACC,gBAAgB,CAACjK,KAAK,CAAC;;UAErD,CAAC,CAAC;UACF,IAAI,CAACsB,UAAU,CAACC,IAAI,CAAC6iB,OAAO,CAAC1jB,MAAM,CAAC;UACpC,IAAI,CAACY,UAAU,CAACuhB,SAAS,GAAG,IAAI,CAACA,SAAS;UAC1C,IAAI,CAAC7c,SAAS,GAAG,KAAK;UACtB,IAAI,CAACob,gBAAgB,EAAE;UACvB;UACA,IAAI,CAACnb,mBAAmB,CAACghB,KAAK,EAAE;UAChC,IAAI,CAAChhB,mBAAmB,CAACwe,UAAU,CAAC;YAClCrb,aAAa,EAAE,IAAI,CAAC3H,gBAAgB,CAACzB,KAAK,CAACif,YAAY;YACvD/V,aAAa,EAAE,IAAI,CAACzH,gBAAgB,CAACzB,KAAK,CAACkf,YAAY;YACvDtV,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAAC0f,QAAQ,GAAG,IAAI,CAAChoB,UAAU,CAACC,IAAI;UACpC,IAAI,CAACgoB,SAAS,EAAE;SACjB,MAAM;UACL,IAAI,CAACvf,MAAM,CAACwf,mBAAmB,CAAC,4BAA4B,CAAC;;OAEhE,MAAM;QACL,IAAI,CAACvjB,mBAAmB,CAACud,gBAAgB,EAAE;QAC3C,IAAI,CAAC9M,eAAe,GAAG,IAAI;QAC3B;QACA,IAAI,CAACiG,EAAE,CAACkC,aAAa,EAAE;;MAEzB,IAAI,CAAClC,EAAE,CAACkC,aAAa,EAAE;;IAEzB,IAAI,CAAC0K,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP1F,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7d,mBAAmB,CAACohB,QAAQ,CAAC,CAAClD,OAAO,CAAC/D,GAAG,IAAG;MAC3D,IAAI,CAACna,mBAAmB,CAACgQ,GAAG,CAACmK,GAAG,CAAC,EAAEqJ,SAAS,CAAC,IAAI,CAAC;IACpD,CAAC,CAAC;EACJ;EAEA1S,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAAC9Q,mBAAmB,CAACvE,OAAO,EAAE;MACpC,IAAI,CAACuE,mBAAmB,CAACud,gBAAgB,EAAE;MAC3C,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;MACpE,IAAI,CAAC9G,EAAE,CAACkC,aAAa,EAAE;KACxB,MAAM;MACL,IAAI6K,cAAc,GAAG,IAAI,CAACP,cAAc,EAAE;MAC1CO,cAAc,CAAC,UAAU,CAAC,GAAG,KAAK;MAClC,IAAIC,eAAe,GAAG,IAAI,CAACroB,UAAU,CAACC,IAAI,CAAC0iB,IAAI,CAC5CxH,EAAE,IACDA,EAAE,CAACzT,cAAc,IAAI0gB,cAAc,CAAC,gBAAgB,CAAC,IACrDjN,EAAE,CAACrT,aAAa,IAAIsgB,cAAc,CAAC,eAAe,CAAC,CACtD;MACD,IAAIC,eAAe,EAAE;QACnB,IAAIpoB,IAAI,GAAG,IAAI,CAAC+nB,QAAQ,CAAC5H,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC1Z,YAAY,KAAK,KAAK,CAAE;QACrE,IAAI2hB,YAAY,GAAS,EAAE;QAC3BA,YAAY,GAAG,IAAI,CAAC/L,QAAQ,CAAC6D,MAAM,CAACjF,EAAE,IACpCA,EAAE,CAACzT,cAAc,KAAK0gB,cAAc,CAAC,gBAAgB,CAAC,IACtDjN,EAAE,CAACrT,aAAa,KAAKsgB,cAAc,CAAC,eAAe,CAAC,CACrD;QACD,IAAIE,YAAY,CAACpoB,MAAM,GAAG,CAAC,EAAE;UAC3BooB,YAAY,CAAC,CAAC,CAAC,CAAC3hB,YAAY,GAAG,IAAI;UACnC2hB,YAAY,CAAC,CAAC,CAAC,CAAC1jB,cAAc,GAAG0jB,YAAY,CAAC,CAAC,CAAC,CAAC1jB,cAAc,CAACqY,WAAW,EAAE;UAC7E,IAAI,CAACV,QAAQ,CAACgM,GAAG,CAACD,YAAY,CAAC;;QAEjC,IAAI,CAACtoB,UAAU,CAACC,IAAI,GAAG,CAAC,GAAGA,IAAI,EAAE,GAAGqoB,YAAY,CAAC;QACjD,IAAI1E,KAAK,GAAG,IAAI,CAAC5jB,UAAU,CAACC,IAAI,CAAC4hB,OAAO,CAACwG,eAAe,CAAC;QACzD,IAAI,CAACroB,UAAU,CAACC,IAAI,CAAC2jB,KAAK,CAAC,GAAGwE,cAAc;QAC5C,IAAII,KAAK,GAAG,IAAI,CAACxoB,UAAU,CAACC,IAAI;QAChC;QACA,IAAI,CAACD,UAAU,CAACC,IAAI,GAAGuoB,KAAK,CAACpI,MAAM,CAACC,IAAI,IACtC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CAClE;QACD,IAAI,CAACoG,QAAQ,GAAG,IAAI,CAAChoB,UAAU,CAACC,IAAI;QACpC,IAAIwoB,OAAO,GAAGD,KAAK,CAACpI,MAAM,CAACC,IAAI,IAC7B,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CACjE;QACD;QACA,IAAI,CAACrF,QAAQ,CAACqI,IAAI,CAAC,GAAG6D,OAAO,CAAC;QAC9B,IAAI,CAACzoB,UAAU,CAACuhB,SAAS,GAAG,IAAI,CAACA,SAAS;QAC1C,IAAI,CAACzB,gBAAgB,EAAE;QACvB,IAAI,CAACpX,MAAM,CAACwa,mBAAmB,CAAC,sBAAsB,CAAC;QACvD,IAAI,CAACve,mBAAmB,CAACghB,KAAK,EAAE;QAChC,IAAI,CAACsC,SAAS,EAAE;QAChB,IAAI,CAACtjB,mBAAmB,CAACwe,UAAU,CAAC;UAClCrb,aAAa,EAAE,IAAI,CAAC3H,gBAAgB,CAACzB,KAAK,CAACif,YAAY;UACvD/V,aAAa,EAAE,IAAI,CAACzH,gBAAgB,CAACzB,KAAK,CAACkf,YAAY;UACvDtV,KAAK,EAAE;SACR,CAAC;QACF,IAAI,CAACqO,cAAc,EAAE;QACrB,IAAI,CAACxJ,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACkO,EAAE,CAACkC,aAAa,EAAE;;;EAG7B;EAEAmL,MAAMA,CAAA;IACJ,IAAI,CAAC7J,WAAW,GAAG,KAAK;IACxB,IAAI,CAACnD,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC/W,mBAAmB,CAACghB,KAAK,EAAE;IAChCgD,UAAU,CAAC,MAAK;MACd,MAAMC,eAAe,GAAG,IAAI,CAACzN,EAAE,CAAC0N,aAAa,CAACC,aAAa,CAAC,UAAU,CAAC;MACvE,IAAIF,eAAe,EAAE;QACnBA,eAAe,CAACG,cAAc,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE,CAAC;;IAE1E,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5G,cAAcA,CAAA;IACZ,MAAM6G,OAAO,GAAG,CACd,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,aAAa,EAAE,aAAa,CAAC,EAC9B,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EACpC,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,CAAC,aAAa,EAAE,aAAa,CAAC,EAC9B,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,WAAW,EAAE,WAAW,CAAC,EAC1B,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,CAAC,mBAAmB,EAAE,cAAc,CAAC,EACrC,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB,CAAC,UAAU,EAAE,UAAU,CAAC,CACzB;IAED,MAAMC,iBAAiB,GAAG,EAAE;IAC5BD,OAAO,CAACrG,OAAO,CAAE/D,GAAG,IAAI;MACtB,IAAIpgB,KAAK,GAAG,IAAI,CAACyB,gBAAgB,CAACzB,KAAK,CAACogB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/C,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE;QACvBqK,iBAAiB,CAACrK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpgB,KAAK,IAAI,CAAC;OACvC,MAAM,IAAIogB,GAAG,CAAC,CAAC,CAAC,IAAI,SAAS,EAAC;QAC7B,IAAI,IAAI,CAAC3e,gBAAgB,CAACzB,KAAK,CAAC2f,IAAI,KAAK,OAAO,EAAE;UAChD3f,KAAK,GAAG,IAAI,CAACyB,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAClW,KAAK;;QAEpDyqB,iBAAiB,CAACrK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI,IAAI,CAACpW,MAAM,CAACC,gBAAgB,CAACjK,KAAK,CAAC,IAAI,CAAC;OACtE,MAAM;QACLyqB,iBAAiB,CAACrK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpgB,KAAK,IAAI,EAAE;;IAE3C,CAAC,CAAC;IACF,OAAOyqB,iBAAiB;EAC1B;EAEAtB,cAAcA,CAAA;IACZ,MAAMqB,OAAO,GAAG,CACd,CAAC,eAAe,EAAE,eAAe,CAAC,EAClC,CAAC,eAAe,EAAE,eAAe,CAAC,EAClC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EACpC,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,CAAC,eAAe,EAAE,eAAe,CAAC,EAClC,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,aAAa,EAAE,aAAa,CAAC,EAC9B,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,WAAW,EAAE,WAAW,CAAC,EAC1B,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,cAAc,EAAE,cAAc,CAAC,CACjC;IACD,MAAME,oBAAoB,GAAG,EAAE;IAC/BF,OAAO,CAACrG,OAAO,CAAE/D,GAAG,IAAI;MACtB,IAAIpgB,KAAK,GAAG,IAAI,CAACiG,mBAAmB,CAACjG,KAAK,CAACogB,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QAC9B,IAAIuK,YAAY,GAAG,IAAI,CAAC3gB,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAAChE,mBAAmB,CAACjG,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAACiG,mBAAmB,CAACjG,KAAK,CAAC,OAAO,CAAC,CAAE;QACzI0qB,oBAAoB,CAACtK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGuK,YAAY;OAC5C,MAAM;QACLD,oBAAoB,CAACtK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpgB,KAAK,IAAI,EAAE;;IAE9C,CAAC,CAAC;IACF,OAAO0qB,oBAAoB;EAC7B;EAEAE,WAAWA,CAAClE,WAAgB;IAC1BA,WAAW,GAAGA,WAAW,CAACK,MAAM,CAAC/mB,KAAK;IACtC0mB,WAAW,GAAGA,WAAW,CAACmE,IAAI,EAAE;IAChCnE,WAAW,GAAGA,WAAW,CAACxD,WAAW,EAAE;IACvC,IAAI,CAAC5hB,UAAU,CAACogB,MAAM,GAAGgF,WAAW;EACtC;EAEAoE,eAAeA,CAAA;IACb,MAAM9J,OAAO,GAAG,IAAI,CAACvf,gBAAgB,CAAC4lB,QAAQ,CAAC,gBAAgB,CAAC;IAChE,IAAI9lB,IAAI,GAAG,CACT,GAAG,IAAI,CAAC4b,kBAAkB,CAACvhB,GAAG,CAAEmvB,QAAQ,IAAKA,QAAQ,CAACrlB,SAAS,CAAC,CACjE;IACD,MAAMslB,cAAc,GAAG,EAAE,CAACrF,MAAM,CAAC,GAAGpkB,IAAI,CAAC;IACzC,IAAIyf,OAAO,CAAChhB,KAAK,CAACwB,MAAM,GAAG,CAAC,KAAKwpB,cAAc,CAACxpB,MAAM,EAAE;MACtDwf,OAAO,CAACK,QAAQ,CAAC,EAAE,CAAC;KACrB,MAAM;MACLL,OAAO,CAACK,QAAQ,CAAC2J,cAAc,CAAC;;EAEpC;EAEA5a,yBAAyBA,CAAA;IACvB,MAAM4Q,OAAO,GAAG,IAAI,CAACvf,gBAAgB,CAAC4lB,QAAQ,CAAC,cAAc,CAAC;IAC9D,IAAIrG,OAAO,CAAChhB,KAAK,CAACwB,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC6b,UAAU,CAAC7b,MAAM,EAAE;MACvDwf,OAAO,CAACK,QAAQ,CAAC,IAAI,CAACjd,iBAAiB,CAAC;KACzC,MAAM;MACL4c,OAAO,CAACK,QAAQ,CAAC,IAAI,CAAChE,UAAU,CAAC;;IAEnC,IAAI,CAACpN,cAAc,CAAC,IAAI,CAACxO,gBAAgB,CAACzB,KAAK,CAACsf,YAAY,CAAC;EAC/D;EAEAzP,uBAAuBA,CAAA;IACrB,MAAMmR,OAAO,GAAG,IAAI,CAACvf,gBAAgB,CAAC4lB,QAAQ,CAAC,YAAY,CAAC;IAC5D,IAAIrG,OAAO,CAAChhB,KAAK,CAACwB,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC6b,UAAU,CAAC7b,MAAM,EAAE;MACvDwf,OAAO,CAACK,QAAQ,CAAC,IAAI,CAAC/d,iBAAiB,CAAC;KACzC,MAAM;MACL0d,OAAO,CAACK,QAAQ,CAAC,IAAI,CAAChE,UAAU,CAAC;;EAErC;EAEAnf,YAAYA,CAACwoB,WAAW;IACtBA,WAAW,GAAGA,WAAW,CAACK,MAAM,CAAC/mB,KAAK;IACtC0mB,WAAW,GAAGA,WAAW,CAACmE,IAAI,EAAE,CAAC3H,WAAW,EAAE;IAC9C,IAAI,CAACnJ,YAAY,GAAG,IAAI,CAACkR,YAAY,CAACvJ,MAAM,CAAEC,IAAI,IAChDA,IAAI,CAACuB,WAAW,EAAE,CAACrgB,QAAQ,CAAC6jB,WAAW,CAAC,CACzC;EACH;EAEA7V,UAAUA,CAACqa,GAAG,EAAEC,QAAQ;IACtB,IAAI,IAAI,CAAC1pB,gBAAgB,CAACzB,KAAK,CAAC4J,KAAK,GAAG,CAAC,EAAE;MACzC,IAAI,CAACnI,gBAAgB,CAACwU,GAAG,CAACkV,QAAQ,CAAC,CAAC9J,QAAQ,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACrX,MAAM,CAACka,gBAAgB,CAAC,qCAAqC,CAAC;KACpE,MAAM,IAAI,IAAI,CAACziB,gBAAgB,CAACzB,KAAK,CAAC4J,KAAK,GAAG,CAAC,EAAE;MAChD,IAAI+gB,YAAY,GAAG,IAAI,CAAC3gB,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAACxI,gBAAgB,CAACzB,KAAK,CAAC4J,KAAK,GAAG,IAAI,CAACnI,gBAAgB,CAACzB,KAAK,CAACmK,WAAW,CAAC;MAC5H,IAAI,CAAC1I,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACoL,QAAQ,CAACsJ,YAAY,CAAC;;EAEhE;EAEA3W,UAAUA,CAACkX,GAAG,EAAEC,QAAQ;IACtB,IAAI,IAAI,CAACllB,mBAAmB,CAACjG,KAAK,CAAC4J,KAAK,GAAG,CAAC,EAAE;MAC5C,IAAI,CAAC3D,mBAAmB,CAACgQ,GAAG,CAACkV,QAAQ,CAAC,CAAC9J,QAAQ,CAAC,CAAC,CAAC;MAClD,IAAI,CAACrX,MAAM,CAACka,gBAAgB,CAAC,qCAAqC,CAAC;KACpE,MAAM,IAAI,IAAI,CAACje,mBAAmB,CAACjG,KAAK,CAAC4J,KAAK,GAAG,CAAC,EAAE;MACnD,IAAIoW,aAAa,GAAI,IAAI,CAAC/Z,mBAAmB,CAACjG,KAAK,CAACmK,WAAW,GAAG,IAAI,CAAClE,mBAAmB,CAACjG,KAAK,CAAC4J,KAAK;MACtG,IAAI,CAAC3D,mBAAmB,CAACgQ,GAAG,CAAC,eAAe,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAAE+V,aAAc,CAAC,CAAC;MACrG,IAAIuG,GAAG,GACL,IAAI,CAACtgB,mBAAmB,CAACjG,KAAK,CAACggB,aAAa,GAC5C,IAAI,CAAC/Z,mBAAmB,CAACjG,KAAK,CAACuK,IAChC;MACD,IAAI,CAACtE,mBAAmB,CAACgQ,GAAG,CAAC,WAAW,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAACsc,GAAG,CAAC,CAAC;;EAEzF;EAEAnT,kBAAkBA,CAAC8X,GAAG;IACpB,IAAI,CAACxU,eAAe,GAAG,KAAK;IAC1B,IAAI,CAACzQ,mBAAmB,CAACgQ,GAAG,CAAC,eAAe,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACpb,mBAAmB,CAACjG,KAAK,CAACmK,WAAW,GAAG,IAAI,CAAClE,mBAAmB,CAACjG,KAAK,CAAC4J,KAAK,CAAC;IACzI,IAAI2c,GAAG,GACL,IAAI,CAACtgB,mBAAmB,CAACjG,KAAK,CAACggB,aAAa,GAC5C,IAAI,CAAC/Z,mBAAmB,CAACjG,KAAK,CAACuK,IAChC;IACD,IAAI,CAACtE,mBAAmB,CAACgQ,GAAG,CAAC,WAAW,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAACsc,GAAG,CAAC,CAAC;EACzF;EAEApM,eAAeA,CAAA;IACb,IAAG,IAAI,CAAC2C,aAAa,IAAI,IAAI,EAAC;MAC5B,IAAI,CAACnf,KAAK,EAAE;MACZ,IAAI,IAAI,CAACirB,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACjrB,KAAK,EAAE;;KAEzB,MAAI;MACH,IAAI,IAAI,CAACirB,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACjrB,KAAK,EAAE;;;EAG5B;EAEAmjB,aAAaA,CAAA;IACX;IACA;IACA;IACM,IAAI,CAACsK,YAAY,GAAG,IAAI,CAAC/O,UAAU,CAACyE,aAAa,EAAE,CAAC9gB,KAAK;IACzD,IAAIqrB,YAAY,GAAGxH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzH,UAAU,CAACyE,aAAa,EAAE,CAAC9gB,KAAK,CAAC,CAACpE,GAAG,CAAC0iB,QAAQ,IAAIA,QAAQ,CAACC,WAAW,EAAE,CAAC;IAC7G,IAAI+M,MAAM,GAAG,CAAC,GAAG,IAAI,CAAClN,WAAW,EAAE,GAAGiN,YAAY,CAAC;IACnD,IAAI,CAACE,UAAU,GAAG,CAAC,GAAG,IAAIvJ,GAAG,CAACsJ,MAAM,CAAC,CAAC;IACtC,IAAI,CAAC/V,OAAO,GAAG,IAAI,CAAC9T,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAACurB,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;IACvJ;IACJ;IACA;IACA;EACF;;EAEAC,gBAAgBA,CAACN,GAAG;IAClB,IAAI,CAACzpB,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACoL,QAAQ,CAAC,EAAE,CAAC;IACrD,IAAI9f,IAAI,GAAG,IAAI,CAACgc,QAAQ,CAAC,kBAAkB,CAAC,CAACmE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACrD,QAAQ,KAAK4M,GAAG,CAAC;IAClF,IAAI,CAACO,cAAc,GAAGlqB,IAAI,CAAC3F,GAAG,CAAC8vB,MAAM,IAAIA,MAAM,CAAC1M,WAAW,CAAC;IAC5D,IAAI,EAAEkM,GAAG,IAAI,IAAI,CAACE,YAAY,CAAC,EAAE;MAC/B,IAAI,CAACA,YAAY,CAACF,GAAG,CAAC,GAAG,EAAE;;IAE7B,IAAIS,SAAS,GAAG,CAAC,GAAG,IAAI,CAACF,cAAc,EAAE,GAAG,IAAI,CAACL,YAAY,CAACF,GAAG,CAAC,CAAC;IACnE,IAAI,CAACU,aAAa,GAAG,CAAC,GAAG,IAAI5J,GAAG,CAAC2J,SAAS,CAAC,CAAC;IAC5C,IAAI,CAAClW,UAAU,GAAG,IAAI,CAAChU,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAAC4rB,aAAa,EAAG,EAAE,CAAC,CAAC,CAAC;EACjK;EAEA1c,iBAAiBA,CAAChQ,MAAW;IAC3B,IAAIA,MAAM,CAACc,KAAK,CAACmjB,OAAO,CAAC,IAAI,CAACtG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,IAAI,CAAC9N,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACyc,gBAAgB,CAAC,IAAI,CAAC/pB,gBAAgB,CAACzB,KAAK,CAACse,QAAQ,CAAC;;IAE7D,IAAI,CAAC/I,OAAO,GAAG,IAAI,CAAC9T,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAACurB,UAAU,EAAG,EAAE,CAAC,CAAC,CAAC;EACxJ;EAEAxc,YAAYA,CAAA;IACV,IAAI,CAACtN,gBAAgB,CAAC4lB,QAAQ,CAAC,UAAU,CAAC,CAAC5C,UAAU,CAAC,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC7lB,gBAAgB,CAACzB,KAAK,CAACse,QAAQ,CAAC,CAAC;IACxH,IAAI,CAACkN,gBAAgB,CAAC,IAAI,CAAC/pB,gBAAgB,CAACzB,KAAK,CAACse,QAAQ,CAAC;EAC7D;EAEA7O,oBAAoBA,CAACvQ,MAAW;IAC9B,IAAIA,MAAM,CAACc,KAAK,CAACmjB,OAAO,CAAC,IAAI,CAACtG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,IAAI,CAACvN,eAAe,EAAE;;IAExB,IAAI,CAACmG,UAAU,GAAG,IAAI,CAAChU,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACsK,YAAY,CAACC,IAAI,CAAC3kB,SAAS,CAAC,EAAE,CAAC,EAAED,GAAG,CAACoE,KAAK,IAAI,IAAI,CAACygB,OAAO,CAAEzgB,KAAK,IAAI,EAAE,EAAG,IAAI,CAAC4rB,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;EAChK;EAEAtc,eAAeA,CAAA;IACb,IAAI,CAAC7N,gBAAgB,CAAC4lB,QAAQ,CAAC,aAAa,CAAC,CAAC5C,UAAU,CAAC,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC7lB,gBAAgB,CAACzB,KAAK,CAACgf,WAAW,CAAC,CAAC;EAChI;EAEA7N,gBAAgBA,CAAC0a,OAAO;IACtB,IAAI,IAAI,CAACpqB,gBAAgB,CAACwU,GAAG,CAAC4V,OAAO,CAAC,EAAE;MACtC,IAAI,IAAI,CAACpqB,gBAAgB,CAACwU,GAAG,CAAC4V,OAAO,CAAC,CAAC7rB,KAAK,KAAK,IAAI,EAAE;QACrD,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC4V,OAAO,CAAC,CAACxK,QAAQ,CAAC,CAAC,CAAC;;;EAGpD;EAEAza,mBAAmBA,CAACilB,OAAO;IACzB,IAAI,IAAI,CAAC5lB,mBAAmB,CAACgQ,GAAG,CAAC4V,OAAO,CAAC,CAAC7rB,KAAK,KAAK,IAAI,EAAE;MACxD,IAAI,CAACiG,mBAAmB,CAACgQ,GAAG,CAAC4V,OAAO,CAAC,CAACxK,QAAQ,CAAC,CAAC,CAAC;;EAErD;EAEArQ,aAAaA,CAAC6a,OAAO;IACnB,IAAI,IAAI,CAAC7hB,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAACxI,gBAAgB,CAACwU,GAAG,CAAC4V,OAAO,CAAC,CAAC7rB,KAAK,CAAC,KAAK,CAAC,EAAE;MAChF,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC4V,OAAO,CAAC,CAACxK,QAAQ,CAAC,IAAI,CAAC;;EAErD;EAEA5a,gBAAgBA,CAAColB,OAAO;IACtB,IAAI,IAAI,CAAC7hB,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAAChE,mBAAmB,CAACgQ,GAAG,CAAC4V,OAAO,CAAC,CAAC7rB,KAAK,CAAC,KAAK,CAAC,EAAE;MACnF,IAAI,CAACiG,mBAAmB,CAACgQ,GAAG,CAAC4V,OAAO,CAAC,CAACxK,QAAQ,CAAC,IAAI,CAAC;;EAExD;EAEOyK,WAAWA,CAAA;IAChB,IAAI,CAACC,cAAc,CAAC5B,aAAa,CAAC6B,QAAQ,CAAC;MACzCC,IAAI,EAAE,IAAI,CAACF,cAAc,CAAC5B,aAAa,CAAC+B,UAAU,GAAG,GAAG;MACxD5B,QAAQ,EAAE;KACX,CAAC;EACJ;EAEO4B,UAAUA,CAAA;IACf,IAAI,CAACH,cAAc,CAAC5B,aAAa,CAAC6B,QAAQ,CAAC;MACzCC,IAAI,EAAE,IAAI,CAACF,cAAc,CAAC5B,aAAa,CAAC+B,UAAU,GAAG,GAAG;MACxD5B,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAnM,WAAWA,CAAA;IACT,IAAI,CAACZ,QAAQ,GAAG,IAAI,CAAClB,UAAU,CAAC8B,WAAW,EAAE,CAACne,KAAK;IACnD,IAAIsgB,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;IACpC8B,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACoG,KAAK;IAClC/D,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;IACtBA,GAAG,CAAC,UAAU,CAAC,GAAG,kBAAkB;IACpC,IAAI,IAAI,CAAC7e,gBAAgB,CAACzB,KAAK,EAAE;MAC/BsgB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC7e,gBAAgB,CAACzB,KAAK,CAACif,YAAY;;IAE5D,IAAI,CAAChD,GAAG,CAAC4J,cAAc,CAACvF,GAAG,CAAC,CAAC5B,SAAS,CAAC;MACrCuD,IAAI,EAAGtD,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAACpB,QAAQ,GAAGoB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC;UAC7C,IAAI,CAACzQ,gBAAgB,GAAG,CACtB,KAAK;UACL;UACA,gBAAgB;UAChB;UACA;UACA;UACA,KAAK,EACL,aAAa,EACb,OAAO,EACP,eAAe,EACf,MAAM,EACN,WAAW,CACZ;UACD,IAAI,CAACyI,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC4G,QAAQ,CAAC,kBAAkB,CAAC,CAACuF,IAAI,CAAC,CAACqJ,CAAC,EAAEC,CAAC,KAAI;YAC9C,IAAID,CAAC,CAAC9jB,QAAQ,KAAK,KAAK,IAAI+jB,CAAC,CAAC/jB,QAAQ,KAAK,KAAK,EAAE;cAChD,OAAO,CAAC,CAAC;;YAEX,IAAI+jB,CAAC,CAAC/jB,QAAQ,KAAK,KAAK,IAAI8jB,CAAC,CAAC9jB,QAAQ,KAAK,KAAK,EAAE;cAChD,OAAO,CAAC;;YAEV,OAAO,CAAC;UACV,CAAC,CAAC;UACF,IAAI,CAAC/G,UAAU,CAACC,IAAI,GAAG,IAAI,CAACgc,QAAQ,CAAC,kBAAkB,CAAC,GACpD,IAAI,CAACA,QAAQ,CAAC,kBAAkB,CAAC,GACjC,EAAE;UACN,IAAI,CAACjc,UAAU,CAACC,IAAI,CAAC4iB,OAAO,CAAE1H,EAAE,IAAI;YAClCA,EAAE,CAAC,eAAe,CAAC,GAAG,IAAI,CAACzS,MAAM,CAACC,gBAAgB,CAACwS,EAAE,CAAC,eAAe,CAAC,CAAC;YACvE,IAAIgD,OAAO,GAAG,CAAC;YACf,IAAImF,YAAY,GAAG,IAAI,CAACnD,QAAQ,CAACwC,IAAI,CAClCtC,IAAI,IAAKA,IAAI,CAACkD,QAAQ,IAAIpI,EAAE,CAAC,gBAAgB,CAAC,CAChD;YACD,IAAIiI,qBAAqB;YACzBA,qBAAqB,GAAGE,YAAY,GAChCA,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,GAC1B,CAAC,GACD,IAAI,GACN,CAAC;YACL,IAAIra,IAAI,GAAGqa,YAAY,GACpB,CAACA,YAAY,CAACpD,cAAc,CAAC,YAAY,CAAC,GAAGoD,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,IAAIF,qBAAqB,GAAIE,YAAY,CAAC,cAAc,CAAC,GACrI,CAAC;YAEHnI,EAAE,CAAC,YAAY,CAAC,GAAGA,EAAE,CAAC,KAAK,CAAC;YAC5B,IAAImI,YAAY,IAAInI,EAAE,CAAC+E,cAAc,CAAC,cAAc,CAAC,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,EAAC4D,SAAS,CAAC,CAACviB,QAAQ,CAAC4Z,EAAE,CAACyD,YAAY,CAAE,EAAE;cAC1GzD,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS;cACrBA,EAAE,CAAC,cAAc,CAAC,GAAG,IAAI,CAACzS,MAAM,CAACC,gBAAgB,CAACwS,EAAE,CAAC,cAAc,CAAC,CAAC;;YAEvEA,EAAE,CAAC,MAAM,CAAC,GAAGlS,IAAI;YACjBkS,EAAE,CAAC,eAAe,CAAC,GAAG,IAAI,CAACzS,MAAM,CAACC,gBAAgB,CAACwS,EAAE,CAAC,eAAe,CAAC,GAAGgD,OAAO,CAAC;YACjFhD,EAAE,CAAC,aAAa,CAAC,GAAG,IAAI,CAACzS,MAAM,CAACC,gBAAgB,CAACwS,EAAE,CAAC,aAAa,CAAC,GAAGgD,OAAO,CAAC;YAC7E;YACAhD,EAAE,CAAC,WAAW,CAAC,GAAGlS,IAAI,GAAGkS,EAAE,CAAC,eAAe,CAAC;UAC9C,CAAC,CAAC;UACF,IAAI,CAAC6M,QAAQ,GAAG,IAAI,CAAChoB,UAAU,CAACC,IAAI;UACpC;UACA,IAAI,CAACD,UAAU,CAACC,IAAI,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,CAACmgB,MAAM,CAACC,IAAI,IACrD,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CAClE;UACD;UACA,IAAI,CAACrF,QAAQ,GAAG,IAAI,CAACyL,QAAQ,CAAC5H,MAAM,CAACC,IAAI,IACvC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CACjE;UACD,IAAI,CAACvG,EAAE,CAACkC,aAAa,EAAE;UACvB,IAAI,CAACvd,UAAU,CAACuhB,SAAS,GAAG,IAAI,CAACA,SAAS;UAC1C,IAAI,CAACvhB,UAAU,CAACwhB,IAAI,GAAG,IAAI,CAACA,IAAI;UAChC,IAAI,CAAC1B,gBAAgB,EAAE;UACvB,IAAI,CAACzE,EAAE,CAACkC,aAAa,EAAE;SACxB,MAAM;UACL,IAAI,CAACtB,QAAQ,GAAG,EAAE;;QAGpB,IAAG,IAAI,CAACT,aAAa,IAAI,IAAI,EAAC;UAC5B,IAAI,CAAC3K,eAAe,EAAE;;MAG1B,CAAC;MACDmQ,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA8J,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC5qB,gBAAgB,CAACC,OAAO,EAAE;MACjC,IAAI,CAACD,gBAAgB,CAAC+hB,gBAAgB,EAAE;MACxC,IAAI,CAACxZ,MAAM,CAACyZ,iBAAiB,CAAC,qCAAqC,CAAC;MACpE,IAAI,CAAC3iB,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAAC6b,EAAE,CAACkC,aAAa,EAAE;KACxB,MAAM;MACL,IAAI,CAACyN,OAAO,CAACrK,IAAI,EAAE;;EAEvB;EAEAvY,QAAQA,CAAC0W,GAAW;IAClB,IAAImM,KAAK,GAAG,IAAI,CAACjrB,UAAU,CAACC,IAAI,CAAC+kB,MAAM,CAAC,CAACiG,KAAK,EAAE5K,IAAI,KAAI;MACtD,MAAM3hB,KAAK,GAAG,IAAI,CAACgK,MAAM,CAACC,gBAAgB,CAAC0X,IAAI,CAACvB,GAAG,CAAC,CAAC,IAAI,CAAC;MAC1D,OAAOmM,KAAK,GAAGvsB,KAAK;IACtB,CAAC,EAAE,CAAC,CAAC;IACL,OAAO,IAAI,CAACgK,MAAM,CAACC,gBAAgB,CAAEsiB,KAAK,GAAG,IAAI,CAAC9O,KAAM,CAAC;EAC3D;EAEA7S,WAAWA,CAACwV,GAAW;IACrB,IAAImM,KAAK,GAAG,IAAI,CAACjrB,UAAU,CAACC,IAAI,CAAC+kB,MAAM,CAAC,CAACiG,KAAK,EAAE5K,IAAI,KAAI;MACtD,MAAM3hB,KAAK,GAAI2hB,IAAI,CAACvB,GAAG,CAAC,IAAK,CAAC;MAC9B,OAAOmM,KAAK,GAAGvsB,KAAK;IACtB,CAAC,EAAE,CAAC,CAAC;IACL,OAASusB,KAAK,GAAG,IAAI,CAAC9O,KAAK;EAC7B;EAEA2E,eAAeA,CAAA;IACb,IAAI,CAACoK,YAAY,GAAG,IAAI,CAACnQ,UAAU,CAACoQ,WAAW,EAAE,CAACzsB,KAAK;IACvD,IAAI,CAACod,YAAY,GAAG,IAAI,CAACoP,YAAY,CAAC5wB,GAAG,CACtC8wB,MAAM,IAAKA,MAAM,CAACpnB,uBAAuB,CAC3C;IACD,IAAI,IAAI,CAAC8X,YAAY,CAAC5b,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACC,gBAAgB,CAACwU,GAAG,CAAC,YAAY,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACjE,YAAY,CAAC;;IAErE,IAAI,CAACzH,qBAAqB,CAACsM,IAAI,CAAC,IAAI,CAAC7E,YAAY,CAAC8E,KAAK,EAAE,CAAC;IAC1D,IAAI,CAACxM,kBAAkB,CAAC6K,YAAY,CACjCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;MACd,IAAI,CAACyD,MAAM,CACT,IAAI,CAAC/E,YAAY,EACjB,IAAI,CAAC1H,kBAAkB,EACvB,IAAI,CAACC,qBAAqB,CAC3B;IACH,CAAC,CAAC;IAEJ,IAAI,CAAC0H,UAAU,GAAG,IAAI,CAACmP,YAAY,CAAC5wB,GAAG,CACpC8wB,MAAM,IAAKA,MAAM,CAACpnB,uBAAuB,CAC3C;IACD,IAAI,IAAI,CAAC+X,UAAU,CAAC7b,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACC,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAAChE,UAAU,CAAC;MACnE,IAAI,CAACpN,cAAc,CAAC,IAAI,CAACxO,gBAAgB,CAACzB,KAAK,CAACsf,YAAY,CAAC;;IAE/D,IAAI,CAACzJ,mBAAmB,CAACoM,IAAI,CAAC,IAAI,CAAC5E,UAAU,CAAC6E,KAAK,EAAE,CAAC;IACtD,IAAI,CAACtM,gBAAgB,CAAC2K,YAAY,CAC/BC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;MACd,IAAI,CAACyD,MAAM,CACT,IAAI,CAAC9E,UAAU,EACf,IAAI,CAACzH,gBAAgB,EACrB,IAAI,CAACC,mBAAmB,CACzB;IACH,CAAC,CAAC;IAEJ,IAAI,IAAI,CAAC0G,UAAU,CAAC6D,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC7D,UAAU,CAACO,aAAa,IAAI,IAAI,EAAG;MAC1E,IAAI,CAACzG,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAC8Q,gBAAgB,CAAC,IAAI,CAAC5K,UAAU,CAACoQ,QAAQ,CAAC;KAChD,MAAM,IAAI,IAAI,CAACpQ,UAAU,CAAC6D,GAAG,IAAI,IAAI,IAAI,IAAI,CAACtD,aAAa,IAAI,KAAK,EAAE;MACrE,IAAI,CAACmO,YAAY,GAAG,IAAI,CAAC1O,UAAU,CAAC0O,YAAY;MAChD,IAAI,CAAClR,YAAY,GAAG,CAAC,GAAG,IAAI,CAACkR,YAAY,CAAC;;IAE5C,IAAI,CAAC9M,WAAW,EAAE;EACpB;EAEA4C,YAAYA,CAAA;IACV,IAAI,CAAC9E,GAAG,CAAC8E,YAAY,CAAC,IAAI,CAAC9C,IAAI,CAACO,QAAQ,CAAC,CAACE,SAAS,CAAC;MAClDuD,IAAI,EAAGtD,GAAG,IAAI;QACZ,IAAIA,GAAG,GAAG,SAAS,CAAC,EAAE;UACpB,MAAMpd,IAAI,GAAGod,GAAG,CAAC,MAAM,CAAC;UACxB,IAAI,CAACiO,IAAI,GAAGrrB,IAAI,CAACsrB,aAAa,CAACD,IAAI;;MAEvC,CAAC;MACDtK,KAAK,EAAGC,GAAG,IAAI;QAAGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAAC;KACpC,CAAC;EACJ;EAEAjiB,WAAWA,CAAA;IACT,MAAMwsB,iBAAiB,GAAG,IAAI,CAACrrB,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACjW,KAAK;IACzE,IAAI,CAAC+sB,MAAM,GAAGD,iBAAiB,KAAK,KAAK,GAAG,QAAQ,GAAG,cAAc;IACrE,MAAME,SAAS,GAAG,IAAI,CAAC1rB,UAAU,CAACC,IAAI,CAAC3F,GAAG,CAAE8sB,OAAY,IAAK,CAC3DA,OAAO,CAACxiB,cAAc,EACtBwiB,OAAO,CAAC1f,cAAc,EACtB0f,OAAO,CAACpf,GAAG,EACXof,OAAO,CAAClf,aAAa,EACrBkf,OAAO,CAAC9e,KAAK,EACb8e,OAAO,CAACve,WAAW,EACnB,IAAI,CAACH,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAACne,IAAI,CAAC,EAC1C,IAAI,CAACP,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAAChe,SAAS,CAAC,CAChD,CAAC;IAEF,IAAI4V,GAAG,GAAG;MACR2M,YAAY,EAAE,IAAI,CAAChP,IAAI,CAACiP,IAAI;MAC5BC,cAAc,EAAE;QACd,iBAAiB,EAAE,IAAI,CAAC1rB,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACjW,KAAK;QAClE,iBAAiB,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACjW,KAAK;QAClE,UAAU,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACjW,KAAK;QACvD,cAAc,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACjW,KAAK;QAC9D,QAAQ,EAAE,IAAI,CAAC+sB,MAAM;QACrB,KAAK,EAAE,IAAI,CAACtrB,gBAAgB,CAACwU,GAAG,CAAC,KAAK,CAAC,CAACjW,KAAK;QAC7C,aAAa,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,YAAY,CAAC,CAACjW,KAAK;QAC5D,aAAa,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,YAAY,CAAC,CAACjW,KAAK,CAAC4jB,IAAI,CAAC,IAAI,CAAC;QACvE,cAAc,EAAE,IAAI,CAACniB,gBAAgB,CAACwU,GAAG,CAAC,cAAc,CAAC,CAACjW,KAAK,CAAC4jB,IAAI,CAAC,IAAI,CAAC;QAC1E,WAAW,EAAE,IAAI,CAACniB,gBAAgB,CAACwU,GAAG,CAAC,gBAAgB,CAAC,CAACjW,KAAK,CAAC4jB,IAAI,CAAC,IAAI;OACzE;MACDgJ,IAAI,EAAE,IAAI,CAACA,IAAI;MACfQ,aAAa,EAAG,CAAC,iBAAiB,EAAC,iBAAiB,EAAC,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,eAAe,EAAC,WAAW,EAAC,YAAY,CAAC;MAC7HC,UAAU,EAAEL,SAAS;MACrBM,OAAO,EAAE;QACP,eAAe,EAAE,GAAG,IAAI,CAAC7rB,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACjW,KAAK,KAAK,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,KAAK,CAAC,CAACjW,KAAK,GAAG;QAChH,OAAO,EAAE,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,OAAO,CAAC,CAACjW,KAAK;QACjD,UAAU,EAAE,GAAG,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,UAAU,CAAC,CAACjW,KAAK,KAAK,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,KAAK,CAAC,CAACjW,KAAK,GAAG;QACxG,WAAW,EAAE,GAAG,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,MAAM,CAAC,CAACjW,KAAK,OAAO;QAC9D,cAAc,EAAE,GAAG,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,aAAa,CAAC,CAACjW,KAAK,KAAK,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,KAAK,CAAC,CAACjW,KAAK,GAAG;QAC/G,kBAAkB,EAAE,GAAG,IAAI,CAACyB,gBAAgB,CAACwU,GAAG,CAAC,MAAM,CAAC,CAACjW,KAAK,OAAO;QACrE,kBAAkB,EAAE,GAAG,IAAI,CAACqa,mBAAmB,EAAE,KAAK,IAAI,CAAC5Y,gBAAgB,CAACwU,GAAG,CAAC,KAAK,CAAC,CAACjW,KAAK,GAAG;QAC/F,gBAAgB,EAAE,GAAG,IAAI,CAACgK,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAACqQ,iBAAiB,EAAE,CAAC;;KAE9E;IACD,IAAI,CAAC2B,GAAG,CAACsR,YAAY,CAACjN,GAAG,CAAC,CAAC5B,SAAS,CAAC;MACnCuD,IAAI,EAAG1gB,IAAI,IAAI;QACb,IAAI,CAAC0a,GAAG,CAACuR,cAAc,CAACjsB,IAAI,CAACksB,UAAU,CAAC;MAC1C,CAAC;MACDnL,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAtS,cAAcA,CAAC2W,KAAK;IAClB,IAAIA,KAAK,EAAE;MACT,MAAM8G,sBAAsB,GAAG,IAAI,CAAClB,YAAY,CAAC9K,MAAM,CAAEgL,MAAM,IAC7D9F,KAAK,CAAC/jB,QAAQ,CAAC6pB,MAAM,CAACpnB,uBAAuB,CAAC,CAC/C;MACD,IAAI,CAAC6X,kBAAkB,GAAGuQ,sBAAsB;MAChD,IAAI,IAAI,CAACvQ,kBAAkB,CAAC3b,MAAM,KAAK,CAAC,EAAE;QACxC,IAAI,CAACC,gBAAgB,CAClBwU,GAAG,CAAC,gBAAgB,CAAC,CACrBoL,QAAQ,CAAC,IAAI,CAAClE,kBAAkB,CAAC;;MAEtC,IAAG,IAAI,CAACtZ,sBAAsB,CAACrC,MAAM,GAAG,CAAC,EAAC;QACxC,IAAI,CAACqC,sBAAsB,CAACsgB,OAAO,CAAC+G,GAAG,IAAG;UACxC,IAAI,CAAC/N,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACvhB,GAAG,CAAC+lB,IAAI,IAAG;YAC3D,IAAIA,IAAI,CAACgM,UAAU,KAAKzC,GAAG,EAAE;cAC3BvJ,IAAI,CAACtc,QAAQ,GAAG,IAAI;;YAEtB,OAAOsc,IAAI;UACb,CAAC,CAAC;QACJ,CAAC,CAAC;;MAEJ,IAAI,CAAC5L,mBAAmB,CAACkM,IAAI,CAAC,IAAI,CAAC9E,kBAAkB,CAAC+E,KAAK,EAAE,CAAC;MAC9D,IAAI,CAACpM,wBAAwB,CAACyK,YAAY,CACvCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;QACd,IAAI,CAAC0E,YAAY,CACf,IAAI,CAACjG,kBAAkB,EACvB,IAAI,CAACrH,wBAAwB,EAC7B,IAAI,CAACC,mBAAmB,CACzB;MACH,CAAC,CAAC;KACL,MAAM;MACL,IAAI,CAACoH,kBAAkB,GAAG,EAAE;;EAEhC;EAEAhL,eAAeA,CAAA;IACb,IAAI,CAACyW,SAAS,GAAG,IAAI,CAACxM,MAAM,CAACyM,IAAI,CAAC,IAAI,CAAC+E,iBAAiB,EAAE;MACxDC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,oBAAoB;MAChCC,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE;KACf,CAAC;EACJ;EAEAtd,cAAcA,CAAA;IACZ,IAAI,CAACkY,SAAS,GAAG,IAAI,CAACxM,MAAM,CAACyM,IAAI,CAAC,IAAI,CAACoF,kBAAkB,EAAE;MACzDJ,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;KACb,CAAC;EACJ;EAEAlmB,SAASA,CAAC8gB,OAAO;IACf,MAAM/G,IAAI,GAAG,IAAI,CAACF,QAAQ,CAACwC,IAAI,CAC5BtC,IAAI,IACHA,IAAI,CAACC,QAAQ,CAACsB,WAAW,EAAE,KAAKwF,OAAO,CAACxiB,cAAc,CAACgd,WAAW,EAAE,CACvE;IACD,IAAI,CAACld,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,mBAAmB,CAACwe,UAAU,CAAC;MAClCzb,cAAc,EAAE0f,OAAO,CAAC,gBAAgB,CAAC;MACzCxiB,cAAc,EAAEyb,IAAI,GAAGA,IAAI,CAACC,QAAQ,GAAG,EAAE;MACzCxY,aAAa,EAAEsf,OAAO,CAAC,eAAe,CAAC;MACvCxf,aAAa,EAAEwf,OAAO,CAAC,eAAe,CAAC;MACvCjS,GAAG,EAAEiS,OAAO,CAAC,KAAK,CAAC;MACnB9e,KAAK,EAAE8e,OAAO,CAAC,OAAO,CAAC;MACvBne,IAAI,EAAE,IAAI,CAACP,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAAC,MAAM,CAAC,CAAC;MACnDhe,SAAS,EAAE,IAAI,CAACV,MAAM,CAACC,gBAAgB,CAACye,OAAO,CAAC,WAAW,CAAC,CAAC;MAC7D1I,aAAa,EAAE0I,OAAO,CAAC,eAAe,CAAC;MACvC5e,IAAI,EAAE4e,OAAO,CAAC,MAAM,CAAC;MACrBve,WAAW,EAAEue,OAAO,CAAC,aAAa,CAAC;MACnC9I,YAAY,EAAE,CAAC,IAAI,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC/c,QAAQ,CAAC6lB,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;MAC9F7I,QAAQ,EAAE6I,OAAO,CAAC,UAAU;KAC7B,CAAC;IAEF,IAAI,CAACA,OAAO,CAAC,eAAe,CAAC,EAAE;MAC7B,IAAI,CAACziB,mBAAmB,CAACgQ,GAAG,CAAC,eAAe,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC;;IAG3D,IAAI,CAACqH,OAAO,CAAC,MAAM,CAAC,EAAE;MACpB,IAAI,CAACziB,mBAAmB,CAACgQ,GAAG,CAAC,MAAM,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC;;IAElD,IAAI,CAACuH,SAAS,GAAG,IAAI,CAACxM,MAAM,CAACyM,IAAI,CAAC,IAAI,CAACqF,gBAAgB,EAAE;MACvDL,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;KACb,CAAC;EACJ;EAEA7S,UAAUA,CAAA;IACR,MAAMkT,aAAa,GAAG,IAAI,CAAC7sB,UAAU,CAACC,IAAI,CAAC0kB,SAAS,CAC/CmI,MAAM,IAAKA,MAAM,CAACplB,cAAc,KAAK,IAAI,CAAC/C,mBAAmB,CAACjG,KAAK,CAACgJ,cAAc,CACpF;IACD,IAAImlB,aAAa,KAAK,CAAC,CAAC,EAAE;MACxB,IAAIxM,IAAI,GAAG,IAAI,CAACrgB,UAAU,CAACC,IAAI,CAAC4sB,aAAa,CAAC,CAAC,CAAE;MACjD,IAAI,CAACvQ,WAAW,CAACsI,IAAI,CAACvE,IAAI,CAAC;MAC3B,IAAI,CAACrgB,UAAU,CAACC,IAAI,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,CACxC2gB,KAAK,CAAC,CAAC,EAAEiM,aAAa,CAAC,CACvBxI,MAAM,CAAC,IAAI,CAACrkB,UAAU,CAACC,IAAI,CAAC2gB,KAAK,CAACiM,aAAa,GAAG,CAAC,CAAC,CAAC;MACtD;;;IAEN,IAAI,CAAC7E,QAAQ,GAAG,IAAI,CAAChoB,UAAU,CAACC,IAAI;IACpC,IAAI,CAAC0E,mBAAmB,CAACghB,KAAK,EAAE;IAC5B,IAAI,CAAChhB,mBAAmB,CAACwe,UAAU,CAAC;MAClCrb,aAAa,EAAE,IAAI,CAAC3H,gBAAgB,CAACzB,KAAK,CAACif,YAAY;MACvD/V,aAAa,EAAE,IAAI,CAACzH,gBAAgB,CAACzB,KAAK,CAACkf;KAChD,CAAC;IACF,IAAI,CAACqK,SAAS,EAAE;IAChB,IAAI,CAACnI,gBAAgB,EAAE;IACvB,IAAI,CAACjH,eAAe,EAAE;EACxB;EAEA4K,gBAAgBA,CAAA;IACd,IAAIzE,GAAG,GAAG,EAAE;IACZ,IAAG,IAAI,CAAC1C,WAAW,CAACpc,MAAM,GAAG,CAAC,EAAC;MAC7B,IAAI,CAACoc,WAAW,CAACuG,OAAO,CAACxC,IAAI,IAAG;QAC9BA,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;QACrB;QACAA,IAAI,CAAC,QAAQ,CAAC,GAAGA,IAAI,CAAC,UAAU,CAAC;QACjC,OAAOA,IAAI,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC;MACFrB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACO,QAAQ;MACpC8B,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACrC,IAAI,CAACoG,KAAK;MAClC/D,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC1C,WAAW;MAC9B0C,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;MACtBA,GAAG,CAAC,UAAU,CAAC,GAAG,WAAW;MAC7B,IAAI,CAACrE,GAAG,CACLoS,UAAU,CAAC/N,GAAG,CAAC,CACfE,IAAI,CAAC7kB,KAAK,EAAE,CAAC,CACb+iB,SAAS,CAAC;QACTuD,IAAI,EAAGtD,GAAG,IAAI;UACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;YAClB,IAAI,CAAChC,EAAE,CAACkC,aAAa,EAAE;YACvB,IAAI,CAAC7U,MAAM,CAACwa,mBAAmB,CAC7B,2BAA2B,CAC5B;;QAEL,CAAC;QACDlC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;QAClB;OACD,CAAC;;EAER;EAEAnH,eAAeA,CAAA;IACX,IAAIsO,cAAc,GAAG,IAAI,CAACP,cAAc,EAAE;IAC1CO,cAAc,CAAC,UAAU,CAAC,GAAG,KAAK;IAClCA,cAAc,CAAC,cAAc,CAAC,GAAG,KAAK;IACtC,IAAIC,eAAe,GAAG,IAAI,CAACroB,UAAU,CAACC,IAAI,CAAC0iB,IAAI,CAC5CxH,EAAE,IACDA,EAAE,CAACzT,cAAc,IAAI0gB,cAAc,CAAC,gBAAgB,CAAC,IACrDjN,EAAE,CAACrT,aAAa,IAAIsgB,cAAc,CAAC,eAAe;IAClD;KACH;;IACD,IAAIC,eAAe,EAAE;MACnBA,eAAe,CAAC,UAAU,CAAC,GAAG,KAAK;MACnCA,eAAe,CAAC,cAAc,CAAC,GAAG,KAAK;MACvC,IAAIzE,KAAK,GAAG,IAAI,CAAC5jB,UAAU,CAACC,IAAI,CAAC4hB,OAAO,CAACwG,eAAe,CAAC;MACzD,IAAI,CAAC9L,QAAQ,CAACqI,IAAI,CAACwD,cAAc,CAAC;MAClC,IAAI,CAACpoB,UAAU,CAACC,IAAI,CAAC2jB,KAAK,CAAC,GAAGwE,cAAc;MAC5C;MACA,IAAInoB,IAAI,GAAG,IAAI,CAACD,UAAU,CAACC,IAAI,CAACmgB,MAAM,CAACC,IAAI,IACzC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CAClE;MACD,IAAI,CAAC5hB,UAAU,CAACC,IAAI,GAAGA,IAAI;MAC3B;MACA,IAAI,CAACob,EAAE,CAACkC,aAAa,EAAE;MACvB,IAAI,CAACuC,gBAAgB,EAAE;MACvB,IAAI,CAACpX,MAAM,CAACwa,mBAAmB,CAAC,sBAAsB,CAAC;MACvD,IAAI,CAACve,mBAAmB,CAACghB,KAAK,EAAE;MAChC,IAAI,CAAChhB,mBAAmB,CAACwe,UAAU,CAAC;QAClCrb,aAAa,EAAE,IAAI,CAAC3H,gBAAgB,CAACzB,KAAK,CAACif,YAAY;QACvD/V,aAAa,EAAE,IAAI,CAACzH,gBAAgB,CAACzB,KAAK,CAACkf;OAC5C,CAAC;MACF,IAAI,CAAC/E,eAAe,EAAE;;EAE5B;EAEAtF,SAASA,CAAA;IACP,IAAG,IAAI,CAACpG,eAAe,IAAI,IAAI,EAAC;MAC9B,IAAI,CAACnN,UAAU,CAACC,IAAI,GAAG,IAAI,CAACsc,QAAQ;KACrC,MAAK,IAAG,IAAI,CAACpP,eAAe,IAAI,KAAK,EAAC;MACrC;MACA,IAAI,CAACnN,UAAU,CAACC,IAAI,GAAG,IAAI,CAAC+nB,QAAQ,CAAC5H,MAAM,CAACC,IAAI,IAC9C,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC9e,QAAQ,CAAC,CAAC8e,IAAI,CAAC1Z,YAAY,IAAI,IAAI,EAAEib,WAAW,EAAE,CAAC,CAClE;;EAEL;EAEAoL,UAAUA,CAACC,MAAiB;IAC1BA,MAAM,CAAC1F,IAAI,EAAE;EACf;EAEAtX,YAAYA,CAACkL,EAAE;IACb,IAAI,IAAI,CAAChb,gBAAgB,CAACzB,KAAK,CAAC2f,IAAI,KAAK,OAAO,EAAE;MAChD,IAAI6O,SAAS,GAAG,IAAI,CAAC/sB,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;MAC1F,IAAI,CAAChe,gBAAgB,CAACwU,GAAG,CAAC,SAAS,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAACukB,SAAS,CAAC,CAAC;KACvF,MAAM;MACL,IAAIC,UAAU,GAAG,IAAI,CAAChtB,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;MAC3F,IAAI,CAAChe,gBAAgB,CAACwU,GAAG,CAAC,SAAS,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAACrX,MAAM,CAACC,gBAAgB,CAACwkB,UAAU,CAAC,CAAC;;EAE3F;EAEApU,mBAAmBA,CAAA;IACjB,IAAIoF,OAAO,GAAG,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;IACjD,IAAI,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAAC2f,IAAI,KAAK,OAAO,EAAE;MAC/CF,OAAO,GAAG,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;;IAEvF,OAAQ,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,IAAI,CAAC,GAAGuJ,OAAO,CAAC;EAC9D;EAEAnF,iBAAiBA,CAAA;IACf,IAAImF,OAAO,GAAG,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;IACjD,IAAI,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAAC2f,IAAI,KAAK,OAAO,EAAE;MAC/CF,OAAO,GAAG,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAG,IAAI,CAACzU,gBAAgB,CAACzB,KAAK,CAACyf,OAAO;;IAEvF,OAAQ,IAAI,CAAChe,gBAAgB,CAACzB,KAAK,CAACuK,IAAI,IAAI,CAAC,GAAGkV,OAAO,CAAC;EAC1D;EAEA9N,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAClQ,gBAAgB,CAACzB,KAAK,CAAC2f,IAAI,KAAK,OAAO,EAAE;MAChD,IAAI,CAACle,gBAAgB,CAACzB,KAAK,CAACyf,OAAO,GAAG,CAAC,GAAG,IAAI,CAAChe,gBAAgB,CAACwU,GAAG,CAAC,SAAS,CAAC,CAACoL,QAAQ,CAAC,IAAI,CAAC5f,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,CAAC,GAAGkP,SAAS;KAC1I,MAAM;MACL,IAAI,CAAC3jB,gBAAgB,CAACzB,KAAK,CAACyf,OAAO,GAAG,CAAC,GAAG,IAAI,CAAChe,gBAAgB,CAACwU,GAAG,CAAC,SAAS,CAAC,CAACoL,QAAQ,CAAC,CAAC,CAAC,GAAG+D,SAAS;;EAE1G;EAEAQ,cAAcA,CAACrkB,IAAI;IACjBA,IAAI,CAAC,aAAa,CAAC,CAAC4iB,OAAO,CAAE1H,EAAE,IAAG;MAChC,MAAMyI,KAAK,GAAG,IAAI,CAAC3H,QAAQ,CAAC,aAAa,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,cAAc,CAAC,KAAKlF,EAAE,CAAC,cAAc,CAAC,CAAC;MACzGA,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI;MACrB,IAAIyI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC3H,QAAQ,CAAC,aAAa,CAAC,CAAC2H,KAAK,CAAC,GAAGzI,EAAE;;IAE5C,CAAC,CAAC;IACFlb,IAAI,CAAC,cAAc,CAAC,CAAC4iB,OAAO,CAAE1H,EAAE,IAAG;MACjC,MAAMiS,WAAW,GAAG,IAAI,CAACnR,QAAQ,CAAC,cAAc,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,cAAc,CAAC,KAAKlF,EAAE,CAAC,cAAc,CAAC,IAAIkF,IAAI,CAAC,gBAAgB,CAAC,KAAKlF,EAAE,CAAC,gBAAgB,CAAC,CAAC;MACnKA,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI;MACrB,IAAIiS,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,IAAI,CAACnR,QAAQ,CAAC,cAAc,CAAC,CAACmR,WAAW,CAAC,GAAGjS,EAAE;;IAEnD,CAAC,CAAC;IAGFlb,IAAI,CAAC,kBAAkB,CAAC,CAAC4iB,OAAO,CAAE1H,EAAE,IAAG;MACrC,MAAMyI,KAAK,GAAG,IAAI,CAAC3H,QAAQ,CAAC,kBAAkB,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,cAAc,CAAC,KAAKlF,EAAE,CAAC,cAAc,CAAC,CAAC;MAC9GA,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI;MACrB,IAAIyI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC3H,QAAQ,CAAC,kBAAkB,CAAC,CAAC2H,KAAK,CAAC,GAAGzI,EAAE;;IAEjD,CAAC,CAAC;IACFlb,IAAI,CAAC,kBAAkB,CAAC,CAAC4iB,OAAO,CAAE1H,EAAE,IAAG;MACrC,MAAMiS,WAAW,GAAG,IAAI,CAACnR,QAAQ,CAAC,kBAAkB,CAAC,CAAC0I,SAAS,CAACtE,IAAI,IAAIA,IAAI,CAAC,eAAe,CAAC,KAAKlF,EAAE,CAAC,eAAe,CAAC,IAAIkF,IAAI,CAAC,gBAAgB,CAAC,KAAKlF,EAAE,CAAC,gBAAgB,CAAC,CAAC;MACzKA,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI;MACrB,IAAIiS,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,IAAI,CAACnR,QAAQ,CAAC,kBAAkB,CAAC,CAACmR,WAAW,CAAC,GAAGjS,EAAE;;IAEvD,CAAC,CAAC;IACF,IAAI,CAACJ,UAAU,CAAC8J,WAAW,CAAC,IAAI,CAAC5I,QAAQ,CAAC;EAC5C;EAEA/a,QAAQA,CAACuoB,QAAgB,EAAEnE,KAAY,EAAE2H,MAAM,EAAEzP,KAAK;IACpD8H,KAAK,CAAC+H,eAAe,EAAE;IACvB,IAAI,CAACC,gBAAgB,GAAGL,MAAM;IAC9B,IAAI,CAAC1S,YAAY,GAAGkP,QAAQ;IAC5B,IAAI,CAAC8D,SAAS,GAAG/P,KAAK;IACtB,IAAI,CAAC8J,SAAS,GAAG,IAAI,CAACxM,MAAM,CAACyM,IAAI,CAAC,IAAI,CAACiG,wBAAwB,EAAE;MAC/DC,KAAK,EAAE,OAAO;MACdjB,UAAU,EAAE;KACb,CAAC;IACF,IAAI,CAAClF,SAAS,CAACI,WAAW,EAAE,CAACtK,SAAS,CAACuK,MAAM,IAAG,CAChD,CAAC,CAAC;EACJ;EAEAhmB,SAASA,CAAC8nB,QAAgB,EAAEnE,KAAY,EAAE2H,MAAM,EAAEzP,KAAK;IACrD8H,KAAK,CAAC+H,eAAe,EAAE;IACvB,IAAGJ,MAAM,KAAK,YAAY,EAAC;MACzB,IAAI,CAAC1qB,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC6d,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKoJ,QAAQ,CAAC;MAC3F,IAAI,CAACpmB,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC+c,MAAM,CAACC,IAAI,IACvEA,IAAI,CAACrc,uBAAuB,KAAKylB,QAAQ,CAC1C;MACD,IAAI,CAAC5N,kBAAkB,GAAI,IAAI,CAACA,kBAAkB,CAACvhB,GAAG,CAAC+lB,IAAI,IAAG;QAC5D,IAAIA,IAAI,CAACrc,uBAAuB,KAAKylB,QAAQ,IAAIpJ,IAAI,CAACH,cAAc,CAAC,UAAU,CAAC,EAAE;UAChF,OAAOG,IAAI,CAACtc,QAAQ;;QAEtB,OAAOsc,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAAC5L,mBAAmB,CAACkM,IAAI,CAAC,IAAI,CAAC9E,kBAAkB,CAAC+E,KAAK,EAAE,CAAC;MAC9D,IAAI,CAACpM,wBAAwB,CAACyK,YAAY,CACvCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;QACd,IAAI,CAAC0E,YAAY,CACf,IAAI,CAACjG,kBAAkB,EACvB,IAAI,CAACrH,wBAAwB,EAC7B,IAAI,CAACC,mBAAmB,CACzB;MACH,CAAC,CAAC;KACL,MAAK,IAAGwY,MAAM,KAAK,UAAU,EAAC;MAC7B,IAAI,CAAC5pB,wBAAwB,CAACwf,OAAO,CAACxC,IAAI,IAAG;QAC3C,IAAIA,IAAI,CAACrc,uBAAuB,KAAKwZ,KAAK,CAACxZ,uBAAuB,EAAE;UAClEqc,IAAI,CAACjc,SAAS,GAAGic,IAAI,CAACjc,SAAS,CAACgc,MAAM,CAAC6B,QAAQ,IAAIA,QAAQ,KAAKwH,QAAQ,CAAC;;MAE7E,CAAC,CAAC;MACF,IAAI,CAACpmB,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC+c,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjc,SAAS,CAAClE,MAAM,GAAG,CAAC,CAAC;KACxG,MAAK,IAAG+sB,MAAM,KAAK,qBAAqB,EAAC;MACxC,IAAI,CAAC3rB,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC8e,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKoJ,QAAQ,CAAC;;IAE7F,IAAI,CAACpO,EAAE,CAACkC,aAAa,EAAE;EACzB;EAEArD,sBAAsBA,CAAA;IACpB,IAAG,IAAI,CAACoT,gBAAgB,KAAK,YAAY,EAAC;MACxC,IAAI,CAAC/qB,sBAAsB,CAACqiB,IAAI,CAAC,IAAI,CAACrK,YAAY,CAAC;MACnD,MAAM6R,sBAAsB,GAAG,IAAI,CAAClB,YAAY,CAAC9K,MAAM,CAACgL,MAAM,IAAI,IAAI,CAAC7Q,YAAY,CAAChZ,QAAQ,CAAC6pB,MAAM,CAACpnB,uBAAuB,CAAC,CAAC;MAC7H,IAAI,CAACX,wBAAwB,CAACuhB,IAAI,CAACwH,sBAAsB,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACvQ,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACvhB,GAAG,CAAC+lB,IAAI,IAAG;QAC3D,IAAIA,IAAI,CAACrc,uBAAuB,KAAK,IAAI,CAACuW,YAAY,EAAE;UACtD8F,IAAI,CAACtc,QAAQ,GAAG,IAAI;;QAEtB,OAAOsc,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAAC5L,mBAAmB,CAACkM,IAAI,CAAC,IAAI,CAAC9E,kBAAkB,CAAC+E,KAAK,EAAE,CAAC;MAC9D,IAAI,CAACpM,wBAAwB,CAACyK,YAAY,CACvCC,IAAI,CAAC1kB,SAAS,CAAC,IAAI,CAACwhB,UAAU,CAAC,CAAC,CAChCoB,SAAS,CAAC,MAAK;QACd,IAAI,CAAC0E,YAAY,CACf,IAAI,CAACjG,kBAAkB,EACvB,IAAI,CAACrH,wBAAwB,EAC7B,IAAI,CAACC,mBAAmB,CACzB;MACH,CAAC,CAAC;KAEL,MAAK,IAAG,IAAI,CAAC6Y,gBAAgB,KAAK,UAAU,EAAC;MAC5C,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC1K,OAAO,CAACxC,IAAI,IAAG;QAC9B,MAAMqN,cAAc,GAAG,IAAI,CAACrqB,wBAAwB,CAACsf,IAAI,CAACgL,UAAU,IAAIA,UAAU,CAAC3pB,uBAAuB,KAAKqc,IAAI,CAACrc,uBAAuB,CAAC;QAC5I,IAAI0pB,cAAc,EAAE;UAChBA,cAAc,CAACtpB,SAAS,CAACwgB,IAAI,CAAC,IAAI,CAACrK,YAAY,CAAC;SACnD,MAAM;UACH,MAAMqT,SAAS,GAAG;YACd5pB,uBAAuB,EAAEqc,IAAI,CAACrc,uBAAuB;YACrDI,SAAS,EAAE,CAAC,IAAI,CAACmW,YAAY;WAChC;UACD,IAAI,CAAClX,wBAAwB,CAACuhB,IAAI,CAACgJ,SAAS,CAAC;;MAEnD,CAAC,CAAC;MACF,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACN,SAAS,CAAC,CAACjzB,GAAG,CAAC+lB,IAAI,IAAG;QACzC,OAAO;UACHrc,uBAAuB,EAAEqc,IAAI,CAACrc,uBAAuB;UACrDI,SAAS,EAAEic,IAAI,CAACjc,SAAS,CAACgc,MAAM,CAAC6B,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC1H,YAAY;SAC9E;MACL,CAAC,CAAC;MACF,IAAI,CAAClX,wBAAwB,CAACuhB,IAAI,CAAC,GAAGiJ,QAAQ,CAAC;KAChD,MAAK,IAAG,IAAI,CAACP,gBAAgB,KAAK,qBAAqB,EAAC;MACvD,IAAI,CAAChsB,sBAAsB,CAACsjB,IAAI,CAAC,IAAI,CAACrK,YAAY,CAAC;;IAErD,IAAI,CAACF,uBAAuB,EAAE;IAC9B,IAAI,CAACgB,EAAE,CAACkC,aAAa,EAAE;EACzB;EAEAlD,uBAAuBA,CAAA;IACrB,IAAI,CAACiN,SAAS,CAACjrB,KAAK,EAAE;EACxB;EAEAgd,YAAYA,CAAA;IACV,OAAS,IAAI,CAAClZ,gBAAgB,CAACzB,KAAK,CAACuK,IAAI,GAAK,IAAI,CAAC9I,gBAAgB,CAACzB,KAAK,CAACkW,QAAQ,GAAI,IAAI;EAC5F;EAEAhR,gBAAgBA,CAAC3D,IAAY,EAAGud,KAAK;IACnC,OAAO,IAAI,CAACna,wBAAwB,CAACyqB,IAAI,CAACzN,IAAI,IAC5CA,IAAI,CAACrc,uBAAuB,KAAKwZ,KAAK,CAACxZ,uBAAuB,IAC9Dqc,IAAI,CAACjc,SAAS,CAAC7C,QAAQ,CAACtB,IAAI,CAAC,CAC9B;EACH;EAEA4D,qBAAqBA,CAAC5D,IAAY,EAAGud,KAAK;IACxC,OAAO,IAAI,CAACna,wBAAwB,CAACyqB,IAAI,CAACzN,IAAI,IAC5CA,IAAI,CAACrc,uBAAuB,KAAKwZ,KAAK,CAACxZ,uBAAuB,IAC9Dqc,IAAI,CAACjc,SAAS,CAAC7C,QAAQ,CAACtB,IAAI,CAAC,CAC9B;EACH;EAEAyjB,4BAA4BA,CAAA;IAC1B,IAAI,CAAC/I,GAAG,CAACoT,eAAe,CAAC;MACvB,UAAU,EAAG,IAAI,CAACpR,IAAI,CAACO,QAAQ;MAC/B,WAAW,EAAG,IAAI,CAACP,IAAI,CAACoG,KAAK;MAC7B,MAAM,EAAG,oBAAoB;MAC7B,uBAAuB,EAAG;QACxB,oBAAoB,EAAG;UACrB,wBAAwB,EAAG,IAAI,CAACzhB,sBAAsB,CAACpB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoB,sBAAsB,GAAG,EAAE;UACpG,oBAAoB,EAAG,IAAI,CAACiB,sBAAsB,CAACrC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqC,sBAAsB,GAAG,EAAE;UAChG,sBAAsB,EAAG,IAAI,CAACc,wBAAwB,CAACnD,MAAM,GAAG,CAAC,GAAG,IAAI,CAACmD,wBAAwB,GAAG;;;KAGzG,CAAC,CAAC6b,IAAI,CAAC7kB,KAAK,EAAE,CAAC,CAAC+iB,SAAS,CAAC;MACzBuD,IAAI,EAAGtD,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAAChC,EAAE,CAACkC,aAAa,EAAE;;MAE3B,CAAC;MACDyD,KAAK,EAAGC,GAAG,IAAI;QAAGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAAC;KACpC,CAAC;EACJ;;;uBAp1DWzG,eAAe,EAAA5e,EAAA,CAAAoyB,iBAAA,CAqGhBrzB,eAAe,GAAAiB,EAAA,CAAAoyB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtyB,EAAA,CAAAoyB,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAxyB,EAAA,CAAAoyB,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1yB,EAAA,CAAAoyB,iBAAA,CAAAK,EAAA,CAAAE,MAAA,GAAA3yB,EAAA,CAAAoyB,iBAAA,CAAAQ,EAAA,CAAAC,SAAA,GAAA7yB,EAAA,CAAAoyB,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAA/yB,EAAA,CAAAoyB,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAAjzB,EAAA,CAAAoyB,iBAAA,CAQfrzB,eAAe,GAAAiB,EAAA,CAAAoyB,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAAnzB,EAAA,CAAAoyB,iBAAA,CAAApyB,EAAA,CAAAozB,SAAA,GAAApzB,EAAA,CAAAoyB,iBAAA,CAAApyB,EAAA,CAAAzC,UAAA,GAAAyC,EAAA,CAAAoyB,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAAtzB,EAAA,CAAAoyB,iBAAA,CAAApyB,EAAA,CAAAuzB,iBAAA,GAAAvzB,EAAA,CAAAoyB,iBAAA,CAAAoB,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YA7Gd7U,eAAe;MAAA8U,SAAA;MAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBACfn0B,YAAY;;;;;iCAMcnC,UAAU;yBACpCgC,OAAO;;yBAFJvB,SAAS;;;;;;;;;;;;;;;;;;;;;;UCnHzBgC,EAAA,CAAAiC,UAAA,IAAA8xB,8BAAA,iBAEM;UACN/zB,EAAA,CAAAC,cAAA,aAAyC;UACvCD,EAAA,CAAAiC,UAAA,IAAA+xB,8BAAA,iBAEM;UACNh0B,EAAA,CAAAiC,UAAA,IAAAgyB,8BAAA,iBAMM;UAENj0B,EAAA,CAAAiC,UAAA,IAAAiyB,8BAAA,mBA8BM;UACNl0B,EAAA,CAAAC,cAAA,aAAiF;UAC/ED,EAAA,CAAAiC,UAAA,IAAAkyB,iCAAA,oBAEgB;UAChBn0B,EAAA,CAAAiC,UAAA,IAAAmyB,iCAAA,oBAMS;UAETp0B,EAAA,CAAAiC,UAAA,IAAAoyB,iCAAA,oBAMS;UAETr0B,EAAA,CAAAiC,UAAA,IAAAqyB,iCAAA,oBAGS;UACXt0B,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAAkB;UAChBD,EAAA,CAAAiC,UAAA,KAAAsyB,+BAAA,kBAEM;UACRv0B,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAiC,UAAA,KAAAuyB,+BAAA,qBAsfM;UAGNx0B,EAAA,CAAAiC,UAAA,KAAAwyB,uCAAA,mCAAAz0B,EAAA,CAAA00B,sBAAA,CA8Fc;UAEd10B,EAAA,CAAAiC,UAAA,KAAA0yB,+BAAA,kBAYM;UAEN30B,EAAA,CAAAiC,UAAA,KAAA2yB,uCAAA,kCAAA50B,EAAA,CAAA00B,sBAAA,CAwCc;UAEd10B,EAAA,CAAAiC,UAAA,KAAA4yB,uCAAA,kCAAA70B,EAAA,CAAA00B,sBAAA,CAyBc;UAEd10B,EAAA,CAAAiC,UAAA,KAAA6yB,uCAAA,kCAAA90B,EAAA,CAAA00B,sBAAA,CA6Bc;UAEd10B,EAAA,CAAAiC,UAAA,KAAA8yB,uCAAA,kCAAA/0B,EAAA,CAAA00B,sBAAA,CAec;UAEhB10B,EAAA,CAAAW,YAAA,EAAM;;;UAzyBiBX,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,SAAyB;UAIxCjjB,EAAA,CAAAmB,SAAA,GAAgC;UAAhCnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAAlU,aAAA,SAAgC;UAGpB5f,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,UAA0B;UAQtCjjB,EAAA,CAAAmB,SAAA,GAAyB;UAAzBnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,SAAyB;UAgCpBjjB,EAAA,CAAAmB,SAAA,GAA0D;UAA1DnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA3a,cAAA,IAAA2a,GAAA,CAAA7Q,WAAA,cAAA6Q,GAAA,CAAAhU,OAAA,CAA0D;UAG1D9f,EAAA,CAAAmB,SAAA,GAA0D;UAA1DnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA3a,cAAA,IAAA2a,GAAA,CAAA7Q,WAAA,cAAA6Q,GAAA,CAAAhU,OAAA,CAA0D;UAQ1D9f,EAAA,CAAAmB,SAAA,GAA2D;UAA3DnB,EAAA,CAAAiB,UAAA,UAAA6yB,GAAA,CAAA3a,cAAA,IAAA2a,GAAA,CAAA7Q,WAAA,cAAA6Q,GAAA,CAAAhU,OAAA,CAA2D;UAQ3D9f,EAAA,CAAAmB,SAAA,GAAsC;UAAtCnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,cAAA6Q,GAAA,CAAAhU,OAAA,CAAsC;UAMV9f,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,UAA0B;UAK3DjjB,EAAA,CAAAmB,SAAA,GAAsC;UAAtCnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,cAAA6Q,GAAA,CAAAhU,OAAA,CAAsC;UAylBtC9f,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAAiB,UAAA,SAAA6yB,GAAA,CAAA7Q,WAAA,UAA0B;;;qBDxlB9BvlB,WAAW,EAAA20B,EAAA,CAAA2C,aAAA,EAAA3C,EAAA,CAAA4C,cAAA,EAAA5C,EAAA,CAAA6C,uBAAA,EAAA7C,EAAA,CAAA8C,oBAAA,EAAA9C,EAAA,CAAA+C,mBAAA,EAAA/C,EAAA,CAAAgD,0BAAA,EAAAhD,EAAA,CAAAiD,eAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,OAAA,EACXx2B,eAAe,EACfY,cAAc,EAAA61B,GAAA,CAAAC,OAAA,EACdl4B,YAAY,EAAAm4B,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,SAAA,EAAAJ,GAAA,CAAAK,aAAA,EACZr4B,mBAAmB,EAAA00B,EAAA,CAAA4D,oBAAA,EAAA5D,EAAA,CAAA6D,kBAAA,EAAA7D,EAAA,CAAA8D,eAAA,EACnBj4B,kBAAkB,EAAAk4B,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EAClBz4B,mBAAmB,EACnBE,mBAAmB,EACnBG,cAAc,EAAAq4B,GAAA,CAAAC,QAAA,EACdr4B,eAAe,EACfR,eAAe,EAAA84B,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACf14B,aAAa,EAAA24B,GAAA,CAAAC,OAAA,EACbj5B,aAAa,EACbQ,eAAe,EAAA04B,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAl5B,SAAA,EAAAk5B,GAAA,CAAAC,WAAA,EACf54B,cAAc,EAAA64B,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdz4B,qBAAqB,EAAA04B,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,sBAAA,EACrB34B,gBAAgB,EAEhBI,wBAAwB,EAAAw4B,GAAA,CAAAC,wBAAA,EACxBv4B,cAAc,EAAAw4B,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,gBAAA,EAAAP,GAAA,CAAAQ,eAAA,EAAAR,GAAA,CAAAS,aAAA,EAAAT,GAAA,CAAAU,OAAA,EAAAV,GAAA,CAAAW,aAAA,EAAAX,GAAA,CAAAY,YAAA,EAAAZ,GAAA,CAAAa,MAAA,EAAAb,GAAA,CAAAc,YAAA,EACdr5B,gBAAgB,EAAAs5B,GAAA,CAAAC,UAAA,EAChBt5B,gBAAgB,EAChBE,gBAAgB,EAChBC,uBAAuB,EAAAo5B,GAAA,CAAAC,0BAAA,EACvBn5B,kBAAkB,EAAAo5B,GAAA,CAAAr5B,YAAA,EAClBG,UAAU,EAAAm5B,GAAA,CAAAC,YAAA,EACVn5B,oBAAoB,EAAAo5B,GAAA,CAAAC,cAAA,EACpBp5B,mBAAmB;MAAAq5B,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIVza,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}