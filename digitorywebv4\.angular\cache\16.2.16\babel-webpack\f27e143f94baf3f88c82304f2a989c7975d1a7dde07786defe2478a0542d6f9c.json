{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport MarkdownIt from 'markdown-it';\nimport { first } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"src/app/services/inventory.service\";\nimport * as i3 from \"@angular/common\";\nfunction RecipeLlmComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 5);\n    i0.ɵɵelement(4, \"path\", 6)(5, \"path\", 7);\n    i0.ɵɵelementStart(6, \"circle\", 8);\n    i0.ɵɵelement(7, \"animate\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"p\", 10);\n    i0.ɵɵtext(9, \"Loading insights...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction RecipeLlmComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.renderedMDHtml, i0.ɵɵsanitizeHtml);\n  }\n}\nlet RecipeLlmComponent = /*#__PURE__*/(() => {\n  class RecipeLlmComponent {\n    constructor(sanitizer, api, cd) {\n      this.sanitizer = sanitizer;\n      this.api = api;\n      this.cd = cd;\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      let mapping = {\n        \"menu_master\": this.master,\n        \"menu_recipes\": this.child\n      };\n      this.api.recipeInsight(mapping).pipe(first()).subscribe({\n        next: res => {\n          this.md = new MarkdownIt();\n          this.md.set({\n            typographer: true,\n            linkify: true,\n            xhtmlOut: true,\n            html: false\n          }).enable(['smartquotes', 'replacements', 'image']);\n          const renderedMarkdown = this.md.render(res['data']);\n          this.renderedMDHtml = this.sanitizer.bypassSecurityTrustHtml(renderedMarkdown);\n          this.isLoading = false;\n          this.cd.detectChanges();\n        },\n        error: err => {\n          console.log(err);\n          this.isLoading = false;\n          this.cd.detectChanges();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function RecipeLlmComponent_Factory(t) {\n        return new (t || RecipeLlmComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RecipeLlmComponent,\n        selectors: [[\"app-recipe-llm\"]],\n        inputs: {\n          master: \"master\",\n          child: \"child\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"recipe-insights-container\", 4, \"ngIf\"], [\"class\", \"markdown-content\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"recipe-insights-container\"], [1, \"loading-container\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 100 100\", \"width\", \"100\", \"height\", \"100\", 1, \"cooking-pot\"], [\"d\", \"M20,50 Q50,20 80,50 L80,80 Q50,100 20,80 Z\", \"fill\", \"#d4d4d4\"], [\"d\", \"M35,40 L65,40 L65,50 Q50,60 35,50 Z\", \"fill\", \"#ffffff\"], [\"cx\", \"50\", \"cy\", \"60\", \"r\", \"5\", \"fill\", \"#ff6b6b\"], [\"attributeName\", \"cy\", \"values\", \"60;55;60\", \"dur\", \"1s\", \"repeatCount\", \"indefinite\"], [1, \"mt-2\"], [1, \"markdown-content\", 3, \"innerHTML\"]],\n        template: function RecipeLlmComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, RecipeLlmComponent_div_0_Template, 10, 0, \"div\", 0);\n            i0.ɵɵtemplate(1, RecipeLlmComponent_div_1_Template, 1, 1, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf],\n        styles: [\".markdown-content[_ngcontent-%COMP%]{font-size:1rem;line-height:1.2}.recipe-insights-container[_ngcontent-%COMP%]{position:relative;width:100%;height:40vh}.loading-container[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:#ffffffe6}.loading-content[_ngcontent-%COMP%]{text-align:center}.cooking-pot[_ngcontent-%COMP%]{width:100px;height:100px;animation:_ngcontent-%COMP%_steam 2s ease-out infinite}@keyframes _ngcontent-%COMP%_steam{0%{transform:translateY(0) scale(1);opacity:.5}50%{transform:translateY(-10px) scale(1.2);opacity:.7}to{transform:translateY(0) scale(1);opacity:.5}}.content-container[_ngcontent-%COMP%]{margin:0 auto;padding:20px}.list-group-item[_ngcontent-%COMP%]{transition:background-color .3s}.list-group-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return RecipeLlmComponent;\n})();\nexport { RecipeLlmComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}