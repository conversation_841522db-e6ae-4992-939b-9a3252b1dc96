{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 1, message_r12.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, ChatBotComponent_div_13_div_2_Template, 4, 4, \"div\", 26);\n    i0.ɵɵelement(3, \"div\", 27);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r12.sender !== \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r12.text), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementStart(5, \"div\", 32);\n    i0.ɵɵtext(6, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_p_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_36_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r16);\n  }\n}\nfunction ChatBotComponent_ul_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 34);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_36_li_1_Template, 2, 1, \"li\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_41_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r18 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r18);\n  }\n}\nfunction ChatBotComponent_ul_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 34);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_41_li_1_Template, 2, 1, \"li\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 36)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    // Flag to track if conversation history has been loaded\n    this.conversationHistoryLoaded = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          if (!duplicateMessage) {\n            // Add new bot message\n            this.messages.push(message);\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m => m.sender === message.sender && m.text === message.text);\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to just the welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 53,\n      vars: 16,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"compact-list\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [\"class\", \"message-header\", 4, \"ngIf\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-header\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-text\"], [1, \"placeholder-text\"], [1, \"compact-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Chat Agent\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_9_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"delete_sweep\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵtemplate(13, ChatBotComponent_div_13_Template, 5, 8, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 7, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"mat-icon\", 17);\n          i0.ɵɵtext(24, \"summarize\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"Summary\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 19)(29, \"h4\");\n          i0.ɵɵtext(30, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, ChatBotComponent_p_31_Template, 2, 1, \"p\", 20);\n          i0.ɵɵtemplate(32, ChatBotComponent_p_32_Template, 2, 0, \"p\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 19)(34, \"h4\");\n          i0.ɵɵtext(35, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, ChatBotComponent_ul_36_Template, 2, 1, \"ul\", 22);\n          i0.ɵɵtemplate(37, ChatBotComponent_p_37_Template, 2, 0, \"p\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 19)(39, \"h4\");\n          i0.ɵɵtext(40, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, ChatBotComponent_ul_41_Template, 2, 1, \"ul\", 22);\n          i0.ɵɵtemplate(42, ChatBotComponent_p_42_Template, 2, 0, \"p\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 19)(44, \"h4\");\n          i0.ɵɵtext(45, \"Menu Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, ChatBotComponent_table_46_Template, 11, 2, \"table\", 23);\n          i0.ɵɵtemplate(47, ChatBotComponent_p_47_Template, 2, 0, \"p\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 19)(49, \"h4\");\n          i0.ɵɵtext(50, \"Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(51, ChatBotComponent_p_51_Template, 2, 1, \"p\", 20);\n          i0.ɵɵtemplate(52, ChatBotComponent_p_52_Template, 2, 0, \"p\", 21);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 450px;\\n  gap: 15px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: #f8a055; \\n\\n  color: white;\\n  min-height: 36px;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  line-height: 32px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 10px 16px;\\n  border-radius: 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  margin-left: 8px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 2px;\\n  background-color: #3f51b5;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n  transform: translateY(0);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.9999s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 12px;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink-bounce {\\n  0%, 100% {\\n    opacity: 0.4;\\n    transform: translateY(0);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateY(-4px);\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 8px;\\n  background-color: #f8f9fa;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 3px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-bottom: 2px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n  color: white;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: #3f51b5;\\n  color: white;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 15px;\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  color: #616161;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 3px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 16px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n  font-size: 13px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 12px;\\n  margin: 0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e0e0e0;\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  width: 40px;\\n  justify-content: center;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 6px;\\n  width: 6px;\\n  margin: 0 2px;\\n  background-color: #555;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n  50% {\\n    transform: translateY(-2px) scale(1.05);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r12", "timestamp", "ɵɵtemplate", "ChatBotComponent_div_13_div_2_Template", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ctx_r2", "restaurantSummary", "location", "cuisine_r16", "ChatBotComponent_ul_36_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r18", "ChatBotComponent_ul_41_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "conversationHistoryLoaded", "ngOnChanges", "changes", "currentValue", "previousValue", "loadConversationHistory", "ngOnInit", "id", "generateId", "Date", "messages$", "subscribe", "message", "startsWith", "detectChanges", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "push", "sort", "a", "b", "getTime", "isDuplicate", "some", "Math", "abs", "console", "log", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "updateRestaurantSummary", "userMessage", "next", "error", "lowerMessage", "toLowerCase", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "for<PERSON>ach", "existingMessage", "clearConversationHistory", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_9_listener", "ChatBotComponent_div_13_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_p_31_Template", "ChatBotComponent_p_32_Template", "ChatBotComponent_ul_36_Template", "ChatBotComponent_p_37_Template", "ChatBotComponent_ul_41_Template", "ChatBotComponent_p_42_Template", "ChatBotComponent_table_46_Template", "ChatBotComponent_p_47_Template", "ChatBotComponent_p_51_Template", "ChatBotComponent_p_52_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if conversation history has been loaded\n  private conversationHistoryLoaded = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            if (!duplicateMessage) {\n              // Add new bot message\n              this.messages.push(message);\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m =>\n              m.sender === message.sender && m.text === message.text\n            );\n\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to just the welcome message\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Cha<PERSON> interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <mat-icon class=\"chat-icon\">chat</mat-icon>\n        <span class=\"assistant-title\">Chat Agent</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n          <mat-icon>delete_sweep</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-header\" *ngIf=\"message.sender !== 'system'\">\n            <div class=\"message-timestamp\">{{ message.timestamp | date:'shortTime' }}</div>\n          </div>\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n        </div>\n      </div>\n\n      <!-- Improved loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n          <div class=\"typing-text\">AI is thinking...</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <mat-icon class=\"summary-icon\">summarize</mat-icon>\n      <span>Summary</span>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Info</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Pending</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICM9CC,EAAA,CAAAC,cAAA,cAAgE;IAC/BD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAH/ER,EAAA,CAAAC,cAAA,cAAqL;IAEjLD,EAAA,CAAAS,UAAA,IAAAC,sCAAA,kBAEM;IACNV,EAAA,CAAAW,SAAA,cAAsE;;IACxEX,EAAA,CAAAG,YAAA,EAAM;;;;IAN4EH,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAP,WAAA,CAAAQ,MAAA,aAAAR,WAAA,CAAAQ,MAAA,YAAgG;IAEnJf,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,SAAAL,WAAA,CAAAQ,MAAA,cAAiC;IAGpCf,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,UAAA,cAAAZ,EAAA,CAAAgB,WAAA,OAAAT,WAAA,CAAAU,IAAA,GAAAjB,EAAA,CAAAkB,cAAA,CAAqC;;;;;IAKnElB,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAW,SAAA,WAAa;IAGbX,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA6BlDH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAc,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtErB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzEH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAiB,WAAA,CAAa;;;;;IAD1EtB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAS,UAAA,IAAAc,oCAAA,iBAA6E;IAC/EvB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,YAAAY,MAAA,CAAAJ,iBAAA,CAAAK,YAAA,CAAiC;;;;;IAE3DzB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzFH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAqB,aAAA,CAAe;;;;;IAD7E1B,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAS,UAAA,IAAAkB,oCAAA,iBAAgF;IAClF3B,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAR,iBAAA,CAAAS,WAAA,CAAgC;;;;;IAE5D7B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAK1FH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAW,SAAA,CAAiC;IAIjC/B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAY,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrDjC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAA6B,OAAA,CAAAd,iBAAA,CAAAe,cAAA,CAAsC;;;;;IAClFnC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADhFzF,MAqBaiC,gBAAgB;EAsB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAxBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA1B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAY,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAmB1D;IACQ,KAAAC,yBAAyB,GAAG,KAAK;EAdtC;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;MAC3D;MACA,IAAID,OAAO,CAAC,UAAU,CAAC,CAACE,aAAa,KAAKF,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;QAC1E,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC,IAAI,CAACK,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,QAAQ,GAAG,EAAE;IAElB;IACA,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACa,uBAAuB,EAAE;KAC/B,MAAM;MACL;MACA,IAAI,CAACX,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,2KAA2K;QACjLF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;;IAGH;IACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAACT,UAAU,CAACqB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAI8C,OAAO,CAACL,EAAE,CAACM,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAChB,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAIF,OAAO,CAAC9C,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMiD,oBAAoB,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKK,OAAO,CAACL,EAAE,CAAC;QAE9E,IAAIQ,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAACrB,QAAQ,CAACqB,oBAAoB,CAAC,GAAGH,OAAO;SAC9C,MAAM;UACL;UACA,MAAMM,gBAAgB,GAAG,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACnD,MAAM,KAAK,KAAK,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IAAI,CAACiD,CAAC,CAACjD,IAAI,CAACoD,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,IAAI,CAACF,gBAAgB,EAAE;YACrB;YACA,IAAI,CAACxB,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;YAE3B;YACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;;;OAGhF,MAAM,IAAIb,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAM4D,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IACvB4D,IAAI,CAACC,GAAG,CAACZ,CAAC,CAAC1D,SAAS,CAACkE,OAAO,EAAE,GAAGb,OAAO,CAACrD,SAAS,CAACkE,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACC,WAAW,EAAE;UAChBI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;UACjD;UACA,IAAI,CAAC0B,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;UAE3B;UACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;SAC5E,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACsB,EAAE,CAACwB,aAAa,EAAE;MAEvB;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACnC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACoC,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACnC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACmC,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC7C,UAAU,CAAC8C,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzC,cAAc,CAAC0C,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC7C,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC+C,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC3C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM2C,aAAa,GAAG,IAAI,CAAC7C,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA;IAEA;IACA,IAAI,CAAC2B,uBAAuB,CAACD,aAAa,CAAC;IAE3C;IACA,MAAME,WAAW,GAAgB;MAC/BnC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBxC,IAAI,EAAEwE,aAAa;MACnB1E,MAAM,EAAE,MAAM;MACdP,SAAS,EAAE,IAAIkD,IAAI;KACpB;IAED;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAKwE,aAAa,CACzB;IAED,IAAI,CAACd,WAAW,EAAE;MAChB,IAAI,CAAChC,QAAQ,CAAC2B,IAAI,CAACqB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC7C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;IACvB,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAAC3C,UAAU,CAAC+C,WAAW,CAAC,IAAI,CAAC5C,QAAQ,EAAEgD,aAAa,CAAC,CAAC7B,SAAS,CAAC;MAClEgC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAAC/C,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACzB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAAC+C,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjD,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQ2B,uBAAuBA,CAAC7B,OAAe;IAC7C,MAAMiC,YAAY,GAAGjC,OAAO,CAACkC,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACzB,QAAQ,CAAC,UAAU,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IACrEyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IACjEyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAACjD,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAAC2E,kBAAkB,CAACF,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAM4B,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACJ,YAAY,CAAC;MACpD,IAAIG,QAAQ,CAAChE,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACb,iBAAiB,CAACK,YAAY,GAAG,CAAC,GAAG,IAAI0E,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACK,YAAY,EAAE,GAAGwE,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIH,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,YAAY,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAMxC,WAAW,GAAG,IAAI,CAACqE,gBAAgB,CAACJ,YAAY,CAAC;MACvD,IAAIjE,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACb,iBAAiB,CAACS,WAAW,GAAG,CAAC,GAAG,IAAIsE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACS,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIiE,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAM+B,cAAc,GAAGN,YAAY,CAACO,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAAChF,iBAAiB,CAACW,SAAS,GAAGuE,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIN,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMkC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACJ,YAAY,CAAC;QACtD,IAAIS,UAAU,CAACtE,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACb,iBAAiB,CAACY,cAAc,GAAG,CAAC,GAAG,IAAImE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACY,cAAc,EAAE,GAAGuE,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIT,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAACjD,iBAAiB,CAACe,cAAc,GAAG,IAAI,CAAC6D,kBAAkB,CAACF,YAAY,CAAC;;IAG/E,IAAI,CAACvD,EAAE,CAACwB,aAAa,EAAE;EACzB;EAEA;;;;EAIQiC,kBAAkBA,CAACnC,OAAe;IACxC;IACA,MAAM2C,cAAc,GAAG3C,OAAO,CAC3B4C,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAACrC,OAAe;IACtC;IACA,IAAIA,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,IAAIR,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOR,OAAO,CACXgD,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzB,IAAI,EAAE,CAAC,CACxB0B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC9E,MAAM,GAAG,CAAC,CAAC,CAC/B6E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACnC,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAoD,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAChC,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAqC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQlE,UAAUA,CAAA;IAChB,OAAOoB,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGjD,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,MAAc,EAAEnE,OAAoB;IAC5C,OAAOA,OAAO,CAACL,EAAE;EACnB;EAEA;;;EAGAF,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACQ,yBAAyB,EAAE;MACpD;;IAGF;IACA,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAACJ,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACP,UAAU,CAACgB,uBAAuB,CAAC,IAAI,CAACb,QAAQ,EAAE,KAAK,CAAC,CAACmB,SAAS,CAAC;MACtEgC,IAAI,EAAGjD,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,IAAI,CAACU,QAAQ,GAAG,EAAE;UAElB;UACAA,QAAQ,CAACsF,OAAO,CAACpE,OAAO,IAAG;YACzB;YACA,MAAMqE,eAAe,GAAG,IAAI,CAACvF,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC1CA,CAAC,CAACnD,MAAM,KAAK8C,OAAO,CAAC9C,MAAM,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,CACvD;YAED,IAAI,CAACiH,eAAe,EAAE;cACpB,IAAI,CAACvF,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;;YAG7B;YACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;cAC7B,IAAI,CAAC2E,uBAAuB,CAAC7B,OAAO,CAAC5C,IAAI,CAAC;;UAE9C,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAAC0B,QAAQ,CAACV,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACU,QAAQ,CAAC2B,IAAI,CAAC;cACjBd,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;cACrBxC,IAAI,EAAE,2KAA2K;cACjLF,MAAM,EAAE,KAAK;cACbP,SAAS,EAAE,IAAIkD,IAAI;aACpB,CAAC;;SAEL,MAAM;UACL;UACA,IAAI,CAACf,QAAQ,GAAG,CAAC;YACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,2KAA2K;YACjLF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CAAC;;QAGJ,IAAI,CAACb,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;QACvB,IAAI,CAACkB,cAAc,EAAE;MACvB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACH,QAAQ,GAAG,CAAC;UACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBxC,IAAI,EAAE,2KAA2K;UACjLF,MAAM,EAAE,KAAK;UACbP,SAAS,EAAE,IAAIkD,IAAI;SACpB,CAAC;QAEF,IAAI,CAACnB,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAoE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACtF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA,IAAI,CAAC3C,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED;IACA,IAAI,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,UAAU,CAAC6F,wBAAwB,CAAC,IAAI,CAAC1F,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAACmB,SAAS,CAAC;QAC5EgC,IAAI,EAAEA,CAAA,KAAK;UACTb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAACrC,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED;UACA,IAAI,CAACT,yBAAyB,GAAG,KAAK;UAEtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAAClD,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;UACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACtC,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;MAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACvB,IAAI,CAACkB,cAAc,EAAE;;EAEzB;;;uBA1fW7C,gBAAgB,EAAApC,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtI,EAAA,CAAAoI,iBAAA,CAAApI,EAAA,CAAAuI,iBAAA,GAAAvI,EAAA,CAAAoI,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBrG,gBAAgB;MAAAsG,SAAA;MAAAC,MAAA;QAAAlG,QAAA;QAAAC,UAAA;MAAA;MAAAkG,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,oBAAA,EAAA9I,EAAA,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC7BrJ,EAAA,CAAAC,cAAA,aAAyB;UAKWD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEjDH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAuJ,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAClFnI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKvCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAS,UAAA,KAAAgJ,gCAAA,iBAOM;UAGNzJ,EAAA,CAAAS,UAAA,KAAAiJ,gCAAA,kBAOM;UACR1J,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAuJ,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA1G,cAAA,GAAAgH,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAArC,UAAA,CAAA2C,MAAA,CAAkB;UAAA,EAFD;UADnC5J,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAuJ,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAC1DrF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAEGD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGtBH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAS,UAAA,KAAAsJ,8BAAA,gBAA0E;UAC1E/J,EAAA,CAAAS,UAAA,KAAAuJ,8BAAA,gBAA2E;UAC7EhK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAS,UAAA,KAAAwJ,+BAAA,iBAEK;UACLjK,EAAA,CAAAS,UAAA,KAAAyJ,8BAAA,gBAA2F;UAC7FlK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAS,UAAA,KAAA0J,+BAAA,iBAEK;UACLnK,EAAA,CAAAS,UAAA,KAAA2J,8BAAA,gBAA0F;UAC5FpK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAS,UAAA,KAAA4J,kCAAA,qBASQ;UACRrK,EAAA,CAAAS,UAAA,KAAA6J,8BAAA,gBAAiF;UACnFtK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAS,UAAA,KAAA8J,8BAAA,gBAAsF;UACtFvK,EAAA,CAAAS,UAAA,KAAA+J,8BAAA,gBAAiF;UACnFxK,EAAA,CAAAG,YAAA,EAAM;;;UAnFmBH,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA3G,QAAA,CAAa,iBAAA2G,GAAA,CAAAvB,SAAA;UAUhC/H,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAxG,oBAAA,CAA0B;UAavB9C,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA1G,cAAA,CAA4B,aAAA0G,GAAA,CAAAzG,YAAA;UAKwB7C,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAY,UAAA,cAAA0I,GAAA,CAAA1G,cAAA,CAAA0C,IAAA,MAAAgE,GAAA,CAAAzG,YAAA,CAAmD;UAgB1G7C,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAgC;UAChCrB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAiC;UAKhCrB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,KAA+C;UAGhDjC,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,OAAiD;UAKhDjC,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,KAA8C;UAG/CjC,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,OAAgD;UAKtBjC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,KAAqC;UAU/D/B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,OAAuC;UAKvC/B,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAsC;UACtCnC,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAuC;;;qBD5E/C9C,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZvL,WAAW,EAAAwL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1L,mBAAmB,EACnBC,eAAe,EAAA0L,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACf3L,aAAa,EACbC,kBAAkB,EAAA2L,EAAA,CAAAC,YAAA,EAClB3L,aAAa,EAAA4L,EAAA,CAAAC,OAAA,EACb5L,cAAc,EAAA6L,EAAA,CAAAC,QAAA,EACd7L,wBAAwB,EACxBC,gBAAgB,EAAA6L,EAAA,CAAAC,UAAA,EAEhB7L,YAAY;MAAA8L,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH1J,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}