{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 24);\n    i0.ɵɵpipe(3, \"markdown\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 2, message_r12.text), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ChatBotComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_p_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_32_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r14 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r14);\n  }\n}\nfunction ChatBotComponent_ul_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 28);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_32_li_1_Template, 2, 1, \"li\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_37_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r16);\n  }\n}\nfunction ChatBotComponent_ul_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 28);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_37_li_1_Template, 2, 1, \"li\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 30)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n  }\n  ngOnChanges(_changes) {\n    // No need to load conversation history on changes\n  }\n  ngOnInit() {\n    // Initialize with a welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Clear any existing conversation history for this tenant\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on initialization');\n        },\n        error: error => {\n          console.error('Error clearing conversation history on initialization:', error);\n        }\n      });\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Add new bot message\n          this.messages.push(message);\n        }\n      } else {\n        // For user messages, add them if they don't exist already\n        const existingUserMessage = this.messages.find(m => m.sender === 'user' && m.text === message.text);\n        if (!existingUserMessage) {\n          this.messages.push(message);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  // Removed loadConversationHistory method\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Reset to just the welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Conversation has been cleared. How can I help you today?',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    // Also clear conversation history on the server if we have a tenant ID\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n        },\n        error: error => {\n          console.error('Error clearing conversation history on server:', error);\n        }\n      });\n    }\n    this.cd.detectChanges();\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 49,\n      vars: 16,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"bot-typing\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"compact-list\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"bot-typing\"], [1, \"typing-indicator\"], [1, \"placeholder-text\"], [1, \"compact-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_7_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"delete_sweep\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 7);\n          i0.ɵɵtemplate(11, ChatBotComponent_div_11_Template, 4, 7, \"div\", 8);\n          i0.ɵɵtemplate(12, ChatBotComponent_div_12_Template, 5, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"mat-form-field\", 11)(15, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_15_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_16_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 14)(20, \"div\", 15)(21, \"span\");\n          i0.ɵɵtext(22, \"Summary\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"div\", 17)(25, \"h4\");\n          i0.ɵɵtext(26, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, ChatBotComponent_p_27_Template, 2, 1, \"p\", 18);\n          i0.ɵɵtemplate(28, ChatBotComponent_p_28_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 17)(30, \"h4\");\n          i0.ɵɵtext(31, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ChatBotComponent_ul_32_Template, 2, 1, \"ul\", 20);\n          i0.ɵɵtemplate(33, ChatBotComponent_p_33_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 17)(35, \"h4\");\n          i0.ɵɵtext(36, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, ChatBotComponent_ul_37_Template, 2, 1, \"ul\", 20);\n          i0.ɵɵtemplate(38, ChatBotComponent_p_38_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 17)(40, \"h4\");\n          i0.ɵɵtext(41, \"Menu Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ChatBotComponent_table_42_Template, 11, 2, \"table\", 21);\n          i0.ɵɵtemplate(43, ChatBotComponent_p_43_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 17)(45, \"h4\");\n          i0.ɵɵtext(46, \"Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ChatBotComponent_p_47_Template, 2, 1, \"p\", 18);\n          i0.ɵɵtemplate(48, ChatBotComponent_p_48_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 450px;\\n  gap: 15px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 350px;\\n  max-height: 600px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  line-height: 30px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f1f1f1;\\n  padding: 12px 16px;\\n  border-radius: 18px;\\n  margin-left: 44px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 1px;\\n  background-color: #9E9EA1;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.9999s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink {\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 8px;\\n  background-color: #f8f9fa;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 3px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n  color: white;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 3px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 16px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n  font-size: 13px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 12px;\\n  margin: 0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 8px 0 8px 16px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e8f5e9;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: 60px;\\n  justify-content: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 2px;\\n  background-color: #388e3c;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.4;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r12", "sender", "ɵɵadvance", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ɵɵtext", "ɵɵtextInterpolate", "ctx_r2", "restaurantSummary", "location", "cuisine_r14", "ɵɵtemplate", "ChatBotComponent_ul_32_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r16", "ChatBotComponent_ul_37_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "ngOnChanges", "_changes", "ngOnInit", "id", "generateId", "timestamp", "Date", "clearConversationHistory", "subscribe", "next", "console", "log", "error", "messages$", "message", "existingMessageIndex", "findIndex", "m", "push", "existingUserMessage", "find", "detectChanges", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "updateRestaurantSummary", "lowerMessage", "toLowerCase", "includes", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "Math", "random", "toString", "substring", "trackById", "_index", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_7_listener", "ChatBotComponent_div_11_Template", "ChatBotComponent_div_12_Template", "ChatBotComponent_Template_input_ngModelChange_15_listener", "$event", "ChatBotComponent_Template_input_keydown_15_listener", "ChatBotComponent_Template_button_click_16_listener", "ChatBotComponent_p_27_Template", "ChatBotComponent_p_28_Template", "ChatBotComponent_ul_32_Template", "ChatBotComponent_p_33_Template", "ChatBotComponent_ul_37_Template", "ChatBotComponent_p_38_Template", "ChatBotComponent_table_42_Template", "ChatBotComponent_p_43_Template", "ChatBotComponent_p_47_Template", "ChatBotComponent_p_48_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(_changes: SimpleChanges): void {\n    // No need to load conversation history on changes\n  }\n\n  ngOnInit(): void {\n    // Initialize with a welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Clear any existing conversation history for this tenant\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on initialization');\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history on initialization:', error);\n        }\n      });\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Add new bot message\n            this.messages.push(message);\n          }\n        } else {\n          // For user messages, add them if they don't exist already\n          const existingUserMessage = this.messages.find(m =>\n            m.sender === 'user' && m.text === message.text\n          );\n\n          if (!existingUserMessage) {\n            this.messages.push(message);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  // Removed loadConversationHistory method\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Reset to just the welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    // Also clear conversation history on the server if we have a tenant ID\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history on server:', error);\n        }\n      });\n    }\n\n    this.cd.detectChanges();\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Cha<PERSON> interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <span class=\"assistant-title\">Assistant</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n          <mat-icon>delete_sweep</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n        </div>\n      </div>\n\n      <!-- Loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"bot-typing\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <span>Summary</span>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Info</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Pending</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;;ICGlDC,EAAA,CAAAC,cAAA,cAAqL;IAEjLD,EAAA,CAAAE,SAAA,cAAsE;;IACxEF,EAAA,CAAAG,YAAA,EAAM;;;;IAH4EH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,MAAA,aAAAD,WAAA,CAAAC,MAAA,YAAgG;IAEtJR,EAAA,CAAAS,SAAA,GAAqC;IAArCT,EAAA,CAAAI,UAAA,cAAAJ,EAAA,CAAAU,WAAA,OAAAH,WAAA,CAAAI,IAAA,GAAAX,EAAA,CAAAY,cAAA,CAAqC;;;;;IAKnEZ,EAAA,CAAAC,cAAA,cAAqD;IAEjDD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;IA2BNH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAa,MAAA,GAAgC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtEjB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzEH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAa,MAAA,GAAa;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAc,iBAAA,CAAAI,WAAA,CAAa;;;;;IAD1ElB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAmB,UAAA,IAAAC,oCAAA,iBAA6E;IAC/EpB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAL,iBAAA,CAAAM,YAAA,CAAiC;;;;;IAE3DtB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzFH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAa,MAAA,GAAe;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAc,iBAAA,CAAAS,aAAA,CAAe;;;;;IAD7EvB,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAmB,UAAA,IAAAK,oCAAA,iBAAgF;IAClFxB,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAAT,iBAAA,CAAAU,WAAA,CAAgC;;;;;IAE5D1B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAK1FH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAa,MAAA,kBAAW;IAAAb,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAiC;IAAAb,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAa,MAAA,kBAAW;IAAAb,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAc,iBAAA,CAAAa,MAAA,CAAAX,iBAAA,CAAAY,SAAA,CAAiC;IAIjC5B,EAAA,CAAAS,SAAA,GAA6C;IAA7CT,EAAA,CAAAc,iBAAA,CAAAa,MAAA,CAAAX,iBAAA,CAAAa,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrD9B,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAa,MAAA,GAAsC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAS,SAAA,GAAsC;IAAtCT,EAAA,CAAAc,iBAAA,CAAAiB,OAAA,CAAAf,iBAAA,CAAAgB,cAAA,CAAsC;;;;;IAClFhC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;AD1EzF,MAqBa8B,gBAAgB;EAsB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAxBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA3B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZK,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAY,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;EAMvD;EAEHC,WAAWA,CAACC,QAAuB;IACjC;EAAA;EAGFC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBvC,IAAI,EAAE,2KAA2K;MACjLH,MAAM,EAAE,KAAK;MACb2C,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IAED;IACA,IAAI,IAAI,CAACd,QAAQ,EAAE;MACjB,IAAI,CAACH,UAAU,CAACkB,wBAAwB,CAAC,IAAI,CAACf,QAAQ,CAAC,CAACgB,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC/D,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;QAChF;OACD,CAAC;;IAGJ;IACA,IAAI,CAACd,mBAAmB,GAAG,IAAI,CAACT,UAAU,CAACwB,SAAS,CAACL,SAAS,CAC3DM,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAACpD,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMqD,oBAAoB,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKW,OAAO,CAACX,EAAE,CAAC;QAE9E,IAAIY,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAACrB,QAAQ,CAACqB,oBAAoB,CAAC,GAAGD,OAAO;SAC9C,MAAM;UACL;UACA,IAAI,CAACpB,QAAQ,CAACwB,IAAI,CAACJ,OAAO,CAAC;;OAE9B,MAAM;QACL;QACA,MAAMK,mBAAmB,GAAG,IAAI,CAACzB,QAAQ,CAAC0B,IAAI,CAACH,CAAC,IAC9CA,CAAC,CAACvD,MAAM,KAAK,MAAM,IAAIuD,CAAC,CAACpD,IAAI,KAAKiD,OAAO,CAACjD,IAAI,CAC/C;QAED,IAAI,CAACsD,mBAAmB,EAAE;UACxB,IAAI,CAACzB,QAAQ,CAACwB,IAAI,CAACJ,OAAO,CAAC;;;MAI/B;MACA,IAAI,CAACxB,EAAE,CAAC+B,aAAa,EAAE;MAEvB;MACA,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACzB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC0B,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACzB,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACyB,WAAW,EAAE;;IAG3C;IACA,IAAI,CAACnC,UAAU,CAACoC,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC/B,cAAc,CAACgC,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACqC,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAACjC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAMiC,aAAa,GAAG,IAAI,CAACnC,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAAC+B,aAAa,EAAE;IAEvB;IACA;IAEA;IACA,IAAI,CAACU,uBAAuB,CAACD,aAAa,CAAC;IAE3C;IACA,IAAI,CAACzC,UAAU,CAACqC,WAAW,CAAC,IAAI,CAAClC,QAAQ,EAAEsC,aAAa,CAAC,CAACtB,SAAS,CAAC;MAClEC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA,IAAI,CAACb,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAAC+B,aAAa,EAAE;MACzB,CAAC;MACDT,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAACqC,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACvC,EAAE,CAAC+B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQU,uBAAuBA,CAACjB,OAAe;IAC7C,MAAMkB,YAAY,GAAGlB,OAAO,CAACmB,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IACrEF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IACjEF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAAChE,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAACgE,kBAAkB,CAACH,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,IACtEF,YAAY,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAME,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACL,YAAY,CAAC;MACpD,IAAII,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACd,iBAAiB,CAACM,YAAY,GAAG,CAAC,GAAG,IAAI8D,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpE,iBAAiB,CAACM,YAAY,EAAE,GAAG4D,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIJ,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,IACtEF,YAAY,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAMtD,WAAW,GAAG,IAAI,CAACyD,gBAAgB,CAACL,YAAY,CAAC;MACvD,IAAIpD,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACd,iBAAiB,CAACU,WAAW,GAAG,CAAC,GAAG,IAAI0D,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpE,iBAAiB,CAACU,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIoD,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC9DF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAMK,cAAc,GAAGP,YAAY,CAACQ,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAACrE,iBAAiB,CAACY,SAAS,GAAG2D,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIP,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMQ,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACL,YAAY,CAAC;QACtD,IAAIU,UAAU,CAAC1D,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACd,iBAAiB,CAACa,cAAc,GAAG,CAAC,GAAG,IAAIuD,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpE,iBAAiB,CAACa,cAAc,EAAE,GAAG2D,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIV,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC9DF,YAAY,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAAChE,iBAAiB,CAACgB,cAAc,GAAG,IAAI,CAACiD,kBAAkB,CAACH,YAAY,CAAC;;IAG/E,IAAI,CAAC1C,EAAE,CAAC+B,aAAa,EAAE;EACzB;EAEA;;;;EAIQc,kBAAkBA,CAACrB,OAAe;IACxC;IACA,MAAM6B,cAAc,GAAG7B,OAAO,CAC3B8B,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAACvB,OAAe;IACtC;IACA,IAAIA,OAAO,CAACoB,QAAQ,CAAC,GAAG,CAAC,IAAIpB,OAAO,CAACoB,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOpB,OAAO,CACXkC,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACvB,IAAI,EAAE,CAAC,CACxBwB,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAClE,MAAM,GAAG,CAAC,CAAC,CAC/BiE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACrB,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAsC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAC9B,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAmC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQ1D,UAAUA,CAAA;IAChB,OAAO2D,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,MAAc,EAAEtD,OAAoB;IAC5C,OAAOA,OAAO,CAACX,EAAE;EACnB;EAEA;EAEA;;;EAGAI,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACb,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBvC,IAAI,EAAE,0DAA0D;MAChEH,MAAM,EAAE,KAAK;MACb2C,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IAED;IACA,IAAI,CAACpC,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZK,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED;IACA,IAAI,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,UAAU,CAACkB,wBAAwB,CAAC,IAAI,CAACf,QAAQ,CAAC,CAACgB,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACxE;OACD,CAAC;;IAGJ,IAAI,CAACtB,EAAE,CAAC+B,aAAa,EAAE;EACzB;;;uBAvUWlC,gBAAgB,EAAAjC,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAnH,EAAA,CAAAsH,iBAAA,GAAAtH,EAAA,CAAAmH,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBvF,gBAAgB;MAAAwF,SAAA;MAAAC,MAAA;QAAApF,QAAA;QAAAC,UAAA;MAAA;MAAAoF,UAAA;MAAAC,QAAA,GAAA5H,EAAA,CAAA6H,oBAAA,EAAA7H,EAAA,CAAA8H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC7BpI,EAAA,CAAAC,cAAA,aAAyB;UAKaD,EAAA,CAAAa,MAAA,gBAAS;UAAAb,EAAA,CAAAG,YAAA,EAAO;UAEhDH,EAAA,CAAAC,cAAA,aAA0B;UACmBD,EAAA,CAAAsI,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAhF,wBAAA,EAA0B;UAAA,EAAC;UAC7ErD,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAa,MAAA,mBAAY;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAKvCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAmB,UAAA,KAAAqH,gCAAA,iBAIM;UAGNxI,EAAA,CAAAmB,UAAA,KAAAsH,gCAAA,iBAMM;UACRzI,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAsI,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA5F,cAAA,GAAAkG,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAAnC,UAAA,CAAAyC,MAAA,CAAkB;UAAA,EAFD;UADnC3I,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAsI,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAA7D,WAAA,EAAa;UAAA,EAAC;UAC1DxE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAEtBD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAG,YAAA,EAAO;UAGtBH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAmB,UAAA,KAAA2H,8BAAA,gBAA0E;UAC1E9I,EAAA,CAAAmB,UAAA,KAAA4H,8BAAA,gBAA2E;UAC7E/I,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAmB,UAAA,KAAA6H,+BAAA,iBAEK;UACLhJ,EAAA,CAAAmB,UAAA,KAAA8H,8BAAA,gBAA2F;UAC7FjJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAmB,UAAA,KAAA+H,+BAAA,iBAEK;UACLlJ,EAAA,CAAAmB,UAAA,KAAAgI,8BAAA,gBAA0F;UAC5FnJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAmB,UAAA,KAAAiI,kCAAA,qBASQ;UACRpJ,EAAA,CAAAmB,UAAA,KAAAkI,8BAAA,gBAAiF;UACnFrJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAmB,UAAA,KAAAmI,8BAAA,gBAAsF;UACtFtJ,EAAA,CAAAmB,UAAA,KAAAoI,8BAAA,gBAAiF;UACnFvJ,EAAA,CAAAG,YAAA,EAAM;;;UA9EmBH,EAAA,CAAAS,SAAA,IAAa;UAAbT,EAAA,CAAAI,UAAA,YAAAiI,GAAA,CAAA7F,QAAA,CAAa,iBAAA6F,GAAA,CAAApB,SAAA;UAOhCjH,EAAA,CAAAS,SAAA,GAA0B;UAA1BT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAA1F,oBAAA,CAA0B;UAYvB3C,EAAA,CAAAS,SAAA,GAA4B;UAA5BT,EAAA,CAAAI,UAAA,YAAAiI,GAAA,CAAA5F,cAAA,CAA4B,aAAA4F,GAAA,CAAA3F,YAAA;UAKwB1C,EAAA,CAAAS,SAAA,GAAmD;UAAnDT,EAAA,CAAAI,UAAA,cAAAiI,GAAA,CAAA5F,cAAA,CAAAgC,IAAA,MAAA4D,GAAA,CAAA3F,YAAA,CAAmD;UAe1G1C,EAAA,CAAAS,SAAA,IAAgC;UAAhCT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAC,QAAA,CAAgC;UAChCjB,EAAA,CAAAS,SAAA,GAAiC;UAAjCT,EAAA,CAAAI,UAAA,UAAAiI,GAAA,CAAArH,iBAAA,CAAAC,QAAA,CAAiC;UAKhCjB,EAAA,CAAAS,SAAA,GAA+C;UAA/CT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAM,YAAA,CAAAQ,MAAA,KAA+C;UAGhD9B,EAAA,CAAAS,SAAA,GAAiD;UAAjDT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAM,YAAA,CAAAQ,MAAA,OAAiD;UAKhD9B,EAAA,CAAAS,SAAA,GAA8C;UAA9CT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAU,WAAA,CAAAI,MAAA,KAA8C;UAG/C9B,EAAA,CAAAS,SAAA,GAAgD;UAAhDT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAU,WAAA,CAAAI,MAAA,OAAgD;UAKtB9B,EAAA,CAAAS,SAAA,GAAqC;UAArCT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAY,SAAA,KAAqC;UAU/D5B,EAAA,CAAAS,SAAA,GAAuC;UAAvCT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAY,SAAA,OAAuC;UAKvC5B,EAAA,CAAAS,SAAA,GAAsC;UAAtCT,EAAA,CAAAI,UAAA,SAAAiI,GAAA,CAAArH,iBAAA,CAAAgB,cAAA,CAAsC;UACtChC,EAAA,CAAAS,SAAA,GAAuC;UAAvCT,EAAA,CAAAI,UAAA,UAAAiI,GAAA,CAAArH,iBAAA,CAAAgB,cAAA,CAAuC;;;qBDtE/C3C,YAAY,EAAAmK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZrK,WAAW,EAAAsK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXxK,mBAAmB,EACnBC,eAAe,EAAAwK,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACfzK,aAAa,EACbC,kBAAkB,EAAAyK,EAAA,CAAAC,YAAA,EAClBzK,aAAa,EAAA0K,EAAA,CAAAC,OAAA,EACb1K,cAAc,EAAA2K,EAAA,CAAAC,QAAA,EACd3K,wBAAwB,EACxBC,gBAAgB,EAAA2K,EAAA,CAAAC,UAAA,EAEhB3K,YAAY;MAAA4K,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH3I,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}