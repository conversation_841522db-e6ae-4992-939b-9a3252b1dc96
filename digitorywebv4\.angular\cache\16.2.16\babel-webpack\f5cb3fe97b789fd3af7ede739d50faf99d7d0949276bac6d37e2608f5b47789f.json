{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵproperty(\"value\", i_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r8.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.getActiveFiltersCount());\n  }\n}\nfunction SmartDashboardComponent_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r10.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r11.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"mat-icon\", 46);\n    i0.ɵɵtext(3, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Ready to Generate Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Configure your filters and ask the AI assistant to generate visualizations for your data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 47)(9, \"div\", 48)(10, \"mat-icon\", 49);\n    i0.ɵɵtext(11, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h5\");\n    i0.ɵɵtext(13, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 48)(15, \"mat-icon\", 49);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h5\");\n    i0.ɵɵtext(18, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 48)(20, \"mat-icon\", 49);\n    i0.ɵɵtext(21, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h5\");\n    i0.ɵɵtext(23, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 48)(25, \"mat-icon\", 49);\n    i0.ɵɵtext(26, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"h5\");\n    i0.ɵɵtext(28, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 50);\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 90,\n      vars: 17,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-reports\"], [1, \"reports-header\"], [\"appearance\", \"outline\", 1, \"report-dropdown\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-filters\"], [1, \"filters-header\"], [1, \"filters-title\"], [\"class\", \"filter-count\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", 3, \"value\", \"valueChange\"], [3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions-sticky\"], [\"mat-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-header\"], [1, \"ai-icon\"], [1, \"ai-title\"], [1, \"ai-subtitle\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me sales trends for the last quarter by region\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-charts\"], [1, \"charts-header\"], [1, \"charts-icon\"], [1, \"charts-content\"], [\"class\", \"charts-placeholder\", 4, \"ngIf\"], [\"class\", \"generated-charts\", 4, \"ngIf\"], [3, \"value\"], [1, \"filter-count\"], [1, \"charts-placeholder\"], [1, \"placeholder-content\"], [1, \"placeholder-icon\"], [1, \"feature-cards\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"generated-charts\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h3\");\n          i0.ɵɵtext(7, \"Reports\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-form-field\", 4)(9, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_9_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_9_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\");\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, SmartDashboardComponent_span_18_Template, 2, 1, \"span\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"div\", 12)(21, \"label\", 13)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 14)(26, \"mat-select\", 15);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_26_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(27, SmartDashboardComponent_mat_option_27_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"label\", 13)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-form-field\", 14)(34, \"mat-select\", 16);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_34_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(35, SmartDashboardComponent_mat_option_35_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 12)(37, \"label\", 13)(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 14)(42, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_42_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"mat-datepicker-toggle\", 18)(44, \"mat-datepicker\", null, 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 12)(47, \"label\", 13)(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-form-field\", 14)(52, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_52_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(53, \"mat-datepicker-toggle\", 18)(54, \"mat-datepicker\", null, 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 22)(57, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_57_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_61_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(65, \"div\", 25)(66, \"div\", 26)(67, \"div\", 27)(68, \"div\", 28)(69, \"mat-icon\", 29);\n          i0.ɵɵtext(70, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 30);\n          i0.ɵɵtext(72, \"Ask AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\", 31);\n          i0.ɵɵtext(74, \"Select filters first...\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 32)(76, \"mat-form-field\", 33)(77, \"input\", 34);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_77_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_77_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_78_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(79, \"mat-icon\");\n          i0.ɵɵtext(80, \"send\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(81, \"div\", 36)(82, \"div\", 37)(83, \"mat-icon\", 38);\n          i0.ɵɵtext(84, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"h3\");\n          i0.ɵɵtext(86, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 39);\n          i0.ɵɵtemplate(88, SmartDashboardComponent_div_88_Template, 29, 0, \"div\", 40);\n          i0.ɵɵtemplate(89, SmartDashboardComponent_div_89_Template, 1, 0, \"div\", 41);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(45);\n          const _r5 = i0.ɵɵreference(55);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim());\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  background-color: #f8f9fa;\\n}\\n[_nghost-%COMP%]   *[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100vh;\\n  background-color: #f8f9fa;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 300px;\\n  min-width: 300px;\\n  background-color: white;\\n  border-right: 1px solid #e0e0e0;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  overflow: hidden;\\n  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n\\n\\n.sidebar-reports[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px;\\n  flex-shrink: 0;\\n}\\n\\n.reports-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.reports-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.reports-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.report-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.report-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.report-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.report-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  font-size: 13px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.sidebar-filters[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  min-height: 0;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.filters-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n  border-radius: 10px;\\n  padding: 2px 6px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  min-width: 16px;\\n  text-align: center;\\n  margin-left: 4px;\\n  line-height: 1.2;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 16px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  min-height: 0;\\n  \\n\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #d0d0d0;\\n  border-radius: 2px;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #b0b0b0;\\n}\\n\\n\\n\\n.filters-actions-sticky[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-top: 1px solid #e0e0e0;\\n  background-color: white;\\n  display: flex;\\n  gap: 8px;\\n  flex-shrink: 0;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  height: 36px;\\n  padding: 0 16px;\\n  font-size: 13px;\\n  border-radius: 6px;\\n  flex: 1;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n}\\n.reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.reset-btn[_ngcontent-%COMP%]:focus, .apply-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n  background: white;\\n}\\n.reset-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #d0d0d0;\\n}\\n\\n.apply-btn[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n  border: none;\\n}\\n.apply-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #444;\\n  margin-bottom: 2px;\\n}\\n.filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  color: #666;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  max-height: 32px;\\n  overflow: hidden;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-datepicker-toggle {\\n  color: #666;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-icon-suffix {\\n  padding-left: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px 24px;\\n  flex-shrink: 0;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.ai-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.ai-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  flex-shrink: 0;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 22px;\\n  width: 22px;\\n  height: 22px;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-subtitle[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #666;\\n  white-space: nowrap;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  flex: 1;\\n}\\n\\n.ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 44px;\\n  border-radius: 8px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e0e0e0;\\n  transition: all 0.2s ease;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper:hover {\\n  border-color: #c0c0c0;\\n  background-color: #f0f0f0;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #2196f3;\\n  background-color: white;\\n  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 12px 16px;\\n  font-size: 14px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     input {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     input::placeholder {\\n  color: #999;\\n  font-size: 14px;\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background-color: #2196f3;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);\\n}\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #e0e0e0;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1976d2;\\n  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);\\n}\\n.send-btn[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);\\n}\\n.send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n\\n\\n.dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  padding: 12px 20px 20px 20px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.charts-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n  flex-shrink: 0;\\n}\\n.charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.charts-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid #e0e0e0;\\n  flex: 1;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex: 1;\\n  padding: 20px;\\n}\\n\\n.placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #ddd;\\n  margin-bottom: 12px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n.feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e8e8e8;\\n  border-radius: 8px;\\n  padding: 10px 8px;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n  border-color: #ddd;\\n}\\n.feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  color: #ff9800;\\n  margin-bottom: 4px;\\n}\\n\\n.generated-charts[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 280px;\\n    min-width: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n    max-height: 50vh;\\n    border-right: none;\\n    border-bottom: 1px solid #e0e0e0;\\n  }\\n  .ai-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 12px;\\n  }\\n  .ai-header[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 6px;\\n  }\\n  .ai-input-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n    height: 40px;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n  .ai-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .sidebar-reports[_ngcontent-%COMP%], .ai-section[_ngcontent-%COMP%], .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .filters-content[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 12px;\\n  }\\n  .filters-actions-sticky[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    max-height: 40vh;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 6px;\\n  }\\n  .charts-placeholder[_ngcontent-%COMP%] {\\n    padding: 16px 12px;\\n  }\\n  .ai-header[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .ai-header[_ngcontent-%COMP%]   .ai-subtitle[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n    height: 36px;\\n  }\\n  .ai-input-field[_ngcontent-%COMP%]     input {\\n    font-size: 12px;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n\\n\\n.sidebar-filters[_ngcontent-%COMP%]::-webkit-scrollbar, .generated-charts[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.sidebar-filters[_ngcontent-%COMP%]::-webkit-scrollbar-track, .generated-charts[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.sidebar-filters[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .generated-charts[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar-filters[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover, .generated-charts[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r9", "ɵɵadvance", "ɵɵtextInterpolate1", "tab_r8", "label", "ɵɵtextInterpolate", "ctx_r1", "getActiveFiltersCount", "location_r10", "value", "baseDate_r11", "ɵɵelement", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "checked", "baseDates", "selectedLocations", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "toggleFilters", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "console", "log", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "length", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_9_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_9_listener", "ɵɵtemplate", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_18_Template", "SmartDashboardComponent_Template_mat_select_valueChange_26_listener", "SmartDashboardComponent_mat_option_27_Template", "SmartDashboardComponent_Template_mat_select_valueChange_34_listener", "SmartDashboardComponent_mat_option_35_Template", "SmartDashboardComponent_Template_input_ngModelChange_42_listener", "SmartDashboardComponent_Template_input_ngModelChange_52_listener", "SmartDashboardComponent_Template_button_click_57_listener", "SmartDashboardComponent_Template_button_click_61_listener", "SmartDashboardComponent_Template_input_ngModelChange_77_listener", "SmartDashboardComponent_Template_input_keydown_77_listener", "SmartDashboardComponent_Template_button_click_78_listener", "SmartDashboardComponent_div_88_Template", "SmartDashboardComponent_div_89_Template", "_r4", "_r5", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatFormField", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Tabs + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Selection (Dropdown) -->\n    <div class=\"sidebar-reports\">\n      <div class=\"reports-header\">\n        <mat-icon>assessment</mat-icon>\n        <h3>Reports</h3>\n      </div>\n      <mat-form-field appearance=\"outline\" class=\"report-dropdown\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            {{ tab.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section (Vertical) -->\n    <div class=\"sidebar-filters\">\n      <div class=\"filters-header\">\n        <div class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          <h3>Smart Filters</h3>\n          <span class=\"filter-count\" *ngIf=\"getActiveFiltersCount() > 0\">{{getActiveFiltersCount()}}</span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- 1. Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>location_on</mat-icon>\n            Restaurants\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple>\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- 2. Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- 3. Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- 4. End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Sticky Filter Actions at Bottom -->\n      <div class=\"filters-actions-sticky\">\n        <button mat-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Right Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Input Section (Top Right) - Compact Horizontal Layout -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-header\">\n          <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n          <span class=\"ai-title\">Ask AI Assistant</span>\n          <span class=\"ai-subtitle\">Select filters first...</span>\n        </div>\n        <div class=\"ai-input-container\">\n          <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n            <input\n              matInput\n              type=\"text\"\n              placeholder=\"e.g., Show me sales trends for the last quarter by region\"\n              [(ngModel)]=\"chatMessage\"\n              (keydown)=\"onKeyPress($event)\"\n            >\n          </mat-form-field>\n          <button mat-icon-button color=\"primary\" class=\"send-btn\" (click)=\"sendMessage()\" [disabled]=\"!chatMessage.trim()\">\n            <mat-icon>send</mat-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area (Bottom Right) -->\n    <div class=\"dashboard-charts\">\n      <div class=\"charts-header\">\n        <mat-icon class=\"charts-icon\">dashboard</mat-icon>\n        <h3>Dashboard</h3>\n      </div>\n\n      <div class=\"charts-content\">\n        <!-- Placeholder content when no charts are generated -->\n        <div class=\"charts-placeholder\" *ngIf=\"!hasGeneratedCharts\">\n          <div class=\"placeholder-content\">\n            <mat-icon class=\"placeholder-icon\">insights</mat-icon>\n            <h4>Ready to Generate Insights</h4>\n            <p>Configure your filters and ask the AI assistant to generate visualizations for your data.</p>\n\n            <div class=\"feature-cards\">\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n                <h5>Interactive Charts</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n                <h5>Smart Insights</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">chat</mat-icon>\n                <h5>Natural Language Queries</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">schedule</mat-icon>\n                <h5>Real-time Analysis</h5>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated charts will appear here -->\n        <div class=\"generated-charts\" *ngIf=\"hasGeneratedCharts\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;ICFlCC,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAW;IAC7DL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAWAT,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA2B;;;;;IAatFZ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAS,YAAA,CAAAC,KAAA,CAAwB;IACrEd,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,YAAA,CAAAJ,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAW,YAAA,CAAAD,KAAA,CAAwB;IACrEd,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,YAAA,CAAAN,KAAA,MACF;;;;;IA8FNT,EAAA,CAAAC,cAAA,cAA4D;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gGAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhGH,EAAA,CAAAC,cAAA,cAA2B;IAEQD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAOnCH,EAAA,CAAAgB,SAAA,cAEM;;;ADnKd,MAqBaC,uBAAuB;EAqClCC,YAAA;IApCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAER,KAAK,EAAE,aAAa;MAAEL,KAAK,EAAE,wBAAwB;MAAEc,OAAO,EAAE;IAAK,CAAE,EACzE;MAAET,KAAK,EAAE,aAAa;MAAEL,KAAK,EAAE,iBAAiB;MAAEc,OAAO,EAAE;IAAK,CAAE,EAClE;MAAET,KAAK,EAAE,aAAa;MAAEL,KAAK,EAAE,aAAa;MAAEc,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAET,KAAK,EAAE,aAAa;MAAEL,KAAK,EAAE,gBAAgB;MAAEc,OAAO,EAAE;IAAK,CAAE,EACjE;MAAET,KAAK,EAAE,aAAa;MAAEL,KAAK,EAAE,oBAAoB;MAAEc,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAC,SAAS,GAAG,CACV;MAAEV,KAAK,EAAE,OAAO;MAAEL,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEK,KAAK,EAAE,WAAW;MAAEL,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEK,KAAK,EAAE,WAAW;MAAEL,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEK,KAAK,EAAE,YAAY;MAAEL,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEK,KAAK,EAAE,QAAQ;MAAEL,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAgB,iBAAiB,GAAa,EAAE;IAChC,KAAAC,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAErB,KAAK,EAAE,YAAY;MAAEsB,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEtB,KAAK,EAAE,iBAAiB;MAAEsB,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEtB,KAAK,EAAE,cAAc;MAAEsB,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEtB,KAAK,EAAE,kBAAkB;MAAEsB,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBC,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACd,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAe,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACf,WAAW,GAAGe,KAAK;IACxB,IAAI,CAACL,IAAI,CAACM,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACN,MAAM,GAAGO,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF;IACAI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACV,IAAI,CAACK,KAAK,CAAC,CAAC1B,KAAK,CAAC;EACzD;EAEAgC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;MAC3B;MACAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACX,WAAW,CAAC;MAE9C;MACA,IAAI,CAACR,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACQ,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;IACAT,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACrB,aAAa,GAAG,KAAK;EAC5B;EAEA8B,YAAYA,CAAA;IACV;IACA,IAAI,CAACxB,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACN,SAAS,CAACc,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAAC3B,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEAX,qBAAqBA,CAAA;IACnB,IAAIuC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1B,iBAAiB,CAAC2B,MAAM,GAAG,CAAC,EAAED,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACzB,gBAAgB,KAAK,OAAO,EAAEyB,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACxB,SAAS,EAAEwB,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACvB,OAAO,EAAEuB,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;;;uBAhGWlC,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAoC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvD,EAAA,CAAAwD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCpC9D,EAAA,CAAAC,cAAA,aAAuC;UAMrBD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElBH,EAAA,CAAAC,cAAA,wBAA6D;UAC/CD,EAAA,CAAAgE,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA3C,WAAA,GAAA8C,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBH,GAAA,CAAA7B,WAAA,CAAAgC,MAAA,CAAApD,KAAA,CAAyB;UAAA,EAA7C;UACjCd,EAAA,CAAAoE,UAAA,KAAAC,8CAAA,wBAEa;UACfrE,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,cAA6B;UAGbD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAoE,UAAA,KAAAE,wCAAA,mBAAiG;UACnGtE,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIbD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAgE,UAAA,yBAAAO,oEAAAL,MAAA;YAAA,OAAAH,GAAA,CAAAtC,iBAAA,GAAAyC,MAAA;UAAA,EAA6B;UACvClE,EAAA,CAAAoE,UAAA,KAAAI,8CAAA,wBAEa;UACfxE,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAgE,UAAA,yBAAAS,oEAAAP,MAAA;YAAA,OAAAH,GAAA,CAAArC,gBAAA,GAAAwC,MAAA;UAAA,EAA4B;UACtClE,EAAA,CAAAoE,UAAA,KAAAM,8CAAA,wBAEa;UACf1E,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAgE,UAAA,2BAAAW,iEAAAT,MAAA;YAAA,OAAAH,GAAA,CAAApC,SAAA,GAAAuC,MAAA;UAAA,EAAuB;UAHzBlE,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgB,SAAA,iCAA6E;UAE/EhB,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAgE,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAH,GAAA,CAAAnC,OAAA,GAAAsC,MAAA;UAAA,EAAqB;UAHvBlE,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgB,SAAA,iCAA2E;UAE7EhB,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAAoC;UACGD,EAAA,CAAAgE,UAAA,mBAAAa,0DAAA;YAAA,OAASd,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAC3DjD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAgE,UAAA,mBAAAc,0DAAA;YAAA,OAASf,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAClFhD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKQD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1DH,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAAgE,UAAA,2BAAAe,iEAAAb,MAAA;YAAA,OAAAH,GAAA,CAAAlC,WAAA,GAAAqC,MAAA;UAAA,EAAyB,qBAAAc,2DAAAd,MAAA;YAAA,OACdH,GAAA,CAAApB,UAAA,CAAAuB,MAAA,CAAkB;UAAA,EADJ;UAJ3BlE,EAAA,CAAAG,YAAA,EAMC;UAEHH,EAAA,CAAAC,cAAA,kBAAkH;UAAzDD,EAAA,CAAAgE,UAAA,mBAAAiB,0DAAA;YAAA,OAASlB,GAAA,CAAAtB,WAAA,EAAa;UAAA,EAAC;UAC9EzC,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAOjCH,EAAA,CAAAC,cAAA,eAA8B;UAEID,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,eAA4B;UAE1BD,EAAA,CAAAoE,UAAA,KAAAc,uCAAA,mBAyBM;UAGNlF,EAAA,CAAAoE,UAAA,KAAAe,uCAAA,kBAEM;UACRnF,EAAA,CAAAG,YAAA,EAAM;;;;;UAzKQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAA2D,GAAA,CAAA3C,WAAA,CAAuB;UACLpB,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAA2D,GAAA,CAAAjC,IAAA,CAAS;UAaT9B,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAA2D,GAAA,CAAAnD,qBAAA,OAAiC;UAY/CZ,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAA2D,GAAA,CAAAtC,iBAAA,CAA6B;UACNzB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA2D,GAAA,CAAAzC,SAAA,CAAY;UAcnCtB,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAA2D,GAAA,CAAArC,gBAAA,CAA4B;UACL1B,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA2D,GAAA,CAAAvC,SAAA,CAAY;UAgB7CxB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAgF,GAAA,CAA6B,YAAArB,GAAA,CAAApC,SAAA;UAKE3B,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAgF,GAAA,CAAmB;UAclDpF,EAAA,CAAAM,SAAA,GAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAiF,GAAA,CAA2B,YAAAtB,GAAA,CAAAnC,OAAA;UAKI5B,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAiF,GAAA,CAAiB;UAoChDrF,EAAA,CAAAM,SAAA,IAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA2D,GAAA,CAAAlC,WAAA,CAAyB;UAIoD7B,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,cAAA2D,GAAA,CAAAlC,WAAA,CAAAa,IAAA,GAAgC;UAgBlF1C,EAAA,CAAAM,SAAA,IAAyB;UAAzBN,EAAA,CAAAI,UAAA,UAAA2D,GAAA,CAAA1C,kBAAA,CAAyB;UA4B3BrB,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAA2D,GAAA,CAAA1C,kBAAA,CAAwB;;;qBD7J3DlC,YAAY,EAAAmG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpG,aAAa,EACbC,eAAe,EAAAoG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfrG,aAAa,EAAAsG,EAAA,CAAAC,OAAA,EACbtG,kBAAkB,EAAAuG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBxG,eAAe,EAAAyG,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACf3G,cAAc,EAAA4G,EAAA,CAAAC,QAAA,EACd5G,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAA0G,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnB5G,mBAAmB,EACnBC,WAAW,EAAA4G,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKF9F,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}