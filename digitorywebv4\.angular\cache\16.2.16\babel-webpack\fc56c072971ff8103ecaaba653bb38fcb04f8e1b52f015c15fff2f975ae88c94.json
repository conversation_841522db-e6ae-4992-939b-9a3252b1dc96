{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction ChatBotComponent_mat_spinner_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 21);\n  }\n}\nfunction ChatBotComponent_div_20_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"smart_toy\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_20_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, ChatBotComponent_div_20_mat_icon_2_Template, 2, 0, \"mat-icon\", 24);\n    i0.ɵɵtemplate(3, ChatBotComponent_div_20_mat_icon_3_Template, 2, 0, \"mat-icon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 25)(5, \"div\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c0, message_r5.sender === \"user\", message_r5.sender === \"bot\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r5.sender === \"bot\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r5.sender === \"user\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r5.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, message_r5.timestamp, \"shortTime\"));\n  }\n}\nfunction ChatBotComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_mat_hint_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 30)(1, \"small\");\n    i0.ɵɵtext(2, \"Ask about restaurant location, cuisine type, hours, etc.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_mat_spinner_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 31);\n  }\n}\nconst _c1 = function () {\n  return {\n    \"connected\": true\n  };\n};\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.isSubmitting = false;\n    this.restaurantInfo = {};\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n  }\n  ngOnInit() {\n    // Initialize with a welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Load conversation history if tenant ID is available\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Find existing bot messages\n      const existingBotMessages = this.messages.filter(m => m.sender === 'bot' && m.id !== this.messages[0].id);\n      if (message.sender === 'bot') {\n        // If this is a bot message and we already have bot messages, replace the last one\n        if (existingBotMessages.length > 0) {\n          const lastBotMessageIndex = this.messages.findIndex(m => m.id === existingBotMessages[existingBotMessages.length - 1].id);\n          if (lastBotMessageIndex !== -1) {\n            this.messages[lastBotMessageIndex] = message;\n          } else {\n            this.messages.push(message);\n          }\n        } else {\n          // Otherwise add the new message\n          this.messages.push(message);\n        }\n      } else {\n        // For user messages, just add them if they don't exist already\n        if (!this.messages.some(m => m.id === message.id)) {\n          this.messages.push(message);\n        }\n      }\n      this.scrollToBottom();\n      this.cd.detectChanges();\n    });\n    // Subscribe to connection status\n    this.connectionSubscription = this.sseService.connectionStatus$.subscribe(connected => {\n      this.isConnected = connected;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n    });\n    // Set connected to true by default - we'll connect on demand when sending messages\n    this.isConnected = true;\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.currentMessage = messageToSend;\n          this.cd.detectChanges();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Submit the collected restaurant information\n   */\n  submitInfo() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to submit information', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Extract restaurant information from chat history\n    this.extractRestaurantInfo();\n    this.isSubmitting = true;\n    this.cd.detectChanges();\n    // Prepare restaurant info with tenant details\n    const restaurantData = {\n      ...this.restaurantInfo,\n      tenantId: this.tenantId,\n      tenantName: this.tenantName\n    };\n    // Add a message to indicate we're submitting the information\n    this.messages.push({\n      id: this.generateId(),\n      text: 'Submitting your restaurant information...',\n      sender: 'bot',\n      timestamp: new Date()\n    });\n    this.scrollToBottom();\n    this.sseService.submitRestaurantInfo(this.tenantId, restaurantData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        // Add a success message\n        this.messages.push({\n          id: this.generateId(),\n          text: 'Thank you! Your restaurant information has been saved successfully.',\n          sender: 'bot',\n          timestamp: new Date()\n        });\n        this.snackBar.open('Restaurant information submitted successfully', 'Close', {\n          duration: 3000\n        });\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error submitting restaurant info:', error);\n        this.isSubmitting = false;\n        // Add an error message\n        this.messages.push({\n          id: this.generateId(),\n          text: 'Sorry, there was an error submitting your information. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        });\n        this.snackBar.open('Failed to submit restaurant information', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.submitInfo();\n        });\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Extract restaurant information from chat history\n   * This is a simple implementation - in a real app, you might want to use\n   * more sophisticated NLP techniques or structured data collection\n   */\n  extractRestaurantInfo() {\n    // Initialize restaurant info with default values\n    this.restaurantInfo = {\n      tenantId: this.tenantId,\n      location: '',\n      businessType: '',\n      cuisineType: '',\n      operatingHours: '',\n      specialties: [],\n      contactInfo: ''\n    };\n    // Extract information from user messages\n    const userMessages = this.messages.filter(m => m.sender === 'user').map(m => m.text.toLowerCase());\n    // Simple keyword-based extraction\n    for (const message of userMessages) {\n      // Location extraction\n      if (message.includes('location') || message.includes('address') || message.includes('located')) {\n        this.restaurantInfo.location = message;\n      }\n      // Business type extraction\n      if (message.includes('business') || message.includes('restaurant type') || message.includes('establishment')) {\n        this.restaurantInfo.businessType = message;\n      }\n      // Cuisine type extraction\n      if (message.includes('cuisine') || message.includes('food') || message.includes('serve')) {\n        this.restaurantInfo.cuisineType = message;\n      }\n      // Hours extraction\n      if (message.includes('hours') || message.includes('open') || message.includes('close') || message.includes('timing')) {\n        this.restaurantInfo.operatingHours = message;\n      }\n      // Contact info extraction\n      if (message.includes('contact') || message.includes('phone') || message.includes('email') || message.includes('website')) {\n        this.restaurantInfo.contactInfo = message;\n      }\n      // Specialties extraction\n      if (message.includes('special') || message.includes('signature') || message.includes('famous for')) {\n        this.restaurantInfo.specialties = [message];\n      }\n    }\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    setTimeout(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    }, 100);\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to load conversation history', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    this.sseService.loadConversationHistory(this.tenantId).subscribe({\n      next: success => {\n        this.isConnecting = false;\n        if (!success) {\n          this.snackBar.open('Failed to load conversation history', 'Retry', {\n            duration: 3000\n          }).onAction().subscribe(() => {\n            this.loadConversationHistory();\n          });\n        }\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to load conversation history', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.loadConversationHistory();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to clear conversation history', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    this.sseService.clearConversationHistory(this.tenantId).subscribe({\n      next: () => {\n        // Reset to just the welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Conversation history has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error clearing conversation history:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to clear conversation history', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.clearConversationHistory();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 13,\n      consts: [[1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Load conversation history\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear conversation history\", 3, \"disabled\", \"click\"], [1, \"connection-status\", 3, \"ngClass\"], [1, \"status-dot\"], [1, \"status-text\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bot-typing\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"align\", \"start\", 4, \"ngIf\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", \"class\", \"submit-spinner\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-avatar\"], [4, \"ngIf\"], [1, \"message-content\"], [1, \"message-text\"], [1, \"message-time\"], [1, \"bot-typing\"], [1, \"typing-indicator\"], [\"align\", \"start\"], [\"diameter\", \"20\", 1, \"submit-spinner\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\", 3);\n          i0.ɵɵtext(4, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6, \"Restaurant Information Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_8_listener() {\n            return ctx.loadConversationHistory();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"history\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_11_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"delete_sweep\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 7);\n          i0.ɵɵelement(15, \"span\", 8);\n          i0.ɵɵelementStart(16, \"span\", 9);\n          i0.ɵɵtext(17, \"Ready\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, ChatBotComponent_mat_spinner_18_Template, 1, 0, \"mat-spinner\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 11);\n          i0.ɵɵtemplate(20, ChatBotComponent_div_20_Template, 10, 11, \"div\", 12);\n          i0.ɵɵtemplate(21, ChatBotComponent_div_21_Template, 5, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 14)(23, \"mat-form-field\", 15)(24, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_24_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_24_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, ChatBotComponent_mat_hint_25_Template, 3, 0, \"mat-hint\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_26_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(27, \"mat-icon\");\n          i0.ɵɵtext(28, \"send\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 4)(30, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_30_listener() {\n            return ctx.submitInfo();\n          });\n          i0.ɵɵelementStart(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34, \"Submit Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, ChatBotComponent_mat_spinner_35_Template, 1, 0, \"mat-spinner\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(12, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConnecting);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConnecting && ctx.messages.length > 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", !ctx.isConnected || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConnected);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting || ctx.isSubmitting);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting || ctx.isSubmitting || ctx.messages.length < 3);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, i6.MatHint, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, i9.MatProgressSpinner],\n      styles: [\".chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  max-height: 500px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background-color: #57705d;\\n  color: white;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.connection-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 12px;\\n}\\n\\n.status-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 6px;\\n}\\n\\n.connected[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.disconnected[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: #f5f5f5;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  flex-direction: row-reverse;\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-avatar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  margin: 0 8px;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe;\\n  color: #0288d1;\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #388e3c;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 10px 14px;\\n  border-radius: 18px;\\n  position: relative;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-top-right-radius: 4px;\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #757575;\\n  margin-top: 4px;\\n  text-align: right;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 8px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 8px 0 8px 16px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e8f5e9;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: 60px;\\n  justify-content: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 2px;\\n  background-color: #388e3c;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.4;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ChatBotComponent_div_20_mat_icon_2_Template", "ChatBotComponent_div_20_mat_icon_3_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r5", "sender", "ɵɵadvance", "ɵɵtextInterpolate", "text", "ɵɵpipeBind2", "timestamp", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnected", "isConnecting", "isSubmitting", "restaurantInfo", "messageSubscription", "connectionSubscription", "ngOnInit", "id", "generateId", "Date", "loadConversationHistory", "messages$", "subscribe", "message", "existingBotMessages", "filter", "m", "length", "lastBotMessageIndex", "findIndex", "push", "some", "scrollToBottom", "detectChanges", "connectionStatus$", "connected", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "next", "error", "console", "onAction", "submitInfo", "extractRestaurantInfo", "restaurantData", "submitRestaurantInfo", "location", "businessType", "cuisineType", "operatingHours", "specialties", "contactInfo", "userMessages", "map", "toLowerCase", "includes", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "setTimeout", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "Math", "random", "toString", "substring", "success", "clearConversationHistory", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_8_listener", "ChatBotComponent_Template_button_click_11_listener", "ChatBotComponent_mat_spinner_18_Template", "ChatBotComponent_div_20_Template", "ChatBotComponent_div_21_Template", "ChatBotComponent_Template_input_ngModelChange_24_listener", "$event", "ChatBotComponent_Template_input_keydown_24_listener", "ChatBotComponent_mat_hint_25_Template", "ChatBotComponent_Template_button_click_26_listener", "ChatBotComponent_Template_button_click_30_listener", "ChatBotComponent_mat_spinner_35_Template", "ɵɵpureFunction0", "_c1", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "MatHint", "i7", "MatIcon", "i8", "MatInput", "i9", "MatProgressSpinner", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage, RestaurantInfo } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnected: boolean = false;\n  isConnecting: boolean = false;\n  isSubmitting: boolean = false;\n  restaurantInfo: Partial<RestaurantInfo> = {};\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Initialize with a welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Load conversation history if tenant ID is available\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Find existing bot messages\n        const existingBotMessages = this.messages.filter(m => m.sender === 'bot' && m.id !== this.messages[0].id);\n\n        if (message.sender === 'bot') {\n          // If this is a bot message and we already have bot messages, replace the last one\n          if (existingBotMessages.length > 0) {\n            const lastBotMessageIndex = this.messages.findIndex(m => m.id === existingBotMessages[existingBotMessages.length - 1].id);\n            if (lastBotMessageIndex !== -1) {\n              this.messages[lastBotMessageIndex] = message;\n            } else {\n              this.messages.push(message);\n            }\n          } else {\n            // Otherwise add the new message\n            this.messages.push(message);\n          }\n        } else {\n          // For user messages, just add them if they don't exist already\n          if (!this.messages.some(m => m.id === message.id)) {\n            this.messages.push(message);\n          }\n        }\n\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      }\n    );\n\n    // Subscribe to connection status\n    this.connectionSubscription = this.sseService.connectionStatus$.subscribe(\n      (connected: boolean) => {\n        this.isConnected = connected;\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      }\n    );\n\n    // Set connected to true by default - we'll connect on demand when sending messages\n    this.isConnected = true;\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.currentMessage = messageToSend;\n          this.cd.detectChanges();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Submit the collected restaurant information\n   */\n  submitInfo(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to submit information', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Extract restaurant information from chat history\n    this.extractRestaurantInfo();\n\n    this.isSubmitting = true;\n    this.cd.detectChanges();\n\n    // Prepare restaurant info with tenant details\n    const restaurantData = {\n      ...this.restaurantInfo,\n      tenantId: this.tenantId,\n      tenantName: this.tenantName\n    };\n\n    // Add a message to indicate we're submitting the information\n    this.messages.push({\n      id: this.generateId(),\n      text: 'Submitting your restaurant information...',\n      sender: 'bot',\n      timestamp: new Date()\n    });\n    this.scrollToBottom();\n\n    this.sseService.submitRestaurantInfo(this.tenantId, restaurantData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n\n        // Add a success message\n        this.messages.push({\n          id: this.generateId(),\n          text: 'Thank you! Your restaurant information has been saved successfully.',\n          sender: 'bot',\n          timestamp: new Date()\n        });\n\n        this.snackBar.open('Restaurant information submitted successfully', 'Close', {\n          duration: 3000\n        });\n\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error submitting restaurant info:', error);\n        this.isSubmitting = false;\n\n        // Add an error message\n        this.messages.push({\n          id: this.generateId(),\n          text: 'Sorry, there was an error submitting your information. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        });\n\n        this.snackBar.open('Failed to submit restaurant information', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.submitInfo();\n        });\n\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Extract restaurant information from chat history\n   * This is a simple implementation - in a real app, you might want to use\n   * more sophisticated NLP techniques or structured data collection\n   */\n  private extractRestaurantInfo(): void {\n    // Initialize restaurant info with default values\n    this.restaurantInfo = {\n      tenantId: this.tenantId,\n      location: '',\n      businessType: '',\n      cuisineType: '',\n      operatingHours: '',\n      specialties: [],\n      contactInfo: ''\n    };\n\n    // Extract information from user messages\n    const userMessages = this.messages.filter(m => m.sender === 'user').map(m => m.text.toLowerCase());\n\n    // Simple keyword-based extraction\n    for (const message of userMessages) {\n      // Location extraction\n      if (message.includes('location') || message.includes('address') || message.includes('located')) {\n        this.restaurantInfo.location = message;\n      }\n\n      // Business type extraction\n      if (message.includes('business') || message.includes('restaurant type') || message.includes('establishment')) {\n        this.restaurantInfo.businessType = message;\n      }\n\n      // Cuisine type extraction\n      if (message.includes('cuisine') || message.includes('food') || message.includes('serve')) {\n        this.restaurantInfo.cuisineType = message;\n      }\n\n      // Hours extraction\n      if (message.includes('hours') || message.includes('open') || message.includes('close') || message.includes('timing')) {\n        this.restaurantInfo.operatingHours = message;\n      }\n\n      // Contact info extraction\n      if (message.includes('contact') || message.includes('phone') || message.includes('email') || message.includes('website')) {\n        this.restaurantInfo.contactInfo = message;\n      }\n\n      // Specialties extraction\n      if (message.includes('special') || message.includes('signature') || message.includes('famous for')) {\n        this.restaurantInfo.specialties = [message];\n      }\n    }\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    setTimeout(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    }, 100);\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to load conversation history', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    this.sseService.loadConversationHistory(this.tenantId).subscribe({\n      next: (success) => {\n        this.isConnecting = false;\n        if (!success) {\n          this.snackBar.open('Failed to load conversation history', 'Retry', {\n            duration: 3000\n          }).onAction().subscribe(() => {\n            this.loadConversationHistory();\n          });\n        }\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to load conversation history', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.loadConversationHistory();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to clear conversation history', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    this.sseService.clearConversationHistory(this.tenantId).subscribe({\n      next: () => {\n        // Reset to just the welcome message\n        this.messages = [\n          {\n            id: this.generateId(),\n            text: 'Conversation history has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }\n        ];\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error clearing conversation history:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to clear conversation history', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.clearConversationHistory();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n}\n", "<div class=\"chat-container\">\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">chat</mat-icon>\n      <span>Restaurant Information Assistant</span>\n    </div>\n    <div class=\"chat-actions\">\n      <button mat-icon-button matTooltip=\"Load conversation history\" (click)=\"loadConversationHistory()\" [disabled]=\"isConnecting\">\n        <mat-icon>history</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Clear conversation history\" (click)=\"clearConversationHistory()\" [disabled]=\"isConnecting\">\n        <mat-icon>delete_sweep</mat-icon>\n      </button>\n    </div>\n    <div class=\"connection-status\" [ngClass]=\"{'connected': true}\">\n      <span class=\"status-dot\"></span>\n      <span class=\"status-text\">Ready</span>\n      <mat-spinner *ngIf=\"isConnecting\" diameter=\"20\"></mat-spinner>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n      <div class=\"message-avatar\">\n        <mat-icon *ngIf=\"message.sender === 'bot'\">smart_toy</mat-icon>\n        <mat-icon *ngIf=\"message.sender === 'user'\">person</mat-icon>\n      </div>\n      <div class=\"message-content\">\n        <div class=\"message-text\">{{ message.text }}</div>\n        <div class=\"message-time\">{{ message.timestamp | date:'shortTime' }}</div>\n      </div>\n    </div>\n\n    <!-- Loading indicator when waiting for a response -->\n    <div *ngIf=\"isConnecting && messages.length > 0\" class=\"bot-typing\">\n      <div class=\"typing-indicator\">\n        <span></span>\n        <span></span>\n        <span></span>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"!isConnected || isSubmitting\">\n      <mat-hint align=\"start\" *ngIf=\"isConnected\">\n        <small>Ask about restaurant location, cuisine type, hours, etc.</small>\n      </mat-hint>\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting || isSubmitting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n\n  <div class=\"chat-actions\">\n    <button mat-raised-button color=\"accent\" (click)=\"submitInfo()\" [disabled]=\"isConnecting || isSubmitting || messages.length < 3\">\n      <mat-icon>save</mat-icon>\n      <span>Submit Information</span>\n      <mat-spinner *ngIf=\"isSubmitting\" diameter=\"20\" class=\"submit-spinner\"></mat-spinner>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;;;;;;;;;;;;;ICSvEC,EAAA,CAAAC,SAAA,sBAA8D;;;;;IAO5DD,EAAA,CAAAE,cAAA,eAA2C;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;;IAC/DJ,EAAA,CAAAE,cAAA,eAA4C;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;;;;;;;;IAHjEJ,EAAA,CAAAE,cAAA,cAAiK;IAE7JF,EAAA,CAAAK,UAAA,IAAAC,2CAAA,uBAA+D;IAC/DN,EAAA,CAAAK,UAAA,IAAAE,2CAAA,uBAA6D;IAC/DP,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAA6B;IACDF,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClDJ,EAAA,CAAAE,cAAA,cAA0B;IAAAF,EAAA,CAAAG,MAAA,GAA0C;;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAPdJ,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,aAAAD,UAAA,CAAAC,MAAA,YAAgG;IAEjJZ,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAQ,UAAA,SAAAG,UAAA,CAAAC,MAAA,WAA8B;IAC9BZ,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAQ,UAAA,SAAAG,UAAA,CAAAC,MAAA,YAA+B;IAGhBZ,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,iBAAA,CAAAH,UAAA,CAAAI,IAAA,CAAkB;IAClBf,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAgB,WAAA,OAAAL,UAAA,CAAAM,SAAA,eAA0C;;;;;IAKxEjB,EAAA,CAAAE,cAAA,cAAoE;IAEhEF,EAAA,CAAAC,SAAA,WAAa;IAGfD,EAAA,CAAAI,YAAA,EAAM;;;;;IAWNJ,EAAA,CAAAE,cAAA,mBAA4C;IACnCF,EAAA,CAAAG,MAAA,+DAAwD;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IAYzEJ,EAAA,CAAAC,SAAA,sBAAqF;;;;;;;;ADhD3F,MAkBaiB,gBAAgB;EAc3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAhBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,cAAc,GAA4B,EAAE;IAEpC,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;EAMvD;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBpB,IAAI,EAAE,2KAA2K;MACjLH,MAAM,EAAE,KAAK;MACbK,SAAS,EAAE,IAAImB,IAAI;KACpB,CACF;IAED;IACA,IAAI,IAAI,CAACb,QAAQ,EAAE;MACjB,IAAI,CAACc,uBAAuB,EAAE;;IAGhC;IACA,IAAI,CAACN,mBAAmB,GAAG,IAAI,CAACX,UAAU,CAACkB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,MAAMC,mBAAmB,GAAG,IAAI,CAAChB,QAAQ,CAACiB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,KAAK,IAAI+B,CAAC,CAACT,EAAE,KAAK,IAAI,CAACT,QAAQ,CAAC,CAAC,CAAC,CAACS,EAAE,CAAC;MAEzG,IAAIM,OAAO,CAAC5B,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,IAAI6B,mBAAmB,CAACG,MAAM,GAAG,CAAC,EAAE;UAClC,MAAMC,mBAAmB,GAAG,IAAI,CAACpB,QAAQ,CAACqB,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKO,mBAAmB,CAACA,mBAAmB,CAACG,MAAM,GAAG,CAAC,CAAC,CAACV,EAAE,CAAC;UACzH,IAAIW,mBAAmB,KAAK,CAAC,CAAC,EAAE;YAC9B,IAAI,CAACpB,QAAQ,CAACoB,mBAAmB,CAAC,GAAGL,OAAO;WAC7C,MAAM;YACL,IAAI,CAACf,QAAQ,CAACsB,IAAI,CAACP,OAAO,CAAC;;SAE9B,MAAM;UACL;UACA,IAAI,CAACf,QAAQ,CAACsB,IAAI,CAACP,OAAO,CAAC;;OAE9B,MAAM;QACL;QACA,IAAI,CAAC,IAAI,CAACf,QAAQ,CAACuB,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,CAAC,EAAE;UACjD,IAAI,CAACT,QAAQ,CAACsB,IAAI,CAACP,OAAO,CAAC;;;MAI/B,IAAI,CAACS,cAAc,EAAE;MACrB,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;IACzB,CAAC,CACF;IAED;IACA,IAAI,CAAClB,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAAC+B,iBAAiB,CAACZ,SAAS,CACtEa,SAAkB,IAAI;MACrB,IAAI,CAACzB,WAAW,GAAGyB,SAAS;MAC5B,IAAI,CAACxB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;IACzB,CAAC,CACF;IAED;IACA,IAAI,CAACvB,WAAW,GAAG,IAAI;EACzB;EAEA0B,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACtB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACuB,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACtB,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACsB,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAClC,UAAU,CAACmC,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC9B,cAAc,CAAC+B,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACoC,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC/B,YAAY,GAAG,IAAI;IAExB;IACA,MAAMgC,aAAa,GAAG,IAAI,CAAClC,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA,IAAI,CAAC9B,UAAU,CAACoC,WAAW,CAAC,IAAI,CAACjC,QAAQ,EAAEqC,aAAa,CAAC,CAACrB,SAAS,CAAC;MAClEsB,IAAI,EAAEA,CAAA,KAAK;QACT;QACA,IAAI,CAACjC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,QAAQ,CAACoC,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC,CAACK,QAAQ,EAAE,CAACzB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACb,cAAc,GAAGkC,aAAa;UACnC,IAAI,CAACvC,EAAE,CAAC6B,aAAa,EAAE;QACzB,CAAC,CAAC;QACF,IAAI,CAAC7B,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAe,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC1C,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACoC,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;QACzEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAACO,qBAAqB,EAAE;IAE5B,IAAI,CAACrC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA,MAAMiB,cAAc,GAAG;MACrB,GAAG,IAAI,CAACrC,cAAc;MACtBP,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA;KAClB;IAED;IACA,IAAI,CAACC,QAAQ,CAACsB,IAAI,CAAC;MACjBb,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBpB,IAAI,EAAE,2CAA2C;MACjDH,MAAM,EAAE,KAAK;MACbK,SAAS,EAAE,IAAImB,IAAI;KACpB,CAAC;IACF,IAAI,CAACa,cAAc,EAAE;IAErB,IAAI,CAAC7B,UAAU,CAACgD,oBAAoB,CAAC,IAAI,CAAC7C,QAAQ,EAAE4C,cAAc,CAAC,CAAC5B,SAAS,CAAC;MAC5EsB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChC,YAAY,GAAG,KAAK;QAEzB;QACA,IAAI,CAACJ,QAAQ,CAACsB,IAAI,CAAC;UACjBb,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBpB,IAAI,EAAE,qEAAqE;UAC3EH,MAAM,EAAE,KAAK;UACbK,SAAS,EAAE,IAAImB,IAAI;SACpB,CAAC;QAEF,IAAI,CAACd,QAAQ,CAACoC,IAAI,CAAC,+CAA+C,EAAE,OAAO,EAAE;UAC3EC,QAAQ,EAAE;SACX,CAAC;QAEF,IAAI,CAACV,cAAc,EAAE;QACrB,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACjC,YAAY,GAAG,KAAK;QAEzB;QACA,IAAI,CAACJ,QAAQ,CAACsB,IAAI,CAAC;UACjBb,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBpB,IAAI,EAAE,0EAA0E;UAChFH,MAAM,EAAE,KAAK;UACbK,SAAS,EAAE,IAAImB,IAAI;SACpB,CAAC;QAEF,IAAI,CAACd,QAAQ,CAACoC,IAAI,CAAC,yCAAyC,EAAE,OAAO,EAAE;UACrEC,QAAQ,EAAE;SACX,CAAC,CAACK,QAAQ,EAAE,CAACzB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAAC0B,UAAU,EAAE;QACnB,CAAC,CAAC;QAEF,IAAI,CAAChB,cAAc,EAAE;QACrB,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;;EAKQgB,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAACpC,cAAc,GAAG;MACpBP,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB8C,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE;KACd;IAED;IACA,MAAMC,YAAY,GAAG,IAAI,CAAClD,QAAQ,CAACiB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/B,MAAM,KAAK,MAAM,CAAC,CAACgE,GAAG,CAACjC,CAAC,IAAIA,CAAC,CAAC5B,IAAI,CAAC8D,WAAW,EAAE,CAAC;IAElG;IACA,KAAK,MAAMrC,OAAO,IAAImC,YAAY,EAAE;MAClC;MACA,IAAInC,OAAO,CAACsC,QAAQ,CAAC,UAAU,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC9F,IAAI,CAAChD,cAAc,CAACuC,QAAQ,GAAG7B,OAAO;;MAGxC;MACA,IAAIA,OAAO,CAACsC,QAAQ,CAAC,UAAU,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,iBAAiB,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC5G,IAAI,CAAChD,cAAc,CAACwC,YAAY,GAAG9B,OAAO;;MAG5C;MACA,IAAIA,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,MAAM,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxF,IAAI,CAAChD,cAAc,CAACyC,WAAW,GAAG/B,OAAO;;MAG3C;MACA,IAAIA,OAAO,CAACsC,QAAQ,CAAC,OAAO,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,MAAM,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,OAAO,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACpH,IAAI,CAAChD,cAAc,CAAC0C,cAAc,GAAGhC,OAAO;;MAG9C;MACA,IAAIA,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,OAAO,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,OAAO,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACxH,IAAI,CAAChD,cAAc,CAAC4C,WAAW,GAAGlC,OAAO;;MAG3C;MACA,IAAIA,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,WAAW,CAAC,IAAItC,OAAO,CAACsC,QAAQ,CAAC,YAAY,CAAC,EAAE;QAClG,IAAI,CAAChD,cAAc,CAAC2C,WAAW,GAAG,CAACjC,OAAO,CAAC;;;EAGjD;EAEA;;;;EAIAuC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAC3B,WAAW,EAAE;;EAEtB;EAEA;;;EAGQP,cAAcA,CAAA;IACpBmC,UAAU,CAAC,MAAK;MACd,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQtD,UAAUA,CAAA;IAChB,OAAOuD,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAxD,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACd,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACoC,IAAI,CAAC,oDAAoD,EAAE,OAAO,EAAE;QAChFC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC/B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;IAEvB,IAAI,CAAC9B,UAAU,CAACiB,uBAAuB,CAAC,IAAI,CAACd,QAAQ,CAAC,CAACgB,SAAS,CAAC;MAC/DsB,IAAI,EAAGiC,OAAO,IAAI;QAChB,IAAI,CAAClE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACkE,OAAO,EAAE;UACZ,IAAI,CAACxE,QAAQ,CAACoC,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;YACjEC,QAAQ,EAAE;WACX,CAAC,CAACK,QAAQ,EAAE,CAACzB,SAAS,CAAC,MAAK;YAC3B,IAAI,CAACF,uBAAuB,EAAE;UAChC,CAAC,CAAC;;QAEJ,IAAI,CAAChB,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,QAAQ,CAACoC,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;UACjEC,QAAQ,EAAE;SACX,CAAC,CAACK,QAAQ,EAAE,CAACzB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACF,uBAAuB,EAAE;QAChC,CAAC,CAAC;QACF,IAAI,CAAChB,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGA6C,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACxE,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACoC,IAAI,CAAC,qDAAqD,EAAE,OAAO,EAAE;QACjFC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC/B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;IAEvB,IAAI,CAAC9B,UAAU,CAAC2E,wBAAwB,CAAC,IAAI,CAACxE,QAAQ,CAAC,CAACgB,SAAS,CAAC;MAChEsB,IAAI,EAAEA,CAAA,KAAK;QACT;QACA,IAAI,CAACpC,QAAQ,GAAG,CACd;UACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBpB,IAAI,EAAE,kEAAkE;UACxEH,MAAM,EAAE,KAAK;UACbK,SAAS,EAAE,IAAImB,IAAI;SACpB,CACF;QACD,IAAI,CAACR,YAAY,GAAG,KAAK;QACzB,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,QAAQ,CAACoC,IAAI,CAAC,sCAAsC,EAAE,OAAO,EAAE;UAClEC,QAAQ,EAAE;SACX,CAAC,CAACK,QAAQ,EAAE,CAACzB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACwD,wBAAwB,EAAE;QACjC,CAAC,CAAC;QACF,IAAI,CAAC1E,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;;;uBA5XWhC,gBAAgB,EAAAlB,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAhG,EAAA,CAAAmG,iBAAA,GAAAnG,EAAA,CAAAgG,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBnF,gBAAgB;MAAAoF,SAAA;MAAAC,MAAA;QAAAhF,QAAA;QAAAC,UAAA;MAAA;MAAAgF,UAAA;MAAAC,QAAA,GAAAzG,EAAA,CAAA0G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjC7BhH,EAAA,CAAAE,cAAA,aAA4B;UAGMF,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3CJ,EAAA,CAAAE,cAAA,WAAM;UAAAF,EAAA,CAAAG,MAAA,uCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAE/CJ,EAAA,CAAAE,cAAA,aAA0B;UACuCF,EAAA,CAAAkH,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAA5E,uBAAA,EAAyB;UAAA,EAAC;UAChGrC,EAAA,CAAAE,cAAA,eAAU;UAAAF,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAE9BJ,EAAA,CAAAE,cAAA,iBAA+H;UAA/DF,EAAA,CAAAkH,UAAA,mBAAAE,mDAAA;YAAA,OAASH,GAAA,CAAAlB,wBAAA,EAA0B;UAAA,EAAC;UAClG/F,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAGrCJ,EAAA,CAAAE,cAAA,cAA+D;UAC7DF,EAAA,CAAAC,SAAA,eAAgC;UAChCD,EAAA,CAAAE,cAAA,eAA0B;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACtCJ,EAAA,CAAAK,UAAA,KAAAgH,wCAAA,0BAA8D;UAChErH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAE,cAAA,eAA2B;UACzBF,EAAA,CAAAK,UAAA,KAAAiH,gCAAA,oBASM;UAGNtH,EAAA,CAAAK,UAAA,KAAAkH,gCAAA,kBAMM;UACRvH,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,eAAwB;UAGbF,EAAA,CAAAkH,UAAA,2BAAAM,0DAAAC,MAAA;YAAA,OAAAR,GAAA,CAAAvF,cAAA,GAAA+F,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBR,GAAA,CAAAlC,UAAA,CAAA0C,MAAA,CAAkB;UAAA,EAFD;UADnCzH,EAAA,CAAAI,YAAA,EAIiD;UACjDJ,EAAA,CAAAK,UAAA,KAAAsH,qCAAA,uBAEW;UACb3H,EAAA,CAAAI,YAAA,EAAiB;UACjBJ,EAAA,CAAAE,cAAA,kBAAiI;UAA5FF,EAAA,CAAAkH,UAAA,mBAAAU,mDAAA;YAAA,OAASX,GAAA,CAAAzD,WAAA,EAAa;UAAA,EAAC;UAC1DxD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAI7BJ,EAAA,CAAAE,cAAA,cAA0B;UACiBF,EAAA,CAAAkH,UAAA,mBAAAW,mDAAA;YAAA,OAASZ,GAAA,CAAAhD,UAAA,EAAY;UAAA,EAAC;UAC7DjE,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACzBJ,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC/BJ,EAAA,CAAAK,UAAA,KAAAyH,wCAAA,0BAAqF;UACvF9H,EAAA,CAAAI,YAAA,EAAS;;;UAzD4FJ,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAQ,UAAA,aAAAyG,GAAA,CAAArF,YAAA,CAAyB;UAGvB5B,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAQ,UAAA,aAAAyG,GAAA,CAAArF,YAAA,CAAyB;UAIjG5B,EAAA,CAAAa,SAAA,GAA+B;UAA/Bb,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAA+H,eAAA,KAAAC,GAAA,EAA+B;UAG9ChI,EAAA,CAAAa,SAAA,GAAkB;UAAlBb,EAAA,CAAAQ,UAAA,SAAAyG,GAAA,CAAArF,YAAA,CAAkB;UAKT5B,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAAQ,UAAA,YAAAyG,GAAA,CAAAxF,QAAA,CAAW;UAY9BzB,EAAA,CAAAa,SAAA,GAAyC;UAAzCb,EAAA,CAAAQ,UAAA,SAAAyG,GAAA,CAAArF,YAAA,IAAAqF,GAAA,CAAAxF,QAAA,CAAAmB,MAAA,KAAyC;UAYtC5C,EAAA,CAAAa,SAAA,GAA4B;UAA5Bb,EAAA,CAAAQ,UAAA,YAAAyG,GAAA,CAAAvF,cAAA,CAA4B,cAAAuF,GAAA,CAAAtF,WAAA,IAAAsF,GAAA,CAAApF,YAAA;UAIV7B,EAAA,CAAAa,SAAA,GAAiB;UAAjBb,EAAA,CAAAQ,UAAA,SAAAyG,GAAA,CAAAtF,WAAA,CAAiB;UAIiB3B,EAAA,CAAAa,SAAA,GAAmE;UAAnEb,EAAA,CAAAQ,UAAA,cAAAyG,GAAA,CAAAvF,cAAA,CAAA+B,IAAA,MAAAwD,GAAA,CAAArF,YAAA,IAAAqF,GAAA,CAAApF,YAAA,CAAmE;UAMhE7B,EAAA,CAAAa,SAAA,GAAgE;UAAhEb,EAAA,CAAAQ,UAAA,aAAAyG,GAAA,CAAArF,YAAA,IAAAqF,GAAA,CAAApF,YAAA,IAAAoF,GAAA,CAAAxF,QAAA,CAAAmB,MAAA,KAAgE;UAGhH5C,EAAA,CAAAa,SAAA,GAAkB;UAAlBb,EAAA,CAAAQ,UAAA,SAAAyG,GAAA,CAAApF,YAAA,CAAkB;;;qBD5ClCtC,YAAY,EAAA0I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ7I,WAAW,EAAA8I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXhJ,mBAAmB,EACnBC,eAAe,EAAAgJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACflJ,aAAa,EACbC,kBAAkB,EAAAkJ,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,OAAA,EAClBnJ,aAAa,EAAAoJ,EAAA,CAAAC,OAAA,EACbpJ,cAAc,EAAAqJ,EAAA,CAAAC,QAAA,EACdrJ,wBAAwB,EAAAsJ,EAAA,CAAAC,kBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMftI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}