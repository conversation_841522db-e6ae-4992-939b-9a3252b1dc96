{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SseService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n    this.eventSource = null;\n    this.messageSubject = new Subject();\n    this.connectionStatusSubject = new Subject();\n    // Flag to track if a request is in progress\n    this.requestInProgress = false;\n    // Timeout reference for auto-closing connections\n    this.connectionTimeout = null;\n  }\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect() {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId, query) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n    const responseSubject = new Subject();\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      this.eventSource = new EventSource(`${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`);\n      // Handle specific event types like in the HTML example\n      // Handle regular messages\n      this.eventSource.addEventListener('message', event => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                responseSubject.next(data.content);\n              }\n              break;\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n      // Handle completion event\n      this.eventSource.addEventListener('complete', _event => {\n        console.log('Stream complete event received');\n        // End of stream\n        responseSubject.complete();\n        this.eventSource?.close();\n        this.eventSource = null;\n        // Reset the flag when the request is complete\n        this.requestInProgress = false;\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      });\n      // Handle general errors\n      this.eventSource.addEventListener('error', event => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n        // Don't immediately close on all errors - some might be recoverable\n      });\n      // Handle connection close\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n      // Handle errors\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect() {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId, message) {\n    // Create a new message from the user\n    const userMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n    // Create an initial bot message with a cursor\n    const initialBotMessage = {\n      id: botMessageId,\n      text: '',\n      sender: 'bot',\n      timestamp: new Date()\n    };\n    // Send the initial message\n    this.messageSubject.next(initialBotMessage);\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: chunk => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n        // Add to the full response\n        fullResponse += chunk;\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = fullResponse + '<span class=\"blinking-cursor\">|</span>';\n        // Update the bot message with the new content\n        const updatedMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n          });\n        } else {\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage = {\n            id: botMessageId,\n            text: fullResponse,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n          });\n        }\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: error => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n      }\n    });\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId, restaurantInfo) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` + `Location: ${restaurantInfo.location || 'Not specified'}\\n` + `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` + `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` + `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` + `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` + `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n  /**\n   * Get the message stream\n   */\n  get messages$() {\n    return this.messageSubject.asObservable();\n  }\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$() {\n    return this.connectionStatusSubject.asObservable();\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId) {\n    return this.http.get(`${this.baseUrl}llm/conversation_history?tenant_id=${tenantId}`).pipe(map(response => {\n      if (response && response.messages) {\n        // Convert backend messages to frontend ChatMessage format\n        return response.messages.map(msg => ({\n          id: msg.id ? msg.id.toString() : this.generateId(),\n          text: msg.content,\n          sender: msg.type === 'human' ? 'user' : 'bot',\n          timestamp: new Date(msg.created_at)\n        }));\n      }\n      return [];\n    }), catchError(error => {\n      console.error('Error fetching conversation history:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  clearConversationHistory(tenantId) {\n    return this.http.post(`${this.baseUrl}llm/clear_history?tenant_id=${tenantId}`, {}).pipe(catchError(error => {\n      console.error('Error clearing conversation history:', error);\n      return throwError(() => new Error('Failed to clear conversation history'));\n    }));\n  }\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   */\n  loadConversationHistory(tenantId) {\n    return this.getConversationHistory(tenantId).pipe(catchError(error => {\n      console.error('Error loading conversation history:', error);\n      return of([]);\n    }));\n  }\n  static {\n    this.ɵfac = function SseService_Factory(t) {\n      return new (t || SseService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SseService,\n      factory: SseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SseService };", "map": {"version": 3, "names": ["Subject", "throwError", "of", "catchError", "map", "environment", "SseService", "constructor", "http", "baseUrl", "engineUrl", "eventSource", "messageSubject", "connectionStatusSubject", "requestInProgress", "connectionTimeout", "connect", "next", "connectionStatus$", "streamResponse", "tenantId", "query", "console", "log", "clearTimeout", "close", "setTimeout", "responseSubject", "complete", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "timestamp", "Date", "getTime", "EventSource", "addEventListener", "event", "data", "JSON", "parse", "type", "content", "error", "Error", "warn", "_event", "customEvent", "onerror", "readyState", "disconnect", "onopen", "asObservable", "sendMessage", "message", "userMessage", "id", "generateId", "text", "sender", "botMessageId", "fullResponse", "initialBotMessage", "subscribe", "chunk", "trim", "textWithCursor", "updatedMessage", "Promise", "resolve", "then", "fallbackMessage", "finalMessage", "errorMessage", "submitRestaurantInfo", "restaurantInfo", "infoSummary", "location", "businessType", "cuisineType", "operatingHours", "specialties", "join", "contactInfo", "messages$", "Math", "random", "toString", "substring", "getConversationHistory", "get", "pipe", "response", "messages", "msg", "created_at", "clearConversationHistory", "post", "loadConversationHistory", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/sse.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, Subject, throwError, of } from 'rxjs';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { ChatMessage } from '../models/chat-message.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SseService {\n  private baseUrl: string = environment.engineUrl;\n  private eventSource: EventSource | null = null;\n  private messageSubject = new Subject<ChatMessage>();\n  private connectionStatusSubject = new Subject<boolean>();\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect(): Observable<boolean> {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n\n  // Flag to track if a request is in progress\n  private requestInProgress = false;\n\n  // Timeout reference for auto-closing connections\n  private connectionTimeout: any = null;\n\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId: string, query: string): Observable<string> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n\n    const responseSubject = new Subject<string>();\n\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      this.eventSource = new EventSource(`${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`);\n\n      // Handle specific event types like in the HTML example\n\n      // Handle regular messages\n      this.eventSource.addEventListener('message', (event) => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                responseSubject.next(data.content);\n              }\n              break;\n\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n\n      // Handle completion event\n      this.eventSource.addEventListener('complete', (_event) => {\n        console.log('Stream complete event received');\n        // End of stream\n        responseSubject.complete();\n        this.eventSource?.close();\n        this.eventSource = null;\n        // Reset the flag when the request is complete\n        this.requestInProgress = false;\n\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      });\n\n      // Handle general errors\n      this.eventSource.addEventListener('error', (event) => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event as any;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n\n        // Don't immediately close on all errors - some might be recoverable\n      });\n\n      // Handle connection close\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n\n      // Handle errors\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect(): void {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId: string, message: string): Observable<ChatMessage> {\n    // Create a new message from the user\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n\n    // Create an initial bot message with a cursor\n    const initialBotMessage: ChatMessage = {\n      id: botMessageId,\n      text: '',\n      sender: 'bot',\n      timestamp: new Date()\n    };\n\n    // Send the initial message\n    this.messageSubject.next(initialBotMessage);\n\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: (chunk: string) => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n\n        // Add to the full response\n        fullResponse += chunk;\n\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = fullResponse + '<span class=\"blinking-cursor\">|</span>';\n\n        // Update the bot message with the new content\n        const updatedMessage: ChatMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage: ChatMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n          });\n        } else {\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage: ChatMessage = {\n            id: botMessageId,\n            text: fullResponse,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n          });\n        }\n\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: (error) => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage: ChatMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n      }\n    });\n\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId: string, restaurantInfo: any): Observable<any> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` +\n      `Location: ${restaurantInfo.location || 'Not specified'}\\n` +\n      `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` +\n      `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` +\n      `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` +\n      `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` +\n      `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n\n  /**\n   * Get the message stream\n   */\n  get messages$(): Observable<ChatMessage> {\n    return this.messageSubject.asObservable();\n  }\n\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$(): Observable<boolean> {\n    return this.connectionStatusSubject.asObservable();\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId: string): Observable<ChatMessage[]> {\n    return this.http.get<any>(`${this.baseUrl}llm/conversation_history?tenant_id=${tenantId}`)\n      .pipe(\n        map(response => {\n          if (response && response.messages) {\n            // Convert backend messages to frontend ChatMessage format\n            return response.messages.map((msg: any) => ({\n              id: msg.id ? msg.id.toString() : this.generateId(),\n              text: msg.content,\n              sender: msg.type === 'human' ? 'user' : 'bot',\n              timestamp: new Date(msg.created_at)\n            }));\n          }\n          return [];\n        }),\n        catchError(error => {\n          console.error('Error fetching conversation history:', error);\n          return of([]);\n        })\n      );\n  }\n\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  clearConversationHistory(tenantId: string): Observable<any> {\n    return this.http.post<any>(`${this.baseUrl}llm/clear_history?tenant_id=${tenantId}`, {})\n      .pipe(\n        catchError(error => {\n          console.error('Error clearing conversation history:', error);\n          return throwError(() => new Error('Failed to clear conversation history'));\n        })\n      );\n  }\n\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   */\n  loadConversationHistory(tenantId: string): Observable<ChatMessage[]> {\n    return this.getConversationHistory(tenantId).pipe(\n      catchError(error => {\n        console.error('Error loading conversation history:', error);\n        return of([]);\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,OAAO,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAE1D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,UAAU;EAMrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IALhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;IACvC,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAG,IAAIZ,OAAO,EAAe;IAC3C,KAAAa,uBAAuB,GAAG,IAAIb,OAAO,EAAW;IAcxD;IACQ,KAAAc,iBAAiB,GAAG,KAAK;IAEjC;IACQ,KAAAC,iBAAiB,GAAQ,IAAI;EAhBG;EAExC;;;EAGAC,OAAOA,CAAA;IACL;IACA;IACA,IAAI,CAACH,uBAAuB,CAACI,IAAI,CAAC,IAAI,CAAC;IACvC,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAQA;;;;;EAKAC,cAAcA,CAACC,QAAgB,EAAEC,KAAa;IAC5C;IACA,IAAI,IAAI,CAACP,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,OAAOrB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAGjB;IACA,IAAI,CAACY,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACc,KAAK,EAAE;MACxB,IAAI,CAACd,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,CAACI,iBAAiB,GAAGW,UAAU,CAAC,MAAK;MACvCJ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI,IAAI,CAACZ,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACc,KAAK,EAAE;QACxB,IAAI,CAACd,WAAW,GAAG,IAAI;;MAEzB,IAAI,CAACG,iBAAiB,GAAG,KAAK;MAC9Ba,eAAe,CAACC,QAAQ,EAAE;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX,MAAMD,eAAe,GAAG,IAAI3B,OAAO,EAAU;IAE7C,IAAI;MACF;MACA,MAAM6B,YAAY,GAAGC,kBAAkB,CAACT,KAAK,CAAC;MAC9C;MACA;MACA,MAAMU,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;MACtC,IAAI,CAACtB,WAAW,GAAG,IAAIuB,WAAW,CAAC,GAAG,IAAI,CAACzB,OAAO,iBAAiBoB,YAAY,cAAcT,QAAQ,MAAMW,SAAS,EAAE,CAAC;MAEvH;MAEA;MACA,IAAI,CAACpB,WAAW,CAACwB,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAI;QACrD,IAAI;UACF;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEc,IAAI,CAAC;UAEtC;UACA,QAAQA,IAAI,CAACG,IAAI;YACf,KAAK,OAAO;cACVlB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC7B;YAEF,KAAK,OAAO;cACV;cACA,IAAIc,IAAI,CAACI,OAAO,EAAE;gBAChBd,eAAe,CAACV,IAAI,CAACoB,IAAI,CAACI,OAAO,CAAC;;cAEpC;YAEF,KAAK,KAAK;cACRnB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BI,eAAe,CAACC,QAAQ,EAAE;cAC1B;YAEF,KAAK,OAAO;cACVN,OAAO,CAACoB,KAAK,CAAC,eAAe,EAAEL,IAAI,CAACI,OAAO,CAAC;cAC5Cd,eAAe,CAACe,KAAK,CAAC,IAAIC,KAAK,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC;cAC9C;YAEF,KAAK,MAAM;cACT;cACA;YAEF;cACEnB,OAAO,CAACsB,IAAI,CAAC,uBAAuB,EAAEP,IAAI,CAACG,IAAI,CAAC;;SAErD,CAAC,OAAOE,KAAK,EAAE;UACdpB,OAAO,CAACoB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAEtD,CAAC,CAAC;MAEF;MACA,IAAI,CAAC/B,WAAW,CAACwB,gBAAgB,CAAC,UAAU,EAAGU,MAAM,IAAI;QACvDvB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C;QACAI,eAAe,CAACC,QAAQ,EAAE;QAC1B,IAAI,CAACjB,WAAW,EAAEc,KAAK,EAAE;QACzB,IAAI,CAACd,WAAW,GAAG,IAAI;QACvB;QACA,IAAI,CAACG,iBAAiB,GAAG,KAAK;QAE9B;QACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;UACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;MAEjC,CAAC,CAAC;MAEF;MACA,IAAI,CAACJ,WAAW,CAACwB,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;QACnDd,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEN,KAAK,CAAC;QACxC;QACA,MAAMU,WAAW,GAAGV,KAAY;QAChC,IAAIU,WAAW,CAACT,IAAI,EAAE;UACpBf,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEI,WAAW,CAACT,IAAI,CAAC;;QAGhD;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC1B,WAAW,CAACoC,OAAO,GAAIL,KAAK,IAAI;QACnCpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAAC/B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqC,UAAU,KAAK,CAAC,EAAE;UACzD1B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C,IAAI,CAACV,uBAAuB,CAACI,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACqB,UAAU,EAAE;;MAErB,CAAC;MAED;MACA,IAAI,CAACtC,WAAW,CAACuC,MAAM,GAAG,MAAK;QAC7B5B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACV,uBAAuB,CAACI,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC;MAED;MACA,IAAI,CAACN,WAAW,CAACoC,OAAO,GAAIL,KAAK,IAAI;QACnCpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAAC/B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqC,UAAU,KAAK,CAAC,EAAE;UACzD,IAAI,CAACnC,uBAAuB,CAACI,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACqB,UAAU,EAAE;UACjB;UACA,IAAI,CAACnC,iBAAiB,GAAG,KAAK;;MAElC,CAAC;MAED,OAAOa,eAAe,CAACwB,YAAY,EAAE;KACtC,CAAC,OAAOT,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC7B,uBAAuB,CAACI,IAAI,CAAC,KAAK,CAAC;MACxC;MACA,IAAI,CAACH,iBAAiB,GAAG,KAAK;MAC9B,OAAOb,UAAU,CAAC,MAAM,IAAI0C,KAAK,CAAC,oCAAoC,CAAC,CAAC;;EAE5E;EAEA;;;EAGAM,UAAUA,CAAA;IACR,IAAI,IAAI,CAACtC,WAAW,EAAE;MACpBW,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,IAAI,CAACZ,WAAW,CAACc,KAAK,EAAE;MACxB,IAAI,CAACd,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,uBAAuB,CAACI,IAAI,CAAC,KAAK,CAAC;;IAG1C;IACA,IAAI,IAAI,CAACF,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,CAACD,iBAAiB,GAAG,KAAK;EAChC;EAEA;;;;;EAKAsC,WAAWA,CAAChC,QAAgB,EAAEiC,OAAe;IAC3C;IACA,MAAMC,WAAW,GAAgB;MAC/BC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,IAAI,EAAEJ,OAAO;MACbK,MAAM,EAAE,MAAM;MACd3B,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,IAAI,CAACpB,cAAc,CAACK,IAAI,CAACqC,WAAW,CAAC;IAErC;IACA,MAAMK,YAAY,GAAG,IAAI,CAACH,UAAU,EAAE;IACtC,IAAII,YAAY,GAAG,EAAE;IAErB;IACA,MAAMC,iBAAiB,GAAgB;MACrCN,EAAE,EAAEI,YAAY;MAChBF,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,KAAK;MACb3B,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,IAAI,CAACpB,cAAc,CAACK,IAAI,CAAC4C,iBAAiB,CAAC;IAE3C;IACA,IAAI,CAAC1C,cAAc,CAACC,QAAQ,EAAEiC,OAAO,CAAC,CAACS,SAAS,CAAC;MAC/C7C,IAAI,EAAG8C,KAAa,IAAI;QACtB;QACA,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,EAAE;QAEnB;QACAJ,YAAY,IAAIG,KAAK;QAErB;QACA,MAAME,cAAc,GAAGL,YAAY,GAAG,wCAAwC;QAE9E;QACA,MAAMM,cAAc,GAAgB;UAClCX,EAAE,EAAEI,YAAY;UAChBF,IAAI,EAAEQ,cAAc;UACpBP,MAAM,EAAE,KAAK;UACb3B,SAAS,EAAE,IAAIC,IAAI;SACpB;QAED;QACAmC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;UAC1B;UACA,IAAI,CAACzD,cAAc,CAACK,IAAI,CAACiD,cAAc,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDtC,QAAQ,EAAEA,CAAA,KAAK;QACbN,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA,IAAI,CAACqC,YAAY,CAACI,IAAI,EAAE,EAAE;UACxB,MAAMM,eAAe,GAAgB;YACnCf,EAAE,EAAEI,YAAY;YAChBF,IAAI,EAAE,8DAA8D;YACpEC,MAAM,EAAE,KAAK;YACb3B,SAAS,EAAE,IAAIC,IAAI;WACpB;UACDmC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAACzD,cAAc,CAACK,IAAI,CAACqD,eAAe,CAAC;UAC3C,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMC,YAAY,GAAgB;YAChChB,EAAE,EAAEI,YAAY;YAChBF,IAAI,EAAEG,YAAY;YAClBF,MAAM,EAAE,KAAK;YACb3B,SAAS,EAAE,IAAIC,IAAI;WACpB;UACDmC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAACzD,cAAc,CAACK,IAAI,CAACsD,YAAY,CAAC;UACxC,CAAC,CAAC;;QAGJ;QACA,IAAI,IAAI,CAAC5D,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,CAACc,KAAK,EAAE;UACxB,IAAI,CAACd,WAAW,GAAG,IAAI;;QAEzB,IAAI,CAACG,iBAAiB,GAAG,KAAK;QAE9B;QACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;UACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;MAEjC,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,MAAM8B,YAAY,GAAgB;UAChCjB,EAAE,EAAEI,YAAY;UAChBF,IAAI,EAAE,sEAAsE;UAC5EC,MAAM,EAAE,KAAK;UACb3B,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAACpB,cAAc,CAACK,IAAI,CAACuD,YAAY,CAAC;MACxC;KACD,CAAC;IAEF;IACA,OAAOtE,EAAE,CAACoD,WAAW,CAAC;EACxB;EAEA;;;;;EAKAmB,oBAAoBA,CAACrD,QAAgB,EAAEsD,cAAmB;IACxD;IACA,IAAI,IAAI,CAAC5D,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,OAAOrB,EAAE,CAAC,IAAI,CAAC;;IAGjB;IACA,MAAMyE,WAAW,GAAG,wDAAwD,GAC1E,aAAaD,cAAc,CAACE,QAAQ,IAAI,eAAe,IAAI,GAC3D,kBAAkBF,cAAc,CAACG,YAAY,IAAI,eAAe,IAAI,GACpE,iBAAiBH,cAAc,CAACI,WAAW,IAAI,eAAe,IAAI,GAClE,oBAAoBJ,cAAc,CAACK,cAAc,IAAI,eAAe,IAAI,GACxE,gBAAgBL,cAAc,CAACM,WAAW,GAAGN,cAAc,CAACM,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,IAAI,GACxG,iBAAiBP,cAAc,CAACQ,WAAW,IAAI,eAAe,EAAE;IAElE;IACA,OAAO,IAAI,CAAC9B,WAAW,CAAChC,QAAQ,EAAEuD,WAAW,CAAC;EAChD;EAEA;;;EAGA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAACvE,cAAc,CAACuC,YAAY,EAAE;EAC3C;EAEA;;;EAGA,IAAIjC,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACL,uBAAuB,CAACsC,YAAY,EAAE;EACpD;EAEA;;;EAGQK,UAAUA,CAAA;IAChB,OAAO4B,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;;EAIAC,sBAAsBA,CAACpE,QAAgB;IACrC,OAAO,IAAI,CAACZ,IAAI,CAACiF,GAAG,CAAM,GAAG,IAAI,CAAChF,OAAO,sCAAsCW,QAAQ,EAAE,CAAC,CACvFsE,IAAI,CACHtF,GAAG,CAACuF,QAAQ,IAAG;MACb,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;QACjC;QACA,OAAOD,QAAQ,CAACC,QAAQ,CAACxF,GAAG,CAAEyF,GAAQ,KAAM;UAC1CtC,EAAE,EAAEsC,GAAG,CAACtC,EAAE,GAAGsC,GAAG,CAACtC,EAAE,CAAC+B,QAAQ,EAAE,GAAG,IAAI,CAAC9B,UAAU,EAAE;UAClDC,IAAI,EAAEoC,GAAG,CAACpD,OAAO;UACjBiB,MAAM,EAAEmC,GAAG,CAACrD,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK;UAC7CT,SAAS,EAAE,IAAIC,IAAI,CAAC6D,GAAG,CAACC,UAAU;SACnC,CAAC,CAAC;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACF3F,UAAU,CAACuC,KAAK,IAAG;MACjBpB,OAAO,CAACoB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAOxC,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIA6F,wBAAwBA,CAAC3E,QAAgB;IACvC,OAAO,IAAI,CAACZ,IAAI,CAACwF,IAAI,CAAM,GAAG,IAAI,CAACvF,OAAO,+BAA+BW,QAAQ,EAAE,EAAE,EAAE,CAAC,CACrFsE,IAAI,CACHvF,UAAU,CAACuC,KAAK,IAAG;MACjBpB,OAAO,CAACoB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAOzC,UAAU,CAAC,MAAM,IAAI0C,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC5E,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAsD,uBAAuBA,CAAC7E,QAAgB;IACtC,OAAO,IAAI,CAACoE,sBAAsB,CAACpE,QAAQ,CAAC,CAACsE,IAAI,CAC/CvF,UAAU,CAACuC,KAAK,IAAG;MACjBpB,OAAO,CAACoB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOxC,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;;;uBAtaWI,UAAU,EAAA4F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAV/F,UAAU;MAAAgG,OAAA,EAAVhG,UAAU,CAAAiG,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA;;SAEPlG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}