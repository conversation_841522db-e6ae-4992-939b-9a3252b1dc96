{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Directive, Input, HostListener, Component, Pipe, Inject, Optional, EventEmitter, ViewChild, Output, ChangeDetectionStrategy, ContentChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { BehaviorSubject, Subject, merge } from 'rxjs';\nimport { shareReplay, tap, debounceTime, distinctUntilChanged, filter, takeUntil, map } from 'rxjs/operators';\nimport { trigger, transition, style, animate, sequence } from '@angular/animations';\nimport { DateTime, Info } from 'luxon';\nimport * as i4 from '@angular/forms';\nimport { FormControl, NG_VALUE_ACCESSOR, FormsModule, ReactiveFormsModule } from '@angular/forms';\nfunction NgxMaterialTimepickerContentComponent_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NgxMaterialTimepickerContentComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerContentComponent_div_0_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngxAppendToInput\", ctx_r0.inputElement);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction NgxMaterialTimepickerContentComponent_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NgxMaterialTimepickerContentComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgxMaterialTimepickerContentComponent_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction NgxMaterialTimepickerContentComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c0 = [\"*\"];\nconst _c1 = [\"editableTimeTmpl\"];\nconst _c2 = function (a0) {\n  return {\n    \"timepicker-dial__item_active\": a0\n  };\n};\nfunction NgxMaterialTimepickerDialControlComponent_input_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 2);\n    i0.ɵɵlistener(\"ngModelChange\", function NgxMaterialTimepickerDialControlComponent_input_0_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.time = $event);\n    })(\"input\", function NgxMaterialTimepickerDialControlComponent_input_0_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.updateTime());\n    })(\"focus\", function NgxMaterialTimepickerDialControlComponent_input_0_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.saveTimeAndChangeTimeUnit($event, ctx_r6.timeUnit));\n    });\n    i0.ɵɵpipe(1, \"timeLocalizer\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c2, ctx_r0.isActive))(\"ngModel\", i0.ɵɵpipeBind2(1, 4, ctx_r0.time, ctx_r0.timeUnit))(\"disabled\", ctx_r0.disabled)(\"timepickerAutofocus\", ctx_r0.isActive);\n  }\n}\nfunction NgxMaterialTimepickerDialControlComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 3, 4);\n    i0.ɵɵlistener(\"focus\", function NgxMaterialTimepickerDialControlComponent_ng_template_1_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.saveTimeAndChangeTimeUnit($event, ctx_r8.timeUnit));\n    })(\"keydown\", function NgxMaterialTimepickerDialControlComponent_ng_template_1_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formControl\", ctx_r2.timeControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c2, ctx_r2.isActive))(\"timepickerAutofocus\", ctx_r2.isActive);\n  }\n}\nfunction NgxMaterialTimepickerPeriodComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"@scaleInOut.done\", function NgxMaterialTimepickerPeriodComponent_div_5_Template_div_animation_scaleInOut_done_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.animationDone());\n    });\n    i0.ɵɵelementStart(1, \"p\");\n    i0.ɵɵtext(2, \"Current time would be invalid in this period.\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@scaleInOut\", undefined);\n  }\n}\nfunction NgxMaterialTimepickerDialComponent_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NgxMaterialTimepickerDialComponent_div_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 10);\n    i0.ɵɵtext(1, \" * use arrows (\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u21C5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \") to change the time\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"timepicker-dial__hint-container--hidden\": a0\n  };\n};\nfunction NgxMaterialTimepickerDialComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerDialComponent_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 8);\n    i0.ɵɵtemplate(2, NgxMaterialTimepickerDialComponent_div_8_ng_template_2_Template, 5, 0, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c3, !ctx_r0.isHintVisible));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.editableHintTmpl ? ctx_r0.editableHintTmpl : _r2);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"timepicker-dial__period--hidden\": a0\n  };\n};\nconst _c5 = [\"clockFace\"];\nconst _c6 = [\"clockHand\"];\nconst _c7 = function (a0) {\n  return {\n    \"transform\": a0\n  };\n};\nconst _c8 = function (a0, a1) {\n  return {\n    \"active\": a0,\n    \"disabled\": a1\n  };\n};\nfunction NgxMaterialTimepickerFaceComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵpipe(2, \"activeHour\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"timeLocalizer\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const time_r7 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(11, _c7, \"rotateZ(\" + time_r7.angle + \"deg) translateX(-50%)\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(13, _c7, \"rotateZ(-\" + time_r7.angle + \"deg)\"))(\"ngClass\", i0.ɵɵpureFunction2(15, _c8, i0.ɵɵpipeBind3(2, 4, time_r7.time, ctx_r5.selectedTime.time, ctx_r5.isClockFaceDisabled), time_r7.disabled));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 8, time_r7.time, ctx_r5.timeUnit.HOUR), \" \");\n  }\n}\nfunction NgxMaterialTimepickerFaceComponent_div_2_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"span\", 10);\n    i0.ɵɵpipe(2, \"activeHour\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"timeLocalizer\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const time_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r8.innerClockFaceSize, \"px\");\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(13, _c7, \"rotateZ(\" + time_r9.angle + \"deg) translateX(-50%)\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(15, _c7, \"rotateZ(-\" + time_r9.angle + \"deg)\"))(\"ngClass\", i0.ɵɵpureFunction2(17, _c8, i0.ɵɵpipeBind3(2, 6, time_r9.time, ctx_r8.selectedTime == null ? null : ctx_r8.selectedTime.time, ctx_r8.isClockFaceDisabled), time_r9.disabled));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 10, time_r9.time, ctx_r8.timeUnit.HOUR), \"\");\n  }\n}\nfunction NgxMaterialTimepickerFaceComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerFaceComponent_div_2_div_3_div_1_Template, 5, 20, \"div\", 12);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", \"calc(50% - \" + ctx_r6.innerClockFaceSize + \"px)\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 4, ctx_r6.faceTime, 12, 24))(\"ngForTrackBy\", ctx_r6.trackByTime);\n  }\n}\nfunction NgxMaterialTimepickerFaceComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerFaceComponent_div_2_div_1_Template, 5, 18, \"div\", 7);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtemplate(3, NgxMaterialTimepickerFaceComponent_div_2_div_3_Template, 3, 8, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 3, ctx_r1.faceTime, 0, 12))(\"ngForTrackBy\", ctx_r1.trackByTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.faceTime.length > 12);\n  }\n}\nfunction NgxMaterialTimepickerFaceComponent_ng_template_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵpipe(2, \"activeMinute\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"timeLocalizer\");\n    i0.ɵɵpipe(5, \"minutesFormatter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const time_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(15, _c7, \"rotateZ(\" + time_r11.angle + \"deg) translateX(-50%)\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(17, _c7, \"rotateZ(-\" + time_r11.angle + \"deg)\"))(\"ngClass\", i0.ɵɵpureFunction2(19, _c8, i0.ɵɵpipeBind4(2, 4, time_r11.time, ctx_r10.selectedTime == null ? null : ctx_r10.selectedTime.time, ctx_r10.minutesGap, ctx_r10.isClockFaceDisabled), time_r11.disabled));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 9, i0.ɵɵpipeBind2(5, 12, time_r11.time, ctx_r10.minutesGap), ctx_r10.timeUnit.MINUTE), \"\");\n  }\n}\nfunction NgxMaterialTimepickerFaceComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerFaceComponent_ng_template_5_div_1_Template, 6, 22, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.faceTime)(\"ngForTrackBy\", ctx_r4.trackByTime);\n  }\n}\nconst _c9 = function (a0) {\n  return {\n    \"clock-face__clock-hand_minute\": a0\n  };\n};\nfunction NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-24-hours-face\", 17);\n    i0.ɵɵlistener(\"hourChange\", function NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template_ngx_material_timepicker_24_hours_face_hourChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onHourChange($event));\n    })(\"hourSelected\", function NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template_ngx_material_timepicker_24_hours_face_hourSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onHourSelected($event));\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"selectedHour\", i0.ɵɵpipeBind1(1, 4, ctx_r8.selectedHour))(\"minTime\", ctx_r8.minTime)(\"maxTime\", ctx_r8.maxTime)(\"format\", ctx_r8.format);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-12-hours-face\", 18);\n    i0.ɵɵlistener(\"hourChange\", function NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template_ngx_material_timepicker_12_hours_face_hourChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onHourChange($event));\n    })(\"hourSelected\", function NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template_ngx_material_timepicker_12_hours_face_hourSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onHourSelected($event));\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"selectedHour\", i0.ɵɵpipeBind1(1, 4, ctx_r10.selectedHour))(\"period\", i0.ɵɵpipeBind1(2, 6, ctx_r10.selectedPeriod))(\"minTime\", ctx_r10.minTime)(\"maxTime\", ctx_r10.maxTime);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template, 2, 6, \"ngx-material-timepicker-24-hours-face\", 15);\n    i0.ɵɵtemplate(2, NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template, 3, 8, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r9 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.format === 24)(\"ngIfElse\", _r9);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_ngx_material_timepicker_minutes_face_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-minutes-face\", 19);\n    i0.ɵɵlistener(\"minuteChange\", function NgxMaterialTimepickerContainerComponent_ngx_material_timepicker_minutes_face_12_Template_ngx_material_timepicker_minutes_face_minuteChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onMinuteChange($event));\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"selectedMinute\", i0.ɵɵpipeBind1(1, 7, ctx_r1.selectedMinute))(\"selectedHour\", (tmp_1_0 = i0.ɵɵpipeBind1(2, 9, ctx_r1.selectedHour)) == null ? null : tmp_1_0.time)(\"minTime\", ctx_r1.minTime)(\"maxTime\", ctx_r1.maxTime)(\"format\", ctx_r1.format)(\"period\", i0.ɵɵpipeBind1(3, 11, ctx_r1.selectedPeriod))(\"minutesGap\", ctx_r1.minutesGap);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-button\");\n    i0.ɵɵtext(1, \"Cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NgxMaterialTimepickerContainerComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-button\");\n    i0.ɵɵtext(1, \"Ok\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c10 = function (a0) {\n  return {\n    \"timepicker-backdrop-overlay--transparent\": a0\n  };\n};\nfunction NgxMaterialTimepickerToggleComponent__svg_svg_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"path\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c11 = [[[\"\", \"ngxMaterialTimepickerToggleIcon\", \"\"]]];\nconst _c12 = [\"[ngxMaterialTimepickerToggleIcon]\"];\nconst _c13 = function (a0) {\n  return {\n    \"ngx-timepicker-control--active\": a0\n  };\n};\nconst _c14 = function (a0) {\n  return {\n    \"period-selector__button--active\": a0\n  };\n};\nfunction NgxTimepickerPeriodSelectorComponent_ul_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 6)(1, \"li\")(2, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function NgxTimepickerPeriodSelectorComponent_ul_7_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.select(ctx_r2.period.AM));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"li\")(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function NgxTimepickerPeriodSelectorComponent_ul_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.select(ctx_r4.period.PM));\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@scaleInOut\", undefined)(\"timepickerAutofocus\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c14, ctx_r0.localizedPeriod === ctx_r0.meridiems[0]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.meridiems[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c14, ctx_r0.localizedPeriod === ctx_r0.meridiems[1]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.meridiems[1]);\n  }\n}\nfunction NgxTimepickerPeriodSelectorComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function NgxTimepickerPeriodSelectorComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.backdropClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c15 = function (a0) {\n  return {\n    \"period-control__button--disabled\": a0\n  };\n};\nfunction NgxTimepickerFieldComponent_ngx_timepicker_period_selector_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-timepicker-period-selector\", 9);\n    i0.ɵɵlistener(\"periodSelected\", function NgxTimepickerFieldComponent_ngx_timepicker_period_selector_7_Template_ngx_timepicker_period_selector_periodSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.changePeriod($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedPeriod\", ctx_r0.period)(\"disabled\", ctx_r0.disabled || ctx_r0.isChangePeriodDisabled);\n  }\n}\nfunction NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c16 = function (a0) {\n  return {\n    \"ngx-timepicker__toggle--left\": a0\n  };\n};\nfunction NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngx-material-timepicker-toggle\", 10)(1, \"span\", 11);\n    i0.ɵɵtemplate(2, NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_ng_container_2_Template, 1, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(10);\n    const _r3 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c16, ctx_r1.buttonAlign === \"left\"))(\"for\", _r2)(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.toggleIcon || _r3);\n  }\n}\nfunction NgxTimepickerFieldComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 13);\n    i0.ɵɵelement(1, \"path\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c17 = function (a0) {\n  return {\n    \"ngx-timepicker--disabled\": a0\n  };\n};\nvar TimeUnit;\n(function (TimeUnit) {\n  TimeUnit[TimeUnit[\"HOUR\"] = 0] = \"HOUR\";\n  TimeUnit[TimeUnit[\"MINUTE\"] = 1] = \"MINUTE\";\n})(TimeUnit || (TimeUnit = {}));\nvar TimePeriod;\n(function (TimePeriod) {\n  TimePeriod[\"AM\"] = \"AM\";\n  TimePeriod[\"PM\"] = \"PM\";\n})(TimePeriod || (TimePeriod = {}));\nvar TimeFormat;\n(function (TimeFormat) {\n  TimeFormat[\"TWELVE\"] = \"hh:mm a\";\n  TimeFormat[\"TWELVE_SHORT\"] = \"h:m a\";\n  TimeFormat[\"TWENTY_FOUR\"] = \"HH:mm\";\n  TimeFormat[\"TWENTY_FOUR_SHORT\"] = \"H:m\";\n})(TimeFormat || (TimeFormat = {}));\nfunction isSameOrAfter(time, compareWith, unit = 'minutes') {\n  if (unit === 'hours') {\n    return time.hour >= compareWith.hour;\n  }\n  if (unit === 'minutes') {\n    return time.hasSame(compareWith, unit) || time.valueOf() > compareWith.valueOf();\n  }\n}\nfunction isSameOrBefore(time, compareWith, unit = 'minutes') {\n  if (unit === 'hours') {\n    return time.hour <= compareWith.hour;\n  }\n  if (unit === 'minutes') {\n    return time.hasSame(compareWith, unit) || time.valueOf() <= compareWith.valueOf();\n  }\n}\nfunction isBetween(time, before, after, unit = 'minutes') {\n  if (unit === 'hours') {\n    return isSameOrBefore(time, after, unit) && isSameOrAfter(time, before, unit);\n  }\n  if (unit === 'minutes') {\n    return isSameOrBefore(time, after) && isSameOrAfter(time, before);\n  }\n}\nfunction isDigit(e) {\n  // Allow: backspace, delete, tab, escape, enter\n  if ([46, 8, 9, 27, 13].some(n => n === e.keyCode) ||\n  // Allow: Ctrl/cmd+A\n  e.keyCode == 65 && (e.ctrlKey === true || e.metaKey === true) ||\n  // Allow: Ctrl/cmd+C\n  e.keyCode == 67 && (e.ctrlKey === true || e.metaKey === true) ||\n  // Allow: Ctrl/cmd+X\n  e.keyCode == 88 && (e.ctrlKey === true || e.metaKey === true) ||\n  // Allow: home, end, left, right, up, down\n  e.keyCode >= 35 && e.keyCode <= 40) {\n    return true;\n  }\n  return !((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105));\n}\n\n// @dynamic\nclass TimeAdapter {\n  static parseTime(time, opts) {\n    const {\n      numberingSystem,\n      locale\n    } = TimeAdapter.getLocaleOptionsByTime(time, opts);\n    const isPeriodExist = time.split(' ').length === 2;\n    const timeMask = isPeriodExist ? TimeFormat.TWELVE_SHORT : TimeFormat.TWENTY_FOUR_SHORT;\n    return DateTime.fromFormat(time, timeMask, {\n      numberingSystem,\n      locale\n    });\n  }\n  static formatTime(time, opts) {\n    if (!time) {\n      return 'Invalid Time';\n    }\n    const {\n      format\n    } = opts;\n    const parsedTime = TimeAdapter.parseTime(time, opts).setLocale(TimeAdapter.DEFAULT_LOCALE);\n    if (!parsedTime.isValid) {\n      return null;\n    }\n    if (format !== 24) {\n      return parsedTime.toLocaleString(Object.assign(Object.assign({}, DateTime.TIME_SIMPLE), {\n        hour12: format !== 24,\n        numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM\n      })).replace(/\\u200E/g, '').replace(/\\u202F/g, ' ');\n    }\n    return parsedTime.toISOTime({\n      includeOffset: false,\n      suppressMilliseconds: true,\n      suppressSeconds: true\n    }).replace(/\\u200E/g, '').replace(/\\u202F/g, ' ');\n  }\n  static toLocaleTimeString(time, opts = {}) {\n    const {\n      format = TimeAdapter.DEFAULT_FORMAT,\n      locale = TimeAdapter.DEFAULT_LOCALE\n    } = opts;\n    const hourCycle = format === 24 ? 'h23' : 'h12';\n    const timeFormat = Object.assign(Object.assign({}, DateTime.TIME_SIMPLE), {\n      hourCycle\n    });\n    const timeMask = format === 24 ? TimeFormat.TWENTY_FOUR_SHORT : TimeFormat.TWELVE_SHORT;\n    const localOpts = Object.assign({\n      locale: opts.locale,\n      numberingSystem: opts.numberingSystem\n    }, timeFormat);\n    return DateTime.fromFormat(time, timeMask).setLocale(locale).toLocaleString(localOpts).replace(/\\u202F/g, ' ');\n  }\n  static isTimeAvailable(time, min, max, granularity, minutesGap, format) {\n    if (!time) {\n      return;\n    }\n    const convertedTime = this.parseTime(time, {\n      format\n    });\n    const minutes = convertedTime.minute;\n    if (minutesGap && minutes === minutes && minutes % minutesGap !== 0) {\n      throw new Error(`Your minutes - ${minutes} doesn\\'t match your minutesGap - ${minutesGap}`);\n    }\n    const isAfter = min && !max && isSameOrAfter(convertedTime, min, granularity);\n    const isBefore = max && !min && isSameOrBefore(convertedTime, max, granularity);\n    const between = min && max && isBetween(convertedTime, min, max, granularity);\n    const isAvailable = !min && !max;\n    return isAfter || isBefore || between || isAvailable;\n  }\n  /***\n   *  Format hour according to time format (12 or 24)\n   */\n  static formatHour(currentHour, format, period) {\n    if (format === 24) {\n      return currentHour;\n    }\n    const hour = period === TimePeriod.AM ? currentHour : currentHour + 12;\n    if (period === TimePeriod.AM && hour === 12) {\n      return 0;\n    } else if (period === TimePeriod.PM && hour === 24) {\n      return 12;\n    }\n    return hour;\n  }\n  static fromDateTimeToString(time, format) {\n    const timeFormat = format === 24 ? TimeFormat.TWENTY_FOUR : TimeFormat.TWELVE;\n    return time.reconfigure({\n      numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM,\n      locale: TimeAdapter.DEFAULT_LOCALE\n    }).toFormat(timeFormat).replace(/\\u202F/g, ' ');\n  }\n  static getLocaleOptionsByTime(time, opts) {\n    const localeConfig = {\n      numberingSystem: opts.numberingSystem,\n      locale: opts.locale\n    };\n    const defaultConfig = {\n      numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM,\n      locale: TimeAdapter.DEFAULT_LOCALE\n    };\n    return isNaN(parseInt(time, 10)) ? localeConfig : defaultConfig;\n  }\n}\nTimeAdapter.DEFAULT_FORMAT = 12;\nTimeAdapter.DEFAULT_LOCALE = 'en-US';\nTimeAdapter.DEFAULT_NUMBERING_SYSTEM = 'latn';\nconst DEFAULT_HOUR = {\n  time: 12,\n  angle: 360\n};\nconst DEFAULT_MINUTE = {\n  time: 0,\n  angle: 360\n};\nclass NgxMaterialTimepickerService {\n  constructor() {\n    this.hourSubject = new BehaviorSubject(DEFAULT_HOUR);\n    this.minuteSubject = new BehaviorSubject(DEFAULT_MINUTE);\n    this.periodSubject = new BehaviorSubject(TimePeriod.AM);\n  }\n  set hour(hour) {\n    this.hourSubject.next(hour);\n  }\n  get selectedHour() {\n    return this.hourSubject.asObservable();\n  }\n  set minute(minute) {\n    this.minuteSubject.next(minute);\n  }\n  get selectedMinute() {\n    return this.minuteSubject.asObservable();\n  }\n  set period(period) {\n    const isPeriodValid = period === TimePeriod.AM || period === TimePeriod.PM;\n    if (isPeriodValid) {\n      this.periodSubject.next(period);\n    }\n  }\n  get selectedPeriod() {\n    return this.periodSubject.asObservable();\n  }\n  setDefaultTimeIfAvailable(time, min, max, format, minutesGap) {\n    /* Workaround to double error message*/\n    try {\n      if (TimeAdapter.isTimeAvailable(time, min, max, 'minutes', minutesGap)) {\n        this.setDefaultTime(time, format);\n      }\n    } catch (e) {\n      console.error(e);\n    }\n  }\n  getFullTime(format) {\n    const selectedHour = this.hourSubject.getValue().time;\n    const selectedMinute = this.minuteSubject.getValue().time;\n    const hour = selectedHour != null ? selectedHour : DEFAULT_HOUR.time;\n    const minute = selectedMinute != null ? selectedMinute : DEFAULT_MINUTE.time;\n    const period = format === 12 ? this.periodSubject.getValue() : '';\n    const time = `${hour}:${minute} ${period}`.trim();\n    return TimeAdapter.formatTime(time, {\n      format\n    });\n  }\n  setDefaultTime(time, format) {\n    const defaultTime = TimeAdapter.parseTime(time, {\n      format\n    }).toJSDate();\n    if (DateTime.fromJSDate(defaultTime).isValid) {\n      const period = time.substr(time.length - 2).toUpperCase();\n      const hour = defaultTime.getHours();\n      this.hour = Object.assign(Object.assign({}, DEFAULT_HOUR), {\n        time: formatHourByPeriod(hour, period)\n      });\n      this.minute = Object.assign(Object.assign({}, DEFAULT_MINUTE), {\n        time: defaultTime.getMinutes()\n      });\n      this.period = period;\n    } else {\n      this.resetTime();\n    }\n  }\n  resetTime() {\n    this.hour = Object.assign({}, DEFAULT_HOUR);\n    this.minute = Object.assign({}, DEFAULT_MINUTE);\n    this.period = TimePeriod.AM;\n  }\n}\nNgxMaterialTimepickerService.ɵfac = function NgxMaterialTimepickerService_Factory(t) {\n  return new (t || NgxMaterialTimepickerService)();\n};\nNgxMaterialTimepickerService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxMaterialTimepickerService,\n  factory: NgxMaterialTimepickerService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/***\n *  Format hour in 24hours format to meridian (AM or PM) format\n */\nfunction formatHourByPeriod(hour, period) {\n  switch (period) {\n    case TimePeriod.AM:\n      return hour === 0 ? 12 : hour;\n    case TimePeriod.PM:\n      return hour === 12 ? 12 : hour - 12;\n    default:\n      return hour;\n  }\n}\nconst TIME_LOCALE = new InjectionToken('TimeLocale', {\n  providedIn: 'root',\n  factory: () => TimeAdapter.DEFAULT_LOCALE\n});\nconst NUMBERING_SYSTEM = new InjectionToken('NumberingSystem', {\n  providedIn: 'root',\n  factory: () => TimeAdapter.DEFAULT_NUMBERING_SYSTEM\n});\nclass NgxMaterialTimepickerEventService {\n  constructor() {\n    this.backdropClickSubject = new Subject();\n    this.keydownEventSubject = new Subject();\n  }\n  get backdropClick() {\n    return this.backdropClickSubject.asObservable().pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n  }\n  get keydownEvent() {\n    return this.keydownEventSubject.asObservable().pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n  }\n  dispatchEvent(event) {\n    switch (event.type) {\n      case 'click':\n        this.backdropClickSubject.next(event);\n        break;\n      case 'keydown':\n        this.keydownEventSubject.next(event);\n        break;\n      default:\n        throw new Error('no such event type');\n    }\n  }\n}\nNgxMaterialTimepickerEventService.ɵfac = function NgxMaterialTimepickerEventService_Factory(t) {\n  return new (t || NgxMaterialTimepickerEventService)();\n};\nNgxMaterialTimepickerEventService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxMaterialTimepickerEventService,\n  factory: NgxMaterialTimepickerEventService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerEventService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass AppendToInputDirective {\n  constructor(elementRef, renderer) {\n    this.renderer = renderer;\n    this.element = elementRef.nativeElement;\n  }\n  get inputCords() {\n    return this.inputElement.getBoundingClientRect();\n  }\n  get direction() {\n    const height = this.element.offsetHeight;\n    const {\n      bottom,\n      top\n    } = this._inputCords;\n    const isElementFit = (window && window.innerHeight) - bottom < height;\n    const isTop = isElementFit && top > height;\n    const isCenter = isElementFit && top < height;\n    if (isTop) {\n      return 'top';\n    } else if (isCenter) {\n      return 'center';\n    }\n    return 'bottom';\n  }\n  ngAfterViewInit() {\n    this._inputCords = this.inputCords;\n    this._direction = this.direction;\n    this.append();\n  }\n  changePosition() {\n    const {\n      bottom,\n      top\n    } = this.inputCords;\n    const y = this.defineElementYByDirection(top, bottom);\n    this.setStyle('top', `${y}px`);\n  }\n  append() {\n    const {\n      left,\n      bottom,\n      top\n    } = this._inputCords;\n    const y = this.defineElementYByDirection(top, bottom);\n    this.setStyle('position', 'fixed');\n    this.setStyle('left', `${left}px`);\n    this.setStyle('top', `${y}px`);\n  }\n  setStyle(style, value) {\n    this.renderer.setStyle(this.element, style, value);\n  }\n  defineElementYByDirection(inputTop, inputBottom) {\n    if (this._direction === 'top') {\n      return inputTop - this.element.offsetHeight;\n    } else if (this._direction === 'center') {\n      return inputTop - this.element.offsetHeight / 2;\n    }\n    return inputBottom;\n  }\n}\nAppendToInputDirective.ɵfac = function AppendToInputDirective_Factory(t) {\n  return new (t || AppendToInputDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n};\nAppendToInputDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: AppendToInputDirective,\n  selectors: [[\"\", \"ngxAppendToInput\", \"\"]],\n  hostBindings: function AppendToInputDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"scroll\", function AppendToInputDirective_scroll_HostBindingHandler() {\n        return ctx.changePosition();\n      }, false, i0.ɵɵresolveWindow);\n    }\n  },\n  inputs: {\n    inputElement: [\"ngxAppendToInput\", \"inputElement\"]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AppendToInputDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxAppendToInput]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }];\n  }, {\n    inputElement: [{\n      type: Input,\n      args: ['ngxAppendToInput']\n    }],\n    changePosition: [{\n      type: HostListener,\n      args: ['window:scroll']\n    }]\n  });\n})();\nclass NgxMaterialTimepickerContentComponent {}\nNgxMaterialTimepickerContentComponent.ɵfac = function NgxMaterialTimepickerContentComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerContentComponent)();\n};\nNgxMaterialTimepickerContentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerContentComponent,\n  selectors: [[\"ngx-material-timepicker-content\"]],\n  inputs: {\n    appendToInput: \"appendToInput\",\n    inputElement: \"inputElement\"\n  },\n  ngContentSelectors: _c0,\n  decls: 5,\n  vars: 2,\n  consts: [[3, \"ngxAppendToInput\", 4, \"ngIf\", \"ngIfElse\"], [\"timepickerModal\", \"\"], [\"timepickerOutlet\", \"\"], [3, \"ngxAppendToInput\"], [4, \"ngTemplateOutlet\"]],\n  template: function NgxMaterialTimepickerContentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, NgxMaterialTimepickerContentComponent_div_0_Template, 2, 2, \"div\", 0);\n      i0.ɵɵtemplate(1, NgxMaterialTimepickerContentComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(3, NgxMaterialTimepickerContentComponent_ng_template_3_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.appendToInput)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i1.NgIf, AppendToInputDirective, i1.NgTemplateOutlet],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-content',\n      templateUrl: './ngx-material-timepicker-content.component.html'\n    }]\n  }], null, {\n    appendToInput: [{\n      type: Input\n    }],\n    inputElement: [{\n      type: Input\n    }]\n  });\n})();\n\n// @dynamic\nclass TimepickerTimeUtils {\n  static getHours(format) {\n    return Array(format).fill(1).map((v, i) => {\n      const angleStep = 30;\n      const time = v + i;\n      const angle = angleStep * time;\n      return {\n        time: time === 24 ? 0 : time,\n        angle\n      };\n    });\n  }\n  static disableHours(hours, config) {\n    if (config.min || config.max) {\n      return hours.map(value => {\n        const hour = config.format === 24 ? value.time : TimeAdapter.formatHour(value.time, config.format, config.period);\n        const currentTime = DateTime.fromObject({\n          hour\n        }).toFormat(TimeFormat.TWELVE);\n        return Object.assign(Object.assign({}, value), {\n          disabled: !TimeAdapter.isTimeAvailable(currentTime, config.min, config.max, 'hours')\n        });\n      });\n    }\n    return hours;\n  }\n  static getMinutes(gap = 1) {\n    const minutesCount = 60;\n    const angleStep = 360 / minutesCount;\n    const minutes = [];\n    for (let i = 0; i < minutesCount; i++) {\n      const angle = angleStep * i;\n      if (i % gap === 0) {\n        minutes.push({\n          time: i,\n          angle: angle !== 0 ? angle : 360\n        });\n      }\n    }\n    return minutes;\n  }\n  static disableMinutes(minutes, selectedHour, config) {\n    if (config.min || config.max) {\n      const hour = TimeAdapter.formatHour(selectedHour, config.format, config.period);\n      return minutes.map(value => {\n        const currentTime = DateTime.fromObject({\n          hour,\n          minute: value.time\n        }).toFormat(TimeFormat.TWELVE);\n        return Object.assign(Object.assign({}, value), {\n          disabled: !TimeAdapter.isTimeAvailable(currentTime, config.min, config.max, 'minutes')\n        });\n      });\n    }\n    return minutes;\n  }\n}\nclass TimeLocalizerPipe {\n  constructor(locale) {\n    this.locale = locale;\n  }\n  transform(time, timeUnit, isKeyboardEnabled = false) {\n    if (time == null || time === '') {\n      return '';\n    }\n    switch (timeUnit) {\n      case TimeUnit.HOUR:\n        {\n          const format = time === 0 || isKeyboardEnabled ? 'HH' : 'H';\n          return this.formatTime('hour', time, format);\n        }\n      case TimeUnit.MINUTE:\n        return this.formatTime('minute', time, 'mm');\n      default:\n        throw new Error(`There is no Time Unit with type ${timeUnit}`);\n    }\n  }\n  formatTime(timeMeasure, time, format) {\n    try {\n      return DateTime.fromObject({\n        [timeMeasure]: +time\n      }).setLocale(this.locale).toFormat(format);\n    } catch (_a) {\n      throw new Error(`Cannot format provided time - ${time} to locale - ${this.locale}`);\n    }\n  }\n}\nTimeLocalizerPipe.ɵfac = function TimeLocalizerPipe_Factory(t) {\n  return new (t || TimeLocalizerPipe)(i0.ɵɵdirectiveInject(TIME_LOCALE, 16));\n};\nTimeLocalizerPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"timeLocalizer\",\n  type: TimeLocalizerPipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimeLocalizerPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'timeLocalizer'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, null);\n})();\nclass TimeParserPipe {\n  constructor(locale, numberingSystem) {\n    this.locale = locale;\n    this.numberingSystem = numberingSystem;\n  }\n  transform(time, timeUnit = TimeUnit.HOUR) {\n    if (time == null || time === '') {\n      return '';\n    }\n    if (!isNaN(+time)) {\n      return time;\n    }\n    if (timeUnit === TimeUnit.MINUTE) {\n      return this.parseTime(time, 'm', 'minute');\n    }\n    return this.parseTime(time, 'H', 'hour');\n  }\n  parseTime(time, format, timeMeasure) {\n    const parsedTime = DateTime.fromFormat(String(time), format, {\n      numberingSystem: this.numberingSystem,\n      locale: this.locale\n    })[timeMeasure];\n    if (!isNaN(parsedTime)) {\n      return parsedTime;\n    }\n    throw new Error(`Cannot parse time - ${time}`);\n  }\n}\nTimeParserPipe.ɵfac = function TimeParserPipe_Factory(t) {\n  return new (t || TimeParserPipe)(i0.ɵɵdirectiveInject(TIME_LOCALE, 16), i0.ɵɵdirectiveInject(NUMBERING_SYSTEM, 16));\n};\nTimeParserPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"timeParser\",\n  type: TimeParserPipe,\n  pure: true\n});\nTimeParserPipe.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TimeParserPipe,\n  factory: TimeParserPipe.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimeParserPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'timeParser'\n    }]\n  }, {\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NUMBERING_SYSTEM]\n      }]\n    }];\n  }, null);\n})();\nclass AutofocusDirective {\n  constructor(element, document) {\n    this.element = element;\n    this.document = document;\n    this.activeElement = this.document.activeElement;\n  }\n  ngOnChanges() {\n    if (this.isFocusActive) {\n      // To avoid ExpressionChangedAfterItHasBeenCheckedError;\n      setTimeout(() => this.element.nativeElement.focus({\n        preventScroll: true\n      }));\n    }\n  }\n  ngOnDestroy() {\n    // To avoid ExpressionChangedAfterItHasBeenCheckedError;\n    setTimeout(() => this.activeElement.focus({\n      preventScroll: true\n    }));\n  }\n}\nAutofocusDirective.ɵfac = function AutofocusDirective_Factory(t) {\n  return new (t || AutofocusDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n};\nAutofocusDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: AutofocusDirective,\n  selectors: [[\"\", \"timepickerAutofocus\", \"\"]],\n  inputs: {\n    isFocusActive: [\"timepickerAutofocus\", \"isFocusActive\"]\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofocusDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[timepickerAutofocus]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    isFocusActive: [{\n      type: Input,\n      args: ['timepickerAutofocus']\n    }]\n  });\n})();\n\n/* tslint:disable:triple-equals */\nclass NgxMaterialTimepickerDialControlComponent {\n  constructor(timeParserPipe, timeLocalizerPipe) {\n    this.timeParserPipe = timeParserPipe;\n    this.timeLocalizerPipe = timeLocalizerPipe;\n    this.timeUnitChanged = new EventEmitter();\n    this.timeChanged = new EventEmitter();\n    this.focused = new EventEmitter();\n    this.unfocused = new EventEmitter();\n  }\n  get selectedTime() {\n    if (!!this.time) {\n      return this.timeList.find(t => t.time === +this.time);\n    }\n  }\n  ngOnInit() {\n    if (this.isEditable) {\n      this.timeControl = new FormControl({\n        value: this.formatTimeForUI(this.time),\n        disabled: this.disabled\n      });\n      this.timeControl.valueChanges.pipe(tap(value => {\n        if (value.length > 2) {\n          this.updateInputValue(value.slice(-1));\n        }\n      }), debounceTime(500), distinctUntilChanged(), filter(value => !isTimeDisabledToChange(this.time, value, this.timeList)), tap(value => this.time = this.timeParserPipe.transform(value, this.timeUnit).toString())).subscribe(() => this.updateTime());\n    }\n  }\n  saveTimeAndChangeTimeUnit(event, unit) {\n    event.preventDefault();\n    this.previousTime = this.time;\n    this.timeUnitChanged.next(unit);\n    this.focused.next();\n  }\n  updateTime() {\n    const time = this.selectedTime;\n    if (time) {\n      this.timeChanged.next(time);\n      this.previousTime = time.time;\n      if (this.isEditable) {\n        this.updateInputValue(this.formatTimeForUI(time.time));\n      }\n    }\n  }\n  onKeydown(e) {\n    if (!isDigit(e)) {\n      e.preventDefault();\n    } else {\n      this.changeTimeByArrow(e.keyCode);\n    }\n  }\n  changeTimeByArrow(keyCode) {\n    const ARROW_UP = 38;\n    const ARROW_DOWN = 40;\n    let time;\n    if (keyCode === ARROW_UP) {\n      time = String(+this.time + (this.minutesGap || 1));\n    } else if (keyCode === ARROW_DOWN) {\n      time = String(+this.time - (this.minutesGap || 1));\n    }\n    if (!isTimeUnavailable(time, this.timeList)) {\n      this.time = time;\n      this.updateTime();\n    }\n  }\n  formatTimeForUI(value) {\n    const parsedTime = this.timeParserPipe.transform(value, this.timeUnit).toString();\n    return this.timeLocalizerPipe.transform(parsedTime, this.timeUnit, true);\n  }\n  updateInputValue(value) {\n    this.editableTimeTmpl.nativeElement.value = value;\n  }\n}\nNgxMaterialTimepickerDialControlComponent.ɵfac = function NgxMaterialTimepickerDialControlComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerDialControlComponent)(i0.ɵɵdirectiveInject(TimeParserPipe), i0.ɵɵdirectiveInject(TimeLocalizerPipe));\n};\nNgxMaterialTimepickerDialControlComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerDialControlComponent,\n  selectors: [[\"ngx-material-timepicker-dial-control\"]],\n  viewQuery: function NgxMaterialTimepickerDialControlComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editableTimeTmpl = _t.first);\n    }\n  },\n  inputs: {\n    timeList: \"timeList\",\n    timeUnit: \"timeUnit\",\n    time: \"time\",\n    isActive: \"isActive\",\n    isEditable: \"isEditable\",\n    minutesGap: \"minutesGap\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    timeUnitChanged: \"timeUnitChanged\",\n    timeChanged: \"timeChanged\",\n    focused: \"focused\",\n    unfocused: \"unfocused\"\n  },\n  features: [i0.ɵɵProvidersFeature([TimeParserPipe, TimeLocalizerPipe])],\n  decls: 3,\n  vars: 2,\n  consts: [[\"class\", \"timepicker-dial__control timepicker-dial__item\", \"readonly\", \"\", 3, \"ngClass\", \"ngModel\", \"disabled\", \"timepickerAutofocus\", \"ngModelChange\", \"input\", \"focus\", 4, \"ngIf\", \"ngIfElse\"], [\"editableTemplate\", \"\"], [\"readonly\", \"\", 1, \"timepicker-dial__control\", \"timepicker-dial__item\", 3, \"ngClass\", \"ngModel\", \"disabled\", \"timepickerAutofocus\", \"ngModelChange\", \"input\", \"focus\"], [1, \"timepicker-dial__control\", \"timepicker-dial__item\", \"timepicker-dial__control_editable\", 3, \"formControl\", \"ngClass\", \"timepickerAutofocus\", \"focus\", \"keydown\"], [\"editableTimeTmpl\", \"\"]],\n  template: function NgxMaterialTimepickerDialControlComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, NgxMaterialTimepickerDialControlComponent_input_0_Template, 2, 9, \"input\", 0);\n      i0.ɵɵtemplate(1, NgxMaterialTimepickerDialControlComponent_ng_template_1_Template, 2, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isEditable)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i1.NgIf, i4.DefaultValueAccessor, i1.NgClass, i4.NgControlStatus, i4.NgModel, AutofocusDirective, i4.FormControlDirective, TimeLocalizerPipe],\n  styles: [\".timepicker-dial__item[_ngcontent-%COMP%]{cursor:pointer;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__item[_ngcontent-%COMP%]{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__item_active[_ngcontent-%COMP%]{color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__item_active[_ngcontent-%COMP%]{color:var(--dial-active-color)}}.timepicker-dial__control[_ngcontent-%COMP%]{border:none;background-color:transparent;font-size:50px;width:60px;padding:0;border-radius:3px;text-align:right}.timepicker-dial__control_editable[_ngcontent-%COMP%]:focus{color:#00bfff;background-color:#fff;outline:deepskyblue}@supports (color: var(--dial-editable-active-color)){.timepicker-dial__control_editable[_ngcontent-%COMP%]:focus{color:var(--dial-editable-active-color)}}@supports (background-color: var(--dial-editable-background-color)){.timepicker-dial__control_editable[_ngcontent-%COMP%]:focus{background-color:var(--dial-editable-background-color)}}@supports (outline: var(--dial-editable-active-color)){.timepicker-dial__control_editable[_ngcontent-%COMP%]:focus{outline:var(--dial-editable-active-color)}}.timepicker-dial__control[_ngcontent-%COMP%]:disabled{cursor:default}.timepicker-dial__control[_ngcontent-%COMP%]:focus-visible{outline:none}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerDialControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-dial-control',\n      templateUrl: 'ngx-material-timepicker-dial-control.component.html',\n      styleUrls: ['ngx-material-timepicker-dial-control.component.scss'],\n      providers: [TimeParserPipe, TimeLocalizerPipe]\n    }]\n  }], function () {\n    return [{\n      type: TimeParserPipe\n    }, {\n      type: TimeLocalizerPipe\n    }];\n  }, {\n    timeList: [{\n      type: Input\n    }],\n    timeUnit: [{\n      type: Input\n    }],\n    time: [{\n      type: Input\n    }],\n    isActive: [{\n      type: Input\n    }],\n    isEditable: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    editableTimeTmpl: [{\n      type: ViewChild,\n      args: ['editableTimeTmpl']\n    }],\n    timeUnitChanged: [{\n      type: Output\n    }],\n    timeChanged: [{\n      type: Output\n    }],\n    focused: [{\n      type: Output\n    }],\n    unfocused: [{\n      type: Output\n    }]\n  });\n})();\nfunction isTimeDisabledToChange(currentTime, nextTime, timeList) {\n  const isNumber = /\\d/.test(nextTime);\n  if (isNumber) {\n    return isTimeUnavailable(nextTime, timeList);\n  }\n}\nfunction isTimeUnavailable(time, timeList) {\n  const selectedTime = timeList.find(value => value.time === +time);\n  return !selectedTime || selectedTime && selectedTime.disabled;\n}\nclass NgxMaterialTimepickerPeriodComponent {\n  constructor() {\n    this.timePeriod = TimePeriod;\n    this.isPeriodAvailable = true;\n    this.periodChanged = new EventEmitter();\n  }\n  changePeriod(period) {\n    this.isPeriodAvailable = this.isSwitchPeriodAvailable(period);\n    if (this.isPeriodAvailable) {\n      this.periodChanged.next(period);\n    }\n  }\n  animationDone() {\n    this.isPeriodAvailable = true;\n  }\n  isSwitchPeriodAvailable(period) {\n    const time = this.getDisabledTimeByPeriod(period);\n    return !time.every(t => t.disabled);\n  }\n  getDisabledTimeByPeriod(period) {\n    switch (this.activeTimeUnit) {\n      case TimeUnit.HOUR:\n        return TimepickerTimeUtils.disableHours(this.hours, {\n          min: this.minTime,\n          max: this.maxTime,\n          format: this.format,\n          period\n        });\n      case TimeUnit.MINUTE:\n        return TimepickerTimeUtils.disableMinutes(this.minutes, +this.selectedHour, {\n          min: this.minTime,\n          max: this.maxTime,\n          format: this.format,\n          period\n        });\n      default:\n        throw new Error('no such TimeUnit');\n    }\n  }\n}\nNgxMaterialTimepickerPeriodComponent.ɵfac = function NgxMaterialTimepickerPeriodComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerPeriodComponent)();\n};\nNgxMaterialTimepickerPeriodComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerPeriodComponent,\n  selectors: [[\"ngx-material-timepicker-period\"]],\n  inputs: {\n    selectedPeriod: \"selectedPeriod\",\n    format: \"format\",\n    activeTimeUnit: \"activeTimeUnit\",\n    hours: \"hours\",\n    minutes: \"minutes\",\n    minTime: \"minTime\",\n    maxTime: \"maxTime\",\n    selectedHour: \"selectedHour\",\n    meridiems: \"meridiems\"\n  },\n  outputs: {\n    periodChanged: \"periodChanged\"\n  },\n  decls: 6,\n  vars: 9,\n  consts: [[1, \"timepicker-period\"], [\"type\", \"button\", 1, \"timepicker-dial__item\", \"timepicker-period__btn\", 3, \"ngClass\", \"click\"], [\"class\", \"timepicker-period__warning\", 4, \"ngIf\"], [1, \"timepicker-period__warning\"]],\n  template: function NgxMaterialTimepickerPeriodComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n      i0.ɵɵlistener(\"click\", function NgxMaterialTimepickerPeriodComponent_Template_button_click_1_listener() {\n        return ctx.changePeriod(ctx.timePeriod.AM);\n      });\n      i0.ɵɵtext(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"button\", 1);\n      i0.ɵɵlistener(\"click\", function NgxMaterialTimepickerPeriodComponent_Template_button_click_3_listener() {\n        return ctx.changePeriod(ctx.timePeriod.PM);\n      });\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, NgxMaterialTimepickerPeriodComponent_div_5_Template, 3, 1, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, ctx.selectedPeriod === ctx.timePeriod.AM));\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate(ctx.meridiems[0]);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c2, ctx.selectedPeriod === ctx.timePeriod.PM));\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate(ctx.meridiems[1]);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPeriodAvailable);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf],\n  styles: [\".timepicker-dial__item[_ngcontent-%COMP%]{cursor:pointer;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__item[_ngcontent-%COMP%]{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__item_active[_ngcontent-%COMP%]{color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__item_active[_ngcontent-%COMP%]{color:var(--dial-active-color)}}.timepicker-period[_ngcontent-%COMP%]{display:flex;flex-direction:column;position:relative}.timepicker-period__btn[_ngcontent-%COMP%]{padding:1px 3px;border:0;background-color:transparent;font-size:18px;font-weight:500;-webkit-user-select:none;-moz-user-select:none;user-select:none;outline:none;border-radius:3px;transition:background-color .5s;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-period__btn[_ngcontent-%COMP%]{font-family:var(--primary-font-family)}}.timepicker-period__btn[_ngcontent-%COMP%]:focus{background-color:#00000012}.timepicker-period__warning[_ngcontent-%COMP%]{padding:5px 10px;border-radius:3px;background-color:#0000008c;color:#fff;position:absolute;width:200px;left:-20px;top:40px}.timepicker-period__warning[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{margin:0;font-size:12px;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-period__warning[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-family:var(--primary-font-family)}}\"],\n  data: {\n    animation: [trigger('scaleInOut', [transition(':enter', [style({\n      transform: 'scale(0)'\n    }), animate('.2s', style({\n      transform: 'scale(1)'\n    })), sequence([animate('3s', style({\n      opacity: 1\n    })), animate('.3s', style({\n      opacity: 0\n    }))])])])]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerPeriodComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-period',\n      templateUrl: 'ngx-material-timepicker-period.component.html',\n      styleUrls: ['ngx-material-timepicker-period.component.scss'],\n      animations: [trigger('scaleInOut', [transition(':enter', [style({\n        transform: 'scale(0)'\n      }), animate('.2s', style({\n        transform: 'scale(1)'\n      })), sequence([animate('3s', style({\n        opacity: 1\n      })), animate('.3s', style({\n        opacity: 0\n      }))])])])]\n    }]\n  }], null, {\n    selectedPeriod: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    activeTimeUnit: [{\n      type: Input\n    }],\n    hours: [{\n      type: Input\n    }],\n    minutes: [{\n      type: Input\n    }],\n    minTime: [{\n      type: Input\n    }],\n    maxTime: [{\n      type: Input\n    }],\n    selectedHour: [{\n      type: Input\n    }],\n    meridiems: [{\n      type: Input\n    }],\n    periodChanged: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxMaterialTimepickerDialComponent {\n  constructor(locale) {\n    this.locale = locale;\n    this.timeUnit = TimeUnit;\n    this.meridiems = Info.meridiems({\n      locale: this.locale\n    });\n    this.periodChanged = new EventEmitter();\n    this.timeUnitChanged = new EventEmitter();\n    this.hourChanged = new EventEmitter();\n    this.minuteChanged = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    if (changes['period'] && changes['period'].currentValue || changes['format'] && changes['format'].currentValue) {\n      const hours = TimepickerTimeUtils.getHours(this.format);\n      this.hours = TimepickerTimeUtils.disableHours(hours, {\n        min: this.minTime,\n        max: this.maxTime,\n        format: this.format,\n        period: this.period\n      });\n    }\n    if (changes['period'] && changes['period'].currentValue || changes['hour'] && changes['hour'].currentValue) {\n      const minutes = TimepickerTimeUtils.getMinutes(this.minutesGap);\n      this.minutes = TimepickerTimeUtils.disableMinutes(minutes, +this.hour, {\n        min: this.minTime,\n        max: this.maxTime,\n        format: this.format,\n        period: this.period\n      });\n    }\n  }\n  changeTimeUnit(unit) {\n    this.timeUnitChanged.next(unit);\n  }\n  changePeriod(period) {\n    this.periodChanged.next(period);\n  }\n  changeHour(hour) {\n    this.hourChanged.next(hour);\n    if (this.isEditable) {\n      this.changeTimeUnit(TimeUnit.MINUTE);\n    }\n  }\n  changeMinute(minute) {\n    this.minuteChanged.next(minute);\n  }\n  showHint() {\n    this.isHintVisible = true;\n  }\n  hideHint() {\n    this.isHintVisible = false;\n  }\n}\nNgxMaterialTimepickerDialComponent.ɵfac = function NgxMaterialTimepickerDialComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerDialComponent)(i0.ɵɵdirectiveInject(TIME_LOCALE));\n};\nNgxMaterialTimepickerDialComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerDialComponent,\n  selectors: [[\"ngx-material-timepicker-dial\"]],\n  inputs: {\n    editableHintTmpl: \"editableHintTmpl\",\n    hour: \"hour\",\n    minute: \"minute\",\n    format: \"format\",\n    period: \"period\",\n    activeTimeUnit: \"activeTimeUnit\",\n    minTime: \"minTime\",\n    maxTime: \"maxTime\",\n    isEditable: \"isEditable\",\n    minutesGap: \"minutesGap\",\n    hoursOnly: \"hoursOnly\"\n  },\n  outputs: {\n    periodChanged: \"periodChanged\",\n    timeUnitChanged: \"timeUnitChanged\",\n    hourChanged: \"hourChanged\",\n    minuteChanged: \"minuteChanged\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 9,\n  vars: 25,\n  consts: [[1, \"timepicker-dial\"], [1, \"timepicker-dial__container\"], [1, \"timepicker-dial__time\"], [3, \"timeList\", \"time\", \"timeUnit\", \"isActive\", \"isEditable\", \"timeUnitChanged\", \"timeChanged\", \"focused\", \"unfocused\"], [3, \"timeList\", \"time\", \"timeUnit\", \"isActive\", \"isEditable\", \"minutesGap\", \"disabled\", \"timeUnitChanged\", \"timeChanged\", \"focused\", \"unfocused\"], [1, \"timepicker-dial__period\", 3, \"ngClass\", \"selectedPeriod\", \"activeTimeUnit\", \"maxTime\", \"minTime\", \"format\", \"hours\", \"minutes\", \"selectedHour\", \"meridiems\", \"periodChanged\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [4, \"ngTemplateOutlet\"], [\"editableHintDefault\", \"\"], [1, \"timepicker-dial__hint\"]],\n  template: function NgxMaterialTimepickerDialComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"ngx-material-timepicker-dial-control\", 3);\n      i0.ɵɵlistener(\"timeUnitChanged\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeUnitChanged_3_listener($event) {\n        return ctx.changeTimeUnit($event);\n      })(\"timeChanged\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeChanged_3_listener($event) {\n        return ctx.changeHour($event);\n      })(\"focused\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_focused_3_listener() {\n        return ctx.showHint();\n      })(\"unfocused\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_unfocused_3_listener() {\n        return ctx.hideHint();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \":\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"ngx-material-timepicker-dial-control\", 4);\n      i0.ɵɵlistener(\"timeUnitChanged\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeUnitChanged_6_listener($event) {\n        return ctx.changeTimeUnit($event);\n      })(\"timeChanged\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeChanged_6_listener($event) {\n        return ctx.changeMinute($event);\n      })(\"focused\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_focused_6_listener() {\n        return ctx.showHint();\n      })(\"unfocused\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_unfocused_6_listener() {\n        return ctx.hideHint();\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(7, \"ngx-material-timepicker-period\", 5);\n      i0.ɵɵlistener(\"periodChanged\", function NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_period_periodChanged_7_listener($event) {\n        return ctx.changePeriod($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(8, NgxMaterialTimepickerDialComponent_div_8_Template, 4, 4, \"div\", 6);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"timeList\", ctx.hours)(\"time\", ctx.hour)(\"timeUnit\", ctx.timeUnit.HOUR)(\"isActive\", ctx.activeTimeUnit === ctx.timeUnit.HOUR)(\"isEditable\", ctx.isEditable);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"timeList\", ctx.minutes)(\"time\", ctx.minute)(\"timeUnit\", ctx.timeUnit.MINUTE)(\"isActive\", ctx.activeTimeUnit === ctx.timeUnit.MINUTE)(\"isEditable\", ctx.isEditable)(\"minutesGap\", ctx.minutesGap)(\"disabled\", ctx.hoursOnly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c4, ctx.format === 24))(\"selectedPeriod\", ctx.period)(\"activeTimeUnit\", ctx.activeTimeUnit)(\"maxTime\", ctx.maxTime)(\"minTime\", ctx.minTime)(\"format\", ctx.format)(\"hours\", ctx.hours)(\"minutes\", ctx.minutes)(\"selectedHour\", ctx.hour)(\"meridiems\", ctx.meridiems);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isEditable || ctx.editableHintTmpl);\n    }\n  },\n  dependencies: [NgxMaterialTimepickerDialControlComponent, NgxMaterialTimepickerPeriodComponent, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet],\n  styles: [\".timepicker-dial[_ngcontent-%COMP%]{text-align:right}.timepicker-dial__container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;-webkit-tap-highlight-color:rgba(0,0,0,0)}.timepicker-dial__time[_ngcontent-%COMP%]{display:flex;align-items:baseline;line-height:normal;font-size:50px;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__time[_ngcontent-%COMP%]{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__period[_ngcontent-%COMP%]{display:block;margin-left:10px}.timepicker-dial__period--hidden[_ngcontent-%COMP%]{visibility:hidden}.timepicker-dial__hint-container--hidden[_ngcontent-%COMP%]{visibility:hidden}.timepicker-dial__hint[_ngcontent-%COMP%]{display:inline-block;font-size:10px;color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__hint[_ngcontent-%COMP%]{color:var(--dial-active-color)}}.timepicker-dial__hint[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px}@media (max-device-width: 1023px) and (orientation: landscape){.timepicker-dial__container[_ngcontent-%COMP%]{flex-direction:column}.timepicker-dial__period[_ngcontent-%COMP%]{margin-left:0}}\"],\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerDialComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-dial',\n      templateUrl: 'ngx-material-timepicker-dial.component.html',\n      styleUrls: ['ngx-material-timepicker-dial.component.scss'],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, {\n    editableHintTmpl: [{\n      type: Input\n    }],\n    hour: [{\n      type: Input\n    }],\n    minute: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    period: [{\n      type: Input\n    }],\n    activeTimeUnit: [{\n      type: Input\n    }],\n    minTime: [{\n      type: Input\n    }],\n    maxTime: [{\n      type: Input\n    }],\n    isEditable: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    hoursOnly: [{\n      type: Input\n    }],\n    periodChanged: [{\n      type: Output\n    }],\n    timeUnitChanged: [{\n      type: Output\n    }],\n    hourChanged: [{\n      type: Output\n    }],\n    minuteChanged: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxMaterialTimepickerHoursFace {\n  constructor(format) {\n    this.hourChange = new EventEmitter();\n    this.hourSelected = new EventEmitter();\n    this.hoursList = [];\n    this.hoursList = TimepickerTimeUtils.getHours(format);\n  }\n  onTimeSelected(time) {\n    this.hourSelected.next(time);\n  }\n}\nNgxMaterialTimepickerHoursFace.ɵfac = function NgxMaterialTimepickerHoursFace_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\nNgxMaterialTimepickerHoursFace.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgxMaterialTimepickerHoursFace,\n  inputs: {\n    selectedHour: \"selectedHour\",\n    minTime: \"minTime\",\n    maxTime: \"maxTime\",\n    format: \"format\"\n  },\n  outputs: {\n    hourChange: \"hourChange\",\n    hourSelected: \"hourSelected\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerHoursFace, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: undefined\n    }];\n  }, {\n    selectedHour: [{\n      type: Input\n    }],\n    minTime: [{\n      type: Input\n    }],\n    maxTime: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    hourChange: [{\n      type: Output\n    }],\n    hourSelected: [{\n      type: Output\n    }]\n  });\n})();\nclass ActiveHourPipe {\n  transform(hour, currentHour, isClockFaceDisabled) {\n    if (hour == null || isClockFaceDisabled) {\n      return false;\n    }\n    return hour === currentHour;\n  }\n}\nActiveHourPipe.ɵfac = function ActiveHourPipe_Factory(t) {\n  return new (t || ActiveHourPipe)();\n};\nActiveHourPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"activeHour\",\n  type: ActiveHourPipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ActiveHourPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'activeHour'\n    }]\n  }], null, null);\n})();\nclass ActiveMinutePipe {\n  transform(minute, currentMinute, gap, isClockFaceDisabled) {\n    if (minute == null || isClockFaceDisabled) {\n      return false;\n    }\n    const defaultGap = 5;\n    return currentMinute === minute && minute % (gap || defaultGap) === 0;\n  }\n}\nActiveMinutePipe.ɵfac = function ActiveMinutePipe_Factory(t) {\n  return new (t || ActiveMinutePipe)();\n};\nActiveMinutePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"activeMinute\",\n  type: ActiveMinutePipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ActiveMinutePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'activeMinute'\n    }]\n  }], null, null);\n})();\nclass MinutesFormatterPipe {\n  transform(minute, gap = 5) {\n    if (!minute) {\n      return minute;\n    }\n    return minute % gap === 0 ? minute : '';\n  }\n}\nMinutesFormatterPipe.ɵfac = function MinutesFormatterPipe_Factory(t) {\n  return new (t || MinutesFormatterPipe)();\n};\nMinutesFormatterPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"minutesFormatter\",\n  type: MinutesFormatterPipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MinutesFormatterPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'minutesFormatter'\n    }]\n  }], null, null);\n})();\nconst CLOCK_HAND_STYLES = {\n  small: {\n    height: '75px',\n    top: 'calc(50% - 75px)'\n  },\n  large: {\n    height: '103px',\n    top: 'calc(50% - 103px)'\n  }\n};\nclass NgxMaterialTimepickerFaceComponent {\n  constructor() {\n    this.timeUnit = TimeUnit;\n    this.innerClockFaceSize = 85;\n    this.timeChange = new EventEmitter();\n    this.timeSelected = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    this.setClockHandPosition();\n    this.addTouchEvents();\n  }\n  ngOnChanges(changes) {\n    const faceTimeChanges = changes['faceTime'];\n    const selectedTimeChanges = changes['selectedTime'];\n    if (faceTimeChanges && faceTimeChanges.currentValue && selectedTimeChanges && selectedTimeChanges.currentValue) {\n      /* Set time according to passed an input value */\n      this.selectedTime = this.faceTime.find(time => time.time === this.selectedTime.time);\n    }\n    if (selectedTimeChanges && selectedTimeChanges.currentValue) {\n      this.setClockHandPosition();\n    }\n    if (faceTimeChanges && faceTimeChanges.currentValue) {\n      // To avoid an error ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => this.selectAvailableTime());\n    }\n  }\n  trackByTime(_, time) {\n    return time.time;\n  }\n  onMousedown(e) {\n    e.preventDefault();\n    this.isStarted = true;\n  }\n  selectTime(e) {\n    if (!this.isStarted && e instanceof MouseEvent && e.type !== 'click') {\n      return;\n    }\n    const clockFaceCords = this.clockFace.nativeElement.getBoundingClientRect();\n    /* Get x0 and y0 of the circle */\n    const centerX = clockFaceCords.left + clockFaceCords.width / 2;\n    const centerY = clockFaceCords.top + clockFaceCords.height / 2;\n    /* Counting the arctangent and convert it to from radian to deg */\n    const arctangent = Math.atan(Math.abs(e.clientX - centerX) / Math.abs(e.clientY - centerY)) * 180 / Math.PI;\n    /* Get angle according to quadrant */\n    const circleAngle = countAngleByCords(centerX, centerY, e.clientX, e.clientY, arctangent);\n    /* Check if selected time from the inner clock face (24 hours format only) */\n    const isInnerClockChosen = this.format && this.isInnerClockFace(centerX, centerY, e.clientX, e.clientY);\n    /* Round angle according to angle step */\n    const angleStep = this.unit === TimeUnit.MINUTE ? 6 * (this.minutesGap || 1) : 30;\n    const roundedAngle = roundAngle(circleAngle, angleStep);\n    const angle = (roundedAngle || 360) + (isInnerClockChosen ? 360 : 0);\n    const selectedTime = this.faceTime.find(val => val.angle === angle);\n    if (selectedTime && !selectedTime.disabled) {\n      this.timeChange.next(selectedTime);\n      /* To let know whether user ended interaction with clock face */\n      if (!this.isStarted) {\n        this.timeSelected.next(selectedTime.time);\n      }\n    }\n  }\n  onMouseup(e) {\n    e.preventDefault();\n    this.isStarted = false;\n  }\n  ngOnDestroy() {\n    this.removeTouchEvents();\n  }\n  addTouchEvents() {\n    this.touchStartHandler = this.onMousedown.bind(this);\n    this.touchEndHandler = this.onMouseup.bind(this);\n    this.clockFace.nativeElement.addEventListener('touchstart', this.touchStartHandler);\n    this.clockFace.nativeElement.addEventListener('touchend', this.touchEndHandler);\n  }\n  removeTouchEvents() {\n    this.clockFace.nativeElement.removeEventListener('touchstart', this.touchStartHandler);\n    this.clockFace.nativeElement.removeEventListener('touchend', this.touchEndHandler);\n  }\n  setClockHandPosition() {\n    if (this.format === 24) {\n      if (this.selectedTime.time > 12 || this.selectedTime.time === 0) {\n        this.decreaseClockHand();\n      } else {\n        this.increaseClockHand();\n      }\n    }\n    this.clockHand.nativeElement.style.transform = `rotate(${this.selectedTime.angle}deg)`;\n  }\n  selectAvailableTime() {\n    const currentTime = this.faceTime.find(time => this.selectedTime.time === time.time);\n    this.isClockFaceDisabled = this.faceTime.every(time => time.disabled);\n    if (currentTime && currentTime.disabled && !this.isClockFaceDisabled) {\n      const availableTime = this.faceTime.find(time => !time.disabled);\n      this.timeChange.next(availableTime);\n    }\n  }\n  isInnerClockFace(x0, y0, x, y) {\n    /* Detect whether time from the inner clock face or not (24 format only) */\n    return Math.sqrt(Math.pow(x - x0, 2) + Math.pow(y - y0, 2)) < this.innerClockFaceSize;\n  }\n  decreaseClockHand() {\n    this.clockHand.nativeElement.style.height = CLOCK_HAND_STYLES.small.height;\n    this.clockHand.nativeElement.style.top = CLOCK_HAND_STYLES.small.top;\n  }\n  increaseClockHand() {\n    this.clockHand.nativeElement.style.height = CLOCK_HAND_STYLES.large.height;\n    this.clockHand.nativeElement.style.top = CLOCK_HAND_STYLES.large.top;\n  }\n}\nNgxMaterialTimepickerFaceComponent.ɵfac = function NgxMaterialTimepickerFaceComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerFaceComponent)();\n};\nNgxMaterialTimepickerFaceComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerFaceComponent,\n  selectors: [[\"ngx-material-timepicker-face\"]],\n  viewQuery: function NgxMaterialTimepickerFaceComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c5, 7);\n      i0.ɵɵviewQuery(_c6, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clockFace = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clockHand = _t.first);\n    }\n  },\n  hostBindings: function NgxMaterialTimepickerFaceComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mousedown\", function NgxMaterialTimepickerFaceComponent_mousedown_HostBindingHandler($event) {\n        return ctx.onMousedown($event);\n      })(\"click\", function NgxMaterialTimepickerFaceComponent_click_HostBindingHandler($event) {\n        return ctx.selectTime($event);\n      })(\"touchmove\", function NgxMaterialTimepickerFaceComponent_touchmove_HostBindingHandler($event) {\n        return ctx.selectTime($event.changedTouches[0]);\n      })(\"touchend\", function NgxMaterialTimepickerFaceComponent_touchend_HostBindingHandler($event) {\n        return ctx.selectTime($event.changedTouches[0]);\n      })(\"mousemove\", function NgxMaterialTimepickerFaceComponent_mousemove_HostBindingHandler($event) {\n        return ctx.selectTime($event);\n      })(\"mouseup\", function NgxMaterialTimepickerFaceComponent_mouseup_HostBindingHandler($event) {\n        return ctx.onMouseup($event);\n      });\n    }\n  },\n  inputs: {\n    faceTime: \"faceTime\",\n    selectedTime: \"selectedTime\",\n    unit: \"unit\",\n    format: \"format\",\n    minutesGap: \"minutesGap\"\n  },\n  outputs: {\n    timeChange: \"timeChange\",\n    timeSelected: \"timeSelected\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 7,\n  vars: 6,\n  consts: [[1, \"clock-face\"], [\"clockFace\", \"\"], [\"class\", \"clock-face__container\", 4, \"ngIf\", \"ngIfElse\"], [1, \"clock-face__clock-hand\", 3, \"ngClass\", \"hidden\"], [\"clockHand\", \"\"], [\"minutesFace\", \"\"], [1, \"clock-face__container\"], [\"class\", \"clock-face__number clock-face__number--outer\", 3, \"ngStyle\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"clock-face__inner\", 3, \"top\", 4, \"ngIf\"], [1, \"clock-face__number\", \"clock-face__number--outer\", 3, \"ngStyle\"], [3, \"ngStyle\", \"ngClass\"], [1, \"clock-face__inner\"], [\"class\", \"clock-face__number clock-face__number--inner\", 3, \"ngStyle\", \"height\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"clock-face__number\", \"clock-face__number--inner\", 3, \"ngStyle\"]],\n  template: function NgxMaterialTimepickerFaceComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵtemplate(2, NgxMaterialTimepickerFaceComponent_div_2_Template, 4, 7, \"div\", 2);\n      i0.ɵɵelement(3, \"span\", 3, 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, NgxMaterialTimepickerFaceComponent_ng_template_5_Template, 2, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r3 = i0.ɵɵreference(6);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.unit !== ctx.timeUnit.MINUTE)(\"ngIfElse\", _r3);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, ctx.unit === ctx.timeUnit.MINUTE))(\"hidden\", ctx.isClockFaceDisabled);\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgForOf, i1.NgStyle, i1.NgClass, i1.SlicePipe, ActiveHourPipe, TimeLocalizerPipe, ActiveMinutePipe, MinutesFormatterPipe],\n  styles: [\".clock-face[_ngcontent-%COMP%]{width:290px;height:290px;border-radius:50%;position:relative;display:flex;justify-content:center;padding:20px;box-sizing:border-box;background-color:#f0f0f0}@supports (background-color: var(--clock-face-background-color)){.clock-face[_ngcontent-%COMP%]{background-color:var(--clock-face-background-color)}}.clock-face__inner[_ngcontent-%COMP%]{position:absolute}.clock-face__container[_ngcontent-%COMP%]{margin-left:-2px}.clock-face__number[_ngcontent-%COMP%]{position:absolute;transform-origin:0 100%;width:50px;text-align:center;z-index:2}.clock-face__number--outer[_ngcontent-%COMP%]{height:calc(290px / 2 - 20px)}.clock-face__number--outer[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;color:#6c6c6c}@supports (color: var(--clock-face-time-inactive-color)){.clock-face__number--outer[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{color:var(--clock-face-time-inactive-color)}}.clock-face__number--inner[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:14px;color:#929292}@supports (color: var(--clock-face-inner-time-inactive-color)){.clock-face__number--inner[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{color:var(--clock-face-inner-time-inactive-color)}}.clock-face__number[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{-webkit-user-select:none;-moz-user-select:none;user-select:none;width:30px;height:30px;display:flex;justify-content:center;align-items:center;margin:auto;border-radius:50%;font-weight:500;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.clock-face__number[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-family:var(--primary-font-family)}}.clock-face__number[_ngcontent-%COMP%] > span.active[_ngcontent-%COMP%]{background-color:#00bfff;color:#fff}@supports (background-color: var(--clock-hand-color)){.clock-face__number[_ngcontent-%COMP%] > span.active[_ngcontent-%COMP%]{background-color:var(--clock-hand-color);color:var(--clock-face-time-active-color)}}.clock-face__number[_ngcontent-%COMP%] > span.disabled[_ngcontent-%COMP%]{color:#c5c5c5}@supports (color: var(--clock-face-time-disabled-color)){.clock-face__number[_ngcontent-%COMP%] > span.disabled[_ngcontent-%COMP%]{color:var(--clock-face-time-disabled-color)}}.clock-face__clock-hand[_ngcontent-%COMP%]{height:103px;width:2px;transform-origin:0 100%;position:absolute;top:calc(50% - 103px);z-index:1;background-color:#00bfff}@supports (background-color: var(--clock-hand-color)){.clock-face__clock-hand[_ngcontent-%COMP%]{background-color:var(--clock-hand-color)}}.clock-face__clock-hand[_ngcontent-%COMP%]:after{content:\\\"\\\";width:7px;height:7px;border-radius:50%;background-color:inherit;position:absolute;bottom:-3px;left:-3.5px}.clock-face__clock-hand_minute[_ngcontent-%COMP%]:before{content:\\\"\\\";width:7px;height:7px;background-color:#fff;border-radius:50%;position:absolute;top:-8px;left:calc(50% - 8px);box-sizing:content-box;border-width:4px;border-style:solid;border-color:#00bfff}@supports (border-color: var(--clock-hand-color)){.clock-face__clock-hand_minute[_ngcontent-%COMP%]:before{border-color:var(--clock-hand-color)}}@media (max-device-width: 1023px) and (orientation: landscape){.clock-face[_ngcontent-%COMP%]{width:225px;height:225px;padding:5px}.clock-face__number--outer[_ngcontent-%COMP%]{height:calc(225px / 2 - 5px)}.clock-face__clock-hand_minute[_ngcontent-%COMP%]:before{top:0}}\"],\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerFaceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-face',\n      templateUrl: './ngx-material-timepicker-face.component.html',\n      styleUrls: ['./ngx-material-timepicker-face.component.scss'],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    faceTime: [{\n      type: Input\n    }],\n    selectedTime: [{\n      type: Input\n    }],\n    unit: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    timeChange: [{\n      type: Output\n    }],\n    timeSelected: [{\n      type: Output\n    }],\n    clockFace: [{\n      type: ViewChild,\n      args: ['clockFace', {\n        static: true\n      }]\n    }],\n    clockHand: [{\n      type: ViewChild,\n      args: ['clockHand', {\n        static: true\n      }]\n    }],\n    onMousedown: [{\n      type: HostListener,\n      args: ['mousedown', ['$event']]\n    }],\n    selectTime: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['touchmove', ['$event.changedTouches[0]']]\n    }, {\n      type: HostListener,\n      args: ['touchend', ['$event.changedTouches[0]']]\n    }, {\n      type: HostListener,\n      args: ['mousemove', ['$event']]\n    }],\n    onMouseup: [{\n      type: HostListener,\n      args: ['mouseup', ['$event']]\n    }]\n  });\n})();\nfunction roundAngle(angle, step) {\n  return Math.round(angle / step) * step;\n}\nfunction countAngleByCords(x0, y0, x, y, currentAngle) {\n  if (y > y0 && x >= x0) {\n    // II quarter\n    return 180 - currentAngle;\n  } else if (y > y0 && x < x0) {\n    // III quarter\n    return 180 + currentAngle;\n  } else if (y < y0 && x < x0) {\n    // IV quarter\n    return 360 - currentAngle;\n  } else {\n    // I quarter\n    return currentAngle;\n  }\n}\nclass NgxMaterialTimepicker24HoursFaceComponent extends NgxMaterialTimepickerHoursFace {\n  constructor() {\n    super(24);\n  }\n  ngAfterContentInit() {\n    this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n      min: this.minTime,\n      max: this.maxTime,\n      format: this.format\n    });\n  }\n}\nNgxMaterialTimepicker24HoursFaceComponent.ɵfac = function NgxMaterialTimepicker24HoursFaceComponent_Factory(t) {\n  return new (t || NgxMaterialTimepicker24HoursFaceComponent)();\n};\nNgxMaterialTimepicker24HoursFaceComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepicker24HoursFaceComponent,\n  selectors: [[\"ngx-material-timepicker-24-hours-face\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 3,\n  consts: [[3, \"selectedTime\", \"faceTime\", \"format\", \"timeChange\", \"timeSelected\"]],\n  template: function NgxMaterialTimepicker24HoursFaceComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ngx-material-timepicker-face\", 0);\n      i0.ɵɵlistener(\"timeChange\", function NgxMaterialTimepicker24HoursFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener($event) {\n        return ctx.hourChange.next($event);\n      })(\"timeSelected\", function NgxMaterialTimepicker24HoursFaceComponent_Template_ngx_material_timepicker_face_timeSelected_0_listener($event) {\n        return ctx.onTimeSelected($event);\n      });\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"selectedTime\", ctx.selectedHour)(\"faceTime\", ctx.hoursList)(\"format\", ctx.format);\n    }\n  },\n  dependencies: [NgxMaterialTimepickerFaceComponent],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepicker24HoursFaceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-24-hours-face',\n      templateUrl: 'ngx-material-timepicker-24-hours-face.component.html',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass NgxMaterialTimepicker12HoursFaceComponent extends NgxMaterialTimepickerHoursFace {\n  constructor() {\n    super(12);\n  }\n  ngOnChanges(changes) {\n    if (changes['period'] && changes['period'].currentValue) {\n      this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n        min: this.minTime,\n        max: this.maxTime,\n        format: this.format,\n        period: this.period\n      });\n    }\n  }\n}\nNgxMaterialTimepicker12HoursFaceComponent.ɵfac = function NgxMaterialTimepicker12HoursFaceComponent_Factory(t) {\n  return new (t || NgxMaterialTimepicker12HoursFaceComponent)();\n};\nNgxMaterialTimepicker12HoursFaceComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepicker12HoursFaceComponent,\n  selectors: [[\"ngx-material-timepicker-12-hours-face\"]],\n  inputs: {\n    period: \"period\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 2,\n  consts: [[3, \"selectedTime\", \"faceTime\", \"timeChange\", \"timeSelected\"]],\n  template: function NgxMaterialTimepicker12HoursFaceComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ngx-material-timepicker-face\", 0);\n      i0.ɵɵlistener(\"timeChange\", function NgxMaterialTimepicker12HoursFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener($event) {\n        return ctx.hourChange.next($event);\n      })(\"timeSelected\", function NgxMaterialTimepicker12HoursFaceComponent_Template_ngx_material_timepicker_face_timeSelected_0_listener($event) {\n        return ctx.onTimeSelected($event);\n      });\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"selectedTime\", ctx.selectedHour)(\"faceTime\", ctx.hoursList);\n    }\n  },\n  dependencies: [NgxMaterialTimepickerFaceComponent],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepicker12HoursFaceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-12-hours-face',\n      templateUrl: 'ngx-material-timepicker-12-hours-face.component.html',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [];\n  }, {\n    period: [{\n      type: Input\n    }]\n  });\n})();\nclass NgxMaterialTimepickerMinutesFaceComponent {\n  constructor() {\n    this.minutesList = [];\n    this.timeUnit = TimeUnit;\n    this.minuteChange = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    if (changes['period'] && changes['period'].currentValue) {\n      const minutes = TimepickerTimeUtils.getMinutes(this.minutesGap);\n      this.minutesList = TimepickerTimeUtils.disableMinutes(minutes, this.selectedHour, {\n        min: this.minTime,\n        max: this.maxTime,\n        format: this.format,\n        period: this.period\n      });\n    }\n  }\n}\nNgxMaterialTimepickerMinutesFaceComponent.ɵfac = function NgxMaterialTimepickerMinutesFaceComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerMinutesFaceComponent)();\n};\nNgxMaterialTimepickerMinutesFaceComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerMinutesFaceComponent,\n  selectors: [[\"ngx-material-timepicker-minutes-face\"]],\n  inputs: {\n    selectedMinute: \"selectedMinute\",\n    selectedHour: \"selectedHour\",\n    period: \"period\",\n    minTime: \"minTime\",\n    maxTime: \"maxTime\",\n    format: \"format\",\n    minutesGap: \"minutesGap\"\n  },\n  outputs: {\n    minuteChange: \"minuteChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 4,\n  consts: [[3, \"faceTime\", \"selectedTime\", \"minutesGap\", \"unit\", \"timeChange\"]],\n  template: function NgxMaterialTimepickerMinutesFaceComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ngx-material-timepicker-face\", 0);\n      i0.ɵɵlistener(\"timeChange\", function NgxMaterialTimepickerMinutesFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener($event) {\n        return ctx.minuteChange.next($event);\n      });\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"faceTime\", ctx.minutesList)(\"selectedTime\", ctx.selectedMinute)(\"minutesGap\", ctx.minutesGap)(\"unit\", ctx.timeUnit.MINUTE);\n    }\n  },\n  dependencies: [NgxMaterialTimepickerFaceComponent],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerMinutesFaceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-minutes-face',\n      templateUrl: './ngx-material-timepicker-minutes-face.component.html'\n    }]\n  }], null, {\n    selectedMinute: [{\n      type: Input\n    }],\n    selectedHour: [{\n      type: Input\n    }],\n    period: [{\n      type: Input\n    }],\n    minTime: [{\n      type: Input\n    }],\n    maxTime: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    minuteChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxMaterialTimepickerButtonComponent {}\nNgxMaterialTimepickerButtonComponent.ɵfac = function NgxMaterialTimepickerButtonComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerButtonComponent)();\n};\nNgxMaterialTimepickerButtonComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerButtonComponent,\n  selectors: [[\"ngx-material-timepicker-button\"]],\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 0,\n  consts: [[\"type\", \"button\", 1, \"timepicker-button\"]],\n  template: function NgxMaterialTimepickerButtonComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0)(1, \"span\");\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd()();\n    }\n  },\n  styles: [\".timepicker-button[_ngcontent-%COMP%]{display:inline-block;height:36px;min-width:88px;line-height:36px;border:12px;border-radius:2px;background-color:transparent;text-align:center;transition:all .45s cubic-bezier(.23,1,.32,1);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:relative;cursor:pointer;outline:none;color:#00bfff}@supports (color: var(--button-color)){.timepicker-button[_ngcontent-%COMP%]{color:var(--button-color)}}.timepicker-button[_ngcontent-%COMP%]:hover, .timepicker-button[_ngcontent-%COMP%]:focus{background-color:#9993}.timepicker-button[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:14px;text-transform:uppercase;font-weight:600;padding-left:16px;padding-right:16px;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-button[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-family:var(--primary-font-family)}}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-button',\n      templateUrl: './ngx-material-timepicker-button.component.html',\n      styleUrls: ['./ngx-material-timepicker-button.component.scss']\n    }]\n  }], null, null);\n})();\nclass OverlayDirective {\n  constructor(eventService) {\n    this.eventService = eventService;\n  }\n  onClick(e) {\n    if (!this.preventClick) {\n      this.eventService.dispatchEvent(e);\n    }\n    e.preventDefault();\n  }\n}\nOverlayDirective.ɵfac = function OverlayDirective_Factory(t) {\n  return new (t || OverlayDirective)(i0.ɵɵdirectiveInject(NgxMaterialTimepickerEventService));\n};\nOverlayDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: OverlayDirective,\n  selectors: [[\"\", \"overlay\", \"\"]],\n  hostBindings: function OverlayDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function OverlayDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n  },\n  inputs: {\n    preventClick: [\"overlay\", \"preventClick\"]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[overlay]'\n    }]\n  }], function () {\n    return [{\n      type: NgxMaterialTimepickerEventService\n    }];\n  }, {\n    preventClick: [{\n      type: Input,\n      args: ['overlay']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass NgxMaterialTimepickerThemeDirective {\n  constructor(elementRef) {\n    this.element = elementRef.nativeElement;\n  }\n  ngAfterViewInit() {\n    if (this.theme) {\n      this.setTheme(this.theme);\n    }\n  }\n  setTheme(theme) {\n    for (const val in theme) {\n      if (theme.hasOwnProperty(val)) {\n        if (typeof theme[val] === 'string') {\n          for (const prop in theme) {\n            if (theme.hasOwnProperty(prop)) {\n              this.element.style.setProperty(`--${camelCaseToDash(prop)}`, theme[prop]);\n            }\n          }\n          return;\n        }\n        this.setTheme(theme[val]);\n      }\n    }\n  }\n}\nNgxMaterialTimepickerThemeDirective.ɵfac = function NgxMaterialTimepickerThemeDirective_Factory(t) {\n  return new (t || NgxMaterialTimepickerThemeDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nNgxMaterialTimepickerThemeDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgxMaterialTimepickerThemeDirective,\n  selectors: [[\"\", \"ngxMaterialTimepickerTheme\", \"\"]],\n  inputs: {\n    theme: [\"ngxMaterialTimepickerTheme\", \"theme\"]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerThemeDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMaterialTimepickerTheme]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    theme: [{\n      type: Input,\n      args: ['ngxMaterialTimepickerTheme']\n    }]\n  });\n})();\nfunction camelCaseToDash(myStr) {\n  return myStr.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\nvar AnimationState;\n(function (AnimationState) {\n  AnimationState[\"ENTER\"] = \"enter\";\n  AnimationState[\"LEAVE\"] = \"leave\";\n})(AnimationState || (AnimationState = {}));\nclass NgxMaterialTimepickerContainerComponent {\n  constructor(timepickerService, eventService, locale) {\n    this.timepickerService = timepickerService;\n    this.eventService = eventService;\n    this.locale = locale;\n    this.timeUnit = TimeUnit;\n    this.activeTimeUnit = TimeUnit.HOUR;\n    this.unsubscribe = new Subject();\n  }\n  set defaultTime(time) {\n    this._defaultTime = time;\n    this.setDefaultTime(time);\n  }\n  get defaultTime() {\n    return this._defaultTime;\n  }\n  onKeydown(e) {\n    this.eventService.dispatchEvent(e);\n    e.stopPropagation();\n  }\n  ngOnInit() {\n    this.animationState = !this.disableAnimation && AnimationState.ENTER;\n    this.defineTime();\n    this.selectedHour = this.timepickerService.selectedHour.pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.selectedMinute = this.timepickerService.selectedMinute.pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.selectedPeriod = this.timepickerService.selectedPeriod.pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.timepickerBaseRef.timeUpdated.pipe(takeUntil(this.unsubscribe)).subscribe(this.setDefaultTime.bind(this));\n  }\n  onHourChange(hour) {\n    this.timepickerService.hour = hour;\n    this.onTimeChange();\n  }\n  onHourSelected(hour) {\n    if (!this.hoursOnly) {\n      this.changeTimeUnit(TimeUnit.MINUTE);\n    }\n    this.timepickerBaseRef.hourSelected.next(hour);\n  }\n  onMinuteChange(minute) {\n    this.timepickerService.minute = minute;\n    this.onTimeChange();\n  }\n  changePeriod(period) {\n    this.timepickerService.period = period;\n    this.onTimeChange();\n  }\n  changeTimeUnit(unit) {\n    this.activeTimeUnit = unit;\n  }\n  setTime() {\n    this.timepickerBaseRef.timeSet.next(this.timepickerService.getFullTime(this.format));\n    this.close();\n  }\n  close() {\n    if (this.disableAnimation) {\n      this.timepickerBaseRef.close();\n      return;\n    }\n    this.animationState = AnimationState.LEAVE;\n  }\n  animationDone(event) {\n    if (event.phaseName === 'done' && event.toState === AnimationState.LEAVE) {\n      this.timepickerBaseRef.close();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.next();\n    this.unsubscribe.complete();\n  }\n  setDefaultTime(time) {\n    this.timepickerService.setDefaultTimeIfAvailable(time, this.minTime, this.maxTime, this.format, this.minutesGap);\n  }\n  defineTime() {\n    const minTime = this.minTime;\n    if (minTime && !this.time && !this.defaultTime) {\n      const time = TimeAdapter.fromDateTimeToString(minTime, this.format);\n      this.setDefaultTime(time);\n    }\n  }\n  onTimeChange() {\n    const time = TimeAdapter.toLocaleTimeString(this.timepickerService.getFullTime(this.format), {\n      locale: this.locale,\n      format: this.format\n    });\n    this.timepickerBaseRef.timeChanged.emit(time);\n  }\n}\nNgxMaterialTimepickerContainerComponent.ɵfac = function NgxMaterialTimepickerContainerComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerContainerComponent)(i0.ɵɵdirectiveInject(NgxMaterialTimepickerService), i0.ɵɵdirectiveInject(NgxMaterialTimepickerEventService), i0.ɵɵdirectiveInject(TIME_LOCALE));\n};\nNgxMaterialTimepickerContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerContainerComponent,\n  selectors: [[\"ngx-material-timepicker-container\"]],\n  hostBindings: function NgxMaterialTimepickerContainerComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function NgxMaterialTimepickerContainerComponent_keydown_HostBindingHandler($event) {\n        return ctx.onKeydown($event);\n      });\n    }\n  },\n  inputs: {\n    defaultTime: \"defaultTime\"\n  },\n  features: [i0.ɵɵProvidersFeature([NgxMaterialTimepickerService])],\n  decls: 22,\n  vars: 31,\n  consts: [[1, \"timepicker-backdrop-overlay\", 3, \"overlay\", \"ngClass\"], [1, \"timepicker-overlay\"], [3, \"appendToInput\", \"inputElement\", \"ngxMaterialTimepickerTheme\"], [1, \"timepicker\", 3, \"ngClass\"], [1, \"timepicker__header\"], [3, \"format\", \"hour\", \"minute\", \"period\", \"activeTimeUnit\", \"minTime\", \"maxTime\", \"isEditable\", \"editableHintTmpl\", \"minutesGap\", \"hoursOnly\", \"periodChanged\", \"timeUnitChanged\", \"hourChanged\", \"minuteChanged\"], [1, \"timepicker__main-content\"], [1, \"timepicker__body\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"selectedMinute\", \"selectedHour\", \"minTime\", \"maxTime\", \"format\", \"period\", \"minutesGap\", \"minuteChange\", 4, \"ngSwitchCase\"], [1, \"timepicker__actions\"], [3, \"click\"], [4, \"ngTemplateOutlet\"], [\"cancelBtnDefault\", \"\"], [\"confirmBtnDefault\", \"\"], [3, \"selectedHour\", \"minTime\", \"maxTime\", \"format\", \"hourChange\", \"hourSelected\", 4, \"ngIf\", \"ngIfElse\"], [\"ampmHours\", \"\"], [3, \"selectedHour\", \"minTime\", \"maxTime\", \"format\", \"hourChange\", \"hourSelected\"], [3, \"selectedHour\", \"period\", \"minTime\", \"maxTime\", \"hourChange\", \"hourSelected\"], [3, \"selectedMinute\", \"selectedHour\", \"minTime\", \"maxTime\", \"format\", \"period\", \"minutesGap\", \"minuteChange\"]],\n  template: function NgxMaterialTimepickerContainerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1)(2, \"ngx-material-timepicker-content\", 2)(3, \"div\", 3);\n      i0.ɵɵlistener(\"@timepicker.done\", function NgxMaterialTimepickerContainerComponent_Template_div_animation_timepicker_done_3_listener($event) {\n        return ctx.animationDone($event);\n      });\n      i0.ɵɵelementStart(4, \"header\", 4)(5, \"ngx-material-timepicker-dial\", 5);\n      i0.ɵɵlistener(\"periodChanged\", function NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_periodChanged_5_listener($event) {\n        return ctx.changePeriod($event);\n      })(\"timeUnitChanged\", function NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_timeUnitChanged_5_listener($event) {\n        return ctx.changeTimeUnit($event);\n      })(\"hourChanged\", function NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_hourChanged_5_listener($event) {\n        return ctx.onHourChange($event);\n      })(\"minuteChanged\", function NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_minuteChanged_5_listener($event) {\n        return ctx.onMinuteChange($event);\n      });\n      i0.ɵɵpipe(6, \"async\");\n      i0.ɵɵpipe(7, \"async\");\n      i0.ɵɵpipe(8, \"async\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7);\n      i0.ɵɵtemplate(11, NgxMaterialTimepickerContainerComponent_div_11_Template, 4, 2, \"div\", 8);\n      i0.ɵɵtemplate(12, NgxMaterialTimepickerContainerComponent_ngx_material_timepicker_minutes_face_12_Template, 4, 13, \"ngx-material-timepicker-minutes-face\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11);\n      i0.ɵɵlistener(\"click\", function NgxMaterialTimepickerContainerComponent_Template_div_click_14_listener() {\n        return ctx.close();\n      });\n      i0.ɵɵtemplate(15, NgxMaterialTimepickerContainerComponent_ng_container_15_Template, 1, 0, \"ng-container\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 11);\n      i0.ɵɵlistener(\"click\", function NgxMaterialTimepickerContainerComponent_Template_div_click_16_listener() {\n        return ctx.setTime();\n      });\n      i0.ɵɵtemplate(17, NgxMaterialTimepickerContainerComponent_ng_container_17_Template, 1, 0, \"ng-container\", 12);\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵtemplate(18, NgxMaterialTimepickerContainerComponent_ng_template_18_Template, 2, 0, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(20, NgxMaterialTimepickerContainerComponent_ng_template_20_Template, 2, 0, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r4 = i0.ɵɵreference(19);\n      const _r6 = i0.ɵɵreference(21);\n      let tmp_8_0;\n      let tmp_9_0;\n      i0.ɵɵproperty(\"overlay\", ctx.preventOverlayClick)(\"ngClass\", i0.ɵɵpureFunction1(29, _c10, ctx.appendToInput));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"appendToInput\", ctx.appendToInput)(\"inputElement\", ctx.inputElement)(\"ngxMaterialTimepickerTheme\", ctx.theme);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"@timepicker\", ctx.animationState)(\"ngClass\", ctx.timepickerClass);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"format\", ctx.format)(\"hour\", (tmp_8_0 = i0.ɵɵpipeBind1(6, 23, ctx.selectedHour)) == null ? null : tmp_8_0.time)(\"minute\", (tmp_9_0 = i0.ɵɵpipeBind1(7, 25, ctx.selectedMinute)) == null ? null : tmp_9_0.time)(\"period\", i0.ɵɵpipeBind1(8, 27, ctx.selectedPeriod))(\"activeTimeUnit\", ctx.activeTimeUnit)(\"minTime\", ctx.minTime)(\"maxTime\", ctx.maxTime)(\"isEditable\", ctx.enableKeyboardInput)(\"editableHintTmpl\", ctx.editableHintTmpl)(\"minutesGap\", ctx.minutesGap)(\"hoursOnly\", ctx.hoursOnly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngSwitch\", ctx.activeTimeUnit);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", ctx.timeUnit.HOUR);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", ctx.timeUnit.MINUTE);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.cancelBtnTmpl ? ctx.cancelBtnTmpl : _r4);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.confirmBtnTmpl ? ctx.confirmBtnTmpl : _r6);\n    }\n  },\n  dependencies: [NgxMaterialTimepickerContentComponent, NgxMaterialTimepickerDialComponent, NgxMaterialTimepicker24HoursFaceComponent, NgxMaterialTimepicker12HoursFaceComponent, NgxMaterialTimepickerMinutesFaceComponent, NgxMaterialTimepickerButtonComponent, OverlayDirective, i1.NgClass, NgxMaterialTimepickerThemeDirective, i1.NgSwitch, i1.NgSwitchCase, i1.NgIf, i1.NgTemplateOutlet, i1.AsyncPipe],\n  styles: [\"[_nghost-%COMP%]{--body-background-color: #fff;--primary-font-family: \\\"Roboto\\\", sans-serif;--button-color: deepskyblue;--dial-active-color: #fff;--dial-inactive-color: rgba(255, 255, 255, .5);--dial-background-color: deepskyblue;--dial-editable-active-color: deepskyblue;--dial-editable-background-color: #fff;--clock-face-time-active-color: #fff;--clock-face-time-inactive-color: #6c6c6c;--clock-face-inner-time-inactive-color: #929292;--clock-face-time-disabled-color: #c5c5c5;--clock-face-background-color: #f0f0f0;--clock-hand-color: deepskyblue}.timepicker-backdrop-overlay[_ngcontent-%COMP%]{position:fixed;top:0;bottom:0;right:0;left:0;background-color:#0000004d;z-index:999;pointer-events:auto}.timepicker-backdrop-overlay--transparent[_ngcontent-%COMP%]{background-color:transparent}.timepicker-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;z-index:999;pointer-events:none}.timepicker[_ngcontent-%COMP%]{width:300px;border-radius:2px;box-shadow:#00000040 0 14px 45px,#00000038 0 10px 18px;outline:none;position:static;z-index:999;pointer-events:auto}.timepicker__header[_ngcontent-%COMP%]{padding:15px 30px;background-color:#00bfff}@supports (background-color: var(--dial-background-color)){.timepicker__header[_ngcontent-%COMP%]{background-color:var(--dial-background-color)}}.timepicker__body[_ngcontent-%COMP%]{padding:15px 5px;display:flex;justify-content:center;align-items:center;background-color:#fff}@supports (background-color: var(--body-background-color)){.timepicker__body[_ngcontent-%COMP%]{background-color:var(--body-background-color)}}.timepicker__actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:15px;background-color:#fff}@supports (background-color: var(--body-background-color)){.timepicker__actions[_ngcontent-%COMP%]{background-color:var(--body-background-color)}}@media (max-device-width: 1023px) and (orientation: landscape){.timepicker[_ngcontent-%COMP%]{display:flex;width:515px}.timepicker__header[_ngcontent-%COMP%]{display:flex;align-items:center}.timepicker__main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.timepicker__actions[_ngcontent-%COMP%]{padding:5px;margin-top:-1px}}\"],\n  data: {\n    animation: [trigger('timepicker', [transition(`* => ${AnimationState.ENTER}`, [style({\n      transform: 'translateY(-30%)'\n    }), animate('0.2s ease-out', style({\n      transform: 'translateY(0)'\n    }))]), transition(`${AnimationState.ENTER} => ${AnimationState.LEAVE}`, [style({\n      transform: 'translateY(0)',\n      opacity: 1\n    }), animate('0.2s ease-out', style({\n      transform: 'translateY(-30%)',\n      opacity: 0\n    }))])])]\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-container',\n      templateUrl: './ngx-material-timepicker-container.component.html',\n      styleUrls: ['./ngx-material-timepicker-container.component.scss'],\n      animations: [trigger('timepicker', [transition(`* => ${AnimationState.ENTER}`, [style({\n        transform: 'translateY(-30%)'\n      }), animate('0.2s ease-out', style({\n        transform: 'translateY(0)'\n      }))]), transition(`${AnimationState.ENTER} => ${AnimationState.LEAVE}`, [style({\n        transform: 'translateY(0)',\n        opacity: 1\n      }), animate('0.2s ease-out', style({\n        transform: 'translateY(-30%)',\n        opacity: 0\n      }))])])],\n      providers: [NgxMaterialTimepickerService]\n    }]\n  }], function () {\n    return [{\n      type: NgxMaterialTimepickerService\n    }, {\n      type: NgxMaterialTimepickerEventService\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, {\n    defaultTime: [{\n      type: Input\n    }],\n    onKeydown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass DomService {\n  constructor(cfr, appRef, injector, document) {\n    this.cfr = cfr;\n    this.appRef = appRef;\n    this.injector = injector;\n    this.document = document;\n  }\n  appendTimepickerToBody(timepicker, config) {\n    this.componentRef = this.cfr.resolveComponentFactory(timepicker).create(this.injector);\n    Object.keys(config).forEach(key => this.componentRef.instance[key] = config[key]);\n    this.appRef.attachView(this.componentRef.hostView);\n    const domElement = this.componentRef.hostView.rootNodes[0];\n    this.document.body.appendChild(domElement);\n  }\n  destroyTimepicker() {\n    this.componentRef.destroy();\n    this.appRef.detachView(this.componentRef.hostView);\n  }\n}\nDomService.ɵfac = function DomService_Factory(t) {\n  return new (t || DomService)(i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DOCUMENT, 8));\n};\nDomService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomService,\n  factory: DomService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst ESCAPE = 27;\nclass NgxMaterialTimepickerComponent {\n  constructor(eventService, domService) {\n    this.eventService = eventService;\n    this.domService = domService;\n    this.timeUpdated = new Subject();\n    this.isEsc = true;\n    this.hoursOnly = false;\n    this.timeSet = new EventEmitter();\n    this.opened = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.hourSelected = new EventEmitter();\n    this.timeChanged = new EventEmitter();\n    this.unsubscribe = new Subject();\n  }\n  /**\n   * @deprecated Since version 5.1.1. Will be deleted on version 6.0.0. Use @Input() theme instead\n   */\n  set ngxMaterialTimepickerTheme(theme) {\n    console.warn(`'ngxMaterialTimepickerTheme' is deprecated. Use 'theme' instead`);\n    this._ngxMaterialTimepickerTheme = theme;\n  }\n  set format(value) {\n    this._format = value === 24 ? 24 : 12;\n  }\n  get format() {\n    return this.timepickerInput ? this.timepickerInput.format : this._format;\n  }\n  set minutesGap(gap) {\n    if (gap == null) {\n      return;\n    }\n    gap = Math.floor(gap);\n    this._minutesGap = gap <= 59 ? gap : 1;\n  }\n  get minutesGap() {\n    return this._minutesGap;\n  }\n  get minTime() {\n    return this.timepickerInput ? this.timepickerInput.min : this.min;\n  }\n  get maxTime() {\n    return this.timepickerInput ? this.timepickerInput.max : this.max;\n  }\n  get disabled() {\n    return this.timepickerInput && this.timepickerInput.disabled;\n  }\n  get time() {\n    return this.timepickerInput && this.timepickerInput.value;\n  }\n  get inputElement() {\n    return this.timepickerInput && this.timepickerInput.element;\n  }\n  /***\n   * Register an input with this timepicker.\n   * input - The timepicker input to register with this timepicker\n   */\n  registerInput(input) {\n    if (this.timepickerInput) {\n      throw Error('A Timepicker can only be associated with a single input.');\n    }\n    this.timepickerInput = input;\n  }\n  open() {\n    this.domService.appendTimepickerToBody(NgxMaterialTimepickerContainerComponent, {\n      timepickerBaseRef: this,\n      time: this.time,\n      defaultTime: this.defaultTime,\n      maxTime: this.maxTime,\n      minTime: this.minTime,\n      format: this.format,\n      minutesGap: this.minutesGap,\n      disableAnimation: this.disableAnimation,\n      cancelBtnTmpl: this.cancelBtnTmpl,\n      confirmBtnTmpl: this.confirmBtnTmpl,\n      editableHintTmpl: this.editableHintTmpl,\n      disabled: this.disabled,\n      enableKeyboardInput: this.enableKeyboardInput,\n      preventOverlayClick: this.preventOverlayClick,\n      appendToInput: this.appendToInput,\n      hoursOnly: this.hoursOnly,\n      theme: this.theme || this._ngxMaterialTimepickerTheme,\n      timepickerClass: this.timepickerClass,\n      inputElement: this.inputElement\n    });\n    this.opened.next();\n    this.subscribeToEvents();\n  }\n  close() {\n    this.domService.destroyTimepicker();\n    this.closed.next();\n    this.unsubscribeFromEvents();\n  }\n  updateTime(time) {\n    this.timeUpdated.next(time);\n  }\n  subscribeToEvents() {\n    merge(this.eventService.backdropClick, this.eventService.keydownEvent.pipe(filter(e => e.keyCode === ESCAPE && this.isEsc))).pipe(takeUntil(this.unsubscribe)).subscribe(() => this.close());\n  }\n  unsubscribeFromEvents() {\n    this.unsubscribe.next();\n    this.unsubscribe.complete();\n  }\n}\nNgxMaterialTimepickerComponent.ɵfac = function NgxMaterialTimepickerComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerComponent)(i0.ɵɵdirectiveInject(NgxMaterialTimepickerEventService), i0.ɵɵdirectiveInject(DomService));\n};\nNgxMaterialTimepickerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerComponent,\n  selectors: [[\"ngx-material-timepicker\"]],\n  inputs: {\n    cancelBtnTmpl: \"cancelBtnTmpl\",\n    editableHintTmpl: \"editableHintTmpl\",\n    confirmBtnTmpl: \"confirmBtnTmpl\",\n    isEsc: [\"ESC\", \"isEsc\"],\n    enableKeyboardInput: \"enableKeyboardInput\",\n    preventOverlayClick: \"preventOverlayClick\",\n    disableAnimation: \"disableAnimation\",\n    appendToInput: \"appendToInput\",\n    hoursOnly: \"hoursOnly\",\n    defaultTime: \"defaultTime\",\n    timepickerClass: \"timepickerClass\",\n    theme: \"theme\",\n    min: \"min\",\n    max: \"max\",\n    ngxMaterialTimepickerTheme: \"ngxMaterialTimepickerTheme\",\n    format: \"format\",\n    minutesGap: \"minutesGap\"\n  },\n  outputs: {\n    timeSet: \"timeSet\",\n    opened: \"opened\",\n    closed: \"closed\",\n    hourSelected: \"hourSelected\",\n    timeChanged: \"timeChanged\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function NgxMaterialTimepickerComponent_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker',\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: NgxMaterialTimepickerEventService\n    }, {\n      type: DomService\n    }];\n  }, {\n    cancelBtnTmpl: [{\n      type: Input\n    }],\n    editableHintTmpl: [{\n      type: Input\n    }],\n    confirmBtnTmpl: [{\n      type: Input\n    }],\n    isEsc: [{\n      type: Input,\n      args: ['ESC']\n    }],\n    enableKeyboardInput: [{\n      type: Input\n    }],\n    preventOverlayClick: [{\n      type: Input\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    appendToInput: [{\n      type: Input\n    }],\n    hoursOnly: [{\n      type: Input\n    }],\n    defaultTime: [{\n      type: Input\n    }],\n    timepickerClass: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    ngxMaterialTimepickerTheme: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    timeSet: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    hourSelected: [{\n      type: Output\n    }],\n    timeChanged: [{\n      type: Output\n    }]\n  });\n})();\n\n/* To override a default toggle icon */\nclass NgxMaterialTimepickerToggleIconDirective {}\nNgxMaterialTimepickerToggleIconDirective.ɵfac = function NgxMaterialTimepickerToggleIconDirective_Factory(t) {\n  return new (t || NgxMaterialTimepickerToggleIconDirective)();\n};\nNgxMaterialTimepickerToggleIconDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgxMaterialTimepickerToggleIconDirective,\n  selectors: [[\"\", \"ngxMaterialTimepickerToggleIcon\", \"\"]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerToggleIconDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMaterialTimepickerToggleIcon]'\n    }]\n  }], null, null);\n})();\nclass NgxMaterialTimepickerToggleComponent {\n  get disabled() {\n    return this._disabled === undefined ? this.timepicker.disabled : this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  open(event) {\n    if (this.timepicker) {\n      this.timepicker.open();\n      event.stopPropagation();\n    }\n  }\n}\nNgxMaterialTimepickerToggleComponent.ɵfac = function NgxMaterialTimepickerToggleComponent_Factory(t) {\n  return new (t || NgxMaterialTimepickerToggleComponent)();\n};\nNgxMaterialTimepickerToggleComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxMaterialTimepickerToggleComponent,\n  selectors: [[\"ngx-material-timepicker-toggle\"]],\n  contentQueries: function NgxMaterialTimepickerToggleComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, NgxMaterialTimepickerToggleIconDirective, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customIcon = _t.first);\n    }\n  },\n  inputs: {\n    timepicker: [\"for\", \"timepicker\"],\n    disabled: \"disabled\"\n  },\n  ngContentSelectors: _c12,\n  decls: 3,\n  vars: 2,\n  consts: [[\"type\", \"button\", 1, \"ngx-material-timepicker-toggle\", 3, \"disabled\", \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", 4, \"ngIf\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\"], [\"d\", \"M 12 2 C 6.4889971 2 2 6.4889971 2 12 C 2 17.511003                   6.4889971 22 12 22 C 17.511003 22 22 17.511003 22 12 C 22 6.4889971 17.511003 2 12 2 z M 12 4 C 16.430123 4 20 7.5698774 20 12 C 20 16.430123 16.430123 20 12 20 C 7.5698774 20 4 16.430123 4 12 C 4 7.5698774 7.5698774 4 12 4 z M 11 6 L 11 12.414062 L 15.292969 16.707031 L 16.707031 15.292969 L 13 11.585938 L 13 6 L 11 6 z\"]],\n  template: function NgxMaterialTimepickerToggleComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c11);\n      i0.ɵɵelementStart(0, \"button\", 0);\n      i0.ɵɵlistener(\"click\", function NgxMaterialTimepickerToggleComponent_Template_button_click_0_listener($event) {\n        return ctx.open($event);\n      });\n      i0.ɵɵtemplate(1, NgxMaterialTimepickerToggleComponent__svg_svg_1_Template, 2, 0, \"svg\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"disabled\", ctx.disabled);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.customIcon);\n    }\n  },\n  dependencies: [i1.NgIf],\n  styles: [\".ngx-material-timepicker-toggle[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:4px;background-color:transparent;border-radius:50%;text-align:center;border:none;outline:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;transition:background-color .3s;cursor:pointer}.ngx-material-timepicker-toggle[_ngcontent-%COMP%]:focus{background-color:#00000012}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerToggleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-material-timepicker-toggle',\n      templateUrl: 'ngx-material-timepicker-toggle.component.html',\n      styleUrls: ['ngx-material-timepicker-toggle.component.scss']\n    }]\n  }], null, {\n    timepicker: [{\n      type: Input,\n      args: ['for']\n    }],\n    disabled: [{\n      type: Input\n    }],\n    customIcon: [{\n      type: ContentChild,\n      args: [NgxMaterialTimepickerToggleIconDirective, {\n        static: true\n      }]\n    }]\n  });\n})();\nclass TimepickerDirective {\n  constructor(elementRef, locale) {\n    this.elementRef = elementRef;\n    this.locale = locale;\n    this._format = 12;\n    this._value = '';\n    this.timepickerSubscriptions = [];\n    this.onTouched = () => {};\n    this.onChange = () => {};\n  }\n  set format(value) {\n    this._format = value === 24 ? 24 : 12;\n    const isDynamicallyChanged = value && this.previousFormat && this.previousFormat !== this._format;\n    if (isDynamicallyChanged) {\n      this.value = this._value;\n      this._timepicker.updateTime(this._value);\n    }\n    this.previousFormat = this._format;\n  }\n  get format() {\n    return this._format;\n  }\n  set min(value) {\n    console.log(value);\n    if (typeof value === 'string') {\n      this._min = TimeAdapter.parseTime(value, {\n        locale: this.locale,\n        format: this.format\n      });\n      return;\n    }\n    this._min = value;\n  }\n  get min() {\n    return this._min;\n  }\n  set max(value) {\n    if (typeof value === 'string') {\n      this._max = TimeAdapter.parseTime(value, {\n        locale: this.locale,\n        format: this.format\n      });\n      return;\n    }\n    this._max = value;\n  }\n  get max() {\n    return this._max;\n  }\n  set timepicker(picker) {\n    this.registerTimepicker(picker);\n  }\n  set value(value) {\n    if (!value) {\n      this._value = '';\n      this.updateInputValue();\n      return;\n    }\n    this.setTimeIfAvailable(value);\n  }\n  get value() {\n    if (!this._value) {\n      return '';\n    }\n    return TimeAdapter.toLocaleTimeString(this._value, {\n      format: this.format,\n      locale: this.locale\n    });\n  }\n  get element() {\n    return this.elementRef && this.elementRef.nativeElement;\n  }\n  set defaultTime(time) {\n    this._timepicker.defaultTime = TimeAdapter.formatTime(time, {\n      locale: this.locale,\n      format: this.format\n    });\n  }\n  updateValue(value) {\n    this.value = value;\n    this.onChange(value);\n  }\n  ngOnChanges(changes) {\n    var _a;\n    const value = (_a = changes === null || changes === void 0 ? void 0 : changes.value) === null || _a === void 0 ? void 0 : _a.currentValue;\n    if (value) {\n      // Call setTimeIfAvailable after @Input setters\n      this.setTimeIfAvailable(value);\n      this.defaultTime = value;\n    }\n  }\n  onClick(event) {\n    if (!this.disableClick) {\n      this._timepicker.open();\n      event.stopPropagation();\n    }\n  }\n  writeValue(value) {\n    this.value = value;\n    if (value) {\n      this.defaultTime = value;\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnDestroy() {\n    this.timepickerSubscriptions.forEach(s => s.unsubscribe());\n  }\n  registerTimepicker(picker) {\n    if (picker) {\n      this._timepicker = picker;\n      this._timepicker.registerInput(this);\n      this.timepickerSubscriptions.push(this._timepicker.timeSet.subscribe(time => {\n        this.value = time;\n        this.onChange(this.value);\n        this.onTouched();\n        this.defaultTime = this._value;\n      }));\n    } else {\n      throw new Error('NgxMaterialTimepickerComponent is not defined.' + ' Please make sure you passed the timepicker to ngxTimepicker directive');\n    }\n  }\n  updateInputValue() {\n    this.elementRef.nativeElement.value = this.value;\n  }\n  setTimeIfAvailable(value) {\n    var _a;\n    const time = TimeAdapter.formatTime(value, {\n      locale: this.locale,\n      format: this.format\n    });\n    const isAvailable = TimeAdapter.isTimeAvailable(time, this._min, this._max, 'minutes', (_a = this._timepicker) === null || _a === void 0 ? void 0 : _a.minutesGap, this._format);\n    if (isAvailable) {\n      this._value = time;\n      this.updateInputValue();\n    } else {\n      this.value = null;\n      console.warn('Selected time doesn\\'t match min or max value');\n    }\n  }\n}\nTimepickerDirective.ɵfac = function TimepickerDirective_Factory(t) {\n  return new (t || TimepickerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(TIME_LOCALE));\n};\nTimepickerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TimepickerDirective,\n  selectors: [[\"\", \"ngxTimepicker\", \"\"]],\n  hostVars: 1,\n  hostBindings: function TimepickerDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"change\", function TimepickerDirective_change_HostBindingHandler($event) {\n        return ctx.updateValue($event.target.value);\n      })(\"blur\", function TimepickerDirective_blur_HostBindingHandler() {\n        return ctx.onTouched();\n      })(\"click\", function TimepickerDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    format: \"format\",\n    min: \"min\",\n    max: \"max\",\n    timepicker: [\"ngxTimepicker\", \"timepicker\"],\n    value: \"value\",\n    disabled: \"disabled\",\n    disableClick: \"disableClick\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: TimepickerDirective,\n    multi: true\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxTimepicker]',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: TimepickerDirective,\n        multi: true\n      }],\n      host: {\n        '[disabled]': 'disabled',\n        '(change)': 'updateValue($event.target.value)',\n        '(blur)': 'onTouched()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, {\n    format: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    timepicker: [{\n      type: Input,\n      args: ['ngxTimepicker']\n    }],\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    disableClick: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass TimeFormatterPipe {\n  transform(time, timeUnit) {\n    if (time == null || time === '') {\n      return time;\n    }\n    switch (timeUnit) {\n      case TimeUnit.HOUR:\n        return DateTime.fromObject({\n          hour: +time\n        }).toFormat('HH');\n      case TimeUnit.MINUTE:\n        return DateTime.fromObject({\n          minute: +time\n        }).toFormat('mm');\n      default:\n        throw new Error('no such time unit');\n    }\n  }\n}\nTimeFormatterPipe.ɵfac = function TimeFormatterPipe_Factory(t) {\n  return new (t || TimeFormatterPipe)();\n};\nTimeFormatterPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"timeFormatter\",\n  type: TimeFormatterPipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimeFormatterPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'timeFormatter'\n    }]\n  }], null, null);\n})();\nclass NgxTimepickerTimeControlComponent {\n  constructor(timeParser) {\n    this.timeParser = timeParser;\n    this.timeChanged = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    if (changes.timeList && this.time != null) {\n      if (this.isSelectedTimeDisabled(this.time)) {\n        this.setAvailableTime();\n      }\n    }\n  }\n  changeTime(event) {\n    event.stopPropagation();\n    const char = String.fromCharCode(event.keyCode);\n    const time = concatTime(String(this.time), char);\n    this.changeTimeIfValid(time);\n  }\n  onKeydown(event) {\n    event.stopPropagation();\n    if (!isDigit(event)) {\n      event.preventDefault();\n    }\n    switch (event.key) {\n      case 'ArrowUp':\n        this.increase();\n        break;\n      case 'ArrowDown':\n        this.decrease();\n        break;\n    }\n    if (this.preventTyping && event.key !== 'Tab') {\n      event.preventDefault();\n    }\n  }\n  increase() {\n    if (!this.disabled) {\n      let nextTime = +this.time + (this.minutesGap || 1);\n      if (nextTime > this.max) {\n        nextTime = this.min;\n      }\n      if (this.isSelectedTimeDisabled(nextTime)) {\n        nextTime = this.getAvailableTime(nextTime, this.getNextAvailableTime.bind(this));\n      }\n      if (nextTime !== this.time) {\n        this.timeChanged.emit(nextTime);\n      }\n    }\n  }\n  decrease() {\n    if (!this.disabled) {\n      let previousTime = +this.time - (this.minutesGap || 1);\n      if (previousTime < this.min) {\n        previousTime = this.minutesGap ? this.max - (this.minutesGap - 1) : this.max;\n      }\n      if (this.isSelectedTimeDisabled(previousTime)) {\n        previousTime = this.getAvailableTime(previousTime, this.getPrevAvailableTime.bind(this));\n      }\n      if (previousTime !== this.time) {\n        this.timeChanged.emit(previousTime);\n      }\n    }\n  }\n  onFocus() {\n    this.isFocused = true;\n    this.previousTime = this.time;\n  }\n  onBlur() {\n    this.isFocused = false;\n    if (this.previousTime !== this.time) {\n      this.changeTimeIfValid(+this.time);\n    }\n  }\n  onModelChange(value) {\n    this.time = +this.timeParser.transform(value, this.timeUnit);\n  }\n  changeTimeIfValid(value) {\n    if (!isNaN(value)) {\n      this.time = value;\n      if (this.time > this.max) {\n        const timeString = String(value);\n        this.time = +timeString[timeString.length - 1];\n      }\n      if (this.time < this.min) {\n        this.time = this.min;\n      }\n      this.timeChanged.emit(this.time);\n    }\n  }\n  isSelectedTimeDisabled(time) {\n    return this.timeList.find(faceTime => faceTime.time === time).disabled;\n  }\n  getNextAvailableTime(index) {\n    const timeCollection = this.timeList;\n    const maxValue = timeCollection.length;\n    for (let i = index + 1; i < maxValue; i++) {\n      const time = timeCollection[i];\n      if (!time.disabled) {\n        return time.time;\n      }\n    }\n  }\n  getPrevAvailableTime(index) {\n    for (let i = index; i >= 0; i--) {\n      const time = this.timeList[i];\n      if (!time.disabled) {\n        return time.time;\n      }\n    }\n  }\n  getAvailableTime(currentTime, fn) {\n    const currentTimeIndex = this.timeList.findIndex(time => time.time === currentTime);\n    const availableTime = fn(currentTimeIndex);\n    return availableTime != null ? availableTime : this.time;\n  }\n  setAvailableTime() {\n    const availableTime = this.timeList.find(t => !t.disabled);\n    if (availableTime != null) {\n      this.time = availableTime.time;\n      this.timeChanged.emit(this.time);\n    }\n  }\n}\nNgxTimepickerTimeControlComponent.ɵfac = function NgxTimepickerTimeControlComponent_Factory(t) {\n  return new (t || NgxTimepickerTimeControlComponent)(i0.ɵɵdirectiveInject(TimeParserPipe));\n};\nNgxTimepickerTimeControlComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxTimepickerTimeControlComponent,\n  selectors: [[\"ngx-timepicker-time-control\"]],\n  inputs: {\n    time: \"time\",\n    min: \"min\",\n    max: \"max\",\n    placeholder: \"placeholder\",\n    timeUnit: \"timeUnit\",\n    disabled: \"disabled\",\n    timeList: \"timeList\",\n    preventTyping: \"preventTyping\",\n    minutesGap: \"minutesGap\"\n  },\n  outputs: {\n    timeChanged: \"timeChanged\"\n  },\n  features: [i0.ɵɵProvidersFeature([TimeParserPipe]), i0.ɵɵNgOnChangesFeature],\n  decls: 9,\n  vars: 13,\n  consts: [[1, \"ngx-timepicker-control\", 3, \"ngClass\"], [\"maxlength\", \"2\", 1, \"ngx-timepicker-control__input\", 3, \"ngModel\", \"placeholder\", \"disabled\", \"ngModelChange\", \"keydown\", \"keypress\", \"focus\", \"blur\"], [1, \"ngx-timepicker-control__arrows\"], [\"role\", \"button\", 1, \"ngx-timepicker-control__arrow\", 3, \"click\"]],\n  template: function NgxTimepickerTimeControlComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"input\", 1);\n      i0.ɵɵlistener(\"ngModelChange\", function NgxTimepickerTimeControlComponent_Template_input_ngModelChange_1_listener($event) {\n        return ctx.onModelChange($event);\n      })(\"keydown\", function NgxTimepickerTimeControlComponent_Template_input_keydown_1_listener($event) {\n        return ctx.onKeydown($event);\n      })(\"keypress\", function NgxTimepickerTimeControlComponent_Template_input_keypress_1_listener($event) {\n        return ctx.changeTime($event);\n      })(\"focus\", function NgxTimepickerTimeControlComponent_Template_input_focus_1_listener() {\n        return ctx.onFocus();\n      })(\"blur\", function NgxTimepickerTimeControlComponent_Template_input_blur_1_listener() {\n        return ctx.onBlur();\n      });\n      i0.ɵɵpipe(2, \"timeLocalizer\");\n      i0.ɵɵpipe(3, \"timeParser\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"div\", 2)(5, \"span\", 3);\n      i0.ɵɵlistener(\"click\", function NgxTimepickerTimeControlComponent_Template_span_click_5_listener() {\n        return ctx.increase();\n      });\n      i0.ɵɵtext(6, \" \\u25B2 \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"span\", 3);\n      i0.ɵɵlistener(\"click\", function NgxTimepickerTimeControlComponent_Template_span_click_7_listener() {\n        return ctx.decrease();\n      });\n      i0.ɵɵtext(8, \" \\u25BC \");\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c13, ctx.isFocused));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngModel\", i0.ɵɵpipeBind3(2, 4, i0.ɵɵpipeBind2(3, 8, ctx.time, ctx.timeUnit), ctx.timeUnit, true))(\"placeholder\", ctx.placeholder)(\"disabled\", ctx.disabled);\n    }\n  },\n  dependencies: [i1.NgClass, i4.DefaultValueAccessor, i4.MaxLengthValidator, i4.NgControlStatus, i4.NgModel, TimeLocalizerPipe, TimeParserPipe],\n  styles: [\".ngx-timepicker-control[_ngcontent-%COMP%]{position:relative;display:flex;width:60px;height:30px;padding:0 5px;box-sizing:border-box}.ngx-timepicker-control--active[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:100%;height:1px;background-color:#00bfff}.ngx-timepicker-control__input[_ngcontent-%COMP%]{width:100%;height:100%;padding:0 5px 0 0;border:0;font-size:1rem;color:inherit;outline:none;text-align:center}.ngx-timepicker-control__input[_ngcontent-%COMP%]:disabled{background-color:transparent}.ngx-timepicker-control__arrows[_ngcontent-%COMP%]{position:absolute;right:2px;top:0;display:flex;flex-direction:column}.ngx-timepicker-control__arrow[_ngcontent-%COMP%]{font-size:11px;color:#0006;cursor:pointer;transition:color .2s;-webkit-user-select:none;-moz-user-select:none;user-select:none}.ngx-timepicker-control__arrow[_ngcontent-%COMP%]:hover{color:#000000e6}\"],\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxTimepickerTimeControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-timepicker-time-control',\n      templateUrl: './ngx-timepicker-time-control.component.html',\n      styleUrls: ['./ngx-timepicker-time-control.component.scss'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [TimeParserPipe]\n    }]\n  }], function () {\n    return [{\n      type: TimeParserPipe\n    }];\n  }, {\n    time: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    timeUnit: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    timeList: [{\n      type: Input\n    }],\n    preventTyping: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    timeChanged: [{\n      type: Output\n    }]\n  });\n})();\nfunction concatTime(currentTime, nextTime) {\n  const isNumber = /\\d/.test(nextTime);\n  if (isNumber) {\n    const time = currentTime + nextTime;\n    return +time;\n  }\n}\nclass NgxTimepickerPeriodSelectorComponent {\n  constructor(locale) {\n    this.locale = locale;\n    this.periodSelected = new EventEmitter();\n    this.period = TimePeriod;\n    this.meridiems = Info.meridiems({\n      locale: this.locale\n    });\n  }\n  set selectedPeriod(period) {\n    if (period) {\n      const periods = [TimePeriod.AM, TimePeriod.PM];\n      this.localizedPeriod = this.meridiems[periods.indexOf(period)];\n    }\n  }\n  open() {\n    if (!this.disabled) {\n      this.isOpened = true;\n    }\n  }\n  select(period) {\n    this.periodSelected.next(period);\n    this.isOpened = false;\n  }\n  backdropClick() {\n    this.isOpened = false;\n  }\n}\nNgxTimepickerPeriodSelectorComponent.ɵfac = function NgxTimepickerPeriodSelectorComponent_Factory(t) {\n  return new (t || NgxTimepickerPeriodSelectorComponent)(i0.ɵɵdirectiveInject(TIME_LOCALE));\n};\nNgxTimepickerPeriodSelectorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxTimepickerPeriodSelectorComponent,\n  selectors: [[\"ngx-timepicker-period-selector\"]],\n  inputs: {\n    isOpened: \"isOpened\",\n    disabled: \"disabled\",\n    selectedPeriod: \"selectedPeriod\"\n  },\n  outputs: {\n    periodSelected: \"periodSelected\"\n  },\n  decls: 9,\n  vars: 6,\n  consts: [[1, \"period\"], [1, \"period-control\"], [\"type\", \"button\", 1, \"period-control__button\", \"period__btn--default\", 3, \"ngClass\", \"click\"], [1, \"period-control__arrow\"], [\"class\", \"period-selector\", 3, \"timepickerAutofocus\", 4, \"ngIf\"], [\"class\", \"overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"period-selector\", 3, \"timepickerAutofocus\"], [\"type\", \"button\", 1, \"period-selector__button\", \"period__btn--default\", 3, \"ngClass\", \"click\"], [1, \"overlay\", 3, \"click\"]],\n  template: function NgxTimepickerPeriodSelectorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n      i0.ɵɵlistener(\"click\", function NgxTimepickerPeriodSelectorComponent_Template_button_click_2_listener() {\n        return ctx.open();\n      });\n      i0.ɵɵelementStart(3, \"span\");\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"span\", 3);\n      i0.ɵɵtext(6, \"\\u25BC\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(7, NgxTimepickerPeriodSelectorComponent_ul_7_Template, 7, 10, \"ul\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, NgxTimepickerPeriodSelectorComponent_div_8_Template, 1, 0, \"div\", 5);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c15, ctx.disabled));\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.localizedPeriod);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.isOpened);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isOpened);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, AutofocusDirective],\n  styles: [\".period[_ngcontent-%COMP%]{position:relative}.period__btn--default[_ngcontent-%COMP%]{padding:0;border:none;background-color:transparent;cursor:pointer;text-align:left;-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-tap-highlight-color:transparent;outline:none}.period-control[_ngcontent-%COMP%]{position:relative}.period-control__button[_ngcontent-%COMP%]{position:relative;width:60px;font-size:1rem;color:inherit;text-align:center}.period-control__button[_ngcontent-%COMP%]:not(.period-control__button--disabled):focus:after{content:\\\"\\\";position:absolute;bottom:-8px;left:0;width:100%;height:1px;background-color:#00bfff}.period-control__arrow[_ngcontent-%COMP%]{margin-left:10px;font-size:12px;color:#0006}.period-selector[_ngcontent-%COMP%]{position:absolute;top:calc(50% - 50px);right:calc(-50% + -50px);max-width:135px;width:150px;padding:6px 0;margin:0;list-style:none;background-color:#f5f5f5;box-shadow:0 1px 3px #0003,0 1px 1px #00000024,0 2px 1px -1px #0000001f;z-index:201}.period-selector__button[_ngcontent-%COMP%]{width:100%;height:48px;padding:0 16px;line-height:48px}.period-selector__button--active[_ngcontent-%COMP%]{color:#00bfff}.period-selector__button[_ngcontent-%COMP%]:focus{background-color:#eee}.overlay[_ngcontent-%COMP%]{position:fixed;width:100%;height:100%;top:0;left:0;background-color:transparent;z-index:200}\"],\n  data: {\n    animation: [trigger('scaleInOut', [transition(':enter', [style({\n      transform: 'scale(0)',\n      opacity: 0\n    }), animate(200, style({\n      transform: 'scale(1)',\n      opacity: 1\n    }))]), transition(':leave', [animate(200, style({\n      transform: 'scale(0)',\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxTimepickerPeriodSelectorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-timepicker-period-selector',\n      templateUrl: 'ngx-timepicker-period-selector.component.html',\n      styleUrls: ['./ngx-timepicker-period-selector.component.scss'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [trigger('scaleInOut', [transition(':enter', [style({\n        transform: 'scale(0)',\n        opacity: 0\n      }), animate(200, style({\n        transform: 'scale(1)',\n        opacity: 1\n      }))]), transition(':leave', [animate(200, style({\n        transform: 'scale(0)',\n        opacity: 0\n      }))])])]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, {\n    isOpened: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    selectedPeriod: [{\n      type: Input\n    }],\n    periodSelected: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxTimepickerFieldComponent {\n  constructor(timepickerService, locale) {\n    this.timepickerService = timepickerService;\n    this.locale = locale;\n    this.minHour = 1;\n    this.maxHour = 12;\n    this.timeUnit = TimeUnit;\n    this.buttonAlign = 'right';\n    this.timeChanged = new EventEmitter();\n    this._format = 12;\n    this.unsubscribe$ = new Subject();\n    this.isFirstTimeChange = true;\n    this.onChange = () => {};\n  }\n  set format(value) {\n    this._format = value === 24 ? 24 : 12;\n    this.minHour = this._format === 12 ? 1 : 0;\n    this.maxHour = this._format === 12 ? 12 : 23;\n    this.hoursList = TimepickerTimeUtils.getHours(this._format);\n    const isDynamicallyChanged = value && this.previousFormat && this.previousFormat !== this._format;\n    if (isDynamicallyChanged) {\n      this.updateTime(this.timepickerTime);\n    }\n    this.previousFormat = this._format;\n  }\n  get format() {\n    return this._format;\n  }\n  set min(value) {\n    if (typeof value === 'string') {\n      this._min = TimeAdapter.parseTime(value, {\n        locale: this.locale,\n        format: this.format\n      });\n      return;\n    }\n    this._min = value;\n  }\n  get min() {\n    return this._min;\n  }\n  set max(value) {\n    if (typeof value === 'string') {\n      this._max = TimeAdapter.parseTime(value, {\n        locale: this.locale,\n        format: this.format\n      });\n      return;\n    }\n    this._max = value;\n  }\n  get max() {\n    return this._max;\n  }\n  set defaultTime(val) {\n    this._defaultTime = val;\n    this.isDefaultTime = !!val;\n  }\n  get defaultTime() {\n    return this._defaultTime;\n  }\n  set minutesGap(gap) {\n    if (gap == null) {\n      return;\n    }\n    gap = Math.floor(gap);\n    this._minutesGap = gap <= 59 ? gap : 1;\n  }\n  get minutesGap() {\n    return this._minutesGap;\n  }\n  ngOnInit() {\n    this.initTime(this.defaultTime);\n    this.hoursList = TimepickerTimeUtils.getHours(this._format);\n    this.minutesList = TimepickerTimeUtils.getMinutes();\n    this.isTimeRangeSet = !!(this.min || this.max);\n    this.hour$ = this.timepickerService.selectedHour.pipe(tap(clockTime => this.selectedHour = clockTime.time), map(this.changeDefaultTimeValue.bind(this)), tap(() => this.isTimeRangeSet && this.updateAvailableMinutes()));\n    this.minute$ = this.timepickerService.selectedMinute.pipe(map(this.changeDefaultTimeValue.bind(this)), tap(() => this.isFirstTimeChange = false));\n    if (this.format === 12) {\n      this.timepickerService.selectedPeriod.pipe(distinctUntilChanged(), tap(period => this.period = period), tap(period => this.isChangePeriodDisabled = this.isPeriodDisabled(period)), takeUntil(this.unsubscribe$)).subscribe(() => this.isTimeRangeSet && this.updateAvailableTime());\n    } else if (this.isTimeRangeSet) {\n      this.updateAvailableTime();\n    }\n  }\n  writeValue(val) {\n    if (val) {\n      this.initTime(val);\n    } else {\n      this.resetTime();\n    }\n  }\n  registerOnTouched(fn) {}\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  changeHour(hour) {\n    this.timepickerService.hour = this.hoursList.find(h => h.time === hour);\n    this.changeTime();\n  }\n  changeMinute(minute) {\n    this.timepickerService.minute = this.minutesList.find(m => m.time === minute);\n    this.changeTime();\n  }\n  changePeriod(period) {\n    this.timepickerService.period = period;\n    this.changeTime();\n  }\n  onTimeSet(time) {\n    this.updateTime(time);\n    this.emitLocalTimeChange(time);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  changeTime() {\n    const time = this.timepickerService.getFullTime(this.format);\n    this.timepickerTime = time;\n    this.emitLocalTimeChange(time);\n  }\n  resetTime() {\n    this.timepickerService.hour = {\n      angle: 0,\n      time: null\n    };\n    this.timepickerService.minute = {\n      angle: 0,\n      time: null\n    };\n  }\n  emitLocalTimeChange(time) {\n    const localTime = TimeAdapter.toLocaleTimeString(time, {\n      format: this.format,\n      locale: this.locale\n    });\n    this.onChange(localTime);\n    this.timeChanged.emit(localTime);\n  }\n  changeDefaultTimeValue(clockFaceTime) {\n    if (!this.isDefaultTime && this.isFirstTimeChange) {\n      return Object.assign(Object.assign({}, clockFaceTime), {\n        time: null\n      });\n    }\n    return clockFaceTime;\n  }\n  updateAvailableHours() {\n    this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n      min: this.min,\n      max: this.max,\n      format: this.format,\n      period: this.period\n    });\n  }\n  updateAvailableMinutes() {\n    this.minutesList = TimepickerTimeUtils.disableMinutes(this.minutesList, this.selectedHour, {\n      min: this.min,\n      max: this.max,\n      format: this.format,\n      period: this.period\n    });\n  }\n  updateAvailableTime() {\n    this.updateAvailableHours();\n    if (this.selectedHour) {\n      this.updateAvailableMinutes();\n    }\n  }\n  updateTime(time) {\n    if (time) {\n      const formattedTime = TimeAdapter.formatTime(time, {\n        locale: this.locale,\n        format: this.format\n      });\n      this.timepickerService.setDefaultTimeIfAvailable(formattedTime, this.min, this.max, this.format);\n      this.timepickerTime = formattedTime;\n    }\n  }\n  initTime(time) {\n    const isDefaultTimeAvailable = TimeAdapter.isTimeAvailable(time, this.min, this.max, 'minutes', null, this.format);\n    if (!isDefaultTimeAvailable) {\n      if (this.min) {\n        this.updateTime(TimeAdapter.fromDateTimeToString(this.min, this.format));\n        return;\n      }\n      if (this.max) {\n        this.updateTime(TimeAdapter.fromDateTimeToString(this.max, this.format));\n        return;\n      }\n    }\n    this.updateTime(time);\n  }\n  isPeriodDisabled(period) {\n    return TimepickerTimeUtils.disableHours(TimepickerTimeUtils.getHours(12), {\n      min: this.min,\n      max: this.max,\n      format: 12,\n      period: period === TimePeriod.AM ? TimePeriod.PM : TimePeriod.AM\n    }).every(time => time.disabled);\n  }\n}\nNgxTimepickerFieldComponent.ɵfac = function NgxTimepickerFieldComponent_Factory(t) {\n  return new (t || NgxTimepickerFieldComponent)(i0.ɵɵdirectiveInject(NgxMaterialTimepickerService), i0.ɵɵdirectiveInject(TIME_LOCALE));\n};\nNgxTimepickerFieldComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxTimepickerFieldComponent,\n  selectors: [[\"ngx-timepicker-field\"]],\n  inputs: {\n    disabled: \"disabled\",\n    toggleIcon: \"toggleIcon\",\n    buttonAlign: \"buttonAlign\",\n    clockTheme: \"clockTheme\",\n    controlOnly: \"controlOnly\",\n    cancelBtnTmpl: \"cancelBtnTmpl\",\n    confirmBtnTmpl: \"confirmBtnTmpl\",\n    format: \"format\",\n    min: \"min\",\n    max: \"max\",\n    defaultTime: \"defaultTime\",\n    minutesGap: \"minutesGap\"\n  },\n  outputs: {\n    timeChanged: \"timeChanged\"\n  },\n  features: [i0.ɵɵProvidersFeature([NgxMaterialTimepickerService, {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: NgxTimepickerFieldComponent,\n    multi: true\n  }])],\n  decls: 13,\n  vars: 34,\n  consts: [[1, \"ngx-timepicker\", 3, \"ngClass\"], [1, \"ngx-timepicker__control--first\", 3, \"placeholder\", \"time\", \"min\", \"max\", \"timeUnit\", \"disabled\", \"timeList\", \"preventTyping\", \"timeChanged\"], [1, \"ngx-timepicker__time-colon\", \"ngx-timepicker__control--second\"], [1, \"ngx-timepicker__control--third\", 3, \"placeholder\", \"time\", \"min\", \"max\", \"timeUnit\", \"disabled\", \"timeList\", \"preventTyping\", \"minutesGap\", \"timeChanged\"], [\"class\", \"ngx-timepicker__control--forth\", 3, \"selectedPeriod\", \"disabled\", \"periodSelected\", 4, \"ngIf\"], [\"class\", \"ngx-timepicker__toggle\", 3, \"ngClass\", \"for\", \"disabled\", 4, \"ngIf\"], [3, \"min\", \"max\", \"theme\", \"defaultTime\", \"format\", \"cancelBtnTmpl\", \"confirmBtnTmpl\", \"minutesGap\", \"timeSet\"], [\"timepicker\", \"\"], [\"defaultIcon\", \"\"], [1, \"ngx-timepicker__control--forth\", 3, \"selectedPeriod\", \"disabled\", \"periodSelected\"], [1, \"ngx-timepicker__toggle\", 3, \"ngClass\", \"for\", \"disabled\"], [\"ngxMaterialTimepickerToggleIcon\", \"\"], [4, \"ngTemplateOutlet\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\"], [\"d\", \"M 12 2 C 6.4889971 2 2 6.4889971 2 12 C 2 17.511003                   6.4889971 22 12 22 C 17.511003 22 22 17.511003 22 12 C 22 6.4889971 17.511003 2 12 2 z M 12 4 C 16.430123 4 20 7.5698774 20 12 C 20 16.430123 16.430123 20 12 20 C 7.5698774 20 4 16.430123 4 12 C 4 7.5698774 7.5698774 4 12 4 z M 11 6 L 11 12.414062 L 15.292969 16.707031 L 16.707031 15.292969 L 13 11.585938 L 13 6 L 11 6 z\"]],\n  template: function NgxTimepickerFieldComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"ngx-timepicker-time-control\", 1);\n      i0.ɵɵlistener(\"timeChanged\", function NgxTimepickerFieldComponent_Template_ngx_timepicker_time_control_timeChanged_1_listener($event) {\n        return ctx.changeHour($event);\n      });\n      i0.ɵɵpipe(2, \"async\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"span\", 2);\n      i0.ɵɵtext(4, \":\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"ngx-timepicker-time-control\", 3);\n      i0.ɵɵlistener(\"timeChanged\", function NgxTimepickerFieldComponent_Template_ngx_timepicker_time_control_timeChanged_5_listener($event) {\n        return ctx.changeMinute($event);\n      });\n      i0.ɵɵpipe(6, \"async\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(7, NgxTimepickerFieldComponent_ngx_timepicker_period_selector_7_Template, 1, 2, \"ngx-timepicker-period-selector\", 4);\n      i0.ɵɵtemplate(8, NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_Template, 3, 6, \"ngx-material-timepicker-toggle\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"ngx-material-timepicker\", 6, 7);\n      i0.ɵɵlistener(\"timeSet\", function NgxTimepickerFieldComponent_Template_ngx_material_timepicker_timeSet_9_listener($event) {\n        return ctx.onTimeSet($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, NgxTimepickerFieldComponent_ng_template_11_Template, 2, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      let tmp_2_0;\n      let tmp_10_0;\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c17, ctx.disabled));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"placeholder\", \"HH\")(\"time\", (tmp_2_0 = i0.ɵɵpipeBind1(2, 28, ctx.hour$)) == null ? null : tmp_2_0.time)(\"min\", ctx.minHour)(\"max\", ctx.maxHour)(\"timeUnit\", ctx.timeUnit.HOUR)(\"disabled\", ctx.disabled)(\"timeList\", ctx.hoursList)(\"preventTyping\", ctx.isTimeRangeSet);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"placeholder\", \"MM\")(\"time\", (tmp_10_0 = i0.ɵɵpipeBind1(6, 30, ctx.minute$)) == null ? null : tmp_10_0.time)(\"min\", 0)(\"max\", 59)(\"timeUnit\", ctx.timeUnit.MINUTE)(\"disabled\", ctx.disabled)(\"timeList\", ctx.minutesList)(\"preventTyping\", ctx.isTimeRangeSet)(\"minutesGap\", ctx.minutesGap);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.format !== 24);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.controlOnly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"min\", ctx.min)(\"max\", ctx.max)(\"theme\", ctx.clockTheme)(\"defaultTime\", ctx.timepickerTime)(\"format\", ctx.format)(\"cancelBtnTmpl\", ctx.cancelBtnTmpl)(\"confirmBtnTmpl\", ctx.confirmBtnTmpl)(\"minutesGap\", ctx.minutesGap);\n    }\n  },\n  dependencies: [NgxTimepickerTimeControlComponent, NgxTimepickerPeriodSelectorComponent, NgxMaterialTimepickerToggleComponent, NgxMaterialTimepickerComponent, i1.NgClass, i1.NgIf, NgxMaterialTimepickerToggleIconDirective, i1.NgTemplateOutlet, i1.AsyncPipe],\n  styles: [\".ngx-timepicker[_ngcontent-%COMP%]{display:flex;align-items:center;height:100%;border-bottom:1px solid rgba(0,0,0,.12)}.ngx-timepicker--disabled[_ngcontent-%COMP%]{background:rgba(0,0,0,.07);pointer-events:none}.ngx-timepicker__time-colon[_ngcontent-%COMP%]{margin-left:10px}.ngx-timepicker__control--first[_ngcontent-%COMP%]{order:1}.ngx-timepicker__control--second[_ngcontent-%COMP%]{order:2}.ngx-timepicker__control--third[_ngcontent-%COMP%]{order:3}.ngx-timepicker__control--forth[_ngcontent-%COMP%]{order:4}.ngx-timepicker__toggle[_ngcontent-%COMP%]{order:4}.ngx-timepicker__toggle--left[_ngcontent-%COMP%]{order:0}\"],\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxTimepickerFieldComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-timepicker-field',\n      templateUrl: './ngx-timepicker-field.component.html',\n      styleUrls: ['./ngx-timepicker-field.component.scss'],\n      providers: [NgxMaterialTimepickerService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: NgxTimepickerFieldComponent,\n        multi: true\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: NgxMaterialTimepickerService\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TIME_LOCALE]\n      }]\n    }];\n  }, {\n    disabled: [{\n      type: Input\n    }],\n    toggleIcon: [{\n      type: Input\n    }],\n    buttonAlign: [{\n      type: Input\n    }],\n    clockTheme: [{\n      type: Input\n    }],\n    controlOnly: [{\n      type: Input\n    }],\n    cancelBtnTmpl: [{\n      type: Input\n    }],\n    confirmBtnTmpl: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    defaultTime: [{\n      type: Input\n    }],\n    minutesGap: [{\n      type: Input\n    }],\n    timeChanged: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxMaterialTimepickerModule {\n  // tslint:disable-next-line:max-line-length\n  static setOpts(locale, numberingSystem = TimeAdapter.DEFAULT_NUMBERING_SYSTEM) {\n    return {\n      ngModule: NgxMaterialTimepickerModule,\n      providers: [{\n        provide: TIME_LOCALE,\n        useValue: locale\n      }, {\n        provide: NUMBERING_SYSTEM,\n        useValue: numberingSystem\n      }]\n    };\n  }\n}\nNgxMaterialTimepickerModule.ɵfac = function NgxMaterialTimepickerModule_Factory(t) {\n  return new (t || NgxMaterialTimepickerModule)();\n};\nNgxMaterialTimepickerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxMaterialTimepickerModule\n});\nNgxMaterialTimepickerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, FormsModule, ReactiveFormsModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaterialTimepickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, FormsModule, ReactiveFormsModule],\n      exports: [NgxMaterialTimepickerComponent, NgxMaterialTimepickerToggleComponent, NgxTimepickerFieldComponent, TimepickerDirective, NgxMaterialTimepickerToggleIconDirective, NgxMaterialTimepickerThemeDirective],\n      declarations: [NgxMaterialTimepickerComponent, NgxMaterialTimepicker24HoursFaceComponent, NgxMaterialTimepicker12HoursFaceComponent, NgxMaterialTimepickerMinutesFaceComponent, NgxMaterialTimepickerFaceComponent, NgxMaterialTimepickerToggleComponent, NgxMaterialTimepickerButtonComponent, NgxMaterialTimepickerDialComponent, NgxMaterialTimepickerDialControlComponent, NgxMaterialTimepickerPeriodComponent, TimeFormatterPipe, TimepickerDirective, OverlayDirective, NgxMaterialTimepickerToggleIconDirective, AutofocusDirective, MinutesFormatterPipe, NgxMaterialTimepickerThemeDirective, NgxTimepickerFieldComponent, NgxTimepickerTimeControlComponent, NgxTimepickerPeriodSelectorComponent, TimeLocalizerPipe, TimeParserPipe, ActiveHourPipe, ActiveMinutePipe, NgxMaterialTimepickerContainerComponent, NgxMaterialTimepickerContentComponent, AppendToInputDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NUMBERING_SYSTEM, NgxMaterialTimepickerComponent, NgxMaterialTimepickerModule, NgxMaterialTimepickerThemeDirective, NgxMaterialTimepickerToggleComponent, NgxMaterialTimepickerToggleIconDirective, NgxTimepickerFieldComponent, TIME_LOCALE, TimepickerDirective };", "map": {"version": 3, "names": ["i0", "Injectable", "InjectionToken", "Directive", "Input", "HostListener", "Component", "<PERSON><PERSON>", "Inject", "Optional", "EventEmitter", "ViewChild", "Output", "ChangeDetectionStrategy", "ContentChild", "NgModule", "i1", "DOCUMENT", "CommonModule", "BehaviorSubject", "Subject", "merge", "shareReplay", "tap", "debounceTime", "distinctUntilChanged", "filter", "takeUntil", "map", "trigger", "transition", "style", "animate", "sequence", "DateTime", "Info", "i4", "FormControl", "NG_VALUE_ACCESSOR", "FormsModule", "ReactiveFormsModule", "NgxMaterialTimepickerContentComponent_div_0_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "NgxMaterialTimepickerContentComponent_div_0_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "_r3", "ɵɵreference", "ɵɵproperty", "inputElement", "ɵɵadvance", "NgxMaterialTimepickerContentComponent_ng_template_1_ng_container_0_Template", "NgxMaterialTimepickerContentComponent_ng_template_1_Template", "NgxMaterialTimepickerContentComponent_ng_template_3_Template", "ɵɵprojection", "_c0", "_c1", "_c2", "a0", "NgxMaterialTimepickerDialControlComponent_input_0_Template", "_r4", "ɵɵgetCurrentView", "ɵɵlistener", "NgxMaterialTimepickerDialControlComponent_input_0_Template_input_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "ctx_r3", "ɵɵresetView", "time", "NgxMaterialTimepickerDialControlComponent_input_0_Template_input_input_0_listener", "ctx_r5", "updateTime", "NgxMaterialTimepickerDialControlComponent_input_0_Template_input_focus_0_listener", "ctx_r6", "saveTimeAndChangeTimeUnit", "timeUnit", "ɵɵpipe", "ɵɵpureFunction1", "isActive", "ɵɵpipeBind2", "disabled", "NgxMaterialTimepickerDialControlComponent_ng_template_1_Template", "_r9", "NgxMaterialTimepickerDialControlComponent_ng_template_1_Template_input_focus_0_listener", "ctx_r8", "NgxMaterialTimepickerDialControlComponent_ng_template_1_Template_input_keydown_0_listener", "ctx_r10", "onKeydown", "ctx_r2", "timeControl", "NgxMaterialTimepickerPeriodComponent_div_5_Template", "_r2", "NgxMaterialTimepickerPeriodComponent_div_5_Template_div_animation_scaleInOut_done_0_listener", "ctx_r1", "animationDone", "ɵɵtext", "undefined", "NgxMaterialTimepickerDialComponent_div_8_ng_container_1_Template", "NgxMaterialTimepickerDialComponent_div_8_ng_template_2_Template", "_c3", "NgxMaterialTimepickerDialComponent_div_8_Template", "ɵɵtemplateRefExtractor", "isHintVisible", "editableHintTmpl", "_c4", "_c5", "_c6", "_c7", "_c8", "a1", "NgxMaterialTimepickerFaceComponent_div_2_div_1_Template", "time_r7", "$implicit", "angle", "ɵɵpureFunction2", "ɵɵpipeBind3", "selectedTime", "isClockFaceDisabled", "ɵɵtextInterpolate1", "HOUR", "NgxMaterialTimepickerFaceComponent_div_2_div_3_div_1_Template", "time_r9", "ɵɵstyleProp", "innerClockFaceSize", "NgxMaterialTimepickerFaceComponent_div_2_div_3_Template", "faceTime", "trackByTime", "NgxMaterialTimepickerFaceComponent_div_2_Template", "length", "NgxMaterialTimepickerFaceComponent_ng_template_5_div_1_Template", "time_r11", "ɵɵpipeBind4", "minutesGap", "MINUTE", "NgxMaterialTimepickerFaceComponent_ng_template_5_Template", "ctx_r4", "_c9", "NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template", "_r12", "NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template_ngx_material_timepicker_24_hours_face_hourChange_0_listener", "ctx_r11", "onHourChange", "NgxMaterialTimepickerContainerComponent_div_11_ngx_material_timepicker_24_hours_face_1_Template_ngx_material_timepicker_24_hours_face_hourSelected_0_listener", "ctx_r13", "onHourSelected", "ɵɵpipeBind1", "selected<PERSON>our", "minTime", "maxTime", "format", "NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template", "_r15", "NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template_ngx_material_timepicker_12_hours_face_hourChange_0_listener", "ctx_r14", "NgxMaterialTimepickerContainerComponent_div_11_ng_template_2_Template_ngx_material_timepicker_12_hours_face_hourSelected_0_listener", "ctx_r16", "<PERSON><PERSON><PERSON><PERSON>", "NgxMaterialTimepickerContainerComponent_div_11_Template", "NgxMaterialTimepickerContainerComponent_ngx_material_timepicker_minutes_face_12_Template", "_r18", "NgxMaterialTimepickerContainerComponent_ngx_material_timepicker_minutes_face_12_Template_ngx_material_timepicker_minutes_face_minuteChange_0_listener", "ctx_r17", "onMinuteChange", "tmp_1_0", "selected<PERSON><PERSON><PERSON>", "NgxMaterialTimepickerContainerComponent_ng_container_15_Template", "NgxMaterialTimepickerContainerComponent_ng_container_17_Template", "NgxMaterialTimepickerContainerComponent_ng_template_18_Template", "NgxMaterialTimepickerContainerComponent_ng_template_20_Template", "_c10", "NgxMaterialTimepickerToggleComponent__svg_svg_1_Template", "ɵɵnamespaceSVG", "ɵɵelement", "_c11", "_c12", "_c13", "_c14", "NgxTimepickerPeriodSelectorComponent_ul_7_Template", "NgxTimepickerPeriodSelectorComponent_ul_7_Template_button_click_2_listener", "select", "period", "AM", "NgxTimepickerPeriodSelectorComponent_ul_7_Template_button_click_5_listener", "PM", "localizedPeriod", "meridiems", "ɵɵtextInterpolate", "NgxTimepickerPeriodSelectorComponent_div_8_Template", "_r6", "NgxTimepickerPeriodSelectorComponent_div_8_Template_div_click_0_listener", "backdropClick", "_c15", "NgxTimepickerFieldComponent_ngx_timepicker_period_selector_7_Template", "NgxTimepickerFieldComponent_ngx_timepicker_period_selector_7_Template_ngx_timepicker_period_selector_periodSelected_0_listener", "changePeriod", "isChangePeriodDisabled", "NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_ng_container_2_Template", "_c16", "NgxTimepickerFieldComponent_ngx_material_timepicker_toggle_8_Template", "buttonAlign", "toggleIcon", "NgxTimepickerFieldComponent_ng_template_11_Template", "_c17", "TimeUnit", "TimePeriod", "TimeFormat", "isSameOrAfter", "compareWith", "unit", "hour", "<PERSON><PERSON><PERSON>", "valueOf", "isSameOrBefore", "isBetween", "before", "after", "isDigit", "e", "some", "n", "keyCode", "ctrl<PERSON>ey", "metaKey", "TimeAdapter", "parseTime", "opts", "numberingSystem", "locale", "getLocaleOptionsByTime", "isPeriodExist", "split", "timeMask", "TWELVE_SHORT", "TWENTY_FOUR_SHORT", "fromFormat", "formatTime", "parsedTime", "setLocale", "DEFAULT_LOCALE", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Object", "assign", "TIME_SIMPLE", "hour12", "DEFAULT_NUMBERING_SYSTEM", "replace", "toISOTime", "includeOffset", "suppressMilliseconds", "suppressSeconds", "toLocaleTimeString", "DEFAULT_FORMAT", "hourCycle", "timeFormat", "localOpts", "isTimeAvailable", "min", "max", "granularity", "convertedTime", "minutes", "minute", "Error", "isAfter", "isBefore", "between", "isAvailable", "formatHour", "currentHour", "fromDateTimeToString", "TWENTY_FOUR", "TWELVE", "reconfigure", "toFormat", "localeConfig", "defaultConfig", "isNaN", "parseInt", "DEFAULT_HOUR", "DEFAULT_MINUTE", "NgxMaterialTimepickerService", "constructor", "hourSubject", "minuteSubject", "periodSubject", "next", "asObservable", "isPeriodValid", "setDefaultTimeIfAvailable", "setDefaultTime", "console", "error", "getFullTime", "getValue", "trim", "defaultTime", "toJSDate", "fromJSDate", "substr", "toUpperCase", "getHours", "formatHourByPeriod", "getMinutes", "resetTime", "ɵfac", "NgxMaterialTimepickerService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "TIME_LOCALE", "NUMBERING_SYSTEM", "NgxMaterialTimepickerEventService", "backdropClickSubject", "keydownEventSubject", "pipe", "bufferSize", "refCount", "keydownEvent", "dispatchEvent", "event", "NgxMaterialTimepickerEventService_Factory", "AppendToInputDirective", "elementRef", "renderer", "element", "nativeElement", "inputCords", "getBoundingClientRect", "direction", "height", "offsetHeight", "bottom", "top", "_inputCords", "isElementFit", "window", "innerHeight", "isTop", "isCenter", "ngAfterViewInit", "_direction", "append", "changePosition", "y", "defineElementYByDirection", "setStyle", "left", "value", "inputTop", "inputBottom", "AppendToInputDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ɵdir", "ɵɵdefineDirective", "selectors", "hostBindings", "AppendToInputDirective_HostBindings", "AppendToInputDirective_scroll_HostBindingHandler", "ɵɵresolveWindow", "inputs", "selector", "NgxMaterialTimepickerContentComponent", "NgxMaterialTimepickerContentComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "appendToInput", "ngContentSelectors", "decls", "vars", "consts", "template", "NgxMaterialTimepickerContentComponent_Template", "ɵɵprojectionDef", "_r1", "dependencies", "NgIf", "NgTemplateOutlet", "encapsulation", "templateUrl", "TimepickerTimeUtils", "Array", "fill", "v", "i", "angleStep", "disableHours", "hours", "config", "currentTime", "fromObject", "gap", "minutesCount", "push", "disableMinutes", "TimeLocalizerPipe", "transform", "isKeyboardEnabled", "timeMeasure", "_a", "TimeLocalizerPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "decorators", "TimeParserPipe", "String", "TimeParserPipe_Factory", "AutofocusDirective", "document", "activeElement", "ngOnChanges", "isFocusActive", "setTimeout", "focus", "preventScroll", "ngOnDestroy", "AutofocusDirective_Factory", "features", "ɵɵNgOnChangesFeature", "NgxMaterialTimepickerDialControlComponent", "timeParserPipe", "timeLocalizerPipe", "timeUnitChanged", "timeChanged", "focused", "unfocused", "timeList", "find", "ngOnInit", "isEditable", "formatTimeForUI", "valueChanges", "updateInputValue", "slice", "isTimeDisabledToChange", "toString", "subscribe", "preventDefault", "previousTime", "changeTimeByArrow", "ARROW_UP", "ARROW_DOWN", "isTimeUnavailable", "editableTimeTmpl", "NgxMaterialTimepickerDialControlComponent_Factory", "viewQuery", "NgxMaterialTimepickerDialControlComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "outputs", "ɵɵProvidersFeature", "NgxMaterialTimepickerDialControlComponent_Template", "DefaultValueAccessor", "Ng<PERSON><PERSON>", "NgControlStatus", "NgModel", "FormControlDirective", "styles", "styleUrls", "providers", "nextTime", "isNumber", "test", "NgxMaterialTimepickerPeriodComponent", "timePeriod", "isPeriodAvailable", "periodChanged", "isSwitchPeriodAvailable", "getDisabledTimeByPeriod", "every", "activeTimeUnit", "NgxMaterialTimepickerPeriodComponent_Factory", "NgxMaterialTimepickerPeriodComponent_Template", "NgxMaterialTimepickerPeriodComponent_Template_button_click_1_listener", "NgxMaterialTimepickerPeriodComponent_Template_button_click_3_listener", "data", "animation", "opacity", "animations", "NgxMaterialTimepickerDialComponent", "hourChanged", "minute<PERSON><PERSON>ed", "changes", "currentValue", "changeTimeUnit", "changeHour", "changeMinute", "showHint", "hideHint", "NgxMaterialTimepickerDialComponent_Factory", "hoursOnly", "NgxMaterialTimepickerDialComponent_Template", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeUnitChanged_3_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeChanged_3_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_focused_3_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_unfocused_3_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeUnitChanged_6_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_timeChanged_6_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_focused_6_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_dial_control_unfocused_6_listener", "NgxMaterialTimepickerDialComponent_Template_ngx_material_timepicker_period_periodChanged_7_listener", "changeDetection", "OnPush", "NgxMaterialTimepickerHoursFace", "hourChange", "hourSelected", "hoursList", "onTimeSelected", "NgxMaterialTimepickerHoursFace_Factory", "ɵɵinvalidFactory", "ActiveHourPipe", "ActiveHourPipe_Factory", "ActiveMinutePipe", "currentMinute", "defaultGap", "ActiveMinutePipe_Factory", "MinutesFormatterPipe", "MinutesFormatterPipe_Factory", "CLOCK_HAND_STYLES", "small", "large", "NgxMaterialTimepickerFaceComponent", "timeChange", "timeSelected", "setClockHandPosition", "addTouchEvents", "faceTimeChanges", "selectedTimeChanges", "selectAvailableTime", "_", "onMousedown", "isStarted", "selectTime", "MouseEvent", "clockFaceCords", "clockFace", "centerX", "width", "centerY", "arctangent", "Math", "atan", "abs", "clientX", "clientY", "PI", "circleAngle", "countAngleByCords", "isInnerClockChosen", "isInnerClockFace", "roundedAngle", "roundAngle", "val", "onMouseup", "removeTouchEvents", "touchStartHandler", "bind", "touchEndHandler", "addEventListener", "removeEventListener", "decreaseClockHand", "increaseClockHand", "clockHand", "availableTime", "x0", "y0", "x", "sqrt", "pow", "NgxMaterialTimepickerFaceComponent_Factory", "NgxMaterialTimepickerFaceComponent_Query", "NgxMaterialTimepickerFaceComponent_HostBindings", "NgxMaterialTimepickerFaceComponent_mousedown_HostBindingHandler", "NgxMaterialTimepickerFaceComponent_click_HostBindingHandler", "NgxMaterialTimepickerFaceComponent_touchmove_HostBindingHandler", "changedTouches", "NgxMaterialTimepickerFaceComponent_touchend_HostBindingHandler", "NgxMaterialTimepickerFaceComponent_mousemove_HostBindingHandler", "NgxMaterialTimepickerFaceComponent_mouseup_HostBindingHandler", "NgxMaterialTimepickerFaceComponent_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgStyle", "SlicePipe", "static", "step", "round", "currentAngle", "NgxMaterialTimepicker24HoursFaceComponent", "ngAfterContentInit", "NgxMaterialTimepicker24HoursFaceComponent_Factory", "ɵɵInheritDefinitionFeature", "NgxMaterialTimepicker24HoursFaceComponent_Template", "NgxMaterialTimepicker24HoursFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener", "NgxMaterialTimepicker24HoursFaceComponent_Template_ngx_material_timepicker_face_timeSelected_0_listener", "NgxMaterialTimepicker12HoursFaceComponent", "NgxMaterialTimepicker12HoursFaceComponent_Factory", "NgxMaterialTimepicker12HoursFaceComponent_Template", "NgxMaterialTimepicker12HoursFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener", "NgxMaterialTimepicker12HoursFaceComponent_Template_ngx_material_timepicker_face_timeSelected_0_listener", "NgxMaterialTimepickerMinutesFaceComponent", "minutesList", "minuteChange", "NgxMaterialTimepickerMinutesFaceComponent_Factory", "NgxMaterialTimepickerMinutesFaceComponent_Template", "NgxMaterialTimepickerMinutesFaceComponent_Template_ngx_material_timepicker_face_timeChange_0_listener", "NgxMaterialTimepickerButtonComponent", "NgxMaterialTimepickerButtonComponent_Factory", "NgxMaterialTimepickerButtonComponent_Template", "OverlayDirective", "eventService", "onClick", "preventClick", "OverlayDirective_Factory", "OverlayDirective_HostBindings", "OverlayDirective_click_HostBindingHandler", "NgxMaterialTimepickerThemeDirective", "theme", "setTheme", "hasOwnProperty", "prop", "setProperty", "camelCaseToDash", "NgxMaterialTimepickerThemeDirective_Factory", "myStr", "toLowerCase", "AnimationState", "NgxMaterialTimepickerContainerComponent", "timepickerService", "unsubscribe", "_defaultTime", "stopPropagation", "animationState", "disableAnimation", "ENTER", "defineTime", "timepickerBaseRef", "timeUpdated", "onTimeChange", "setTime", "timeSet", "close", "LEAVE", "phaseName", "toState", "complete", "emit", "NgxMaterialTimepickerContainerComponent_Factory", "NgxMaterialTimepickerContainerComponent_HostBindings", "NgxMaterialTimepickerContainerComponent_keydown_HostBindingHandler", "NgxMaterialTimepickerContainerComponent_Template", "NgxMaterialTimepickerContainerComponent_Template_div_animation_timepicker_done_3_listener", "NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_periodChanged_5_listener", "NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_timeUnitChanged_5_listener", "NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_hourChanged_5_listener", "NgxMaterialTimepickerContainerComponent_Template_ngx_material_timepicker_dial_minuteChanged_5_listener", "NgxMaterialTimepickerContainerComponent_Template_div_click_14_listener", "NgxMaterialTimepickerContainerComponent_Template_div_click_16_listener", "tmp_8_0", "tmp_9_0", "preventOverlayClick", "timepickerClass", "enableKeyboardInput", "cancelBtnTmpl", "confirmBtnTmpl", "NgSwitch", "NgSwitchCase", "AsyncPipe", "DomService", "cfr", "appRef", "injector", "appendTimepickerToBody", "timepicker", "componentRef", "resolveComponentFactory", "create", "keys", "for<PERSON>ach", "key", "instance", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "dom<PERSON>lement", "rootNodes", "body", "append<PERSON><PERSON><PERSON>", "destroyTimepicker", "destroy", "detach<PERSON>iew", "DomService_Factory", "ɵɵinject", "ComponentFactoryResolver", "ApplicationRef", "Injector", "ESCAPE", "NgxMaterialTimepickerComponent", "domService", "isEsc", "opened", "closed", "ngxMaterialTimepickerTheme", "warn", "_ngxMaterialTimepickerTheme", "_format", "timepickerInput", "floor", "_minutesGap", "registerInput", "input", "open", "subscribeToEvents", "unsubscribeFromEvents", "NgxMaterialTimepickerComponent_Factory", "NgxMaterialTimepickerComponent_Template", "NgxMaterialTimepickerToggleIconDirective", "NgxMaterialTimepickerToggleIconDirective_Factory", "NgxMaterialTimepickerToggleComponent", "_disabled", "NgxMaterialTimepickerToggleComponent_Factory", "contentQueries", "NgxMaterialTimepickerToggleComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "customIcon", "NgxMaterialTimepickerToggleComponent_Template", "NgxMaterialTimepickerToggleComponent_Template_button_click_0_listener", "TimepickerDirective", "_value", "timepickerSubscriptions", "onTouched", "onChange", "isDynamicallyChanged", "previousFormat", "_timepicker", "log", "_min", "_max", "picker", "registerTimepicker", "setTimeIfAvailable", "updateValue", "disableClick", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "s", "TimepickerDirective_Factory", "hostVars", "TimepickerDirective_HostBindings", "TimepickerDirective_change_HostBindingHandler", "target", "TimepickerDirective_blur_HostBindingHandler", "TimepickerDirective_click_HostBindingHandler", "ɵɵhostProperty", "provide", "useExisting", "multi", "host", "TimeFormatterPipe", "TimeFormatterPipe_Factory", "NgxTimepickerTimeControlComponent", "<PERSON><PERSON><PERSON><PERSON>", "isSelectedTimeDisabled", "setAvailableTime", "changeTime", "char", "fromCharCode", "concatTime", "changeTimeIfValid", "increase", "decrease", "preventTyping", "getAvailableTime", "getNextAvailableTime", "getPrevAvailableTime", "onFocus", "isFocused", "onBlur", "onModelChange", "timeString", "index", "timeCollection", "maxValue", "currentTimeIndex", "findIndex", "NgxTimepickerTimeControlComponent_Factory", "placeholder", "NgxTimepickerTimeControlComponent_Template", "NgxTimepickerTimeControlComponent_Template_input_ngModelChange_1_listener", "NgxTimepickerTimeControlComponent_Template_input_keydown_1_listener", "NgxTimepickerTimeControlComponent_Template_input_keypress_1_listener", "NgxTimepickerTimeControlComponent_Template_input_focus_1_listener", "NgxTimepickerTimeControlComponent_Template_input_blur_1_listener", "NgxTimepickerTimeControlComponent_Template_span_click_5_listener", "NgxTimepickerTimeControlComponent_Template_span_click_7_listener", "MaxLengthValidator", "NgxTimepickerPeriodSelectorComponent", "periodSelected", "periods", "indexOf", "isOpened", "NgxTimepickerPeriodSelectorComponent_Factory", "NgxTimepickerPeriodSelectorComponent_Template", "NgxTimepickerPeriodSelectorComponent_Template_button_click_2_listener", "NgxTimepickerFieldComponent", "minHour", "maxHour", "unsubscribe$", "isFirstTimeChange", "timepickerTime", "isDefaultTime", "initTime", "isTimeRangeSet", "hour$", "clockTime", "changeDefaultTimeValue", "updateAvailableMinutes", "minute$", "isPeriodDisabled", "updateAvailableTime", "h", "m", "onTimeSet", "emitLocalTimeChange", "localTime", "clockFaceTime", "updateAvailableHours", "formattedTime", "isDefaultTimeAvailable", "NgxTimepickerFieldComponent_Factory", "clockTheme", "controlOnly", "NgxTimepickerFieldComponent_Template", "NgxTimepickerFieldComponent_Template_ngx_timepicker_time_control_timeChanged_1_listener", "NgxTimepickerFieldComponent_Template_ngx_timepicker_time_control_timeChanged_5_listener", "NgxTimepickerFieldComponent_Template_ngx_material_timepicker_timeSet_9_listener", "tmp_2_0", "tmp_10_0", "NgxMaterialTimepickerModule", "setOpts", "ngModule", "useValue", "NgxMaterialTimepickerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["/home/<USER>/other/digi/digitorywebv4/node_modules/ngx-material-timepicker/fesm2015/ngx-material-timepicker.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Directive, Input, HostListener, Component, Pipe, Inject, Optional, EventEmitter, ViewChild, Output, ChangeDetectionStrategy, ContentChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { BehaviorSubject, Subject, merge } from 'rxjs';\nimport { shareReplay, tap, debounceTime, distinctUntilChanged, filter, takeUntil, map } from 'rxjs/operators';\nimport { trigger, transition, style, animate, sequence } from '@angular/animations';\nimport { DateTime, Info } from 'luxon';\nimport * as i4 from '@angular/forms';\nimport { FormControl, NG_VALUE_ACCESSOR, FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nvar TimeUnit;\n(function (TimeUnit) {\n    TimeUnit[TimeUnit[\"HOUR\"] = 0] = \"HOUR\";\n    TimeUnit[TimeUnit[\"MINUTE\"] = 1] = \"MINUTE\";\n})(TimeUnit || (TimeUnit = {}));\n\nvar TimePeriod;\n(function (TimePeriod) {\n    TimePeriod[\"AM\"] = \"AM\";\n    TimePeriod[\"PM\"] = \"PM\";\n})(TimePeriod || (TimePeriod = {}));\n\nvar TimeFormat;\n(function (TimeFormat) {\n    TimeFormat[\"TWELVE\"] = \"hh:mm a\";\n    TimeFormat[\"TWELVE_SHORT\"] = \"h:m a\";\n    TimeFormat[\"TWENTY_FOUR\"] = \"HH:mm\";\n    TimeFormat[\"TWENTY_FOUR_SHORT\"] = \"H:m\";\n})(TimeFormat || (TimeFormat = {}));\n\nfunction isSameOrAfter(time, compareWith, unit = 'minutes') {\n    if (unit === 'hours') {\n        return time.hour >= compareWith.hour;\n    }\n    if (unit === 'minutes') {\n        return time.hasSame(compareWith, unit) || time.valueOf() > compareWith.valueOf();\n    }\n}\nfunction isSameOrBefore(time, compareWith, unit = 'minutes') {\n    if (unit === 'hours') {\n        return time.hour <= compareWith.hour;\n    }\n    if (unit === 'minutes') {\n        return time.hasSame(compareWith, unit) || time.valueOf() <= compareWith.valueOf();\n    }\n}\nfunction isBetween(time, before, after, unit = 'minutes') {\n    if (unit === 'hours') {\n        return isSameOrBefore(time, after, unit) && isSameOrAfter(time, before, unit);\n    }\n    if (unit === 'minutes') {\n        return isSameOrBefore(time, after) && isSameOrAfter(time, before);\n    }\n}\nfunction isDigit(e) {\n    // Allow: backspace, delete, tab, escape, enter\n    if ([46, 8, 9, 27, 13].some(n => n === e.keyCode) ||\n        // Allow: Ctrl/cmd+A\n        (e.keyCode == 65 && (e.ctrlKey === true || e.metaKey === true)) ||\n        // Allow: Ctrl/cmd+C\n        (e.keyCode == 67 && (e.ctrlKey === true || e.metaKey === true)) ||\n        // Allow: Ctrl/cmd+X\n        (e.keyCode == 88 && (e.ctrlKey === true || e.metaKey === true)) ||\n        // Allow: home, end, left, right, up, down\n        (e.keyCode >= 35 && e.keyCode <= 40)) {\n        return true;\n    }\n    return !((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105));\n}\n\n// @dynamic\nclass TimeAdapter {\n    static parseTime(time, opts) {\n        const { numberingSystem, locale } = TimeAdapter.getLocaleOptionsByTime(time, opts);\n        const isPeriodExist = time.split(' ').length === 2;\n        const timeMask = isPeriodExist ? TimeFormat.TWELVE_SHORT : TimeFormat.TWENTY_FOUR_SHORT;\n        return DateTime.fromFormat(time, timeMask, { numberingSystem, locale });\n    }\n    static formatTime(time, opts) {\n        if (!time) {\n            return 'Invalid Time';\n        }\n        const { format } = opts;\n        const parsedTime = TimeAdapter.parseTime(time, opts).setLocale(TimeAdapter.DEFAULT_LOCALE);\n        if (!parsedTime.isValid) {\n            return null;\n        }\n        if (format !== 24) {\n            return parsedTime.toLocaleString(Object.assign(Object.assign({}, DateTime.TIME_SIMPLE), { hour12: format !== 24, numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM })).replace(/\\u200E/g, '').replace(/\\u202F/g, ' ');\n        }\n        return parsedTime.toISOTime({\n            includeOffset: false,\n            suppressMilliseconds: true,\n            suppressSeconds: true\n        }).replace(/\\u200E/g, '').replace(/\\u202F/g, ' ');\n    }\n    static toLocaleTimeString(time, opts = {}) {\n        const { format = TimeAdapter.DEFAULT_FORMAT, locale = TimeAdapter.DEFAULT_LOCALE } = opts;\n        const hourCycle = format === 24 ? 'h23' : 'h12';\n        const timeFormat = Object.assign(Object.assign({}, DateTime.TIME_SIMPLE), { hourCycle });\n        const timeMask = (format === 24) ? TimeFormat.TWENTY_FOUR_SHORT : TimeFormat.TWELVE_SHORT;\n        const localOpts = Object.assign({ locale: opts.locale, numberingSystem: opts.numberingSystem }, timeFormat);\n        return DateTime.fromFormat(time, timeMask).setLocale(locale).toLocaleString(localOpts).replace(/\\u202F/g, ' ');\n    }\n    static isTimeAvailable(time, min, max, granularity, minutesGap, format) {\n        if (!time) {\n            return;\n        }\n        const convertedTime = this.parseTime(time, { format });\n        const minutes = convertedTime.minute;\n        if (minutesGap && minutes === minutes && minutes % minutesGap !== 0) {\n            throw new Error(`Your minutes - ${minutes} doesn\\'t match your minutesGap - ${minutesGap}`);\n        }\n        const isAfter = (min && !max)\n            && isSameOrAfter(convertedTime, min, granularity);\n        const isBefore = (max && !min)\n            && isSameOrBefore(convertedTime, max, granularity);\n        const between = (min && max)\n            && isBetween(convertedTime, min, max, granularity);\n        const isAvailable = !min && !max;\n        return isAfter || isBefore || between || isAvailable;\n    }\n    /***\n     *  Format hour according to time format (12 or 24)\n     */\n    static formatHour(currentHour, format, period) {\n        if (format === 24) {\n            return currentHour;\n        }\n        const hour = period === TimePeriod.AM ? currentHour : currentHour + 12;\n        if (period === TimePeriod.AM && hour === 12) {\n            return 0;\n        }\n        else if (period === TimePeriod.PM && hour === 24) {\n            return 12;\n        }\n        return hour;\n    }\n    static fromDateTimeToString(time, format) {\n        const timeFormat = format === 24 ? TimeFormat.TWENTY_FOUR : TimeFormat.TWELVE;\n        return time.reconfigure({\n            numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM,\n            locale: TimeAdapter.DEFAULT_LOCALE\n        }).toFormat(timeFormat).replace(/\\u202F/g, ' ');\n    }\n    static getLocaleOptionsByTime(time, opts) {\n        const localeConfig = { numberingSystem: opts.numberingSystem, locale: opts.locale };\n        const defaultConfig = { numberingSystem: TimeAdapter.DEFAULT_NUMBERING_SYSTEM, locale: TimeAdapter.DEFAULT_LOCALE };\n        return isNaN(parseInt(time, 10)) ? localeConfig : defaultConfig;\n    }\n}\nTimeAdapter.DEFAULT_FORMAT = 12;\nTimeAdapter.DEFAULT_LOCALE = 'en-US';\nTimeAdapter.DEFAULT_NUMBERING_SYSTEM = 'latn';\n\nconst DEFAULT_HOUR = {\n    time: 12,\n    angle: 360\n};\nconst DEFAULT_MINUTE = {\n    time: 0,\n    angle: 360\n};\nclass NgxMaterialTimepickerService {\n    constructor() {\n        this.hourSubject = new BehaviorSubject(DEFAULT_HOUR);\n        this.minuteSubject = new BehaviorSubject(DEFAULT_MINUTE);\n        this.periodSubject = new BehaviorSubject(TimePeriod.AM);\n    }\n    set hour(hour) {\n        this.hourSubject.next(hour);\n    }\n    get selectedHour() {\n        return this.hourSubject.asObservable();\n    }\n    set minute(minute) {\n        this.minuteSubject.next(minute);\n    }\n    get selectedMinute() {\n        return this.minuteSubject.asObservable();\n    }\n    set period(period) {\n        const isPeriodValid = (period === TimePeriod.AM) || (period === TimePeriod.PM);\n        if (isPeriodValid) {\n            this.periodSubject.next(period);\n        }\n    }\n    get selectedPeriod() {\n        return this.periodSubject.asObservable();\n    }\n    setDefaultTimeIfAvailable(time, min, max, format, minutesGap) {\n        /* Workaround to double error message*/\n        try {\n            if (TimeAdapter.isTimeAvailable(time, min, max, 'minutes', minutesGap)) {\n                this.setDefaultTime(time, format);\n            }\n        }\n        catch (e) {\n            console.error(e);\n        }\n    }\n    getFullTime(format) {\n        const selectedHour = this.hourSubject.getValue().time;\n        const selectedMinute = this.minuteSubject.getValue().time;\n        const hour = selectedHour != null ? selectedHour : DEFAULT_HOUR.time;\n        const minute = selectedMinute != null ? selectedMinute : DEFAULT_MINUTE.time;\n        const period = format === 12 ? this.periodSubject.getValue() : '';\n        const time = `${hour}:${minute} ${period}`.trim();\n        return TimeAdapter.formatTime(time, { format });\n    }\n    setDefaultTime(time, format) {\n        const defaultTime = TimeAdapter.parseTime(time, { format }).toJSDate();\n        if (DateTime.fromJSDate(defaultTime).isValid) {\n            const period = time.substr(time.length - 2).toUpperCase();\n            const hour = defaultTime.getHours();\n            this.hour = Object.assign(Object.assign({}, DEFAULT_HOUR), { time: formatHourByPeriod(hour, period) });\n            this.minute = Object.assign(Object.assign({}, DEFAULT_MINUTE), { time: defaultTime.getMinutes() });\n            this.period = period;\n        }\n        else {\n            this.resetTime();\n        }\n    }\n    resetTime() {\n        this.hour = Object.assign({}, DEFAULT_HOUR);\n        this.minute = Object.assign({}, DEFAULT_MINUTE);\n        this.period = TimePeriod.AM;\n    }\n}\nNgxMaterialTimepickerService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgxMaterialTimepickerService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n/***\n *  Format hour in 24hours format to meridian (AM or PM) format\n */\nfunction formatHourByPeriod(hour, period) {\n    switch (period) {\n        case TimePeriod.AM:\n            return hour === 0 ? 12 : hour;\n        case TimePeriod.PM:\n            return hour === 12 ? 12 : hour - 12;\n        default:\n            return hour;\n    }\n}\n\nconst TIME_LOCALE = new InjectionToken('TimeLocale', {\n    providedIn: 'root',\n    factory: () => TimeAdapter.DEFAULT_LOCALE\n});\nconst NUMBERING_SYSTEM = new InjectionToken('NumberingSystem', {\n    providedIn: 'root',\n    factory: () => TimeAdapter.DEFAULT_NUMBERING_SYSTEM\n});\n\nclass NgxMaterialTimepickerEventService {\n    constructor() {\n        this.backdropClickSubject = new Subject();\n        this.keydownEventSubject = new Subject();\n    }\n    get backdropClick() {\n        return this.backdropClickSubject.asObservable().pipe(shareReplay({ bufferSize: 1, refCount: true }));\n    }\n    get keydownEvent() {\n        return this.keydownEventSubject.asObservable().pipe(shareReplay({ bufferSize: 1, refCount: true }));\n    }\n    dispatchEvent(event) {\n        switch (event.type) {\n            case 'click':\n                this.backdropClickSubject.next(event);\n                break;\n            case 'keydown':\n                this.keydownEventSubject.next(event);\n                break;\n            default:\n                throw new Error('no such event type');\n        }\n    }\n}\nNgxMaterialTimepickerEventService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerEventService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgxMaterialTimepickerEventService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerEventService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerEventService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass AppendToInputDirective {\n    constructor(elementRef, renderer) {\n        this.renderer = renderer;\n        this.element = elementRef.nativeElement;\n    }\n    get inputCords() {\n        return this.inputElement.getBoundingClientRect();\n    }\n    get direction() {\n        const height = this.element.offsetHeight;\n        const { bottom, top } = this._inputCords;\n        const isElementFit = (window && window.innerHeight) - bottom < height;\n        const isTop = isElementFit && top > height;\n        const isCenter = isElementFit && top < height;\n        if (isTop) {\n            return 'top';\n        }\n        else if (isCenter) {\n            return 'center';\n        }\n        return 'bottom';\n    }\n    ngAfterViewInit() {\n        this._inputCords = this.inputCords;\n        this._direction = this.direction;\n        this.append();\n    }\n    changePosition() {\n        const { bottom, top } = this.inputCords;\n        const y = this.defineElementYByDirection(top, bottom);\n        this.setStyle('top', `${y}px`);\n    }\n    append() {\n        const { left, bottom, top } = this._inputCords;\n        const y = this.defineElementYByDirection(top, bottom);\n        this.setStyle('position', 'fixed');\n        this.setStyle('left', `${left}px`);\n        this.setStyle('top', `${y}px`);\n    }\n    setStyle(style, value) {\n        this.renderer.setStyle(this.element, style, value);\n    }\n    defineElementYByDirection(inputTop, inputBottom) {\n        if (this._direction === 'top') {\n            return inputTop - this.element.offsetHeight;\n        }\n        else if (this._direction === 'center') {\n            return inputTop - (this.element.offsetHeight / 2);\n        }\n        return inputBottom;\n    }\n}\nAppendToInputDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: AppendToInputDirective, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }], target: i0.ɵɵFactoryTarget.Directive });\nAppendToInputDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: AppendToInputDirective, selector: \"[ngxAppendToInput]\", inputs: { inputElement: [\"ngxAppendToInput\", \"inputElement\"] }, host: { listeners: { \"window:scroll\": \"changePosition()\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: AppendToInputDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ngxAppendToInput]'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }]; }, propDecorators: { inputElement: [{\n                type: Input,\n                args: ['ngxAppendToInput']\n            }], changePosition: [{\n                type: HostListener,\n                args: ['window:scroll']\n            }] } });\n\nclass NgxMaterialTimepickerContentComponent {\n}\nNgxMaterialTimepickerContentComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerContentComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerContentComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerContentComponent, selector: \"ngx-material-timepicker-content\", inputs: { appendToInput: \"appendToInput\", inputElement: \"inputElement\" }, ngImport: i0, template: \"<div [ngxAppendToInput]=\\\"inputElement\\\" *ngIf=\\\"appendToInput;else timepickerModal\\\">\\n    <!--suppress HtmlUnknownAttribute -->\\n    <ng-container *ngTemplateOutlet=\\\"timepickerOutlet\\\"></ng-container>\\n</div>\\n\\n<ng-template #timepickerModal>\\n    <!--suppress HtmlUnknownAttribute -->\\n    <ng-container *ngTemplateOutlet=\\\"timepickerOutlet\\\"></ng-container>\\n</ng-template>\\n\\n<ng-template #timepickerOutlet>\\n    <ng-content></ng-content>\\n</ng-template>\\n\", directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: AppendToInputDirective, selector: \"[ngxAppendToInput]\", inputs: [\"ngxAppendToInput\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerContentComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-content',\n                    templateUrl: './ngx-material-timepicker-content.component.html',\n                }]\n        }], propDecorators: { appendToInput: [{\n                type: Input\n            }], inputElement: [{\n                type: Input\n            }] } });\n\n// @dynamic\nclass TimepickerTimeUtils {\n    static getHours(format) {\n        return Array(format).fill(1).map((v, i) => {\n            const angleStep = 30;\n            const time = v + i;\n            const angle = angleStep * time;\n            return { time: time === 24 ? 0 : time, angle };\n        });\n    }\n    static disableHours(hours, config) {\n        if (config.min || config.max) {\n            return hours.map(value => {\n                const hour = config.format === 24 ? value.time : TimeAdapter.formatHour(value.time, config.format, config.period);\n                const currentTime = DateTime.fromObject({ hour }).toFormat(TimeFormat.TWELVE);\n                return Object.assign(Object.assign({}, value), { disabled: !TimeAdapter.isTimeAvailable(currentTime, config.min, config.max, 'hours') });\n            });\n        }\n        return hours;\n    }\n    static getMinutes(gap = 1) {\n        const minutesCount = 60;\n        const angleStep = 360 / minutesCount;\n        const minutes = [];\n        for (let i = 0; i < minutesCount; i++) {\n            const angle = angleStep * i;\n            if (i % gap === 0) {\n                minutes.push({ time: i, angle: angle !== 0 ? angle : 360 });\n            }\n        }\n        return minutes;\n    }\n    static disableMinutes(minutes, selectedHour, config) {\n        if (config.min || config.max) {\n            const hour = TimeAdapter.formatHour(selectedHour, config.format, config.period);\n            return minutes.map(value => {\n                const currentTime = DateTime.fromObject({ hour, minute: value.time }).toFormat(TimeFormat.TWELVE);\n                return Object.assign(Object.assign({}, value), { disabled: !TimeAdapter.isTimeAvailable(currentTime, config.min, config.max, 'minutes') });\n            });\n        }\n        return minutes;\n    }\n}\n\nclass TimeLocalizerPipe {\n    constructor(locale) {\n        this.locale = locale;\n    }\n    transform(time, timeUnit, isKeyboardEnabled = false) {\n        if (time == null || time === '') {\n            return '';\n        }\n        switch (timeUnit) {\n            case TimeUnit.HOUR: {\n                const format = (time === 0 || isKeyboardEnabled) ? 'HH' : 'H';\n                return this.formatTime('hour', time, format);\n            }\n            case TimeUnit.MINUTE:\n                return this.formatTime('minute', time, 'mm');\n            default:\n                throw new Error(`There is no Time Unit with type ${timeUnit}`);\n        }\n    }\n    formatTime(timeMeasure, time, format) {\n        try {\n            return DateTime.fromObject({ [timeMeasure]: +time }).setLocale(this.locale).toFormat(format);\n        }\n        catch (_a) {\n            throw new Error(`Cannot format provided time - ${time} to locale - ${this.locale}`);\n        }\n    }\n}\nTimeLocalizerPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeLocalizerPipe, deps: [{ token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Pipe });\nTimeLocalizerPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeLocalizerPipe, name: \"timeLocalizer\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeLocalizerPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'timeLocalizer'\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; } });\n\nclass TimeParserPipe {\n    constructor(locale, numberingSystem) {\n        this.locale = locale;\n        this.numberingSystem = numberingSystem;\n    }\n    transform(time, timeUnit = TimeUnit.HOUR) {\n        if (time == null || time === '') {\n            return '';\n        }\n        if (!isNaN(+time)) {\n            return time;\n        }\n        if (timeUnit === TimeUnit.MINUTE) {\n            return this.parseTime(time, 'm', 'minute');\n        }\n        return this.parseTime(time, 'H', 'hour');\n    }\n    parseTime(time, format, timeMeasure) {\n        const parsedTime = DateTime.fromFormat(String(time), format, {\n            numberingSystem: this.numberingSystem,\n            locale: this.locale\n        })[timeMeasure];\n        if (!isNaN(parsedTime)) {\n            return parsedTime;\n        }\n        throw new Error(`Cannot parse time - ${time}`);\n    }\n}\nTimeParserPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeParserPipe, deps: [{ token: TIME_LOCALE }, { token: NUMBERING_SYSTEM }], target: i0.ɵɵFactoryTarget.Pipe });\nTimeParserPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeParserPipe, name: \"timeParser\" });\nTimeParserPipe.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeParserPipe });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeParserPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'timeParser'\n                }]\n        }, {\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NUMBERING_SYSTEM]\n                }] }]; } });\n\nclass AutofocusDirective {\n    constructor(element, document) {\n        this.element = element;\n        this.document = document;\n        this.activeElement = this.document.activeElement;\n    }\n    ngOnChanges() {\n        if (this.isFocusActive) {\n            // To avoid ExpressionChangedAfterItHasBeenCheckedError;\n            setTimeout(() => this.element.nativeElement.focus({ preventScroll: true }));\n        }\n    }\n    ngOnDestroy() {\n        // To avoid ExpressionChangedAfterItHasBeenCheckedError;\n        setTimeout(() => this.activeElement.focus({ preventScroll: true }));\n    }\n}\nAutofocusDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: AutofocusDirective, deps: [{ token: i0.ElementRef }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nAutofocusDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: AutofocusDirective, selector: \"[timepickerAutofocus]\", inputs: { isFocusActive: [\"timepickerAutofocus\", \"isFocusActive\"] }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: AutofocusDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[timepickerAutofocus]'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { isFocusActive: [{\n                type: Input,\n                args: ['timepickerAutofocus']\n            }] } });\n\n/* tslint:disable:triple-equals */\nclass NgxMaterialTimepickerDialControlComponent {\n    constructor(timeParserPipe, timeLocalizerPipe) {\n        this.timeParserPipe = timeParserPipe;\n        this.timeLocalizerPipe = timeLocalizerPipe;\n        this.timeUnitChanged = new EventEmitter();\n        this.timeChanged = new EventEmitter();\n        this.focused = new EventEmitter();\n        this.unfocused = new EventEmitter();\n    }\n    get selectedTime() {\n        if (!!this.time) {\n            return this.timeList.find(t => t.time === +this.time);\n        }\n    }\n    ngOnInit() {\n        if (this.isEditable) {\n            this.timeControl = new FormControl({ value: this.formatTimeForUI(this.time), disabled: this.disabled });\n            this.timeControl.valueChanges.pipe(tap((value) => {\n                if (value.length > 2) {\n                    this.updateInputValue(value.slice(-1));\n                }\n            }), debounceTime(500), distinctUntilChanged(), filter((value) => !isTimeDisabledToChange(this.time, value, this.timeList)), tap((value) => this.time = this.timeParserPipe.transform(value, this.timeUnit).toString())).subscribe(() => this.updateTime());\n        }\n    }\n    saveTimeAndChangeTimeUnit(event, unit) {\n        event.preventDefault();\n        this.previousTime = this.time;\n        this.timeUnitChanged.next(unit);\n        this.focused.next();\n    }\n    updateTime() {\n        const time = this.selectedTime;\n        if (time) {\n            this.timeChanged.next(time);\n            this.previousTime = time.time;\n            if (this.isEditable) {\n                this.updateInputValue(this.formatTimeForUI(time.time));\n            }\n        }\n    }\n    onKeydown(e) {\n        if (!isDigit(e)) {\n            e.preventDefault();\n        }\n        else {\n            this.changeTimeByArrow(e.keyCode);\n        }\n    }\n    changeTimeByArrow(keyCode) {\n        const ARROW_UP = 38;\n        const ARROW_DOWN = 40;\n        let time;\n        if (keyCode === ARROW_UP) {\n            time = String(+this.time + (this.minutesGap || 1));\n        }\n        else if (keyCode === ARROW_DOWN) {\n            time = String(+this.time - (this.minutesGap || 1));\n        }\n        if (!isTimeUnavailable(time, this.timeList)) {\n            this.time = time;\n            this.updateTime();\n        }\n    }\n    formatTimeForUI(value) {\n        const parsedTime = this.timeParserPipe.transform(value, this.timeUnit).toString();\n        return this.timeLocalizerPipe.transform(parsedTime, this.timeUnit, true);\n    }\n    updateInputValue(value) {\n        this.editableTimeTmpl.nativeElement.value = value;\n    }\n}\nNgxMaterialTimepickerDialControlComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerDialControlComponent, deps: [{ token: TimeParserPipe }, { token: TimeLocalizerPipe }], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerDialControlComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerDialControlComponent, selector: \"ngx-material-timepicker-dial-control\", inputs: { timeList: \"timeList\", timeUnit: \"timeUnit\", time: \"time\", isActive: \"isActive\", isEditable: \"isEditable\", minutesGap: \"minutesGap\", disabled: \"disabled\" }, outputs: { timeUnitChanged: \"timeUnitChanged\", timeChanged: \"timeChanged\", focused: \"focused\", unfocused: \"unfocused\" }, providers: [TimeParserPipe, TimeLocalizerPipe], viewQueries: [{ propertyName: \"editableTimeTmpl\", first: true, predicate: [\"editableTimeTmpl\"], descendants: true }], ngImport: i0, template: \"<!--suppress HtmlFormInputWithoutLabel, HtmlUnknownAttribute -->\\n<input class=\\\"timepicker-dial__control timepicker-dial__item\\\"\\n       [ngClass]=\\\"{'timepicker-dial__item_active': isActive}\\\"\\n       [ngModel]=\\\"time | timeLocalizer: timeUnit\\\"\\n       (ngModelChange)=\\\"time = $event\\\"\\n       [disabled]=\\\"disabled\\\"\\n       (input)=\\\"updateTime()\\\" (focus)=\\\"saveTimeAndChangeTimeUnit($event, timeUnit)\\\"\\n       readonly [timepickerAutofocus]=\\\"isActive\\\"\\n       *ngIf=\\\"!isEditable;else editableTemplate\\\">\\n\\n<ng-template #editableTemplate>\\n    <!--suppress HtmlFormInputWithoutLabel, HtmlUnknownAttribute -->\\n    <input class=\\\"timepicker-dial__control timepicker-dial__item timepicker-dial__control_editable\\\"\\n           #editableTimeTmpl\\n           [formControl]=\\\"timeControl\\\"\\n           [ngClass]=\\\"{'timepicker-dial__item_active': isActive}\\\"\\n           [timepickerAutofocus]=\\\"isActive\\\"\\n           (focus)=\\\"saveTimeAndChangeTimeUnit($event, timeUnit)\\\"\\n           (keydown)=\\\"onKeydown($event)\\\">\\n</ng-template>\\n\", styles: [\".timepicker-dial__item{cursor:pointer;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__item{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__item_active{color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__item_active{color:var(--dial-active-color)}}.timepicker-dial__control{border:none;background-color:transparent;font-size:50px;width:60px;padding:0;border-radius:3px;text-align:right}.timepicker-dial__control_editable:focus{color:#00bfff;background-color:#fff;outline:deepskyblue}@supports (color: var(--dial-editable-active-color)){.timepicker-dial__control_editable:focus{color:var(--dial-editable-active-color)}}@supports (background-color: var(--dial-editable-background-color)){.timepicker-dial__control_editable:focus{background-color:var(--dial-editable-background-color)}}@supports (outline: var(--dial-editable-active-color)){.timepicker-dial__control_editable:focus{outline:var(--dial-editable-active-color)}}.timepicker-dial__control:disabled{cursor:default}.timepicker-dial__control:focus-visible{outline:none}\\n\"], directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i4.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i4.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { type: i4.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { type: AutofocusDirective, selector: \"[timepickerAutofocus]\", inputs: [\"timepickerAutofocus\"] }, { type: i4.FormControlDirective, selector: \"[formControl]\", inputs: [\"disabled\", \"formControl\", \"ngModel\"], outputs: [\"ngModelChange\"], exportAs: [\"ngForm\"] }], pipes: { \"timeLocalizer\": TimeLocalizerPipe } });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerDialControlComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-dial-control',\n                    templateUrl: 'ngx-material-timepicker-dial-control.component.html',\n                    styleUrls: ['ngx-material-timepicker-dial-control.component.scss'],\n                    providers: [TimeParserPipe, TimeLocalizerPipe],\n                }]\n        }], ctorParameters: function () { return [{ type: TimeParserPipe }, { type: TimeLocalizerPipe }]; }, propDecorators: { timeList: [{\n                type: Input\n            }], timeUnit: [{\n                type: Input\n            }], time: [{\n                type: Input\n            }], isActive: [{\n                type: Input\n            }], isEditable: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], editableTimeTmpl: [{\n                type: ViewChild,\n                args: ['editableTimeTmpl']\n            }], timeUnitChanged: [{\n                type: Output\n            }], timeChanged: [{\n                type: Output\n            }], focused: [{\n                type: Output\n            }], unfocused: [{\n                type: Output\n            }] } });\nfunction isTimeDisabledToChange(currentTime, nextTime, timeList) {\n    const isNumber = /\\d/.test(nextTime);\n    if (isNumber) {\n        return isTimeUnavailable(nextTime, timeList);\n    }\n}\nfunction isTimeUnavailable(time, timeList) {\n    const selectedTime = timeList.find(value => value.time === +time);\n    return !selectedTime || (selectedTime && selectedTime.disabled);\n}\n\nclass NgxMaterialTimepickerPeriodComponent {\n    constructor() {\n        this.timePeriod = TimePeriod;\n        this.isPeriodAvailable = true;\n        this.periodChanged = new EventEmitter();\n    }\n    changePeriod(period) {\n        this.isPeriodAvailable = this.isSwitchPeriodAvailable(period);\n        if (this.isPeriodAvailable) {\n            this.periodChanged.next(period);\n        }\n    }\n    animationDone() {\n        this.isPeriodAvailable = true;\n    }\n    isSwitchPeriodAvailable(period) {\n        const time = this.getDisabledTimeByPeriod(period);\n        return !time.every(t => t.disabled);\n    }\n    getDisabledTimeByPeriod(period) {\n        switch (this.activeTimeUnit) {\n            case TimeUnit.HOUR:\n                return TimepickerTimeUtils.disableHours(this.hours, {\n                    min: this.minTime,\n                    max: this.maxTime,\n                    format: this.format,\n                    period\n                });\n            case TimeUnit.MINUTE:\n                return TimepickerTimeUtils.disableMinutes(this.minutes, +this.selectedHour, {\n                    min: this.minTime,\n                    max: this.maxTime,\n                    format: this.format,\n                    period\n                });\n            default:\n                throw new Error('no such TimeUnit');\n        }\n    }\n}\nNgxMaterialTimepickerPeriodComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerPeriodComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerPeriodComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerPeriodComponent, selector: \"ngx-material-timepicker-period\", inputs: { selectedPeriod: \"selectedPeriod\", format: \"format\", activeTimeUnit: \"activeTimeUnit\", hours: \"hours\", minutes: \"minutes\", minTime: \"minTime\", maxTime: \"maxTime\", selectedHour: \"selectedHour\", meridiems: \"meridiems\" }, outputs: { periodChanged: \"periodChanged\" }, ngImport: i0, template: \"<div class=\\\"timepicker-period\\\">\\n\\t\\t\\t<button class=\\\"timepicker-dial__item timepicker-period__btn\\\"\\n                  [ngClass]=\\\"{'timepicker-dial__item_active': selectedPeriod === timePeriod.AM}\\\"\\n                  (click)=\\\"changePeriod(timePeriod.AM)\\\"\\n                  type=\\\"button\\\">{{meridiems[0]}}</button>\\n    <button class=\\\"timepicker-dial__item timepicker-period__btn\\\"\\n          [ngClass]=\\\"{'timepicker-dial__item_active': selectedPeriod === timePeriod.PM}\\\"\\n          (click)=\\\"changePeriod(timePeriod.PM)\\\"\\n          type=\\\"button\\\">{{meridiems[1]}}</button>\\n    <div class=\\\"timepicker-period__warning\\\" [@scaleInOut] (@scaleInOut.done)=\\\"animationDone()\\\" *ngIf=\\\"!isPeriodAvailable\\\">\\n        <p>Current time would be invalid in this period.</p>\\n    </div>\\n</div>\\n\", styles: [\".timepicker-dial__item{cursor:pointer;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__item{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__item_active{color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__item_active{color:var(--dial-active-color)}}.timepicker-period{display:flex;flex-direction:column;position:relative}.timepicker-period__btn{padding:1px 3px;border:0;background-color:transparent;font-size:18px;font-weight:500;-webkit-user-select:none;-moz-user-select:none;user-select:none;outline:none;border-radius:3px;transition:background-color .5s;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-period__btn{font-family:var(--primary-font-family)}}.timepicker-period__btn:focus{background-color:#00000012}.timepicker-period__warning{padding:5px 10px;border-radius:3px;background-color:#0000008c;color:#fff;position:absolute;width:200px;left:-20px;top:40px}.timepicker-period__warning>p{margin:0;font-size:12px;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-period__warning>p{font-family:var(--primary-font-family)}}\\n\"], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [\n        trigger('scaleInOut', [\n            transition(':enter', [\n                style({ transform: 'scale(0)' }),\n                animate('.2s', style({ transform: 'scale(1)' })),\n                sequence([\n                    animate('3s', style({ opacity: 1 })),\n                    animate('.3s', style({ opacity: 0 }))\n                ])\n            ])\n        ])\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerPeriodComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-period',\n                    templateUrl: 'ngx-material-timepicker-period.component.html',\n                    styleUrls: ['ngx-material-timepicker-period.component.scss'],\n                    animations: [\n                        trigger('scaleInOut', [\n                            transition(':enter', [\n                                style({ transform: 'scale(0)' }),\n                                animate('.2s', style({ transform: 'scale(1)' })),\n                                sequence([\n                                    animate('3s', style({ opacity: 1 })),\n                                    animate('.3s', style({ opacity: 0 }))\n                                ])\n                            ])\n                        ])\n                    ]\n                }]\n        }], propDecorators: { selectedPeriod: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], activeTimeUnit: [{\n                type: Input\n            }], hours: [{\n                type: Input\n            }], minutes: [{\n                type: Input\n            }], minTime: [{\n                type: Input\n            }], maxTime: [{\n                type: Input\n            }], selectedHour: [{\n                type: Input\n            }], meridiems: [{\n                type: Input\n            }], periodChanged: [{\n                type: Output\n            }] } });\n\nclass NgxMaterialTimepickerDialComponent {\n    constructor(locale) {\n        this.locale = locale;\n        this.timeUnit = TimeUnit;\n        this.meridiems = Info.meridiems({ locale: this.locale });\n        this.periodChanged = new EventEmitter();\n        this.timeUnitChanged = new EventEmitter();\n        this.hourChanged = new EventEmitter();\n        this.minuteChanged = new EventEmitter();\n    }\n    ngOnChanges(changes) {\n        if (changes['period'] && changes['period'].currentValue\n            || changes['format'] && changes['format'].currentValue) {\n            const hours = TimepickerTimeUtils.getHours(this.format);\n            this.hours = TimepickerTimeUtils.disableHours(hours, {\n                min: this.minTime,\n                max: this.maxTime,\n                format: this.format,\n                period: this.period,\n            });\n        }\n        if (changes['period'] && changes['period'].currentValue\n            || changes['hour'] && changes['hour'].currentValue) {\n            const minutes = TimepickerTimeUtils.getMinutes(this.minutesGap);\n            this.minutes = TimepickerTimeUtils.disableMinutes(minutes, +this.hour, {\n                min: this.minTime,\n                max: this.maxTime,\n                format: this.format,\n                period: this.period,\n            });\n        }\n    }\n    changeTimeUnit(unit) {\n        this.timeUnitChanged.next(unit);\n    }\n    changePeriod(period) {\n        this.periodChanged.next(period);\n    }\n    changeHour(hour) {\n        this.hourChanged.next(hour);\n        if (this.isEditable) {\n            this.changeTimeUnit(TimeUnit.MINUTE);\n        }\n    }\n    changeMinute(minute) {\n        this.minuteChanged.next(minute);\n    }\n    showHint() {\n        this.isHintVisible = true;\n    }\n    hideHint() {\n        this.isHintVisible = false;\n    }\n}\nNgxMaterialTimepickerDialComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerDialComponent, deps: [{ token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerDialComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerDialComponent, selector: \"ngx-material-timepicker-dial\", inputs: { editableHintTmpl: \"editableHintTmpl\", hour: \"hour\", minute: \"minute\", format: \"format\", period: \"period\", activeTimeUnit: \"activeTimeUnit\", minTime: \"minTime\", maxTime: \"maxTime\", isEditable: \"isEditable\", minutesGap: \"minutesGap\", hoursOnly: \"hoursOnly\" }, outputs: { periodChanged: \"periodChanged\", timeUnitChanged: \"timeUnitChanged\", hourChanged: \"hourChanged\", minuteChanged: \"minuteChanged\" }, usesOnChanges: true, ngImport: i0, template: \"<div class=\\\"timepicker-dial\\\">\\n    <div class=\\\"timepicker-dial__container\\\">\\n        <div class=\\\"timepicker-dial__time\\\">\\n            <ngx-material-timepicker-dial-control [timeList]=\\\"hours\\\" [time]=\\\"hour\\\" [timeUnit]=\\\"timeUnit.HOUR\\\"\\n                                                  [isActive]=\\\"activeTimeUnit === timeUnit.HOUR\\\"\\n                                                  [isEditable]=\\\"isEditable\\\"\\n                                                  (timeUnitChanged)=\\\"changeTimeUnit($event)\\\"\\n                                                  (timeChanged)=\\\"changeHour($event)\\\"\\n                                                  (focused)=\\\"showHint()\\\"\\n                                                  (unfocused)=\\\"hideHint()\\\">\\n\\n            </ngx-material-timepicker-dial-control>\\n            <span>:</span>\\n            <ngx-material-timepicker-dial-control [timeList]=\\\"minutes\\\" [time]=\\\"minute\\\" [timeUnit]=\\\"timeUnit.MINUTE\\\"\\n                                                  [isActive]=\\\"activeTimeUnit === timeUnit.MINUTE\\\"\\n                                                  [isEditable]=\\\"isEditable\\\"\\n                                                  [minutesGap]=\\\"minutesGap\\\"\\n                                                  [disabled]=\\\"hoursOnly\\\"\\n                                                  (timeUnitChanged)=\\\"changeTimeUnit($event)\\\"\\n                                                  (timeChanged)=\\\"changeMinute($event)\\\"\\n                                                  (focused)=\\\"showHint()\\\"\\n                                                  (unfocused)=\\\"hideHint()\\\">\\n\\n            </ngx-material-timepicker-dial-control>\\n        </div>\\n        <ngx-material-timepicker-period class=\\\"timepicker-dial__period\\\"\\n                                        [ngClass]=\\\"{'timepicker-dial__period--hidden': format === 24}\\\"\\n                                        [selectedPeriod]=\\\"period\\\" [activeTimeUnit]=\\\"activeTimeUnit\\\"\\n                                        [maxTime]=\\\"maxTime\\\" [minTime]=\\\"minTime\\\" [format]=\\\"format\\\"\\n                                        [hours]=\\\"hours\\\" [minutes]=\\\"minutes\\\" [selectedHour]=\\\"hour\\\"\\n                                        [meridiems]=\\\"meridiems\\\"\\n                                        (periodChanged)=\\\"changePeriod($event)\\\"></ngx-material-timepicker-period>\\n    </div>\\n    <div *ngIf=\\\"isEditable || editableHintTmpl\\\" [ngClass]=\\\"{'timepicker-dial__hint-container--hidden': !isHintVisible}\\\">\\n        <!--suppress HtmlUnknownAttribute -->\\n        <ng-container *ngTemplateOutlet=\\\"editableHintTmpl ? editableHintTmpl : editableHintDefault\\\"></ng-container>\\n        <ng-template #editableHintDefault>\\n            <small class=\\\"timepicker-dial__hint\\\"> * use arrows (<span>&#8645;</span>) to change the time</small>\\n        </ng-template>\\n    </div>\\n</div>\\n\", styles: [\".timepicker-dial{text-align:right}.timepicker-dial__container{display:flex;align-items:center;justify-content:flex-end;-webkit-tap-highlight-color:rgba(0,0,0,0)}.timepicker-dial__time{display:flex;align-items:baseline;line-height:normal;font-size:50px;color:#ffffff80;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-dial__time{font-family:var(--primary-font-family);color:var(--dial-inactive-color)}}.timepicker-dial__period{display:block;margin-left:10px}.timepicker-dial__period--hidden{visibility:hidden}.timepicker-dial__hint-container--hidden{visibility:hidden}.timepicker-dial__hint{display:inline-block;font-size:10px;color:#fff}@supports (color: var(--dial-active-color)){.timepicker-dial__hint{color:var(--dial-active-color)}}.timepicker-dial__hint span{font-size:14px}@media (max-device-width: 1023px) and (orientation: landscape){.timepicker-dial__container{flex-direction:column}.timepicker-dial__period{margin-left:0}}\\n\"], components: [{ type: NgxMaterialTimepickerDialControlComponent, selector: \"ngx-material-timepicker-dial-control\", inputs: [\"timeList\", \"timeUnit\", \"time\", \"isActive\", \"isEditable\", \"minutesGap\", \"disabled\"], outputs: [\"timeUnitChanged\", \"timeChanged\", \"focused\", \"unfocused\"] }, { type: NgxMaterialTimepickerPeriodComponent, selector: \"ngx-material-timepicker-period\", inputs: [\"selectedPeriod\", \"format\", \"activeTimeUnit\", \"hours\", \"minutes\", \"minTime\", \"maxTime\", \"selectedHour\", \"meridiems\"], outputs: [\"periodChanged\"] }], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerDialComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-dial',\n                    templateUrl: 'ngx-material-timepicker-dial.component.html',\n                    styleUrls: ['ngx-material-timepicker-dial.component.scss'],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; }, propDecorators: { editableHintTmpl: [{\n                type: Input\n            }], hour: [{\n                type: Input\n            }], minute: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], period: [{\n                type: Input\n            }], activeTimeUnit: [{\n                type: Input\n            }], minTime: [{\n                type: Input\n            }], maxTime: [{\n                type: Input\n            }], isEditable: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], hoursOnly: [{\n                type: Input\n            }], periodChanged: [{\n                type: Output\n            }], timeUnitChanged: [{\n                type: Output\n            }], hourChanged: [{\n                type: Output\n            }], minuteChanged: [{\n                type: Output\n            }] } });\n\nclass NgxMaterialTimepickerHoursFace {\n    constructor(format) {\n        this.hourChange = new EventEmitter();\n        this.hourSelected = new EventEmitter();\n        this.hoursList = [];\n        this.hoursList = TimepickerTimeUtils.getHours(format);\n    }\n    onTimeSelected(time) {\n        this.hourSelected.next(time);\n    }\n}\nNgxMaterialTimepickerHoursFace.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerHoursFace, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\nNgxMaterialTimepickerHoursFace.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerHoursFace, inputs: { selectedHour: \"selectedHour\", minTime: \"minTime\", maxTime: \"maxTime\", format: \"format\" }, outputs: { hourChange: \"hourChange\", hourSelected: \"hourSelected\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerHoursFace, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: undefined }]; }, propDecorators: { selectedHour: [{\n                type: Input\n            }], minTime: [{\n                type: Input\n            }], maxTime: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], hourChange: [{\n                type: Output\n            }], hourSelected: [{\n                type: Output\n            }] } });\n\nclass ActiveHourPipe {\n    transform(hour, currentHour, isClockFaceDisabled) {\n        if (hour == null || isClockFaceDisabled) {\n            return false;\n        }\n        return hour === currentHour;\n    }\n}\nActiveHourPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveHourPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\nActiveHourPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveHourPipe, name: \"activeHour\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveHourPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'activeHour'\n                }]\n        }] });\n\nclass ActiveMinutePipe {\n    transform(minute, currentMinute, gap, isClockFaceDisabled) {\n        if (minute == null || isClockFaceDisabled) {\n            return false;\n        }\n        const defaultGap = 5;\n        return ((currentMinute === minute) && (minute % (gap || defaultGap) === 0));\n    }\n}\nActiveMinutePipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveMinutePipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\nActiveMinutePipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveMinutePipe, name: \"activeMinute\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: ActiveMinutePipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'activeMinute'\n                }]\n        }] });\n\nclass MinutesFormatterPipe {\n    transform(minute, gap = 5) {\n        if (!minute) {\n            return minute;\n        }\n        return minute % gap === 0 ? minute : '';\n    }\n}\nMinutesFormatterPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: MinutesFormatterPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\nMinutesFormatterPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: MinutesFormatterPipe, name: \"minutesFormatter\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: MinutesFormatterPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'minutesFormatter'\n                }]\n        }] });\n\nconst CLOCK_HAND_STYLES = {\n    small: {\n        height: '75px',\n        top: 'calc(50% - 75px)'\n    },\n    large: {\n        height: '103px',\n        top: 'calc(50% - 103px)'\n    }\n};\nclass NgxMaterialTimepickerFaceComponent {\n    constructor() {\n        this.timeUnit = TimeUnit;\n        this.innerClockFaceSize = 85;\n        this.timeChange = new EventEmitter();\n        this.timeSelected = new EventEmitter();\n    }\n    ngAfterViewInit() {\n        this.setClockHandPosition();\n        this.addTouchEvents();\n    }\n    ngOnChanges(changes) {\n        const faceTimeChanges = changes['faceTime'];\n        const selectedTimeChanges = changes['selectedTime'];\n        if ((faceTimeChanges && faceTimeChanges.currentValue)\n            && (selectedTimeChanges && selectedTimeChanges.currentValue)) {\n            /* Set time according to passed an input value */\n            this.selectedTime = this.faceTime.find(time => time.time === this.selectedTime.time);\n        }\n        if (selectedTimeChanges && selectedTimeChanges.currentValue) {\n            this.setClockHandPosition();\n        }\n        if (faceTimeChanges && faceTimeChanges.currentValue) {\n            // To avoid an error ExpressionChangedAfterItHasBeenCheckedError\n            setTimeout(() => this.selectAvailableTime());\n        }\n    }\n    trackByTime(_, time) {\n        return time.time;\n    }\n    onMousedown(e) {\n        e.preventDefault();\n        this.isStarted = true;\n    }\n    selectTime(e) {\n        if (!this.isStarted && (e instanceof MouseEvent && e.type !== 'click')) {\n            return;\n        }\n        const clockFaceCords = this.clockFace.nativeElement.getBoundingClientRect();\n        /* Get x0 and y0 of the circle */\n        const centerX = clockFaceCords.left + clockFaceCords.width / 2;\n        const centerY = clockFaceCords.top + clockFaceCords.height / 2;\n        /* Counting the arctangent and convert it to from radian to deg */\n        const arctangent = Math.atan(Math.abs(e.clientX - centerX) / Math.abs(e.clientY - centerY)) * 180 / Math.PI;\n        /* Get angle according to quadrant */\n        const circleAngle = countAngleByCords(centerX, centerY, e.clientX, e.clientY, arctangent);\n        /* Check if selected time from the inner clock face (24 hours format only) */\n        const isInnerClockChosen = this.format && this.isInnerClockFace(centerX, centerY, e.clientX, e.clientY);\n        /* Round angle according to angle step */\n        const angleStep = this.unit === TimeUnit.MINUTE ? (6 * (this.minutesGap || 1)) : 30;\n        const roundedAngle = roundAngle(circleAngle, angleStep);\n        const angle = (roundedAngle || 360) + (isInnerClockChosen ? 360 : 0);\n        const selectedTime = this.faceTime.find(val => val.angle === angle);\n        if (selectedTime && !selectedTime.disabled) {\n            this.timeChange.next(selectedTime);\n            /* To let know whether user ended interaction with clock face */\n            if (!this.isStarted) {\n                this.timeSelected.next(selectedTime.time);\n            }\n        }\n    }\n    onMouseup(e) {\n        e.preventDefault();\n        this.isStarted = false;\n    }\n    ngOnDestroy() {\n        this.removeTouchEvents();\n    }\n    addTouchEvents() {\n        this.touchStartHandler = this.onMousedown.bind(this);\n        this.touchEndHandler = this.onMouseup.bind(this);\n        this.clockFace.nativeElement.addEventListener('touchstart', this.touchStartHandler);\n        this.clockFace.nativeElement.addEventListener('touchend', this.touchEndHandler);\n    }\n    removeTouchEvents() {\n        this.clockFace.nativeElement.removeEventListener('touchstart', this.touchStartHandler);\n        this.clockFace.nativeElement.removeEventListener('touchend', this.touchEndHandler);\n    }\n    setClockHandPosition() {\n        if (this.format === 24) {\n            if (this.selectedTime.time > 12 || this.selectedTime.time === 0) {\n                this.decreaseClockHand();\n            }\n            else {\n                this.increaseClockHand();\n            }\n        }\n        this.clockHand.nativeElement.style.transform = `rotate(${this.selectedTime.angle}deg)`;\n    }\n    selectAvailableTime() {\n        const currentTime = this.faceTime.find(time => this.selectedTime.time === time.time);\n        this.isClockFaceDisabled = this.faceTime.every(time => time.disabled);\n        if ((currentTime && currentTime.disabled) && !this.isClockFaceDisabled) {\n            const availableTime = this.faceTime.find(time => !time.disabled);\n            this.timeChange.next(availableTime);\n        }\n    }\n    isInnerClockFace(x0, y0, x, y) {\n        /* Detect whether time from the inner clock face or not (24 format only) */\n        return Math.sqrt(Math.pow(x - x0, 2) + Math.pow(y - y0, 2)) < this.innerClockFaceSize;\n    }\n    decreaseClockHand() {\n        this.clockHand.nativeElement.style.height = CLOCK_HAND_STYLES.small.height;\n        this.clockHand.nativeElement.style.top = CLOCK_HAND_STYLES.small.top;\n    }\n    increaseClockHand() {\n        this.clockHand.nativeElement.style.height = CLOCK_HAND_STYLES.large.height;\n        this.clockHand.nativeElement.style.top = CLOCK_HAND_STYLES.large.top;\n    }\n}\nNgxMaterialTimepickerFaceComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerFaceComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerFaceComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerFaceComponent, selector: \"ngx-material-timepicker-face\", inputs: { faceTime: \"faceTime\", selectedTime: \"selectedTime\", unit: \"unit\", format: \"format\", minutesGap: \"minutesGap\" }, outputs: { timeChange: \"timeChange\", timeSelected: \"timeSelected\" }, host: { listeners: { \"mousedown\": \"onMousedown($event)\", \"click\": \"selectTime($event)\", \"touchmove\": \"selectTime($event.changedTouches[0])\", \"touchend\": \"selectTime($event.changedTouches[0])\", \"mousemove\": \"selectTime($event)\", \"mouseup\": \"onMouseup($event)\" } }, viewQueries: [{ propertyName: \"clockFace\", first: true, predicate: [\"clockFace\"], descendants: true, static: true }, { propertyName: \"clockHand\", first: true, predicate: [\"clockHand\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: \"<div class=\\\"clock-face\\\" #clockFace>\\n    <div *ngIf=\\\"unit !== timeUnit.MINUTE;else minutesFace\\\" class=\\\"clock-face__container\\\">\\n        <div class=\\\"clock-face__number clock-face__number--outer\\\"\\n             [ngStyle]=\\\"{'transform': 'rotateZ('+ time.angle +'deg) translateX(-50%)'}\\\"\\n             *ngFor=\\\"let time of faceTime | slice: 0 : 12; trackBy: trackByTime\\\">\\n\\t\\t\\t<span [ngStyle]=\\\"{'transform': 'rotateZ(-'+ time.angle +'deg)'}\\\"\\n                  [ngClass]=\\\"{'active': time.time | activeHour: selectedTime.time : isClockFaceDisabled,\\n                   'disabled': time.disabled}\\\">\\n                {{time.time | timeLocalizer: timeUnit.HOUR}}\\n            </span>\\n        </div>\\n        <div class=\\\"clock-face__inner\\\" *ngIf=\\\"faceTime.length > 12\\\"\\n             [style.top]=\\\"'calc(50% - ' + innerClockFaceSize + 'px)'\\\">\\n            <div class=\\\"clock-face__number clock-face__number--inner\\\"\\n                 [ngStyle]=\\\"{'transform': 'rotateZ('+ time.angle +'deg) translateX(-50%)'}\\\"\\n                 [style.height.px]=\\\"innerClockFaceSize\\\"\\n                 *ngFor=\\\"let time of faceTime | slice: 12 : 24; trackBy: trackByTime\\\">\\n\\t\\t\\t<span [ngStyle]=\\\"{'transform': 'rotateZ(-'+ time.angle +'deg)'}\\\"\\n                  [ngClass]=\\\"{'active': time.time | activeHour: selectedTime?.time : isClockFaceDisabled,\\n                   'disabled': time.disabled}\\\">\\n                {{time.time | timeLocalizer: timeUnit.HOUR}}</span>\\n            </div>\\n        </div>\\n    </div>\\n\\n    <span class=\\\"clock-face__clock-hand\\\" [ngClass]=\\\"{'clock-face__clock-hand_minute': unit === timeUnit.MINUTE}\\\"\\n          #clockHand [hidden]=\\\"isClockFaceDisabled\\\"></span>\\n</div>\\n<ng-template #minutesFace>\\n    <div class=\\\"clock-face__container\\\">\\n        <div class=\\\"clock-face__number clock-face__number--outer\\\"\\n             [ngStyle]=\\\"{'transform': 'rotateZ('+ time.angle +'deg) translateX(-50%)'}\\\"\\n             *ngFor=\\\"let time of faceTime; trackBy: trackByTime\\\">\\n\\t<span [ngStyle]=\\\"{'transform': 'rotateZ(-'+ time.angle +'deg)'}\\\"\\n          [ngClass]=\\\"{'active': time.time | activeMinute: selectedTime?.time:minutesGap:isClockFaceDisabled,\\n           'disabled': time.disabled}\\\">\\n\\t{{time.time | minutesFormatter: minutesGap | timeLocalizer: timeUnit.MINUTE}}</span>\\n        </div>\\n    </div>\\n</ng-template>\\n\", styles: [\".clock-face{width:290px;height:290px;border-radius:50%;position:relative;display:flex;justify-content:center;padding:20px;box-sizing:border-box;background-color:#f0f0f0}@supports (background-color: var(--clock-face-background-color)){.clock-face{background-color:var(--clock-face-background-color)}}.clock-face__inner{position:absolute}.clock-face__container{margin-left:-2px}.clock-face__number{position:absolute;transform-origin:0 100%;width:50px;text-align:center;z-index:2}.clock-face__number--outer{height:calc(290px / 2 - 20px)}.clock-face__number--outer>span{font-size:16px;color:#6c6c6c}@supports (color: var(--clock-face-time-inactive-color)){.clock-face__number--outer>span{color:var(--clock-face-time-inactive-color)}}.clock-face__number--inner>span{font-size:14px;color:#929292}@supports (color: var(--clock-face-inner-time-inactive-color)){.clock-face__number--inner>span{color:var(--clock-face-inner-time-inactive-color)}}.clock-face__number>span{-webkit-user-select:none;-moz-user-select:none;user-select:none;width:30px;height:30px;display:flex;justify-content:center;align-items:center;margin:auto;border-radius:50%;font-weight:500;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.clock-face__number>span{font-family:var(--primary-font-family)}}.clock-face__number>span.active{background-color:#00bfff;color:#fff}@supports (background-color: var(--clock-hand-color)){.clock-face__number>span.active{background-color:var(--clock-hand-color);color:var(--clock-face-time-active-color)}}.clock-face__number>span.disabled{color:#c5c5c5}@supports (color: var(--clock-face-time-disabled-color)){.clock-face__number>span.disabled{color:var(--clock-face-time-disabled-color)}}.clock-face__clock-hand{height:103px;width:2px;transform-origin:0 100%;position:absolute;top:calc(50% - 103px);z-index:1;background-color:#00bfff}@supports (background-color: var(--clock-hand-color)){.clock-face__clock-hand{background-color:var(--clock-hand-color)}}.clock-face__clock-hand:after{content:\\\"\\\";width:7px;height:7px;border-radius:50%;background-color:inherit;position:absolute;bottom:-3px;left:-3.5px}.clock-face__clock-hand_minute:before{content:\\\"\\\";width:7px;height:7px;background-color:#fff;border-radius:50%;position:absolute;top:-8px;left:calc(50% - 8px);box-sizing:content-box;border-width:4px;border-style:solid;border-color:#00bfff}@supports (border-color: var(--clock-hand-color)){.clock-face__clock-hand_minute:before{border-color:var(--clock-hand-color)}}@media (max-device-width: 1023px) and (orientation: landscape){.clock-face{width:225px;height:225px;padding:5px}.clock-face__number--outer{height:calc(225px / 2 - 5px)}.clock-face__clock-hand_minute:before{top:0}}\\n\"], directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], pipes: { \"slice\": i1.SlicePipe, \"activeHour\": ActiveHourPipe, \"timeLocalizer\": TimeLocalizerPipe, \"activeMinute\": ActiveMinutePipe, \"minutesFormatter\": MinutesFormatterPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerFaceComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-face',\n                    templateUrl: './ngx-material-timepicker-face.component.html',\n                    styleUrls: ['./ngx-material-timepicker-face.component.scss'],\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], propDecorators: { faceTime: [{\n                type: Input\n            }], selectedTime: [{\n                type: Input\n            }], unit: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], timeChange: [{\n                type: Output\n            }], timeSelected: [{\n                type: Output\n            }], clockFace: [{\n                type: ViewChild,\n                args: ['clockFace', { static: true }]\n            }], clockHand: [{\n                type: ViewChild,\n                args: ['clockHand', { static: true }]\n            }], onMousedown: [{\n                type: HostListener,\n                args: ['mousedown', ['$event']]\n            }], selectTime: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['touchmove', ['$event.changedTouches[0]']]\n            }, {\n                type: HostListener,\n                args: ['touchend', ['$event.changedTouches[0]']]\n            }, {\n                type: HostListener,\n                args: ['mousemove', ['$event']]\n            }], onMouseup: [{\n                type: HostListener,\n                args: ['mouseup', ['$event']]\n            }] } });\nfunction roundAngle(angle, step) {\n    return Math.round(angle / step) * step;\n}\nfunction countAngleByCords(x0, y0, x, y, currentAngle) {\n    if (y > y0 && x >= x0) { // II quarter\n        return 180 - currentAngle;\n    }\n    else if (y > y0 && x < x0) { // III quarter\n        return 180 + currentAngle;\n    }\n    else if (y < y0 && x < x0) { // IV quarter\n        return 360 - currentAngle;\n    }\n    else { // I quarter\n        return currentAngle;\n    }\n}\n\nclass NgxMaterialTimepicker24HoursFaceComponent extends NgxMaterialTimepickerHoursFace {\n    constructor() {\n        super(24);\n    }\n    ngAfterContentInit() {\n        this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n            min: this.minTime,\n            max: this.maxTime,\n            format: this.format\n        });\n    }\n}\nNgxMaterialTimepicker24HoursFaceComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepicker24HoursFaceComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepicker24HoursFaceComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepicker24HoursFaceComponent, selector: \"ngx-material-timepicker-24-hours-face\", usesInheritance: true, ngImport: i0, template: \"<ngx-material-timepicker-face [selectedTime]=\\\"selectedHour\\\" [faceTime]=\\\"hoursList\\\" [format]=\\\"format\\\"\\n                              (timeChange)=\\\"hourChange.next($event)\\\"\\n                              (timeSelected)=\\\"onTimeSelected($event)\\\"></ngx-material-timepicker-face>\\n\", components: [{ type: NgxMaterialTimepickerFaceComponent, selector: \"ngx-material-timepicker-face\", inputs: [\"faceTime\", \"selectedTime\", \"unit\", \"format\", \"minutesGap\"], outputs: [\"timeChange\", \"timeSelected\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepicker24HoursFaceComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-24-hours-face',\n                    templateUrl: 'ngx-material-timepicker-24-hours-face.component.html',\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass NgxMaterialTimepicker12HoursFaceComponent extends NgxMaterialTimepickerHoursFace {\n    constructor() {\n        super(12);\n    }\n    ngOnChanges(changes) {\n        if (changes['period'] && changes['period'].currentValue) {\n            this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n                min: this.minTime,\n                max: this.maxTime,\n                format: this.format,\n                period: this.period\n            });\n        }\n    }\n}\nNgxMaterialTimepicker12HoursFaceComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepicker12HoursFaceComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepicker12HoursFaceComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepicker12HoursFaceComponent, selector: \"ngx-material-timepicker-12-hours-face\", inputs: { period: \"period\" }, usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ngx-material-timepicker-face [selectedTime]=\\\"selectedHour\\\" [faceTime]=\\\"hoursList\\\"\\n                              (timeChange)=\\\"hourChange.next($event)\\\" (timeSelected)=\\\"onTimeSelected($event)\\\"></ngx-material-timepicker-face>\\n\", components: [{ type: NgxMaterialTimepickerFaceComponent, selector: \"ngx-material-timepicker-face\", inputs: [\"faceTime\", \"selectedTime\", \"unit\", \"format\", \"minutesGap\"], outputs: [\"timeChange\", \"timeSelected\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepicker12HoursFaceComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-12-hours-face',\n                    templateUrl: 'ngx-material-timepicker-12-hours-face.component.html',\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { period: [{\n                type: Input\n            }] } });\n\nclass NgxMaterialTimepickerMinutesFaceComponent {\n    constructor() {\n        this.minutesList = [];\n        this.timeUnit = TimeUnit;\n        this.minuteChange = new EventEmitter();\n    }\n    ngOnChanges(changes) {\n        if (changes['period'] && changes['period'].currentValue) {\n            const minutes = TimepickerTimeUtils.getMinutes(this.minutesGap);\n            this.minutesList = TimepickerTimeUtils.disableMinutes(minutes, this.selectedHour, {\n                min: this.minTime,\n                max: this.maxTime,\n                format: this.format,\n                period: this.period\n            });\n        }\n    }\n}\nNgxMaterialTimepickerMinutesFaceComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerMinutesFaceComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerMinutesFaceComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerMinutesFaceComponent, selector: \"ngx-material-timepicker-minutes-face\", inputs: { selectedMinute: \"selectedMinute\", selectedHour: \"selectedHour\", period: \"period\", minTime: \"minTime\", maxTime: \"maxTime\", format: \"format\", minutesGap: \"minutesGap\" }, outputs: { minuteChange: \"minuteChange\" }, usesOnChanges: true, ngImport: i0, template: \"<ngx-material-timepicker-face [faceTime]=\\\"minutesList\\\" [selectedTime]=\\\"selectedMinute\\\"\\n                              [minutesGap]=\\\"minutesGap\\\"\\n                              (timeChange)=\\\"minuteChange.next($event)\\\" [unit]=\\\"timeUnit.MINUTE\\\"></ngx-material-timepicker-face>\\n\", components: [{ type: NgxMaterialTimepickerFaceComponent, selector: \"ngx-material-timepicker-face\", inputs: [\"faceTime\", \"selectedTime\", \"unit\", \"format\", \"minutesGap\"], outputs: [\"timeChange\", \"timeSelected\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerMinutesFaceComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-minutes-face',\n                    templateUrl: './ngx-material-timepicker-minutes-face.component.html'\n                }]\n        }], propDecorators: { selectedMinute: [{\n                type: Input\n            }], selectedHour: [{\n                type: Input\n            }], period: [{\n                type: Input\n            }], minTime: [{\n                type: Input\n            }], maxTime: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], minuteChange: [{\n                type: Output\n            }] } });\n\nclass NgxMaterialTimepickerButtonComponent {\n}\nNgxMaterialTimepickerButtonComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerButtonComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerButtonComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerButtonComponent, selector: \"ngx-material-timepicker-button\", ngImport: i0, template: \"<button class=\\\"timepicker-button\\\" type=\\\"button\\\">\\n  <span><ng-content></ng-content></span>\\n</button>\\n\", styles: [\".timepicker-button{display:inline-block;height:36px;min-width:88px;line-height:36px;border:12px;border-radius:2px;background-color:transparent;text-align:center;transition:all .45s cubic-bezier(.23,1,.32,1);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:relative;cursor:pointer;outline:none;color:#00bfff}@supports (color: var(--button-color)){.timepicker-button{color:var(--button-color)}}.timepicker-button:hover,.timepicker-button:focus{background-color:#9993}.timepicker-button>span{font-size:14px;text-transform:uppercase;font-weight:600;padding-left:16px;padding-right:16px;font-family:\\\"Roboto\\\",sans-serif}@supports (font-family: var(--primary-font-family)){.timepicker-button>span{font-family:var(--primary-font-family)}}\\n\"] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerButtonComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-button',\n                    templateUrl: './ngx-material-timepicker-button.component.html',\n                    styleUrls: ['./ngx-material-timepicker-button.component.scss']\n                }]\n        }] });\n\nclass OverlayDirective {\n    constructor(eventService) {\n        this.eventService = eventService;\n    }\n    onClick(e) {\n        if (!this.preventClick) {\n            this.eventService.dispatchEvent(e);\n        }\n        e.preventDefault();\n    }\n}\nOverlayDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: OverlayDirective, deps: [{ token: NgxMaterialTimepickerEventService }], target: i0.ɵɵFactoryTarget.Directive });\nOverlayDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: OverlayDirective, selector: \"[overlay]\", inputs: { preventClick: [\"overlay\", \"preventClick\"] }, host: { listeners: { \"click\": \"onClick($event)\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: OverlayDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[overlay]'\n                }]\n        }], ctorParameters: function () { return [{ type: NgxMaterialTimepickerEventService }]; }, propDecorators: { preventClick: [{\n                type: Input,\n                args: ['overlay']\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\nclass NgxMaterialTimepickerThemeDirective {\n    constructor(elementRef) {\n        this.element = elementRef.nativeElement;\n    }\n    ngAfterViewInit() {\n        if (this.theme) {\n            this.setTheme(this.theme);\n        }\n    }\n    setTheme(theme) {\n        for (const val in theme) {\n            if (theme.hasOwnProperty(val)) {\n                if (typeof theme[val] === 'string') {\n                    for (const prop in theme) {\n                        if (theme.hasOwnProperty(prop)) {\n                            this.element.style.setProperty(`--${camelCaseToDash(prop)}`, theme[prop]);\n                        }\n                    }\n                    return;\n                }\n                this.setTheme(theme[val]);\n            }\n        }\n    }\n}\nNgxMaterialTimepickerThemeDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerThemeDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgxMaterialTimepickerThemeDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerThemeDirective, selector: \"[ngxMaterialTimepickerTheme]\", inputs: { theme: [\"ngxMaterialTimepickerTheme\", \"theme\"] }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerThemeDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ngxMaterialTimepickerTheme]' }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { theme: [{\n                type: Input,\n                args: ['ngxMaterialTimepickerTheme']\n            }] } });\nfunction camelCaseToDash(myStr) {\n    return myStr.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nvar AnimationState;\n(function (AnimationState) {\n    AnimationState[\"ENTER\"] = \"enter\";\n    AnimationState[\"LEAVE\"] = \"leave\";\n})(AnimationState || (AnimationState = {}));\nclass NgxMaterialTimepickerContainerComponent {\n    constructor(timepickerService, eventService, locale) {\n        this.timepickerService = timepickerService;\n        this.eventService = eventService;\n        this.locale = locale;\n        this.timeUnit = TimeUnit;\n        this.activeTimeUnit = TimeUnit.HOUR;\n        this.unsubscribe = new Subject();\n    }\n    set defaultTime(time) {\n        this._defaultTime = time;\n        this.setDefaultTime(time);\n    }\n    get defaultTime() {\n        return this._defaultTime;\n    }\n    onKeydown(e) {\n        this.eventService.dispatchEvent(e);\n        e.stopPropagation();\n    }\n    ngOnInit() {\n        this.animationState = !this.disableAnimation && AnimationState.ENTER;\n        this.defineTime();\n        this.selectedHour = this.timepickerService.selectedHour\n            .pipe(shareReplay({ bufferSize: 1, refCount: true }));\n        this.selectedMinute = this.timepickerService.selectedMinute\n            .pipe(shareReplay({ bufferSize: 1, refCount: true }));\n        this.selectedPeriod = this.timepickerService.selectedPeriod\n            .pipe(shareReplay({ bufferSize: 1, refCount: true }));\n        this.timepickerBaseRef.timeUpdated.pipe(takeUntil(this.unsubscribe))\n            .subscribe(this.setDefaultTime.bind(this));\n    }\n    onHourChange(hour) {\n        this.timepickerService.hour = hour;\n        this.onTimeChange();\n    }\n    onHourSelected(hour) {\n        if (!this.hoursOnly) {\n            this.changeTimeUnit(TimeUnit.MINUTE);\n        }\n        this.timepickerBaseRef.hourSelected.next(hour);\n    }\n    onMinuteChange(minute) {\n        this.timepickerService.minute = minute;\n        this.onTimeChange();\n    }\n    changePeriod(period) {\n        this.timepickerService.period = period;\n        this.onTimeChange();\n    }\n    changeTimeUnit(unit) {\n        this.activeTimeUnit = unit;\n    }\n    setTime() {\n        this.timepickerBaseRef.timeSet.next(this.timepickerService.getFullTime(this.format));\n        this.close();\n    }\n    close() {\n        if (this.disableAnimation) {\n            this.timepickerBaseRef.close();\n            return;\n        }\n        this.animationState = AnimationState.LEAVE;\n    }\n    animationDone(event) {\n        if (event.phaseName === 'done' && event.toState === AnimationState.LEAVE) {\n            this.timepickerBaseRef.close();\n        }\n    }\n    ngOnDestroy() {\n        this.unsubscribe.next();\n        this.unsubscribe.complete();\n    }\n    setDefaultTime(time) {\n        this.timepickerService.setDefaultTimeIfAvailable(time, this.minTime, this.maxTime, this.format, this.minutesGap);\n    }\n    defineTime() {\n        const minTime = this.minTime;\n        if (minTime && (!this.time && !this.defaultTime)) {\n            const time = TimeAdapter.fromDateTimeToString(minTime, this.format);\n            this.setDefaultTime(time);\n        }\n    }\n    onTimeChange() {\n        const time = TimeAdapter.toLocaleTimeString(this.timepickerService.getFullTime(this.format), {\n            locale: this.locale,\n            format: this.format\n        });\n        this.timepickerBaseRef.timeChanged.emit(time);\n    }\n}\nNgxMaterialTimepickerContainerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerContainerComponent, deps: [{ token: NgxMaterialTimepickerService }, { token: NgxMaterialTimepickerEventService }, { token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerContainerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerContainerComponent, selector: \"ngx-material-timepicker-container\", inputs: { defaultTime: \"defaultTime\" }, host: { listeners: { \"keydown\": \"onKeydown($event)\" } }, providers: [NgxMaterialTimepickerService], ngImport: i0, template: \"<div class=\\\"timepicker-backdrop-overlay\\\" [overlay]=\\\"preventOverlayClick\\\"\\n     [ngClass]=\\\"{'timepicker-backdrop-overlay--transparent': appendToInput}\\\"></div>\\n<div class=\\\"timepicker-overlay\\\">\\n    <ngx-material-timepicker-content [appendToInput]=\\\"appendToInput\\\"\\n                                     [inputElement]=\\\"inputElement\\\"\\n                                     [ngxMaterialTimepickerTheme]=\\\"theme\\\">\\n        <div class=\\\"timepicker\\\"\\n             [@timepicker]=\\\"animationState\\\"\\n             (@timepicker.done)=\\\"animationDone($event)\\\"\\n             [ngClass]=\\\"timepickerClass\\\">\\n            <header class=\\\"timepicker__header\\\">\\n                <ngx-material-timepicker-dial [format]=\\\"format\\\" [hour]=\\\"(selectedHour | async)?.time\\\"\\n                                              [minute]=\\\"(selectedMinute | async)?.time\\\"\\n                                              [period]=\\\"selectedPeriod | async\\\"\\n                                              [activeTimeUnit]=\\\"activeTimeUnit\\\"\\n                                              [minTime]=\\\"minTime\\\"\\n                                              [maxTime]=\\\"maxTime\\\"\\n                                              [isEditable]=\\\"enableKeyboardInput\\\"\\n                                              [editableHintTmpl]=\\\"editableHintTmpl\\\"\\n                                              [minutesGap]=\\\"minutesGap\\\"\\n                                              [hoursOnly]=\\\"hoursOnly\\\"\\n                                              (periodChanged)=\\\"changePeriod($event)\\\"\\n                                              (timeUnitChanged)=\\\"changeTimeUnit($event)\\\"\\n                                              (hourChanged)=\\\"onHourChange($event)\\\"\\n                                              (minuteChanged)=\\\"onMinuteChange($event)\\\"\\n                ></ngx-material-timepicker-dial>\\n            </header>\\n            <div class=\\\"timepicker__main-content\\\">\\n                <div class=\\\"timepicker__body\\\" [ngSwitch]=\\\"activeTimeUnit\\\">\\n                    <div *ngSwitchCase=\\\"timeUnit.HOUR\\\">\\n                        <ngx-material-timepicker-24-hours-face *ngIf=\\\"format === 24;else ampmHours\\\"\\n                                                               (hourChange)=\\\"onHourChange($event)\\\"\\n                                                               [selectedHour]=\\\"selectedHour | async\\\"\\n                                                               [minTime]=\\\"minTime\\\"\\n                                                               [maxTime]=\\\"maxTime\\\"\\n                                                               [format]=\\\"format\\\"\\n                                                               (hourSelected)=\\\"onHourSelected($event)\\\"></ngx-material-timepicker-24-hours-face>\\n                        <ng-template #ampmHours>\\n                            <ngx-material-timepicker-12-hours-face\\n                                (hourChange)=\\\"onHourChange($event)\\\"\\n                                [selectedHour]=\\\"selectedHour | async\\\"\\n                                [period]=\\\"selectedPeriod | async\\\"\\n                                [minTime]=\\\"minTime\\\"\\n                                [maxTime]=\\\"maxTime\\\"\\n                                (hourSelected)=\\\"onHourSelected($event)\\\"></ngx-material-timepicker-12-hours-face>\\n                        </ng-template>\\n                    </div>\\n                    <ngx-material-timepicker-minutes-face *ngSwitchCase=\\\"timeUnit.MINUTE\\\"\\n                                                          [selectedMinute]=\\\"selectedMinute | async\\\"\\n                                                          [selectedHour]=\\\"(selectedHour | async)?.time\\\"\\n                                                          [minTime]=\\\"minTime\\\"\\n                                                          [maxTime]=\\\"maxTime\\\"\\n                                                          [format]=\\\"format\\\"\\n                                                          [period]=\\\"selectedPeriod | async\\\"\\n                                                          [minutesGap]=\\\"minutesGap\\\"\\n                                                          (minuteChange)=\\\"onMinuteChange($event)\\\"></ngx-material-timepicker-minutes-face>\\n                </div>\\n                <div class=\\\"timepicker__actions\\\">\\n                    <div (click)=\\\"close()\\\">\\n                        <!--suppress HtmlUnknownAttribute -->\\n                        <ng-container\\n                            *ngTemplateOutlet=\\\"cancelBtnTmpl ? cancelBtnTmpl : cancelBtnDefault\\\"></ng-container>\\n                    </div>\\n                    <div (click)=\\\"setTime()\\\">\\n                        <!--suppress HtmlUnknownAttribute -->\\n                        <ng-container\\n                            *ngTemplateOutlet=\\\"confirmBtnTmpl ? confirmBtnTmpl : confirmBtnDefault\\\"></ng-container>\\n                    </div>\\n                </div>\\n            </div>\\n        </div>\\n    </ngx-material-timepicker-content>\\n</div>\\n<ng-template #cancelBtnDefault>\\n    <ngx-material-timepicker-button>Cancel</ngx-material-timepicker-button>\\n</ng-template>\\n<ng-template #confirmBtnDefault>\\n    <ngx-material-timepicker-button>Ok</ngx-material-timepicker-button>\\n</ng-template>\\n\", styles: [\":host{--body-background-color: #fff;--primary-font-family: \\\"Roboto\\\", sans-serif;--button-color: deepskyblue;--dial-active-color: #fff;--dial-inactive-color: rgba(255, 255, 255, .5);--dial-background-color: deepskyblue;--dial-editable-active-color: deepskyblue;--dial-editable-background-color: #fff;--clock-face-time-active-color: #fff;--clock-face-time-inactive-color: #6c6c6c;--clock-face-inner-time-inactive-color: #929292;--clock-face-time-disabled-color: #c5c5c5;--clock-face-background-color: #f0f0f0;--clock-hand-color: deepskyblue}.timepicker-backdrop-overlay{position:fixed;top:0;bottom:0;right:0;left:0;background-color:#0000004d;z-index:999;pointer-events:auto}.timepicker-backdrop-overlay--transparent{background-color:transparent}.timepicker-overlay{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;z-index:999;pointer-events:none}.timepicker{width:300px;border-radius:2px;box-shadow:#00000040 0 14px 45px,#00000038 0 10px 18px;outline:none;position:static;z-index:999;pointer-events:auto}.timepicker__header{padding:15px 30px;background-color:#00bfff}@supports (background-color: var(--dial-background-color)){.timepicker__header{background-color:var(--dial-background-color)}}.timepicker__body{padding:15px 5px;display:flex;justify-content:center;align-items:center;background-color:#fff}@supports (background-color: var(--body-background-color)){.timepicker__body{background-color:var(--body-background-color)}}.timepicker__actions{display:flex;justify-content:flex-end;padding:15px;background-color:#fff}@supports (background-color: var(--body-background-color)){.timepicker__actions{background-color:var(--body-background-color)}}@media (max-device-width: 1023px) and (orientation: landscape){.timepicker{display:flex;width:515px}.timepicker__header{display:flex;align-items:center}.timepicker__main-content{display:flex;flex-direction:column;width:100%}.timepicker__actions{padding:5px;margin-top:-1px}}\\n\"], components: [{ type: NgxMaterialTimepickerContentComponent, selector: \"ngx-material-timepicker-content\", inputs: [\"appendToInput\", \"inputElement\"] }, { type: NgxMaterialTimepickerDialComponent, selector: \"ngx-material-timepicker-dial\", inputs: [\"editableHintTmpl\", \"hour\", \"minute\", \"format\", \"period\", \"activeTimeUnit\", \"minTime\", \"maxTime\", \"isEditable\", \"minutesGap\", \"hoursOnly\"], outputs: [\"periodChanged\", \"timeUnitChanged\", \"hourChanged\", \"minuteChanged\"] }, { type: NgxMaterialTimepicker24HoursFaceComponent, selector: \"ngx-material-timepicker-24-hours-face\" }, { type: NgxMaterialTimepicker12HoursFaceComponent, selector: \"ngx-material-timepicker-12-hours-face\", inputs: [\"period\"] }, { type: NgxMaterialTimepickerMinutesFaceComponent, selector: \"ngx-material-timepicker-minutes-face\", inputs: [\"selectedMinute\", \"selectedHour\", \"period\", \"minTime\", \"maxTime\", \"format\", \"minutesGap\"], outputs: [\"minuteChange\"] }, { type: NgxMaterialTimepickerButtonComponent, selector: \"ngx-material-timepicker-button\" }], directives: [{ type: OverlayDirective, selector: \"[overlay]\", inputs: [\"overlay\"] }, { type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: NgxMaterialTimepickerThemeDirective, selector: \"[ngxMaterialTimepickerTheme]\", inputs: [\"ngxMaterialTimepickerTheme\"] }, { type: i1.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { type: i1.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], pipes: { \"async\": i1.AsyncPipe }, animations: [\n        trigger('timepicker', [\n            transition(`* => ${AnimationState.ENTER}`, [\n                style({ transform: 'translateY(-30%)' }),\n                animate('0.2s ease-out', style({ transform: 'translateY(0)' }))\n            ]),\n            transition(`${AnimationState.ENTER} => ${AnimationState.LEAVE}`, [\n                style({ transform: 'translateY(0)', opacity: 1 }),\n                animate('0.2s ease-out', style({ transform: 'translateY(-30%)', opacity: 0 }))\n            ])\n        ])\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerContainerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-container',\n                    templateUrl: './ngx-material-timepicker-container.component.html',\n                    styleUrls: ['./ngx-material-timepicker-container.component.scss'],\n                    animations: [\n                        trigger('timepicker', [\n                            transition(`* => ${AnimationState.ENTER}`, [\n                                style({ transform: 'translateY(-30%)' }),\n                                animate('0.2s ease-out', style({ transform: 'translateY(0)' }))\n                            ]),\n                            transition(`${AnimationState.ENTER} => ${AnimationState.LEAVE}`, [\n                                style({ transform: 'translateY(0)', opacity: 1 }),\n                                animate('0.2s ease-out', style({ transform: 'translateY(-30%)', opacity: 0 }))\n                            ])\n                        ])\n                    ],\n                    providers: [NgxMaterialTimepickerService]\n                }]\n        }], ctorParameters: function () { return [{ type: NgxMaterialTimepickerService }, { type: NgxMaterialTimepickerEventService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; }, propDecorators: { defaultTime: [{\n                type: Input\n            }], onKeydown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass DomService {\n    constructor(cfr, appRef, injector, document) {\n        this.cfr = cfr;\n        this.appRef = appRef;\n        this.injector = injector;\n        this.document = document;\n    }\n    appendTimepickerToBody(timepicker, config) {\n        this.componentRef = this.cfr.resolveComponentFactory(timepicker).create(this.injector);\n        Object.keys(config).forEach(key => this.componentRef.instance[key] = config[key]);\n        this.appRef.attachView(this.componentRef.hostView);\n        const domElement = this.componentRef.hostView\n            .rootNodes[0];\n        this.document.body.appendChild(domElement);\n    }\n    destroyTimepicker() {\n        this.componentRef.destroy();\n        this.appRef.detachView(this.componentRef.hostView);\n    }\n}\nDomService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DomService, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.Injector }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nDomService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DomService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DomService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst ESCAPE = 27;\nclass NgxMaterialTimepickerComponent {\n    constructor(eventService, domService) {\n        this.eventService = eventService;\n        this.domService = domService;\n        this.timeUpdated = new Subject();\n        this.isEsc = true;\n        this.hoursOnly = false;\n        this.timeSet = new EventEmitter();\n        this.opened = new EventEmitter();\n        this.closed = new EventEmitter();\n        this.hourSelected = new EventEmitter();\n        this.timeChanged = new EventEmitter();\n        this.unsubscribe = new Subject();\n    }\n    /**\n     * @deprecated Since version 5.1.1. Will be deleted on version 6.0.0. Use @Input() theme instead\n     */\n    set ngxMaterialTimepickerTheme(theme) {\n        console.warn(`'ngxMaterialTimepickerTheme' is deprecated. Use 'theme' instead`);\n        this._ngxMaterialTimepickerTheme = theme;\n    }\n    set format(value) {\n        this._format = value === 24 ? 24 : 12;\n    }\n    get format() {\n        return this.timepickerInput ? this.timepickerInput.format : this._format;\n    }\n    set minutesGap(gap) {\n        if (gap == null) {\n            return;\n        }\n        gap = Math.floor(gap);\n        this._minutesGap = gap <= 59 ? gap : 1;\n    }\n    get minutesGap() {\n        return this._minutesGap;\n    }\n    get minTime() {\n        return this.timepickerInput ? this.timepickerInput.min : this.min;\n    }\n    get maxTime() {\n        return this.timepickerInput ? this.timepickerInput.max : this.max;\n    }\n    get disabled() {\n        return this.timepickerInput && this.timepickerInput.disabled;\n    }\n    get time() {\n        return this.timepickerInput && this.timepickerInput.value;\n    }\n    get inputElement() {\n        return this.timepickerInput && this.timepickerInput.element;\n    }\n    /***\n     * Register an input with this timepicker.\n     * input - The timepicker input to register with this timepicker\n     */\n    registerInput(input) {\n        if (this.timepickerInput) {\n            throw Error('A Timepicker can only be associated with a single input.');\n        }\n        this.timepickerInput = input;\n    }\n    open() {\n        this.domService.appendTimepickerToBody(NgxMaterialTimepickerContainerComponent, {\n            timepickerBaseRef: this,\n            time: this.time,\n            defaultTime: this.defaultTime,\n            maxTime: this.maxTime,\n            minTime: this.minTime,\n            format: this.format,\n            minutesGap: this.minutesGap,\n            disableAnimation: this.disableAnimation,\n            cancelBtnTmpl: this.cancelBtnTmpl,\n            confirmBtnTmpl: this.confirmBtnTmpl,\n            editableHintTmpl: this.editableHintTmpl,\n            disabled: this.disabled,\n            enableKeyboardInput: this.enableKeyboardInput,\n            preventOverlayClick: this.preventOverlayClick,\n            appendToInput: this.appendToInput,\n            hoursOnly: this.hoursOnly,\n            theme: this.theme || this._ngxMaterialTimepickerTheme,\n            timepickerClass: this.timepickerClass,\n            inputElement: this.inputElement\n        });\n        this.opened.next();\n        this.subscribeToEvents();\n    }\n    close() {\n        this.domService.destroyTimepicker();\n        this.closed.next();\n        this.unsubscribeFromEvents();\n    }\n    updateTime(time) {\n        this.timeUpdated.next(time);\n    }\n    subscribeToEvents() {\n        merge(this.eventService.backdropClick, this.eventService.keydownEvent.pipe(filter(e => e.keyCode === ESCAPE && this.isEsc)))\n            .pipe(takeUntil(this.unsubscribe))\n            .subscribe(() => this.close());\n    }\n    unsubscribeFromEvents() {\n        this.unsubscribe.next();\n        this.unsubscribe.complete();\n    }\n}\nNgxMaterialTimepickerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerComponent, deps: [{ token: NgxMaterialTimepickerEventService }, { token: DomService }], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerComponent, selector: \"ngx-material-timepicker\", inputs: { cancelBtnTmpl: \"cancelBtnTmpl\", editableHintTmpl: \"editableHintTmpl\", confirmBtnTmpl: \"confirmBtnTmpl\", isEsc: [\"ESC\", \"isEsc\"], enableKeyboardInput: \"enableKeyboardInput\", preventOverlayClick: \"preventOverlayClick\", disableAnimation: \"disableAnimation\", appendToInput: \"appendToInput\", hoursOnly: \"hoursOnly\", defaultTime: \"defaultTime\", timepickerClass: \"timepickerClass\", theme: \"theme\", min: \"min\", max: \"max\", ngxMaterialTimepickerTheme: \"ngxMaterialTimepickerTheme\", format: \"format\", minutesGap: \"minutesGap\" }, outputs: { timeSet: \"timeSet\", opened: \"opened\", closed: \"closed\", hourSelected: \"hourSelected\", timeChanged: \"timeChanged\" }, ngImport: i0, template: '', isInline: true });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker',\n                    template: '',\n                }]\n        }], ctorParameters: function () { return [{ type: NgxMaterialTimepickerEventService }, { type: DomService }]; }, propDecorators: { cancelBtnTmpl: [{\n                type: Input\n            }], editableHintTmpl: [{\n                type: Input\n            }], confirmBtnTmpl: [{\n                type: Input\n            }], isEsc: [{\n                type: Input,\n                args: ['ESC']\n            }], enableKeyboardInput: [{\n                type: Input\n            }], preventOverlayClick: [{\n                type: Input\n            }], disableAnimation: [{\n                type: Input\n            }], appendToInput: [{\n                type: Input\n            }], hoursOnly: [{\n                type: Input\n            }], defaultTime: [{\n                type: Input\n            }], timepickerClass: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], ngxMaterialTimepickerTheme: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], timeSet: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], hourSelected: [{\n                type: Output\n            }], timeChanged: [{\n                type: Output\n            }] } });\n\n/* To override a default toggle icon */\nclass NgxMaterialTimepickerToggleIconDirective {\n}\nNgxMaterialTimepickerToggleIconDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerToggleIconDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nNgxMaterialTimepickerToggleIconDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerToggleIconDirective, selector: \"[ngxMaterialTimepickerToggleIcon]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerToggleIconDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ngxMaterialTimepickerToggleIcon]' }]\n        }] });\n\nclass NgxMaterialTimepickerToggleComponent {\n    get disabled() {\n        return this._disabled === undefined ? this.timepicker.disabled : this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    open(event) {\n        if (this.timepicker) {\n            this.timepicker.open();\n            event.stopPropagation();\n        }\n    }\n}\nNgxMaterialTimepickerToggleComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerToggleComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxMaterialTimepickerToggleComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxMaterialTimepickerToggleComponent, selector: \"ngx-material-timepicker-toggle\", inputs: { timepicker: [\"for\", \"timepicker\"], disabled: \"disabled\" }, queries: [{ propertyName: \"customIcon\", first: true, predicate: NgxMaterialTimepickerToggleIconDirective, descendants: true, static: true }], ngImport: i0, template: \"<button class=\\\"ngx-material-timepicker-toggle\\\" (click)=\\\"open($event)\\\" [disabled]=\\\"disabled\\\" type=\\\"button\\\">\\n    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" *ngIf=\\\"!customIcon\\\">\\n        <path\\n            d=\\\"M 12 2 C 6.4889971 2 2 6.4889971 2 12 C 2 17.511003                   6.4889971 22 12 22 C 17.511003 22 22 17.511003 22 12 C 22 6.4889971 17.511003 2 12 2 z M 12 4 C 16.430123 4 20 7.5698774 20 12 C 20 16.430123 16.430123 20 12 20 C 7.5698774 20 4 16.430123 4 12 C 4 7.5698774 7.5698774 4 12 4 z M 11 6 L 11 12.414062 L 15.292969 16.707031 L 16.707031 15.292969 L 13 11.585938 L 13 6 L 11 6 z\\\"/>\\n    </svg>\\n\\n    <ng-content select=\\\"[ngxMaterialTimepickerToggleIcon]\\\"></ng-content>\\n</button>\\n\", styles: [\".ngx-material-timepicker-toggle{display:flex;justify-content:center;align-items:center;padding:4px;background-color:transparent;border-radius:50%;text-align:center;border:none;outline:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;transition:background-color .3s;cursor:pointer}.ngx-material-timepicker-toggle:focus{background-color:#00000012}\\n\"], directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerToggleComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-material-timepicker-toggle',\n                    templateUrl: 'ngx-material-timepicker-toggle.component.html',\n                    styleUrls: ['ngx-material-timepicker-toggle.component.scss']\n                }]\n        }], propDecorators: { timepicker: [{\n                type: Input,\n                args: ['for']\n            }], disabled: [{\n                type: Input\n            }], customIcon: [{\n                type: ContentChild,\n                args: [NgxMaterialTimepickerToggleIconDirective, { static: true }]\n            }] } });\n\nclass TimepickerDirective {\n    constructor(elementRef, locale) {\n        this.elementRef = elementRef;\n        this.locale = locale;\n        this._format = 12;\n        this._value = '';\n        this.timepickerSubscriptions = [];\n        this.onTouched = () => {\n        };\n        this.onChange = () => {\n        };\n    }\n    set format(value) {\n        this._format = value === 24 ? 24 : 12;\n        const isDynamicallyChanged = value && (this.previousFormat && this.previousFormat !== this._format);\n        if (isDynamicallyChanged) {\n            this.value = this._value;\n            this._timepicker.updateTime(this._value);\n        }\n        this.previousFormat = this._format;\n    }\n    get format() {\n        return this._format;\n    }\n    set min(value) {\n        console.log(value);\n        if (typeof value === 'string') {\n            this._min = TimeAdapter.parseTime(value, { locale: this.locale, format: this.format });\n            return;\n        }\n        this._min = value;\n    }\n    get min() {\n        return this._min;\n    }\n    set max(value) {\n        if (typeof value === 'string') {\n            this._max = TimeAdapter.parseTime(value, { locale: this.locale, format: this.format });\n            return;\n        }\n        this._max = value;\n    }\n    get max() {\n        return this._max;\n    }\n    set timepicker(picker) {\n        this.registerTimepicker(picker);\n    }\n    set value(value) {\n        if (!value) {\n            this._value = '';\n            this.updateInputValue();\n            return;\n        }\n        this.setTimeIfAvailable(value);\n    }\n    get value() {\n        if (!this._value) {\n            return '';\n        }\n        return TimeAdapter.toLocaleTimeString(this._value, { format: this.format, locale: this.locale });\n    }\n    get element() {\n        return this.elementRef && this.elementRef.nativeElement;\n    }\n    set defaultTime(time) {\n        this._timepicker.defaultTime = TimeAdapter.formatTime(time, { locale: this.locale, format: this.format });\n    }\n    updateValue(value) {\n        this.value = value;\n        this.onChange(value);\n    }\n    ngOnChanges(changes) {\n        var _a;\n        const value = (_a = changes === null || changes === void 0 ? void 0 : changes.value) === null || _a === void 0 ? void 0 : _a.currentValue;\n        if (value) {\n            // Call setTimeIfAvailable after @Input setters\n            this.setTimeIfAvailable(value);\n            this.defaultTime = value;\n        }\n    }\n    onClick(event) {\n        if (!this.disableClick) {\n            this._timepicker.open();\n            event.stopPropagation();\n        }\n    }\n    writeValue(value) {\n        this.value = value;\n        if (value) {\n            this.defaultTime = value;\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    ngOnDestroy() {\n        this.timepickerSubscriptions.forEach(s => s.unsubscribe());\n    }\n    registerTimepicker(picker) {\n        if (picker) {\n            this._timepicker = picker;\n            this._timepicker.registerInput(this);\n            this.timepickerSubscriptions.push(this._timepicker.timeSet.subscribe((time) => {\n                this.value = time;\n                this.onChange(this.value);\n                this.onTouched();\n                this.defaultTime = this._value;\n            }));\n        }\n        else {\n            throw new Error('NgxMaterialTimepickerComponent is not defined.' +\n                ' Please make sure you passed the timepicker to ngxTimepicker directive');\n        }\n    }\n    updateInputValue() {\n        this.elementRef.nativeElement.value = this.value;\n    }\n    setTimeIfAvailable(value) {\n        var _a;\n        const time = TimeAdapter.formatTime(value, { locale: this.locale, format: this.format });\n        const isAvailable = TimeAdapter.isTimeAvailable(time, this._min, this._max, 'minutes', (_a = this._timepicker) === null || _a === void 0 ? void 0 : _a.minutesGap, this._format);\n        if (isAvailable) {\n            this._value = time;\n            this.updateInputValue();\n        }\n        else {\n            this.value = null;\n            console.warn('Selected time doesn\\'t match min or max value');\n        }\n    }\n}\nTimepickerDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimepickerDirective, deps: [{ token: i0.ElementRef }, { token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Directive });\nTimepickerDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.17\", type: TimepickerDirective, selector: \"[ngxTimepicker]\", inputs: { format: \"format\", min: \"min\", max: \"max\", timepicker: [\"ngxTimepicker\", \"timepicker\"], value: \"value\", disabled: \"disabled\", disableClick: \"disableClick\" }, host: { listeners: { \"change\": \"updateValue($event.target.value)\", \"blur\": \"onTouched()\", \"click\": \"onClick($event)\" }, properties: { \"disabled\": \"disabled\" } }, providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: TimepickerDirective,\n            multi: true\n        }\n    ], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimepickerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ngxTimepicker]',\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: TimepickerDirective,\n                            multi: true\n                        }\n                    ],\n                    host: {\n                        '[disabled]': 'disabled',\n                        '(change)': 'updateValue($event.target.value)',\n                        '(blur)': 'onTouched()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; }, propDecorators: { format: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], timepicker: [{\n                type: Input,\n                args: ['ngxTimepicker']\n            }], value: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], disableClick: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\nclass TimeFormatterPipe {\n    transform(time, timeUnit) {\n        if (time == null || time === '') {\n            return time;\n        }\n        switch (timeUnit) {\n            case TimeUnit.HOUR:\n                return DateTime.fromObject({ hour: +time }).toFormat('HH');\n            case TimeUnit.MINUTE:\n                return DateTime.fromObject({ minute: +time }).toFormat('mm');\n            default:\n                throw new Error('no such time unit');\n        }\n    }\n}\nTimeFormatterPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeFormatterPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\nTimeFormatterPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeFormatterPipe, name: \"timeFormatter\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: TimeFormatterPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'timeFormatter'\n                }]\n        }] });\n\nclass NgxTimepickerTimeControlComponent {\n    constructor(timeParser) {\n        this.timeParser = timeParser;\n        this.timeChanged = new EventEmitter();\n    }\n    ngOnChanges(changes) {\n        if (changes.timeList && this.time != null) {\n            if (this.isSelectedTimeDisabled(this.time)) {\n                this.setAvailableTime();\n            }\n        }\n    }\n    changeTime(event) {\n        event.stopPropagation();\n        const char = String.fromCharCode(event.keyCode);\n        const time = concatTime(String(this.time), char);\n        this.changeTimeIfValid(time);\n    }\n    onKeydown(event) {\n        event.stopPropagation();\n        if (!isDigit(event)) {\n            event.preventDefault();\n        }\n        switch (event.key) {\n            case 'ArrowUp':\n                this.increase();\n                break;\n            case 'ArrowDown':\n                this.decrease();\n                break;\n        }\n        if (this.preventTyping && event.key !== 'Tab') {\n            event.preventDefault();\n        }\n    }\n    increase() {\n        if (!this.disabled) {\n            let nextTime = +this.time + (this.minutesGap || 1);\n            if (nextTime > this.max) {\n                nextTime = this.min;\n            }\n            if (this.isSelectedTimeDisabled(nextTime)) {\n                nextTime = this.getAvailableTime(nextTime, this.getNextAvailableTime.bind(this));\n            }\n            if (nextTime !== this.time) {\n                this.timeChanged.emit(nextTime);\n            }\n        }\n    }\n    decrease() {\n        if (!this.disabled) {\n            let previousTime = +this.time - (this.minutesGap || 1);\n            if (previousTime < this.min) {\n                previousTime = this.minutesGap ? this.max - (this.minutesGap - 1) : this.max;\n            }\n            if (this.isSelectedTimeDisabled(previousTime)) {\n                previousTime = this.getAvailableTime(previousTime, this.getPrevAvailableTime.bind(this));\n            }\n            if (previousTime !== this.time) {\n                this.timeChanged.emit(previousTime);\n            }\n        }\n    }\n    onFocus() {\n        this.isFocused = true;\n        this.previousTime = this.time;\n    }\n    onBlur() {\n        this.isFocused = false;\n        if (this.previousTime !== this.time) {\n            this.changeTimeIfValid(+this.time);\n        }\n    }\n    onModelChange(value) {\n        this.time = +this.timeParser.transform(value, this.timeUnit);\n    }\n    changeTimeIfValid(value) {\n        if (!isNaN(value)) {\n            this.time = value;\n            if (this.time > this.max) {\n                const timeString = String(value);\n                this.time = +timeString[timeString.length - 1];\n            }\n            if (this.time < this.min) {\n                this.time = this.min;\n            }\n            this.timeChanged.emit(this.time);\n        }\n    }\n    isSelectedTimeDisabled(time) {\n        return this.timeList.find((faceTime) => faceTime.time === time).disabled;\n    }\n    getNextAvailableTime(index) {\n        const timeCollection = this.timeList;\n        const maxValue = timeCollection.length;\n        for (let i = index + 1; i < maxValue; i++) {\n            const time = timeCollection[i];\n            if (!time.disabled) {\n                return time.time;\n            }\n        }\n    }\n    getPrevAvailableTime(index) {\n        for (let i = index; i >= 0; i--) {\n            const time = this.timeList[i];\n            if (!time.disabled) {\n                return time.time;\n            }\n        }\n    }\n    getAvailableTime(currentTime, fn) {\n        const currentTimeIndex = this.timeList.findIndex(time => time.time === currentTime);\n        const availableTime = fn(currentTimeIndex);\n        return availableTime != null ? availableTime : this.time;\n    }\n    setAvailableTime() {\n        const availableTime = this.timeList.find(t => !t.disabled);\n        if (availableTime != null) {\n            this.time = availableTime.time;\n            this.timeChanged.emit(this.time);\n        }\n    }\n}\nNgxTimepickerTimeControlComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerTimeControlComponent, deps: [{ token: TimeParserPipe }], target: i0.ɵɵFactoryTarget.Component });\nNgxTimepickerTimeControlComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxTimepickerTimeControlComponent, selector: \"ngx-timepicker-time-control\", inputs: { time: \"time\", min: \"min\", max: \"max\", placeholder: \"placeholder\", timeUnit: \"timeUnit\", disabled: \"disabled\", timeList: \"timeList\", preventTyping: \"preventTyping\", minutesGap: \"minutesGap\" }, outputs: { timeChanged: \"timeChanged\" }, providers: [TimeParserPipe], usesOnChanges: true, ngImport: i0, template: \"<div class=\\\"ngx-timepicker-control\\\" [ngClass]=\\\"{'ngx-timepicker-control--active': isFocused}\\\">\\n    <!--suppress HtmlFormInputWithoutLabel -->\\n    <input class=\\\"ngx-timepicker-control__input\\\"\\n           maxlength=\\\"2\\\"\\n           [ngModel]=\\\"time | timeParser: timeUnit | timeLocalizer: timeUnit : true\\\"\\n           (ngModelChange)=\\\"onModelChange($event)\\\"\\n           [placeholder]=\\\"placeholder\\\"\\n           [disabled]=\\\"disabled\\\"\\n           (keydown)=\\\"onKeydown($event)\\\"\\n           (keypress)=\\\"changeTime($event)\\\"\\n           (focus)=\\\"onFocus()\\\"\\n           (blur)=\\\"onBlur()\\\">\\n    <div class=\\\"ngx-timepicker-control__arrows\\\">\\n            <span class=\\\"ngx-timepicker-control__arrow\\\" role=\\\"button\\\" (click)=\\\"increase()\\\">\\n                &#9650;\\n            </span>\\n        <span class=\\\"ngx-timepicker-control__arrow\\\" role=\\\"button\\\" (click)=\\\"decrease()\\\">\\n                &#9660;\\n            </span>\\n    </div>\\n</div>\\n\", styles: [\".ngx-timepicker-control{position:relative;display:flex;width:60px;height:30px;padding:0 5px;box-sizing:border-box}.ngx-timepicker-control--active:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:100%;height:1px;background-color:#00bfff}.ngx-timepicker-control__input{width:100%;height:100%;padding:0 5px 0 0;border:0;font-size:1rem;color:inherit;outline:none;text-align:center}.ngx-timepicker-control__input:disabled{background-color:transparent}.ngx-timepicker-control__arrows{position:absolute;right:2px;top:0;display:flex;flex-direction:column}.ngx-timepicker-control__arrow{font-size:11px;color:#0006;cursor:pointer;transition:color .2s;-webkit-user-select:none;-moz-user-select:none;user-select:none}.ngx-timepicker-control__arrow:hover{color:#000000e6}\\n\"], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i4.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { type: i4.MaxLengthValidator, selector: \"[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]\", inputs: [\"maxlength\"] }, { type: i4.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { type: i4.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }], pipes: { \"timeLocalizer\": TimeLocalizerPipe, \"timeParser\": TimeParserPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerTimeControlComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-timepicker-time-control',\n                    templateUrl: './ngx-timepicker-time-control.component.html',\n                    styleUrls: ['./ngx-timepicker-time-control.component.scss'],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [TimeParserPipe],\n                }]\n        }], ctorParameters: function () { return [{ type: TimeParserPipe }]; }, propDecorators: { time: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], timeUnit: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], timeList: [{\n                type: Input\n            }], preventTyping: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], timeChanged: [{\n                type: Output\n            }] } });\nfunction concatTime(currentTime, nextTime) {\n    const isNumber = /\\d/.test(nextTime);\n    if (isNumber) {\n        const time = currentTime + nextTime;\n        return +time;\n    }\n}\n\nclass NgxTimepickerPeriodSelectorComponent {\n    constructor(locale) {\n        this.locale = locale;\n        this.periodSelected = new EventEmitter();\n        this.period = TimePeriod;\n        this.meridiems = Info.meridiems({ locale: this.locale });\n    }\n    set selectedPeriod(period) {\n        if (period) {\n            const periods = [TimePeriod.AM, TimePeriod.PM];\n            this.localizedPeriod = this.meridiems[periods.indexOf(period)];\n        }\n    }\n    open() {\n        if (!this.disabled) {\n            this.isOpened = true;\n        }\n    }\n    select(period) {\n        this.periodSelected.next(period);\n        this.isOpened = false;\n    }\n    backdropClick() {\n        this.isOpened = false;\n    }\n}\nNgxTimepickerPeriodSelectorComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerPeriodSelectorComponent, deps: [{ token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Component });\nNgxTimepickerPeriodSelectorComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxTimepickerPeriodSelectorComponent, selector: \"ngx-timepicker-period-selector\", inputs: { isOpened: \"isOpened\", disabled: \"disabled\", selectedPeriod: \"selectedPeriod\" }, outputs: { periodSelected: \"periodSelected\" }, ngImport: i0, template: \"<div class=\\\"period\\\">\\n    <div class=\\\"period-control\\\">\\n        <button class=\\\"period-control__button period__btn--default\\\"\\n                [ngClass]=\\\"{'period-control__button--disabled': disabled}\\\"\\n                type=\\\"button\\\"\\n                (click)=\\\"open()\\\">\\n            <span>{{localizedPeriod}}</span>\\n            <span class=\\\"period-control__arrow\\\">&#9660;</span>\\n        </button>\\n    </div>\\n    <ul class=\\\"period-selector\\\" @scaleInOut *ngIf=\\\"isOpened\\\" [timepickerAutofocus]=\\\"true\\\">\\n        <li>\\n            <button class=\\\"period-selector__button period__btn--default\\\"\\n                    type=\\\"button\\\"\\n                    (click)=\\\"select(period.AM)\\\"\\n                    [ngClass]=\\\"{'period-selector__button--active': localizedPeriod === meridiems[0]}\\\">{{meridiems[0]}}</button>\\n        </li>\\n        <li>\\n            <button class=\\\"period-selector__button period__btn--default\\\"\\n                    type=\\\"button\\\"\\n                    (click)=\\\"select(period.PM)\\\"\\n                    [ngClass]=\\\"{'period-selector__button--active': localizedPeriod === meridiems[1]}\\\">{{meridiems[1]}}</button>\\n        </li>\\n    </ul>\\n</div>\\n<div class=\\\"overlay\\\" (click)=\\\"backdropClick()\\\" *ngIf=\\\"isOpened\\\"></div>\\n\", styles: [\".period{position:relative}.period__btn--default{padding:0;border:none;background-color:transparent;cursor:pointer;text-align:left;-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-tap-highlight-color:transparent;outline:none}.period-control{position:relative}.period-control__button{position:relative;width:60px;font-size:1rem;color:inherit;text-align:center}.period-control__button:not(.period-control__button--disabled):focus:after{content:\\\"\\\";position:absolute;bottom:-8px;left:0;width:100%;height:1px;background-color:#00bfff}.period-control__arrow{margin-left:10px;font-size:12px;color:#0006}.period-selector{position:absolute;top:calc(50% - 50px);right:calc(-50% + -50px);max-width:135px;width:150px;padding:6px 0;margin:0;list-style:none;background-color:#f5f5f5;box-shadow:0 1px 3px #0003,0 1px 1px #00000024,0 2px 1px -1px #0000001f;z-index:201}.period-selector__button{width:100%;height:48px;padding:0 16px;line-height:48px}.period-selector__button--active{color:#00bfff}.period-selector__button:focus{background-color:#eee}.overlay{position:fixed;width:100%;height:100%;top:0;left:0;background-color:transparent;z-index:200}\\n\"], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: AutofocusDirective, selector: \"[timepickerAutofocus]\", inputs: [\"timepickerAutofocus\"] }], animations: [\n        trigger('scaleInOut', [\n            transition(':enter', [\n                style({ transform: 'scale(0)', opacity: 0 }),\n                animate(200, style({ transform: 'scale(1)', opacity: 1 }))\n            ]),\n            transition(':leave', [\n                animate(200, style({ transform: 'scale(0)', opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerPeriodSelectorComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-timepicker-period-selector',\n                    templateUrl: 'ngx-timepicker-period-selector.component.html',\n                    styleUrls: ['./ngx-timepicker-period-selector.component.scss'],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    animations: [\n                        trigger('scaleInOut', [\n                            transition(':enter', [\n                                style({ transform: 'scale(0)', opacity: 0 }),\n                                animate(200, style({ transform: 'scale(1)', opacity: 1 }))\n                            ]),\n                            transition(':leave', [\n                                animate(200, style({ transform: 'scale(0)', opacity: 0 }))\n                            ])\n                        ])\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; }, propDecorators: { isOpened: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], selectedPeriod: [{\n                type: Input\n            }], periodSelected: [{\n                type: Output\n            }] } });\n\nclass NgxTimepickerFieldComponent {\n    constructor(timepickerService, locale) {\n        this.timepickerService = timepickerService;\n        this.locale = locale;\n        this.minHour = 1;\n        this.maxHour = 12;\n        this.timeUnit = TimeUnit;\n        this.buttonAlign = 'right';\n        this.timeChanged = new EventEmitter();\n        this._format = 12;\n        this.unsubscribe$ = new Subject();\n        this.isFirstTimeChange = true;\n        this.onChange = () => {\n        };\n    }\n    set format(value) {\n        this._format = value === 24 ? 24 : 12;\n        this.minHour = this._format === 12 ? 1 : 0;\n        this.maxHour = this._format === 12 ? 12 : 23;\n        this.hoursList = TimepickerTimeUtils.getHours(this._format);\n        const isDynamicallyChanged = value && (this.previousFormat && this.previousFormat !== this._format);\n        if (isDynamicallyChanged) {\n            this.updateTime(this.timepickerTime);\n        }\n        this.previousFormat = this._format;\n    }\n    get format() {\n        return this._format;\n    }\n    set min(value) {\n        if (typeof value === 'string') {\n            this._min = TimeAdapter.parseTime(value, { locale: this.locale, format: this.format });\n            return;\n        }\n        this._min = value;\n    }\n    get min() {\n        return this._min;\n    }\n    set max(value) {\n        if (typeof value === 'string') {\n            this._max = TimeAdapter.parseTime(value, { locale: this.locale, format: this.format });\n            return;\n        }\n        this._max = value;\n    }\n    get max() {\n        return this._max;\n    }\n    set defaultTime(val) {\n        this._defaultTime = val;\n        this.isDefaultTime = !!val;\n    }\n    get defaultTime() {\n        return this._defaultTime;\n    }\n    set minutesGap(gap) {\n        if (gap == null) {\n            return;\n        }\n        gap = Math.floor(gap);\n        this._minutesGap = gap <= 59 ? gap : 1;\n    }\n    get minutesGap() {\n        return this._minutesGap;\n    }\n    ngOnInit() {\n        this.initTime(this.defaultTime);\n        this.hoursList = TimepickerTimeUtils.getHours(this._format);\n        this.minutesList = TimepickerTimeUtils.getMinutes();\n        this.isTimeRangeSet = !!(this.min || this.max);\n        this.hour$ = this.timepickerService.selectedHour.pipe(tap((clockTime) => this.selectedHour = clockTime.time), map(this.changeDefaultTimeValue.bind(this)), tap(() => this.isTimeRangeSet && this.updateAvailableMinutes()));\n        this.minute$ = this.timepickerService.selectedMinute.pipe(map(this.changeDefaultTimeValue.bind(this)), tap(() => this.isFirstTimeChange = false));\n        if (this.format === 12) {\n            this.timepickerService.selectedPeriod.pipe(distinctUntilChanged(), tap((period) => this.period = period), tap(period => this.isChangePeriodDisabled = this.isPeriodDisabled(period)), takeUntil(this.unsubscribe$)).subscribe(() => this.isTimeRangeSet && this.updateAvailableTime());\n        }\n        else if (this.isTimeRangeSet) {\n            this.updateAvailableTime();\n        }\n    }\n    writeValue(val) {\n        if (val) {\n            this.initTime(val);\n        }\n        else {\n            this.resetTime();\n        }\n    }\n    registerOnTouched(fn) {\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    changeHour(hour) {\n        this.timepickerService.hour = this.hoursList.find(h => h.time === hour);\n        this.changeTime();\n    }\n    changeMinute(minute) {\n        this.timepickerService.minute = this.minutesList.find(m => m.time === minute);\n        this.changeTime();\n    }\n    changePeriod(period) {\n        this.timepickerService.period = period;\n        this.changeTime();\n    }\n    onTimeSet(time) {\n        this.updateTime(time);\n        this.emitLocalTimeChange(time);\n    }\n    ngOnDestroy() {\n        this.unsubscribe$.next();\n        this.unsubscribe$.complete();\n    }\n    changeTime() {\n        const time = this.timepickerService.getFullTime(this.format);\n        this.timepickerTime = time;\n        this.emitLocalTimeChange(time);\n    }\n    resetTime() {\n        this.timepickerService.hour = { angle: 0, time: null };\n        this.timepickerService.minute = { angle: 0, time: null };\n    }\n    emitLocalTimeChange(time) {\n        const localTime = TimeAdapter.toLocaleTimeString(time, { format: this.format, locale: this.locale });\n        this.onChange(localTime);\n        this.timeChanged.emit(localTime);\n    }\n    changeDefaultTimeValue(clockFaceTime) {\n        if (!this.isDefaultTime && this.isFirstTimeChange) {\n            return Object.assign(Object.assign({}, clockFaceTime), { time: null });\n        }\n        return clockFaceTime;\n    }\n    updateAvailableHours() {\n        this.hoursList = TimepickerTimeUtils.disableHours(this.hoursList, {\n            min: this.min,\n            max: this.max,\n            format: this.format,\n            period: this.period\n        });\n    }\n    updateAvailableMinutes() {\n        this.minutesList = TimepickerTimeUtils.disableMinutes(this.minutesList, this.selectedHour, {\n            min: this.min,\n            max: this.max,\n            format: this.format,\n            period: this.period\n        });\n    }\n    updateAvailableTime() {\n        this.updateAvailableHours();\n        if (this.selectedHour) {\n            this.updateAvailableMinutes();\n        }\n    }\n    updateTime(time) {\n        if (time) {\n            const formattedTime = TimeAdapter.formatTime(time, { locale: this.locale, format: this.format });\n            this.timepickerService.setDefaultTimeIfAvailable(formattedTime, this.min, this.max, this.format);\n            this.timepickerTime = formattedTime;\n        }\n    }\n    initTime(time) {\n        const isDefaultTimeAvailable = TimeAdapter\n            .isTimeAvailable(time, this.min, this.max, 'minutes', null, this.format);\n        if (!isDefaultTimeAvailable) {\n            if (this.min) {\n                this.updateTime(TimeAdapter.fromDateTimeToString(this.min, this.format));\n                return;\n            }\n            if (this.max) {\n                this.updateTime(TimeAdapter.fromDateTimeToString(this.max, this.format));\n                return;\n            }\n        }\n        this.updateTime(time);\n    }\n    isPeriodDisabled(period) {\n        return TimepickerTimeUtils.disableHours(TimepickerTimeUtils.getHours(12), {\n            min: this.min,\n            max: this.max,\n            format: 12,\n            period: period === TimePeriod.AM ? TimePeriod.PM : TimePeriod.AM\n        }).every(time => time.disabled);\n    }\n}\nNgxTimepickerFieldComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerFieldComponent, deps: [{ token: NgxMaterialTimepickerService }, { token: TIME_LOCALE }], target: i0.ɵɵFactoryTarget.Component });\nNgxTimepickerFieldComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.17\", type: NgxTimepickerFieldComponent, selector: \"ngx-timepicker-field\", inputs: { disabled: \"disabled\", toggleIcon: \"toggleIcon\", buttonAlign: \"buttonAlign\", clockTheme: \"clockTheme\", controlOnly: \"controlOnly\", cancelBtnTmpl: \"cancelBtnTmpl\", confirmBtnTmpl: \"confirmBtnTmpl\", format: \"format\", min: \"min\", max: \"max\", defaultTime: \"defaultTime\", minutesGap: \"minutesGap\" }, outputs: { timeChanged: \"timeChanged\" }, providers: [\n        NgxMaterialTimepickerService,\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: NgxTimepickerFieldComponent,\n            multi: true\n        }\n    ], ngImport: i0, template: \"<div class=\\\"ngx-timepicker\\\" [ngClass]=\\\"{'ngx-timepicker--disabled': disabled}\\\">\\n    <ngx-timepicker-time-control\\n        class=\\\"ngx-timepicker__control--first\\\"\\n        [placeholder]=\\\"'HH'\\\"\\n        [time]=\\\"(hour$ | async)?.time\\\"\\n        [min]=\\\"minHour\\\"\\n        [max]=\\\"maxHour\\\"\\n        [timeUnit]=\\\"timeUnit.HOUR\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [timeList]=\\\"hoursList\\\"\\n        [preventTyping]=\\\"isTimeRangeSet\\\"\\n        (timeChanged)=\\\"changeHour($event)\\\"></ngx-timepicker-time-control>\\n    <span class=\\\"ngx-timepicker__time-colon ngx-timepicker__control--second\\\">:</span>\\n    <ngx-timepicker-time-control\\n        class=\\\"ngx-timepicker__control--third\\\"\\n        [placeholder]=\\\"'MM'\\\"\\n        [time]=\\\"(minute$ | async)?.time\\\"\\n        [min]=\\\"0\\\"\\n        [max]=\\\"59\\\"\\n        [timeUnit]=\\\"timeUnit.MINUTE\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [timeList]=\\\"minutesList\\\"\\n        [preventTyping]=\\\"isTimeRangeSet\\\"\\n        [minutesGap]=\\\"minutesGap\\\"\\n        (timeChanged)=\\\"changeMinute($event)\\\"></ngx-timepicker-time-control>\\n    <ngx-timepicker-period-selector\\n        class=\\\"ngx-timepicker__control--forth\\\"\\n        [selectedPeriod]=\\\"period\\\"\\n        [disabled]=\\\"disabled || isChangePeriodDisabled\\\"\\n        (periodSelected)=\\\"changePeriod($event)\\\"\\n        *ngIf=\\\"format !== 24\\\"></ngx-timepicker-period-selector>\\n    <ngx-material-timepicker-toggle\\n        class=\\\"ngx-timepicker__toggle\\\"\\n        *ngIf=\\\"!controlOnly\\\"\\n        [ngClass]=\\\"{'ngx-timepicker__toggle--left': buttonAlign === 'left'}\\\"\\n        [for]=\\\"timepicker\\\"\\n        [disabled]=\\\"disabled\\\">\\n        <span ngxMaterialTimepickerToggleIcon>\\n            <!--suppress HtmlUnknownAttribute -->\\n            <ng-container *ngTemplateOutlet=\\\"toggleIcon || defaultIcon\\\"></ng-container>\\n        </span>\\n    </ngx-material-timepicker-toggle>\\n</div>\\n<ngx-material-timepicker\\n    [min]=\\\"min\\\"\\n    [max]=\\\"max\\\"\\n    [theme]=\\\"clockTheme\\\"\\n    [defaultTime]=\\\"timepickerTime\\\"\\n    [format]=\\\"format\\\"\\n    [cancelBtnTmpl]=\\\"cancelBtnTmpl\\\"\\n    [confirmBtnTmpl]=\\\"confirmBtnTmpl\\\"\\n    [minutesGap]=\\\"minutesGap\\\"\\n    (timeSet)=\\\"onTimeSet($event)\\\" #timepicker></ngx-material-timepicker>\\n\\n<ng-template #defaultIcon>\\n    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\">\\n        <!--suppress CheckEmptyScriptTag -->\\n        <path\\n            d=\\\"M 12 2 C 6.4889971 2 2 6.4889971 2 12 C 2 17.511003                   6.4889971 22 12 22 C 17.511003 22 22 17.511003 22 12 C 22 6.4889971 17.511003 2 12 2 z M 12 4 C 16.430123 4 20 7.5698774 20 12 C 20 16.430123 16.430123 20 12 20 C 7.5698774 20 4 16.430123 4 12 C 4 7.5698774 7.5698774 4 12 4 z M 11 6 L 11 12.414062 L 15.292969 16.707031 L 16.707031 15.292969 L 13 11.585938 L 13 6 L 11 6 z\\\"/>\\n    </svg>\\n</ng-template>\\n\", styles: [\".ngx-timepicker{display:flex;align-items:center;height:100%;border-bottom:1px solid rgba(0,0,0,.12)}.ngx-timepicker--disabled{background:rgba(0,0,0,.07);pointer-events:none}.ngx-timepicker__time-colon{margin-left:10px}.ngx-timepicker__control--first{order:1}.ngx-timepicker__control--second{order:2}.ngx-timepicker__control--third{order:3}.ngx-timepicker__control--forth{order:4}.ngx-timepicker__toggle{order:4}.ngx-timepicker__toggle--left{order:0}\\n\"], components: [{ type: NgxTimepickerTimeControlComponent, selector: \"ngx-timepicker-time-control\", inputs: [\"time\", \"min\", \"max\", \"placeholder\", \"timeUnit\", \"disabled\", \"timeList\", \"preventTyping\", \"minutesGap\"], outputs: [\"timeChanged\"] }, { type: NgxTimepickerPeriodSelectorComponent, selector: \"ngx-timepicker-period-selector\", inputs: [\"isOpened\", \"disabled\", \"selectedPeriod\"], outputs: [\"periodSelected\"] }, { type: NgxMaterialTimepickerToggleComponent, selector: \"ngx-material-timepicker-toggle\", inputs: [\"for\", \"disabled\"] }, { type: NgxMaterialTimepickerComponent, selector: \"ngx-material-timepicker\", inputs: [\"cancelBtnTmpl\", \"editableHintTmpl\", \"confirmBtnTmpl\", \"ESC\", \"enableKeyboardInput\", \"preventOverlayClick\", \"disableAnimation\", \"appendToInput\", \"hoursOnly\", \"defaultTime\", \"timepickerClass\", \"theme\", \"min\", \"max\", \"ngxMaterialTimepickerTheme\", \"format\", \"minutesGap\"], outputs: [\"timeSet\", \"opened\", \"closed\", \"hourSelected\", \"timeChanged\"] }], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: NgxMaterialTimepickerToggleIconDirective, selector: \"[ngxMaterialTimepickerToggleIcon]\" }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], pipes: { \"async\": i1.AsyncPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxTimepickerFieldComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ngx-timepicker-field',\n                    templateUrl: './ngx-timepicker-field.component.html',\n                    styleUrls: ['./ngx-timepicker-field.component.scss'],\n                    providers: [\n                        NgxMaterialTimepickerService,\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: NgxTimepickerFieldComponent,\n                            multi: true\n                        }\n                    ],\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: function () { return [{ type: NgxMaterialTimepickerService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TIME_LOCALE]\n                }] }]; }, propDecorators: { disabled: [{\n                type: Input\n            }], toggleIcon: [{\n                type: Input\n            }], buttonAlign: [{\n                type: Input\n            }], clockTheme: [{\n                type: Input\n            }], controlOnly: [{\n                type: Input\n            }], cancelBtnTmpl: [{\n                type: Input\n            }], confirmBtnTmpl: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], defaultTime: [{\n                type: Input\n            }], minutesGap: [{\n                type: Input\n            }], timeChanged: [{\n                type: Output\n            }] } });\n\nclass NgxMaterialTimepickerModule {\n    // tslint:disable-next-line:max-line-length\n    static setOpts(locale, numberingSystem = TimeAdapter.DEFAULT_NUMBERING_SYSTEM) {\n        return {\n            ngModule: NgxMaterialTimepickerModule,\n            providers: [\n                { provide: TIME_LOCALE, useValue: locale },\n                { provide: NUMBERING_SYSTEM, useValue: numberingSystem }\n            ]\n        };\n    }\n}\nNgxMaterialTimepickerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgxMaterialTimepickerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerModule, declarations: [NgxMaterialTimepickerComponent,\n        NgxMaterialTimepicker24HoursFaceComponent,\n        NgxMaterialTimepicker12HoursFaceComponent,\n        NgxMaterialTimepickerMinutesFaceComponent,\n        NgxMaterialTimepickerFaceComponent,\n        NgxMaterialTimepickerToggleComponent,\n        NgxMaterialTimepickerButtonComponent,\n        NgxMaterialTimepickerDialComponent,\n        NgxMaterialTimepickerDialControlComponent,\n        NgxMaterialTimepickerPeriodComponent,\n        TimeFormatterPipe,\n        TimepickerDirective,\n        OverlayDirective,\n        NgxMaterialTimepickerToggleIconDirective,\n        AutofocusDirective,\n        MinutesFormatterPipe,\n        NgxMaterialTimepickerThemeDirective,\n        NgxTimepickerFieldComponent,\n        NgxTimepickerTimeControlComponent,\n        NgxTimepickerPeriodSelectorComponent,\n        TimeLocalizerPipe,\n        TimeParserPipe,\n        ActiveHourPipe,\n        ActiveMinutePipe,\n        NgxMaterialTimepickerContainerComponent,\n        NgxMaterialTimepickerContentComponent,\n        AppendToInputDirective], imports: [CommonModule,\n        FormsModule,\n        ReactiveFormsModule], exports: [NgxMaterialTimepickerComponent,\n        NgxMaterialTimepickerToggleComponent,\n        NgxTimepickerFieldComponent,\n        TimepickerDirective,\n        NgxMaterialTimepickerToggleIconDirective,\n        NgxMaterialTimepickerThemeDirective] });\nNgxMaterialTimepickerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerModule, imports: [[\n            CommonModule,\n            FormsModule,\n            ReactiveFormsModule,\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxMaterialTimepickerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        FormsModule,\n                        ReactiveFormsModule,\n                    ],\n                    exports: [\n                        NgxMaterialTimepickerComponent,\n                        NgxMaterialTimepickerToggleComponent,\n                        NgxTimepickerFieldComponent,\n                        TimepickerDirective,\n                        NgxMaterialTimepickerToggleIconDirective,\n                        NgxMaterialTimepickerThemeDirective,\n                    ],\n                    declarations: [\n                        NgxMaterialTimepickerComponent,\n                        NgxMaterialTimepicker24HoursFaceComponent,\n                        NgxMaterialTimepicker12HoursFaceComponent,\n                        NgxMaterialTimepickerMinutesFaceComponent,\n                        NgxMaterialTimepickerFaceComponent,\n                        NgxMaterialTimepickerToggleComponent,\n                        NgxMaterialTimepickerButtonComponent,\n                        NgxMaterialTimepickerDialComponent,\n                        NgxMaterialTimepickerDialControlComponent,\n                        NgxMaterialTimepickerPeriodComponent,\n                        TimeFormatterPipe,\n                        TimepickerDirective,\n                        OverlayDirective,\n                        NgxMaterialTimepickerToggleIconDirective,\n                        AutofocusDirective,\n                        MinutesFormatterPipe,\n                        NgxMaterialTimepickerThemeDirective,\n                        NgxTimepickerFieldComponent,\n                        NgxTimepickerTimeControlComponent,\n                        NgxTimepickerPeriodSelectorComponent,\n                        TimeLocalizerPipe,\n                        TimeParserPipe,\n                        ActiveHourPipe,\n                        ActiveMinutePipe,\n                        NgxMaterialTimepickerContainerComponent,\n                        NgxMaterialTimepickerContentComponent,\n                        AppendToInputDirective\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NUMBERING_SYSTEM, NgxMaterialTimepickerComponent, NgxMaterialTimepickerModule, NgxMaterialTimepickerThemeDirective, NgxMaterialTimepickerToggleComponent, NgxMaterialTimepickerToggleIconDirective, NgxTimepickerFieldComponent, TIME_LOCALE, TimepickerDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC/M,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACtD,SAASC,WAAW,EAAEC,GAAG,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAC7G,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,qBAAqB;AACnF,SAASC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AACtC,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAAC,SAAAC,oEAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA6Nc1C,EAAE,CAAA4C,kBAAA,EAsIsY,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtIzY1C,EAAE,CAAA8C,cAAA,YAsIiR,CAAC;IAtIpR9C,EAAE,CAAA+C,UAAA,IAAAN,mEAAA,yBAsIsY,CAAC;IAtIzYzC,EAAE,CAAAgD,YAAA,CAsI8Y,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAtIjZjD,EAAE,CAAAkD,aAAA;IAAA,MAAAC,GAAA,GAAFnD,EAAE,CAAAoD,WAAA;IAAFpD,EAAE,CAAAqD,UAAA,qBAAAJ,MAAA,CAAAK,YAsImO,CAAC;IAtItOtD,EAAE,CAAAuD,SAAA,EAsIoX,CAAC;IAtIvXvD,EAAE,CAAAqD,UAAA,qBAAAF,GAsIoX,CAAC;EAAA;AAAA;AAAA,SAAAK,4EAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtIvX1C,EAAE,CAAA4C,kBAAA,EAsIqiB,CAAC;EAAA;AAAA;AAAA,SAAAa,6DAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtIxiB1C,EAAE,CAAA+C,UAAA,IAAAS,2EAAA,yBAsIqiB,CAAC;EAAA;EAAA,IAAAd,EAAA;IAtIxiB1C,EAAE,CAAAkD,aAAA;IAAA,MAAAC,GAAA,GAAFnD,EAAE,CAAAoD,WAAA;IAAFpD,EAAE,CAAAqD,UAAA,qBAAAF,GAsImhB,CAAC;EAAA;AAAA;AAAA,SAAAO,6DAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtIthB1C,EAAE,CAAA2D,YAAA,EAsIunB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAA,gCAAAA;EAAA;AAAA;AAAA,SAAAC,2DAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuB,GAAA,GAtI1nBjE,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,cAgYskC,CAAC;IAhYzkC9C,EAAE,CAAAmE,UAAA,2BAAAC,0FAAAC,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFvE,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAAAD,MAAA,CAAAE,IAAA,GAAAJ,MAAA;IAAA,CAgYo2B,CAAC,mBAAAK,kFAAA;MAhYv2B1E,EAAE,CAAAsE,aAAA,CAAAL,GAAA;MAAA,MAAAU,MAAA,GAAF3E,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgYw5BG,MAAA,CAAAC,UAAA,CAAW,EAAC;IAAA,CAAhE,CAAC,mBAAAC,kFAAAR,MAAA;MAhYv2BrE,EAAE,CAAAsE,aAAA,CAAAL,GAAA;MAAA,MAAAa,MAAA,GAAF9E,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgYi7BM,MAAA,CAAAC,yBAAA,CAAAV,MAAA,EAAAS,MAAA,CAAAE,QAA0C,EAAC;IAAA,CAAxH,CAAC;IAhYv2BhF,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAgYskC,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAhYzkCjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAApB,GAAA,EAAAb,MAAA,CAAAkC,QAAA,CAgYqwB,CAAC,YAhYxwBnF,EAAE,CAAAoF,WAAA,OAAAnC,MAAA,CAAAwB,IAAA,EAAAxB,MAAA,CAAA+B,QAAA,CAgYqwB,CAAC,aAAA/B,MAAA,CAAAoC,QAAD,CAAC,wBAAApC,MAAA,CAAAkC,QAAD,CAAC;EAAA;AAAA;AAAA,SAAAG,iEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6C,GAAA,GAhYxwBvF,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,iBAgYmkD,CAAC;IAhYtkD9C,EAAE,CAAAmE,UAAA,mBAAAqB,wFAAAnB,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAAiB,GAAA;MAAA,MAAAE,MAAA,GAAFzF,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgY0+CiB,MAAA,CAAAV,yBAAA,CAAAV,MAAA,EAAAoB,MAAA,CAAAT,QAA0C,EAAC;IAAA,CAAC,CAAC,qBAAAU,0FAAArB,MAAA;MAhYzhDrE,EAAE,CAAAsE,aAAA,CAAAiB,GAAA;MAAA,MAAAI,OAAA,GAAF3F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgYgjDmB,OAAA,CAAAC,SAAA,CAAAvB,MAAgB,EAAC;IAAA,CAA3C,CAAC;IAhYzhDrE,EAAE,CAAAgD,YAAA,CAgYmkD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAmD,MAAA,GAhYtkD7F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,gBAAAwC,MAAA,CAAAC,WAgY81C,CAAC,YAhYj2C9F,EAAE,CAAAkF,eAAA,IAAApB,GAAA,EAAA+B,MAAA,CAAAV,QAAA,CAgY81C,CAAC,wBAAAU,MAAA,CAAAV,QAAD,CAAC;EAAA;AAAA;AAAA,SAAAY,oDAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsD,GAAA,GAhYj2ChG,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,YAud4kC,CAAC;IAvd/kC9C,EAAE,CAAAmE,UAAA,8BAAA8B,6FAAA;MAAFjG,EAAE,CAAAsE,aAAA,CAAA0B,GAAA;MAAA,MAAAE,MAAA,GAAFlG,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAud8hC0B,MAAA,CAAAC,aAAA,CAAc,EAAC;IAAA,CAAC,CAAC;IAvdjjCnG,EAAE,CAAA8C,cAAA,OAudylC,CAAC;IAvd5lC9C,EAAE,CAAAoG,MAAA,mDAudsoC,CAAC;IAvdzoCpG,EAAE,CAAAgD,YAAA,CAud0oC,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAvd7oC1C,EAAE,CAAAqD,UAAA,gBAAAgD,SAudugC,CAAC;EAAA;AAAA;AAAA,SAAAC,iEAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvd1gC1C,EAAE,CAAA4C,kBAAA,EAmkB8pG,CAAC;EAAA;AAAA;AAAA,SAAA2D,gEAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnkBjqG1C,EAAE,CAAA8C,cAAA,eAmkB+vG,CAAC;IAnkBlwG9C,EAAE,CAAAoG,MAAA,qBAmkB8wG,CAAC;IAnkBjxGpG,EAAE,CAAA8C,cAAA,UAmkBoxG,CAAC;IAnkBvxG9C,EAAE,CAAAoG,MAAA,YAmkB2xG,CAAC;IAnkB9xGpG,EAAE,CAAAgD,YAAA,CAmkBkyG,CAAC;IAnkBryGhD,EAAE,CAAAoG,MAAA,0BAmkBszG,CAAC;IAnkBzzGpG,EAAE,CAAAgD,YAAA,CAmkB8zG,CAAC;EAAA;AAAA;AAAA,MAAAwD,GAAA,YAAAA,CAAAzC,EAAA;EAAA;IAAA,2CAAAA;EAAA;AAAA;AAAA,SAAA0C,kDAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnkBj0G1C,EAAE,CAAA8C,cAAA,YAmkBw/F,CAAC;IAnkB3/F9C,EAAE,CAAA+C,UAAA,IAAAuD,gEAAA,yBAmkB8pG,CAAC;IAnkBjqGtG,EAAE,CAAA+C,UAAA,IAAAwD,+DAAA,gCAAFvG,EAAE,CAAA0G,sBAmkBs1G,CAAC;IAnkBz1G1G,EAAE,CAAAgD,YAAA,CAmkBk2G,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAsD,GAAA,GAnkBr2GhG,EAAE,CAAAoD,WAAA;IAAA,MAAAH,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAAsB,GAAA,GAAAvD,MAAA,CAAA0D,aAAA,CAmkBu/F,CAAC;IAnkB1/F3G,EAAE,CAAAuD,SAAA,EAmkB4oG,CAAC;IAnkB/oGvD,EAAE,CAAAqD,UAAA,qBAAAJ,MAAA,CAAA2D,gBAAA,GAAA3D,MAAA,CAAA2D,gBAAA,GAAAZ,GAmkB4oG,CAAC;EAAA;AAAA;AAAA,MAAAa,GAAA,YAAAA,CAAA9C,EAAA;EAAA;IAAA,mCAAAA;EAAA;AAAA;AAAA,MAAA+C,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAjD,EAAA;EAAA;IAAA,aAAAA;EAAA;AAAA;AAAA,MAAAkD,GAAA,YAAAA,CAAAlD,EAAA,EAAAmD,EAAA;EAAA;IAAA,UAAAnD,EAAA;IAAA,YAAAmD;EAAA;AAAA;AAAA,SAAAC,wDAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnkB/oG1C,EAAE,CAAA8C,cAAA,YAyzB0pC,CAAC,cAAD,CAAC;IAzzB7pC9C,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAoG,MAAA,EAyzB68C,CAAC;IAzzBh9CpG,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAyzBo9C,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA0E,OAAA,GAAAzE,GAAA,CAAA0E,SAAA;IAAA,MAAA1C,MAAA,GAzzBv9C3E,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,eAAAI,OAAA,CAAAE,KAAA,2BAyzBqkC,CAAC;IAzzBxkCtH,EAAE,CAAAuD,SAAA,EAyzBouC,CAAC;IAzzBvuCvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,gBAAAI,OAAA,CAAAE,KAAA,UAyzBouC,CAAC,YAzzBvuCtH,EAAE,CAAAuH,eAAA,KAAAN,GAAA,EAAFjH,EAAE,CAAAwH,WAAA,OAAAJ,OAAA,CAAA3C,IAAA,EAAAE,MAAA,CAAA8C,YAAA,CAAAhD,IAAA,EAAAE,MAAA,CAAA+C,mBAAA,GAAAN,OAAA,CAAA/B,QAAA,CAyzBouC,CAAC;IAzzBvuCrF,EAAE,CAAAuD,SAAA,EAyzB68C,CAAC;IAzzBh9CvD,EAAE,CAAA2H,kBAAA,MAAF3H,EAAE,CAAAoF,WAAA,OAAAgC,OAAA,CAAA3C,IAAA,EAAAE,MAAA,CAAAK,QAAA,CAAA4C,IAAA,MAyzB68C,CAAC;EAAA;AAAA;AAAA,SAAAC,8DAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzBh9C1C,EAAE,CAAA8C,cAAA,aAyzBo7D,CAAC,cAAD,CAAC;IAzzBv7D9C,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAoG,MAAA,EAyzB0tE,CAAC;IAzzB7tEpG,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAyzBiuE,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAoF,OAAA,GAAAnF,GAAA,CAAA0E,SAAA;IAAA,MAAA5B,MAAA,GAzzBpuEzF,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA+H,WAAA,WAAAtC,MAAA,CAAAuC,kBAAA,MAyzB01D,CAAC;IAzzB71DhI,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,eAAAc,OAAA,CAAAR,KAAA,2BAyzB+xD,CAAC;IAzzBlyDtH,EAAE,CAAAuD,SAAA,EAyzB8/D,CAAC;IAzzBjgEvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,gBAAAc,OAAA,CAAAR,KAAA,UAyzB8/D,CAAC,YAzzBjgEtH,EAAE,CAAAuH,eAAA,KAAAN,GAAA,EAAFjH,EAAE,CAAAwH,WAAA,OAAAM,OAAA,CAAArD,IAAA,EAAAgB,MAAA,CAAAgC,YAAA,kBAAAhC,MAAA,CAAAgC,YAAA,CAAAhD,IAAA,EAAAgB,MAAA,CAAAiC,mBAAA,GAAAI,OAAA,CAAAzC,QAAA,CAyzB8/D,CAAC;IAzzBjgErF,EAAE,CAAAuD,SAAA,EAyzB0tE,CAAC;IAzzB7tEvD,EAAE,CAAA2H,kBAAA,MAAF3H,EAAE,CAAAoF,WAAA,QAAA0C,OAAA,CAAArD,IAAA,EAAAgB,MAAA,CAAAT,QAAA,CAAA4C,IAAA,KAyzB0tE,CAAC;EAAA;AAAA;AAAA,SAAAK,wDAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzB7tE1C,EAAE,CAAA8C,cAAA,aAyzBunD,CAAC;IAzzB1nD9C,EAAE,CAAA+C,UAAA,IAAA8E,6DAAA,kBAyzBqvE,CAAC;IAzzBxvE7H,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAyzBqwE,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAoC,MAAA,GAzzBxwE9E,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA+H,WAAA,wBAAAjD,MAAA,CAAAkD,kBAAA,QAyzBsnD,CAAC;IAzzBznDhI,EAAE,CAAAuD,SAAA,EAyzB65D,CAAC;IAzzBh6DvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAwH,WAAA,OAAA1C,MAAA,CAAAoD,QAAA,SAyzB65D,CAAC,iBAAApD,MAAA,CAAAqD,WAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAA1F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzBh6D1C,EAAE,CAAA8C,cAAA,YAyzBq6B,CAAC;IAzzBx6B9C,EAAE,CAAA+C,UAAA,IAAAoE,uDAAA,iBAyzBo+C,CAAC;IAzzBv+CnH,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAA+C,UAAA,IAAAkF,uDAAA,gBAyzBqwE,CAAC;IAzzBxwEjI,EAAE,CAAAgD,YAAA,CAyzBixE,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAwD,MAAA,GAzzBpxElG,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuD,SAAA,EAyzBmoC,CAAC;IAzzBtoCvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAwH,WAAA,OAAAtB,MAAA,CAAAgC,QAAA,QAyzBmoC,CAAC,iBAAAhC,MAAA,CAAAiC,WAAD,CAAC;IAzzBtoCnI,EAAE,CAAAuD,SAAA,EAyzB2iD,CAAC;IAzzB9iDvD,EAAE,CAAAqD,UAAA,SAAA6C,MAAA,CAAAgC,QAAA,CAAAG,MAAA,KAyzB2iD,CAAC;EAAA;AAAA;AAAA,SAAAC,gEAAA5F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzB9iD1C,EAAE,CAAA8C,cAAA,YAyzB4vF,CAAC,cAAD,CAAC;IAzzB/vF9C,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAoG,MAAA,EAyzB4iG,CAAC;IAzzB/iGpG,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAyzBmjG,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA6F,QAAA,GAAA5F,GAAA,CAAA0E,SAAA;IAAA,MAAA1B,OAAA,GAzzBtjG3F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,eAAAuB,QAAA,CAAAjB,KAAA,2BAyzBurF,CAAC;IAzzB1rFtH,EAAE,CAAAuD,SAAA,EAyzBk0F,CAAC;IAzzBr0FvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA8B,GAAA,gBAAAuB,QAAA,CAAAjB,KAAA,UAyzBk0F,CAAC,YAzzBr0FtH,EAAE,CAAAuH,eAAA,KAAAN,GAAA,EAAFjH,EAAE,CAAAwI,WAAA,OAAAD,QAAA,CAAA9D,IAAA,EAAAkB,OAAA,CAAA8B,YAAA,kBAAA9B,OAAA,CAAA8B,YAAA,CAAAhD,IAAA,EAAAkB,OAAA,CAAA8C,UAAA,EAAA9C,OAAA,CAAA+B,mBAAA,GAAAa,QAAA,CAAAlD,QAAA,CAyzBk0F,CAAC;IAzzBr0FrF,EAAE,CAAAuD,SAAA,EAyzB4iG,CAAC;IAzzB/iGvD,EAAE,CAAA2H,kBAAA,MAAF3H,EAAE,CAAAoF,WAAA,OAAFpF,EAAE,CAAAoF,WAAA,QAAAmD,QAAA,CAAA9D,IAAA,EAAAkB,OAAA,CAAA8C,UAAA,GAAA9C,OAAA,CAAAX,QAAA,CAAA0D,MAAA,KAyzB4iG,CAAC;EAAA;AAAA;AAAA,SAAAC,0DAAAjG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzB/iG1C,EAAE,CAAA8C,cAAA,YAyzBuhF,CAAC;IAzzB1hF9C,EAAE,CAAA+C,UAAA,IAAAuF,+DAAA,iBAyzBmkG,CAAC;IAzzBtkGtI,EAAE,CAAAgD,YAAA,CAyzB+kG,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAkG,MAAA,GAzzBllG5I,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuD,SAAA,EAyzBquF,CAAC;IAzzBxuFvD,EAAE,CAAAqD,UAAA,YAAAuF,MAAA,CAAAV,QAyzBquF,CAAC,iBAAAU,MAAA,CAAAT,WAAD,CAAC;EAAA;AAAA;AAAA,MAAAU,GAAA,YAAAA,CAAA9E,EAAA;EAAA;IAAA,iCAAAA;EAAA;AAAA;AAAA,SAAA+E,gGAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqG,IAAA,GAzzBxuF/I,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,+CAwoCs9F,CAAC;IAxoCz9F9C,EAAE,CAAAmE,UAAA,wBAAA6E,4JAAA3E,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAAyE,IAAA;MAAA,MAAAE,OAAA,GAAFjJ,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAwoC8+EyE,OAAA,CAAAC,YAAA,CAAA7E,MAAmB,EAAC;IAAA,CAAC,CAAC,0BAAA8E,8JAAA9E,MAAA;MAxoCtgFrE,EAAE,CAAAsE,aAAA,CAAAyE,IAAA;MAAA,MAAAK,OAAA,GAAFpJ,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAwoC87F4E,OAAA,CAAAC,cAAA,CAAAhF,MAAqB,EAAC;IAAA,CAAjd,CAAC;IAxoCtgFrE,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAwoC8/F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA+C,MAAA,GAxoCjgGzF,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,iBAAFrD,EAAE,CAAAsJ,WAAA,OAAA7D,MAAA,CAAA8D,YAAA,CAwoC2mF,CAAC,YAAA9D,MAAA,CAAA+D,OAAD,CAAC,YAAA/D,MAAA,CAAAgE,OAAD,CAAC,WAAAhE,MAAA,CAAAiE,MAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAjH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkH,IAAA,GAxoC9mF5J,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,+CAwoCmgH,CAAC;IAxoCtgH9C,EAAE,CAAAmE,UAAA,wBAAA0F,kIAAAxF,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAAsF,IAAA;MAAA,MAAAE,OAAA,GAAF9J,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAwoCsqGsF,OAAA,CAAAZ,YAAA,CAAA7E,MAAmB,EAAC;IAAA,CAAC,CAAC,0BAAA0F,oIAAA1F,MAAA;MAxoC9rGrE,EAAE,CAAAsE,aAAA,CAAAsF,IAAA;MAAA,MAAAI,OAAA,GAAFhK,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAwoC2+GwF,OAAA,CAAAX,cAAA,CAAAhF,MAAqB,EAAC;IAAA,CAAtU,CAAC;IAxoC9rGrE,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAwoC2iH,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAiD,OAAA,GAxoC9iH3F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,iBAAFrD,EAAE,CAAAsJ,WAAA,OAAA3D,OAAA,CAAA4D,YAAA,CAwoCowG,CAAC,WAxoCvwGvJ,EAAE,CAAAsJ,WAAA,OAAA3D,OAAA,CAAAsE,cAAA,CAwoCowG,CAAC,YAAAtE,OAAA,CAAA6D,OAAD,CAAC,YAAA7D,OAAA,CAAA8D,OAAD,CAAC;EAAA;AAAA;AAAA,SAAAS,wDAAAxH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCvwG1C,EAAE,CAAA8C,cAAA,SAwoCszE,CAAC;IAxoCzzE9C,EAAE,CAAA+C,UAAA,IAAA+F,+FAAA,mDAwoC8/F,CAAC;IAxoCjgG9I,EAAE,CAAA+C,UAAA,IAAA4G,qEAAA,iCAAF3J,EAAE,CAAA0G,sBAwoCmlH,CAAC;IAxoCtlH1G,EAAE,CAAAgD,YAAA,CAwoC+mH,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA6C,GAAA,GAxoClnHvF,EAAE,CAAAoD,WAAA;IAAA,MAAAH,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuD,SAAA,EAwoC64E,CAAC;IAxoCh5EvD,EAAE,CAAAqD,UAAA,SAAAJ,MAAA,CAAAyG,MAAA,OAwoC64E,CAAC,aAAAnE,GAAD,CAAC;EAAA;AAAA;AAAA,SAAA4E,yFAAAzH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0H,IAAA,GAxoCh5EpK,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,8CAwoC26I,CAAC;IAxoC96I9C,EAAE,CAAAmE,UAAA,0BAAAkG,sJAAAhG,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAA8F,IAAA;MAAA,MAAAE,OAAA,GAAFtK,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAwoCm5I8F,OAAA,CAAAC,cAAA,CAAAlG,MAAqB,EAAC;IAAA,CAAC,CAAC;IAxoC76IrE,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAiF,MAAA;IAAFjF,EAAE,CAAAgD,YAAA,CAwoCk9I,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAwD,MAAA,GAxoCr9IlG,EAAE,CAAAkD,aAAA;IAAA,IAAAsH,OAAA;IAAFxK,EAAE,CAAAqD,UAAA,mBAAFrD,EAAE,CAAAsJ,WAAA,OAAApD,MAAA,CAAAuE,cAAA,CAwoCmzH,CAAC,kBAAAD,OAAA,GAxoCtzHxK,EAAE,CAAAsJ,WAAA,OAAApD,MAAA,CAAAqD,YAAA,oBAAAiB,OAAA,CAAA/F,IAwoCmzH,CAAC,YAAAyB,MAAA,CAAAsD,OAAD,CAAC,YAAAtD,MAAA,CAAAuD,OAAD,CAAC,WAAAvD,MAAA,CAAAwD,MAAD,CAAC,WAxoCtzH1J,EAAE,CAAAsJ,WAAA,QAAApD,MAAA,CAAA+D,cAAA,CAwoCmzH,CAAC,eAAA/D,MAAA,CAAAuC,UAAD,CAAC;EAAA;AAAA;AAAA,SAAAiC,iEAAAhI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCtzH1C,EAAE,CAAA4C,kBAAA,EAwoCwyJ,CAAC;EAAA;AAAA;AAAA,SAAA+H,iEAAAjI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoC3yJ1C,EAAE,CAAA4C,kBAAA,EAwoCklK,CAAC;EAAA;AAAA;AAAA,SAAAgI,gEAAAlI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCrlK1C,EAAE,CAAA8C,cAAA,oCAwoCiyK,CAAC;IAxoCpyK9C,EAAE,CAAAoG,MAAA,YAwoCuyK,CAAC;IAxoC1yKpG,EAAE,CAAAgD,YAAA,CAwoCw0K,CAAC;EAAA;AAAA;AAAA,SAAA6H,gEAAAnI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoC30K1C,EAAE,CAAA8C,cAAA,oCAwoCg6K,CAAC;IAxoCn6K9C,EAAE,CAAAoG,MAAA,QAwoCk6K,CAAC;IAxoCr6KpG,EAAE,CAAAgD,YAAA,CAwoCm8K,CAAC;EAAA;AAAA;AAAA,MAAA8H,IAAA,YAAAA,CAAA/G,EAAA;EAAA;IAAA,4CAAAA;EAAA;AAAA;AAAA,SAAAgH,yDAAArI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCt8K1C,EAAE,CAAAgL,cAAA,CA84C8iB,CAAC;IA94CjjBhL,EAAE,CAAA8C,cAAA,YA84C8iB,CAAC;IA94CjjB9C,EAAE,CAAAiL,SAAA,aA84C29B,CAAC;IA94C99BjL,EAAE,CAAAgD,YAAA,CA84Cu+B,CAAC;EAAA;AAAA;AAAA,MAAAkI,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA,YAAAA,CAAArH,EAAA;EAAA;IAAA,kCAAAA;EAAA;AAAA;AAAA,MAAAsH,IAAA,YAAAA,CAAAtH,EAAA;EAAA;IAAA,mCAAAA;EAAA;AAAA;AAAA,SAAAuH,mDAAA5I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAS,GAAA,GA94C1+BnD,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,WAgzD6vB,CAAC,QAAD,CAAC,eAAD,CAAC;IAhzDhwB9C,EAAE,CAAAmE,UAAA,mBAAAoH,2EAAA;MAAFvL,EAAE,CAAAsE,aAAA,CAAAnB,GAAA;MAAA,MAAA0C,MAAA,GAAF7F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgzD65BqB,MAAA,CAAA2F,MAAA,CAAA3F,MAAA,CAAA4F,MAAA,CAAAC,EAAgB,EAAC;IAAA,CAAC,CAAC;IAhzDl7B1L,EAAE,CAAAoG,MAAA,EAgzDyiC,CAAC;IAhzD5iCpG,EAAE,CAAAgD,YAAA,CAgzDkjC,CAAC,CAAD,CAAC;IAhzDrjChD,EAAE,CAAA8C,cAAA,QAgzD+kC,CAAC,eAAD,CAAC;IAhzDllC9C,EAAE,CAAAmE,UAAA,mBAAAwH,2EAAA;MAAF3L,EAAE,CAAAsE,aAAA,CAAAnB,GAAA;MAAA,MAAAyF,MAAA,GAAF5I,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgzDiuCoE,MAAA,CAAA4C,MAAA,CAAA5C,MAAA,CAAA6C,MAAA,CAAAG,EAAgB,EAAC;IAAA,CAAC,CAAC;IAhzDtvC5L,EAAE,CAAAoG,MAAA,EAgzD62C,CAAC;IAhzDh3CpG,EAAE,CAAAgD,YAAA,CAgzDs3C,CAAC,CAAD,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAhzDz3CjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,gBAAAgD,SAgzD0sB,CAAC,4BAAD,CAAC;IAhzD7sBrG,EAAE,CAAAuD,SAAA,EAgzDwhC,CAAC;IAhzD3hCvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAAmG,IAAA,EAAApI,MAAA,CAAA4I,eAAA,KAAA5I,MAAA,CAAA6I,SAAA,IAgzDwhC,CAAC;IAhzD3hC9L,EAAE,CAAAuD,SAAA,EAgzDyiC,CAAC;IAhzD5iCvD,EAAE,CAAA+L,iBAAA,CAAA9I,MAAA,CAAA6I,SAAA,GAgzDyiC,CAAC;IAhzD5iC9L,EAAE,CAAAuD,SAAA,EAgzD41C,CAAC;IAhzD/1CvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAAmG,IAAA,EAAApI,MAAA,CAAA4I,eAAA,KAAA5I,MAAA,CAAA6I,SAAA,IAgzD41C,CAAC;IAhzD/1C9L,EAAE,CAAAuD,SAAA,EAgzD62C,CAAC;IAhzDh3CvD,EAAE,CAAA+L,iBAAA,CAAA9I,MAAA,CAAA6I,SAAA,GAgzD62C,CAAC;EAAA;AAAA;AAAA,SAAAE,oDAAAtJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuJ,GAAA,GAhzDh3CjM,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,YAgzDg+C,CAAC;IAhzDn+C9C,EAAE,CAAAmE,UAAA,mBAAA+H,yEAAA;MAAFlM,EAAE,CAAAsE,aAAA,CAAA2H,GAAA;MAAA,MAAAtH,MAAA,GAAF3E,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgzD47CG,MAAA,CAAAwH,aAAA,CAAc,EAAC;IAAA,CAAC,CAAC;IAhzD/8CnM,EAAE,CAAAgD,YAAA,CAgzDs+C,CAAC;EAAA;AAAA;AAAA,MAAAoJ,IAAA,YAAAA,CAAArI,EAAA;EAAA;IAAA,oCAAAA;EAAA;AAAA;AAAA,SAAAsI,sEAAA3J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuJ,GAAA,GAhzDz+CjM,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA8C,cAAA,uCAgiEmvC,CAAC;IAhiEtvC9C,EAAE,CAAAmE,UAAA,4BAAAmI,+HAAAjI,MAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAA2H,GAAA;MAAA,MAAAtH,MAAA,GAAF3E,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAwE,WAAA,CAgiE4rCG,MAAA,CAAA4H,YAAA,CAAAlI,MAAmB,EAAC;IAAA,CAAC,CAAC;IAhiEptCrE,EAAE,CAAAgD,YAAA,CAgiEoxC,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAhiEvxCjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAqD,UAAA,mBAAAJ,MAAA,CAAAwI,MAgiEmmC,CAAC,aAAAxI,MAAA,CAAAoC,QAAA,IAAApC,MAAA,CAAAuJ,sBAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,qFAAA/J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhiEtmC1C,EAAE,CAAA4C,kBAAA,EAgiEitD,CAAC;EAAA;AAAA;AAAA,MAAA8J,IAAA,YAAAA,CAAA3I,EAAA;EAAA;IAAA,gCAAAA;EAAA;AAAA;AAAA,SAAA4I,sEAAAjK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhiEptD1C,EAAE,CAAA8C,cAAA,wCAgiEmhD,CAAC,cAAD,CAAC;IAhiEthD9C,EAAE,CAAA+C,UAAA,IAAA0J,oFAAA,0BAgiEitD,CAAC;IAhiEptDzM,EAAE,CAAAgD,YAAA,CAgiEkuD,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAwD,MAAA,GAhiEruDlG,EAAE,CAAAkD,aAAA;IAAA,MAAA8C,GAAA,GAAFhG,EAAE,CAAAoD,WAAA;IAAA,MAAAD,GAAA,GAAFnD,EAAE,CAAAoD,WAAA;IAAFpD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAAwH,IAAA,EAAAxG,MAAA,CAAA0G,WAAA,YAgiEm9C,CAAC,QAAA5G,GAAD,CAAC,aAAAE,MAAA,CAAAb,QAAD,CAAC;IAhiEt9CrF,EAAE,CAAAuD,SAAA,EAgiE+rD,CAAC;IAhiElsDvD,EAAE,CAAAqD,UAAA,qBAAA6C,MAAA,CAAA2G,UAAA,IAAA1J,GAgiE+rD,CAAC;EAAA;AAAA;AAAA,SAAA2J,oDAAApK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhiElsD1C,EAAE,CAAAgL,cAAA,CAgiE4uE,CAAC;IAhiE/uEhL,EAAE,CAAA8C,cAAA,aAgiE4uE,CAAC;IAhiE/uE9C,EAAE,CAAAiL,SAAA,cAgiEusF,CAAC;IAhiE1sFjL,EAAE,CAAAgD,YAAA,CAgiEmtF,CAAC;EAAA;AAAA;AAAA,MAAA+J,IAAA,YAAAA,CAAAhJ,EAAA;EAAA;IAAA,4BAAAA;EAAA;AAAA;AA3vEt0F,IAAIiJ,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvCA,QAAQ,CAACA,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC/C,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAE/B,IAAIC,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI;EACvBA,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI;AAC3B,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AAEnC,IAAIC,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS;EAChCA,UAAU,CAAC,cAAc,CAAC,GAAG,OAAO;EACpCA,UAAU,CAAC,aAAa,CAAC,GAAG,OAAO;EACnCA,UAAU,CAAC,mBAAmB,CAAC,GAAG,KAAK;AAC3C,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AAEnC,SAASC,aAAaA,CAAC1I,IAAI,EAAE2I,WAAW,EAAEC,IAAI,GAAG,SAAS,EAAE;EACxD,IAAIA,IAAI,KAAK,OAAO,EAAE;IAClB,OAAO5I,IAAI,CAAC6I,IAAI,IAAIF,WAAW,CAACE,IAAI;EACxC;EACA,IAAID,IAAI,KAAK,SAAS,EAAE;IACpB,OAAO5I,IAAI,CAAC8I,OAAO,CAACH,WAAW,EAAEC,IAAI,CAAC,IAAI5I,IAAI,CAAC+I,OAAO,CAAC,CAAC,GAAGJ,WAAW,CAACI,OAAO,CAAC,CAAC;EACpF;AACJ;AACA,SAASC,cAAcA,CAAChJ,IAAI,EAAE2I,WAAW,EAAEC,IAAI,GAAG,SAAS,EAAE;EACzD,IAAIA,IAAI,KAAK,OAAO,EAAE;IAClB,OAAO5I,IAAI,CAAC6I,IAAI,IAAIF,WAAW,CAACE,IAAI;EACxC;EACA,IAAID,IAAI,KAAK,SAAS,EAAE;IACpB,OAAO5I,IAAI,CAAC8I,OAAO,CAACH,WAAW,EAAEC,IAAI,CAAC,IAAI5I,IAAI,CAAC+I,OAAO,CAAC,CAAC,IAAIJ,WAAW,CAACI,OAAO,CAAC,CAAC;EACrF;AACJ;AACA,SAASE,SAASA,CAACjJ,IAAI,EAAEkJ,MAAM,EAAEC,KAAK,EAAEP,IAAI,GAAG,SAAS,EAAE;EACtD,IAAIA,IAAI,KAAK,OAAO,EAAE;IAClB,OAAOI,cAAc,CAAChJ,IAAI,EAAEmJ,KAAK,EAAEP,IAAI,CAAC,IAAIF,aAAa,CAAC1I,IAAI,EAAEkJ,MAAM,EAAEN,IAAI,CAAC;EACjF;EACA,IAAIA,IAAI,KAAK,SAAS,EAAE;IACpB,OAAOI,cAAc,CAAChJ,IAAI,EAAEmJ,KAAK,CAAC,IAAIT,aAAa,CAAC1I,IAAI,EAAEkJ,MAAM,CAAC;EACrE;AACJ;AACA,SAASE,OAAOA,CAACC,CAAC,EAAE;EAChB;EACA,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAKF,CAAC,CAACG,OAAO,CAAC;EAC7C;EACCH,CAAC,CAACG,OAAO,IAAI,EAAE,KAAKH,CAAC,CAACI,OAAO,KAAK,IAAI,IAAIJ,CAAC,CAACK,OAAO,KAAK,IAAI,CAAE;EAC/D;EACCL,CAAC,CAACG,OAAO,IAAI,EAAE,KAAKH,CAAC,CAACI,OAAO,KAAK,IAAI,IAAIJ,CAAC,CAACK,OAAO,KAAK,IAAI,CAAE;EAC/D;EACCL,CAAC,CAACG,OAAO,IAAI,EAAE,KAAKH,CAAC,CAACI,OAAO,KAAK,IAAI,IAAIJ,CAAC,CAACK,OAAO,KAAK,IAAI,CAAE;EAC/D;EACCL,CAAC,CAACG,OAAO,IAAI,EAAE,IAAIH,CAAC,CAACG,OAAO,IAAI,EAAG,EAAE;IACtC,OAAO,IAAI;EACf;EACA,OAAO,EAAE,CAACH,CAAC,CAACG,OAAO,GAAG,EAAE,IAAIH,CAAC,CAACG,OAAO,GAAG,EAAE,MAAMH,CAAC,CAACG,OAAO,GAAG,EAAE,IAAIH,CAAC,CAACG,OAAO,GAAG,GAAG,CAAC,CAAC;AACvF;;AAEA;AACA,MAAMG,WAAW,CAAC;EACd,OAAOC,SAASA,CAAC5J,IAAI,EAAE6J,IAAI,EAAE;IACzB,MAAM;MAAEC,eAAe;MAAEC;IAAO,CAAC,GAAGJ,WAAW,CAACK,sBAAsB,CAAChK,IAAI,EAAE6J,IAAI,CAAC;IAClF,MAAMI,aAAa,GAAGjK,IAAI,CAACkK,KAAK,CAAC,GAAG,CAAC,CAACtG,MAAM,KAAK,CAAC;IAClD,MAAMuG,QAAQ,GAAGF,aAAa,GAAGxB,UAAU,CAAC2B,YAAY,GAAG3B,UAAU,CAAC4B,iBAAiB;IACvF,OAAO5M,QAAQ,CAAC6M,UAAU,CAACtK,IAAI,EAAEmK,QAAQ,EAAE;MAAEL,eAAe;MAAEC;IAAO,CAAC,CAAC;EAC3E;EACA,OAAOQ,UAAUA,CAACvK,IAAI,EAAE6J,IAAI,EAAE;IAC1B,IAAI,CAAC7J,IAAI,EAAE;MACP,OAAO,cAAc;IACzB;IACA,MAAM;MAAEiF;IAAO,CAAC,GAAG4E,IAAI;IACvB,MAAMW,UAAU,GAAGb,WAAW,CAACC,SAAS,CAAC5J,IAAI,EAAE6J,IAAI,CAAC,CAACY,SAAS,CAACd,WAAW,CAACe,cAAc,CAAC;IAC1F,IAAI,CAACF,UAAU,CAACG,OAAO,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAI1F,MAAM,KAAK,EAAE,EAAE;MACf,OAAOuF,UAAU,CAACI,cAAc,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErN,QAAQ,CAACsN,WAAW,CAAC,EAAE;QAAEC,MAAM,EAAE/F,MAAM,KAAK,EAAE;QAAE6E,eAAe,EAAEH,WAAW,CAACsB;MAAyB,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IAC7N;IACA,OAAOV,UAAU,CAACW,SAAS,CAAC;MACxBC,aAAa,EAAE,KAAK;MACpBC,oBAAoB,EAAE,IAAI;MAC1BC,eAAe,EAAE;IACrB,CAAC,CAAC,CAACJ,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EACrD;EACA,OAAOK,kBAAkBA,CAACvL,IAAI,EAAE6J,IAAI,GAAG,CAAC,CAAC,EAAE;IACvC,MAAM;MAAE5E,MAAM,GAAG0E,WAAW,CAAC6B,cAAc;MAAEzB,MAAM,GAAGJ,WAAW,CAACe;IAAe,CAAC,GAAGb,IAAI;IACzF,MAAM4B,SAAS,GAAGxG,MAAM,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK;IAC/C,MAAMyG,UAAU,GAAGb,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErN,QAAQ,CAACsN,WAAW,CAAC,EAAE;MAAEU;IAAU,CAAC,CAAC;IACxF,MAAMtB,QAAQ,GAAIlF,MAAM,KAAK,EAAE,GAAIwD,UAAU,CAAC4B,iBAAiB,GAAG5B,UAAU,CAAC2B,YAAY;IACzF,MAAMuB,SAAS,GAAGd,MAAM,CAACC,MAAM,CAAC;MAAEf,MAAM,EAAEF,IAAI,CAACE,MAAM;MAAED,eAAe,EAAED,IAAI,CAACC;IAAgB,CAAC,EAAE4B,UAAU,CAAC;IAC3G,OAAOjO,QAAQ,CAAC6M,UAAU,CAACtK,IAAI,EAAEmK,QAAQ,CAAC,CAACM,SAAS,CAACV,MAAM,CAAC,CAACa,cAAc,CAACe,SAAS,CAAC,CAACT,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EAClH;EACA,OAAOU,eAAeA,CAAC5L,IAAI,EAAE6L,GAAG,EAAEC,GAAG,EAAEC,WAAW,EAAE/H,UAAU,EAAEiB,MAAM,EAAE;IACpE,IAAI,CAACjF,IAAI,EAAE;MACP;IACJ;IACA,MAAMgM,aAAa,GAAG,IAAI,CAACpC,SAAS,CAAC5J,IAAI,EAAE;MAAEiF;IAAO,CAAC,CAAC;IACtD,MAAMgH,OAAO,GAAGD,aAAa,CAACE,MAAM;IACpC,IAAIlI,UAAU,IAAIiI,OAAO,KAAKA,OAAO,IAAIA,OAAO,GAAGjI,UAAU,KAAK,CAAC,EAAE;MACjE,MAAM,IAAImI,KAAK,CAAE,kBAAiBF,OAAQ,qCAAoCjI,UAAW,EAAC,CAAC;IAC/F;IACA,MAAMoI,OAAO,GAAIP,GAAG,IAAI,CAACC,GAAG,IACrBpD,aAAa,CAACsD,aAAa,EAAEH,GAAG,EAAEE,WAAW,CAAC;IACrD,MAAMM,QAAQ,GAAIP,GAAG,IAAI,CAACD,GAAG,IACtB7C,cAAc,CAACgD,aAAa,EAAEF,GAAG,EAAEC,WAAW,CAAC;IACtD,MAAMO,OAAO,GAAIT,GAAG,IAAIC,GAAG,IACpB7C,SAAS,CAAC+C,aAAa,EAAEH,GAAG,EAAEC,GAAG,EAAEC,WAAW,CAAC;IACtD,MAAMQ,WAAW,GAAG,CAACV,GAAG,IAAI,CAACC,GAAG;IAChC,OAAOM,OAAO,IAAIC,QAAQ,IAAIC,OAAO,IAAIC,WAAW;EACxD;EACA;AACJ;AACA;EACI,OAAOC,UAAUA,CAACC,WAAW,EAAExH,MAAM,EAAE+B,MAAM,EAAE;IAC3C,IAAI/B,MAAM,KAAK,EAAE,EAAE;MACf,OAAOwH,WAAW;IACtB;IACA,MAAM5D,IAAI,GAAG7B,MAAM,KAAKwB,UAAU,CAACvB,EAAE,GAAGwF,WAAW,GAAGA,WAAW,GAAG,EAAE;IACtE,IAAIzF,MAAM,KAAKwB,UAAU,CAACvB,EAAE,IAAI4B,IAAI,KAAK,EAAE,EAAE;MACzC,OAAO,CAAC;IACZ,CAAC,MACI,IAAI7B,MAAM,KAAKwB,UAAU,CAACrB,EAAE,IAAI0B,IAAI,KAAK,EAAE,EAAE;MAC9C,OAAO,EAAE;IACb;IACA,OAAOA,IAAI;EACf;EACA,OAAO6D,oBAAoBA,CAAC1M,IAAI,EAAEiF,MAAM,EAAE;IACtC,MAAMyG,UAAU,GAAGzG,MAAM,KAAK,EAAE,GAAGwD,UAAU,CAACkE,WAAW,GAAGlE,UAAU,CAACmE,MAAM;IAC7E,OAAO5M,IAAI,CAAC6M,WAAW,CAAC;MACpB/C,eAAe,EAAEH,WAAW,CAACsB,wBAAwB;MACrDlB,MAAM,EAAEJ,WAAW,CAACe;IACxB,CAAC,CAAC,CAACoC,QAAQ,CAACpB,UAAU,CAAC,CAACR,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EACnD;EACA,OAAOlB,sBAAsBA,CAAChK,IAAI,EAAE6J,IAAI,EAAE;IACtC,MAAMkD,YAAY,GAAG;MAAEjD,eAAe,EAAED,IAAI,CAACC,eAAe;MAAEC,MAAM,EAAEF,IAAI,CAACE;IAAO,CAAC;IACnF,MAAMiD,aAAa,GAAG;MAAElD,eAAe,EAAEH,WAAW,CAACsB,wBAAwB;MAAElB,MAAM,EAAEJ,WAAW,CAACe;IAAe,CAAC;IACnH,OAAOuC,KAAK,CAACC,QAAQ,CAAClN,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG+M,YAAY,GAAGC,aAAa;EACnE;AACJ;AACArD,WAAW,CAAC6B,cAAc,GAAG,EAAE;AAC/B7B,WAAW,CAACe,cAAc,GAAG,OAAO;AACpCf,WAAW,CAACsB,wBAAwB,GAAG,MAAM;AAE7C,MAAMkC,YAAY,GAAG;EACjBnN,IAAI,EAAE,EAAE;EACR6C,KAAK,EAAE;AACX,CAAC;AACD,MAAMuK,cAAc,GAAG;EACnBpN,IAAI,EAAE,CAAC;EACP6C,KAAK,EAAE;AACX,CAAC;AACD,MAAMwK,4BAA4B,CAAC;EAC/BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAI7Q,eAAe,CAACyQ,YAAY,CAAC;IACpD,IAAI,CAACK,aAAa,GAAG,IAAI9Q,eAAe,CAAC0Q,cAAc,CAAC;IACxD,IAAI,CAACK,aAAa,GAAG,IAAI/Q,eAAe,CAAC8L,UAAU,CAACvB,EAAE,CAAC;EAC3D;EACA,IAAI4B,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAAC0E,WAAW,CAACG,IAAI,CAAC7E,IAAI,CAAC;EAC/B;EACA,IAAI/D,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACyI,WAAW,CAACI,YAAY,CAAC,CAAC;EAC1C;EACA,IAAIzB,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACsB,aAAa,CAACE,IAAI,CAACxB,MAAM,CAAC;EACnC;EACA,IAAIlG,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwH,aAAa,CAACG,YAAY,CAAC,CAAC;EAC5C;EACA,IAAI3G,MAAMA,CAACA,MAAM,EAAE;IACf,MAAM4G,aAAa,GAAI5G,MAAM,KAAKwB,UAAU,CAACvB,EAAE,IAAMD,MAAM,KAAKwB,UAAU,CAACrB,EAAG;IAC9E,IAAIyG,aAAa,EAAE;MACf,IAAI,CAACH,aAAa,CAACC,IAAI,CAAC1G,MAAM,CAAC;IACnC;EACJ;EACA,IAAIxB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACiI,aAAa,CAACE,YAAY,CAAC,CAAC;EAC5C;EACAE,yBAAyBA,CAAC7N,IAAI,EAAE6L,GAAG,EAAEC,GAAG,EAAE7G,MAAM,EAAEjB,UAAU,EAAE;IAC1D;IACA,IAAI;MACA,IAAI2F,WAAW,CAACiC,eAAe,CAAC5L,IAAI,EAAE6L,GAAG,EAAEC,GAAG,EAAE,SAAS,EAAE9H,UAAU,CAAC,EAAE;QACpE,IAAI,CAAC8J,cAAc,CAAC9N,IAAI,EAAEiF,MAAM,CAAC;MACrC;IACJ,CAAC,CACD,OAAOoE,CAAC,EAAE;MACN0E,OAAO,CAACC,KAAK,CAAC3E,CAAC,CAAC;IACpB;EACJ;EACA4E,WAAWA,CAAChJ,MAAM,EAAE;IAChB,MAAMH,YAAY,GAAG,IAAI,CAACyI,WAAW,CAACW,QAAQ,CAAC,CAAC,CAAClO,IAAI;IACrD,MAAMgG,cAAc,GAAG,IAAI,CAACwH,aAAa,CAACU,QAAQ,CAAC,CAAC,CAAClO,IAAI;IACzD,MAAM6I,IAAI,GAAG/D,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGqI,YAAY,CAACnN,IAAI;IACpE,MAAMkM,MAAM,GAAGlG,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGoH,cAAc,CAACpN,IAAI;IAC5E,MAAMgH,MAAM,GAAG/B,MAAM,KAAK,EAAE,GAAG,IAAI,CAACwI,aAAa,CAACS,QAAQ,CAAC,CAAC,GAAG,EAAE;IACjE,MAAMlO,IAAI,GAAI,GAAE6I,IAAK,IAAGqD,MAAO,IAAGlF,MAAO,EAAC,CAACmH,IAAI,CAAC,CAAC;IACjD,OAAOxE,WAAW,CAACY,UAAU,CAACvK,IAAI,EAAE;MAAEiF;IAAO,CAAC,CAAC;EACnD;EACA6I,cAAcA,CAAC9N,IAAI,EAAEiF,MAAM,EAAE;IACzB,MAAMmJ,WAAW,GAAGzE,WAAW,CAACC,SAAS,CAAC5J,IAAI,EAAE;MAAEiF;IAAO,CAAC,CAAC,CAACoJ,QAAQ,CAAC,CAAC;IACtE,IAAI5Q,QAAQ,CAAC6Q,UAAU,CAACF,WAAW,CAAC,CAACzD,OAAO,EAAE;MAC1C,MAAM3D,MAAM,GAAGhH,IAAI,CAACuO,MAAM,CAACvO,IAAI,CAAC4D,MAAM,GAAG,CAAC,CAAC,CAAC4K,WAAW,CAAC,CAAC;MACzD,MAAM3F,IAAI,GAAGuF,WAAW,CAACK,QAAQ,CAAC,CAAC;MACnC,IAAI,CAAC5F,IAAI,GAAGgC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqC,YAAY,CAAC,EAAE;QAAEnN,IAAI,EAAE0O,kBAAkB,CAAC7F,IAAI,EAAE7B,MAAM;MAAE,CAAC,CAAC;MACtG,IAAI,CAACkF,MAAM,GAAGrB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsC,cAAc,CAAC,EAAE;QAAEpN,IAAI,EAAEoO,WAAW,CAACO,UAAU,CAAC;MAAE,CAAC,CAAC;MAClG,IAAI,CAAC3H,MAAM,GAAGA,MAAM;IACxB,CAAC,MACI;MACD,IAAI,CAAC4H,SAAS,CAAC,CAAC;IACpB;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,CAAC/F,IAAI,GAAGgC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqC,YAAY,CAAC;IAC3C,IAAI,CAACjB,MAAM,GAAGrB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsC,cAAc,CAAC;IAC/C,IAAI,CAACpG,MAAM,GAAGwB,UAAU,CAACvB,EAAE;EAC/B;AACJ;AACAoG,4BAA4B,CAACwB,IAAI,YAAAC,qCAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyF1B,4BAA4B;AAAA,CAAoD;AAC1MA,4BAA4B,CAAC2B,KAAK,kBAD8EzT,EAAE,CAAA0T,kBAAA;EAAAC,KAAA,EACY7B,4BAA4B;EAAA8B,OAAA,EAA5B9B,4BAA4B,CAAAwB,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACjL;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFgH9T,EAAE,CAAA+T,iBAAA,CAEtBjC,4BAA4B,EAAc,CAAC;IAC3HkC,IAAI,EAAE/T,UAAU;IAChBgU,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,SAASV,kBAAkBA,CAAC7F,IAAI,EAAE7B,MAAM,EAAE;EACtC,QAAQA,MAAM;IACV,KAAKwB,UAAU,CAACvB,EAAE;MACd,OAAO4B,IAAI,KAAK,CAAC,GAAG,EAAE,GAAGA,IAAI;IACjC,KAAKL,UAAU,CAACrB,EAAE;MACd,OAAO0B,IAAI,KAAK,EAAE,GAAG,EAAE,GAAGA,IAAI,GAAG,EAAE;IACvC;MACI,OAAOA,IAAI;EACnB;AACJ;AAEA,MAAM4G,WAAW,GAAG,IAAIhU,cAAc,CAAC,YAAY,EAAE;EACjD2T,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAMxF,WAAW,CAACe;AAC/B,CAAC,CAAC;AACF,MAAMgF,gBAAgB,GAAG,IAAIjU,cAAc,CAAC,iBAAiB,EAAE;EAC3D2T,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAMxF,WAAW,CAACsB;AAC/B,CAAC,CAAC;AAEF,MAAM0E,iCAAiC,CAAC;EACpCrC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsC,oBAAoB,GAAG,IAAIjT,OAAO,CAAC,CAAC;IACzC,IAAI,CAACkT,mBAAmB,GAAG,IAAIlT,OAAO,CAAC,CAAC;EAC5C;EACA,IAAI+K,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACkI,oBAAoB,CAACjC,YAAY,CAAC,CAAC,CAACmC,IAAI,CAACjT,WAAW,CAAC;MAAEkT,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;EACxG;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,mBAAmB,CAAClC,YAAY,CAAC,CAAC,CAACmC,IAAI,CAACjT,WAAW,CAAC;MAAEkT,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;EACvG;EACAE,aAAaA,CAACC,KAAK,EAAE;IACjB,QAAQA,KAAK,CAACZ,IAAI;MACd,KAAK,OAAO;QACR,IAAI,CAACK,oBAAoB,CAAClC,IAAI,CAACyC,KAAK,CAAC;QACrC;MACJ,KAAK,SAAS;QACV,IAAI,CAACN,mBAAmB,CAACnC,IAAI,CAACyC,KAAK,CAAC;QACpC;MACJ;QACI,MAAM,IAAIhE,KAAK,CAAC,oBAAoB,CAAC;IAC7C;EACJ;AACJ;AACAwD,iCAAiC,CAACd,IAAI,YAAAuB,0CAAArB,CAAA;EAAA,YAAAA,CAAA,IAAyFY,iCAAiC;AAAA,CAAoD;AACpNA,iCAAiC,CAACX,KAAK,kBAxDyEzT,EAAE,CAAA0T,kBAAA;EAAAC,KAAA,EAwDiBS,iCAAiC;EAAAR,OAAA,EAAjCQ,iCAAiC,CAAAd,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC3L;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzDgH9T,EAAE,CAAA+T,iBAAA,CAyDtBK,iCAAiC,EAAc,CAAC;IAChIJ,IAAI,EAAE/T,UAAU;IAChBgU,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMiB,sBAAsB,CAAC;EACzB/C,WAAWA,CAACgD,UAAU,EAAEC,QAAQ,EAAE;IAC9B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGF,UAAU,CAACG,aAAa;EAC3C;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC7R,YAAY,CAAC8R,qBAAqB,CAAC,CAAC;EACpD;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACL,OAAO,CAACM,YAAY;IACxC,MAAM;MAAEC,MAAM;MAAEC;IAAI,CAAC,GAAG,IAAI,CAACC,WAAW;IACxC,MAAMC,YAAY,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,WAAW,IAAIL,MAAM,GAAGF,MAAM;IACrE,MAAMQ,KAAK,GAAGH,YAAY,IAAIF,GAAG,GAAGH,MAAM;IAC1C,MAAMS,QAAQ,GAAGJ,YAAY,IAAIF,GAAG,GAAGH,MAAM;IAC7C,IAAIQ,KAAK,EAAE;MACP,OAAO,KAAK;IAChB,CAAC,MACI,IAAIC,QAAQ,EAAE;MACf,OAAO,QAAQ;IACnB;IACA,OAAO,QAAQ;EACnB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACN,WAAW,GAAG,IAAI,CAACP,UAAU;IAClC,IAAI,CAACc,UAAU,GAAG,IAAI,CAACZ,SAAS;IAChC,IAAI,CAACa,MAAM,CAAC,CAAC;EACjB;EACAC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEX,MAAM;MAAEC;IAAI,CAAC,GAAG,IAAI,CAACN,UAAU;IACvC,MAAMiB,CAAC,GAAG,IAAI,CAACC,yBAAyB,CAACZ,GAAG,EAAED,MAAM,CAAC;IACrD,IAAI,CAACc,QAAQ,CAAC,KAAK,EAAG,GAAEF,CAAE,IAAG,CAAC;EAClC;EACAF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEK,IAAI;MAAEf,MAAM;MAAEC;IAAI,CAAC,GAAG,IAAI,CAACC,WAAW;IAC9C,MAAMU,CAAC,GAAG,IAAI,CAACC,yBAAyB,CAACZ,GAAG,EAAED,MAAM,CAAC;IACrD,IAAI,CAACc,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;IAClC,IAAI,CAACA,QAAQ,CAAC,MAAM,EAAG,GAAEC,IAAK,IAAG,CAAC;IAClC,IAAI,CAACD,QAAQ,CAAC,KAAK,EAAG,GAAEF,CAAE,IAAG,CAAC;EAClC;EACAE,QAAQA,CAACvU,KAAK,EAAEyU,KAAK,EAAE;IACnB,IAAI,CAACxB,QAAQ,CAACsB,QAAQ,CAAC,IAAI,CAACrB,OAAO,EAAElT,KAAK,EAAEyU,KAAK,CAAC;EACtD;EACAH,yBAAyBA,CAACI,QAAQ,EAAEC,WAAW,EAAE;IAC7C,IAAI,IAAI,CAACT,UAAU,KAAK,KAAK,EAAE;MAC3B,OAAOQ,QAAQ,GAAG,IAAI,CAACxB,OAAO,CAACM,YAAY;IAC/C,CAAC,MACI,IAAI,IAAI,CAACU,UAAU,KAAK,QAAQ,EAAE;MACnC,OAAOQ,QAAQ,GAAI,IAAI,CAACxB,OAAO,CAACM,YAAY,GAAG,CAAE;IACrD;IACA,OAAOmB,WAAW;EACtB;AACJ;AACA5B,sBAAsB,CAACxB,IAAI,YAAAqD,+BAAAnD,CAAA;EAAA,YAAAA,CAAA,IAAyFsB,sBAAsB,EApH1B9U,EAAE,CAAA4W,iBAAA,CAoH0C5W,EAAE,CAAC6W,UAAU,GApHzD7W,EAAE,CAAA4W,iBAAA,CAoHoE5W,EAAE,CAAC8W,SAAS;AAAA,CAA4C;AAC9OhC,sBAAsB,CAACiC,IAAI,kBArHqF/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EAqHVc,sBAAsB;EAAAmC,SAAA;EAAAC,YAAA,WAAAC,oCAAAzU,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MArHd1C,EAAE,CAAAmE,UAAA,oBAAAiT,iDAAA;QAAA,OAqHVzU,GAAA,CAAAwT,cAAA,CAAe,CAAC;MAAA,UArHRnW,EAAE,CAAAqX,eAAA;IAAA;EAAA;EAAAC,MAAA;IAAAhU,YAAA;EAAA;AAAA,EAqH2L;AAC7S;EAAA,QAAAwQ,SAAA,oBAAAA,SAAA,KAtHgH9T,EAAE,CAAA+T,iBAAA,CAsHtBe,sBAAsB,EAAc,CAAC;IACrHd,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAEhU,EAAE,CAAC6W;IAAW,CAAC,EAAE;MAAE7C,IAAI,EAAEhU,EAAE,CAAC8W;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAExT,YAAY,EAAE,CAAC;MACxH0Q,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEkC,cAAc,EAAE,CAAC;MACjBnC,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuD,qCAAqC,CAAC;AAE5CA,qCAAqC,CAAClE,IAAI,YAAAmE,8CAAAjE,CAAA;EAAA,YAAAA,CAAA,IAAyFgE,qCAAqC;AAAA,CAAmD;AAC3NA,qCAAqC,CAACE,IAAI,kBAtIsE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAsIKwD,qCAAqC;EAAAP,SAAA;EAAAK,MAAA;IAAAM,aAAA;IAAAtU,YAAA;EAAA;EAAAuU,kBAAA,EAAAjU,GAAA;EAAAkU,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+CAAAxV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtI5C1C,EAAE,CAAAmY,eAAA;MAAFnY,EAAE,CAAA+C,UAAA,IAAAF,oDAAA,gBAsI8Y,CAAC;MAtIjZ7C,EAAE,CAAA+C,UAAA,IAAAU,4DAAA,gCAAFzD,EAAE,CAAA0G,sBAsIqjB,CAAC;MAtIxjB1G,EAAE,CAAA+C,UAAA,IAAAW,4DAAA,gCAAF1D,EAAE,CAAA0G,sBAsIuoB,CAAC;IAAA;IAAA,IAAAhE,EAAA;MAAA,MAAA0V,GAAA,GAtI1oBpY,EAAE,CAAAoD,WAAA;MAAFpD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAAiV,aAsI0P,CAAC,aAAAQ,GAAD,CAAC;IAAA;EAAA;EAAAC,YAAA,GAAuarX,EAAE,CAACsX,IAAI,EAA0ExD,sBAAsB,EAA0E9T,EAAE,CAACuX,gBAAgB;EAAAC,aAAA;AAAA,EAA+F;AACvjC;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAvIgH9T,EAAE,CAAA+T,iBAAA,CAuItByD,qCAAqC,EAAc,CAAC;IACpIxD,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,iCAAiC;MAC3CkB,WAAW,EAAE;IACjB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEb,aAAa,EAAE,CAAC;MAC9B5D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkD,YAAY,EAAE,CAAC;MACf0Q,IAAI,EAAE5T;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMsY,mBAAmB,CAAC;EACtB,OAAOxF,QAAQA,CAACxJ,MAAM,EAAE;IACpB,OAAOiP,KAAK,CAACjP,MAAM,CAAC,CAACkP,IAAI,CAAC,CAAC,CAAC,CAAChX,GAAG,CAAC,CAACiX,CAAC,EAAEC,CAAC,KAAK;MACvC,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMtU,IAAI,GAAGoU,CAAC,GAAGC,CAAC;MAClB,MAAMxR,KAAK,GAAGyR,SAAS,GAAGtU,IAAI;MAC9B,OAAO;QAAEA,IAAI,EAAEA,IAAI,KAAK,EAAE,GAAG,CAAC,GAAGA,IAAI;QAAE6C;MAAM,CAAC;IAClD,CAAC,CAAC;EACN;EACA,OAAO0R,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;IAC/B,IAAIA,MAAM,CAAC5I,GAAG,IAAI4I,MAAM,CAAC3I,GAAG,EAAE;MAC1B,OAAO0I,KAAK,CAACrX,GAAG,CAAC4U,KAAK,IAAI;QACtB,MAAMlJ,IAAI,GAAG4L,MAAM,CAACxP,MAAM,KAAK,EAAE,GAAG8M,KAAK,CAAC/R,IAAI,GAAG2J,WAAW,CAAC6C,UAAU,CAACuF,KAAK,CAAC/R,IAAI,EAAEyU,MAAM,CAACxP,MAAM,EAAEwP,MAAM,CAACzN,MAAM,CAAC;QACjH,MAAM0N,WAAW,GAAGjX,QAAQ,CAACkX,UAAU,CAAC;UAAE9L;QAAK,CAAC,CAAC,CAACiE,QAAQ,CAACrE,UAAU,CAACmE,MAAM,CAAC;QAC7E,OAAO/B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiH,KAAK,CAAC,EAAE;UAAEnR,QAAQ,EAAE,CAAC+I,WAAW,CAACiC,eAAe,CAAC8I,WAAW,EAAED,MAAM,CAAC5I,GAAG,EAAE4I,MAAM,CAAC3I,GAAG,EAAE,OAAO;QAAE,CAAC,CAAC;MAC5I,CAAC,CAAC;IACN;IACA,OAAO0I,KAAK;EAChB;EACA,OAAO7F,UAAUA,CAACiG,GAAG,GAAG,CAAC,EAAE;IACvB,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMP,SAAS,GAAG,GAAG,GAAGO,YAAY;IACpC,MAAM5I,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIoI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,YAAY,EAAER,CAAC,EAAE,EAAE;MACnC,MAAMxR,KAAK,GAAGyR,SAAS,GAAGD,CAAC;MAC3B,IAAIA,CAAC,GAAGO,GAAG,KAAK,CAAC,EAAE;QACf3I,OAAO,CAAC6I,IAAI,CAAC;UAAE9U,IAAI,EAAEqU,CAAC;UAAExR,KAAK,EAAEA,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG;QAAI,CAAC,CAAC;MAC/D;IACJ;IACA,OAAOoJ,OAAO;EAClB;EACA,OAAO8I,cAAcA,CAAC9I,OAAO,EAAEnH,YAAY,EAAE2P,MAAM,EAAE;IACjD,IAAIA,MAAM,CAAC5I,GAAG,IAAI4I,MAAM,CAAC3I,GAAG,EAAE;MAC1B,MAAMjD,IAAI,GAAGc,WAAW,CAAC6C,UAAU,CAAC1H,YAAY,EAAE2P,MAAM,CAACxP,MAAM,EAAEwP,MAAM,CAACzN,MAAM,CAAC;MAC/E,OAAOiF,OAAO,CAAC9O,GAAG,CAAC4U,KAAK,IAAI;QACxB,MAAM2C,WAAW,GAAGjX,QAAQ,CAACkX,UAAU,CAAC;UAAE9L,IAAI;UAAEqD,MAAM,EAAE6F,KAAK,CAAC/R;QAAK,CAAC,CAAC,CAAC8M,QAAQ,CAACrE,UAAU,CAACmE,MAAM,CAAC;QACjG,OAAO/B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiH,KAAK,CAAC,EAAE;UAAEnR,QAAQ,EAAE,CAAC+I,WAAW,CAACiC,eAAe,CAAC8I,WAAW,EAAED,MAAM,CAAC5I,GAAG,EAAE4I,MAAM,CAAC3I,GAAG,EAAE,SAAS;QAAE,CAAC,CAAC;MAC9I,CAAC,CAAC;IACN;IACA,OAAOG,OAAO;EAClB;AACJ;AAEA,MAAM+I,iBAAiB,CAAC;EACpB1H,WAAWA,CAACvD,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAkL,SAASA,CAACjV,IAAI,EAAEO,QAAQ,EAAE2U,iBAAiB,GAAG,KAAK,EAAE;IACjD,IAAIlV,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;MAC7B,OAAO,EAAE;IACb;IACA,QAAQO,QAAQ;MACZ,KAAKgI,QAAQ,CAACpF,IAAI;QAAE;UAChB,MAAM8B,MAAM,GAAIjF,IAAI,KAAK,CAAC,IAAIkV,iBAAiB,GAAI,IAAI,GAAG,GAAG;UAC7D,OAAO,IAAI,CAAC3K,UAAU,CAAC,MAAM,EAAEvK,IAAI,EAAEiF,MAAM,CAAC;QAChD;MACA,KAAKsD,QAAQ,CAACtE,MAAM;QAChB,OAAO,IAAI,CAACsG,UAAU,CAAC,QAAQ,EAAEvK,IAAI,EAAE,IAAI,CAAC;MAChD;QACI,MAAM,IAAImM,KAAK,CAAE,mCAAkC5L,QAAS,EAAC,CAAC;IACtE;EACJ;EACAgK,UAAUA,CAAC4K,WAAW,EAAEnV,IAAI,EAAEiF,MAAM,EAAE;IAClC,IAAI;MACA,OAAOxH,QAAQ,CAACkX,UAAU,CAAC;QAAE,CAACQ,WAAW,GAAG,CAACnV;MAAK,CAAC,CAAC,CAACyK,SAAS,CAAC,IAAI,CAACV,MAAM,CAAC,CAAC+C,QAAQ,CAAC7H,MAAM,CAAC;IAChG,CAAC,CACD,OAAOmQ,EAAE,EAAE;MACP,MAAM,IAAIjJ,KAAK,CAAE,iCAAgCnM,IAAK,gBAAe,IAAI,CAAC+J,MAAO,EAAC,CAAC;IACvF;EACJ;AACJ;AACAiL,iBAAiB,CAACnG,IAAI,YAAAwG,0BAAAtG,CAAA;EAAA,YAAAA,CAAA,IAAyFiG,iBAAiB,EA3NhBzZ,EAAE,CAAA4W,iBAAA,CA2NgC1C,WAAW;AAAA,CAAuC;AACpMuF,iBAAiB,CAACM,KAAK,kBA5NyF/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EA4NLyF,iBAAiB;EAAAS,IAAA;AAAA,EAA0B;AACxJ;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KA7NgH9T,EAAE,CAAA+T,iBAAA,CA6NtB0F,iBAAiB,EAAc,CAAC;IAChHzF,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjG,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QAC9DnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMkG,cAAc,CAAC;EACjBrI,WAAWA,CAACvD,MAAM,EAAED,eAAe,EAAE;IACjC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,eAAe,GAAGA,eAAe;EAC1C;EACAmL,SAASA,CAACjV,IAAI,EAAEO,QAAQ,GAAGgI,QAAQ,CAACpF,IAAI,EAAE;IACtC,IAAInD,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;MAC7B,OAAO,EAAE;IACb;IACA,IAAI,CAACiN,KAAK,CAAC,CAACjN,IAAI,CAAC,EAAE;MACf,OAAOA,IAAI;IACf;IACA,IAAIO,QAAQ,KAAKgI,QAAQ,CAACtE,MAAM,EAAE;MAC9B,OAAO,IAAI,CAAC2F,SAAS,CAAC5J,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC4J,SAAS,CAAC5J,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC;EAC5C;EACA4J,SAASA,CAAC5J,IAAI,EAAEiF,MAAM,EAAEkQ,WAAW,EAAE;IACjC,MAAM3K,UAAU,GAAG/M,QAAQ,CAAC6M,UAAU,CAACsL,MAAM,CAAC5V,IAAI,CAAC,EAAEiF,MAAM,EAAE;MACzD6E,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC,CAACoL,WAAW,CAAC;IACf,IAAI,CAAClI,KAAK,CAACzC,UAAU,CAAC,EAAE;MACpB,OAAOA,UAAU;IACrB;IACA,MAAM,IAAI2B,KAAK,CAAE,uBAAsBnM,IAAK,EAAC,CAAC;EAClD;AACJ;AACA2V,cAAc,CAAC9G,IAAI,YAAAgH,uBAAA9G,CAAA;EAAA,YAAAA,CAAA,IAAyF4G,cAAc,EAnQVpa,EAAE,CAAA4W,iBAAA,CAmQ0B1C,WAAW,OAnQvClU,EAAE,CAAA4W,iBAAA,CAmQkDzC,gBAAgB;AAAA,CAAuC;AAC3NiG,cAAc,CAACL,KAAK,kBApQ4F/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EAoQRoG,cAAc;EAAAF,IAAA;AAAA,EAAuB;AAC/IE,cAAc,CAAC3G,KAAK,kBArQ4FzT,EAAE,CAAA0T,kBAAA;EAAAC,KAAA,EAqQFyG,cAAc;EAAAxG,OAAA,EAAdwG,cAAc,CAAA9G;AAAA,EAAG;AACjI;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAtQgH9T,EAAE,CAAA+T,iBAAA,CAsQtBqG,cAAc,EAAc,CAAC;IAC7GpG,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,EAAE;IACCjG,IAAI,EAAE/T;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+T,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QAC9DnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEF,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QAClCnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACE,gBAAgB;MAC3B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMoG,kBAAkB,CAAC;EACrBxI,WAAWA,CAACkD,OAAO,EAAEuF,QAAQ,EAAE;IAC3B,IAAI,CAACvF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,QAAQ,CAACC,aAAa;EACpD;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,aAAa,EAAE;MACpB;MACAC,UAAU,CAAC,MAAM,IAAI,CAAC3F,OAAO,CAACC,aAAa,CAAC2F,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC,CAAC;IAC/E;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV;IACAH,UAAU,CAAC,MAAM,IAAI,CAACH,aAAa,CAACI,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;EACvE;AACJ;AACAP,kBAAkB,CAACjH,IAAI,YAAA0H,2BAAAxH,CAAA;EAAA,YAAAA,CAAA,IAAyF+G,kBAAkB,EAtSlBva,EAAE,CAAA4W,iBAAA,CAsSkC5W,EAAE,CAAC6W,UAAU,GAtSjD7W,EAAE,CAAA4W,iBAAA,CAsS4D3V,QAAQ;AAAA,CAA4D;AAClPsZ,kBAAkB,CAACxD,IAAI,kBAvSyF/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EAuSduG,kBAAkB;EAAAtD,SAAA;EAAAK,MAAA;IAAAqD,aAAA;EAAA;EAAAM,QAAA,GAvSNjb,EAAE,CAAAkb,oBAAA;AAAA,EAuSkJ;AACpQ;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KAxSgH9T,EAAE,CAAA+T,iBAAA,CAwStBwG,kBAAkB,EAAc,CAAC;IACjHvG,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAEhU,EAAE,CAAC6W;IAAW,CAAC,EAAE;MAAE7C,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QACvFnG,IAAI,EAAEvT;MACV,CAAC,EAAE;QACCuT,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAAChT,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0Z,aAAa,EAAE,CAAC;MAC5C3G,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMkH,yCAAyC,CAAC;EAC5CpJ,WAAWA,CAACqJ,cAAc,EAAEC,iBAAiB,EAAE;IAC3C,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,eAAe,GAAG,IAAI5a,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC6a,WAAW,GAAG,IAAI7a,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC8a,OAAO,GAAG,IAAI9a,YAAY,CAAC,CAAC;IACjC,IAAI,CAAC+a,SAAS,GAAG,IAAI/a,YAAY,CAAC,CAAC;EACvC;EACA,IAAI+G,YAAYA,CAAA,EAAG;IACf,IAAI,CAAC,CAAC,IAAI,CAAChD,IAAI,EAAE;MACb,OAAO,IAAI,CAACiX,QAAQ,CAACC,IAAI,CAACnI,CAAC,IAAIA,CAAC,CAAC/O,IAAI,KAAK,CAAC,IAAI,CAACA,IAAI,CAAC;IACzD;EACJ;EACAmX,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAAC/V,WAAW,GAAG,IAAIzD,WAAW,CAAC;QAAEmU,KAAK,EAAE,IAAI,CAACsF,eAAe,CAAC,IAAI,CAACrX,IAAI,CAAC;QAAEY,QAAQ,EAAE,IAAI,CAACA;MAAS,CAAC,CAAC;MACvG,IAAI,CAACS,WAAW,CAACiW,YAAY,CAACxH,IAAI,CAAChT,GAAG,CAAEiV,KAAK,IAAK;QAC9C,IAAIA,KAAK,CAACnO,MAAM,GAAG,CAAC,EAAE;UAClB,IAAI,CAAC2T,gBAAgB,CAACxF,KAAK,CAACyF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C;MACJ,CAAC,CAAC,EAAEza,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEC,MAAM,CAAE8U,KAAK,IAAK,CAAC0F,sBAAsB,CAAC,IAAI,CAACzX,IAAI,EAAE+R,KAAK,EAAE,IAAI,CAACkF,QAAQ,CAAC,CAAC,EAAEna,GAAG,CAAEiV,KAAK,IAAK,IAAI,CAAC/R,IAAI,GAAG,IAAI,CAAC2W,cAAc,CAAC1B,SAAS,CAAClD,KAAK,EAAE,IAAI,CAACxR,QAAQ,CAAC,CAACmX,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACxX,UAAU,CAAC,CAAC,CAAC;IAC9P;EACJ;EACAG,yBAAyBA,CAAC6P,KAAK,EAAEvH,IAAI,EAAE;IACnCuH,KAAK,CAACyH,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC7X,IAAI;IAC7B,IAAI,CAAC6W,eAAe,CAACnJ,IAAI,CAAC9E,IAAI,CAAC;IAC/B,IAAI,CAACmO,OAAO,CAACrJ,IAAI,CAAC,CAAC;EACvB;EACAvN,UAAUA,CAAA,EAAG;IACT,MAAMH,IAAI,GAAG,IAAI,CAACgD,YAAY;IAC9B,IAAIhD,IAAI,EAAE;MACN,IAAI,CAAC8W,WAAW,CAACpJ,IAAI,CAAC1N,IAAI,CAAC;MAC3B,IAAI,CAAC6X,YAAY,GAAG7X,IAAI,CAACA,IAAI;MAC7B,IAAI,IAAI,CAACoX,UAAU,EAAE;QACjB,IAAI,CAACG,gBAAgB,CAAC,IAAI,CAACF,eAAe,CAACrX,IAAI,CAACA,IAAI,CAAC,CAAC;MAC1D;IACJ;EACJ;EACAmB,SAASA,CAACkI,CAAC,EAAE;IACT,IAAI,CAACD,OAAO,CAACC,CAAC,CAAC,EAAE;MACbA,CAAC,CAACuO,cAAc,CAAC,CAAC;IACtB,CAAC,MACI;MACD,IAAI,CAACE,iBAAiB,CAACzO,CAAC,CAACG,OAAO,CAAC;IACrC;EACJ;EACAsO,iBAAiBA,CAACtO,OAAO,EAAE;IACvB,MAAMuO,QAAQ,GAAG,EAAE;IACnB,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIhY,IAAI;IACR,IAAIwJ,OAAO,KAAKuO,QAAQ,EAAE;MACtB/X,IAAI,GAAG4V,MAAM,CAAC,CAAC,IAAI,CAAC5V,IAAI,IAAI,IAAI,CAACgE,UAAU,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAIwF,OAAO,KAAKwO,UAAU,EAAE;MAC7BhY,IAAI,GAAG4V,MAAM,CAAC,CAAC,IAAI,CAAC5V,IAAI,IAAI,IAAI,CAACgE,UAAU,IAAI,CAAC,CAAC,CAAC;IACtD;IACA,IAAI,CAACiU,iBAAiB,CAACjY,IAAI,EAAE,IAAI,CAACiX,QAAQ,CAAC,EAAE;MACzC,IAAI,CAACjX,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACG,UAAU,CAAC,CAAC;IACrB;EACJ;EACAkX,eAAeA,CAACtF,KAAK,EAAE;IACnB,MAAMvH,UAAU,GAAG,IAAI,CAACmM,cAAc,CAAC1B,SAAS,CAAClD,KAAK,EAAE,IAAI,CAACxR,QAAQ,CAAC,CAACmX,QAAQ,CAAC,CAAC;IACjF,OAAO,IAAI,CAACd,iBAAiB,CAAC3B,SAAS,CAACzK,UAAU,EAAE,IAAI,CAACjK,QAAQ,EAAE,IAAI,CAAC;EAC5E;EACAgX,gBAAgBA,CAACxF,KAAK,EAAE;IACpB,IAAI,CAACmG,gBAAgB,CAACzH,aAAa,CAACsB,KAAK,GAAGA,KAAK;EACrD;AACJ;AACA2E,yCAAyC,CAAC7H,IAAI,YAAAsJ,kDAAApJ,CAAA;EAAA,YAAAA,CAAA,IAAyF2H,yCAAyC,EA/XhEnb,EAAE,CAAA4W,iBAAA,CA+XgFwD,cAAc,GA/XhGpa,EAAE,CAAA4W,iBAAA,CA+X2G6C,iBAAiB;AAAA,CAA4C;AAC1R0B,yCAAyC,CAACzD,IAAI,kBAhYkE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAgYSmH,yCAAyC;EAAAlE,SAAA;EAAA4F,SAAA,WAAAC,gDAAApa,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAhYpD1C,EAAE,CAAA+c,WAAA,CAAAlZ,GAAA;IAAA;IAAA,IAAAnB,EAAA;MAAA,IAAAsa,EAAA;MAAFhd,EAAE,CAAAid,cAAA,CAAAD,EAAA,GAAFhd,EAAE,CAAAkd,WAAA,QAAAva,GAAA,CAAAga,gBAAA,GAAAK,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA7F,MAAA;IAAAoE,QAAA;IAAA1W,QAAA;IAAAP,IAAA;IAAAU,QAAA;IAAA0W,UAAA;IAAApT,UAAA;IAAApD,QAAA;EAAA;EAAA+X,OAAA;IAAA9B,eAAA;IAAAC,WAAA;IAAAC,OAAA;IAAAC,SAAA;EAAA;EAAAR,QAAA,GAAFjb,EAAE,CAAAqd,kBAAA,CAgYgZ,CAACjD,cAAc,EAAEX,iBAAiB,CAAC;EAAA3B,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAqF,mDAAA5a,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAhYrb1C,EAAE,CAAA+C,UAAA,IAAAiB,0DAAA,kBAgYskC,CAAC;MAhYzkChE,EAAE,CAAA+C,UAAA,IAAAuC,gEAAA,gCAAFtF,EAAE,CAAA0G,sBAgYmlD,CAAC;IAAA;IAAA,IAAAhE,EAAA;MAAA,MAAA0V,GAAA,GAhYtlDpY,EAAE,CAAAoD,WAAA;MAAFpD,EAAE,CAAAqD,UAAA,UAAAV,GAAA,CAAAkZ,UAgY8iC,CAAC,aAAAzD,GAAD,CAAC;IAAA;EAAA;EAAAC,YAAA,GAAutDrX,EAAE,CAACsX,IAAI,EAA0ElW,EAAE,CAACmb,oBAAoB,EAAsOvc,EAAE,CAACwc,OAAO,EAAiEpb,EAAE,CAACqb,eAAe,EAAmErb,EAAE,CAACsb,OAAO,EAA2LnD,kBAAkB,EAAgFnY,EAAE,CAACub,oBAAoB,EAA4JlE,iBAAiB;EAAAmE,MAAA;AAAA,EAAK;AACt1H;EAAA,QAAA9J,SAAA,oBAAAA,SAAA,KAjYgH9T,EAAE,CAAA+T,iBAAA,CAiYtBoH,yCAAyC,EAAc,CAAC;IACxInH,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,sCAAsC;MAChDkB,WAAW,EAAE,qDAAqD;MAClEoF,SAAS,EAAE,CAAC,qDAAqD,CAAC;MAClEC,SAAS,EAAE,CAAC1D,cAAc,EAAEX,iBAAiB;IACjD,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzF,IAAI,EAAEoG;IAAe,CAAC,EAAE;MAAEpG,IAAI,EAAEyF;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEiC,QAAQ,EAAE,CAAC;MAC1H1H,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE4E,QAAQ,EAAE,CAAC;MACXgP,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqE,IAAI,EAAE,CAAC;MACPuP,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE+E,QAAQ,EAAE,CAAC;MACX6O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyb,UAAU,EAAE,CAAC;MACb7H,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiF,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEuc,gBAAgB,EAAE,CAAC;MACnB3I,IAAI,EAAErT,SAAS;MACfsT,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEqH,eAAe,EAAE,CAAC;MAClBtH,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE2a,WAAW,EAAE,CAAC;MACdvH,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE4a,OAAO,EAAE,CAAC;MACVxH,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE6a,SAAS,EAAE,CAAC;MACZzH,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,SAASsb,sBAAsBA,CAAC/C,WAAW,EAAE4E,QAAQ,EAAErC,QAAQ,EAAE;EAC7D,MAAMsC,QAAQ,GAAG,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC;EACpC,IAAIC,QAAQ,EAAE;IACV,OAAOtB,iBAAiB,CAACqB,QAAQ,EAAErC,QAAQ,CAAC;EAChD;AACJ;AACA,SAASgB,iBAAiBA,CAACjY,IAAI,EAAEiX,QAAQ,EAAE;EACvC,MAAMjU,YAAY,GAAGiU,QAAQ,CAACC,IAAI,CAACnF,KAAK,IAAIA,KAAK,CAAC/R,IAAI,KAAK,CAACA,IAAI,CAAC;EACjE,OAAO,CAACgD,YAAY,IAAKA,YAAY,IAAIA,YAAY,CAACpC,QAAS;AACnE;AAEA,MAAM6Y,oCAAoC,CAAC;EACvCnM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoM,UAAU,GAAGlR,UAAU;IAC5B,IAAI,CAACmR,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,aAAa,GAAG,IAAI3d,YAAY,CAAC,CAAC;EAC3C;EACA6L,YAAYA,CAACd,MAAM,EAAE;IACjB,IAAI,CAAC2S,iBAAiB,GAAG,IAAI,CAACE,uBAAuB,CAAC7S,MAAM,CAAC;IAC7D,IAAI,IAAI,CAAC2S,iBAAiB,EAAE;MACxB,IAAI,CAACC,aAAa,CAAClM,IAAI,CAAC1G,MAAM,CAAC;IACnC;EACJ;EACAtF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACiY,iBAAiB,GAAG,IAAI;EACjC;EACAE,uBAAuBA,CAAC7S,MAAM,EAAE;IAC5B,MAAMhH,IAAI,GAAG,IAAI,CAAC8Z,uBAAuB,CAAC9S,MAAM,CAAC;IACjD,OAAO,CAAChH,IAAI,CAAC+Z,KAAK,CAAChL,CAAC,IAAIA,CAAC,CAACnO,QAAQ,CAAC;EACvC;EACAkZ,uBAAuBA,CAAC9S,MAAM,EAAE;IAC5B,QAAQ,IAAI,CAACgT,cAAc;MACvB,KAAKzR,QAAQ,CAACpF,IAAI;QACd,OAAO8Q,mBAAmB,CAACM,YAAY,CAAC,IAAI,CAACC,KAAK,EAAE;UAChD3I,GAAG,EAAE,IAAI,CAAC9G,OAAO;UACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB+B;QACJ,CAAC,CAAC;MACN,KAAKuB,QAAQ,CAACtE,MAAM;QAChB,OAAOgQ,mBAAmB,CAACc,cAAc,CAAC,IAAI,CAAC9I,OAAO,EAAE,CAAC,IAAI,CAACnH,YAAY,EAAE;UACxE+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;UACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB+B;QACJ,CAAC,CAAC;MACN;QACI,MAAM,IAAImF,KAAK,CAAC,kBAAkB,CAAC;IAC3C;EACJ;AACJ;AACAsN,oCAAoC,CAAC5K,IAAI,YAAAoL,6CAAAlL,CAAA;EAAA,YAAAA,CAAA,IAAyF0K,oCAAoC;AAAA,CAAmD;AACzNA,oCAAoC,CAACxG,IAAI,kBAvduE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAudIkK,oCAAoC;EAAAjH,SAAA;EAAAK,MAAA;IAAArN,cAAA;IAAAP,MAAA;IAAA+U,cAAA;IAAAxF,KAAA;IAAAvI,OAAA;IAAAlH,OAAA;IAAAC,OAAA;IAAAF,YAAA;IAAAuC,SAAA;EAAA;EAAAsR,OAAA;IAAAiB,aAAA;EAAA;EAAAvG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA0G,8CAAAjc,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvd1C1C,EAAE,CAAA8C,cAAA,YAudga,CAAC,eAAD,CAAC;MAvdna9C,EAAE,CAAAmE,UAAA,mBAAAya,sEAAA;QAAA,OAudymBjc,GAAA,CAAA4J,YAAA,CAAA5J,GAAA,CAAAwb,UAAA,CAAAzS,EAA0B,CAAC;MAAA,CAAC,CAAC;MAvdxoB1L,EAAE,CAAAoG,MAAA,EAudyrB,CAAC;MAvd5rBpG,EAAE,CAAAgD,YAAA,CAudksB,CAAC;MAvdrsBhD,EAAE,CAAA8C,cAAA,eAudi7B,CAAC;MAvdp7B9C,EAAE,CAAAmE,UAAA,mBAAA0a,sEAAA;QAAA,OAudy3Blc,GAAA,CAAA4J,YAAA,CAAA5J,GAAA,CAAAwb,UAAA,CAAAvS,EAA0B,CAAC;MAAA,CAAC,CAAC;MAvdx5B5L,EAAE,CAAAoG,MAAA,EAudi8B,CAAC;MAvdp8BpG,EAAE,CAAAgD,YAAA,CAud08B,CAAC;MAvd78BhD,EAAE,CAAA+C,UAAA,IAAAgD,mDAAA,gBAudspC,CAAC;MAvdzpC/F,EAAE,CAAAgD,YAAA,CAud8pC,CAAC;IAAA;IAAA,IAAAN,EAAA;MAvdjqC1C,EAAE,CAAAuD,SAAA,EAud0kB,CAAC;MAvd7kBvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAApB,GAAA,EAAAnB,GAAA,CAAAsH,cAAA,KAAAtH,GAAA,CAAAwb,UAAA,CAAAzS,EAAA,CAud0kB,CAAC;MAvd7kB1L,EAAE,CAAAuD,SAAA,EAudyrB,CAAC;MAvd5rBvD,EAAE,CAAA+L,iBAAA,CAAApJ,GAAA,CAAAmJ,SAAA,GAudyrB,CAAC;MAvd5rB9L,EAAE,CAAAuD,SAAA,EAudk2B,CAAC;MAvdr2BvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAApB,GAAA,EAAAnB,GAAA,CAAAsH,cAAA,KAAAtH,GAAA,CAAAwb,UAAA,CAAAvS,EAAA,CAudk2B,CAAC;MAvdr2B5L,EAAE,CAAAuD,SAAA,EAudi8B,CAAC;MAvdp8BvD,EAAE,CAAA+L,iBAAA,CAAApJ,GAAA,CAAAmJ,SAAA,GAudi8B,CAAC;MAvdp8B9L,EAAE,CAAAuD,SAAA,EAudykC,CAAC;MAvd5kCvD,EAAE,CAAAqD,UAAA,UAAAV,GAAA,CAAAyb,iBAudykC,CAAC;IAAA;EAAA;EAAA/F,YAAA,GAA02CrX,EAAE,CAACwc,OAAO,EAAiExc,EAAE,CAACsX,IAAI;EAAAsF,MAAA;EAAAkB,IAAA;IAAAC,SAAA,EAA+E,CAC/rFld,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;MAAE2X,SAAS,EAAE;IAAW,CAAC,CAAC,EAChC1X,OAAO,CAAC,KAAK,EAAED,KAAK,CAAC;MAAE2X,SAAS,EAAE;IAAW,CAAC,CAAC,CAAC,EAChDzX,QAAQ,CAAC,CACLD,OAAO,CAAC,IAAI,EAAED,KAAK,CAAC;MAAEid,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,EACpChd,OAAO,CAAC,KAAK,EAAED,KAAK,CAAC;MAAEid,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CACxC,CAAC,CACL,CAAC,CACL,CAAC;EACL;AAAA,EAAG;AACR;EAAA,QAAAlL,SAAA,oBAAAA,SAAA,KAnegH9T,EAAE,CAAA+T,iBAAA,CAmetBmK,oCAAoC,EAAc,CAAC;IACnIlK,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,gCAAgC;MAC1CkB,WAAW,EAAE,+CAA+C;MAC5DoF,SAAS,EAAE,CAAC,+CAA+C,CAAC;MAC5DoB,UAAU,EAAE,CACRpd,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;QAAE2X,SAAS,EAAE;MAAW,CAAC,CAAC,EAChC1X,OAAO,CAAC,KAAK,EAAED,KAAK,CAAC;QAAE2X,SAAS,EAAE;MAAW,CAAC,CAAC,CAAC,EAChDzX,QAAQ,CAAC,CACLD,OAAO,CAAC,IAAI,EAAED,KAAK,CAAC;QAAEid,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EACpChd,OAAO,CAAC,KAAK,EAAED,KAAK,CAAC;QAAEid,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CACxC,CAAC,CACL,CAAC,CACL,CAAC;IAEV,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE/U,cAAc,EAAE,CAAC;MAC/B+J,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqe,cAAc,EAAE,CAAC;MACjBzK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE6Y,KAAK,EAAE,CAAC;MACRjF,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsQ,OAAO,EAAE,CAAC;MACVsD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoJ,OAAO,EAAE,CAAC;MACVwK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVuK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmJ,YAAY,EAAE,CAAC;MACfyK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE0L,SAAS,EAAE,CAAC;MACZkI,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEie,aAAa,EAAE,CAAC;MAChBrK,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMse,kCAAkC,CAAC;EACrCnN,WAAWA,CAACvD,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxJ,QAAQ,GAAGgI,QAAQ;IACxB,IAAI,CAAClB,SAAS,GAAG3J,IAAI,CAAC2J,SAAS,CAAC;MAAE0C,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;IACxD,IAAI,CAAC6P,aAAa,GAAG,IAAI3d,YAAY,CAAC,CAAC;IACvC,IAAI,CAAC4a,eAAe,GAAG,IAAI5a,YAAY,CAAC,CAAC;IACzC,IAAI,CAACye,WAAW,GAAG,IAAIze,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC0e,aAAa,GAAG,IAAI1e,YAAY,CAAC,CAAC;EAC3C;EACAga,WAAWA,CAAC2E,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY,IAChDD,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY,EAAE;MACxD,MAAMrG,KAAK,GAAGP,mBAAmB,CAACxF,QAAQ,CAAC,IAAI,CAACxJ,MAAM,CAAC;MACvD,IAAI,CAACuP,KAAK,GAAGP,mBAAmB,CAACM,YAAY,CAACC,KAAK,EAAE;QACjD3I,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB+B,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;IACN;IACA,IAAI4T,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY,IAChDD,OAAO,CAAC,MAAM,CAAC,IAAIA,OAAO,CAAC,MAAM,CAAC,CAACC,YAAY,EAAE;MACpD,MAAM5O,OAAO,GAAGgI,mBAAmB,CAACtF,UAAU,CAAC,IAAI,CAAC3K,UAAU,CAAC;MAC/D,IAAI,CAACiI,OAAO,GAAGgI,mBAAmB,CAACc,cAAc,CAAC9I,OAAO,EAAE,CAAC,IAAI,CAACpD,IAAI,EAAE;QACnEgD,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB+B,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;IACN;EACJ;EACA8T,cAAcA,CAAClS,IAAI,EAAE;IACjB,IAAI,CAACiO,eAAe,CAACnJ,IAAI,CAAC9E,IAAI,CAAC;EACnC;EACAd,YAAYA,CAACd,MAAM,EAAE;IACjB,IAAI,CAAC4S,aAAa,CAAClM,IAAI,CAAC1G,MAAM,CAAC;EACnC;EACA+T,UAAUA,CAAClS,IAAI,EAAE;IACb,IAAI,CAAC6R,WAAW,CAAChN,IAAI,CAAC7E,IAAI,CAAC;IAC3B,IAAI,IAAI,CAACuO,UAAU,EAAE;MACjB,IAAI,CAAC0D,cAAc,CAACvS,QAAQ,CAACtE,MAAM,CAAC;IACxC;EACJ;EACA+W,YAAYA,CAAC9O,MAAM,EAAE;IACjB,IAAI,CAACyO,aAAa,CAACjN,IAAI,CAACxB,MAAM,CAAC;EACnC;EACA+O,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC/Y,aAAa,GAAG,IAAI;EAC7B;EACAgZ,QAAQA,CAAA,EAAG;IACP,IAAI,CAAChZ,aAAa,GAAG,KAAK;EAC9B;AACJ;AACAuY,kCAAkC,CAAC5L,IAAI,YAAAsM,2CAAApM,CAAA;EAAA,YAAAA,CAAA,IAAyF0L,kCAAkC,EAlkBlDlf,EAAE,CAAA4W,iBAAA,CAkkBkE1C,WAAW;AAAA,CAA4C;AAC3OgL,kCAAkC,CAACxH,IAAI,kBAnkByE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAmkBEkL,kCAAkC;EAAAjI,SAAA;EAAAK,MAAA;IAAA1Q,gBAAA;IAAA0G,IAAA;IAAAqD,MAAA;IAAAjH,MAAA;IAAA+B,MAAA;IAAAgT,cAAA;IAAAjV,OAAA;IAAAC,OAAA;IAAAoS,UAAA;IAAApT,UAAA;IAAAoX,SAAA;EAAA;EAAAzC,OAAA;IAAAiB,aAAA;IAAA/C,eAAA;IAAA6D,WAAA;IAAAC,aAAA;EAAA;EAAAnE,QAAA,GAnkBtCjb,EAAE,CAAAkb,oBAAA;EAAApD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA6H,4CAAApd,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,YAmkBqjB,CAAC,YAAD,CAAC,YAAD,CAAC,6CAAD,CAAC;MAnkBxjB9C,EAAE,CAAAmE,UAAA,6BAAA4b,4GAAA1b,MAAA;QAAA,OAmkBogC1B,GAAA,CAAA4c,cAAA,CAAAlb,MAAqB,CAAC;MAAA,CAAC,CAAC,yBAAA2b,wGAAA3b,MAAA;QAAA,OAAoE1B,GAAA,CAAA6c,UAAA,CAAAnb,MAAiB,CAAC;MAAA,CAAvF,CAAC,qBAAA4b,oGAAA;QAAA,OAAwJtd,GAAA,CAAA+c,QAAA,CAAS,CAAC;MAAA,CAAnK,CAAC,uBAAAQ,sGAAA;QAAA,OAAsOvd,GAAA,CAAAgd,QAAA,CAAS,CAAC;MAAA,CAAjP,CAAC;MAnkB9hC3f,EAAE,CAAAgD,YAAA,CAmkBq0C,CAAC;MAnkBx0ChD,EAAE,CAAA8C,cAAA,UAmkBy1C,CAAC;MAnkB51C9C,EAAE,CAAAoG,MAAA,OAmkB01C,CAAC;MAnkB71CpG,EAAE,CAAAgD,YAAA,CAmkBi2C,CAAC;MAnkBp2ChD,EAAE,CAAA8C,cAAA,6CAmkBgoE,CAAC;MAnkBnoE9C,EAAE,CAAAmE,UAAA,6BAAAgc,4GAAA9b,MAAA;QAAA,OAmkBo3D1B,GAAA,CAAA4c,cAAA,CAAAlb,MAAqB,CAAC;MAAA,CAAC,CAAC,yBAAA+b,wGAAA/b,MAAA;QAAA,OAAoE1B,GAAA,CAAA8c,YAAA,CAAApb,MAAmB,CAAC;MAAA,CAAzF,CAAC,qBAAAgc,oGAAA;QAAA,OAA0J1d,GAAA,CAAA+c,QAAA,CAAS,CAAC;MAAA,CAArK,CAAC,uBAAAY,sGAAA;QAAA,OAAwO3d,GAAA,CAAAgd,QAAA,CAAS,CAAC;MAAA,CAAnP,CAAC;MAnkB94D3f,EAAE,CAAAgD,YAAA,CAmkBurE,CAAC,CAAD,CAAC;MAnkB1rEhD,EAAE,CAAA8C,cAAA,uCAmkB60F,CAAC;MAnkBh1F9C,EAAE,CAAAmE,UAAA,2BAAAoc,oGAAAlc,MAAA;QAAA,OAmkBuzF1B,GAAA,CAAA4J,YAAA,CAAAlI,MAAmB,CAAC;MAAA,CAAC,CAAC;MAnkB/0FrE,EAAE,CAAAgD,YAAA,CAmkB82F,CAAC,CAAD,CAAC;MAnkBj3FhD,EAAE,CAAA+C,UAAA,IAAA0D,iDAAA,gBAmkBk2G,CAAC;MAnkBr2GzG,EAAE,CAAAgD,YAAA,CAmkB02G,CAAC;IAAA;IAAA,IAAAN,EAAA;MAnkB72G1C,EAAE,CAAAuD,SAAA,EAmkB4tB,CAAC;MAnkB/tBvD,EAAE,CAAAqD,UAAA,aAAAV,GAAA,CAAAsW,KAmkB4tB,CAAC,SAAAtW,GAAA,CAAA2K,IAAD,CAAC,aAAA3K,GAAA,CAAAqC,QAAA,CAAA4C,IAAD,CAAC,aAAAjF,GAAA,CAAA8b,cAAA,KAAA9b,GAAA,CAAAqC,QAAA,CAAA4C,IAAD,CAAC,eAAAjF,GAAA,CAAAkZ,UAAD,CAAC;MAnkB/tB7b,EAAE,CAAAuD,SAAA,EAmkB26C,CAAC;MAnkB96CvD,EAAE,CAAAqD,UAAA,aAAAV,GAAA,CAAA+N,OAmkB26C,CAAC,SAAA/N,GAAA,CAAAgO,MAAD,CAAC,aAAAhO,GAAA,CAAAqC,QAAA,CAAA0D,MAAD,CAAC,aAAA/F,GAAA,CAAA8b,cAAA,KAAA9b,GAAA,CAAAqC,QAAA,CAAA0D,MAAD,CAAC,eAAA/F,GAAA,CAAAkZ,UAAD,CAAC,eAAAlZ,GAAA,CAAA8F,UAAD,CAAC,aAAA9F,GAAA,CAAAkd,SAAD,CAAC;MAnkB96C7f,EAAE,CAAAuD,SAAA,EAmkB43E,CAAC;MAnkB/3EvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA2B,GAAA,EAAAlE,GAAA,CAAA+G,MAAA,QAmkB43E,CAAC,mBAAA/G,GAAA,CAAA8I,MAAD,CAAC,mBAAA9I,GAAA,CAAA8b,cAAD,CAAC,YAAA9b,GAAA,CAAA8G,OAAD,CAAC,YAAA9G,GAAA,CAAA6G,OAAD,CAAC,WAAA7G,GAAA,CAAA+G,MAAD,CAAC,UAAA/G,GAAA,CAAAsW,KAAD,CAAC,YAAAtW,GAAA,CAAA+N,OAAD,CAAC,iBAAA/N,GAAA,CAAA2K,IAAD,CAAC,cAAA3K,GAAA,CAAAmJ,SAAD,CAAC;MAnkB/3E9L,EAAE,CAAAuD,SAAA,EAmkB26F,CAAC;MAnkB96FvD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAAkZ,UAAA,IAAAlZ,GAAA,CAAAiE,gBAmkB26F,CAAC;IAAA;EAAA;EAAAyR,YAAA,GAA87C8C,yCAAyC,EAAiO+C,oCAAoC,EAAiOld,EAAE,CAACwc,OAAO,EAAiExc,EAAE,CAACsX,IAAI,EAA0EtX,EAAE,CAACuX,gBAAgB;EAAAqF,MAAA;EAAA4C,eAAA;AAAA,EAAmJ;AAC7yK;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KApkBgH9T,EAAE,CAAA+T,iBAAA,CAokBtBmL,kCAAkC,EAAc,CAAC;IACjIlL,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,8BAA8B;MACxCkB,WAAW,EAAE,6CAA6C;MAC1DoF,SAAS,EAAE,CAAC,6CAA6C,CAAC;MAC1D2C,eAAe,EAAE3f,uBAAuB,CAAC4f;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzM,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QAC9DnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEtN,gBAAgB,EAAE,CAAC;MAC/CoN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkN,IAAI,EAAE,CAAC;MACP0G,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEuQ,MAAM,EAAE,CAAC;MACTqD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqL,MAAM,EAAE,CAAC;MACTuI,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqe,cAAc,EAAE,CAAC;MACjBzK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoJ,OAAO,EAAE,CAAC;MACVwK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVuK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyb,UAAU,EAAE,CAAC;MACb7H,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyf,SAAS,EAAE,CAAC;MACZ7L,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEie,aAAa,EAAE,CAAC;MAChBrK,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE0a,eAAe,EAAE,CAAC;MAClBtH,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEue,WAAW,EAAE,CAAC;MACdnL,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEwe,aAAa,EAAE,CAAC;MAChBpL,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8f,8BAA8B,CAAC;EACjC3O,WAAWA,CAACrI,MAAM,EAAE;IAChB,IAAI,CAACiX,UAAU,GAAG,IAAIjgB,YAAY,CAAC,CAAC;IACpC,IAAI,CAACkgB,YAAY,GAAG,IAAIlgB,YAAY,CAAC,CAAC;IACtC,IAAI,CAACmgB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGnI,mBAAmB,CAACxF,QAAQ,CAACxJ,MAAM,CAAC;EACzD;EACAoX,cAAcA,CAACrc,IAAI,EAAE;IACjB,IAAI,CAACmc,YAAY,CAACzO,IAAI,CAAC1N,IAAI,CAAC;EAChC;AACJ;AACAic,8BAA8B,CAACpN,IAAI,YAAAyN,uCAAAvN,CAAA;EA1nB6ExT,EAAE,CAAAghB,gBAAA;AAAA,CA0nBkG;AACpNN,8BAA8B,CAAC3J,IAAI,kBA3nB6E/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EA2nBF0M,8BAA8B;EAAApJ,MAAA;IAAA/N,YAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA0T,OAAA;IAAAuD,UAAA;IAAAC,YAAA;EAAA;AAAA,EAA0L;AACxU;EAAA,QAAA9M,SAAA,oBAAAA,SAAA,KA5nBgH9T,EAAE,CAAA+T,iBAAA,CA4nBtB2M,8BAA8B,EAAc,CAAC;IAC7H1M,IAAI,EAAE7T;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6T,IAAI,EAAE3N;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkD,YAAY,EAAE,CAAC;MAC5FyK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoJ,OAAO,EAAE,CAAC;MACVwK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVuK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEugB,UAAU,EAAE,CAAC;MACb3M,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEggB,YAAY,EAAE,CAAC;MACf5M,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqgB,cAAc,CAAC;EACjBvH,SAASA,CAACpM,IAAI,EAAE4D,WAAW,EAAExJ,mBAAmB,EAAE;IAC9C,IAAI4F,IAAI,IAAI,IAAI,IAAI5F,mBAAmB,EAAE;MACrC,OAAO,KAAK;IAChB;IACA,OAAO4F,IAAI,KAAK4D,WAAW;EAC/B;AACJ;AACA+P,cAAc,CAAC3N,IAAI,YAAA4N,uBAAA1N,CAAA;EAAA,YAAAA,CAAA,IAAyFyN,cAAc;AAAA,CAA8C;AACxKA,cAAc,CAAClH,KAAK,kBArpB4F/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EAqpBRiN,cAAc;EAAA/G,IAAA;AAAA,EAAuB;AAC/I;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KAtpBgH9T,EAAE,CAAA+T,iBAAA,CAspBtBkN,cAAc,EAAc,CAAC;IAC7GjN,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMkH,gBAAgB,CAAC;EACnBzH,SAASA,CAAC/I,MAAM,EAAEyQ,aAAa,EAAE/H,GAAG,EAAE3R,mBAAmB,EAAE;IACvD,IAAIiJ,MAAM,IAAI,IAAI,IAAIjJ,mBAAmB,EAAE;MACvC,OAAO,KAAK;IAChB;IACA,MAAM2Z,UAAU,GAAG,CAAC;IACpB,OAASD,aAAa,KAAKzQ,MAAM,IAAMA,MAAM,IAAI0I,GAAG,IAAIgI,UAAU,CAAC,KAAK,CAAE;EAC9E;AACJ;AACAF,gBAAgB,CAAC7N,IAAI,YAAAgO,yBAAA9N,CAAA;EAAA,YAAAA,CAAA,IAAyF2N,gBAAgB;AAAA,CAA8C;AAC5KA,gBAAgB,CAACpH,KAAK,kBAvqB0F/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EAuqBNmN,gBAAgB;EAAAjH,IAAA;AAAA,EAAyB;AACrJ;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KAxqBgH9T,EAAE,CAAA+T,iBAAA,CAwqBtBoN,gBAAgB,EAAc,CAAC;IAC/GnN,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsH,oBAAoB,CAAC;EACvB7H,SAASA,CAAC/I,MAAM,EAAE0I,GAAG,GAAG,CAAC,EAAE;IACvB,IAAI,CAAC1I,MAAM,EAAE;MACT,OAAOA,MAAM;IACjB;IACA,OAAOA,MAAM,GAAG0I,GAAG,KAAK,CAAC,GAAG1I,MAAM,GAAG,EAAE;EAC3C;AACJ;AACA4Q,oBAAoB,CAACjO,IAAI,YAAAkO,6BAAAhO,CAAA;EAAA,YAAAA,CAAA,IAAyF+N,oBAAoB;AAAA,CAA8C;AACpLA,oBAAoB,CAACxH,KAAK,kBAxrBsF/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EAwrBFuN,oBAAoB;EAAArH,IAAA;AAAA,EAA6B;AACjK;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KAzrBgH9T,EAAE,CAAA+T,iBAAA,CAyrBtBwN,oBAAoB,EAAc,CAAC;IACnHvN,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMwH,iBAAiB,GAAG;EACtBC,KAAK,EAAE;IACHpM,MAAM,EAAE,MAAM;IACdG,GAAG,EAAE;EACT,CAAC;EACDkM,KAAK,EAAE;IACHrM,MAAM,EAAE,OAAO;IACfG,GAAG,EAAE;EACT;AACJ,CAAC;AACD,MAAMmM,kCAAkC,CAAC;EACrC7P,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/M,QAAQ,GAAGgI,QAAQ;IACxB,IAAI,CAAChF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC6Z,UAAU,GAAG,IAAInhB,YAAY,CAAC,CAAC;IACpC,IAAI,CAACohB,YAAY,GAAG,IAAIphB,YAAY,CAAC,CAAC;EAC1C;EACAsV,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC+L,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAtH,WAAWA,CAAC2E,OAAO,EAAE;IACjB,MAAM4C,eAAe,GAAG5C,OAAO,CAAC,UAAU,CAAC;IAC3C,MAAM6C,mBAAmB,GAAG7C,OAAO,CAAC,cAAc,CAAC;IACnD,IAAK4C,eAAe,IAAIA,eAAe,CAAC3C,YAAY,IAC5C4C,mBAAmB,IAAIA,mBAAmB,CAAC5C,YAAa,EAAE;MAC9D;MACA,IAAI,CAAC7X,YAAY,GAAG,IAAI,CAACS,QAAQ,CAACyT,IAAI,CAAClX,IAAI,IAAIA,IAAI,CAACA,IAAI,KAAK,IAAI,CAACgD,YAAY,CAAChD,IAAI,CAAC;IACxF;IACA,IAAIyd,mBAAmB,IAAIA,mBAAmB,CAAC5C,YAAY,EAAE;MACzD,IAAI,CAACyC,oBAAoB,CAAC,CAAC;IAC/B;IACA,IAAIE,eAAe,IAAIA,eAAe,CAAC3C,YAAY,EAAE;MACjD;MACA1E,UAAU,CAAC,MAAM,IAAI,CAACuH,mBAAmB,CAAC,CAAC,CAAC;IAChD;EACJ;EACAha,WAAWA,CAACia,CAAC,EAAE3d,IAAI,EAAE;IACjB,OAAOA,IAAI,CAACA,IAAI;EACpB;EACA4d,WAAWA,CAACvU,CAAC,EAAE;IACXA,CAAC,CAACuO,cAAc,CAAC,CAAC;IAClB,IAAI,CAACiG,SAAS,GAAG,IAAI;EACzB;EACAC,UAAUA,CAACzU,CAAC,EAAE;IACV,IAAI,CAAC,IAAI,CAACwU,SAAS,IAAKxU,CAAC,YAAY0U,UAAU,IAAI1U,CAAC,CAACkG,IAAI,KAAK,OAAQ,EAAE;MACpE;IACJ;IACA,MAAMyO,cAAc,GAAG,IAAI,CAACC,SAAS,CAACxN,aAAa,CAACE,qBAAqB,CAAC,CAAC;IAC3E;IACA,MAAMuN,OAAO,GAAGF,cAAc,CAAClM,IAAI,GAAGkM,cAAc,CAACG,KAAK,GAAG,CAAC;IAC9D,MAAMC,OAAO,GAAGJ,cAAc,CAAChN,GAAG,GAAGgN,cAAc,CAACnN,MAAM,GAAG,CAAC;IAC9D;IACA,MAAMwN,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACnV,CAAC,CAACoV,OAAO,GAAGP,OAAO,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACnV,CAAC,CAACqV,OAAO,GAAGN,OAAO,CAAC,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACK,EAAE;IAC3G;IACA,MAAMC,WAAW,GAAGC,iBAAiB,CAACX,OAAO,EAAEE,OAAO,EAAE/U,CAAC,CAACoV,OAAO,EAAEpV,CAAC,CAACqV,OAAO,EAAEL,UAAU,CAAC;IACzF;IACA,MAAMS,kBAAkB,GAAG,IAAI,CAAC7Z,MAAM,IAAI,IAAI,CAAC8Z,gBAAgB,CAACb,OAAO,EAAEE,OAAO,EAAE/U,CAAC,CAACoV,OAAO,EAAEpV,CAAC,CAACqV,OAAO,CAAC;IACvG;IACA,MAAMpK,SAAS,GAAG,IAAI,CAAC1L,IAAI,KAAKL,QAAQ,CAACtE,MAAM,GAAI,CAAC,IAAI,IAAI,CAACD,UAAU,IAAI,CAAC,CAAC,GAAI,EAAE;IACnF,MAAMgb,YAAY,GAAGC,UAAU,CAACL,WAAW,EAAEtK,SAAS,CAAC;IACvD,MAAMzR,KAAK,GAAG,CAACmc,YAAY,IAAI,GAAG,KAAKF,kBAAkB,GAAG,GAAG,GAAG,CAAC,CAAC;IACpE,MAAM9b,YAAY,GAAG,IAAI,CAACS,QAAQ,CAACyT,IAAI,CAACgI,GAAG,IAAIA,GAAG,CAACrc,KAAK,KAAKA,KAAK,CAAC;IACnE,IAAIG,YAAY,IAAI,CAACA,YAAY,CAACpC,QAAQ,EAAE;MACxC,IAAI,CAACwc,UAAU,CAAC1P,IAAI,CAAC1K,YAAY,CAAC;MAClC;MACA,IAAI,CAAC,IAAI,CAAC6a,SAAS,EAAE;QACjB,IAAI,CAACR,YAAY,CAAC3P,IAAI,CAAC1K,YAAY,CAAChD,IAAI,CAAC;MAC7C;IACJ;EACJ;EACAmf,SAASA,CAAC9V,CAAC,EAAE;IACTA,CAAC,CAACuO,cAAc,CAAC,CAAC;IAClB,IAAI,CAACiG,SAAS,GAAG,KAAK;EAC1B;EACAvH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8I,iBAAiB,CAAC,CAAC;EAC5B;EACA7B,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC8B,iBAAiB,GAAG,IAAI,CAACzB,WAAW,CAAC0B,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,SAAS,CAACG,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACrB,SAAS,CAACxN,aAAa,CAAC+O,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACH,iBAAiB,CAAC;IACnF,IAAI,CAACpB,SAAS,CAACxN,aAAa,CAAC+O,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACD,eAAe,CAAC;EACnF;EACAH,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnB,SAAS,CAACxN,aAAa,CAACgP,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACJ,iBAAiB,CAAC;IACtF,IAAI,CAACpB,SAAS,CAACxN,aAAa,CAACgP,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACF,eAAe,CAAC;EACtF;EACAjC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACrY,MAAM,KAAK,EAAE,EAAE;MACpB,IAAI,IAAI,CAACjC,YAAY,CAAChD,IAAI,GAAG,EAAE,IAAI,IAAI,CAACgD,YAAY,CAAChD,IAAI,KAAK,CAAC,EAAE;QAC7D,IAAI,CAAC0f,iBAAiB,CAAC,CAAC;MAC5B,CAAC,MACI;QACD,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ;IACA,IAAI,CAACC,SAAS,CAACnP,aAAa,CAACnT,KAAK,CAAC2X,SAAS,GAAI,UAAS,IAAI,CAACjS,YAAY,CAACH,KAAM,MAAK;EAC1F;EACA6a,mBAAmBA,CAAA,EAAG;IAClB,MAAMhJ,WAAW,GAAG,IAAI,CAACjR,QAAQ,CAACyT,IAAI,CAAClX,IAAI,IAAI,IAAI,CAACgD,YAAY,CAAChD,IAAI,KAAKA,IAAI,CAACA,IAAI,CAAC;IACpF,IAAI,CAACiD,mBAAmB,GAAG,IAAI,CAACQ,QAAQ,CAACsW,KAAK,CAAC/Z,IAAI,IAAIA,IAAI,CAACY,QAAQ,CAAC;IACrE,IAAK8T,WAAW,IAAIA,WAAW,CAAC9T,QAAQ,IAAK,CAAC,IAAI,CAACqC,mBAAmB,EAAE;MACpE,MAAM4c,aAAa,GAAG,IAAI,CAACpc,QAAQ,CAACyT,IAAI,CAAClX,IAAI,IAAI,CAACA,IAAI,CAACY,QAAQ,CAAC;MAChE,IAAI,CAACwc,UAAU,CAAC1P,IAAI,CAACmS,aAAa,CAAC;IACvC;EACJ;EACAd,gBAAgBA,CAACe,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAErO,CAAC,EAAE;IAC3B;IACA,OAAO2M,IAAI,CAAC2B,IAAI,CAAC3B,IAAI,CAAC4B,GAAG,CAACF,CAAC,GAAGF,EAAE,EAAE,CAAC,CAAC,GAAGxB,IAAI,CAAC4B,GAAG,CAACvO,CAAC,GAAGoO,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxc,kBAAkB;EACzF;EACAmc,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,SAAS,CAACnP,aAAa,CAACnT,KAAK,CAACuT,MAAM,GAAGmM,iBAAiB,CAACC,KAAK,CAACpM,MAAM;IAC1E,IAAI,CAAC+O,SAAS,CAACnP,aAAa,CAACnT,KAAK,CAAC0T,GAAG,GAAGgM,iBAAiB,CAACC,KAAK,CAACjM,GAAG;EACxE;EACA2O,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAACnP,aAAa,CAACnT,KAAK,CAACuT,MAAM,GAAGmM,iBAAiB,CAACE,KAAK,CAACrM,MAAM;IAC1E,IAAI,CAAC+O,SAAS,CAACnP,aAAa,CAACnT,KAAK,CAAC0T,GAAG,GAAGgM,iBAAiB,CAACE,KAAK,CAAClM,GAAG;EACxE;AACJ;AACAmM,kCAAkC,CAACtO,IAAI,YAAAsR,2CAAApR,CAAA;EAAA,YAAAA,CAAA,IAAyFoO,kCAAkC;AAAA,CAAmD;AACrNA,kCAAkC,CAAClK,IAAI,kBAzzByE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAyzBE4N,kCAAkC;EAAA3K,SAAA;EAAA4F,SAAA,WAAAgI,yCAAAniB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzzBtC1C,EAAE,CAAA+c,WAAA,CAAAjW,GAAA;MAAF9G,EAAE,CAAA+c,WAAA,CAAAhW,GAAA;IAAA;IAAA,IAAArE,EAAA;MAAA,IAAAsa,EAAA;MAAFhd,EAAE,CAAAid,cAAA,CAAAD,EAAA,GAAFhd,EAAE,CAAAkd,WAAA,QAAAva,GAAA,CAAA+f,SAAA,GAAA1F,EAAA,CAAAG,KAAA;MAAFnd,EAAE,CAAAid,cAAA,CAAAD,EAAA,GAAFhd,EAAE,CAAAkd,WAAA,QAAAva,GAAA,CAAA0hB,SAAA,GAAArH,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAjG,YAAA,WAAA4N,gDAAApiB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAAmE,UAAA,uBAAA4gB,gEAAA1gB,MAAA;QAAA,OAyzBE1B,GAAA,CAAA0f,WAAA,CAAAhe,MAAkB,CAAC;MAAA,qBAAA2gB,4DAAA3gB,MAAA;QAAA,OAAnB1B,GAAA,CAAA4f,UAAA,CAAAle,MAAiB,CAAC;MAAA,yBAAA4gB,gEAAA5gB,MAAA;QAAA,OAAlB1B,GAAA,CAAA4f,UAAA,CAAAle,MAAA,CAAA6gB,cAAA,CAAiC,CAAC,CAAC,CAAC;MAAA,wBAAAC,+DAAA9gB,MAAA;QAAA,OAApC1B,GAAA,CAAA4f,UAAA,CAAAle,MAAA,CAAA6gB,cAAA,CAAiC,CAAC,CAAC,CAAC;MAAA,yBAAAE,gEAAA/gB,MAAA;QAAA,OAApC1B,GAAA,CAAA4f,UAAA,CAAAle,MAAiB,CAAC;MAAA,uBAAAghB,8DAAAhhB,MAAA;QAAA,OAAlB1B,GAAA,CAAAihB,SAAA,CAAAvf,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAAiT,MAAA;IAAApP,QAAA;IAAAT,YAAA;IAAA4F,IAAA;IAAA3D,MAAA;IAAAjB,UAAA;EAAA;EAAA2U,OAAA;IAAAyE,UAAA;IAAAC,YAAA;EAAA;EAAA7G,QAAA,GAzzBrBjb,EAAE,CAAAkb,oBAAA;EAAApD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAqN,4CAAA5iB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,eAyzBs0B,CAAC;MAzzBz0B9C,EAAE,CAAA+C,UAAA,IAAAqF,iDAAA,gBAyzBixE,CAAC;MAzzBpxEpI,EAAE,CAAAiL,SAAA,gBAyzBw8E,CAAC;MAzzB38EjL,EAAE,CAAAgD,YAAA,CAyzBg9E,CAAC;MAzzBn9EhD,EAAE,CAAA+C,UAAA,IAAA4F,yDAAA,gCAAF3I,EAAE,CAAA0G,sBAyzB+lG,CAAC;IAAA;IAAA,IAAAhE,EAAA;MAAA,MAAAS,GAAA,GAzzBlmGnD,EAAE,CAAAoD,WAAA;MAAFpD,EAAE,CAAAuD,SAAA,EAyzBk3B,CAAC;MAzzBr3BvD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAA0K,IAAA,KAAA1K,GAAA,CAAAqC,QAAA,CAAA0D,MAyzBk3B,CAAC,aAAAvF,GAAD,CAAC;MAzzBr3BnD,EAAE,CAAAuD,SAAA,EAyzBy4E,CAAC;MAzzB54EvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAA2D,GAAA,EAAAlG,GAAA,CAAA0K,IAAA,KAAA1K,GAAA,CAAAqC,QAAA,CAAA0D,MAAA,CAyzBy4E,CAAC,WAAA/F,GAAA,CAAA+E,mBAAD,CAAC;IAAA;EAAA;EAAA2Q,YAAA,GAA45GrX,EAAE,CAACsX,IAAI,EAA0EtX,EAAE,CAACukB,OAAO,EAAgGvkB,EAAE,CAACwkB,OAAO,EAAwDxkB,EAAE,CAACwc,OAAO,EAA4Exc,EAAE,CAACykB,SAAS,EAAgBxE,cAAc,EAAmBxH,iBAAiB,EAAkB0H,gBAAgB,EAAsBI,oBAAoB;EAAA3D,MAAA;EAAA4C,eAAA;AAAA,EAAyD;AAC97M;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KA1zBgH9T,EAAE,CAAA+T,iBAAA,CA0zBtB6N,kCAAkC,EAAc,CAAC;IACjI5N,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,8BAA8B;MACxCkB,WAAW,EAAE,+CAA+C;MAC5DoF,SAAS,EAAE,CAAC,+CAA+C,CAAC;MAC5D2C,eAAe,EAAE3f,uBAAuB,CAAC4f;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEvY,QAAQ,EAAE,CAAC;MACzB8L,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqH,YAAY,EAAE,CAAC;MACfuM,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiN,IAAI,EAAE,CAAC;MACP2G,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyhB,UAAU,EAAE,CAAC;MACb7N,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEkhB,YAAY,EAAE,CAAC;MACf9N,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE8hB,SAAS,EAAE,CAAC;MACZ1O,IAAI,EAAErT,SAAS;MACfsT,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEyR,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAErB,SAAS,EAAE,CAAC;MACZrQ,IAAI,EAAErT,SAAS;MACfsT,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEyR,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAErD,WAAW,EAAE,CAAC;MACdrO,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAEsO,UAAU,EAAE,CAAC;MACbvO,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC,EAAE;MACCD,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,0BAA0B,CAAC;IACpD,CAAC,EAAE;MACCD,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC,0BAA0B,CAAC;IACnD,CAAC,EAAE;MACCD,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAE2P,SAAS,EAAE,CAAC;MACZ5P,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAChB,SAASyP,UAAUA,CAACpc,KAAK,EAAEqe,IAAI,EAAE;EAC7B,OAAO5C,IAAI,CAAC6C,KAAK,CAACte,KAAK,GAAGqe,IAAI,CAAC,GAAGA,IAAI;AAC1C;AACA,SAASrC,iBAAiBA,CAACiB,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAErO,CAAC,EAAEyP,YAAY,EAAE;EACnD,IAAIzP,CAAC,GAAGoO,EAAE,IAAIC,CAAC,IAAIF,EAAE,EAAE;IAAE;IACrB,OAAO,GAAG,GAAGsB,YAAY;EAC7B,CAAC,MACI,IAAIzP,CAAC,GAAGoO,EAAE,IAAIC,CAAC,GAAGF,EAAE,EAAE;IAAE;IACzB,OAAO,GAAG,GAAGsB,YAAY;EAC7B,CAAC,MACI,IAAIzP,CAAC,GAAGoO,EAAE,IAAIC,CAAC,GAAGF,EAAE,EAAE;IAAE;IACzB,OAAO,GAAG,GAAGsB,YAAY;EAC7B,CAAC,MACI;IAAE;IACH,OAAOA,YAAY;EACvB;AACJ;AAEA,MAAMC,yCAAyC,SAASpF,8BAA8B,CAAC;EACnF3O,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,EAAE,CAAC;EACb;EACAgU,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAClF,SAAS,GAAGnI,mBAAmB,CAACM,YAAY,CAAC,IAAI,CAAC6H,SAAS,EAAE;MAC9DvQ,GAAG,EAAE,IAAI,CAAC9G,OAAO;MACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;AACJ;AACAoc,yCAAyC,CAACxS,IAAI,YAAA0S,kDAAAxS,CAAA;EAAA,YAAAA,CAAA,IAAyFsS,yCAAyC;AAAA,CAAmD;AACnOA,yCAAyC,CAACpO,IAAI,kBAx4BkE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAw4BS8R,yCAAyC;EAAA7O,SAAA;EAAAgE,QAAA,GAx4BpDjb,EAAE,CAAAimB,0BAAA;EAAAnO,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAiO,mDAAAxjB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,qCAw4BkZ,CAAC;MAx4BrZ9C,EAAE,CAAAmE,UAAA,wBAAAgiB,sGAAA9hB,MAAA;QAAA,OAw4BgT1B,GAAA,CAAAge,UAAA,CAAAxO,IAAA,CAAA9N,MAAsB,CAAC;MAAA,CAAC,CAAC,0BAAA+hB,wGAAA/hB,MAAA;QAAA,OAAiD1B,GAAA,CAAAme,cAAA,CAAAzc,MAAqB,CAAC;MAAA,CAAxE,CAAC;MAx4B3UrE,EAAE,CAAAgD,YAAA,CAw4Bib,CAAC;IAAA;IAAA,IAAAN,EAAA;MAx4Bpb1C,EAAE,CAAAqD,UAAA,iBAAAV,GAAA,CAAA4G,YAw4BmN,CAAC,aAAA5G,GAAA,CAAAke,SAAD,CAAC,WAAAle,GAAA,CAAA+G,MAAD,CAAC;IAAA;EAAA;EAAA2O,YAAA,GAAwPuJ,kCAAkC;EAAApJ,aAAA;EAAAgI,eAAA;AAAA,EAAmN;AACnzB;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KAz4BgH9T,EAAE,CAAA+T,iBAAA,CAy4BtB+R,yCAAyC,EAAc,CAAC;IACxI9R,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,uCAAuC;MACjDkB,WAAW,EAAE,sDAAsD;MACnE+H,eAAe,EAAE3f,uBAAuB,CAAC4f;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAM4F,yCAAyC,SAAS3F,8BAA8B,CAAC;EACnF3O,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,EAAE,CAAC;EACb;EACA2I,WAAWA,CAAC2E,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY,EAAE;MACrD,IAAI,CAACuB,SAAS,GAAGnI,mBAAmB,CAACM,YAAY,CAAC,IAAI,CAAC6H,SAAS,EAAE;QAC9DvQ,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB+B,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;IACN;EACJ;AACJ;AACA4a,yCAAyC,CAAC/S,IAAI,YAAAgT,kDAAA9S,CAAA;EAAA,YAAAA,CAAA,IAAyF6S,yCAAyC;AAAA,CAAmD;AACnOA,yCAAyC,CAAC3O,IAAI,kBAl6BkE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAk6BSqS,yCAAyC;EAAApP,SAAA;EAAAK,MAAA;IAAA7L,MAAA;EAAA;EAAAwP,QAAA,GAl6BpDjb,EAAE,CAAAimB,0BAAA,EAAFjmB,EAAE,CAAAkb,oBAAA;EAAApD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAsO,mDAAA7jB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,qCAk6BkZ,CAAC;MAl6BrZ9C,EAAE,CAAAmE,UAAA,wBAAAqiB,sGAAAniB,MAAA;QAAA,OAk6B+U1B,GAAA,CAAAge,UAAA,CAAAxO,IAAA,CAAA9N,MAAsB,CAAC;MAAA,CAAC,CAAC,0BAAAoiB,wGAAApiB,MAAA;QAAA,OAAkB1B,GAAA,CAAAme,cAAA,CAAAzc,MAAqB,CAAC;MAAA,CAAzC,CAAC;MAl6B1WrE,EAAE,CAAAgD,YAAA,CAk6Bib,CAAC;IAAA;IAAA,IAAAN,EAAA;MAl6Bpb1C,EAAE,CAAAqD,UAAA,iBAAAV,GAAA,CAAA4G,YAk6BsQ,CAAC,aAAA5G,GAAA,CAAAke,SAAD,CAAC;IAAA;EAAA;EAAAxI,YAAA,GAAqMuJ,kCAAkC;EAAApJ,aAAA;EAAAgI,eAAA;AAAA,EAAmN;AACnzB;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KAn6BgH9T,EAAE,CAAA+T,iBAAA,CAm6BtBsS,yCAAyC,EAAc,CAAC;IACxIrS,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,uCAAuC;MACjDkB,WAAW,EAAE,sDAAsD;MACnE+H,eAAe,EAAE3f,uBAAuB,CAAC4f;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEhV,MAAM,EAAE,CAAC;MACnEuI,IAAI,EAAE5T;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsmB,yCAAyC,CAAC;EAC5C3U,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4U,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC3hB,QAAQ,GAAGgI,QAAQ;IACxB,IAAI,CAAC4Z,YAAY,GAAG,IAAIlmB,YAAY,CAAC,CAAC;EAC1C;EACAga,WAAWA,CAAC2E,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY,EAAE;MACrD,MAAM5O,OAAO,GAAGgI,mBAAmB,CAACtF,UAAU,CAAC,IAAI,CAAC3K,UAAU,CAAC;MAC/D,IAAI,CAACke,WAAW,GAAGjO,mBAAmB,CAACc,cAAc,CAAC9I,OAAO,EAAE,IAAI,CAACnH,YAAY,EAAE;QAC9E+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjB+G,GAAG,EAAE,IAAI,CAAC9G,OAAO;QACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB+B,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;IACN;EACJ;AACJ;AACAib,yCAAyC,CAACpT,IAAI,YAAAuT,kDAAArT,CAAA;EAAA,YAAAA,CAAA,IAAyFkT,yCAAyC;AAAA,CAAmD;AACnOA,yCAAyC,CAAChP,IAAI,kBAj8BkE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAi8BS0S,yCAAyC;EAAAzP,SAAA;EAAAK,MAAA;IAAA7M,cAAA;IAAAlB,YAAA;IAAAkC,MAAA;IAAAjC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAjB,UAAA;EAAA;EAAA2U,OAAA;IAAAwJ,YAAA;EAAA;EAAA3L,QAAA,GAj8BpDjb,EAAE,CAAAkb,oBAAA;EAAApD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA6O,mDAAApkB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,qCAi8B2mB,CAAC;MAj8B9mB9C,EAAE,CAAAmE,UAAA,wBAAA4iB,sGAAA1iB,MAAA;QAAA,OAi8BqjB1B,GAAA,CAAAikB,YAAA,CAAAzU,IAAA,CAAA9N,MAAwB,CAAC;MAAA,CAAC,CAAC;MAj8BllBrE,EAAE,CAAAgD,YAAA,CAi8B0oB,CAAC;IAAA;IAAA,IAAAN,EAAA;MAj8B7oB1C,EAAE,CAAAqD,UAAA,aAAAV,GAAA,CAAAgkB,WAi8Bwa,CAAC,iBAAAhkB,GAAA,CAAA8H,cAAD,CAAC,eAAA9H,GAAA,CAAA8F,UAAD,CAAC,SAAA9F,GAAA,CAAAqC,QAAA,CAAA0D,MAAD,CAAC;IAAA;EAAA;EAAA2P,YAAA,GAA4PuJ,kCAAkC;EAAApJ,aAAA;AAAA,EAA+J;AACx9B;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAl8BgH9T,EAAE,CAAA+T,iBAAA,CAk8BtB2S,yCAAyC,EAAc,CAAC;IACxI1S,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,sCAAsC;MAChDkB,WAAW,EAAE;IACjB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEhO,cAAc,EAAE,CAAC;MAC/BuJ,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmJ,YAAY,EAAE,CAAC;MACfyK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqL,MAAM,EAAE,CAAC;MACTuI,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoJ,OAAO,EAAE,CAAC;MACVwK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVuK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwmB,YAAY,EAAE,CAAC;MACf5S,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMomB,oCAAoC,CAAC;AAE3CA,oCAAoC,CAAC1T,IAAI,YAAA2T,6CAAAzT,CAAA;EAAA,YAAAA,CAAA,IAAyFwT,oCAAoC;AAAA,CAAmD;AACzNA,oCAAoC,CAACtP,IAAI,kBA79BuE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EA69BIgT,oCAAoC;EAAA/P,SAAA;EAAAY,kBAAA,EAAAjU,GAAA;EAAAkU,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAiP,8CAAAxkB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA79B1C1C,EAAE,CAAAmY,eAAA;MAAFnY,EAAE,CAAA8C,cAAA,eA69BkK,CAAC,UAAD,CAAC;MA79BrK9C,EAAE,CAAA2D,YAAA,EA69BqM,CAAC;MA79BxM3D,EAAE,CAAAgD,YAAA,CA69B4M,CAAC,CAAD,CAAC;IAAA;EAAA;EAAA4a,MAAA;AAAA,EAAqyB;AACpmC;EAAA,QAAA9J,SAAA,oBAAAA,SAAA,KA99BgH9T,EAAE,CAAA+T,iBAAA,CA89BtBiT,oCAAoC,EAAc,CAAC;IACnIhT,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,gCAAgC;MAC1CkB,WAAW,EAAE,iDAAiD;MAC9DoF,SAAS,EAAE,CAAC,iDAAiD;IACjE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsJ,gBAAgB,CAAC;EACnBpV,WAAWA,CAACqV,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAC,OAAOA,CAACvZ,CAAC,EAAE;IACP,IAAI,CAAC,IAAI,CAACwZ,YAAY,EAAE;MACpB,IAAI,CAACF,YAAY,CAACzS,aAAa,CAAC7G,CAAC,CAAC;IACtC;IACAA,CAAC,CAACuO,cAAc,CAAC,CAAC;EACtB;AACJ;AACA8K,gBAAgB,CAAC7T,IAAI,YAAAiU,yBAAA/T,CAAA;EAAA,YAAAA,CAAA,IAAyF2T,gBAAgB,EAl/BdnnB,EAAE,CAAA4W,iBAAA,CAk/B8BxC,iCAAiC;AAAA,CAA4C;AAC7N+S,gBAAgB,CAACpQ,IAAI,kBAn/B2F/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EAm/BhBmT,gBAAgB;EAAAlQ,SAAA;EAAAC,YAAA,WAAAsQ,8BAAA9kB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAn/BF1C,EAAE,CAAAmE,UAAA,mBAAAsjB,0CAAApjB,MAAA;QAAA,OAm/BhB1B,GAAA,CAAA0kB,OAAA,CAAAhjB,MAAc,CAAC;MAAA;IAAA;EAAA;EAAAiT,MAAA;IAAAgQ,YAAA;EAAA;AAAA,EAAqJ;AACtQ;EAAA,QAAAxT,SAAA,oBAAAA,SAAA,KAp/BgH9T,EAAE,CAAA+T,iBAAA,CAo/BtBoT,gBAAgB,EAAc,CAAC;IAC/GnT,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAEI;IAAkC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkT,YAAY,EAAE,CAAC;MACpHtT,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEoT,OAAO,EAAE,CAAC;MACVrT,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyT,mCAAmC,CAAC;EACtC3V,WAAWA,CAACgD,UAAU,EAAE;IACpB,IAAI,CAACE,OAAO,GAAGF,UAAU,CAACG,aAAa;EAC3C;EACAc,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC2R,KAAK,EAAE;MACZ,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACD,KAAK,CAAC;IAC7B;EACJ;EACAC,QAAQA,CAACD,KAAK,EAAE;IACZ,KAAK,MAAMhE,GAAG,IAAIgE,KAAK,EAAE;MACrB,IAAIA,KAAK,CAACE,cAAc,CAAClE,GAAG,CAAC,EAAE;QAC3B,IAAI,OAAOgE,KAAK,CAAChE,GAAG,CAAC,KAAK,QAAQ,EAAE;UAChC,KAAK,MAAMmE,IAAI,IAAIH,KAAK,EAAE;YACtB,IAAIA,KAAK,CAACE,cAAc,CAACC,IAAI,CAAC,EAAE;cAC5B,IAAI,CAAC7S,OAAO,CAAClT,KAAK,CAACgmB,WAAW,CAAE,KAAIC,eAAe,CAACF,IAAI,CAAE,EAAC,EAAEH,KAAK,CAACG,IAAI,CAAC,CAAC;YAC7E;UACJ;UACA;QACJ;QACA,IAAI,CAACF,QAAQ,CAACD,KAAK,CAAChE,GAAG,CAAC,CAAC;MAC7B;IACJ;EACJ;AACJ;AACA+D,mCAAmC,CAACpU,IAAI,YAAA2U,4CAAAzU,CAAA;EAAA,YAAAA,CAAA,IAAyFkU,mCAAmC,EA1hCpD1nB,EAAE,CAAA4W,iBAAA,CA0hCoE5W,EAAE,CAAC6W,UAAU;AAAA,CAA4C;AAC/O6Q,mCAAmC,CAAC3Q,IAAI,kBA3hCwE/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EA2hCG0T,mCAAmC;EAAAzQ,SAAA;EAAAK,MAAA;IAAAqQ,KAAA;EAAA;AAAA,EAAuH;AAC/Q;EAAA,QAAA7T,SAAA,oBAAAA,SAAA,KA5hCgH9T,EAAE,CAAA+T,iBAAA,CA4hCtB2T,mCAAmC,EAAc,CAAC;IAClI1T,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MAAEsD,QAAQ,EAAE;IAA+B,CAAC;EACvD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAEhU,EAAE,CAAC6W;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8Q,KAAK,EAAE,CAAC;MACzF3T,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC;EAAE,CAAC;AAAA;AAChB,SAAS+T,eAAeA,CAACE,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACvY,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACwY,WAAW,CAAC,CAAC;AAClE;AAEA,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjCA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;AACrC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAMC,uCAAuC,CAAC;EAC1CtW,WAAWA,CAACuW,iBAAiB,EAAElB,YAAY,EAAE5Y,MAAM,EAAE;IACjD,IAAI,CAAC8Z,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAClB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC5Y,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxJ,QAAQ,GAAGgI,QAAQ;IACxB,IAAI,CAACyR,cAAc,GAAGzR,QAAQ,CAACpF,IAAI;IACnC,IAAI,CAAC2gB,WAAW,GAAG,IAAInnB,OAAO,CAAC,CAAC;EACpC;EACA,IAAIyR,WAAWA,CAACpO,IAAI,EAAE;IAClB,IAAI,CAAC+jB,YAAY,GAAG/jB,IAAI;IACxB,IAAI,CAAC8N,cAAc,CAAC9N,IAAI,CAAC;EAC7B;EACA,IAAIoO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC2V,YAAY;EAC5B;EACA5iB,SAASA,CAACkI,CAAC,EAAE;IACT,IAAI,CAACsZ,YAAY,CAACzS,aAAa,CAAC7G,CAAC,CAAC;IAClCA,CAAC,CAAC2a,eAAe,CAAC,CAAC;EACvB;EACA7M,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC8M,cAAc,GAAG,CAAC,IAAI,CAACC,gBAAgB,IAAIP,cAAc,CAACQ,KAAK;IACpE,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACtf,YAAY,GAAG,IAAI,CAAC+e,iBAAiB,CAAC/e,YAAY,CAClDgL,IAAI,CAACjT,WAAW,CAAC;MAAEkT,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAAChK,cAAc,GAAG,IAAI,CAAC6d,iBAAiB,CAAC7d,cAAc,CACtD8J,IAAI,CAACjT,WAAW,CAAC;MAAEkT,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAACxK,cAAc,GAAG,IAAI,CAACqe,iBAAiB,CAACre,cAAc,CACtDsK,IAAI,CAACjT,WAAW,CAAC;MAAEkT,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAACqU,iBAAiB,CAACC,WAAW,CAACxU,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAAC4mB,WAAW,CAAC,CAAC,CAC/DnM,SAAS,CAAC,IAAI,CAAC7J,cAAc,CAACwR,IAAI,CAAC,IAAI,CAAC,CAAC;EAClD;EACA7a,YAAYA,CAACoE,IAAI,EAAE;IACf,IAAI,CAACgb,iBAAiB,CAAChb,IAAI,GAAGA,IAAI;IAClC,IAAI,CAAC0b,YAAY,CAAC,CAAC;EACvB;EACA3f,cAAcA,CAACiE,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACuS,SAAS,EAAE;MACjB,IAAI,CAACN,cAAc,CAACvS,QAAQ,CAACtE,MAAM,CAAC;IACxC;IACA,IAAI,CAACogB,iBAAiB,CAAClI,YAAY,CAACzO,IAAI,CAAC7E,IAAI,CAAC;EAClD;EACA/C,cAAcA,CAACoG,MAAM,EAAE;IACnB,IAAI,CAAC2X,iBAAiB,CAAC3X,MAAM,GAAGA,MAAM;IACtC,IAAI,CAACqY,YAAY,CAAC,CAAC;EACvB;EACAzc,YAAYA,CAACd,MAAM,EAAE;IACjB,IAAI,CAAC6c,iBAAiB,CAAC7c,MAAM,GAAGA,MAAM;IACtC,IAAI,CAACud,YAAY,CAAC,CAAC;EACvB;EACAzJ,cAAcA,CAAClS,IAAI,EAAE;IACjB,IAAI,CAACoR,cAAc,GAAGpR,IAAI;EAC9B;EACA4b,OAAOA,CAAA,EAAG;IACN,IAAI,CAACH,iBAAiB,CAACI,OAAO,CAAC/W,IAAI,CAAC,IAAI,CAACmW,iBAAiB,CAAC5V,WAAW,CAAC,IAAI,CAAChJ,MAAM,CAAC,CAAC;IACpF,IAAI,CAACyf,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACvB,IAAI,CAACG,iBAAiB,CAACK,KAAK,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,CAACT,cAAc,GAAGN,cAAc,CAACgB,KAAK;EAC9C;EACAjjB,aAAaA,CAACyO,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACyU,SAAS,KAAK,MAAM,IAAIzU,KAAK,CAAC0U,OAAO,KAAKlB,cAAc,CAACgB,KAAK,EAAE;MACtE,IAAI,CAACN,iBAAiB,CAACK,KAAK,CAAC,CAAC;IAClC;EACJ;EACApO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwN,WAAW,CAACpW,IAAI,CAAC,CAAC;IACvB,IAAI,CAACoW,WAAW,CAACgB,QAAQ,CAAC,CAAC;EAC/B;EACAhX,cAAcA,CAAC9N,IAAI,EAAE;IACjB,IAAI,CAAC6jB,iBAAiB,CAAChW,yBAAyB,CAAC7N,IAAI,EAAE,IAAI,CAAC+E,OAAO,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACjB,UAAU,CAAC;EACpH;EACAogB,UAAUA,CAAA,EAAG;IACT,MAAMrf,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,IAAK,CAAC,IAAI,CAAC/E,IAAI,IAAI,CAAC,IAAI,CAACoO,WAAY,EAAE;MAC9C,MAAMpO,IAAI,GAAG2J,WAAW,CAAC+C,oBAAoB,CAAC3H,OAAO,EAAE,IAAI,CAACE,MAAM,CAAC;MACnE,IAAI,CAAC6I,cAAc,CAAC9N,IAAI,CAAC;IAC7B;EACJ;EACAukB,YAAYA,CAAA,EAAG;IACX,MAAMvkB,IAAI,GAAG2J,WAAW,CAAC4B,kBAAkB,CAAC,IAAI,CAACsY,iBAAiB,CAAC5V,WAAW,CAAC,IAAI,CAAChJ,MAAM,CAAC,EAAE;MACzF8E,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB9E,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;IACF,IAAI,CAACof,iBAAiB,CAACvN,WAAW,CAACiO,IAAI,CAAC/kB,IAAI,CAAC;EACjD;AACJ;AACA4jB,uCAAuC,CAAC/U,IAAI,YAAAmW,gDAAAjW,CAAA;EAAA,YAAAA,CAAA,IAAyF6U,uCAAuC,EAvoC5DroB,EAAE,CAAA4W,iBAAA,CAuoC4E9E,4BAA4B,GAvoC1G9R,EAAE,CAAA4W,iBAAA,CAuoCqHxC,iCAAiC,GAvoCxJpU,EAAE,CAAA4W,iBAAA,CAuoCmK1C,WAAW;AAAA,CAA4C;AAC5UmU,uCAAuC,CAAC3Q,IAAI,kBAxoCoE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAwoCOqU,uCAAuC;EAAApR,SAAA;EAAAC,YAAA,WAAAwS,qDAAAhnB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxoChD1C,EAAE,CAAAmE,UAAA,qBAAAwlB,mEAAAtlB,MAAA;QAAA,OAwoCO1B,GAAA,CAAAiD,SAAA,CAAAvB,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAAiT,MAAA;IAAAzE,WAAA;EAAA;EAAAoI,QAAA,GAxoC1Bjb,EAAE,CAAAqd,kBAAA,CAwoC2M,CAACvL,4BAA4B,CAAC;EAAAgG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA2R,iDAAAlnB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxoC3O1C,EAAE,CAAAiL,SAAA,YAwoCsa,CAAC;MAxoCzajL,EAAE,CAAA8C,cAAA,YAwoC0c,CAAC,wCAAD,CAAC,YAAD,CAAC;MAxoC7c9C,EAAE,CAAAmE,UAAA,8BAAA0lB,0FAAAxlB,MAAA;QAAA,OAwoC6xB1B,GAAA,CAAAwD,aAAA,CAAA9B,MAAoB,CAAC;MAAA,CAAC,CAAC;MAxoCtzBrE,EAAE,CAAA8C,cAAA,eAwoCm5B,CAAC,qCAAD,CAAC;MAxoCt5B9C,EAAE,CAAAmE,UAAA,2BAAA2lB,uGAAAzlB,MAAA;QAAA,OAwoC2wD1B,GAAA,CAAA4J,YAAA,CAAAlI,MAAmB,CAAC;MAAA,CAAC,CAAC,6BAAA0lB,yGAAA1lB,MAAA;QAAA,OAAoE1B,GAAA,CAAA4c,cAAA,CAAAlb,MAAqB,CAAC;MAAA,CAA3F,CAAC,yBAAA2lB,qGAAA3lB,MAAA;QAAA,OAA4J1B,GAAA,CAAAuG,YAAA,CAAA7E,MAAmB,CAAC;MAAA,CAAjL,CAAC,2BAAA4lB,uGAAA5lB,MAAA;QAAA,OAAoP1B,GAAA,CAAA4H,cAAA,CAAAlG,MAAqB,CAAC;MAAA,CAA3Q,CAAC;MAxoCnyDrE,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAgD,YAAA,CAwoC8lE,CAAC,CAAD,CAAC;MAxoCjmEhD,EAAE,CAAA8C,cAAA,YAwoC2qE,CAAC,aAAD,CAAC;MAxoC9qE9C,EAAE,CAAA+C,UAAA,KAAAmH,uDAAA,gBAwoC+mH,CAAC;MAxoClnHlK,EAAE,CAAA+C,UAAA,KAAAoH,wFAAA,kDAwoCk9I,CAAC;MAxoCr9InK,EAAE,CAAAgD,YAAA,CAwoC0+I,CAAC;MAxoC7+IhD,EAAE,CAAA8C,cAAA,cAwoC+hJ,CAAC,cAAD,CAAC;MAxoCliJ9C,EAAE,CAAAmE,UAAA,mBAAA+lB,uEAAA;QAAA,OAwoCqkJvnB,GAAA,CAAAwmB,KAAA,CAAM,CAAC;MAAA,CAAC,CAAC;MAxoChlJnpB,EAAE,CAAA+C,UAAA,KAAA2H,gEAAA,0BAwoCwyJ,CAAC;MAxoC3yJ1K,EAAE,CAAAgD,YAAA,CAwoCo0J,CAAC;MAxoCv0JhD,EAAE,CAAA8C,cAAA,cAwoCq3J,CAAC;MAxoCx3J9C,EAAE,CAAAmE,UAAA,mBAAAgmB,uEAAA;QAAA,OAwoC02JxnB,GAAA,CAAAsmB,OAAA,CAAQ,CAAC;MAAA,CAAC,CAAC;MAxoCv3JjpB,EAAE,CAAA+C,UAAA,KAAA4H,gEAAA,0BAwoCklK,CAAC;MAxoCrlK3K,EAAE,CAAAgD,YAAA,CAwoC8mK,CAAC,CAAD,CAAC,CAAD,CAAC,CAAD,CAAC,CAAD,CAAC,CAAD,CAAC;MAxoCjnKhD,EAAE,CAAA+C,UAAA,KAAA6H,+DAAA,iCAAF5K,EAAE,CAAA0G,sBAwoCw1K,CAAC;MAxoC31K1G,EAAE,CAAA+C,UAAA,KAAA8H,+DAAA,iCAAF7K,EAAE,CAAA0G,sBAwoCm9K,CAAC;IAAA;IAAA,IAAAhE,EAAA;MAAA,MAAAuB,GAAA,GAxoCt9KjE,EAAE,CAAAoD,WAAA;MAAA,MAAA6I,GAAA,GAAFjM,EAAE,CAAAoD,WAAA;MAAA,IAAAgnB,OAAA;MAAA,IAAAC,OAAA;MAAFrqB,EAAE,CAAAqD,UAAA,YAAAV,GAAA,CAAA2nB,mBAwoC+U,CAAC,YAxoClVtqB,EAAE,CAAAkF,eAAA,KAAA4F,IAAA,EAAAnI,GAAA,CAAAiV,aAAA,CAwoC+U,CAAC;MAxoClV5X,EAAE,CAAAuD,SAAA,EAwoCkhB,CAAC;MAxoCrhBvD,EAAE,CAAAqD,UAAA,kBAAAV,GAAA,CAAAiV,aAwoCkhB,CAAC,iBAAAjV,GAAA,CAAAW,YAAD,CAAC,+BAAAX,GAAA,CAAAglB,KAAD,CAAC;MAxoCrhB3nB,EAAE,CAAAuD,SAAA,EAwoCwvB,CAAC;MAxoC3vBvD,EAAE,CAAAqD,UAAA,gBAAAV,GAAA,CAAA+lB,cAwoCwvB,CAAC,YAAA/lB,GAAA,CAAA4nB,eAAD,CAAC;MAxoC3vBvqB,EAAE,CAAAuD,SAAA,EAwoCs9B,CAAC;MAxoCz9BvD,EAAE,CAAAqD,UAAA,WAAAV,GAAA,CAAA+G,MAwoCs9B,CAAC,UAAA0gB,OAAA,GAxoCz9BpqB,EAAE,CAAAsJ,WAAA,QAAA3G,GAAA,CAAA4G,YAAA,oBAAA6gB,OAAA,CAAA3lB,IAwoCs9B,CAAC,YAAA4lB,OAAA,GAxoCz9BrqB,EAAE,CAAAsJ,WAAA,QAAA3G,GAAA,CAAA8H,cAAA,oBAAA4f,OAAA,CAAA5lB,IAwoCs9B,CAAC,WAxoCz9BzE,EAAE,CAAAsJ,WAAA,QAAA3G,GAAA,CAAAsH,cAAA,CAwoCs9B,CAAC,mBAAAtH,GAAA,CAAA8b,cAAD,CAAC,YAAA9b,GAAA,CAAA6G,OAAD,CAAC,YAAA7G,GAAA,CAAA8G,OAAD,CAAC,eAAA9G,GAAA,CAAA6nB,mBAAD,CAAC,qBAAA7nB,GAAA,CAAAiE,gBAAD,CAAC,eAAAjE,GAAA,CAAA8F,UAAD,CAAC,cAAA9F,GAAA,CAAAkd,SAAD,CAAC;MAxoCz9B7f,EAAE,CAAAuD,SAAA,EAwoC0vE,CAAC;MAxoC7vEvD,EAAE,CAAAqD,UAAA,aAAAV,GAAA,CAAA8b,cAwoC0vE,CAAC;MAxoC7vEze,EAAE,CAAAuD,SAAA,EAwoCmzE,CAAC;MAxoCtzEvD,EAAE,CAAAqD,UAAA,iBAAAV,GAAA,CAAAqC,QAAA,CAAA4C,IAwoCmzE,CAAC;MAxoCtzE5H,EAAE,CAAAuD,SAAA,EAwoC0sH,CAAC;MAxoC7sHvD,EAAE,CAAAqD,UAAA,iBAAAV,GAAA,CAAAqC,QAAA,CAAA0D,MAwoC0sH,CAAC;MAxoC7sH1I,EAAE,CAAAuD,SAAA,EAwoCsxJ,CAAC;MAxoCzxJvD,EAAE,CAAAqD,UAAA,qBAAAV,GAAA,CAAA8nB,aAAA,GAAA9nB,GAAA,CAAA8nB,aAAA,GAAAxmB,GAwoCsxJ,CAAC;MAxoCzxJjE,EAAE,CAAAuD,SAAA,EAwoCgkK,CAAC;MAxoCnkKvD,EAAE,CAAAqD,UAAA,qBAAAV,GAAA,CAAA+nB,cAAA,GAAA/nB,GAAA,CAAA+nB,cAAA,GAAAze,GAwoCgkK,CAAC;IAAA;EAAA;EAAAoM,YAAA,GAA03Eb,qCAAqC,EAAoG0H,kCAAkC,EAA0R4G,yCAAyC,EAA+DO,yCAAyC,EAAmFK,yCAAyC,EAA6LM,oCAAoC,EAAsEG,gBAAgB,EAAwDnmB,EAAE,CAACwc,OAAO,EAAiEkK,mCAAmC,EAA8F1mB,EAAE,CAAC2pB,QAAQ,EAA0D3pB,EAAE,CAAC4pB,YAAY,EAAkE5pB,EAAE,CAACsX,IAAI,EAA0EtX,EAAE,CAACuX,gBAAgB,EAAgHvX,EAAE,CAAC6pB,SAAS;EAAAjN,MAAA;EAAAkB,IAAA;IAAAC,SAAA,EAAgB,CACxrSld,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAE,QAAOsmB,cAAc,CAACQ,KAAM,EAAC,EAAE,CACvC7mB,KAAK,CAAC;MAAE2X,SAAS,EAAE;IAAmB,CAAC,CAAC,EACxC1X,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;MAAE2X,SAAS,EAAE;IAAgB,CAAC,CAAC,CAAC,CAClE,CAAC,EACF5X,UAAU,CAAE,GAAEsmB,cAAc,CAACQ,KAAM,OAAMR,cAAc,CAACgB,KAAM,EAAC,EAAE,CAC7DrnB,KAAK,CAAC;MAAE2X,SAAS,EAAE,eAAe;MAAEsF,OAAO,EAAE;IAAE,CAAC,CAAC,EACjDhd,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;MAAE2X,SAAS,EAAE,kBAAkB;MAAEsF,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CACjF,CAAC,CACL,CAAC;EACL;AAAA,EAAG;AACR;EAAA,QAAAlL,SAAA,oBAAAA,SAAA,KAppCgH9T,EAAE,CAAA+T,iBAAA,CAopCtBsU,uCAAuC,EAAc,CAAC;IACtIrU,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,mCAAmC;MAC7CkB,WAAW,EAAE,oDAAoD;MACjEoF,SAAS,EAAE,CAAC,oDAAoD,CAAC;MACjEoB,UAAU,EAAE,CACRpd,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAE,QAAOsmB,cAAc,CAACQ,KAAM,EAAC,EAAE,CACvC7mB,KAAK,CAAC;QAAE2X,SAAS,EAAE;MAAmB,CAAC,CAAC,EACxC1X,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;QAAE2X,SAAS,EAAE;MAAgB,CAAC,CAAC,CAAC,CAClE,CAAC,EACF5X,UAAU,CAAE,GAAEsmB,cAAc,CAACQ,KAAM,OAAMR,cAAc,CAACgB,KAAM,EAAC,EAAE,CAC7DrnB,KAAK,CAAC;QAAE2X,SAAS,EAAE,eAAe;QAAEsF,OAAO,EAAE;MAAE,CAAC,CAAC,EACjDhd,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;QAAE2X,SAAS,EAAE,kBAAkB;QAAEsF,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CACjF,CAAC,CACL,CAAC,CACL;MACDlB,SAAS,EAAE,CAAChM,4BAA4B;IAC5C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEkC,IAAI,EAAElC;IAA6B,CAAC,EAAE;MAAEkC,IAAI,EAAEI;IAAkC,CAAC,EAAE;MAAEJ,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QACnJnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAErB,WAAW,EAAE,CAAC;MAC1CmB,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwF,SAAS,EAAE,CAAC;MACZoO,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6W,UAAU,CAAC;EACb/Y,WAAWA,CAACgZ,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEzQ,QAAQ,EAAE;IACzC,IAAI,CAACuQ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACzQ,QAAQ,GAAGA,QAAQ;EAC5B;EACA0Q,sBAAsBA,CAACC,UAAU,EAAEjS,MAAM,EAAE;IACvC,IAAI,CAACkS,YAAY,GAAG,IAAI,CAACL,GAAG,CAACM,uBAAuB,CAACF,UAAU,CAAC,CAACG,MAAM,CAAC,IAAI,CAACL,QAAQ,CAAC;IACtF3b,MAAM,CAACic,IAAI,CAACrS,MAAM,CAAC,CAACsS,OAAO,CAACC,GAAG,IAAI,IAAI,CAACL,YAAY,CAACM,QAAQ,CAACD,GAAG,CAAC,GAAGvS,MAAM,CAACuS,GAAG,CAAC,CAAC;IACjF,IAAI,CAACT,MAAM,CAACW,UAAU,CAAC,IAAI,CAACP,YAAY,CAACQ,QAAQ,CAAC;IAClD,MAAMC,UAAU,GAAG,IAAI,CAACT,YAAY,CAACQ,QAAQ,CACxCE,SAAS,CAAC,CAAC,CAAC;IACjB,IAAI,CAACtR,QAAQ,CAACuR,IAAI,CAACC,WAAW,CAACH,UAAU,CAAC;EAC9C;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACb,YAAY,CAACc,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAClB,MAAM,CAACmB,UAAU,CAAC,IAAI,CAACf,YAAY,CAACQ,QAAQ,CAAC;EACtD;AACJ;AACAd,UAAU,CAACxX,IAAI,YAAA8Y,mBAAA5Y,CAAA;EAAA,YAAAA,CAAA,IAAyFsX,UAAU,EAtsCF9qB,EAAE,CAAAqsB,QAAA,CAssCkBrsB,EAAE,CAACssB,wBAAwB,GAtsC/CtsB,EAAE,CAAAqsB,QAAA,CAssC0DrsB,EAAE,CAACusB,cAAc,GAtsC7EvsB,EAAE,CAAAqsB,QAAA,CAssCwFrsB,EAAE,CAACwsB,QAAQ,GAtsCrGxsB,EAAE,CAAAqsB,QAAA,CAssCgHprB,QAAQ;AAAA,CAA6D;AACvS6pB,UAAU,CAACrX,KAAK,kBAvsCgGzT,EAAE,CAAA0T,kBAAA;EAAAC,KAAA,EAusCNmX,UAAU;EAAAlX,OAAA,EAAVkX,UAAU,CAAAxX,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC7I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxsCgH9T,EAAE,CAAA+T,iBAAA,CAwsCtB+W,UAAU,EAAc,CAAC;IACzG9W,IAAI,EAAE/T,UAAU;IAChBgU,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEhU,EAAE,CAACssB;IAAyB,CAAC,EAAE;MAAEtY,IAAI,EAAEhU,EAAE,CAACusB;IAAe,CAAC,EAAE;MAAEvY,IAAI,EAAEhU,EAAE,CAACwsB;IAAS,CAAC,EAAE;MAAExY,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QACzJnG,IAAI,EAAEvT;MACV,CAAC,EAAE;QACCuT,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAAChT,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMwrB,MAAM,GAAG,EAAE;AACjB,MAAMC,8BAA8B,CAAC;EACjC3a,WAAWA,CAACqV,YAAY,EAAEuF,UAAU,EAAE;IAClC,IAAI,CAACvF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACuF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC5D,WAAW,GAAG,IAAI3nB,OAAO,CAAC,CAAC;IAChC,IAAI,CAACwrB,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC/M,SAAS,GAAG,KAAK;IACtB,IAAI,CAACqJ,OAAO,GAAG,IAAIxoB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACmsB,MAAM,GAAG,IAAInsB,YAAY,CAAC,CAAC;IAChC,IAAI,CAACosB,MAAM,GAAG,IAAIpsB,YAAY,CAAC,CAAC;IAChC,IAAI,CAACkgB,YAAY,GAAG,IAAIlgB,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC6a,WAAW,GAAG,IAAI7a,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC6nB,WAAW,GAAG,IAAInnB,OAAO,CAAC,CAAC;EACpC;EACA;AACJ;AACA;EACI,IAAI2rB,0BAA0BA,CAACpF,KAAK,EAAE;IAClCnV,OAAO,CAACwa,IAAI,CAAE,iEAAgE,CAAC;IAC/E,IAAI,CAACC,2BAA2B,GAAGtF,KAAK;EAC5C;EACA,IAAIje,MAAMA,CAAC8M,KAAK,EAAE;IACd,IAAI,CAAC0W,OAAO,GAAG1W,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;EACzC;EACA,IAAI9M,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACyjB,eAAe,GAAG,IAAI,CAACA,eAAe,CAACzjB,MAAM,GAAG,IAAI,CAACwjB,OAAO;EAC5E;EACA,IAAIzkB,UAAUA,CAAC4Q,GAAG,EAAE;IAChB,IAAIA,GAAG,IAAI,IAAI,EAAE;MACb;IACJ;IACAA,GAAG,GAAG0J,IAAI,CAACqK,KAAK,CAAC/T,GAAG,CAAC;IACrB,IAAI,CAACgU,WAAW,GAAGhU,GAAG,IAAI,EAAE,GAAGA,GAAG,GAAG,CAAC;EAC1C;EACA,IAAI5Q,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC4kB,WAAW;EAC3B;EACA,IAAI7jB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2jB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC7c,GAAG,GAAG,IAAI,CAACA,GAAG;EACrE;EACA,IAAI7G,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC0jB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC5c,GAAG,GAAG,IAAI,CAACA,GAAG;EACrE;EACA,IAAIlL,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC8nB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC9nB,QAAQ;EAChE;EACA,IAAIZ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC0oB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC3W,KAAK;EAC7D;EACA,IAAIlT,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC6pB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAClY,OAAO;EAC/D;EACA;AACJ;AACA;AACA;EACIqY,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,IAAI,CAACJ,eAAe,EAAE;MACtB,MAAMvc,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACuc,eAAe,GAAGI,KAAK;EAChC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACb,UAAU,CAACzB,sBAAsB,CAAC7C,uCAAuC,EAAE;MAC5ES,iBAAiB,EAAE,IAAI;MACvBrkB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfoO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BpJ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBjB,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BkgB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvC8B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnC9jB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCvB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBmlB,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CF,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7C1S,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCiI,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB8H,KAAK,EAAE,IAAI,CAACA,KAAK,IAAI,IAAI,CAACsF,2BAA2B;MACrD1C,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCjnB,YAAY,EAAE,IAAI,CAACA;IACvB,CAAC,CAAC;IACF,IAAI,CAACupB,MAAM,CAAC1a,IAAI,CAAC,CAAC;IAClB,IAAI,CAACsb,iBAAiB,CAAC,CAAC;EAC5B;EACAtE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACwD,UAAU,CAACV,iBAAiB,CAAC,CAAC;IACnC,IAAI,CAACa,MAAM,CAAC3a,IAAI,CAAC,CAAC;IAClB,IAAI,CAACub,qBAAqB,CAAC,CAAC;EAChC;EACA9oB,UAAUA,CAACH,IAAI,EAAE;IACb,IAAI,CAACskB,WAAW,CAAC5W,IAAI,CAAC1N,IAAI,CAAC;EAC/B;EACAgpB,iBAAiBA,CAAA,EAAG;IAChBpsB,KAAK,CAAC,IAAI,CAAC+lB,YAAY,CAACjb,aAAa,EAAE,IAAI,CAACib,YAAY,CAAC1S,YAAY,CAACH,IAAI,CAAC7S,MAAM,CAACoM,CAAC,IAAIA,CAAC,CAACG,OAAO,KAAKwe,MAAM,IAAI,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CACvHrY,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAAC4mB,WAAW,CAAC,CAAC,CACjCnM,SAAS,CAAC,MAAM,IAAI,CAAC+M,KAAK,CAAC,CAAC,CAAC;EACtC;EACAuE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACnF,WAAW,CAACpW,IAAI,CAAC,CAAC;IACvB,IAAI,CAACoW,WAAW,CAACgB,QAAQ,CAAC,CAAC;EAC/B;AACJ;AACAmD,8BAA8B,CAACpZ,IAAI,YAAAqa,uCAAAna,CAAA;EAAA,YAAAA,CAAA,IAAyFkZ,8BAA8B,EA9zC1C1sB,EAAE,CAAA4W,iBAAA,CA8zC0DxC,iCAAiC,GA9zC7FpU,EAAE,CAAA4W,iBAAA,CA8zCwGkU,UAAU;AAAA,CAA4C;AAChR4B,8BAA8B,CAAChV,IAAI,kBA/zC6E1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EA+zCF0Y,8BAA8B;EAAAzV,SAAA;EAAAK,MAAA;IAAAmT,aAAA;IAAA7jB,gBAAA;IAAA8jB,cAAA;IAAAkC,KAAA;IAAApC,mBAAA;IAAAF,mBAAA;IAAA3B,gBAAA;IAAA/Q,aAAA;IAAAiI,SAAA;IAAAhN,WAAA;IAAA0X,eAAA;IAAA5C,KAAA;IAAArX,GAAA;IAAAC,GAAA;IAAAwc,0BAAA;IAAArjB,MAAA;IAAAjB,UAAA;EAAA;EAAA2U,OAAA;IAAA8L,OAAA;IAAA2D,MAAA;IAAAC,MAAA;IAAAlM,YAAA;IAAArF,WAAA;EAAA;EAAAzD,KAAA;EAAAC,IAAA;EAAAE,QAAA,WAAA2V,wCAAAlrB,EAAA,EAAAC,GAAA;EAAA6V,aAAA;AAAA,EAAouB;AACl3B;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAh0CgH9T,EAAE,CAAA+T,iBAAA,CAg0CtB2Y,8BAA8B,EAAc,CAAC;IAC7H1Y,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,yBAAyB;MACnCU,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEI;IAAkC,CAAC,EAAE;MAAEJ,IAAI,EAAE8W;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEL,aAAa,EAAE,CAAC;MAC3IzW,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwG,gBAAgB,EAAE,CAAC;MACnBoN,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsqB,cAAc,EAAE,CAAC;MACjB1W,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwsB,KAAK,EAAE,CAAC;MACR5Y,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAEuW,mBAAmB,EAAE,CAAC;MACtBxW,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkqB,mBAAmB,EAAE,CAAC;MACtBtW,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEuoB,gBAAgB,EAAE,CAAC;MACnB3U,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwX,aAAa,EAAE,CAAC;MAChB5D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyf,SAAS,EAAE,CAAC;MACZ7L,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyS,WAAW,EAAE,CAAC;MACdmB,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmqB,eAAe,EAAE,CAAC;MAClBvW,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEunB,KAAK,EAAE,CAAC;MACR3T,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkQ,GAAG,EAAE,CAAC;MACN0D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmQ,GAAG,EAAE,CAAC;MACNyD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE2sB,0BAA0B,EAAE,CAAC;MAC7B/Y,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE8oB,OAAO,EAAE,CAAC;MACVlV,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEisB,MAAM,EAAE,CAAC;MACT7Y,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEksB,MAAM,EAAE,CAAC;MACT9Y,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAEggB,YAAY,EAAE,CAAC;MACf5M,IAAI,EAAEpT;IACV,CAAC,CAAC;IAAE2a,WAAW,EAAE,CAAC;MACdvH,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMitB,wCAAwC,CAAC;AAE/CA,wCAAwC,CAACva,IAAI,YAAAwa,iDAAAta,CAAA;EAAA,YAAAA,CAAA,IAAyFqa,wCAAwC;AAAA,CAAmD;AACjOA,wCAAwC,CAAC9W,IAAI,kBAz3CmE/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EAy3CQ6Z,wCAAwC;EAAA5W,SAAA;AAAA,EAAgE;AAClO;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KA13CgH9T,EAAE,CAAA+T,iBAAA,CA03CtB8Z,wCAAwC,EAAc,CAAC;IACvI7Z,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MAAEsD,QAAQ,EAAE;IAAoC,CAAC;EAC5D,CAAC,CAAC;AAAA;AAEV,MAAMwW,oCAAoC,CAAC;EACvC,IAAI1oB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC2oB,SAAS,KAAK3nB,SAAS,GAAG,IAAI,CAAC8kB,UAAU,CAAC9lB,QAAQ,GAAG,IAAI,CAAC2oB,SAAS;EACnF;EACA,IAAI3oB,QAAQA,CAACmR,KAAK,EAAE;IAChB,IAAI,CAACwX,SAAS,GAAGxX,KAAK;EAC1B;EACAgX,IAAIA,CAAC5Y,KAAK,EAAE;IACR,IAAI,IAAI,CAACuW,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACqC,IAAI,CAAC,CAAC;MACtB5Y,KAAK,CAAC6T,eAAe,CAAC,CAAC;IAC3B;EACJ;AACJ;AACAsF,oCAAoC,CAACza,IAAI,YAAA2a,6CAAAza,CAAA;EAAA,YAAAA,CAAA,IAAyFua,oCAAoC;AAAA,CAAmD;AACzNA,oCAAoC,CAACrW,IAAI,kBA94CuE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EA84CI+Z,oCAAoC;EAAA9W,SAAA;EAAAiX,cAAA,WAAAC,oDAAAzrB,EAAA,EAAAC,GAAA,EAAAyrB,QAAA;IAAA,IAAA1rB,EAAA;MA94C1C1C,EAAE,CAAAquB,cAAA,CAAAD,QAAA,EA84C2NP,wCAAwC;IAAA;IAAA,IAAAnrB,EAAA;MAAA,IAAAsa,EAAA;MA94CrQhd,EAAE,CAAAid,cAAA,CAAAD,EAAA,GAAFhd,EAAE,CAAAkd,WAAA,QAAAva,GAAA,CAAA2rB,UAAA,GAAAtR,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA7F,MAAA;IAAA6T,UAAA;IAAA9lB,QAAA;EAAA;EAAAwS,kBAAA,EAAA1M,IAAA;EAAA2M,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAsW,8CAAA7rB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAAmY,eAAA,CAAAjN,IAAA;MAAFlL,EAAE,CAAA8C,cAAA,eA84Cmb,CAAC;MA94Ctb9C,EAAE,CAAAmE,UAAA,mBAAAqqB,sEAAAnqB,MAAA;QAAA,OA84C6X1B,GAAA,CAAA6qB,IAAA,CAAAnpB,MAAW,CAAC;MAAA,CAAC,CAAC;MA94C7YrE,EAAE,CAAA+C,UAAA,IAAAgI,wDAAA,gBA84Cu+B,CAAC;MA94C1+B/K,EAAE,CAAA2D,YAAA,EA84CqjC,CAAC;MA94CxjC3D,EAAE,CAAAgD,YAAA,CA84CgkC,CAAC;IAAA;IAAA,IAAAN,EAAA;MA94CnkC1C,EAAE,CAAAqD,UAAA,aAAAV,GAAA,CAAA0C,QA84Cka,CAAC;MA94CrarF,EAAE,CAAAuD,SAAA,EA84C2iB,CAAC;MA94C9iBvD,EAAE,CAAAqD,UAAA,UAAAV,GAAA,CAAA2rB,UA84C2iB,CAAC;IAAA;EAAA;EAAAjW,YAAA,GAA46BrX,EAAE,CAACsX,IAAI;EAAAsF,MAAA;AAAA,EAAoE;AACrpD;EAAA,QAAA9J,SAAA,oBAAAA,SAAA,KA/4CgH9T,EAAE,CAAA+T,iBAAA,CA+4CtBga,oCAAoC,EAAc,CAAC;IACnI/Z,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,gCAAgC;MAC1CkB,WAAW,EAAE,+CAA+C;MAC5DoF,SAAS,EAAE,CAAC,+CAA+C;IAC/D,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEsN,UAAU,EAAE,CAAC;MAC3BnX,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAE5O,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkuB,UAAU,EAAE,CAAC;MACbta,IAAI,EAAElT,YAAY;MAClBmT,IAAI,EAAE,CAAC4Z,wCAAwC,EAAE;QAAEnI,MAAM,EAAE;MAAK,CAAC;IACrE,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+I,mBAAmB,CAAC;EACtB1c,WAAWA,CAACgD,UAAU,EAAEvG,MAAM,EAAE;IAC5B,IAAI,CAACuG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACvG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC0e,OAAO,GAAG,EAAE;IACjB,IAAI,CAACwB,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,SAAS,GAAG,MAAM,CACvB,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,MAAM,CACtB,CAAC;EACL;EACA,IAAInlB,MAAMA,CAAC8M,KAAK,EAAE;IACd,IAAI,CAAC0W,OAAO,GAAG1W,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;IACrC,MAAMsY,oBAAoB,GAAGtY,KAAK,IAAK,IAAI,CAACuY,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,IAAI,CAAC7B,OAAQ;IACnG,IAAI4B,oBAAoB,EAAE;MACtB,IAAI,CAACtY,KAAK,GAAG,IAAI,CAACkY,MAAM;MACxB,IAAI,CAACM,WAAW,CAACpqB,UAAU,CAAC,IAAI,CAAC8pB,MAAM,CAAC;IAC5C;IACA,IAAI,CAACK,cAAc,GAAG,IAAI,CAAC7B,OAAO;EACtC;EACA,IAAIxjB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwjB,OAAO;EACvB;EACA,IAAI5c,GAAGA,CAACkG,KAAK,EAAE;IACXhE,OAAO,CAACyc,GAAG,CAACzY,KAAK,CAAC;IAClB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC0Y,IAAI,GAAG9gB,WAAW,CAACC,SAAS,CAACmI,KAAK,EAAE;QAAEhI,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE9E,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MACtF;IACJ;IACA,IAAI,CAACwlB,IAAI,GAAG1Y,KAAK;EACrB;EACA,IAAIlG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC4e,IAAI;EACpB;EACA,IAAI3e,GAAGA,CAACiG,KAAK,EAAE;IACX,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC2Y,IAAI,GAAG/gB,WAAW,CAACC,SAAS,CAACmI,KAAK,EAAE;QAAEhI,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE9E,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MACtF;IACJ;IACA,IAAI,CAACylB,IAAI,GAAG3Y,KAAK;EACrB;EACA,IAAIjG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC4e,IAAI;EACpB;EACA,IAAIhE,UAAUA,CAACiE,MAAM,EAAE;IACnB,IAAI,CAACC,kBAAkB,CAACD,MAAM,CAAC;EACnC;EACA,IAAI5Y,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACkY,MAAM,GAAG,EAAE;MAChB,IAAI,CAAC1S,gBAAgB,CAAC,CAAC;MACvB;IACJ;IACA,IAAI,CAACsT,kBAAkB,CAAC9Y,KAAK,CAAC;EAClC;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACkY,MAAM,EAAE;MACd,OAAO,EAAE;IACb;IACA,OAAOtgB,WAAW,CAAC4B,kBAAkB,CAAC,IAAI,CAAC0e,MAAM,EAAE;MAAEhlB,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE8E,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;EACpG;EACA,IAAIyG,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,UAAU,IAAI,IAAI,CAACA,UAAU,CAACG,aAAa;EAC3D;EACA,IAAIrC,WAAWA,CAACpO,IAAI,EAAE;IAClB,IAAI,CAACuqB,WAAW,CAACnc,WAAW,GAAGzE,WAAW,CAACY,UAAU,CAACvK,IAAI,EAAE;MAAE+J,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE9E,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;EAC7G;EACA6lB,WAAWA,CAAC/Y,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqY,QAAQ,CAACrY,KAAK,CAAC;EACxB;EACAkE,WAAWA,CAAC2E,OAAO,EAAE;IACjB,IAAIxF,EAAE;IACN,MAAMrD,KAAK,GAAG,CAACqD,EAAE,GAAGwF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC7I,KAAK,MAAM,IAAI,IAAIqD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyF,YAAY;IACzI,IAAI9I,KAAK,EAAE;MACP;MACA,IAAI,CAAC8Y,kBAAkB,CAAC9Y,KAAK,CAAC;MAC9B,IAAI,CAAC3D,WAAW,GAAG2D,KAAK;IAC5B;EACJ;EACA6Q,OAAOA,CAACzS,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAAC4a,YAAY,EAAE;MACpB,IAAI,CAACR,WAAW,CAACxB,IAAI,CAAC,CAAC;MACvB5Y,KAAK,CAAC6T,eAAe,CAAC,CAAC;IAC3B;EACJ;EACAgH,UAAUA,CAACjZ,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC3D,WAAW,GAAG2D,KAAK;IAC5B;EACJ;EACAkZ,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACd,QAAQ,GAAGc,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACf,SAAS,GAAGe,EAAE;EACvB;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACzqB,QAAQ,GAAGyqB,UAAU;EAC9B;EACA/U,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4T,uBAAuB,CAACnD,OAAO,CAACuE,CAAC,IAAIA,CAAC,CAACxH,WAAW,CAAC,CAAC,CAAC;EAC9D;EACA8G,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACJ,WAAW,GAAGI,MAAM;MACzB,IAAI,CAACJ,WAAW,CAAC1B,aAAa,CAAC,IAAI,CAAC;MACpC,IAAI,CAACqB,uBAAuB,CAACpV,IAAI,CAAC,IAAI,CAACyV,WAAW,CAAC9F,OAAO,CAAC9M,SAAS,CAAE3X,IAAI,IAAK;QAC3E,IAAI,CAAC+R,KAAK,GAAG/R,IAAI;QACjB,IAAI,CAACoqB,QAAQ,CAAC,IAAI,CAACrY,KAAK,CAAC;QACzB,IAAI,CAACoY,SAAS,CAAC,CAAC;QAChB,IAAI,CAAC/b,WAAW,GAAG,IAAI,CAAC6b,MAAM;MAClC,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,MAAM,IAAI9d,KAAK,CAAC,gDAAgD,GAC5D,wEAAwE,CAAC;IACjF;EACJ;EACAoL,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACjH,UAAU,CAACG,aAAa,CAACsB,KAAK,GAAG,IAAI,CAACA,KAAK;EACpD;EACA8Y,kBAAkBA,CAAC9Y,KAAK,EAAE;IACtB,IAAIqD,EAAE;IACN,MAAMpV,IAAI,GAAG2J,WAAW,CAACY,UAAU,CAACwH,KAAK,EAAE;MAAEhI,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE9E,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;IACxF,MAAMsH,WAAW,GAAG5C,WAAW,CAACiC,eAAe,CAAC5L,IAAI,EAAE,IAAI,CAACyqB,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,SAAS,EAAE,CAACtV,EAAE,GAAG,IAAI,CAACmV,WAAW,MAAM,IAAI,IAAInV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpR,UAAU,EAAE,IAAI,CAACykB,OAAO,CAAC;IAChL,IAAIlc,WAAW,EAAE;MACb,IAAI,CAAC0d,MAAM,GAAGjqB,IAAI;MAClB,IAAI,CAACuX,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACxF,KAAK,GAAG,IAAI;MACjBhE,OAAO,CAACwa,IAAI,CAAC,+CAA+C,CAAC;IACjE;EACJ;AACJ;AACAyB,mBAAmB,CAACnb,IAAI,YAAA0c,4BAAAxc,CAAA;EAAA,YAAAA,CAAA,IAAyFib,mBAAmB,EA1iDpBzuB,EAAE,CAAA4W,iBAAA,CA0iDoC5W,EAAE,CAAC6W,UAAU,GA1iDnD7W,EAAE,CAAA4W,iBAAA,CA0iD8D1C,WAAW;AAAA,CAA4C;AACvOua,mBAAmB,CAAC1X,IAAI,kBA3iDwF/W,EAAE,CAAAgX,iBAAA;EAAAhD,IAAA,EA2iDbya,mBAAmB;EAAAxX,SAAA;EAAAgZ,QAAA;EAAA/Y,YAAA,WAAAgZ,iCAAAxtB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3iDR1C,EAAE,CAAAmE,UAAA,oBAAAgsB,8CAAA9rB,MAAA;QAAA,OA2iDb1B,GAAA,CAAA4sB,WAAA,CAAAlrB,MAAA,CAAA+rB,MAAA,CAAA5Z,KAA+B,CAAC;MAAA,oBAAA6Z,4CAAA;QAAA,OAAhC1tB,GAAA,CAAAisB,SAAA,CAAU,CAAC;MAAA,qBAAA0B,6CAAAjsB,MAAA;QAAA,OAAX1B,GAAA,CAAA0kB,OAAA,CAAAhjB,MAAc,CAAC;MAAA;IAAA;IAAA,IAAA3B,EAAA;MA3iDJ1C,EAAE,CAAAuwB,cAAA,aAAA5tB,GAAA,CAAA0C,QAAA;IAAA;EAAA;EAAAiS,MAAA;IAAA5N,MAAA;IAAA4G,GAAA;IAAAC,GAAA;IAAA4a,UAAA;IAAA3U,KAAA;IAAAnR,QAAA;IAAAmqB,YAAA;EAAA;EAAAvU,QAAA,GAAFjb,EAAE,CAAAqd,kBAAA,CA2iDyX,CACne;IACImT,OAAO,EAAEluB,iBAAiB;IAC1BmuB,WAAW,EAAEhC,mBAAmB;IAChCiC,KAAK,EAAE;EACX,CAAC,CACJ,GAjjD2G1wB,EAAE,CAAAkb,oBAAA;AAAA,EAijDvE;AAC3C;EAAA,QAAApH,SAAA,oBAAAA,SAAA,KAljDgH9T,EAAE,CAAA+T,iBAAA,CAkjDtB0a,mBAAmB,EAAc,CAAC;IAClHza,IAAI,EAAE7T,SAAS;IACf8T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,iBAAiB;MAC3BuG,SAAS,EAAE,CACP;QACI0S,OAAO,EAAEluB,iBAAiB;QAC1BmuB,WAAW,EAAEhC,mBAAmB;QAChCiC,KAAK,EAAE;MACX,CAAC,CACJ;MACDC,IAAI,EAAE;QACF,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,kCAAkC;QAC9C,QAAQ,EAAE;MACd;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3c,IAAI,EAAEhU,EAAE,CAAC6W;IAAW,CAAC,EAAE;MAAE7C,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QACvFnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAExK,MAAM,EAAE,CAAC;MACrCsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkQ,GAAG,EAAE,CAAC;MACN0D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmQ,GAAG,EAAE,CAAC;MACNyD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE+qB,UAAU,EAAE,CAAC;MACbnX,IAAI,EAAE5T,KAAK;MACX6T,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEuC,KAAK,EAAE,CAAC;MACRxC,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiF,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEovB,YAAY,EAAE,CAAC;MACfxb,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEinB,OAAO,EAAE,CAAC;MACVrT,IAAI,EAAE3T,YAAY;MAClB4T,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2c,iBAAiB,CAAC;EACpBlX,SAASA,CAACjV,IAAI,EAAEO,QAAQ,EAAE;IACtB,IAAIP,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;MAC7B,OAAOA,IAAI;IACf;IACA,QAAQO,QAAQ;MACZ,KAAKgI,QAAQ,CAACpF,IAAI;QACd,OAAO1F,QAAQ,CAACkX,UAAU,CAAC;UAAE9L,IAAI,EAAE,CAAC7I;QAAK,CAAC,CAAC,CAAC8M,QAAQ,CAAC,IAAI,CAAC;MAC9D,KAAKvE,QAAQ,CAACtE,MAAM;QAChB,OAAOxG,QAAQ,CAACkX,UAAU,CAAC;UAAEzI,MAAM,EAAE,CAAClM;QAAK,CAAC,CAAC,CAAC8M,QAAQ,CAAC,IAAI,CAAC;MAChE;QACI,MAAM,IAAIX,KAAK,CAAC,mBAAmB,CAAC;IAC5C;EACJ;AACJ;AACAggB,iBAAiB,CAACtd,IAAI,YAAAud,0BAAArd,CAAA;EAAA,YAAAA,CAAA,IAAyFod,iBAAiB;AAAA,CAA8C;AAC9KA,iBAAiB,CAAC7W,KAAK,kBA1mDyF/Z,EAAE,CAAAga,YAAA;EAAAC,IAAA;EAAAjG,IAAA,EA0mDL4c,iBAAiB;EAAA1W,IAAA;AAAA,EAA0B;AACxJ;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KA3mDgH9T,EAAE,CAAA+T,iBAAA,CA2mDtB6c,iBAAiB,EAAc,CAAC;IAChH5c,IAAI,EAAEzT,IAAI;IACV0T,IAAI,EAAE,CAAC;MACCgG,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM6W,iCAAiC,CAAC;EACpC/e,WAAWA,CAACgf,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACxV,WAAW,GAAG,IAAI7a,YAAY,CAAC,CAAC;EACzC;EACAga,WAAWA,CAAC2E,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC3D,QAAQ,IAAI,IAAI,CAACjX,IAAI,IAAI,IAAI,EAAE;MACvC,IAAI,IAAI,CAACusB,sBAAsB,CAAC,IAAI,CAACvsB,IAAI,CAAC,EAAE;QACxC,IAAI,CAACwsB,gBAAgB,CAAC,CAAC;MAC3B;IACJ;EACJ;EACAC,UAAUA,CAACtc,KAAK,EAAE;IACdA,KAAK,CAAC6T,eAAe,CAAC,CAAC;IACvB,MAAM0I,IAAI,GAAG9W,MAAM,CAAC+W,YAAY,CAACxc,KAAK,CAAC3G,OAAO,CAAC;IAC/C,MAAMxJ,IAAI,GAAG4sB,UAAU,CAAChX,MAAM,CAAC,IAAI,CAAC5V,IAAI,CAAC,EAAE0sB,IAAI,CAAC;IAChD,IAAI,CAACG,iBAAiB,CAAC7sB,IAAI,CAAC;EAChC;EACAmB,SAASA,CAACgP,KAAK,EAAE;IACbA,KAAK,CAAC6T,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC5a,OAAO,CAAC+G,KAAK,CAAC,EAAE;MACjBA,KAAK,CAACyH,cAAc,CAAC,CAAC;IAC1B;IACA,QAAQzH,KAAK,CAAC6W,GAAG;MACb,KAAK,SAAS;QACV,IAAI,CAAC8F,QAAQ,CAAC,CAAC;QACf;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;QACf;IACR;IACA,IAAI,IAAI,CAACC,aAAa,IAAI7c,KAAK,CAAC6W,GAAG,KAAK,KAAK,EAAE;MAC3C7W,KAAK,CAACyH,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAkV,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAClsB,QAAQ,EAAE;MAChB,IAAI0Y,QAAQ,GAAG,CAAC,IAAI,CAACtZ,IAAI,IAAI,IAAI,CAACgE,UAAU,IAAI,CAAC,CAAC;MAClD,IAAIsV,QAAQ,GAAG,IAAI,CAACxN,GAAG,EAAE;QACrBwN,QAAQ,GAAG,IAAI,CAACzN,GAAG;MACvB;MACA,IAAI,IAAI,CAAC0gB,sBAAsB,CAACjT,QAAQ,CAAC,EAAE;QACvCA,QAAQ,GAAG,IAAI,CAAC2T,gBAAgB,CAAC3T,QAAQ,EAAE,IAAI,CAAC4T,oBAAoB,CAAC5N,IAAI,CAAC,IAAI,CAAC,CAAC;MACpF;MACA,IAAIhG,QAAQ,KAAK,IAAI,CAACtZ,IAAI,EAAE;QACxB,IAAI,CAAC8W,WAAW,CAACiO,IAAI,CAACzL,QAAQ,CAAC;MACnC;IACJ;EACJ;EACAyT,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACnsB,QAAQ,EAAE;MAChB,IAAIiX,YAAY,GAAG,CAAC,IAAI,CAAC7X,IAAI,IAAI,IAAI,CAACgE,UAAU,IAAI,CAAC,CAAC;MACtD,IAAI6T,YAAY,GAAG,IAAI,CAAChM,GAAG,EAAE;QACzBgM,YAAY,GAAG,IAAI,CAAC7T,UAAU,GAAG,IAAI,CAAC8H,GAAG,IAAI,IAAI,CAAC9H,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC8H,GAAG;MAChF;MACA,IAAI,IAAI,CAACygB,sBAAsB,CAAC1U,YAAY,CAAC,EAAE;QAC3CA,YAAY,GAAG,IAAI,CAACoV,gBAAgB,CAACpV,YAAY,EAAE,IAAI,CAACsV,oBAAoB,CAAC7N,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5F;MACA,IAAIzH,YAAY,KAAK,IAAI,CAAC7X,IAAI,EAAE;QAC5B,IAAI,CAAC8W,WAAW,CAACiO,IAAI,CAAClN,YAAY,CAAC;MACvC;IACJ;EACJ;EACAuV,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACxV,YAAY,GAAG,IAAI,CAAC7X,IAAI;EACjC;EACAstB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAACxV,YAAY,KAAK,IAAI,CAAC7X,IAAI,EAAE;MACjC,IAAI,CAAC6sB,iBAAiB,CAAC,CAAC,IAAI,CAAC7sB,IAAI,CAAC;IACtC;EACJ;EACAutB,aAAaA,CAACxb,KAAK,EAAE;IACjB,IAAI,CAAC/R,IAAI,GAAG,CAAC,IAAI,CAACssB,UAAU,CAACrX,SAAS,CAAClD,KAAK,EAAE,IAAI,CAACxR,QAAQ,CAAC;EAChE;EACAssB,iBAAiBA,CAAC9a,KAAK,EAAE;IACrB,IAAI,CAAC9E,KAAK,CAAC8E,KAAK,CAAC,EAAE;MACf,IAAI,CAAC/R,IAAI,GAAG+R,KAAK;MACjB,IAAI,IAAI,CAAC/R,IAAI,GAAG,IAAI,CAAC8L,GAAG,EAAE;QACtB,MAAM0hB,UAAU,GAAG5X,MAAM,CAAC7D,KAAK,CAAC;QAChC,IAAI,CAAC/R,IAAI,GAAG,CAACwtB,UAAU,CAACA,UAAU,CAAC5pB,MAAM,GAAG,CAAC,CAAC;MAClD;MACA,IAAI,IAAI,CAAC5D,IAAI,GAAG,IAAI,CAAC6L,GAAG,EAAE;QACtB,IAAI,CAAC7L,IAAI,GAAG,IAAI,CAAC6L,GAAG;MACxB;MACA,IAAI,CAACiL,WAAW,CAACiO,IAAI,CAAC,IAAI,CAAC/kB,IAAI,CAAC;IACpC;EACJ;EACAusB,sBAAsBA,CAACvsB,IAAI,EAAE;IACzB,OAAO,IAAI,CAACiX,QAAQ,CAACC,IAAI,CAAEzT,QAAQ,IAAKA,QAAQ,CAACzD,IAAI,KAAKA,IAAI,CAAC,CAACY,QAAQ;EAC5E;EACAssB,oBAAoBA,CAACO,KAAK,EAAE;IACxB,MAAMC,cAAc,GAAG,IAAI,CAACzW,QAAQ;IACpC,MAAM0W,QAAQ,GAAGD,cAAc,CAAC9pB,MAAM;IACtC,KAAK,IAAIyQ,CAAC,GAAGoZ,KAAK,GAAG,CAAC,EAAEpZ,CAAC,GAAGsZ,QAAQ,EAAEtZ,CAAC,EAAE,EAAE;MACvC,MAAMrU,IAAI,GAAG0tB,cAAc,CAACrZ,CAAC,CAAC;MAC9B,IAAI,CAACrU,IAAI,CAACY,QAAQ,EAAE;QAChB,OAAOZ,IAAI,CAACA,IAAI;MACpB;IACJ;EACJ;EACAmtB,oBAAoBA,CAACM,KAAK,EAAE;IACxB,KAAK,IAAIpZ,CAAC,GAAGoZ,KAAK,EAAEpZ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7B,MAAMrU,IAAI,GAAG,IAAI,CAACiX,QAAQ,CAAC5C,CAAC,CAAC;MAC7B,IAAI,CAACrU,IAAI,CAACY,QAAQ,EAAE;QAChB,OAAOZ,IAAI,CAACA,IAAI;MACpB;IACJ;EACJ;EACAitB,gBAAgBA,CAACvY,WAAW,EAAEwW,EAAE,EAAE;IAC9B,MAAM0C,gBAAgB,GAAG,IAAI,CAAC3W,QAAQ,CAAC4W,SAAS,CAAC7tB,IAAI,IAAIA,IAAI,CAACA,IAAI,KAAK0U,WAAW,CAAC;IACnF,MAAMmL,aAAa,GAAGqL,EAAE,CAAC0C,gBAAgB,CAAC;IAC1C,OAAO/N,aAAa,IAAI,IAAI,GAAGA,aAAa,GAAG,IAAI,CAAC7f,IAAI;EAC5D;EACAwsB,gBAAgBA,CAAA,EAAG;IACf,MAAM3M,aAAa,GAAG,IAAI,CAAC5I,QAAQ,CAACC,IAAI,CAACnI,CAAC,IAAI,CAACA,CAAC,CAACnO,QAAQ,CAAC;IAC1D,IAAIif,aAAa,IAAI,IAAI,EAAE;MACvB,IAAI,CAAC7f,IAAI,GAAG6f,aAAa,CAAC7f,IAAI;MAC9B,IAAI,CAAC8W,WAAW,CAACiO,IAAI,CAAC,IAAI,CAAC/kB,IAAI,CAAC;IACpC;EACJ;AACJ;AACAqsB,iCAAiC,CAACxd,IAAI,YAAAif,0CAAA/e,CAAA;EAAA,YAAAA,CAAA,IAAyFsd,iCAAiC,EA7uDhD9wB,EAAE,CAAA4W,iBAAA,CA6uDgEwD,cAAc;AAAA,CAA4C;AAC5O0W,iCAAiC,CAACpZ,IAAI,kBA9uD0E1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EA8uDC8c,iCAAiC;EAAA7Z,SAAA;EAAAK,MAAA;IAAA7S,IAAA;IAAA6L,GAAA;IAAAC,GAAA;IAAAiiB,WAAA;IAAAxtB,QAAA;IAAAK,QAAA;IAAAqW,QAAA;IAAA+V,aAAA;IAAAhpB,UAAA;EAAA;EAAA2U,OAAA;IAAA7B,WAAA;EAAA;EAAAN,QAAA,GA9uDpCjb,EAAE,CAAAqd,kBAAA,CA8uD2U,CAACjD,cAAc,CAAC,GA9uD7Vpa,EAAE,CAAAkb,oBAAA;EAAApD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAwa,2CAAA/vB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1C,EAAE,CAAA8C,cAAA,YA8uD4e,CAAC,cAAD,CAAC;MA9uD/e9C,EAAE,CAAAmE,UAAA,2BAAAuuB,0EAAAruB,MAAA;QAAA,OA8uDmuB1B,GAAA,CAAAqvB,aAAA,CAAA3tB,MAAoB,CAAC;MAAA,CAAC,CAAC,qBAAAsuB,oEAAAtuB,MAAA;QAAA,OAAuG1B,GAAA,CAAAiD,SAAA,CAAAvB,MAAgB,CAAC;MAAA,CAAzH,CAAC,sBAAAuuB,qEAAAvuB,MAAA;QAAA,OAAoJ1B,GAAA,CAAAuuB,UAAA,CAAA7sB,MAAiB,CAAC;MAAA,CAAvK,CAAC,mBAAAwuB,kEAAA;QAAA,OAA+LlwB,GAAA,CAAAkvB,OAAA,CAAQ,CAAC;MAAA,CAAzM,CAAC,kBAAAiB,iEAAA;QAAA,OAAgOnwB,GAAA,CAAAovB,MAAA,CAAO,CAAC;MAAA,CAAzO,CAAC;MA9uD5vB/xB,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAgD,YAAA,CA8uDo+B,CAAC;MA9uDv+BhD,EAAE,CAAA8C,cAAA,YA8uDwhC,CAAC,aAAD,CAAC;MA9uD3hC9C,EAAE,CAAAmE,UAAA,mBAAA4uB,iEAAA;QAAA,OA8uD+mCpwB,GAAA,CAAA4uB,QAAA,CAAS,CAAC;MAAA,CAAC,CAAC;MA9uD7nCvxB,EAAE,CAAAoG,MAAA,cA8uDkqC,CAAC;MA9uDrqCpG,EAAE,CAAAgD,YAAA,CA8uDyqC,CAAC;MA9uD5qChD,EAAE,CAAA8C,cAAA,aA8uDwwC,CAAC;MA9uD3wC9C,EAAE,CAAAmE,UAAA,mBAAA6uB,iEAAA;QAAA,OA8uD4vCrwB,GAAA,CAAA6uB,QAAA,CAAS,CAAC;MAAA,CAAC,CAAC;MA9uD1wCxxB,EAAE,CAAAoG,MAAA,cA8uD+yC,CAAC;MA9uDlzCpG,EAAE,CAAAgD,YAAA,CA8uDszC,CAAC,CAAD,CAAC,CAAD,CAAC;IAAA;IAAA,IAAAN,EAAA;MA9uDzzC1C,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAAkG,IAAA,EAAAzI,GAAA,CAAAmvB,SAAA,CA8uD2e,CAAC;MA9uD9e9xB,EAAE,CAAAuD,SAAA,EA8uDmsB,CAAC;MA9uDtsBvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAwH,WAAA,OAAFxH,EAAE,CAAAoF,WAAA,OAAAzC,GAAA,CAAA8B,IAAA,EAAA9B,GAAA,CAAAqC,QAAA,GAAArC,GAAA,CAAAqC,QAAA,OA8uDmsB,CAAC,gBAAArC,GAAA,CAAA6vB,WAAD,CAAC,aAAA7vB,GAAA,CAAA0C,QAAD,CAAC;IAAA;EAAA;EAAAgT,YAAA,GAAo7CrX,EAAE,CAACwc,OAAO,EAAiEpb,EAAE,CAACmb,oBAAoB,EAAsOnb,EAAE,CAAC6wB,kBAAkB,EAA2H7wB,EAAE,CAACqb,eAAe,EAAmErb,EAAE,CAACsb,OAAO,EAA8MjE,iBAAiB,EAAgBW,cAAc;EAAAwD,MAAA;EAAA4C,eAAA;AAAA,EAAyD;AACvlG;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KA/uDgH9T,EAAE,CAAA+T,iBAAA,CA+uDtB+c,iCAAiC,EAAc,CAAC;IAChI9c,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,6BAA6B;MACvCkB,WAAW,EAAE,8CAA8C;MAC3DoF,SAAS,EAAE,CAAC,8CAA8C,CAAC;MAC3D2C,eAAe,EAAE3f,uBAAuB,CAAC4f,MAAM;MAC/C3C,SAAS,EAAE,CAAC1D,cAAc;IAC9B,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpG,IAAI,EAAEoG;IAAe,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3V,IAAI,EAAE,CAAC;MACzFuP,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkQ,GAAG,EAAE,CAAC;MACN0D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmQ,GAAG,EAAE,CAAC;MACNyD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEoyB,WAAW,EAAE,CAAC;MACdxe,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE4E,QAAQ,EAAE,CAAC;MACXgP,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiF,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsb,QAAQ,EAAE,CAAC;MACX1H,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqxB,aAAa,EAAE,CAAC;MAChBzd,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmb,WAAW,EAAE,CAAC;MACdvH,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,SAASywB,UAAUA,CAAClY,WAAW,EAAE4E,QAAQ,EAAE;EACvC,MAAMC,QAAQ,GAAG,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC;EACpC,IAAIC,QAAQ,EAAE;IACV,MAAMvZ,IAAI,GAAG0U,WAAW,GAAG4E,QAAQ;IACnC,OAAO,CAACtZ,IAAI;EAChB;AACJ;AAEA,MAAMyuB,oCAAoC,CAAC;EACvCnhB,WAAWA,CAACvD,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC2kB,cAAc,GAAG,IAAIzyB,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC+K,MAAM,GAAGwB,UAAU;IACxB,IAAI,CAACnB,SAAS,GAAG3J,IAAI,CAAC2J,SAAS,CAAC;MAAE0C,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;EAC5D;EACA,IAAIvE,cAAcA,CAACwB,MAAM,EAAE;IACvB,IAAIA,MAAM,EAAE;MACR,MAAM2nB,OAAO,GAAG,CAACnmB,UAAU,CAACvB,EAAE,EAAEuB,UAAU,CAACrB,EAAE,CAAC;MAC9C,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,SAAS,CAACsnB,OAAO,CAACC,OAAO,CAAC5nB,MAAM,CAAC,CAAC;IAClE;EACJ;EACA+hB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACnoB,QAAQ,EAAE;MAChB,IAAI,CAACiuB,QAAQ,GAAG,IAAI;IACxB;EACJ;EACA9nB,MAAMA,CAACC,MAAM,EAAE;IACX,IAAI,CAAC0nB,cAAc,CAAChhB,IAAI,CAAC1G,MAAM,CAAC;IAChC,IAAI,CAAC6nB,QAAQ,GAAG,KAAK;EACzB;EACAnnB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACmnB,QAAQ,GAAG,KAAK;EACzB;AACJ;AACAJ,oCAAoC,CAAC5f,IAAI,YAAAigB,6CAAA/f,CAAA;EAAA,YAAAA,CAAA,IAAyF0f,oCAAoC,EA/yDtDlzB,EAAE,CAAA4W,iBAAA,CA+yDsE1C,WAAW;AAAA,CAA4C;AAC/Ogf,oCAAoC,CAACxb,IAAI,kBAhzDuE1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAgzDIkf,oCAAoC;EAAAjc,SAAA;EAAAK,MAAA;IAAAgc,QAAA;IAAAjuB,QAAA;IAAA4E,cAAA;EAAA;EAAAmT,OAAA;IAAA+V,cAAA;EAAA;EAAArb,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAub,8CAAA9wB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAhzD1C1C,EAAE,CAAA8C,cAAA,YAgzD6Q,CAAC,YAAD,CAAC,eAAD,CAAC;MAhzDhR9C,EAAE,CAAAmE,UAAA,mBAAAsvB,sEAAA;QAAA,OAgzDogB9wB,GAAA,CAAA6qB,IAAA,CAAK,CAAC;MAAA,CAAC,CAAC;MAhzD9gBxtB,EAAE,CAAA8C,cAAA,UAgzDgiB,CAAC;MAhzDniB9C,EAAE,CAAAoG,MAAA,EAgzDmjB,CAAC;MAhzDtjBpG,EAAE,CAAAgD,YAAA,CAgzD0jB,CAAC;MAhzD7jBhD,EAAE,CAAA8C,cAAA,aAgzD8mB,CAAC;MAhzDjnB9C,EAAE,CAAAoG,MAAA,YAgzDqnB,CAAC;MAhzDxnBpG,EAAE,CAAAgD,YAAA,CAgzD4nB,CAAC,CAAD,CAAC,CAAD,CAAC;MAhzD/nBhD,EAAE,CAAA+C,UAAA,IAAAuI,kDAAA,gBAgzDg5C,CAAC;MAhzDn5CtL,EAAE,CAAAgD,YAAA,CAgzDw5C,CAAC;MAhzD35ChD,EAAE,CAAA+C,UAAA,IAAAiJ,mDAAA,gBAgzDs+C,CAAC;IAAA;IAAA,IAAAtJ,EAAA;MAhzDz+C1C,EAAE,CAAAuD,SAAA,EAgzDsc,CAAC;MAhzDzcvD,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,IAAAkH,IAAA,EAAAzJ,GAAA,CAAA0C,QAAA,CAgzDsc,CAAC;MAhzDzcrF,EAAE,CAAAuD,SAAA,EAgzDmjB,CAAC;MAhzDtjBvD,EAAE,CAAA+L,iBAAA,CAAApJ,GAAA,CAAAkJ,eAgzDmjB,CAAC;MAhzDtjB7L,EAAE,CAAAuD,SAAA,EAgzD2tB,CAAC;MAhzD9tBvD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAA2wB,QAgzD2tB,CAAC;MAhzD9tBtzB,EAAE,CAAAuD,SAAA,EAgzD69C,CAAC;MAhzDh+CvD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAA2wB,QAgzD69C,CAAC;IAAA;EAAA;EAAAjb,YAAA,GAAyrCrX,EAAE,CAACwc,OAAO,EAAiExc,EAAE,CAACsX,IAAI,EAA0EiC,kBAAkB;EAAAqD,MAAA;EAAAkB,IAAA;IAAAC,SAAA,EAAqF,CACpgGld,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;MAAE2X,SAAS,EAAE,UAAU;MAAEsF,OAAO,EAAE;IAAE,CAAC,CAAC,EAC5Chd,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;MAAE2X,SAAS,EAAE,UAAU;MAAEsF,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CAC7D,CAAC,EACFld,UAAU,CAAC,QAAQ,EAAE,CACjBE,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;MAAE2X,SAAS,EAAE,UAAU;MAAEsF,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CAC7D,CAAC,CACL,CAAC;EACL;EAAAwB,eAAA;AAAA,EAAuD;AAC5D;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KA3zDgH9T,EAAE,CAAA+T,iBAAA,CA2zDtBmf,oCAAoC,EAAc,CAAC;IACnIlf,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,gCAAgC;MAC1CkB,WAAW,EAAE,+CAA+C;MAC5DoF,SAAS,EAAE,CAAC,iDAAiD,CAAC;MAC9D2C,eAAe,EAAE3f,uBAAuB,CAAC4f,MAAM;MAC/CxB,UAAU,EAAE,CACRpd,OAAO,CAAC,YAAY,EAAE,CAClBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;QAAE2X,SAAS,EAAE,UAAU;QAAEsF,OAAO,EAAE;MAAE,CAAC,CAAC,EAC5Chd,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;QAAE2X,SAAS,EAAE,UAAU;QAAEsF,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAC7D,CAAC,EACFld,UAAU,CAAC,QAAQ,EAAE,CACjBE,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;QAAE2X,SAAS,EAAE,UAAU;QAAEsF,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAC7D,CAAC,CACL,CAAC;IAEV,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhL,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QAC9DnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEof,QAAQ,EAAE,CAAC;MACvCtf,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEiF,QAAQ,EAAE,CAAC;MACX2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE6J,cAAc,EAAE,CAAC;MACjB+J,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAE+yB,cAAc,EAAE,CAAC;MACjBnf,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8yB,2BAA2B,CAAC;EAC9B3hB,WAAWA,CAACuW,iBAAiB,EAAE9Z,MAAM,EAAE;IACnC,IAAI,CAAC8Z,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC9Z,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACmlB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC5uB,QAAQ,GAAGgI,QAAQ;IACxB,IAAI,CAACJ,WAAW,GAAG,OAAO;IAC1B,IAAI,CAAC2O,WAAW,GAAG,IAAI7a,YAAY,CAAC,CAAC;IACrC,IAAI,CAACwsB,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC2G,YAAY,GAAG,IAAIzyB,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC0yB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACjF,QAAQ,GAAG,MAAM,CACtB,CAAC;EACL;EACA,IAAInlB,MAAMA,CAAC8M,KAAK,EAAE;IACd,IAAI,CAAC0W,OAAO,GAAG1W,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;IACrC,IAAI,CAACmd,OAAO,GAAG,IAAI,CAACzG,OAAO,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAAC0G,OAAO,GAAG,IAAI,CAAC1G,OAAO,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;IAC5C,IAAI,CAACrM,SAAS,GAAGnI,mBAAmB,CAACxF,QAAQ,CAAC,IAAI,CAACga,OAAO,CAAC;IAC3D,MAAM4B,oBAAoB,GAAGtY,KAAK,IAAK,IAAI,CAACuY,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,IAAI,CAAC7B,OAAQ;IACnG,IAAI4B,oBAAoB,EAAE;MACtB,IAAI,CAAClqB,UAAU,CAAC,IAAI,CAACmvB,cAAc,CAAC;IACxC;IACA,IAAI,CAAChF,cAAc,GAAG,IAAI,CAAC7B,OAAO;EACtC;EACA,IAAIxjB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwjB,OAAO;EACvB;EACA,IAAI5c,GAAGA,CAACkG,KAAK,EAAE;IACX,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC0Y,IAAI,GAAG9gB,WAAW,CAACC,SAAS,CAACmI,KAAK,EAAE;QAAEhI,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE9E,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MACtF;IACJ;IACA,IAAI,CAACwlB,IAAI,GAAG1Y,KAAK;EACrB;EACA,IAAIlG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC4e,IAAI;EACpB;EACA,IAAI3e,GAAGA,CAACiG,KAAK,EAAE;IACX,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC2Y,IAAI,GAAG/gB,WAAW,CAACC,SAAS,CAACmI,KAAK,EAAE;QAAEhI,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE9E,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MACtF;IACJ;IACA,IAAI,CAACylB,IAAI,GAAG3Y,KAAK;EACrB;EACA,IAAIjG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC4e,IAAI;EACpB;EACA,IAAItc,WAAWA,CAAC8Q,GAAG,EAAE;IACjB,IAAI,CAAC6E,YAAY,GAAG7E,GAAG;IACvB,IAAI,CAACqQ,aAAa,GAAG,CAAC,CAACrQ,GAAG;EAC9B;EACA,IAAI9Q,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC2V,YAAY;EAC5B;EACA,IAAI/f,UAAUA,CAAC4Q,GAAG,EAAE;IAChB,IAAIA,GAAG,IAAI,IAAI,EAAE;MACb;IACJ;IACAA,GAAG,GAAG0J,IAAI,CAACqK,KAAK,CAAC/T,GAAG,CAAC;IACrB,IAAI,CAACgU,WAAW,GAAGhU,GAAG,IAAI,EAAE,GAAGA,GAAG,GAAG,CAAC;EAC1C;EACA,IAAI5Q,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC4kB,WAAW;EAC3B;EACAzR,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqY,QAAQ,CAAC,IAAI,CAACphB,WAAW,CAAC;IAC/B,IAAI,CAACgO,SAAS,GAAGnI,mBAAmB,CAACxF,QAAQ,CAAC,IAAI,CAACga,OAAO,CAAC;IAC3D,IAAI,CAACvG,WAAW,GAAGjO,mBAAmB,CAACtF,UAAU,CAAC,CAAC;IACnD,IAAI,CAAC8gB,cAAc,GAAG,CAAC,EAAE,IAAI,CAAC5jB,GAAG,IAAI,IAAI,CAACC,GAAG,CAAC;IAC9C,IAAI,CAAC4jB,KAAK,GAAG,IAAI,CAAC7L,iBAAiB,CAAC/e,YAAY,CAACgL,IAAI,CAAChT,GAAG,CAAE6yB,SAAS,IAAK,IAAI,CAAC7qB,YAAY,GAAG6qB,SAAS,CAAC3vB,IAAI,CAAC,EAAE7C,GAAG,CAAC,IAAI,CAACyyB,sBAAsB,CAACtQ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAExiB,GAAG,CAAC,MAAM,IAAI,CAAC2yB,cAAc,IAAI,IAAI,CAACI,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC3N,IAAI,CAACC,OAAO,GAAG,IAAI,CAACjM,iBAAiB,CAAC7d,cAAc,CAAC8J,IAAI,CAAC3S,GAAG,CAAC,IAAI,CAACyyB,sBAAsB,CAACtQ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAExiB,GAAG,CAAC,MAAM,IAAI,CAACuyB,iBAAiB,GAAG,KAAK,CAAC,CAAC;IACjJ,IAAI,IAAI,CAACpqB,MAAM,KAAK,EAAE,EAAE;MACpB,IAAI,CAAC4e,iBAAiB,CAACre,cAAc,CAACsK,IAAI,CAAC9S,oBAAoB,CAAC,CAAC,EAAEF,GAAG,CAAEkK,MAAM,IAAK,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAC,EAAElK,GAAG,CAACkK,MAAM,IAAI,IAAI,CAACe,sBAAsB,GAAG,IAAI,CAACgoB,gBAAgB,CAAC/oB,MAAM,CAAC,CAAC,EAAE9J,SAAS,CAAC,IAAI,CAACkyB,YAAY,CAAC,CAAC,CAACzX,SAAS,CAAC,MAAM,IAAI,CAAC8X,cAAc,IAAI,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC;IAC1R,CAAC,MACI,IAAI,IAAI,CAACP,cAAc,EAAE;MAC1B,IAAI,CAACO,mBAAmB,CAAC,CAAC;IAC9B;EACJ;EACAhF,UAAUA,CAAC9L,GAAG,EAAE;IACZ,IAAIA,GAAG,EAAE;MACL,IAAI,CAACsQ,QAAQ,CAACtQ,GAAG,CAAC;IACtB,CAAC,MACI;MACD,IAAI,CAACtQ,SAAS,CAAC,CAAC;IACpB;EACJ;EACAuc,iBAAiBA,CAACD,EAAE,EAAE,CACtB;EACAD,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACd,QAAQ,GAAGc,EAAE;EACtB;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACzqB,QAAQ,GAAGyqB,UAAU;EAC9B;EACAtQ,UAAUA,CAAClS,IAAI,EAAE;IACb,IAAI,CAACgb,iBAAiB,CAAChb,IAAI,GAAG,IAAI,CAACuT,SAAS,CAAClF,IAAI,CAAC+Y,CAAC,IAAIA,CAAC,CAACjwB,IAAI,KAAK6I,IAAI,CAAC;IACvE,IAAI,CAAC4jB,UAAU,CAAC,CAAC;EACrB;EACAzR,YAAYA,CAAC9O,MAAM,EAAE;IACjB,IAAI,CAAC2X,iBAAiB,CAAC3X,MAAM,GAAG,IAAI,CAACgW,WAAW,CAAChL,IAAI,CAACgZ,CAAC,IAAIA,CAAC,CAAClwB,IAAI,KAAKkM,MAAM,CAAC;IAC7E,IAAI,CAACugB,UAAU,CAAC,CAAC;EACrB;EACA3kB,YAAYA,CAACd,MAAM,EAAE;IACjB,IAAI,CAAC6c,iBAAiB,CAAC7c,MAAM,GAAGA,MAAM;IACtC,IAAI,CAACylB,UAAU,CAAC,CAAC;EACrB;EACA0D,SAASA,CAACnwB,IAAI,EAAE;IACZ,IAAI,CAACG,UAAU,CAACH,IAAI,CAAC;IACrB,IAAI,CAACowB,mBAAmB,CAACpwB,IAAI,CAAC;EAClC;EACAsW,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8Y,YAAY,CAAC1hB,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC0hB,YAAY,CAACtK,QAAQ,CAAC,CAAC;EAChC;EACA2H,UAAUA,CAAA,EAAG;IACT,MAAMzsB,IAAI,GAAG,IAAI,CAAC6jB,iBAAiB,CAAC5V,WAAW,CAAC,IAAI,CAAChJ,MAAM,CAAC;IAC5D,IAAI,CAACqqB,cAAc,GAAGtvB,IAAI;IAC1B,IAAI,CAACowB,mBAAmB,CAACpwB,IAAI,CAAC;EAClC;EACA4O,SAASA,CAAA,EAAG;IACR,IAAI,CAACiV,iBAAiB,CAAChb,IAAI,GAAG;MAAEhG,KAAK,EAAE,CAAC;MAAE7C,IAAI,EAAE;IAAK,CAAC;IACtD,IAAI,CAAC6jB,iBAAiB,CAAC3X,MAAM,GAAG;MAAErJ,KAAK,EAAE,CAAC;MAAE7C,IAAI,EAAE;IAAK,CAAC;EAC5D;EACAowB,mBAAmBA,CAACpwB,IAAI,EAAE;IACtB,MAAMqwB,SAAS,GAAG1mB,WAAW,CAAC4B,kBAAkB,CAACvL,IAAI,EAAE;MAAEiF,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE8E,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC;IACpG,IAAI,CAACqgB,QAAQ,CAACiG,SAAS,CAAC;IACxB,IAAI,CAACvZ,WAAW,CAACiO,IAAI,CAACsL,SAAS,CAAC;EACpC;EACAT,sBAAsBA,CAACU,aAAa,EAAE;IAClC,IAAI,CAAC,IAAI,CAACf,aAAa,IAAI,IAAI,CAACF,iBAAiB,EAAE;MAC/C,OAAOxkB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwlB,aAAa,CAAC,EAAE;QAAEtwB,IAAI,EAAE;MAAK,CAAC,CAAC;IAC1E;IACA,OAAOswB,aAAa;EACxB;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACnU,SAAS,GAAGnI,mBAAmB,CAACM,YAAY,CAAC,IAAI,CAAC6H,SAAS,EAAE;MAC9DvQ,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,GAAG,EAAE,IAAI,CAACA,GAAG;MACb7G,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB+B,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;EACA6oB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC3N,WAAW,GAAGjO,mBAAmB,CAACc,cAAc,CAAC,IAAI,CAACmN,WAAW,EAAE,IAAI,CAACpd,YAAY,EAAE;MACvF+G,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,GAAG,EAAE,IAAI,CAACA,GAAG;MACb7G,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB+B,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;EACAgpB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACO,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACzrB,YAAY,EAAE;MACnB,IAAI,CAAC+qB,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA1vB,UAAUA,CAACH,IAAI,EAAE;IACb,IAAIA,IAAI,EAAE;MACN,MAAMwwB,aAAa,GAAG7mB,WAAW,CAACY,UAAU,CAACvK,IAAI,EAAE;QAAE+J,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE9E,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MAChG,IAAI,CAAC4e,iBAAiB,CAAChW,yBAAyB,CAAC2iB,aAAa,EAAE,IAAI,CAAC3kB,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAAC7G,MAAM,CAAC;MAChG,IAAI,CAACqqB,cAAc,GAAGkB,aAAa;IACvC;EACJ;EACAhB,QAAQA,CAACxvB,IAAI,EAAE;IACX,MAAMywB,sBAAsB,GAAG9mB,WAAW,CACrCiC,eAAe,CAAC5L,IAAI,EAAE,IAAI,CAAC6L,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC7G,MAAM,CAAC;IAC5E,IAAI,CAACwrB,sBAAsB,EAAE;MACzB,IAAI,IAAI,CAAC5kB,GAAG,EAAE;QACV,IAAI,CAAC1L,UAAU,CAACwJ,WAAW,CAAC+C,oBAAoB,CAAC,IAAI,CAACb,GAAG,EAAE,IAAI,CAAC5G,MAAM,CAAC,CAAC;QACxE;MACJ;MACA,IAAI,IAAI,CAAC6G,GAAG,EAAE;QACV,IAAI,CAAC3L,UAAU,CAACwJ,WAAW,CAAC+C,oBAAoB,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAAC7G,MAAM,CAAC,CAAC;QACxE;MACJ;IACJ;IACA,IAAI,CAAC9E,UAAU,CAACH,IAAI,CAAC;EACzB;EACA+vB,gBAAgBA,CAAC/oB,MAAM,EAAE;IACrB,OAAOiN,mBAAmB,CAACM,YAAY,CAACN,mBAAmB,CAACxF,QAAQ,CAAC,EAAE,CAAC,EAAE;MACtE5C,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,GAAG,EAAE,IAAI,CAACA,GAAG;MACb7G,MAAM,EAAE,EAAE;MACV+B,MAAM,EAAEA,MAAM,KAAKwB,UAAU,CAACvB,EAAE,GAAGuB,UAAU,CAACrB,EAAE,GAAGqB,UAAU,CAACvB;IAClE,CAAC,CAAC,CAAC8S,KAAK,CAAC/Z,IAAI,IAAIA,IAAI,CAACY,QAAQ,CAAC;EACnC;AACJ;AACAquB,2BAA2B,CAACpgB,IAAI,YAAA6hB,oCAAA3hB,CAAA;EAAA,YAAAA,CAAA,IAAyFkgB,2BAA2B,EAxhEpC1zB,EAAE,CAAA4W,iBAAA,CAwhEoD9E,4BAA4B,GAxhElF9R,EAAE,CAAA4W,iBAAA,CAwhE6F1C,WAAW;AAAA,CAA4C;AACtQwf,2BAA2B,CAAChc,IAAI,kBAzhEgF1X,EAAE,CAAA2X,iBAAA;EAAA3D,IAAA,EAyhEL0f,2BAA2B;EAAAzc,SAAA;EAAAK,MAAA;IAAAjS,QAAA;IAAAwH,UAAA;IAAAD,WAAA;IAAAwoB,UAAA;IAAAC,WAAA;IAAA5K,aAAA;IAAAC,cAAA;IAAAhhB,MAAA;IAAA4G,GAAA;IAAAC,GAAA;IAAAsC,WAAA;IAAApK,UAAA;EAAA;EAAA2U,OAAA;IAAA7B,WAAA;EAAA;EAAAN,QAAA,GAzhExBjb,EAAE,CAAAqd,kBAAA,CAyhE8Z,CACxgBvL,4BAA4B,EAC5B;IACI0e,OAAO,EAAEluB,iBAAiB;IAC1BmuB,WAAW,EAAEiD,2BAA2B;IACxChD,KAAK,EAAE;EACX,CAAC,CACJ;EAAA5Y,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAqd,qCAAA5yB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAhiE2G1C,EAAE,CAAA8C,cAAA,YAgiEA,CAAC,oCAAD,CAAC;MAhiEH9C,EAAE,CAAAmE,UAAA,yBAAAoxB,wFAAAlxB,MAAA;QAAA,OAgiEoY1B,GAAA,CAAA6c,UAAA,CAAAnb,MAAiB,CAAC;MAAA,CAAC,CAAC;MAhiE1ZrE,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAgD,YAAA,CAgiEsb,CAAC;MAhiEzbhD,EAAE,CAAA8C,cAAA,aAgiEugB,CAAC;MAhiE1gB9C,EAAE,CAAAoG,MAAA,OAgiEwgB,CAAC;MAhiE3gBpG,EAAE,CAAAgD,YAAA,CAgiE+gB,CAAC;MAhiElhBhD,EAAE,CAAA8C,cAAA,oCAgiEy8B,CAAC;MAhiE58B9C,EAAE,CAAAmE,UAAA,yBAAAqxB,wFAAAnxB,MAAA;QAAA,OAgiEm7B1B,GAAA,CAAA8c,YAAA,CAAApb,MAAmB,CAAC;MAAA,CAAC,CAAC;MAhiE38BrE,EAAE,CAAAiF,MAAA;MAAFjF,EAAE,CAAAgD,YAAA,CAgiEu+B,CAAC;MAhiE1+BhD,EAAE,CAAA+C,UAAA,IAAAsJ,qEAAA,2CAgiEoxC,CAAC;MAhiEvxCrM,EAAE,CAAA+C,UAAA,IAAA4J,qEAAA,2CAgiEywD,CAAC;MAhiE5wD3M,EAAE,CAAAgD,YAAA,CAgiEixD,CAAC;MAhiEpxDhD,EAAE,CAAA8C,cAAA,mCAgiE+kE,CAAC;MAhiEllE9C,EAAE,CAAAmE,UAAA,qBAAAsxB,gFAAApxB,MAAA;QAAA,OAgiEgjE1B,GAAA,CAAAiyB,SAAA,CAAAvwB,MAAgB,CAAC;MAAA,CAAC,CAAC;MAhiErkErE,EAAE,CAAAgD,YAAA,CAgiEymE,CAAC;MAhiE5mEhD,EAAE,CAAA+C,UAAA,KAAA+J,mDAAA,gCAAF9M,EAAE,CAAA0G,sBAgiEmuF,CAAC;IAAA;IAAA,IAAAhE,EAAA;MAAA,IAAAgzB,OAAA;MAAA,IAAAC,QAAA;MAhiEtuF31B,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAkF,eAAA,KAAA6H,IAAA,EAAApK,GAAA,CAAA0C,QAAA,CAgiED,CAAC;MAhiEFrF,EAAE,CAAAuD,SAAA,EAgiEoH,CAAC;MAhiEvHvD,EAAE,CAAAqD,UAAA,oBAgiEoH,CAAC,UAAAqyB,OAAA,GAhiEvH11B,EAAE,CAAAsJ,WAAA,QAAA3G,GAAA,CAAAwxB,KAAA,oBAAAuB,OAAA,CAAAjxB,IAgiEoH,CAAC,QAAA9B,GAAA,CAAAgxB,OAAD,CAAC,QAAAhxB,GAAA,CAAAixB,OAAD,CAAC,aAAAjxB,GAAA,CAAAqC,QAAA,CAAA4C,IAAD,CAAC,aAAAjF,GAAA,CAAA0C,QAAD,CAAC,aAAA1C,GAAA,CAAAke,SAAD,CAAC,kBAAAle,GAAA,CAAAuxB,cAAD,CAAC;MAhiEvHl0B,EAAE,CAAAuD,SAAA,EAgiEmoB,CAAC;MAhiEtoBvD,EAAE,CAAAqD,UAAA,oBAgiEmoB,CAAC,UAAAsyB,QAAA,GAhiEtoB31B,EAAE,CAAAsJ,WAAA,QAAA3G,GAAA,CAAA4xB,OAAA,oBAAAoB,QAAA,CAAAlxB,IAgiEmoB,CAAC,SAAD,CAAC,UAAD,CAAC,aAAA9B,GAAA,CAAAqC,QAAA,CAAA0D,MAAD,CAAC,aAAA/F,GAAA,CAAA0C,QAAD,CAAC,aAAA1C,GAAA,CAAAgkB,WAAD,CAAC,kBAAAhkB,GAAA,CAAAuxB,cAAD,CAAC,eAAAvxB,GAAA,CAAA8F,UAAD,CAAC;MAhiEtoBzI,EAAE,CAAAuD,SAAA,EAgiEgvC,CAAC;MAhiEnvCvD,EAAE,CAAAqD,UAAA,SAAAV,GAAA,CAAA+G,MAAA,OAgiEgvC,CAAC;MAhiEnvC1J,EAAE,CAAAuD,SAAA,EAgiEi4C,CAAC;MAhiEp4CvD,EAAE,CAAAqD,UAAA,UAAAV,GAAA,CAAA0yB,WAgiEi4C,CAAC;MAhiEp4Cr1B,EAAE,CAAAuD,SAAA,EAgiE8zD,CAAC;MAhiEj0DvD,EAAE,CAAAqD,UAAA,QAAAV,GAAA,CAAA2N,GAgiE8zD,CAAC,QAAA3N,GAAA,CAAA4N,GAAD,CAAC,UAAA5N,GAAA,CAAAyyB,UAAD,CAAC,gBAAAzyB,GAAA,CAAAoxB,cAAD,CAAC,WAAApxB,GAAA,CAAA+G,MAAD,CAAC,kBAAA/G,GAAA,CAAA8nB,aAAD,CAAC,mBAAA9nB,GAAA,CAAA+nB,cAAD,CAAC,eAAA/nB,GAAA,CAAA8F,UAAD,CAAC;IAAA;EAAA;EAAA4P,YAAA,GAAg5CyY,iCAAiC,EAAiMoC,oCAAoC,EAAyInF,oCAAoC,EAAqFrB,8BAA8B,EAA+Z1rB,EAAE,CAACwc,OAAO,EAAiExc,EAAE,CAACsX,IAAI,EAA0EuV,wCAAwC,EAA2D7sB,EAAE,CAACuX,gBAAgB,EAAgHvX,EAAE,CAAC6pB,SAAS;EAAAjN,MAAA;EAAA4C,eAAA;AAAA,EAAyD;AAC7sJ;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KAjiEgH9T,EAAE,CAAA+T,iBAAA,CAiiEtB2f,2BAA2B,EAAc,CAAC;IAC1H1f,IAAI,EAAE1T,SAAS;IACf2T,IAAI,EAAE,CAAC;MACCsD,QAAQ,EAAE,sBAAsB;MAChCkB,WAAW,EAAE,uCAAuC;MACpDoF,SAAS,EAAE,CAAC,uCAAuC,CAAC;MACpDC,SAAS,EAAE,CACPhM,4BAA4B,EAC5B;QACI0e,OAAO,EAAEluB,iBAAiB;QAC1BmuB,WAAW,EAAEiD,2BAA2B;QACxChD,KAAK,EAAE;MACX,CAAC,CACJ;MACDlQ,eAAe,EAAE3f,uBAAuB,CAAC4f;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzM,IAAI,EAAElC;IAA6B,CAAC,EAAE;MAAEkC,IAAI,EAAE3N,SAAS;MAAE8T,UAAU,EAAE,CAAC;QACtGnG,IAAI,EAAExT,MAAM;QACZyT,IAAI,EAAE,CAACC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE7O,QAAQ,EAAE,CAAC;MACvC2O,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyM,UAAU,EAAE,CAAC;MACbmH,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEwM,WAAW,EAAE,CAAC;MACdoH,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEg1B,UAAU,EAAE,CAAC;MACbphB,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEi1B,WAAW,EAAE,CAAC;MACdrhB,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqqB,aAAa,EAAE,CAAC;MAChBzW,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsqB,cAAc,EAAE,CAAC;MACjB1W,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEsJ,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEkQ,GAAG,EAAE,CAAC;MACN0D,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmQ,GAAG,EAAE,CAAC;MACNyD,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEyS,WAAW,EAAE,CAAC;MACdmB,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACbuL,IAAI,EAAE5T;IACV,CAAC,CAAC;IAAEmb,WAAW,EAAE,CAAC;MACdvH,IAAI,EAAEpT;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMg1B,2BAA2B,CAAC;EAC9B;EACA,OAAOC,OAAOA,CAACrnB,MAAM,EAAED,eAAe,GAAGH,WAAW,CAACsB,wBAAwB,EAAE;IAC3E,OAAO;MACHomB,QAAQ,EAAEF,2BAA2B;MACrC9X,SAAS,EAAE,CACP;QAAE0S,OAAO,EAAEtc,WAAW;QAAE6hB,QAAQ,EAAEvnB;MAAO,CAAC,EAC1C;QAAEgiB,OAAO,EAAErc,gBAAgB;QAAE4hB,QAAQ,EAAExnB;MAAgB,CAAC;IAEhE,CAAC;EACL;AACJ;AACAqnB,2BAA2B,CAACtiB,IAAI,YAAA0iB,oCAAAxiB,CAAA;EAAA,YAAAA,CAAA,IAAyFoiB,2BAA2B;AAAA,CAAkD;AACtMA,2BAA2B,CAACK,IAAI,kBA7lEgFj2B,EAAE,CAAAk2B,gBAAA;EAAAliB,IAAA,EA6lEQ4hB;AAA2B,EAiCtG;AAC/CA,2BAA2B,CAACO,IAAI,kBA/nEgFn2B,EAAE,CAAAo2B,gBAAA;EAAAC,OAAA,GA+nE+C,CACrJn1B,YAAY,EACZqB,WAAW,EACXC,mBAAmB,CACtB;AAAA,EAAI;AACb;EAAA,QAAAsR,SAAA,oBAAAA,SAAA,KApoEgH9T,EAAE,CAAA+T,iBAAA,CAooEtB6hB,2BAA2B,EAAc,CAAC;IAC1H5hB,IAAI,EAAEjT,QAAQ;IACdkT,IAAI,EAAE,CAAC;MACCoiB,OAAO,EAAE,CACLn1B,YAAY,EACZqB,WAAW,EACXC,mBAAmB,CACtB;MACD8zB,OAAO,EAAE,CACL5J,8BAA8B,EAC9BqB,oCAAoC,EACpC2F,2BAA2B,EAC3BjF,mBAAmB,EACnBZ,wCAAwC,EACxCnG,mCAAmC,CACtC;MACD6O,YAAY,EAAE,CACV7J,8BAA8B,EAC9B5G,yCAAyC,EACzCO,yCAAyC,EACzCK,yCAAyC,EACzC9E,kCAAkC,EAClCmM,oCAAoC,EACpC/G,oCAAoC,EACpC9H,kCAAkC,EAClC/D,yCAAyC,EACzC+C,oCAAoC,EACpC0S,iBAAiB,EACjBnC,mBAAmB,EACnBtH,gBAAgB,EAChB0G,wCAAwC,EACxCtT,kBAAkB,EAClBgH,oBAAoB,EACpBmG,mCAAmC,EACnCgM,2BAA2B,EAC3B5C,iCAAiC,EACjCoC,oCAAoC,EACpCzZ,iBAAiB,EACjBW,cAAc,EACd6G,cAAc,EACdE,gBAAgB,EAChBkH,uCAAuC,EACvC7Q,qCAAqC,EACrC1C,sBAAsB;IAE9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASX,gBAAgB,EAAEuY,8BAA8B,EAAEkJ,2BAA2B,EAAElO,mCAAmC,EAAEqG,oCAAoC,EAAEF,wCAAwC,EAAE6F,2BAA2B,EAAExf,WAAW,EAAEua,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}