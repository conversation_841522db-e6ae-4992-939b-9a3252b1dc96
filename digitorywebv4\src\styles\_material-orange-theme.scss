// Global Angular Material Orange Theme & Compact Sizing Override
// This file provides consistent orange theming and compact sizing for all Material components

// ===== FORM FIELDS =====
// Global compact sizing for all Material form fields
.mat-mdc-form-field {
  // Reduce overall form field height
  .mat-mdc-text-field-wrapper {
    height: 36px !important;
    min-height: 36px !important;

    .mat-mdc-form-field-infix {
      padding: 6px 12px !important;
      min-height: 24px !important;
      border-top: none !important;
    }

    .mat-mdc-form-field-flex {
      align-items: center !important;
      height: 36px !important;
    }
  }

  // Remove subscript wrapper to save space
  .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }

  // Orange theme for outlines
  .mat-mdc-form-field-outline {
    color: #dee2e6 !important;
  }

  .mat-mdc-form-field-outline-thick {
    color: #ff6b35 !important;
  }

  // Orange theme for labels
  .mat-mdc-form-field-label {
    color: #666 !important;
    font-size: 13px !important;
    top: 18px !important; // Adjust label position for smaller height
  }

  &.mat-focused {
    .mat-mdc-form-field-label {
      color: #ff6b35 !important;
    }
  }

  // Floating label adjustments
  &.mat-form-field-should-float {
    .mat-mdc-form-field-label {
      transform: translateY(-12px) scale(0.75) !important;
    }
  }
}

// ===== SELECT DROPDOWNS =====
.mat-mdc-select {
  .mat-mdc-select-trigger {
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-select-value {
    font-size: 13px !important;
    line-height: 24px !important;
  }

  .mat-mdc-select-arrow {
    color: #ff6b35 !important;
  }
}

// Select panel styling
.mat-mdc-select-panel {
  .mat-mdc-option {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 13px !important;
    padding: 0 16px !important;

    &.mat-mdc-option-active {
      background: rgba(255, 107, 53, 0.1) !important;
      color: #ff6b35 !important;
    }

    &:hover {
      background: rgba(255, 107, 53, 0.05) !important;
    }
  }
}

// ===== DATE PICKERS =====
.mat-mdc-input-element {
  font-size: 13px !important;
  height: 24px !important;
  line-height: 24px !important;
}

.mat-datepicker-toggle {
  .mat-icon {
    color: #ff6b35 !important;
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}

// Date picker panel
.mat-datepicker-content {
  .mat-calendar-header {
    background: #ff6b35 !important;
    color: white !important;
  }

  .mat-calendar-body-selected {
    background-color: #ff6b35 !important;
    color: white !important;
  }

  .mat-calendar-body-today:not(.mat-calendar-body-selected) {
    border-color: #ff6b35 !important;
  }
}

// ===== BUTTONS =====
.mat-mdc-raised-button {
  &.mat-primary {
    background-color: #ff6b35 !important;
    color: white !important;

    &:hover {
      background-color: #ff5722 !important;
    }
  }

  // Compact button sizing
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
}

.mat-mdc-outlined-button {
  &.mat-primary {
    border-color: #ff6b35 !important;
    color: #ff6b35 !important;

    &:hover {
      background-color: rgba(255, 107, 53, 0.05) !important;
    }
  }

  height: 32px !important;
  line-height: 32px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
}

  // ===== CHECKBOXES & RADIO BUTTONS =====
  .mat-mdc-checkbox {
    .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
      background-color: #ff6b35 !important;
      border-color: #ff6b35 !important;
    }
  }
  
  .mat-mdc-radio-button {
    .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
      background-color: #ff6b35 !important;
    }
    
    .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
      border-color: #ff6b35 !important;
    }
  }

  // ===== SLIDERS =====
  .mat-mdc-slider {
    .mdc-slider__track--active_fill {
      background-color: #ff6b35 !important;
    }
    
    .mdc-slider__thumb {
      background-color: #ff6b35 !important;
    }
  }

  // ===== PROGRESS BARS & SPINNERS =====
  .mat-mdc-progress-bar {
    .mdc-linear-progress__bar-inner {
      background-color: #ff6b35 !important;
    }
  }
  
  .mat-mdc-progress-spinner {
    circle {
      stroke: #ff6b35 !important;
    }
  }

  // ===== TABS =====
  .mat-mdc-tab-group {
    .mat-mdc-tab-label-active {
      color: #ff6b35 !important;
    }
    
    .mdc-tab-indicator__content--underline {
      background-color: #ff6b35 !important;
    }
  }

  // ===== CHIPS =====
  .mat-mdc-chip {
    &.mat-mdc-chip-selected {
      background-color: #ff6b35 !important;
      color: white !important;
    }
    
    height: 32px !important;
    font-size: 13px !important;
  }

  // ===== AUTOCOMPLETE =====
  .mat-mdc-autocomplete-panel {
    .mat-mdc-option {
      height: 36px !important;
      line-height: 36px !important;
      font-size: 14px !important;
      
      &.mat-mdc-option-active {
        background: rgba(255, 107, 53, 0.1) !important;
        color: #ff6b35 !important;
      }
    }
  }

  // ===== TOOLTIPS =====
  .mat-mdc-tooltip {
    background: #ff6b35 !important;
    color: white !important;
    font-size: 12px !important;
  }

// ===== SNACKBARS =====
.mat-mdc-snack-bar-container {
  &.mat-primary {
    .mdc-snackbar__surface {
      background-color: #ff6b35 !important;
      color: white !important;
    }
  }
}

// ===== ADDITIONAL COMPACT OVERRIDES =====
// Force compact sizing on all form fields globally
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px !important;
      min-height: 36px !important;
    }

    .mat-mdc-form-field-infix {
      padding: 6px 12px !important;
      min-height: 24px !important;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
    }
  }

  // Compact select dropdowns
  .mat-mdc-select-trigger {
    height: 36px !important;
  }

  // Compact date picker inputs
  .mat-mdc-input-element {
    font-size: 13px !important;
    height: 24px !important;
  }

  // Orange theme overrides
  .mat-mdc-form-field-outline-thick {
    color: #ff6b35 !important;
  }

  .mat-focused .mat-mdc-form-field-label {
    color: #ff6b35 !important;
  }

  .mat-mdc-select-arrow {
    color: #ff6b35 !important;
  }

  .mat-datepicker-toggle .mat-icon {
    color: #ff6b35 !important;
  }
}
