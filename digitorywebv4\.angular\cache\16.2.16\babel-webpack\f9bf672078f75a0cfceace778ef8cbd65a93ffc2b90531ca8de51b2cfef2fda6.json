{"ast": null, "code": "// Process *this* and _that_\n//\n'use strict';\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function emphasis(state, silent) {\n  var i,\n    scanned,\n    token,\n    start = state.pos,\n    marker = state.src.charCodeAt(start);\n  if (silent) {\n    return false;\n  }\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) {\n    return false;\n  }\n  scanned = state.scanDelims(state.pos, marker === 0x2A);\n  for (i = 0; i < scanned.length; i++) {\n    token = state.push('text', '', 0);\n    token.content = String.fromCharCode(marker);\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker: marker,\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n      // A position of the token this delimiter corresponds to.\n      //\n      token: state.tokens.length - 1,\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end: -1,\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open: scanned.can_open,\n      close: scanned.can_close\n    });\n  }\n  state.pos += scanned.length;\n  return true;\n};\nfunction postProcess(state, delimiters) {\n  var i,\n    startDelim,\n    endDelim,\n    token,\n    ch,\n    isStrong,\n    max = delimiters.length;\n  for (i = max - 1; i >= 0; i--) {\n    startDelim = delimiters[i];\n    if (startDelim.marker !== 0x5F /* _ */ && startDelim.marker !== 0x2A /* * */) {\n      continue;\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue;\n    }\n    endDelim = delimiters[startDelim.end];\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    isStrong = i > 0 && delimiters[i - 1].end === startDelim.end + 1 &&\n    // check that first two markers match and adjacent\n    delimiters[i - 1].marker === startDelim.marker && delimiters[i - 1].token === startDelim.token - 1 &&\n    // check that last two markers are adjacent (we can safely assume they match)\n    delimiters[startDelim.end + 1].token === endDelim.token + 1;\n    ch = String.fromCharCode(startDelim.marker);\n    token = state.tokens[startDelim.token];\n    token.type = isStrong ? 'strong_open' : 'em_open';\n    token.tag = isStrong ? 'strong' : 'em';\n    token.nesting = 1;\n    token.markup = isStrong ? ch + ch : ch;\n    token.content = '';\n    token = state.tokens[endDelim.token];\n    token.type = isStrong ? 'strong_close' : 'em_close';\n    token.tag = isStrong ? 'strong' : 'em';\n    token.nesting = -1;\n    token.markup = isStrong ? ch + ch : ch;\n    token.content = '';\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = '';\n      state.tokens[delimiters[startDelim.end + 1].token].content = '';\n      i--;\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function emphasis(state) {\n  var curr,\n    tokens_meta = state.tokens_meta,\n    max = state.tokens_meta.length;\n  postProcess(state, state.delimiters);\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}