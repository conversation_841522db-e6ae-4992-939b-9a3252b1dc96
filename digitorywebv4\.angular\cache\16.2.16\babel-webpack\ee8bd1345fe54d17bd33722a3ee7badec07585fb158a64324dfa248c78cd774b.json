{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/radio\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/card\";\nimport * as i18 from \"@angular/material/progress-bar\";\nimport * as i19 from \"@angular/material/tabs\";\nfunction AccountSetupComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"div\", 52)(3, \"span\", 53);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 54);\n    i0.ɵɵtext(6, \"Loading account...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"img\", 56);\n    i0.ɵɵelementStart(2, \"div\", 57)(3, \"div\", 58)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59);\n    i0.ɵɵtext(7, \"Click to change\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61);\n    i0.ɵɵtext(4, \"Click to upload logo\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 52)(2, \"span\", 63);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_error_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 64);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_6_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 82);\n    i0.ɵɵtext(1, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"Chat Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_6_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 82);\n    i0.ɵɵtext(1, \"dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"Generate Datasets\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74)(2, \"mat-tab-group\", 75);\n    i0.ɵɵlistener(\"selectedIndexChange\", function AccountSetupComponent_mat_card_120_div_6_Template_mat_tab_group_selectedIndexChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.selectedAITab = $event);\n    });\n    i0.ɵɵelementStart(3, \"mat-tab\");\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_120_div_6_ng_template_4_Template, 4, 0, \"ng-template\", 76);\n    i0.ɵɵelementStart(5, \"div\", 77);\n    i0.ɵɵelement(6, \"app-chat-bot\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-tab\");\n    i0.ɵɵtemplate(8, AccountSetupComponent_mat_card_120_div_6_ng_template_8_Template, 4, 0, \"ng-template\", 76);\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"div\", 80)(11, \"p\");\n    i0.ɵɵtext(12, \"Our AI will analyze your restaurant information to create optimized inventory and packaging datasets.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" This process takes approximately 15 minutes to complete.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_120_div_6_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.startAIProcessing());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"play_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Generate Datasets \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selectedIndex\", ctx_r10.selectedAITab);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"tenantId\", ctx_r10.registrationForm.value.tenantId)(\"tenantName\", ctx_r10.registrationForm.value.tenantName);\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r19.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"span\", 53);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_120_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_120_div_7_div_16_div_3_Template, 3, 0, \"div\", 101);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102)(6, \"div\", 103);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 104);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, step_r23.completed, ctx_r22.activeStep === i_r24, !step_r23.completed && ctx_r22.activeStep !== i_r24));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r23.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep === i_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep !== i_r24);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r23.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h3\", 85)(2, \"mat-icon\", 86);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 87);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 88);\n    i0.ɵɵelementStart(7, \"div\", 89);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 90)(10, \"mat-icon\", 91);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_120_div_7_span_12_Template, 3, 4, \"span\", 20);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_120_div_7_span_13_Template, 2, 0, \"span\", 20);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_120_div_7_span_14_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 93);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_120_div_7_div_16_Template, 10, 10, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 95)(18, \"div\", 96)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 97);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r11.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 0 && ctx_r11.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 107)(2, \"mat-icon\", 108);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 109);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 110)(9, \"div\", 111)(10, \"mat-card\", 112)(11, \"mat-card-header\")(12, \"div\", 113)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-card-title\");\n    i0.ɵɵtext(16, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-subtitle\");\n    i0.ɵɵtext(18, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_120_div_8_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.downloadInventory());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Download \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 111)(25, \"mat-card\", 112)(26, \"mat-card-header\")(27, \"div\", 115)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-card-title\");\n    i0.ɵɵtext(31, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-card-subtitle\");\n    i0.ɵɵtext(33, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card-actions\")(35, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_120_div_8_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.downloadPackaging());\n    });\n    i0.ɵɵelementStart(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Download \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117)(2, \"mat-icon\", 118);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 119);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 120)(9, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_120_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.startAIProcessing());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 65)(1, \"div\", 66)(2, \"mat-icon\", 67);\n    i0.ɵɵtext(3, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 68);\n    i0.ɵɵtext(5, \"Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_120_div_6_Template, 21, 3, \"div\", 69);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_120_div_7_Template, 25, 6, \"div\", 70);\n    i0.ɵɵtemplate(8, AccountSetupComponent_mat_card_120_div_8_Template, 39, 0, \"div\", 71);\n    i0.ɵɵtemplate(9, AccountSetupComponent_mat_card_120_div_9_Template, 13, 0, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showChatBot && !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadFailed);\n  }\n}\nconst _c1 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c2 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.selectedTabIndex = 0;\n    this.tenantCreated = false;\n    this.aiDataAvailable = false;\n    this.isLoading = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // AI Data Generation properties\n    this.showChatBot = true; // Show the tabbed interface by default\n    this.selectedAITab = 0; // 0 = Chat Agent tab, 1 = Dataset tab\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          this.isLoading = true; // Show loading spinner\n          this.cd.detectChanges();\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n              this.isLoading = false; // Hide loading spinner\n              this.cd.detectChanges();\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n              this.isLoading = false; // Hide loading spinner\n              this.cd.detectChanges();\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  onTabChange(index) {\n    this.selectedTabIndex = index;\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  // Switch to the Chat Agent tab\n  switchToChatAgent() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before using the chat agent');\n      return;\n    }\n    // Select the Chat Agent tab\n    this.selectedAITab = 0;\n    this.cd.detectChanges();\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  // Switch between Chat Agent and Dataset tabs\n  switchAITab(tabIndex) {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startAIProcessing();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 121,\n      vars: 21,\n      consts: [[\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"status-header\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\", 1, \"compact-radio\"], [\"value\", \"no\", \"color\", \"primary\", 1, \"compact-radio\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-header\"], [1, \"logo-container\"], [1, \"logo-dropzone\", 3, \"click\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [\"class\", \"logo-loading\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"spinner-container\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"visually-hidden\"], [1, \"loading-text\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-overlay\"], [1, \"overlay-content\"], [1, \"change-text\"], [1, \"logo-placeholder\"], [1, \"upload-text\"], [1, \"logo-loading\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"section-header\"], [1, \"section-icon\"], [1, \"section-title\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"chat-bot-section\"], [1, \"ai-data-tabs\"], [\"animationDuration\", \"300ms\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"mat-tab-label\", \"\"], [1, \"tab-content\"], [3, \"tenantId\", \"tenantName\"], [1, \"tab-content\", \"dataset-tab-content\"], [1, \"dataset-info\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-btn\", 3, \"click\"], [1, \"tab-icon\"], [1, \"tab-label\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"row\", \"download-options\"], [1, \"col-md-6\", \"mb-3\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r33 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, AccountSetupComponent_div_0_Template, 7, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 4)(10, \"mat-icon\", 5);\n          i0.ɵɵtext(11, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"span\", 6);\n          i0.ɵɵtext(15, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\", 7)(17, \"mat-icon\", 5);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 8)(22, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_22_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"mat-card\", 11)(28, \"mat-card-content\")(29, \"form\", 12)(30, \"h3\", 13);\n          i0.ɵɵtext(31, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 14)(33, \"div\", 15)(34, \"mat-form-field\", 16)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 17);\n          i0.ɵɵelementStart(38, \"mat-icon\", 18);\n          i0.ɵɵtext(39, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"mat-form-field\", 16)(41, \"mat-label\");\n          i0.ɵɵtext(42, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"input\", 19);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_43_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-icon\", 18);\n          i0.ɵɵtext(45, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, AccountSetupComponent_mat_error_46_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 16)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_50_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-icon\", 18);\n          i0.ɵɵtext(52, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(53, AccountSetupComponent_mat_error_53_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 15)(55, \"mat-form-field\", 16)(56, \"mat-label\");\n          i0.ɵɵtext(57, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 22);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_58_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"mat-icon\", 18);\n          i0.ɵɵtext(60, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(61, AccountSetupComponent_mat_error_61_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-form-field\", 16)(63, \"mat-label\");\n          i0.ɵɵtext(64, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 23);\n          i0.ɵɵelementStart(66, \"mat-icon\", 18);\n          i0.ɵɵtext(67, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"mat-form-field\", 16)(69, \"mat-label\");\n          i0.ɵɵtext(70, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(71, \"input\", 24);\n          i0.ɵɵelementStart(72, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_72_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(73, \"mat-icon\");\n          i0.ɵɵtext(74);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 26)(76, \"h3\", 13);\n          i0.ɵɵtext(77, \"Account Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"div\", 27)(79, \"div\", 28)(80, \"div\", 29)(81, \"h4\", 30);\n          i0.ɵɵtext(82, \"Account Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 31)(84, \"div\", 32)(85, \"label\", 33);\n          i0.ɵɵtext(86, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-radio-group\", 34)(88, \"mat-radio-button\", 35);\n          i0.ɵɵtext(89, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"mat-radio-button\", 36);\n          i0.ɵɵtext(91, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"div\", 32)(93, \"label\", 33);\n          i0.ɵɵtext(94, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-radio-group\", 37)(96, \"mat-radio-button\", 35);\n          i0.ɵɵtext(97, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-radio-button\", 36);\n          i0.ɵɵtext(99, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(100, \"div\", 32)(101, \"label\", 33);\n          i0.ɵɵtext(102, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"mat-radio-group\", 38)(104, \"mat-radio-button\", 35);\n          i0.ɵɵtext(105, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"mat-radio-button\", 36);\n          i0.ɵɵtext(107, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(108, \"div\", 39)(109, \"div\", 40)(110, \"h4\", 30);\n          i0.ɵɵtext(111, \"Company Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(112, \"div\", 41)(113, \"div\", 42);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_div_click_113_listener() {\n            i0.ɵɵrestoreView(_r33);\n            const _r7 = i0.ɵɵreference(118);\n            return i0.ɵɵresetView(_r7.click());\n          });\n          i0.ɵɵtemplate(114, AccountSetupComponent_div_114_Template, 8, 1, \"div\", 43);\n          i0.ɵɵtemplate(115, AccountSetupComponent_div_115_Template, 5, 0, \"div\", 44);\n          i0.ɵɵtemplate(116, AccountSetupComponent_div_116_Template, 4, 0, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"input\", 46, 47);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_117_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(119, AccountSetupComponent_mat_error_119_Template, 2, 0, \"mat-error\", 48);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(120, AccountSetupComponent_mat_card_120_Template, 10, 4, \"mat-card\", 49);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c2));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(39);\n          i0.ɵɵclassProp(\"has-logo\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i15.MatRadioGroup, i15.MatRadioButton, MatButtonModule, i16.MatButton, i16.MatIconButton, MatCardModule, i17.MatCard, i17.MatCardActions, i17.MatCardAvatar, i17.MatCardContent, i17.MatCardHeader, i17.MatCardSubtitle, i17.MatCardTitle, MatSelectModule, MatProgressBarModule, i18.MatProgressBar, MatTabsModule, i19.MatTabLabel, i19.MatTab, i19.MatTabGroup, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 8px;\\n  text-align: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  padding: 0;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 250px;\\n  padding-right: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  padding-left: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  border-bottom: 1px solid #eee;\\n  justify-content: flex-start;\\n  width: 100%;\\n  text-align: left;\\n  padding-bottom: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 0;\\n  color: #555;\\n  padding-bottom: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n  width: 100%;\\n  align-self: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  text-align: left;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #666;\\n  font-weight: 500;\\n  text-align: left;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label {\\n  margin: 0;\\n  padding: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-container {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-outer-circle, .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-inner-circle {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label-content {\\n  padding-left: 4px;\\n  font-size: 13px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 180px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 140px;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  position: relative;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  border: 2px dashed #ddd;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]:hover {\\n  border-color: #f8a055;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%] {\\n  border-style: solid;\\n  border-color: #f8a055;\\n  position: relative;\\n  \\n\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"Edit\\\";\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  background-color: #f8a055;\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 500;\\n  padding: 3px 8px;\\n  border-top-left-radius: 6px;\\n  z-index: 2;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]:hover {\\n  border-color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]:hover   .logo-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  background-color: rgba(0, 0, 0, 0.6);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n  padding: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0; \\n\\n  transition: all 0.2s ease;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  padding: 10px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   .change-text[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  color: #aaa;\\n  padding: 16px;\\n  text-align: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  width: 36px;\\n  height: 36px;\\n  margin-bottom: 8px;\\n  color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #666;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-loading[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-loading[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  text-align: center;\\n  margin-top: 4px;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 0;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding-left: 0;\\n    padding-right: 0;\\n    align-items: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n    align-items: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  z-index: 9999;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n}\\n@media (min-width: 992px) {\\n  .loading-overlay[_ngcontent-%COMP%] {\\n    padding-left: 120px; \\n\\n  }\\n}\\n@media (max-width: 991px) {\\n  .loading-overlay[_ngcontent-%COMP%] {\\n    padding-left: 0; \\n\\n  }\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  padding: 15px 25px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  margin-right: 10%;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-width: 0.2rem;\\n  color: #f8a055; \\n\\n}\\n\\n.loading-text[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  font-size: 0.9rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.section-icon[_ngcontent-%COMP%] {\\n  color: #555;\\n  margin-right: 10px;\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 0;\\n  overflow: hidden;\\n  padding: 0 20px 20px;\\n  background-color: white;\\n}\\n\\n.ai-data-tabs[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n  .chat-bot-section .mat-mdc-tab-header {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  margin-bottom: 20px;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab {\\n  min-width: 160px;\\n  padding: 0 16px;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab-label-container {\\n  padding: 0 16px;\\n}\\n\\n  .chat-bot-section .mdc-tab__text-label {\\n  color: #666 !important;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {\\n  color: #555 !important;\\n  font-weight: 500;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab-header .mat-mdc-tab .mdc-tab-indicator__content--underline {\\n  border-color: #555 !important;\\n}\\n\\n  .chat-bot-section .mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron {\\n  border-color: #666;\\n}\\n\\n.tab-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.tab-label[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background-color: white;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 550px;\\n}\\n\\n\\n\\n.dataset-tab-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  min-height: 550px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  text-align: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n  color: #f8a055; \\n\\n  font-size: 24px;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #f8a055; \\n\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  color: #555;\\n}\\n\\n.generate-btn[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "MatTabsModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_120_div_6_Template_mat_tab_group_selectedIndexChange_2_listener", "$event", "ɵɵrestoreView", "_r17", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "selectedAITab", "ɵɵtemplate", "AccountSetupComponent_mat_card_120_div_6_ng_template_4_Template", "AccountSetupComponent_mat_card_120_div_6_ng_template_8_Template", "AccountSetupComponent_mat_card_120_div_6_Template_button_click_17_listener", "ctx_r18", "startAIProcessing", "ctx_r10", "registrationForm", "value", "tenantId", "tenantName", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r19", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_120_div_7_div_16_div_3_Template", "AccountSetupComponent_mat_card_120_div_7_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c0", "step_r23", "completed", "ctx_r22", "activeStep", "i_r24", "ɵɵtextInterpolate", "name", "description", "AccountSetupComponent_mat_card_120_div_7_span_12_Template", "AccountSetupComponent_mat_card_120_div_7_span_13_Template", "AccountSetupComponent_mat_card_120_div_7_span_14_Template", "AccountSetupComponent_mat_card_120_div_7_div_16_Template", "ctx_r11", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_120_div_8_Template_button_click_20_listener", "_r29", "ctx_r28", "downloadInventory", "AccountSetupComponent_mat_card_120_div_8_Template_button_click_35_listener", "ctx_r30", "downloadPackaging", "AccountSetupComponent_mat_card_120_div_9_Template_button_click_9_listener", "_r32", "ctx_r31", "AccountSetupComponent_mat_card_120_div_6_Template", "AccountSetupComponent_mat_card_120_div_7_Template", "AccountSetupComponent_mat_card_120_div_8_Template", "AccountSetupComponent_mat_card_120_div_9_Template", "ctx_r9", "showChatBot", "isDownloading", "downloadComplete", "downloadFailed", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "selectedTabIndex", "tenantCreated", "aiDataAvailable", "isLoading", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "detectChanges", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "onTabChange", "index", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "switchToChatAgent", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "switchAITab", "tabIndex", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_div_0_Template", "AccountSetupComponent_Template_button_click_22_listener", "AccountSetupComponent_Template_input_keyup_43_listener", "AccountSetupComponent_mat_error_46_Template", "AccountSetupComponent_Template_input_keyup_50_listener", "AccountSetupComponent_mat_error_53_Template", "AccountSetupComponent_Template_input_keyup_58_listener", "AccountSetupComponent_mat_error_61_Template", "AccountSetupComponent_Template_button_click_72_listener", "AccountSetupComponent_Template_div_click_113_listener", "_r33", "_r7", "ɵɵreference", "click", "AccountSetupComponent_div_114_Template", "AccountSetupComponent_div_115_Template", "AccountSetupComponent_div_116_Template", "AccountSetupComponent_Template_input_change_117_listener", "AccountSetupComponent_mat_error_119_Template", "AccountSetupComponent_mat_card_120_Template", "ɵɵpureFunction0", "_c1", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵclassProp", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i15", "MatRadioGroup", "MatRadioButton", "i16", "MatButton", "MatIconButton", "i17", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i18", "MatProgressBar", "i19", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    MatTabsModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n  selectedTabIndex: number = 0;\n  tenantCreated: boolean = false;\n  aiDataAvailable: boolean = false;\n  isLoading: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // AI Data Generation properties\n  showChatBot: boolean = true; // Show the tabbed interface by default\n  selectedAITab: number = 0; // 0 = Chat Agent tab, 1 = Dataset tab\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          this.isLoading = true; // Show loading spinner\n          this.cd.detectChanges();\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n              this.isLoading = false; // Hide loading spinner\n              this.cd.detectChanges();\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n              this.isLoading = false; // Hide loading spinner\n              this.cd.detectChanges();\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  onTabChange(index: number) {\n    this.selectedTabIndex = index;\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   // Switch to the Chat Agent tab\n   switchToChatAgent(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before using the chat agent');\n      return;\n    }\n\n    // Select the Chat Agent tab\n    this.selectedAITab = 0;\n    this.cd.detectChanges();\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  // Switch between Chat Agent and Dataset tabs\n  switchAITab(tabIndex: number): void {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startAIProcessing();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<!-- Full-screen loading spinner overlay -->\n<div *ngIf=\"isLoading\" class=\"loading-overlay\">\n  <div class=\"spinner-container\">\n    <div class=\"spinner-border\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"loading-text\">Loading account...</div>\n  </div>\n</div>\n\n<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n          <h3 class=\"form-section-title\">Account Information</h3>\n          <div class=\"compact-form-grid\">\n            <!-- First row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant Name</mat-label>\n                <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                <mat-icon matSuffix>business</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant ID</mat-label>\n                <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                <mat-icon matSuffix>fingerprint</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                  Tenant ID already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Account Number</mat-label>\n                <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                <mat-icon matSuffix>account_balance</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                  Account number already exists\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Second row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>G-Sheet</mat-label>\n                <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                  (keyup)=\"checkGSheet($event)\">\n                <mat-icon matSuffix>table_chart</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                  G-Sheet number already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Email</mat-label>\n                <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                <mat-icon matSuffix>email</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Password</mat-label>\n                <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                  [type]=\"hidePassword ? 'password' : 'text'\" />\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n              </mat-form-field>\n            </div>\n\n            <!-- Settings section with two-column layout -->\n            <div class=\"settings-section\">\n              <h3 class=\"form-section-title\">Account Configuration</h3>\n              <div class=\"two-column-grid\">\n                <!-- Left column: Status options -->\n                <div class=\"left-column\">\n                  <div class=\"status-header\">\n                    <h4 class=\"section-label\">Account Status</h4>\n                  </div>\n                  <div class=\"status-options\">\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Account</label>\n                      <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Active</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Inactive</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Forecast</label>\n                      <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Sales</label>\n                      <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Right column: Logo upload -->\n                <div class=\"right-column\">\n                  <div class=\"logo-header\">\n                    <h4 class=\"section-label\">Company Logo</h4>\n                  </div>\n                  <div class=\"logo-container\">\n                    <!-- Interactive logo upload area -->\n                    <div class=\"logo-dropzone\" (click)=\"fileInput.click()\" [class.has-logo]=\"logoUrl\">\n                      <!-- Logo preview with change instruction overlay -->\n                      <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                        <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                        <div class=\"logo-overlay\">\n                          <div class=\"overlay-content\">\n                            <mat-icon>edit</mat-icon>\n                            <div class=\"change-text\">Click to change</div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Placeholder with upload instruction -->\n                      <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                        <mat-icon>cloud_upload</mat-icon>\n                        <div class=\"upload-text\">Click to upload logo</div>\n                      </div>\n\n                      <!-- Loading spinner -->\n                      <div class=\"logo-loading\" *ngIf=\"loadSpinnerForLogo\">\n                        <div class=\"spinner-border\" role=\"status\">\n                          <span class=\"sr-only\">Loading...</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Hidden file input -->\n                    <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n\n                    <!-- Error message -->\n                    <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                      Please upload a logo\n                    </mat-error>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- AI Data Download Section - Shows after tenant creation -->\n    <mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n      <div class=\"section-header\">\n        <mat-icon class=\"section-icon\">auto_awesome</mat-icon>\n        <h3 class=\"section-title\">Generate AI-Powered Datasets</h3>\n      </div>\n\n      <!-- AI Data Generation Section with Tabs -->\n      <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n        <div class=\"ai-data-tabs\">\n          <mat-tab-group [(selectedIndex)]=\"selectedAITab\" animationDuration=\"300ms\">\n            <!-- Chat Agent Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">chat</mat-icon>\n                <span class=\"tab-label\">Chat Agent</span>\n              </ng-template>\n\n              <div class=\"tab-content\">\n                <app-chat-bot [tenantId]=\"registrationForm.value.tenantId\"\n                  [tenantName]=\"registrationForm.value.tenantName\">\n                </app-chat-bot>\n              </div>\n            </mat-tab>\n\n            <!-- Dataset Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">dataset</mat-icon>\n                <span class=\"tab-label\">Generate Datasets</span>\n              </ng-template>\n\n              <div class=\"tab-content dataset-tab-content\">\n                <div class=\"dataset-info\">\n                  <p>Our AI will analyze your restaurant information to create optimized inventory and packaging datasets.</p>\n                  <p><strong>Note:</strong> This process takes approximately 15 minutes to complete.</p>\n\n                  <button mat-raised-button color=\"primary\" (click)=\"startAIProcessing()\" class=\"generate-btn\">\n                    <mat-icon>play_circle</mat-icon>\n                    Generate Datasets\n                  </button>\n                </div>\n              </div>\n            </mat-tab>\n          </mat-tab-group>\n        </div>\n      </div>\n\n      <!-- Processing state -->\n      <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n        <h3 class=\"processing-title\">\n          <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n          Processing Your Data\n        </h3>\n\n        <!-- Progress indicator -->\n        <div class=\"progress-container\">\n          <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n          <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n        </div>\n\n        <!-- Estimated time -->\n        <div class=\"estimated-time\">\n          <mat-icon class=\"icon\">access_time</mat-icon>\n          <span *ngIf=\"estimatedTimeRemaining > 60\">\n            Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n            Estimated time remaining: less than a minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n            Calculating...\n          </span>\n        </div>\n\n        <!-- Processing steps -->\n        <div class=\"processing-steps\">\n          <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n            [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n            <div class=\"step-status\">\n              <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n              <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                <span class=\"visually-hidden\">Loading...</span>\n              </div>\n              <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n            </div>\n\n            <div class=\"step-details\">\n              <div class=\"step-name\">{{step.name}}</div>\n              <div class=\"step-description\">{{step.description}}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Helpful tips section -->\n        <div class=\"tips-section\">\n          <div class=\"tip-header\">\n            <mat-icon>lightbulb</mat-icon>\n            <span>Did You Know?</span>\n          </div>\n          <div class=\"tip-content\">\n            AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n            insights\n          </div>\n        </div>\n      </div>\n\n      <!-- Download complete state -->\n      <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n        <div class=\"success-header\">\n          <mat-icon class=\"success-icon\">task_alt</mat-icon>\n          <h3>Processing Complete!</h3>\n        </div>\n\n        <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.\n        </p>\n\n        <div class=\"row download-options\">\n          <!-- Inventory Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"inventory-icon\">\n                  <mat-icon>inventory_2</mat-icon>\n                </div>\n                <mat-card-title>Inventory Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n\n          <!-- Packaging Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"packaging-icon\">\n                  <mat-icon>category</mat-icon>\n                </div>\n                <mat-card-title>Packaging Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n              </mat-card-header>\n\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n        </div>\n      </div>\n\n      <!-- Error state -->\n      <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n        <div class=\"error-header\">\n          <mat-icon class=\"error-icon\">error_outline</mat-icon>\n          <h3>Processing Failed</h3>\n        </div>\n\n        <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n          load or connection issues.</p>\n\n        <div class=\"error-actions\">\n          <button mat-raised-button color=\"warn\" (click)=\"startAIProcessing()\">\n            <mat-icon>refresh</mat-icon> Try Again\n          </button>\n        </div>\n      </div>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICvB5BC,EAAA,CAAAC,cAAA,cAA+C;IAGXD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAkDtCH,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgENH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IACxCJ,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ7CH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAUtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIrDH,EAAA,CAAAC,cAAA,cAAqD;IAE3BD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS7CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAwBhBH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAazCH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IArB1DH,EAAA,CAAAC,cAAA,cAA4G;IAEzFD,EAAA,CAAAU,UAAA,iCAAAC,+FAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAAF,OAAA,CAAAG,aAAA,GAAAN,MAAA;IAAA,EAAiC;IAE9CZ,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAmB,UAAA,IAAAC,+DAAA,0BAGc;IAEdpB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,uBAEe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAmB,UAAA,IAAAE,+DAAA,0BAGc;IAEdrB,EAAA,CAAAC,cAAA,cAA6C;IAEtCD,EAAA,CAAAE,MAAA,6GAAqG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5GH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iEAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEtFH,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAU,UAAA,mBAAAY,2EAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAAvB,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAM,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrExB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA9BFH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,kBAAAmB,OAAA,CAAAP,aAAA,CAAiC;IAS5BlB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAM,UAAA,aAAAmB,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;IA6ChE7B,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA8B,kBAAA,gCAAA9B,EAAA,CAAA+B,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACAjC,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,eAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,cACuI;IAGnID,EAAA,CAAAmB,UAAA,IAAAe,mEAAA,uBAAwD;IACxDlC,EAAA,CAAAmB,UAAA,IAAAgB,8DAAA,mBAEM;IACNnC,EAAA,CAAAmB,UAAA,IAAAiB,mEAAA,uBAAuF;IACzFpC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvH3C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAiC,QAAA,CAAAC,SAAA,CAAoB;IACzBxC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAiC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpC3C,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAiC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7B3C,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA4C,iBAAA,CAAAL,QAAA,CAAAM,IAAA,CAAa;IACN7C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA4C,iBAAA,CAAAL,QAAA,CAAAO,WAAA,CAAoB;;;;;IAzC1D9C,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAmB,UAAA,KAAA4B,yDAAA,mBAEO;IACP/C,EAAA,CAAAmB,UAAA,KAAA6B,yDAAA,mBAEO;IACPhD,EAAA,CAAAmB,UAAA,KAAA8B,yDAAA,mBAEO;IACTjD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAmB,UAAA,KAAA+B,wDAAA,oBAeM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAA6C,OAAA,CAAAC,gBAAA,CAA0B;IACnCpD,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA8B,kBAAA,KAAAqB,OAAA,CAAAC,gBAAA,eAA8B;IAMnDpD,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,MAAiC;IAGjCjC,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,QAAAkB,OAAA,CAAAlB,sBAAA,OAAgE;IAGhEjC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,OAAkC;IAOnBjC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAA6C,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5CrD,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,6FAC3B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,eAAkC;IAMdD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAA4C,2EAAA;MAAAtD,EAAA,CAAAa,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAuC,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEzD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,gBAA2B;IAITD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAgD,2EAAA;MAAA1D,EAAA,CAAAa,aAAA,CAAA0C,IAAA;MAAA,MAAAI,OAAA,GAAA3D,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAA0C,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrE5D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQnBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAAmD,0EAAA;MAAA7D,EAAA,CAAAa,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAA8C,OAAA,CAAAvC,iBAAA,EAAmB;IAAA,EAAC;IAClExB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA1KfH,EAAA,CAAAC,cAAA,mBAA2D;IAExBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI7DH,EAAA,CAAAmB,UAAA,IAAA6C,iDAAA,mBAsCM;IAGNhE,EAAA,CAAAmB,UAAA,IAAA8C,iDAAA,mBAyDM;IAGNjE,EAAA,CAAAmB,UAAA,IAAA+C,iDAAA,mBA+CM;IAGNlE,EAAA,CAAAmB,UAAA,IAAAgD,iDAAA,mBAcM;IACRnE,EAAA,CAAAG,YAAA,EAAW;;;;IAtKHH,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAA8D,MAAA,CAAAC,WAAA,KAAAD,MAAA,CAAAE,aAAA,KAAAF,MAAA,CAAAG,gBAAA,KAAAH,MAAA,CAAAI,cAAA,CAA2E;IAyC3ExE,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAA8D,MAAA,CAAAE,aAAA,CAAmB;IA4DnBtE,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAA8D,MAAA,CAAAG,gBAAA,CAAsB;IAkDtBvE,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAA8D,MAAA,CAAAI,cAAA,CAAoB;;;;;;;;;AD5ThC,MAwBaC,qBAAqB;EAyChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IAnDpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAApF,OAAO,GAAkB,IAAI;IAC7B,KAAAqF,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,SAAS,GAAY,KAAK;IAG1B,KAAA7C,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA8C,gBAAgB,GAAY,IAAI;IAChC,KAAA7B,aAAa,GAAY,KAAK;IAC9B,KAAAlB,gBAAgB,GAAW,CAAC;IAC5B,KAAAmB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA9B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAoC,WAAW,GAAY,IAAI,CAAC,CAAC;IAC7B,KAAAnD,aAAa,GAAW,CAAC,CAAC,CAAC;IAkBzB,IAAI,CAACkF,IAAI,GAAG,IAAI,CAACjB,IAAI,CAACkB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACvB,UAAU,CAACwB,WAAW,EAAE,CAAC5E,KAAK;IAEnD;IACA,IAAI,IAAI,CAAC0D,UAAU,EAAE;MACnB,IAAI,CAACmB,WAAW,GAAG,IAAI,CAACnB,UAAU,CAACoB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC9E,gBAAgB,GAAG,IAAI,CAACoD,EAAE,CAAC4B,KAAK,CAAC;MACpC9E,QAAQ,EAAE,IAAIlD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAE;MAC3D9E,UAAU,EAAE,IAAInD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAIlI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAInI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAIpI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIrI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAItI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAIvI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAIxI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC8H,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAIzI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC8H,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN;IACA,IAAI,CAACtB,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACoB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACX,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACuB,WAAW,CAAC,IAAI,CAAChC,UAAU,CAACiC,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAACzC,KAAK,CAAC0C,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAAC3B,UAAU,GAAG,IAAI,CAAC,CAAC;UACxB,IAAI,CAACI,SAAS,GAAG,IAAI,CAAC,CAAC;UACvB,IAAI,CAACd,EAAE,CAACsC,aAAa,EAAE;UAEvB;UACA,IAAI,CAACxC,GAAG,CAACyC,cAAc,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACV,WAAW,CAACQ,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAACjC,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAACgD,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAACpD,MAAM,CAACqD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;cAE9C,IAAI,CAAC/B,SAAS,GAAG,KAAK,CAAC,CAAC;cACxB,IAAI,CAACd,EAAE,CAACsC,aAAa,EAAE;YACzB,CAAC;YACDQ,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAACrC,UAAU,GAAG,KAAK,CAAC,CAAC;cACzBsC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAACnD,MAAM,CAACgD,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAACpD,MAAM,CAACqD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;cAC5C,IAAI,CAAC/B,SAAS,GAAG,KAAK,CAAC,CAAC;cACxB,IAAI,CAACd,EAAE,CAACsC,aAAa,EAAE;YACzB;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAL,WAAWA,CAACU,IAAI;IACd,IAAI,CAACrG,gBAAgB,CAAC2G,UAAU,CAAC;MAC/BxG,UAAU,EAAEkG,IAAI,CAAClG,UAAU;MAC3BD,QAAQ,EAAEmG,IAAI,CAACnG,QAAQ;MACvBgF,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,MAAM,EAAEkB,IAAI,CAAClB,MAAM;MACnBC,SAAS,EAAEiB,IAAI,CAACjB,SAAS;MACzBC,QAAQ,EAAEgB,IAAI,CAAChB,QAAQ;MACvBC,OAAO,EAAEe,IAAI,CAACO,MAAM,CAACtB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEc,IAAI,CAACO,MAAM,CAACrB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEa,IAAI,CAACO,MAAM,CAACpB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEY,IAAI,CAACQ,aAAa,EAAEpB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIY,IAAI,CAACQ,aAAa,EAAEpB,IAAI,EAAE;MAC5B,IAAI,CAAC3G,OAAO,GAAGuH,IAAI,CAACQ,aAAa,CAACpB,IAAI;MACtC,IAAI,CAACvB,aAAa,GAAG,CAAC;QACpB4C,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACpB;OACzB,CAAC;;IAEJ,IAAI,CAAC/B,EAAE,CAACsC,aAAa,EAAE;EACzB;EAEAe,KAAKA,CAAA;IACH,IAAI,CAAC7D,MAAM,CAACqD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAS,IAAIA,CAAA;IACF,IAAI,IAAI,CAAChH,gBAAgB,CAACiH,OAAO,EAAE;MACjC,IAAI,CAACjH,gBAAgB,CAACkH,gBAAgB,EAAE;MACxC,IAAI,CAAC5D,MAAM,CAACgD,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIa,MAAM,GAAG,IAAI,CAACnH,gBAAgB,CAACC,KAAK;MACxC,IAAImH,GAAG,GAAQ;QACTlH,QAAQ,EAAEiH,MAAM,CAACjH,QAAQ;QACzBC,UAAU,EAAEgH,MAAM,CAAChH,UAAU;QAC7BiF,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrByB,MAAM,EAAE;UACNtB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDqB,aAAa,EAAE;UACfpB,IAAI,EAAE,IAAI,CAACvB,aAAa,CAACmD,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnD,aAAa,CAAC,CAAC,CAAC,CAAC4C,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACtD,GAAG,CAAC8D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAAC9C,MAAM,CAACiE,mBAAmB,CAAC,IAAI,CAACnD,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAACE,aAAa,GAAG,IAAI,CAAC,CAAC;YAC3B,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;YAC7B,IAAI,CAACrB,MAAM,CAACqD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAChD,iBAAiB,CAACiE,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAAClE,MAAM,CAACgD,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACe,GAAG,CAAChB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAiB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACtD,gBAAgB,GAAGsD,KAAK;EAC/B;EAEAC,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAIoG,IAAI,GAAG,IAAI,CAAChD,UAAU,CAACwB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC/H,QAAQ,KAAM2H,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAIoG,IAAI,GAAG,IAAI,CAAChD,UAAU,CAACwB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7C,SAAS,KAAMyC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC7H,KAAK;IACtC,IAAIoG,IAAI,GAAG,IAAI,CAAChD,UAAU,CAACwB,WAAW,EAAE,CAAC5E,KAAK;IAC9C,MAAM8H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9C,MAAM,KAAM0C,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC/H,gBAAgB,CAACkI,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAACnI,gBAAgB,CAACkI,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACvE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9B0E,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACjL,OAAO,GAAGgL,MAAM;YACrB,IAAI,CAAC9J,gBAAgB,CAAC2G,UAAU,CAAC;cAAElB,IAAI,EAAE,IAAI,CAAC3G;YAAO,CAAE,CAAC;YACxD,IAAI,CAACoF,aAAa,CAAC8F,IAAI,CAAC;cAAElD,GAAG,EAAEgD;YAAM,CAAE,CAAC;YACxC,IAAI,CAAC9F,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACsC,aAAa,EAAE;UACzB,CAAC;UACDoD,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACzI,aAAa,CAACiH,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACvJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEC;EACAsJ,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACtK,gBAAgB,CAACiH,OAAO,EAAE;MACjC,IAAI,CAAC3D,MAAM,CAACgD,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAAC9G,aAAa,GAAG,CAAC;IACtB,IAAI,CAACkE,EAAE,CAACsC,aAAa,EAAE;EACzB;EAEAlG,iBAAiBA,CAAA;IACf,IAAI,CAAC8C,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACnB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACsC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACsH,UAAU,EAAE;IACjB,MAAMlK,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACsD,GAAG,CAAC+G,eAAe,CAACrK,QAAQ,CAAC,CAACsK,IAAI,CACrCxM,UAAU,CAACyM,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAOvM,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAAC2H,SAAS,CAAE6E,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAACvE,OAAO,EAAE;QAChC,IAAI,CAACwE,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAAC3K,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACwK,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEA;EACAI,WAAWA,CAACC,QAAgB;IAC1B,IAAI,CAACvL,aAAa,GAAGuL,QAAQ;IAC7B,IAAI,CAACrH,EAAE,CAACsC,aAAa,EAAE;EACzB;EAEA6E,kBAAkBA,CAAC3K,QAAgB;IACjC,IAAI,CAAC8K,aAAa,GAAG5M,QAAQ,CAAC,KAAK,CAAC,CAACoM,IAAI,CACvCvM,SAAS,CAAC,MAAM,IAAI,CAACuF,GAAG,CAACyH,SAAS,CAAC/K,QAAQ,CAAC,CAAC,EAC7ChC,SAAS,CAAEyM,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAAC/D,MAAM,KAAK,UAAU,IAAI+D,QAAQ,CAAC/D,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACd,SAAS,CAAE6E,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAAC/D,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAAC8D,mBAAmB,CAACC,QAAQ,CAACO,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACxJ,gBAAgB,GAAGiJ,QAAQ,CAACQ,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAAC5K,sBAAsB,GAAGoK,QAAQ,CAACS,wBAAwB,IAAI,CAAC;MAEpE,IAAIT,QAAQ,CAACU,WAAW,KAAKC,SAAS,IAAIX,QAAQ,CAACU,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACrK,UAAU,GAAG2J,QAAQ,CAACU,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIZ,QAAQ,CAACU,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAAC5J,aAAa,CAAC0F,MAAM,EAAE;YACjC,IAAI,CAAC1F,aAAa,CAAC4J,CAAC,CAAC,CAACzK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAAC4C,EAAE,CAACsC,aAAa,EAAE;MAEvB,IAAI2E,QAAQ,CAAC/D,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC4E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEf,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAc,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC7I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,EAAE,CAACsC,aAAa,EAAE;IACvB,IAAI,CAACpC,QAAQ,CAAC8H,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAnB,mBAAmBA,CAACQ,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC7I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACc,QAAQ,CAAC8H,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAChG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAChG,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAiC,iBAAiBA,CAAA;IACf,IAAI,CAACyB,GAAG,CAACuI,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC/L,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAAC4F,SAAS,CAC/EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGhO,IAAI,CAACiO,IAAI,CAACH,YAAY,EAAE;UAAErD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAMyD,QAAQ,GAAG,GAAG,IAAI,CAACvM,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9E7B,IAAI,CAACmO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAO/F,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC5C,QAAQ,CAAC8H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAnF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC5C,QAAQ,CAAC8H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGAzJ,iBAAiBA,CAAA;IACf,IAAI,CAACsB,GAAG,CAACuI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC/L,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAAC4F,SAAS,CAC7EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGhO,IAAI,CAACiO,IAAI,CAACH,YAAY,EAAE;UAAErD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAMyD,QAAQ,GAAG,GAAG,IAAI,CAACvM,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9E7B,IAAI,CAACmO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAO/F,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC5C,QAAQ,CAAC8H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAnF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC5C,QAAQ,CAAC8H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAzbW1I,qBAAqB,EAAAzE,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAxO,EAAA,CAAAoO,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA3O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA7O,EAAA,CAAAoO,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAA/O,EAAA,CAAAoO,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAjP,EAAA,CAAAoO,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAnP,EAAA,CAAAoO,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAArP,EAAA,CAAAoO,iBAAA,CAAApO,EAAA,CAAAsP,iBAAA,GAAAtP,EAAA,CAAAoO,iBAAA,CAoDVnP,eAAe,MAAAe,EAAA,CAAAoO,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAAxP,EAAA,CAAAoO,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YApD1BjL,qBAAqB;MAAAkL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7P,EAAA,CAAA8P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAjF,GAAA;QAAA,IAAAiF,EAAA;;UCnDlCpQ,EAAA,CAAAmB,UAAA,IAAAkP,oCAAA,iBAOM;UAENrQ,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAA4P,wDAAA;YAAA,OAASnF,GAAA,CAAAzC,IAAA,EAAM;UAAA,EAAC;UACtB1I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6B;UAIUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAA6P,uDAAA3P,MAAA;YAAA,OAASuK,GAAA,CAAA7B,aAAA,CAAA1I,MAAA,CAAqB;UAAA,EAAC;UADjFZ,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAmB,UAAA,KAAAqP,2CAAA,wBAEY;UACdxQ,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAA+P,uDAAA7P,MAAA;YAAA,OAASuK,GAAA,CAAArB,cAAA,CAAAlJ,MAAA,CAAsB;UAAA,EAAC;UADlFZ,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAmB,UAAA,KAAAuP,2CAAA,wBAEY;UACd1Q,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAiQ,uDAAA/P,MAAA;YAAA,OAASuK,GAAA,CAAApB,WAAA,CAAAnJ,MAAA,CAAmB;UAAA,EAAC;UAD/BZ,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAmB,UAAA,KAAAyP,2CAAA,wBAEY;UACd5Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAmQ,wDAAA;YAAA,OAAA1F,GAAA,CAAAtF,YAAA,IAAAsF,GAAA,CAAAtF,YAAA;UAAA,EAAsC;UACtE7F,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UACGD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,eAA6B;UAIGD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UACvCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC7FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UACzCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACnCD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,6BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOtGH,EAAA,CAAAC,cAAA,gBAA0B;UAEID,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,gBAA4B;UAECD,EAAA,CAAAU,UAAA,mBAAAoQ,sDAAA;YAAA9Q,EAAA,CAAAa,aAAA,CAAAkQ,IAAA;YAAA,MAAAC,GAAA,GAAAhR,EAAA,CAAAiR,WAAA;YAAA,OAASjR,EAAA,CAAAiB,WAAA,CAAA+P,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAEpDlR,EAAA,CAAAmB,UAAA,MAAAgQ,sCAAA,kBAQM;UAGNnR,EAAA,CAAAmB,UAAA,MAAAiQ,sCAAA,kBAGM;UAGNpR,EAAA,CAAAmB,UAAA,MAAAkQ,sCAAA,kBAIM;UACRrR,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAA4Q,yDAAA1Q,MAAA;YAAA,OAAUuK,GAAA,CAAAnB,cAAA,CAAApJ,MAAA,CAAsB;UAAA,EAAC;UAA/DZ,EAAA,CAAAG,YAAA,EAAwG;UAGxGH,EAAA,CAAAmB,UAAA,MAAAoQ,4CAAA,wBAEY;UACdvR,EAAA,CAAAG,YAAA,EAAM;UAUpBH,EAAA,CAAAmB,UAAA,MAAAqQ,2CAAA,wBA6KW;UACbxR,EAAA,CAAAG,YAAA,EAAM;;;UAvWFH,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAjF,SAAA,CAAe;UAaZlG,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAyR,eAAA,KAAAC,GAAA,EAAkC;UAIlC1R,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAyR,eAAA,KAAAE,GAAA,EAAqC;UAMJ3R,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAA4C,iBAAA,CAAAuI,GAAA,CAAArF,UAAA,yBAAsC;UAClE9F,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA4C,iBAAA,CAAAuI,GAAA,CAAArF,UAAA,kCAA+C;UAOrD9F,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAA8B,kBAAA,MAAAqJ,GAAA,CAAArF,UAAA,4BACF;UAO6B9F,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAA6K,GAAA,CAAAzJ,gBAAA,CAA8B;UAgBrC1B,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,aAAAgI,QAAA,mBAAiE;UAUjE5R,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,cAAAgI,QAAA,oBAAmE;UAanE5R,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,WAAAgI,QAAA,iBAA6D;UAcvE5R,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAtF,YAAA,uBAA2C;UAEjC7F,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAA4C,iBAAA,CAAAuI,GAAA,CAAAtF,YAAA,mCAAkD;UAgDH7F,EAAA,CAAAK,SAAA,IAA0B;UAA1BL,EAAA,CAAA6R,WAAA,aAAA1G,GAAA,CAAA3K,OAAA,CAA0B;UAEpDR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAA3K,OAAA,CAAa;UAWTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAA6K,GAAA,CAAA3K,OAAA,CAAc;UAMlBR,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAzF,kBAAA,CAAwB;UAWzC1F,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,SAAAjB,OAAA,IAAAwC,GAAA,CAAAzJ,gBAAA,CAAAkI,GAAA,SAAAkI,OAAA,CAAkF;UAanG9R,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAA6K,GAAA,CAAAhF,gBAAA,CAAsB;;;qBDvJjC1H,YAAY,EAAAsT,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZrT,aAAa,EAAAsT,GAAA,CAAAC,OAAA,EACbtT,cAAc,EAAAuT,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACd5T,gBAAgB,EAChBL,WAAW,EAAA+P,EAAA,CAAAmE,aAAA,EAAAnE,EAAA,CAAAoE,oBAAA,EAAApE,EAAA,CAAAqE,eAAA,EAAArE,EAAA,CAAAsE,oBAAA,EACXpU,mBAAmB,EAAA8P,EAAA,CAAAuE,kBAAA,EAAAvE,EAAA,CAAAwE,eAAA,EACnB7T,cAAc,EAAA8T,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACd/T,eAAe,EAAAgU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfjU,aAAa,EAAAkU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACbxU,eAAe,EACfN,oBAAoB,EAAA+U,GAAA,CAAAC,cAAA,EACpB/U,aAAa,EAAAgV,GAAA,CAAAC,WAAA,EAAAD,GAAA,CAAAE,MAAA,EAAAF,GAAA,CAAAG,WAAA,EACb7U,gBAAgB,EAChBL,YAAY,EAAAmP,EAAA,CAAAgG,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHhQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}