{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/sidenav\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/button\";\nimport * as i14 from \"@angular/material/input\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"ngx-markdown\";\nconst _c0 = [\"chatContainer\"];\nfunction SmartDashboardComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", period_r6, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_25_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const category_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onFilterChange(\"categories\", category_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", category_r7.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r7.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_31_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const region_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onFilterChange(\"regions\", region_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", region_r10.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r10.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_37_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const metric_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onFilterChange(\"keyMetrics\", metric_r13));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const metric_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", metric_r13.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", metric_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"psychology\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h1\");\n    i0.ɵɵtext(6, \"AI-Powered Data Analysis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Configure your report filters and generate a dashboard to start analyzing your data with AI.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"div\", 31)(11, \"div\", 32)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"h3\");\n    i0.ɵɵtext(15, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 31)(17, \"div\", 32)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"h3\");\n    i0.ɵɵtext(21, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 31)(23, \"div\", 32)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"h3\");\n    i0.ɵɵtext(27, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 31)(29, \"div\", 32)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"h3\");\n    i0.ɵɵtext(33, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 33)(35, \"div\", 34)(36, \"div\", 35)(37, \"mat-icon\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"Select your filters (categories/regions)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 34)(42, \"div\", 35)(43, \"mat-icon\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"Ask the AI assistant what you'd like to visualize\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(35);\n    i0.ɵɵclassProp(\"completed\", ctx_r4.getActiveFiltersCount() > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getActiveFiltersCount() > 0 ? \"check_circle\" : \"radio_button_unchecked\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"completed\", ctx_r4.chatMessages.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.chatMessages.length > 0 ? \"check_circle\" : \"radio_button_unchecked\");\n  }\n}\nfunction SmartDashboardComponent_div_51_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵtext(2, \"Chart visualization would appear here\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"ai-message\": a1\n  };\n};\nfunction SmartDashboardComponent_div_51_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 43)(3, \"span\", 44)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 45);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 46);\n    i0.ɵɵelement(11, \"markdown\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SmartDashboardComponent_div_51_div_3_div_12_Template, 3, 0, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, message_r19.type === \"human\", message_r19.type === \"ai\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(message_r19.type === \"human\" ? \"person\" : \"psychology\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r19.type === \"human\" ? \"You\" : \"AI Assistant\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 6, message_r19.created_at, \"short\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"data\", message_r19.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.has_visualization && message_r19.chart_data);\n  }\n}\nfunction SmartDashboardComponent_div_51_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"mat-spinner\", 52);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"DIGI is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36, 37)(2, \"div\", 38);\n    i0.ɵɵtemplate(3, SmartDashboardComponent_div_51_div_3_Template, 13, 12, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_51_div_4_Template, 4, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.chatMessages)(\"ngForTrackBy\", ctx_r5.trackByIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading);\n  }\n}\nclass SmartDashboardComponent {\n  constructor(cd, smartDashboardService, authService, snackBar) {\n    this.cd = cd;\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    // Form Controls\n    this.messageControl = new FormControl('');\n    this.reportTypeControl = new FormControl('GRN Report');\n    // State\n    this.isLoading = false;\n    this.currentSession = null;\n    this.chatMessages = [];\n    this.destroy$ = new Subject();\n    // Smart Filters\n    this.smartFilters = {\n      timePeriod: 'Last 30 days',\n      categories: [{\n        value: 'ecommerce',\n        label: 'E-commerce',\n        checked: false\n      }, {\n        value: 'saas',\n        label: 'SaaS',\n        checked: false\n      }, {\n        value: 'mobile-apps',\n        label: 'Mobile Apps',\n        checked: false\n      }, {\n        value: 'marketing',\n        label: 'Marketing',\n        checked: false\n      }, {\n        value: 'support',\n        label: 'Support',\n        checked: false\n      }],\n      regions: [{\n        value: 'north-america',\n        label: 'North America',\n        checked: false\n      }, {\n        value: 'europe',\n        label: 'Europe',\n        checked: true\n      }, {\n        value: 'asia-pacific',\n        label: 'Asia Pacific',\n        checked: false\n      }, {\n        value: 'latin-america',\n        label: 'Latin America',\n        checked: false\n      }, {\n        value: 'africa',\n        label: 'Africa',\n        checked: false\n      }],\n      keyMetrics: [{\n        value: 'revenue',\n        label: 'Revenue',\n        checked: false\n      }, {\n        value: 'users',\n        label: 'Users',\n        checked: false\n      }, {\n        value: 'conversions',\n        label: 'Conversions',\n        checked: false\n      }, {\n        value: 'engagement',\n        label: 'Engagement',\n        checked: false\n      }, {\n        value: 'retention',\n        label: 'Retention',\n        checked: false\n      }]\n    };\n    // Time period options\n    this.timePeriodOptions = ['Last 7 days', 'Last 30 days', 'Last 90 days', 'Last 6 months', 'Last year', 'Custom range'];\n    // Example queries for quick actions\n    this.exampleQueries = ['Show me revenue trends by region', 'Compare user engagement across categories', 'What are the top performing metrics?', 'Create a conversion funnel analysis'];\n    this.user = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    // Initialize component\n    this.setupInitialState();\n    // Subscribe to current session changes\n    this.smartDashboardService.currentSession$.pipe(takeUntil(this.destroy$)).subscribe(session => {\n      this.currentSession = session;\n      this.cd.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupInitialState() {\n    // Set Europe as selected by default (as shown in the image)\n    const europeFilter = this.smartFilters.regions.find(r => r.value === 'europe');\n    if (europeFilter) {\n      europeFilter.checked = true;\n    }\n  }\n  // Filter management\n  getActiveFiltersCount() {\n    let count = 0;\n    count += this.smartFilters.categories.filter(c => c.checked).length;\n    count += this.smartFilters.regions.filter(r => r.checked).length;\n    count += this.smartFilters.keyMetrics.filter(m => m.checked).length;\n    return count;\n  }\n  onFilterChange(filterType, option) {\n    if (filterType !== 'timePeriod') {\n      option.checked = !option.checked;\n      this.cd.detectChanges();\n    }\n  }\n  onTimePeriodChange(period) {\n    this.smartFilters.timePeriod = period;\n  }\n  // Chat functionality\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message || this.isLoading) return;\n    // Add user message\n    this.chatMessages.push({\n      type: 'human',\n      content: message,\n      created_at: new Date()\n    });\n    this.messageControl.setValue('');\n    this.isLoading = true;\n    // Simulate AI response (replace with actual API call)\n    setTimeout(() => {\n      this.chatMessages.push({\n        type: 'ai',\n        content: 'I understand you want to analyze the data. Let me process your request and create the appropriate visualization.',\n        created_at: new Date(),\n        has_visualization: false\n      });\n      this.isLoading = false;\n      this.scrollToBottom();\n      this.cd.detectChanges();\n    }, 1500);\n    this.scrollToBottom();\n  }\n  sendExampleQuery(query) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.chatContainer) {\n        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;\n      }\n    }, 100);\n  }\n  // Utility methods\n  trackByIndex(index, _item) {\n    return index;\n  }\n  onFiltersApplied(filters) {\n    // Handle filter application\n    console.log('Filters applied:', filters);\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 52,\n      vars: 11,\n      consts: [[1, \"dashboard-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"filters-sidenav\"], [1, \"filters-content\"], [1, \"filters-header\"], [1, \"filters-title\"], [1, \"filter-icon\"], [1, \"filter-count\"], [1, \"filter-section\"], [1, \"filter-label\"], [1, \"section-icon\"], [\"appearance\", \"outline\", 1, \"time-period-select\"], [3, \"value\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"checkbox-group\"], [\"class\", \"filter-checkbox\", 3, \"checked\", \"change\", 4, \"ngFor\", \"ngForOf\"], [1, \"main-content\"], [1, \"ai-input-section\"], [1, \"ai-input-container\"], [\"color\", \"primary\", 1, \"ai-icon\"], [1, \"ai-label\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask AI to create a chart...\", 3, \"formControl\", \"disabled\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-button\", 3, \"disabled\", \"click\"], [\"class\", \"welcome-state\", 4, \"ngIf\"], [\"class\", \"chat-messages\", 4, \"ngIf\"], [3, \"value\"], [1, \"filter-checkbox\", 3, \"checked\", \"change\"], [1, \"welcome-state\"], [1, \"welcome-content\"], [1, \"ai-brain-icon\"], [1, \"features-grid\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"steps-section\"], [1, \"step-item\"], [1, \"step-icon\"], [1, \"chat-messages\"], [\"chatContainer\", \"\"], [1, \"messages-list\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"loading-message\", 4, \"ngIf\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-header\"], [1, \"message-sender\"], [1, \"message-time\"], [1, \"message-text\"], [3, \"data\"], [\"class\", \"message-chart\", 4, \"ngIf\"], [1, \"message-chart\"], [1, \"chart-placeholder\"], [1, \"loading-message\"], [\"diameter\", \"20\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-sidenav-container\", 0)(1, \"mat-sidenav\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"mat-chip\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"mat-icon\", 9);\n          i0.ɵɵtext(14, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Time Period\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-form-field\", 10)(18, \"mat-select\", 11);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_18_listener($event) {\n            return ctx.onTimePeriodChange($event.value);\n          });\n          i0.ɵɵtemplate(19, SmartDashboardComponent_mat_option_19_Template, 2, 2, \"mat-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"div\", 8)(22, \"span\");\n          i0.ɵɵtext(23, \"Categories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 13);\n          i0.ɵɵtemplate(25, SmartDashboardComponent_mat_checkbox_25_Template, 2, 2, \"mat-checkbox\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 7)(27, \"div\", 8)(28, \"span\");\n          i0.ɵɵtext(29, \"Regions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 13);\n          i0.ɵɵtemplate(31, SmartDashboardComponent_mat_checkbox_31_Template, 2, 2, \"mat-checkbox\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 7)(33, \"div\", 8)(34, \"span\");\n          i0.ɵɵtext(35, \"Key Metrics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 13);\n          i0.ɵɵtemplate(37, SmartDashboardComponent_mat_checkbox_37_Template, 2, 2, \"mat-checkbox\", 14);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"mat-sidenav-content\", 15)(39, \"div\", 16)(40, \"div\", 17)(41, \"mat-icon\", 18);\n          i0.ɵɵtext(42, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\", 19);\n          i0.ɵɵtext(44, \"Ask AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-form-field\", 20)(46, \"input\", 21);\n          i0.ɵɵlistener(\"keydown\", function SmartDashboardComponent_Template_input_keydown_46_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_47_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(50, SmartDashboardComponent_div_50_Template, 47, 6, \"div\", 23);\n          i0.ɵɵtemplate(51, SmartDashboardComponent_div_51_Template, 5, 3, \"div\", 24);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount());\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.smartFilters.timePeriod);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.timePeriodOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.categories);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.regions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.keyMetrics);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.messageControl)(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !(ctx.messageControl.value == null ? null : ctx.messageControl.value.trim()) || ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentSession && ctx.chatMessages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.chatMessages.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, ReactiveFormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, MatSidenavModule, i6.MatSidenav, i6.MatSidenavContainer, i6.MatSidenavContent, MatCardModule, MatIconModule, i7.MatIcon, MatChipsModule, i8.MatChip, MatExpansionModule, MatFormFieldModule, i9.MatFormField, MatSelectModule, i10.MatSelect, i11.MatOption, MatCheckboxModule, i12.MatCheckbox, MatButtonModule, i13.MatIconButton, MatInputModule, i14.MatInput, MatTabsModule, MatProgressSpinnerModule, i15.MatProgressSpinner, MarkdownModule, i16.MarkdownComponent],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  background-color: #f5f7fa;\\n}\\n\\n.filters-sidenav[_ngcontent-%COMP%] {\\n  width: 260px;\\n  background-color: #ffffff;\\n  border-right: 1px solid #e8eaed;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding-bottom: 12px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #202124;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #5f6368;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #1a73e8;\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 500;\\n  min-height: 18px;\\n  height: 18px;\\n  line-height: 18px;\\n  padding: 0 6px;\\n  border-radius: 9px;\\n  margin-left: auto;\\n}\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #3c4043;\\n  margin-bottom: 8px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #5f6368;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 36px;\\n  background-color: #f8f9fa;\\n  border-radius: 4px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  font-size: 13px;\\n  color: #3c4043;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mdc-checkbox {\\n  padding: 4px;\\n  --mdc-checkbox-unselected-icon-color: #dadce0;\\n  --mdc-checkbox-selected-icon-color: #1a73e8;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  color: #3c4043;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox-touch-target {\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 20px;\\n  overflow-y: auto;\\n}\\n\\n.ai-input-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 16px 20px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1976d2;\\n  white-space: nowrap;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n}\\n\\n.welcome-state[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 40px;\\n  text-align: center;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.welcome-content[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #1976d2;\\n}\\n.welcome-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 12px;\\n}\\n.welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 32px;\\n  line-height: 1.5;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 20px;\\n  margin-bottom: 32px;\\n}\\n@media (max-width: 768px) {\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  text-align: center;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #1976d2;\\n}\\n.feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.steps-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  text-align: left;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n.step-item.completed[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #ccc;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-height: calc(100vh - 200px);\\n  overflow-y: auto;\\n}\\n\\n.messages-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.message.user-message[_ngcontent-%COMP%] {\\n  align-self: flex-end;\\n  max-width: 70%;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n  color: white;\\n  border-radius: 18px 18px 4px 18px;\\n}\\n.message.ai-message[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  max-width: 85%;\\n}\\n.message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-radius: 18px 18px 18px 4px;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n}\\n\\n.message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 12px;\\n  opacity: 0.8;\\n}\\n.message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-weight: 500;\\n}\\n.message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.message-header[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.chart-placeholder[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding: 20px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 8px;\\n  text-align: center;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n.loading-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  color: #666;\\n  font-style: italic;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "ReactiveFormsModule", "MatSidenavModule", "MatCardModule", "MatIconModule", "MatChipsModule", "MatExpansionModule", "MatFormFieldModule", "MatSelectModule", "MatCheckboxModule", "MatButtonModule", "MatInputModule", "MatTabsModule", "MatProgressSpinnerModule", "MarkdownModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "period_r6", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵlistener", "SmartDashboardComponent_mat_checkbox_25_Template_mat_checkbox_change_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "category_r7", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "checked", "label", "SmartDashboardComponent_mat_checkbox_31_Template_mat_checkbox_change_0_listener", "_r12", "region_r10", "ctx_r11", "SmartDashboardComponent_mat_checkbox_37_Template_mat_checkbox_change_0_listener", "_r15", "metric_r13", "ctx_r14", "ɵɵclassProp", "ctx_r4", "getActiveFiltersCount", "ɵɵtextInterpolate", "chatMessages", "length", "ɵɵelement", "ɵɵtemplate", "SmartDashboardComponent_div_51_div_3_div_12_Template", "ɵɵpureFunction2", "_c1", "message_r19", "type", "ɵɵpipeBind2", "created_at", "content", "has_visualization", "chart_data", "SmartDashboardComponent_div_51_div_3_Template", "SmartDashboardComponent_div_51_div_4_Template", "ctx_r5", "trackByIndex", "isLoading", "SmartDashboardComponent", "constructor", "cd", "smartDashboardService", "authService", "snackBar", "messageControl", "reportTypeControl", "currentSession", "destroy$", "smartFilters", "timePeriod", "categories", "value", "regions", "keyMetrics", "timePeriodOptions", "exampleQueries", "user", "getCurrentUser", "ngOnInit", "setupInitialState", "currentSession$", "pipe", "subscribe", "session", "detectChanges", "ngOnDestroy", "next", "complete", "europeFilter", "find", "r", "count", "filter", "c", "m", "filterType", "option", "onTimePeriodChange", "period", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "sendMessage", "message", "trim", "push", "Date", "setValue", "setTimeout", "scrollToBottom", "send<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "query", "chatContainer", "nativeElement", "scrollTop", "scrollHeight", "index", "_item", "onFiltersApplied", "filters", "console", "log", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_selectionChange_18_listener", "$event", "SmartDashboardComponent_mat_option_19_Template", "SmartDashboardComponent_mat_checkbox_25_Template", "SmartDashboardComponent_mat_checkbox_31_Template", "SmartDashboardComponent_mat_checkbox_37_Template", "SmartDashboardComponent_Template_input_keydown_46_listener", "SmartDashboardComponent_Template_button_click_47_listener", "SmartDashboardComponent_div_50_Template", "SmartDashboardComponent_div_51_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "i6", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i7", "MatIcon", "i8", "MatChip", "i9", "MatFormField", "i10", "MatSelect", "i11", "MatOption", "i12", "MatCheckbox", "i13", "MatIconButton", "i14", "MatInput", "i15", "MatProgressSpinner", "i16", "MarkdownComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject, takeUntil } from 'rxjs';\nimport {\n  SmartDashboardService,\n  SmartFilters,\n  ChatMessage,\n  DashboardSession,\n  FilterOption\n} from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatSidenavModule,\n    MatCardModule,\n    MatIconModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatButtonModule,\n    MatInputModule,\n    MatTabsModule,\n    MatProgressSpinnerModule,\n    MarkdownModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  @ViewChild('chatContainer') chatContainer!: ElementRef;\n\n  // Form Controls\n  messageControl = new FormControl('');\n  reportTypeControl = new FormControl('GRN Report');\n\n  // State\n  isLoading = false;\n  currentSession: DashboardSession | null = null;\n  chatMessages: ChatMessage[] = [];\n  private destroy$ = new Subject<void>();\n  private user: any;\n\n  // Smart Filters\n  smartFilters: SmartFilters = {\n    timePeriod: 'Last 30 days',\n    categories: [\n      { value: 'ecommerce', label: 'E-commerce', checked: false },\n      { value: 'saas', label: 'SaaS', checked: false },\n      { value: 'mobile-apps', label: 'Mobile Apps', checked: false },\n      { value: 'marketing', label: 'Marketing', checked: false },\n      { value: 'support', label: 'Support', checked: false }\n    ],\n    regions: [\n      { value: 'north-america', label: 'North America', checked: false },\n      { value: 'europe', label: 'Europe', checked: true },\n      { value: 'asia-pacific', label: 'Asia Pacific', checked: false },\n      { value: 'latin-america', label: 'Latin America', checked: false },\n      { value: 'africa', label: 'Africa', checked: false }\n    ],\n    keyMetrics: [\n      { value: 'revenue', label: 'Revenue', checked: false },\n      { value: 'users', label: 'Users', checked: false },\n      { value: 'conversions', label: 'Conversions', checked: false },\n      { value: 'engagement', label: 'Engagement', checked: false },\n      { value: 'retention', label: 'Retention', checked: false }\n    ]\n  };\n\n  // Time period options\n  timePeriodOptions = [\n    'Last 7 days',\n    'Last 30 days',\n    'Last 90 days',\n    'Last 6 months',\n    'Last year',\n    'Custom range'\n  ];\n\n  // Example queries for quick actions\n  exampleQueries = [\n    'Show me revenue trends by region',\n    'Compare user engagement across categories',\n    'What are the top performing metrics?',\n    'Create a conversion funnel analysis'\n  ];\n\n  constructor(\n    private cd: ChangeDetectorRef,\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.user = this.authService.getCurrentUser();\n  }\n\n  ngOnInit() {\n    // Initialize component\n    this.setupInitialState();\n\n    // Subscribe to current session changes\n    this.smartDashboardService.currentSession$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(session => {\n        this.currentSession = session;\n        this.cd.detectChanges();\n      });\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private setupInitialState() {\n    // Set Europe as selected by default (as shown in the image)\n    const europeFilter = this.smartFilters.regions.find(r => r.value === 'europe');\n    if (europeFilter) {\n      europeFilter.checked = true;\n    }\n  }\n\n  // Filter management\n  getActiveFiltersCount(): number {\n    let count = 0;\n    count += this.smartFilters.categories.filter(c => c.checked).length;\n    count += this.smartFilters.regions.filter(r => r.checked).length;\n    count += this.smartFilters.keyMetrics.filter(m => m.checked).length;\n    return count;\n  }\n\n  onFilterChange(filterType: keyof SmartFilters, option: FilterOption) {\n    if (filterType !== 'timePeriod') {\n      option.checked = !option.checked;\n      this.cd.detectChanges();\n    }\n  }\n\n  onTimePeriodChange(period: string) {\n    this.smartFilters.timePeriod = period;\n  }\n\n  // Chat functionality\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message || this.isLoading) return;\n\n    // Add user message\n    this.chatMessages.push({\n      type: 'human',\n      content: message,\n      created_at: new Date()\n    });\n\n    this.messageControl.setValue('');\n    this.isLoading = true;\n\n    // Simulate AI response (replace with actual API call)\n    setTimeout(() => {\n      this.chatMessages.push({\n        type: 'ai',\n        content: 'I understand you want to analyze the data. Let me process your request and create the appropriate visualization.',\n        created_at: new Date(),\n        has_visualization: false\n      });\n      this.isLoading = false;\n      this.scrollToBottom();\n      this.cd.detectChanges();\n    }, 1500);\n\n    this.scrollToBottom();\n  }\n\n  sendExampleQuery(query: string) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.chatContainer) {\n        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // Utility methods\n  trackByIndex(index: number, _item: any): number {\n    return index;\n  }\n\n  onFiltersApplied(filters: any) {\n    // Handle filter application\n    console.log('Filters applied:', filters);\n  }\n}\n", "<mat-sidenav-container class=\"dashboard-container\">\n  <!-- Left Sidebar - Smart Filters -->\n  <mat-sidenav mode=\"side\" opened=\"true\" class=\"filters-sidenav\">\n    <div class=\"filters-content\">\n      <!-- Smart Filters Header -->\n      <div class=\"filters-header\">\n        <div class=\"filters-title\">\n          <mat-icon class=\"filter-icon\">tune</mat-icon>\n          <span>Smart Filters</span>\n          <mat-chip class=\"filter-count\">{{ getActiveFiltersCount() }}</mat-chip>\n        </div>\n      </div>\n\n      <!-- Time Period Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <mat-icon class=\"section-icon\">schedule</mat-icon>\n          <span>Time Period</span>\n        </div>\n        <mat-form-field appearance=\"outline\" class=\"time-period-select\">\n          <mat-select \n            [value]=\"smartFilters.timePeriod\" \n            (selectionChange)=\"onTimePeriodChange($event.value)\">\n            <mat-option *ngFor=\"let period of timePeriodOptions\" [value]=\"period\">\n              {{ period }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- Categories Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Categories</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox \n            *ngFor=\"let category of smartFilters.categories\"\n            [checked]=\"category.checked\"\n            (change)=\"onFilterChange('categories', category)\"\n            class=\"filter-checkbox\">\n            {{ category.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Regions Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Regions</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox \n            *ngFor=\"let region of smartFilters.regions\"\n            [checked]=\"region.checked\"\n            (change)=\"onFilterChange('regions', region)\"\n            class=\"filter-checkbox\">\n            {{ region.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Key Metrics Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Key Metrics</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox \n            *ngFor=\"let metric of smartFilters.keyMetrics\"\n            [checked]=\"metric.checked\"\n            (change)=\"onFilterChange('keyMetrics', metric)\"\n            class=\"filter-checkbox\">\n            {{ metric.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n    </div>\n  </mat-sidenav>\n\n  <!-- Main Content Area -->\n  <mat-sidenav-content class=\"main-content\">\n    <!-- AI Assistant Input -->\n    <div class=\"ai-input-section\">\n      <div class=\"ai-input-container\">\n        <mat-icon class=\"ai-icon\" color=\"primary\">psychology</mat-icon>\n        <span class=\"ai-label\">Ask AI Assistant</span>\n        <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n          <input\n            matInput\n            [formControl]=\"messageControl\"\n            placeholder=\"Ask AI to create a chart...\"\n            (keydown)=\"onKeyPress($event)\"\n            [disabled]=\"isLoading\">\n        </mat-form-field>\n        <button\n          mat-icon-button\n          color=\"primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!messageControl.value?.trim() || isLoading\"\n          class=\"send-button\">\n          <mat-icon>send</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <!-- Welcome State -->\n    <div class=\"welcome-state\" *ngIf=\"!currentSession && chatMessages.length === 0\">\n      <div class=\"welcome-content\">\n        <div class=\"ai-brain-icon\">\n          <mat-icon>psychology</mat-icon>\n        </div>\n        <h1>AI-Powered Data Analysis</h1>\n        <p>Configure your report filters and generate a dashboard to start analyzing your data with AI.</p>\n\n        <div class=\"features-grid\">\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>bar_chart</mat-icon>\n            </div>\n            <h3>Interactive Charts</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h3>Smart Insights</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>chat</mat-icon>\n            </div>\n            <h3>Natural Language Queries</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>speed</mat-icon>\n            </div>\n            <h3>Real-time Analysis</h3>\n          </div>\n        </div>\n\n        <!-- Steps -->\n        <div class=\"steps-section\">\n          <div class=\"step-item\" [class.completed]=\"getActiveFiltersCount() > 0\">\n            <div class=\"step-icon\">\n              <mat-icon>{{ getActiveFiltersCount() > 0 ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <span>Select your filters (categories/regions)</span>\n          </div>\n          <div class=\"step-item\" [class.completed]=\"chatMessages.length > 0\">\n            <div class=\"step-icon\">\n              <mat-icon>{{ chatMessages.length > 0 ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <span>Ask the AI assistant what you'd like to visualize</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Chat Messages -->\n    <div class=\"chat-messages\" #chatContainer *ngIf=\"chatMessages.length > 0\">\n      <div class=\"messages-list\">\n        <div\n          *ngFor=\"let message of chatMessages; trackBy: trackByIndex\"\n          class=\"message\"\n          [ngClass]=\"{'user-message': message.type === 'human', 'ai-message': message.type === 'ai'}\">\n          \n          <div class=\"message-content\">\n            <div class=\"message-header\">\n              <span class=\"message-sender\">\n                <mat-icon>{{ message.type === 'human' ? 'person' : 'psychology' }}</mat-icon>\n                {{ message.type === 'human' ? 'You' : 'AI Assistant' }}\n              </span>\n              <span class=\"message-time\">\n                {{ message.created_at | date:'short' }}\n              </span>\n            </div>\n\n            <div class=\"message-text\">\n              <markdown [data]=\"message.content\"></markdown>\n            </div>\n\n            <!-- Chart Visualization -->\n            <div *ngIf=\"message.has_visualization && message.chart_data\" class=\"message-chart\">\n              <!-- Chart component would go here -->\n              <div class=\"chart-placeholder\">Chart visualization would appear here</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading indicator -->\n      <div *ngIf=\"isLoading\" class=\"loading-message\">\n        <mat-spinner diameter=\"20\"></mat-spinner>\n        <span>DIGI is thinking...</span>\n      </div>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,cAAc;AAE7C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;ICM7BC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IACnEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,SAAA,MACF;;;;;;IAWFL,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAC,gFAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAe,YAAY,EAAAL,WAAA,CAAW;IAAA,EAAC;IAEjDb,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAS,WAAA,CAAAM,OAAA,CAA4B;IAG5BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,WAAA,CAAAO,KAAA,MACF;;;;;;IAUApB,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAa,gFAAA;MAAA,MAAAX,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAb,WAAA,CAAAI,SAAA;MAAA,MAAAU,OAAA,GAAAxB,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAO,OAAA,CAAAN,cAAA,CAAe,SAAS,EAAAK,UAAA,CAAS;IAAA,EAAC;IAE5CvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAmB,UAAA,CAAAJ,OAAA,CAA0B;IAG1BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAgB,UAAA,CAAAH,KAAA,MACF;;;;;;IAUApB,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAiB,gFAAA;MAAA,MAAAf,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAe,IAAA;MAAA,MAAAC,UAAA,GAAAjB,WAAA,CAAAI,SAAA;MAAA,MAAAc,OAAA,GAAA5B,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAW,OAAA,CAAAV,cAAA,CAAe,YAAY,EAAAS,UAAA,CAAS;IAAA,EAAC;IAE/C3B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAuB,UAAA,CAAAR,OAAA,CAA0B;IAG1BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAoB,UAAA,CAAAP,KAAA,MACF;;;;;IAiCNpB,EAAA,CAAAC,cAAA,cAAgF;IAGhED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mGAA4F;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnGH,EAAA,CAAAC,cAAA,cAA2B;IAGXD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAK/BH,EAAA,CAAAC,cAAA,eAA2B;IAGXD,EAAA,CAAAE,MAAA,IAA6E;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,eAAmE;IAErDD,EAAA,CAAAE,MAAA,IAAyE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yDAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAVzCH,EAAA,CAAAM,SAAA,IAA+C;IAA/CN,EAAA,CAAA6B,WAAA,cAAAC,MAAA,CAAAC,qBAAA,OAA+C;IAExD/B,EAAA,CAAAM,SAAA,GAA6E;IAA7EN,EAAA,CAAAgC,iBAAA,CAAAF,MAAA,CAAAC,qBAAA,mDAA6E;IAIpE/B,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA6B,WAAA,cAAAC,MAAA,CAAAG,YAAA,CAAAC,MAAA,KAA2C;IAEpDlC,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAgC,iBAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAC,MAAA,iDAAyE;;;;;IAgCrFlC,EAAA,CAAAC,cAAA,cAAmF;IAElDD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAvBhFH,EAAA,CAAAC,cAAA,cAG8F;IAK5ED,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7EH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAmC,SAAA,oBAA8C;IAChDnC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoC,UAAA,KAAAC,oDAAA,kBAGM;IACRrC,EAAA,CAAAG,YAAA,EAAM;;;;IAtBNH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,cAAAD,WAAA,CAAAC,IAAA,WAA2F;IAK3EzC,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAAgC,iBAAA,CAAAQ,WAAA,CAAAC,IAAA,uCAAwD;IAClEzC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAiC,WAAA,CAAAC,IAAA,2CACF;IAEEzC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA0C,WAAA,OAAAF,WAAA,CAAAG,UAAA,gBACF;IAIU3C,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAoC,WAAA,CAAAI,OAAA,CAAwB;IAI9B5C,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAI,UAAA,SAAAoC,WAAA,CAAAK,iBAAA,IAAAL,WAAA,CAAAM,UAAA,CAAqD;;;;;IASjE9C,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAmC,SAAA,sBAAyC;IACzCnC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAlCpCH,EAAA,CAAAC,cAAA,kBAA0E;IAEtED,EAAA,CAAAoC,UAAA,IAAAW,6CAAA,oBA0BM;IACR/C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoC,UAAA,IAAAY,6CAAA,kBAGM;IACRhD,EAAA,CAAAG,YAAA,EAAM;;;;IAjCoBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAA6C,MAAA,CAAAhB,YAAA,CAAiB,iBAAAgB,MAAA,CAAAC,YAAA;IA6BnClD,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,SAAA6C,MAAA,CAAAE,SAAA,CAAe;;;ADtK3B,MAuBaC,uBAAuB;EA0DlCC,YACUC,EAAqB,EACrBC,qBAA4C,EAC5CC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IA3DlB;IACA,KAAAC,cAAc,GAAG,IAAI3E,WAAW,CAAC,EAAE,CAAC;IACpC,KAAA4E,iBAAiB,GAAG,IAAI5E,WAAW,CAAC,YAAY,CAAC;IAEjD;IACA,KAAAoE,SAAS,GAAG,KAAK;IACjB,KAAAS,cAAc,GAA4B,IAAI;IAC9C,KAAA3B,YAAY,GAAkB,EAAE;IACxB,KAAA4B,QAAQ,GAAG,IAAI/D,OAAO,EAAQ;IAGtC;IACA,KAAAgE,YAAY,GAAiB;MAC3BC,UAAU,EAAE,cAAc;MAC1BC,UAAU,EAAE,CACV;QAAEC,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,YAAY;QAAED,OAAO,EAAE;MAAK,CAAE,EAC3D;QAAE8C,KAAK,EAAE,MAAM;QAAE7C,KAAK,EAAE,MAAM;QAAED,OAAO,EAAE;MAAK,CAAE,EAChD;QAAE8C,KAAK,EAAE,aAAa;QAAE7C,KAAK,EAAE,aAAa;QAAED,OAAO,EAAE;MAAK,CAAE,EAC9D;QAAE8C,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,WAAW;QAAED,OAAO,EAAE;MAAK,CAAE,EAC1D;QAAE8C,KAAK,EAAE,SAAS;QAAE7C,KAAK,EAAE,SAAS;QAAED,OAAO,EAAE;MAAK,CAAE,CACvD;MACD+C,OAAO,EAAE,CACP;QAAED,KAAK,EAAE,eAAe;QAAE7C,KAAK,EAAE,eAAe;QAAED,OAAO,EAAE;MAAK,CAAE,EAClE;QAAE8C,KAAK,EAAE,QAAQ;QAAE7C,KAAK,EAAE,QAAQ;QAAED,OAAO,EAAE;MAAI,CAAE,EACnD;QAAE8C,KAAK,EAAE,cAAc;QAAE7C,KAAK,EAAE,cAAc;QAAED,OAAO,EAAE;MAAK,CAAE,EAChE;QAAE8C,KAAK,EAAE,eAAe;QAAE7C,KAAK,EAAE,eAAe;QAAED,OAAO,EAAE;MAAK,CAAE,EAClE;QAAE8C,KAAK,EAAE,QAAQ;QAAE7C,KAAK,EAAE,QAAQ;QAAED,OAAO,EAAE;MAAK,CAAE,CACrD;MACDgD,UAAU,EAAE,CACV;QAAEF,KAAK,EAAE,SAAS;QAAE7C,KAAK,EAAE,SAAS;QAAED,OAAO,EAAE;MAAK,CAAE,EACtD;QAAE8C,KAAK,EAAE,OAAO;QAAE7C,KAAK,EAAE,OAAO;QAAED,OAAO,EAAE;MAAK,CAAE,EAClD;QAAE8C,KAAK,EAAE,aAAa;QAAE7C,KAAK,EAAE,aAAa;QAAED,OAAO,EAAE;MAAK,CAAE,EAC9D;QAAE8C,KAAK,EAAE,YAAY;QAAE7C,KAAK,EAAE,YAAY;QAAED,OAAO,EAAE;MAAK,CAAE,EAC5D;QAAE8C,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,WAAW;QAAED,OAAO,EAAE;MAAK,CAAE;KAE7D;IAED;IACA,KAAAiD,iBAAiB,GAAG,CAClB,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,WAAW,EACX,cAAc,CACf;IAED;IACA,KAAAC,cAAc,GAAG,CACf,kCAAkC,EAClC,2CAA2C,EAC3C,sCAAsC,EACtC,qCAAqC,CACtC;IAQC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACd,WAAW,CAACe,cAAc,EAAE;EAC/C;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAClB,qBAAqB,CAACmB,eAAe,CACvCC,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAAC8D,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAACC,OAAO,IAAG;MACnB,IAAI,CAACjB,cAAc,GAAGiB,OAAO;MAC7B,IAAI,CAACvB,EAAE,CAACwB,aAAa,EAAE;IACzB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClB,QAAQ,CAACmB,IAAI,EAAE;IACpB,IAAI,CAACnB,QAAQ,CAACoB,QAAQ,EAAE;EAC1B;EAEQR,iBAAiBA,CAAA;IACvB;IACA,MAAMS,YAAY,GAAG,IAAI,CAACpB,YAAY,CAACI,OAAO,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,KAAK,QAAQ,CAAC;IAC9E,IAAIiB,YAAY,EAAE;MAChBA,YAAY,CAAC/D,OAAO,GAAG,IAAI;;EAE/B;EAEA;EACAY,qBAAqBA,CAAA;IACnB,IAAIsD,KAAK,GAAG,CAAC;IACbA,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACE,UAAU,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpE,OAAO,CAAC,CAACe,MAAM;IACnEmD,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACI,OAAO,CAACoB,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACjE,OAAO,CAAC,CAACe,MAAM;IAChEmD,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACK,UAAU,CAACmB,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACrE,OAAO,CAAC,CAACe,MAAM;IACnE,OAAOmD,KAAK;EACd;EAEAnE,cAAcA,CAACuE,UAA8B,EAAEC,MAAoB;IACjE,IAAID,UAAU,KAAK,YAAY,EAAE;MAC/BC,MAAM,CAACvE,OAAO,GAAG,CAACuE,MAAM,CAACvE,OAAO;MAChC,IAAI,CAACmC,EAAE,CAACwB,aAAa,EAAE;;EAE3B;EAEAa,kBAAkBA,CAACC,MAAc;IAC/B,IAAI,CAAC9B,YAAY,CAACC,UAAU,GAAG6B,MAAM;EACvC;EAEA;EACAC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEAA,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAI,CAACzC,cAAc,CAACO,KAAK,EAAEmC,IAAI,EAAE;IACjD,IAAI,CAACD,OAAO,IAAI,IAAI,CAAChD,SAAS,EAAE;IAEhC;IACA,IAAI,CAAClB,YAAY,CAACoE,IAAI,CAAC;MACrB5D,IAAI,EAAE,OAAO;MACbG,OAAO,EAAEuD,OAAO;MAChBxD,UAAU,EAAE,IAAI2D,IAAI;KACrB,CAAC;IAEF,IAAI,CAAC5C,cAAc,CAAC6C,QAAQ,CAAC,EAAE,CAAC;IAChC,IAAI,CAACpD,SAAS,GAAG,IAAI;IAErB;IACAqD,UAAU,CAAC,MAAK;MACd,IAAI,CAACvE,YAAY,CAACoE,IAAI,CAAC;QACrB5D,IAAI,EAAE,IAAI;QACVG,OAAO,EAAE,kHAAkH;QAC3HD,UAAU,EAAE,IAAI2D,IAAI,EAAE;QACtBzD,iBAAiB,EAAE;OACpB,CAAC;MACF,IAAI,CAACM,SAAS,GAAG,KAAK;MACtB,IAAI,CAACsD,cAAc,EAAE;MACrB,IAAI,CAACnD,EAAE,CAACwB,aAAa,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAAC2B,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAACC,KAAa;IAC5B,IAAI,CAACjD,cAAc,CAAC6C,QAAQ,CAACI,KAAK,CAAC;IACnC,IAAI,CAACT,WAAW,EAAE;EACpB;EAEQO,cAAcA,CAAA;IACpBD,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACI,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,YAAY;;IAE9F,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA7D,YAAYA,CAAC8D,KAAa,EAAEC,KAAU;IACpC,OAAOD,KAAK;EACd;EAEAE,gBAAgBA,CAACC,OAAY;IAC3B;IACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;EAC1C;;;uBA5KW/D,uBAAuB,EAAApD,EAAA,CAAAsH,iBAAA,CAAAtH,EAAA,CAAAuH,iBAAA,GAAAvH,EAAA,CAAAsH,iBAAA,CAAAE,EAAA,CAAAC,qBAAA,GAAAzH,EAAA,CAAAsH,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAsH,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBzE,uBAAuB;MAAA0E,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UClDpCjI,EAAA,CAAAC,cAAA,+BAAmD;UAOXD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAC,cAAA,kBAA+B;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAK3EH,EAAA,CAAAC,cAAA,cAA4B;UAEOD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,0BAAgE;UAG5DD,EAAA,CAAAQ,UAAA,6BAAA2H,wEAAAC,MAAA;YAAA,OAAmBF,GAAA,CAAAvC,kBAAA,CAAAyC,MAAA,CAAAnE,KAAA,CAAgC;UAAA,EAAC;UACpDjE,EAAA,CAAAoC,UAAA,KAAAiG,8CAAA,yBAEa;UACfrI,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAkG,gDAAA,2BAMe;UACjBtI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtBH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAmG,gDAAA,2BAMe;UACjBvI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAoG,gDAAA,2BAMe;UACjBxI,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,+BAA0C;UAIMD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/DH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,0BAA4D;UAKxDD,EAAA,CAAAQ,UAAA,qBAAAiI,2DAAAL,MAAA;YAAA,OAAWF,GAAA,CAAArC,UAAA,CAAAuC,MAAA,CAAkB;UAAA,EAAC;UAJhCpI,EAAA,CAAAG,YAAA,EAKyB;UAE3BH,EAAA,CAAAC,cAAA,kBAKsB;UAFpBD,EAAA,CAAAQ,UAAA,mBAAAkI,0DAAA;YAAA,OAASR,GAAA,CAAAhC,WAAA,EAAa;UAAA,EAAC;UAGvBlG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAoC,UAAA,KAAAuG,uCAAA,mBAmDM;UAGN3I,EAAA,CAAAoC,UAAA,KAAAwG,uCAAA,kBAoCM;UACR5I,EAAA,CAAAG,YAAA,EAAsB;;;UA7LiBH,EAAA,CAAAM,SAAA,IAA6B;UAA7BN,EAAA,CAAAgC,iBAAA,CAAAkG,GAAA,CAAAnG,qBAAA,GAA6B;UAY1D/B,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAA8H,GAAA,CAAApE,YAAA,CAAAC,UAAA,CAAiC;UAEF/D,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAA9D,iBAAA,CAAoB;UAc9BpE,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAE,UAAA,CAA0B;UAgB5BhE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAI,OAAA,CAAuB;UAgBvBlE,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAK,UAAA,CAA0B;UAqB7CnE,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,gBAAA8H,GAAA,CAAAxE,cAAA,CAA8B,aAAAwE,GAAA,CAAA/E,SAAA;UAShCnD,EAAA,CAAAM,SAAA,GAAuD;UAAvDN,EAAA,CAAAI,UAAA,eAAA8H,GAAA,CAAAxE,cAAA,CAAAO,KAAA,kBAAAiE,GAAA,CAAAxE,cAAA,CAAAO,KAAA,CAAAmC,IAAA,OAAA8B,GAAA,CAAA/E,SAAA,CAAuD;UAQjCnD,EAAA,CAAAM,SAAA,GAAkD;UAAlDN,EAAA,CAAAI,UAAA,UAAA8H,GAAA,CAAAtE,cAAA,IAAAsE,GAAA,CAAAjG,YAAA,CAAAC,MAAA,OAAkD;UAsDnClC,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,SAAA8H,GAAA,CAAAjG,YAAA,CAAAC,MAAA,KAA6B;;;qBDlIxEpD,YAAY,EAAA+J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZjK,mBAAmB,EAAAkK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EACnBpK,gBAAgB,EAAAqK,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChBvK,aAAa,EACbC,aAAa,EAAAuK,EAAA,CAAAC,OAAA,EACbvK,cAAc,EAAAwK,EAAA,CAAAC,OAAA,EACdxK,kBAAkB,EAClBC,kBAAkB,EAAAwK,EAAA,CAAAC,YAAA,EAClBxK,eAAe,EAAAyK,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf3K,iBAAiB,EAAA4K,GAAA,CAAAC,WAAA,EACjB5K,eAAe,EAAA6K,GAAA,CAAAC,aAAA,EACf7K,cAAc,EAAA8K,GAAA,CAAAC,QAAA,EACd9K,aAAa,EACbC,wBAAwB,EAAA8K,GAAA,CAAAC,kBAAA,EACxB9K,cAAc,EAAA+K,GAAA,CAAAC,iBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKL1H,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}