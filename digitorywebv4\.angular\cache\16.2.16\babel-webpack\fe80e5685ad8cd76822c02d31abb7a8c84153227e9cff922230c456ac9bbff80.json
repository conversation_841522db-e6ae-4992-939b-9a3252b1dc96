{"ast": null, "code": "'use strict';\n\nmodule.exports.encode = require('./encode');\nmodule.exports.decode = require('./decode');\nmodule.exports.format = require('./format');\nmodule.exports.parse = require('./parse');", "map": {"version": 3, "names": ["module", "exports", "encode", "require", "decode", "format", "parse"], "sources": ["/home/<USER>/other/digi/digitorywebv4/node_modules/mdurl/index.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports.encode = require('./encode');\nmodule.exports.decode = require('./decode');\nmodule.exports.format = require('./format');\nmodule.exports.parse  = require('./parse');\n"], "mappings": "AAAA,YAAY;;AAGZA,MAAM,CAACC,OAAO,CAACC,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3CH,MAAM,CAACC,OAAO,CAACG,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAC3CH,MAAM,CAACC,OAAO,CAACI,MAAM,GAAGF,OAAO,CAAC,UAAU,CAAC;AAC3CH,MAAM,CAACC,OAAO,CAACK,KAAK,GAAIH,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}