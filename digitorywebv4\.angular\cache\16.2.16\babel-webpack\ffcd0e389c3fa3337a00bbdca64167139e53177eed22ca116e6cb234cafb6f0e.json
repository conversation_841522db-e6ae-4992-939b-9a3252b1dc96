{"ast": null, "code": "// Join raw text tokens with the rest of the text\n//\n// This is set as a separate rule to provide an opportunity for plugins\n// to run text replacements after text join, but before escape join.\n//\n// For example, `\\:)` shouldn't be replaced with an emoji.\n//\n'use strict';\n\nmodule.exports = function text_join(state) {\n  var j,\n    l,\n    tokens,\n    curr,\n    max,\n    last,\n    blockTokens = state.tokens;\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') continue;\n    tokens = blockTokens[j].children;\n    max = tokens.length;\n    for (curr = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text_special') {\n        tokens[curr].type = 'text';\n      }\n    }\n    for (curr = last = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text' && curr + 1 < max && tokens[curr + 1].type === 'text') {\n        // collapse two adjacent text nodes\n        tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n      } else {\n        if (curr !== last) {\n          tokens[last] = tokens[curr];\n        }\n        last++;\n      }\n    }\n    if (curr !== last) {\n      tokens.length = last;\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}