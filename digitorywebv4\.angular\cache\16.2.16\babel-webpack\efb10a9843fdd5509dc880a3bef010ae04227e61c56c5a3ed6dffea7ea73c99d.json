{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/share-data.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/auth.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/toolbar\";\nimport * as i10 from \"@angular/material/menu\";\nconst _c0 = [\"allSelected\"];\nfunction DashboardToolbarComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtext(2, \"D\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardToolbarComponent_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 23)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", item_r4.path);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.title);\n  }\n}\nclass DashboardToolbarComponent {\n  constructor(selectedBranchesService, dialog, router, auth, sharedData, cd, notify) {\n    this.selectedBranchesService = selectedBranchesService;\n    this.dialog = dialog;\n    this.router = router;\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.showNavbarToggleButton = false;\n    this.menuItems = [];\n    this.logoUrl = '';\n    this.toggleMenu = new EventEmitter();\n    this.branchList = [];\n    this.branches = new FormControl('');\n    this.globalLocation = new FormControl();\n    this.VendorBank = [];\n    this.vendorFilterCtrl = new FormControl();\n    this.vendorsBanks = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.cardDesc = '';\n    this.showBanner = false;\n    this.message = 'Update to latest version by pressing';\n    this.rolesList = [];\n    this.links = [];\n    this.filteredBranches = [];\n    // No hardcoded categories needed\n    this.categories = [];\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    this.globalLocation.setValue(this.user.restaurantAccess[0]);\n    this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    this.sharedData.getVersionNumber.subscribe(data => {\n      this.versionNumber = data;\n    });\n    this.sharedData.checkSettingAvailable.subscribe(data => {\n      this.enableSettingBtn = data;\n    });\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      if (this.versionNumber !== data['versionUI']) {\n        this.showBanner = true;\n        // this.notify.openSnackBar(\n        //   'Update to latest version by pressing CTL + SHIFT + R'\n        // );\n      } else {\n        this.showBanner = false;\n      }\n      this.cd.detectChanges();\n    });\n  }\n  ngOnInit() {\n    this.VendorBank = this.user.restaurantAccess.filter(branch => branch && branch.branchName);\n    this.vendorsBanks.next(this.VendorBank.slice());\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    this.selectedBranchesService.updateSelectedBranches(this.user.restaurantAccess);\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(newValue => {\n      this.vendorfilterBanks(newValue);\n    });\n  }\n  applyFilter(event) {\n    const filterValue = event.target.value;\n    this.filteredBranches = this.user.restaurantAccess.filter(branch => branch && branch.branchName.toLowerCase().includes(filterValue.toLowerCase()));\n  }\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n  vendorfilterBanks(newValue) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(this.VendorBank.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)));\n    } else {\n      const searchTerms = search.split(' ').filter(term => term.trim() !== '');\n      this.vendorsBanks.next(this.VendorBank.filter(branch => {\n        const branchNameLowerCase = branch.branchName.toLowerCase();\n        return searchTerms.every(term => branchNameLowerCase.includes(term));\n      }));\n    }\n  }\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n  static {\n    this.ɵfac = function DashboardToolbarComponent_Factory(t) {\n      return new (t || DashboardToolbarComponent)(i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardToolbarComponent,\n      selectors: [[\"app-dashboard-toolbar\"]],\n      viewQuery: function DashboardToolbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatSidenav, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allSelected = _t.first);\n        }\n      },\n      inputs: {\n        showNavbarToggleButton: \"showNavbarToggleButton\",\n        menuItems: \"menuItems\",\n        logoUrl: \"logoUrl\",\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      outputs: {\n        toggleMenu: \"toggleMenu\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 10,\n      consts: [[1, \"toolbar-left\"], [1, \"logo-container\"], [\"class\", \"logo-square\", 4, \"ngIf\"], [\"alt\", \"Company Logo\", \"class\", \"company-logo\", 3, \"src\", 4, \"ngIf\"], [1, \"nav-menu\"], [4, \"ngFor\", \"ngForOf\"], [1, \"example-spacer\"], [1, \"formal-text\"], [1, \"beta-tag\"], [1, \"user-info\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"], [\"xPosition\", \"before\"], [\"beforeMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-gear\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fa-solid\", \"fa-right-from-bracket\"], [1, \"logo-square\"], [1, \"logo-letter\"], [\"alt\", \"Company Logo\", 1, \"company-logo\", 3, \"src\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"]],\n      template: function DashboardToolbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵtemplate(3, DashboardToolbarComponent_div_3_Template, 3, 0, \"div\", 2);\n          i0.ɵɵtemplate(4, DashboardToolbarComponent_img_4_Template, 1, 1, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, DashboardToolbarComponent_ng_container_6_Template, 6, 3, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(7, \"span\", 6);\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementStart(10, \"span\", 8);\n          i0.ɵɵtext(11, \"Beta\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"span\", 12);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 13);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"mat-menu\", 14, 15)(23, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_23_listener() {\n            return ctx.setting();\n          });\n          i0.ɵɵelement(24, \"i\", 17);\n          i0.ɵɵtext(25, \" \\u00A0 Setting \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_26_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(27, \"i\", 19);\n          i0.ɵɵtext(28, \" \\u00A0 Logout \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r3 = i0.ɵɵreference(22);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"no-logo\", !ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.versionNumber, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.user == null ? null : ctx.user.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.cardDesc);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.enableSettingBtn);\n        }\n      },\n      dependencies: [FormsModule, ReactiveFormsModule, MatDialogModule, CommonModule, i6.NgForOf, i6.NgIf, MatIconModule, i7.MatIcon, MatButtonModule, i8.MatAnchor, i8.MatButton, MatFormFieldModule, MatToolbarModule, i9.MatToolbar, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatSelectModule, MatTooltipModule, MatCardModule, RouterModule, i3.RouterLink, i3.RouterLinkActive],\n      styles: [\"mat-toolbar[_ngcontent-%COMP%] {\\n  background-color: white; \\n\\n  color: #333;\\n  padding: 0 16px;\\n  height: 60px; \\n\\n  min-height: 60px;\\n  display: flex;\\n  align-items: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid #ff9100; \\n\\n  border-bottom: 1px solid #e0e0e0; \\n\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 16px;\\n  height: 100%;\\n  padding: 4px 0;\\n  min-width: 56px; \\n\\n}\\n\\n.logo-square[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background-color: #ff9100;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 12px;\\n}\\n\\n.logo-letter[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  font-weight: bold;\\n}\\n\\n.company-logo[_ngcontent-%COMP%] {\\n  height: 100%;\\n  max-height: 44px; \\n\\n  margin-right: 12px;\\n  object-fit: contain;\\n  width: auto;\\n}\\n\\n.app-name[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 8px; \\n\\n}\\n.nav-menu.no-logo[_ngcontent-%COMP%] {\\n  margin-left: 0; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  height: 60px; \\n\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0;\\n  color: #333;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  margin: 0 2px; \\n\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mat-mdc-button-touch-target {\\n  height: 100%;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #ff9100; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  background-color: rgba(255, 145, 0, 0.05);\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -1px; \\n\\n  left: 0;\\n  width: 100%;\\n  height: 2px; \\n\\n  background-color: #ff9100;\\n}\\n\\n\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n\\n  .mat-toolbar-row, .mat-toolbar-single-row[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n  height: 56px !important;\\n  max-height: 56px !important;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 10px;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0 12px;\\n  border-radius: 4px;\\n  transition: background-color 0.2s ease;\\n}\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.user-menu-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n  color: #ff9100; \\n\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  line-height: 1.2;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  opacity: 0.8;\\n}\\n\\n.formal-text[_ngcontent-%COMP%] {\\n  font-family: Arial, sans-serif;\\n  font-size: 14px;\\n  text-align: right;\\n  line-height: 1.5;\\n  margin: 0 16px;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n\\n.beta-tag[_ngcontent-%COMP%] {\\n  color: #ff9100;\\n  font-weight: bold;\\n  margin-left: 3px;\\n  font-size: 12px;\\n  background-color: rgba(255, 145, 0, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%]    > .mat-icon[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mdc-text-field--filled:not(.mdc-text-field--disabled) {\\n  background-color: #ebebeb;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%] {\\n  width: 215px;\\n  height: 45px;\\n  margin-bottom: 10px;\\n  margin-right: 5px;\\n}\\n\\n.globalSelectInput[_ngcontent-%COMP%] {\\n  height: 30px;\\n  padding: 10px;\\n}\\n\\n.mdc-text-field--no-label[_ngcontent-%COMP%]:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea)   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding-top: 14px;\\n  padding-bottom: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardToolbarComponent };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "MatIconModule", "MatButtonModule", "MatToolbarModule", "MatDialogModule", "DialogComponent", "MatMenuModule", "RouterModule", "MatSelectModule", "MatFormFieldModule", "FormControl", "FormsModule", "ReactiveFormsModule", "MatTooltipModule", "MatCardModule", "<PERSON><PERSON><PERSON><PERSON>", "ReplaySubject", "Subject", "debounceTime", "distinctUntilChanged", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "ctx_r1", "logoUrl", "ɵɵsanitizeUrl", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵadvance", "item_r4", "path", "ɵɵtextInterpolate", "icon", "title", "DashboardToolbarComponent", "constructor", "selectedBranchesService", "dialog", "router", "auth", "sharedData", "cd", "notify", "showNavbarToggleButton", "menuItems", "toggleMenu", "branchList", "branches", "globalLocation", "VendorBank", "vendorFilterCtrl", "vendorsBanks", "_onD<PERSON>roy", "cardDesc", "showBanner", "message", "rolesList", "links", "filteredBranches", "categories", "user", "getCurrentUser", "role", "setValue", "restaurantAccess", "setGlLocation", "getVersionNumber", "subscribe", "data", "versionNumber", "checkSettingAvailable", "enableSettingBtn", "getRolesList", "tenantId", "detectChanges", "ngOnInit", "filter", "branch", "branchName", "next", "slice", "pattern", "updateSelectedBranches", "valueChanges", "pipe", "newValue", "vendorfilterBanks", "applyFilter", "event", "filterValue", "target", "value", "toLowerCase", "includes", "setting", "navigate", "search", "replace", "searchTerms", "split", "term", "trim", "branchNameLowerCase", "every", "logout", "dialogRef", "open", "autoFocus", "afterClosed", "result", "localStorage", "clear", "sessionStorage", "window", "location", "reload", "restaurantChange", "ɵɵdirectiveInject", "i1", "ShareDataService", "i2", "MatDialog", "i3", "Router", "i4", "AuthService", "ChangeDetectorRef", "i5", "NotificationService", "selectors", "viewQuery", "DashboardToolbarComponent_Query", "rf", "ctx", "ɵɵtemplate", "DashboardToolbarComponent_div_3_Template", "DashboardToolbarComponent_img_4_Template", "DashboardToolbarComponent_ng_container_6_Template", "ɵɵlistener", "DashboardToolbarComponent_Template_button_click_23_listener", "DashboardToolbarComponent_Template_button_click_26_listener", "ɵɵclassProp", "ɵɵtextInterpolate1", "_r3", "name", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatIcon", "i8", "<PERSON><PERSON><PERSON><PERSON>", "MatButton", "i9", "MatToolbar", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "RouterLink", "RouterLinkActive", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  Output,\n  OnInit,\n  EventEmitter,\n  ViewChild,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIcon, MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { first } from 'rxjs';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatOption } from '@angular/material/core';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\n@Component({\n  selector: 'app-dashboard-toolbar',\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    CommonModule,\n    MatIconModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatToolbarModule,\n    MatMenuModule,\n    MatSelectModule,\n    MatTooltipModule,\n    MatCardModule,\n    RouterModule,\n  ],\n  templateUrl: './dashboard-toolbar.component.html',\n  styleUrls: ['./dashboard-toolbar.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardToolbarComponent {\n  user: any;\n  @Input() showNavbarToggleButton = false;\n  @Input() menuItems: any[] = [];\n  @Input() logoUrl: string = '';\n  @Output() toggleMenu = new EventEmitter();\n  public branchList = [];\n  public branches = new FormControl('');\n  public globalLocation: FormControl = new FormControl();\n  public VendorBank: any[] = [];\n  public vendorFilterCtrl: FormControl = new FormControl();\n  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  protected _onDestroy = new Subject<void>();\n  cardDesc: string = '';\n  enableSettingBtn: boolean;\n  @ViewChild(MatSidenav)\n  sidenav!: MatSidenav;\n  @Input() showBanner: boolean = false;\n  @ViewChild('allSelected') private allSelected: MatOption;\n  @Input() message: string = 'Update to latest version by pressing';\n  versionNumber: string;\n  rolesList: any = [];\n  links: any = [];\n  access: any;\n  filteredBranches: any[] = [];\n\n  // No hardcoded categories needed\n  categories = [];\n  constructor(\n    private selectedBranchesService: ShareDataService,\n    private dialog: MatDialog,\n    private router: Router,\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService\n  ) {\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    this.globalLocation.setValue(this.user.restaurantAccess[0]);\n    this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    this.sharedData.getVersionNumber.subscribe((data) => {\n      this.versionNumber = data;\n    });\n    this.sharedData.checkSettingAvailable.subscribe((data) => {\n      this.enableSettingBtn = data;\n    });\n    this.auth\n      .getRolesList({ tenantId: this.user.tenantId })\n      .subscribe((data) => {\n        if (this.versionNumber !== data['versionUI']) {\n          this.showBanner = true;\n          // this.notify.openSnackBar(\n          //   'Update to latest version by pressing CTL + SHIFT + R'\n          // );\n        } else {\n          this.showBanner = false;\n        }\n        this.cd.detectChanges();\n      });\n  }\n  ngOnInit() {\n    this.VendorBank = this.user.restaurantAccess.filter(\n      (branch) => branch && branch.branchName\n    );\n    this.vendorsBanks.next(this.VendorBank.slice());\n    this.vendorFilterCtrl = new FormControl(\n      '',\n      Validators.pattern('[a-zA-Z0-9\\\\s]*')\n    );\n    this.selectedBranchesService.updateSelectedBranches(\n      this.user.restaurantAccess\n    );\n    this.vendorFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged()\n      )\n      .subscribe((newValue) => {\n        this.vendorfilterBanks(newValue);\n      });\n  }\n\n\n\n  applyFilter(event: Event) {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.filteredBranches = this.user.restaurantAccess.filter(\n      (branch) =>\n        branch &&\n        branch.branchName.toLowerCase().includes(filterValue.toLowerCase())\n    );\n  }\n\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n\n  protected vendorfilterBanks(newValue?: unknown) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) =>\n          branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)\n        )\n      );\n    } else {\n      const searchTerms = search\n        .split(' ')\n        .filter((term) => term.trim() !== '');\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) => {\n          const branchNameLowerCase = branch.branchName.toLowerCase();\n          return searchTerms.every((term) =>\n            branchNameLowerCase.includes(term)\n          );\n        })\n      );\n    }\n  }\n\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout',\n      },\n    });\n\n    dialogRef.afterClosed().subscribe((result) => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n}\n", "<mat-toolbar>\n  <div class=\"toolbar-left\">\n    <div class=\"logo-container\">\n      <div class=\"logo-square\" *ngIf=\"!logoUrl\">\n        <div class=\"logo-letter\">D</div>\n      </div>\n      <img *ngIf=\"logoUrl\" [src]=\"logoUrl\" alt=\"Company Logo\" class=\"company-logo\">\n    </div>\n\n    <!-- Main Navigation Menu -->\n    <div class=\"nav-menu\" [class.no-logo]=\"!logoUrl\">\n      <ng-container *ngFor=\"let item of menuItems\">\n        <a mat-button [routerLink]=\"item.path\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>{{item.icon}}</mat-icon>\n          <span>{{item.title}}</span>\n        </a>\n      </ng-container>\n    </div>\n  </div>\n\n  <span class=\"example-spacer\"></span>\n\n  <p class=\"formal-text\">{{ versionNumber }} <span class=\"beta-tag\">Beta</span></p>\n  <div class=\"user-info\">\n    <button mat-button [matMenuTriggerFor]=\"beforeMenu\" class=\"user-menu-button\">\n      <mat-icon>account_circle</mat-icon>\n      <div class=\"user-details\">\n        <span class=\"user-name\">{{ user?.name }}</span>\n        <span class=\"user-role\">{{ cardDesc }}</span>\n      </div>\n    </button>\n  </div>\n\n  <mat-menu #beforeMenu=\"matMenu\" xPosition=\"before\">\n    <button mat-menu-item (click)=\"setting()\" [disabled]=\"!enableSettingBtn\">\n      <i class=\"fa-solid fa-gear\"></i> &nbsp; Setting\n    </button>\n    <button mat-menu-item (click)=\"logout()\">\n      <i class=\"fa-solid fa-right-from-bracket\"></i> &nbsp; Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>"], "mappings": "AAAA,SAMEA,YAAY,QAGP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAkBC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAoBC,eAAe,QAAQ,0BAA0B;AAErE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAG9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAItD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,oBAAoB,QAAmB,gBAAgB;AAC9E,SAASC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;IC7BrCC,EAAA,CAAAC,cAAA,cAA0C;IACfD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAElCH,EAAA,CAAAI,SAAA,cAA6E;;;;IAAxDJ,EAAA,CAAAK,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAP,EAAA,CAAAQ,aAAA,CAAe;;;;;IAKpCR,EAAA,CAAAS,uBAAA,GAA6C;IAC3CT,EAAA,CAAAC,cAAA,YAAkF;IACtED,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/BH,EAAA,CAAAU,qBAAA,EAAe;;;;IAJCV,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAK,UAAA,eAAAO,OAAA,CAAAC,IAAA,CAAwB;IAC1Bb,EAAA,CAAAW,SAAA,GAAa;IAAbX,EAAA,CAAAc,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAa;IACjBf,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAc,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAc;;;ADmB9B,MAsBaC,yBAAyB;EA4BpCC,YACUC,uBAAyC,EACzCC,MAAiB,EACjBC,MAAc,EACdC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B;IAN3B,KAAAN,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IAjCP,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAApB,OAAO,GAAW,EAAE;IACnB,KAAAqB,UAAU,GAAG,IAAIlD,YAAY,EAAE;IAClC,KAAAmD,UAAU,GAAG,EAAE;IACf,KAAAC,QAAQ,GAAG,IAAIzC,WAAW,CAAC,EAAE,CAAC;IAC9B,KAAA0C,cAAc,GAAgB,IAAI1C,WAAW,EAAE;IAC/C,KAAA2C,UAAU,GAAU,EAAE;IACtB,KAAAC,gBAAgB,GAAgB,IAAI5C,WAAW,EAAE;IACjD,KAAA6C,YAAY,GAAyB,IAAIvC,aAAa,CAAQ,CAAC,CAAC;IAC7D,KAAAwC,UAAU,GAAG,IAAIvC,OAAO,EAAQ;IAC1C,KAAAwC,QAAQ,GAAW,EAAE;IAIZ,KAAAC,UAAU,GAAY,KAAK;IAE3B,KAAAC,OAAO,GAAW,sCAAsC;IAEjE,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,KAAK,GAAQ,EAAE;IAEf,KAAAC,gBAAgB,GAAU,EAAE;IAE5B;IACA,KAAAC,UAAU,GAAG,EAAE;IAUb,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,IAAI,CAACsB,cAAc,EAAE;IACtC,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACO,IAAI,CAACE,IAAI;IAC/B,IAAI,CAACd,cAAc,CAACe,QAAQ,CAAC,IAAI,CAACH,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACxB,UAAU,CAACyB,aAAa,CAAC,IAAI,CAACL,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACxB,UAAU,CAAC0B,gBAAgB,CAACC,SAAS,CAAEC,IAAI,IAAI;MAClD,IAAI,CAACC,aAAa,GAAGD,IAAI;IAC3B,CAAC,CAAC;IACF,IAAI,CAAC5B,UAAU,CAAC8B,qBAAqB,CAACH,SAAS,CAAEC,IAAI,IAAI;MACvD,IAAI,CAACG,gBAAgB,GAAGH,IAAI;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC7B,IAAI,CACNiC,YAAY,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACb,IAAI,CAACa;IAAQ,CAAE,CAAC,CAC9CN,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,IAAI,CAACC,aAAa,KAAKD,IAAI,CAAC,WAAW,CAAC,EAAE;QAC5C,IAAI,CAACd,UAAU,GAAG,IAAI;QACtB;QACA;QACA;OACD,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,KAAK;;MAEzB,IAAI,CAACb,EAAE,CAACiC,aAAa,EAAE;IACzB,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA;IACN,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAACW,IAAI,CAACI,gBAAgB,CAACY,MAAM,CAChDC,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAACC,UAAU,CACxC;IACD,IAAI,CAAC3B,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;IAC/C,IAAI,CAAC9B,gBAAgB,GAAG,IAAI5C,WAAW,CACrC,EAAE,EACFU,UAAU,CAACiE,OAAO,CAAC,iBAAiB,CAAC,CACtC;IACD,IAAI,CAAC7C,uBAAuB,CAAC8C,sBAAsB,CACjD,IAAI,CAACtB,IAAI,CAACI,gBAAgB,CAC3B;IACD,IAAI,CAACd,gBAAgB,CAACiC,YAAY,CAC/BC,IAAI,CACHtE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACAoD,SAAS,CAAEkB,QAAQ,IAAI;MACtB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC;EACN;EAIAE,WAAWA,CAACC,KAAY;IACtB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK;IAC5D,IAAI,CAACjC,gBAAgB,GAAG,IAAI,CAACE,IAAI,CAACI,gBAAgB,CAACY,MAAM,CACtDC,MAAM,IACLA,MAAM,IACNA,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE,CAACC,QAAQ,CAACJ,WAAW,CAACG,WAAW,EAAE,CAAC,CACtE;EACH;EAEAE,OAAOA,CAAA;IACL,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEUT,iBAAiBA,CAACD,QAAkB;IAC5C,IAAI,CAAC,IAAI,CAACpC,UAAU,EAAE;MACpB;;IAEF,IAAI+C,MAAM,GAAG,IAAI,CAAC9C,gBAAgB,CAACyC,KAAK;IACxC,IAAI,CAACK,MAAM,EAAE;MACX,IAAI,CAAC7C,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;MAC/C;KACD,MAAM;MACLgB,MAAM,GAAGA,MAAM,CAACJ,WAAW,EAAE;;IAE/B,IAAI,CAACI,MAAM,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC1C,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAC5BA,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACJ,QAAQ,CAACG,MAAM,CAAC,CACpE,CACF;KACF,MAAM;MACL,MAAME,WAAW,GAAGF,MAAM,CACvBG,KAAK,CAAC,GAAG,CAAC,CACVvB,MAAM,CAAEwB,IAAI,IAAKA,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACvC,IAAI,CAAClD,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAAI;QAChC,MAAMyB,mBAAmB,GAAGzB,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE;QAC3D,OAAOM,WAAW,CAACK,KAAK,CAAEH,IAAI,IAC5BE,mBAAmB,CAACT,QAAQ,CAACO,IAAI,CAAC,CACnC;MACH,CAAC,CAAC,CACH;;EAEL;EAEAI,MAAMA,CAAA;IACJ,MAAMC,SAAS,GAAG,IAAI,CAACpE,MAAM,CAACqE,IAAI,CAACzG,eAAe,EAAE;MAClD0G,SAAS,EAAE,KAAK;MAChBvC,IAAI,EAAE;QACJb,OAAO,EAAE,kCAAkC;QAC3CtB,KAAK,EAAE;;KAEV,CAAC;IAEFwE,SAAS,CAACG,WAAW,EAAE,CAACzC,SAAS,CAAE0C,MAAM,IAAI;MAC3C,IAAIA,MAAM,EAAE;QACVC,YAAY,CAACC,KAAK,EAAE;QACpBC,cAAc,CAACD,KAAK,EAAE;QACtBE,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QACxB,IAAI,CAAC7E,MAAM,CAACyD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;IAErC,CAAC,CAAC;EACJ;EAEAqB,gBAAgBA,CAAC5B,KAAK;IACpB,IAAI,CAAChD,UAAU,CAACyB,aAAa,CAACuB,KAAK,CAAC;EACtC;;;uBAvJWtD,yBAAyB,EAAAjB,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA1G,EAAA,CAAAoG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA5G,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAApG,EAAA,CAAA6G,iBAAA,GAAA7G,EAAA,CAAAoG,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzB9F,yBAAyB;MAAA+F,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAezBzH,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;UCtEvBM,EAAA,CAAAC,cAAA,kBAAa;UAGPD,EAAA,CAAAqH,UAAA,IAAAC,wCAAA,iBAEM;UACNtH,EAAA,CAAAqH,UAAA,IAAAE,wCAAA,iBAA6E;UAC/EvH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,aAAiD;UAC/CD,EAAA,CAAAqH,UAAA,IAAAG,iDAAA,0BAKe;UACjBxH,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAI,SAAA,cAAoC;UAEpCJ,EAAA,CAAAC,cAAA,WAAuB;UAAAD,EAAA,CAAAE,MAAA,GAAoB;UAAAF,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7EH,EAAA,CAAAC,cAAA,cAAuB;UAETD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnCH,EAAA,CAAAC,cAAA,eAA0B;UACAD,EAAA,CAAAE,MAAA,IAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/CH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,IAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKnDH,EAAA,CAAAC,cAAA,wBAAmD;UAC3BD,EAAA,CAAAyH,UAAA,mBAAAC,4DAAA;YAAA,OAASN,GAAA,CAAAvC,OAAA,EAAS;UAAA,EAAC;UACvC7E,EAAA,CAAAI,SAAA,aAAgC;UAACJ,EAAA,CAAAE,MAAA,wBACnC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAyH,UAAA,mBAAAE,4DAAA;YAAA,OAASP,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UACtCvF,EAAA,CAAAI,SAAA,aAA8C;UAACJ,EAAA,CAAAE,MAAA,uBACjD;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;UApCmBH,EAAA,CAAAW,SAAA,GAAc;UAAdX,EAAA,CAAAK,UAAA,UAAA+G,GAAA,CAAA7G,OAAA,CAAc;UAGlCP,EAAA,CAAAW,SAAA,GAAa;UAAbX,EAAA,CAAAK,UAAA,SAAA+G,GAAA,CAAA7G,OAAA,CAAa;UAICP,EAAA,CAAAW,SAAA,GAA0B;UAA1BX,EAAA,CAAA4H,WAAA,aAAAR,GAAA,CAAA7G,OAAA,CAA0B;UACfP,EAAA,CAAAW,SAAA,GAAY;UAAZX,EAAA,CAAAK,UAAA,YAAA+G,GAAA,CAAAzF,SAAA,CAAY;UAWxB3B,EAAA,CAAAW,SAAA,GAAoB;UAApBX,EAAA,CAAA6H,kBAAA,KAAAT,GAAA,CAAAhE,aAAA,MAAoB;UAEtBpD,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAK,UAAA,sBAAAyH,GAAA,CAAgC;UAGvB9H,EAAA,CAAAW,SAAA,GAAgB;UAAhBX,EAAA,CAAAc,iBAAA,CAAAsG,GAAA,CAAAzE,IAAA,kBAAAyE,GAAA,CAAAzE,IAAA,CAAAoF,IAAA,CAAgB;UAChB/H,EAAA,CAAAW,SAAA,GAAc;UAAdX,EAAA,CAAAc,iBAAA,CAAAsG,GAAA,CAAAhF,QAAA,CAAc;UAMApC,EAAA,CAAAW,SAAA,GAA8B;UAA9BX,EAAA,CAAAK,UAAA,cAAA+G,GAAA,CAAA9D,gBAAA,CAA8B;;;qBDGxEhE,WAAW,EACXC,mBAAmB,EACnBR,eAAe,EACfJ,YAAY,EAAAqJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZtJ,aAAa,EAAAuJ,EAAA,CAAAC,OAAA,EACbvJ,eAAe,EAAAwJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfnJ,kBAAkB,EAClBN,gBAAgB,EAAA0J,EAAA,CAAAC,UAAA,EAChBxJ,aAAa,EAAAyJ,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACb1J,eAAe,EACfK,gBAAgB,EAChBC,aAAa,EACbP,YAAY,EAAAuH,EAAA,CAAAqC,UAAA,EAAArC,EAAA,CAAAsC,gBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMHhI,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}