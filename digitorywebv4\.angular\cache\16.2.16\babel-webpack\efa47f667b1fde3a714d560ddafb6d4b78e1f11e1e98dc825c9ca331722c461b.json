{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SseService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n    this.eventSource = null;\n    this.messageSubject = new Subject();\n    this.connectionStatusSubject = new Subject();\n    // Cache for conversation history to prevent duplicate API calls\n    this.conversationHistoryCache = {};\n    // Flag to track if a request is in progress\n    this.requestInProgress = false;\n    // Timeout reference for auto-closing connections\n    this.connectionTimeout = null;\n  }\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect() {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId, query) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n    const responseSubject = new Subject();\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      this.eventSource = new EventSource(`${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`);\n      // Handle specific event types like in the HTML example\n      // Handle regular messages\n      this.eventSource.addEventListener('message', event => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                responseSubject.next(data.content);\n              }\n              break;\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n      // Handle connection close\n      this.eventSource.addEventListener('error', () => {\n        // Check if the connection is closed\n        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {\n          console.log('Connection closed');\n          this.eventSource = null;\n          this.requestInProgress = false;\n          // Clean up\n          if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n          }\n          // Complete the subject if not already completed\n          if (!responseSubject.closed) {\n            responseSubject.complete();\n          }\n        }\n      });\n      // Handle general errors\n      this.eventSource.addEventListener('error', event => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n        // Don't immediately close on all errors - some might be recoverable\n      });\n      // Handle connection close\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n      // Handle errors\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect() {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId, message) {\n    // Create a new message from the user with the current timestamp\n    const now = new Date();\n    const userMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: now\n    };\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n    // Create an initial bot message with a cursor\n    // Use a timestamp slightly after the user message to ensure correct ordering\n    const botTimestamp = new Date(now.getTime() + 100); // 100ms after user message\n    const initialBotMessage = {\n      id: botMessageId,\n      text: '',\n      sender: 'bot',\n      timestamp: botTimestamp\n    };\n    // Send the initial message\n    this.messageSubject.next(initialBotMessage);\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: chunk => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n        // Add to the full response\n        fullResponse += chunk;\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = fullResponse + '<span class=\"blinking-cursor\">|</span>';\n        // Update the bot message with the new content\n        const updatedMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n\n            this.messageSubject.next(completionMessage);\n          });\n        } else {\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage = {\n            id: botMessageId,\n            text: fullResponse,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n\n            this.messageSubject.next(completionMessage);\n          });\n        }\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: error => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n        // Emit a special message to indicate the response is complete\n        // This will be used to hide the loading indicator\n        const completionMessage = {\n          id: 'completion-' + botMessageId,\n          text: '',\n          sender: 'system',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(completionMessage);\n      }\n    });\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId, restaurantInfo) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` + `Location: ${restaurantInfo.location || 'Not specified'}\\n` + `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` + `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` + `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` + `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` + `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n  /**\n   * Get the message stream\n   */\n  get messages$() {\n    return this.messageSubject.asObservable();\n  }\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$() {\n    return this.connectionStatusSubject.asObservable();\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId) {\n    return this.http.get(`${this.baseUrl}llm/conversation_history?tenant_id=${tenantId}`).pipe(map(response => {\n      if (response && response.messages) {\n        // Convert backend messages to frontend ChatMessage format\n        return response.messages.map(msg => ({\n          id: msg.id ? msg.id.toString() : this.generateId(),\n          text: msg.content,\n          sender: msg.type === 'human' ? 'user' : 'bot',\n          timestamp: new Date(msg.created_at)\n        }));\n      }\n      return [];\n    }), catchError(error => {\n      console.error('Error fetching conversation history:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   * @param clearCache Whether to clear the cache (default: true)\n   * @param clearServer Whether to clear the server data (default: false)\n   */\n  clearConversationHistory(tenantId, clearCache = true, clearServer = false) {\n    // Clear the cache if requested\n    if (clearCache) {\n      delete this.conversationHistoryCache[tenantId];\n    }\n    // Clear the server data if requested\n    if (clearServer) {\n      return this.http.post(`${this.baseUrl}llm/clear_history?tenant_id=${tenantId}`, {}).pipe(catchError(error => {\n        console.error('Error clearing conversation history on server:', error);\n        return throwError(() => new Error('Failed to clear conversation history on server'));\n      }));\n    }\n    // If not clearing server data, just return success\n    return of({\n      status: 'ok',\n      message: 'Conversation history cleared in UI'\n    });\n  }\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   * @param addToStream Whether to add messages to the message stream\n   */\n  loadConversationHistory(tenantId, addToStream = true) {\n    // Check if we already have the conversation history in the cache\n    if (this.conversationHistoryCache[tenantId]) {\n      console.log('Using cached conversation history for tenant', tenantId);\n      const messages = this.conversationHistoryCache[tenantId];\n      // Add messages to the stream if addToStream is true\n      if (addToStream) {\n        messages.forEach(message => {\n          this.messageSubject.next(message);\n        });\n      }\n      return of(messages);\n    }\n    // If not in cache, fetch from server\n    return this.getConversationHistory(tenantId).pipe(map(messages => {\n      // Cache the conversation history\n      this.conversationHistoryCache[tenantId] = messages;\n      // Add messages to the stream if addToStream is true\n      if (addToStream) {\n        // Add each message to the stream\n        messages.forEach(message => {\n          this.messageSubject.next(message);\n        });\n      }\n      return messages;\n    }), catchError(error => {\n      console.error('Error loading conversation history:', error);\n      return of([]);\n    }));\n  }\n  static {\n    this.ɵfac = function SseService_Factory(t) {\n      return new (t || SseService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SseService,\n      factory: SseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SseService };", "map": {"version": 3, "names": ["Subject", "throwError", "of", "catchError", "map", "environment", "SseService", "constructor", "http", "baseUrl", "engineUrl", "eventSource", "messageSubject", "connectionStatusSubject", "conversation<PERSON><PERSON><PERSON><PERSON>ache", "requestInProgress", "connectionTimeout", "connect", "next", "connectionStatus$", "streamResponse", "tenantId", "query", "console", "log", "clearTimeout", "close", "setTimeout", "responseSubject", "complete", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "timestamp", "Date", "getTime", "EventSource", "addEventListener", "event", "data", "JSON", "parse", "type", "content", "error", "Error", "warn", "readyState", "CLOSED", "closed", "customEvent", "onerror", "disconnect", "onopen", "asObservable", "sendMessage", "message", "now", "userMessage", "id", "generateId", "text", "sender", "botMessageId", "fullResponse", "botTimestamp", "initialBotMessage", "subscribe", "chunk", "trim", "textWithCursor", "updatedMessage", "Promise", "resolve", "then", "fallbackMessage", "completionMessage", "finalMessage", "errorMessage", "submitRestaurantInfo", "restaurantInfo", "infoSummary", "location", "businessType", "cuisineType", "operatingHours", "specialties", "join", "contactInfo", "messages$", "Math", "random", "toString", "substring", "getConversationHistory", "get", "pipe", "response", "messages", "msg", "created_at", "clearConversationHistory", "clearCache", "clearServer", "post", "status", "loadConversationHistory", "addToStream", "for<PERSON>ach", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/sse.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, Subject, throwError, of } from 'rxjs';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { ChatMessage } from '../models/chat-message.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SseService {\n  private baseUrl: string = environment.engineUrl;\n  private eventSource: EventSource | null = null;\n  private messageSubject = new Subject<ChatMessage>();\n  private connectionStatusSubject = new Subject<boolean>();\n\n  // Cache for conversation history to prevent duplicate API calls\n  private conversationHistoryCache: { [tenantId: string]: ChatMessage[] } = {};\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect(): Observable<boolean> {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n\n  // Flag to track if a request is in progress\n  private requestInProgress = false;\n\n  // Timeout reference for auto-closing connections\n  private connectionTimeout: any = null;\n\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId: string, query: string): Observable<string> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n\n    const responseSubject = new Subject<string>();\n\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      this.eventSource = new EventSource(`${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`);\n\n      // Handle specific event types like in the HTML example\n\n      // Handle regular messages\n      this.eventSource.addEventListener('message', (event) => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                responseSubject.next(data.content);\n              }\n              break;\n\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n\n      // Handle connection close\n      this.eventSource.addEventListener('error', () => {\n        // Check if the connection is closed\n        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {\n          console.log('Connection closed');\n          this.eventSource = null;\n          this.requestInProgress = false;\n\n          // Clean up\n          if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n          }\n\n          // Complete the subject if not already completed\n          if (!responseSubject.closed) {\n            responseSubject.complete();\n          }\n        }\n      });\n\n      // Handle general errors\n      this.eventSource.addEventListener('error', (event) => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event as any;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n\n        // Don't immediately close on all errors - some might be recoverable\n      });\n\n      // Handle connection close\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n\n      // Handle errors\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect(): void {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId: string, message: string): Observable<ChatMessage> {\n    // Create a new message from the user with the current timestamp\n    const now = new Date();\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: now\n    };\n\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n\n    // Create an initial bot message with a cursor\n    // Use a timestamp slightly after the user message to ensure correct ordering\n    const botTimestamp = new Date(now.getTime() + 100); // 100ms after user message\n    const initialBotMessage: ChatMessage = {\n      id: botMessageId,\n      text: '',\n      sender: 'bot',\n      timestamp: botTimestamp\n    };\n\n    // Send the initial message\n    this.messageSubject.next(initialBotMessage);\n\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: (chunk: string) => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n\n        // Add to the full response\n        fullResponse += chunk;\n\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = fullResponse + '<span class=\"blinking-cursor\">|</span>';\n\n        // Update the bot message with the new content\n        const updatedMessage: ChatMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage: ChatMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage: ChatMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n            this.messageSubject.next(completionMessage);\n          });\n        } else {\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage: ChatMessage = {\n            id: botMessageId,\n            text: fullResponse,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage: ChatMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n            this.messageSubject.next(completionMessage);\n          });\n        }\n\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: (error) => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage: ChatMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n\n        // Emit a special message to indicate the response is complete\n        // This will be used to hide the loading indicator\n        const completionMessage: ChatMessage = {\n          id: 'completion-' + botMessageId,\n          text: '',\n          sender: 'system',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(completionMessage);\n      }\n    });\n\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId: string, restaurantInfo: any): Observable<any> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` +\n      `Location: ${restaurantInfo.location || 'Not specified'}\\n` +\n      `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` +\n      `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` +\n      `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` +\n      `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` +\n      `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n\n  /**\n   * Get the message stream\n   */\n  get messages$(): Observable<ChatMessage> {\n    return this.messageSubject.asObservable();\n  }\n\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$(): Observable<boolean> {\n    return this.connectionStatusSubject.asObservable();\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId: string): Observable<ChatMessage[]> {\n    return this.http.get<any>(`${this.baseUrl}llm/conversation_history?tenant_id=${tenantId}`)\n      .pipe(\n        map(response => {\n          if (response && response.messages) {\n            // Convert backend messages to frontend ChatMessage format\n            return response.messages.map((msg: any) => ({\n              id: msg.id ? msg.id.toString() : this.generateId(),\n              text: msg.content,\n              sender: msg.type === 'human' ? 'user' : 'bot',\n              timestamp: new Date(msg.created_at)\n            }));\n          }\n          return [];\n        }),\n        catchError(error => {\n          console.error('Error fetching conversation history:', error);\n          return of([]);\n        })\n      );\n  }\n\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   * @param clearCache Whether to clear the cache (default: true)\n   * @param clearServer Whether to clear the server data (default: false)\n   */\n  clearConversationHistory(tenantId: string, clearCache: boolean = true, clearServer: boolean = false): Observable<any> {\n    // Clear the cache if requested\n    if (clearCache) {\n      delete this.conversationHistoryCache[tenantId];\n    }\n\n    // Clear the server data if requested\n    if (clearServer) {\n      return this.http.post<any>(`${this.baseUrl}llm/clear_history?tenant_id=${tenantId}`, {})\n        .pipe(\n          catchError(error => {\n            console.error('Error clearing conversation history on server:', error);\n            return throwError(() => new Error('Failed to clear conversation history on server'));\n          })\n        );\n    }\n\n    // If not clearing server data, just return success\n    return of({ status: 'ok', message: 'Conversation history cleared in UI' });\n  }\n\n\n\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   * @param addToStream Whether to add messages to the message stream\n   */\n  loadConversationHistory(tenantId: string, addToStream: boolean = true): Observable<ChatMessage[]> {\n    // Check if we already have the conversation history in the cache\n    if (this.conversationHistoryCache[tenantId]) {\n      console.log('Using cached conversation history for tenant', tenantId);\n      const messages = this.conversationHistoryCache[tenantId];\n\n      // Add messages to the stream if addToStream is true\n      if (addToStream) {\n        messages.forEach(message => {\n          this.messageSubject.next(message);\n        });\n      }\n\n      return of(messages);\n    }\n\n    // If not in cache, fetch from server\n    return this.getConversationHistory(tenantId).pipe(\n      map(messages => {\n        // Cache the conversation history\n        this.conversationHistoryCache[tenantId] = messages;\n\n        // Add messages to the stream if addToStream is true\n        if (addToStream) {\n          // Add each message to the stream\n          messages.forEach(message => {\n            this.messageSubject.next(message);\n          });\n        }\n        return messages;\n      }),\n      catchError(error => {\n        console.error('Error loading conversation history:', error);\n        return of([]);\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,OAAO,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAE1D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,UAAU;EASrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IARhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;IACvC,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAG,IAAIZ,OAAO,EAAe;IAC3C,KAAAa,uBAAuB,GAAG,IAAIb,OAAO,EAAW;IAExD;IACQ,KAAAc,wBAAwB,GAA0C,EAAE;IAc5E;IACQ,KAAAC,iBAAiB,GAAG,KAAK;IAEjC;IACQ,KAAAC,iBAAiB,GAAQ,IAAI;EAhBG;EAExC;;;EAGAC,OAAOA,CAAA;IACL;IACA;IACA,IAAI,CAACJ,uBAAuB,CAACK,IAAI,CAAC,IAAI,CAAC;IACvC,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAQA;;;;;EAKAC,cAAcA,CAACC,QAAgB,EAAEC,KAAa;IAC5C;IACA,IAAI,IAAI,CAACP,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,OAAOtB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAGjB;IACA,IAAI,CAACa,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACe,KAAK,EAAE;MACxB,IAAI,CAACf,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,CAACK,iBAAiB,GAAGW,UAAU,CAAC,MAAK;MACvCJ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI,IAAI,CAACb,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACe,KAAK,EAAE;QACxB,IAAI,CAACf,WAAW,GAAG,IAAI;;MAEzB,IAAI,CAACI,iBAAiB,GAAG,KAAK;MAC9Ba,eAAe,CAACC,QAAQ,EAAE;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX,MAAMD,eAAe,GAAG,IAAI5B,OAAO,EAAU;IAE7C,IAAI;MACF;MACA,MAAM8B,YAAY,GAAGC,kBAAkB,CAACT,KAAK,CAAC;MAC9C;MACA;MACA,MAAMU,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;MACtC,IAAI,CAACvB,WAAW,GAAG,IAAIwB,WAAW,CAAC,GAAG,IAAI,CAAC1B,OAAO,iBAAiBqB,YAAY,cAAcT,QAAQ,MAAMW,SAAS,EAAE,CAAC;MAEvH;MAEA;MACA,IAAI,CAACrB,WAAW,CAACyB,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAI;QACrD,IAAI;UACF;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEc,IAAI,CAAC;UAEtC;UACA,QAAQA,IAAI,CAACG,IAAI;YACf,KAAK,OAAO;cACVlB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC7B;YAEF,KAAK,OAAO;cACV;cACA,IAAIc,IAAI,CAACI,OAAO,EAAE;gBAChBd,eAAe,CAACV,IAAI,CAACoB,IAAI,CAACI,OAAO,CAAC;;cAEpC;YAEF,KAAK,KAAK;cACRnB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BI,eAAe,CAACC,QAAQ,EAAE;cAC1B;YAEF,KAAK,OAAO;cACVN,OAAO,CAACoB,KAAK,CAAC,eAAe,EAAEL,IAAI,CAACI,OAAO,CAAC;cAC5Cd,eAAe,CAACe,KAAK,CAAC,IAAIC,KAAK,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC;cAC9C;YAEF,KAAK,MAAM;cACT;cACA;YAEF;cACEnB,OAAO,CAACsB,IAAI,CAAC,uBAAuB,EAAEP,IAAI,CAACG,IAAI,CAAC;;SAErD,CAAC,OAAOE,KAAK,EAAE;UACdpB,OAAO,CAACoB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAEtD,CAAC,CAAC;MAEF;MACA,IAAI,CAAChC,WAAW,CAACyB,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC9C;QACA,IAAI,IAAI,CAACzB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACmC,UAAU,KAAKX,WAAW,CAACY,MAAM,EAAE;UAC1ExB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;UAChC,IAAI,CAACb,WAAW,GAAG,IAAI;UACvB,IAAI,CAACI,iBAAiB,GAAG,KAAK;UAE9B;UACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;YAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;YACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;UAG/B;UACA,IAAI,CAACY,eAAe,CAACoB,MAAM,EAAE;YAC3BpB,eAAe,CAACC,QAAQ,EAAE;;;MAGhC,CAAC,CAAC;MAEF;MACA,IAAI,CAAClB,WAAW,CAACyB,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;QACnDd,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEN,KAAK,CAAC;QACxC;QACA,MAAMY,WAAW,GAAGZ,KAAY;QAChC,IAAIY,WAAW,CAACX,IAAI,EAAE;UACpBf,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEM,WAAW,CAACX,IAAI,CAAC;;QAGhD;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC3B,WAAW,CAACuC,OAAO,GAAIP,KAAK,IAAI;QACnCpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAAChC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACmC,UAAU,KAAK,CAAC,EAAE;UACzDvB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C,IAAI,CAACX,uBAAuB,CAACK,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACsB,UAAU,EAAE;;MAErB,CAAC;MAED;MACA,IAAI,CAACxC,WAAW,CAACyC,MAAM,GAAG,MAAK;QAC7B7B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACX,uBAAuB,CAACK,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC;MAED;MACA,IAAI,CAACP,WAAW,CAACuC,OAAO,GAAIP,KAAK,IAAI;QACnCpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAAChC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACmC,UAAU,KAAK,CAAC,EAAE;UACzD,IAAI,CAACjC,uBAAuB,CAACK,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACsB,UAAU,EAAE;UACjB;UACA,IAAI,CAACpC,iBAAiB,GAAG,KAAK;;MAElC,CAAC;MAED,OAAOa,eAAe,CAACyB,YAAY,EAAE;KACtC,CAAC,OAAOV,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC9B,uBAAuB,CAACK,IAAI,CAAC,KAAK,CAAC;MACxC;MACA,IAAI,CAACH,iBAAiB,GAAG,KAAK;MAC9B,OAAOd,UAAU,CAAC,MAAM,IAAI2C,KAAK,CAAC,oCAAoC,CAAC,CAAC;;EAE5E;EAEA;;;EAGAO,UAAUA,CAAA;IACR,IAAI,IAAI,CAACxC,WAAW,EAAE;MACpBY,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,IAAI,CAACb,WAAW,CAACe,KAAK,EAAE;MACxB,IAAI,CAACf,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,uBAAuB,CAACK,IAAI,CAAC,KAAK,CAAC;;IAG1C;IACA,IAAI,IAAI,CAACF,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,CAACD,iBAAiB,GAAG,KAAK;EAChC;EAEA;;;;;EAKAuC,WAAWA,CAACjC,QAAgB,EAAEkC,OAAe;IAC3C;IACA,MAAMC,GAAG,GAAG,IAAIvB,IAAI,EAAE;IACtB,MAAMwB,WAAW,GAAgB;MAC/BC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,IAAI,EAAEL,OAAO;MACbM,MAAM,EAAE,MAAM;MACd7B,SAAS,EAAEwB;KACZ;IAED;IACA,IAAI,CAAC5C,cAAc,CAACM,IAAI,CAACuC,WAAW,CAAC;IAErC;IACA,MAAMK,YAAY,GAAG,IAAI,CAACH,UAAU,EAAE;IACtC,IAAII,YAAY,GAAG,EAAE;IAErB;IACA;IACA,MAAMC,YAAY,GAAG,IAAI/B,IAAI,CAACuB,GAAG,CAACtB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;IACpD,MAAM+B,iBAAiB,GAAgB;MACrCP,EAAE,EAAEI,YAAY;MAChBF,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,KAAK;MACb7B,SAAS,EAAEgC;KACZ;IAED;IACA,IAAI,CAACpD,cAAc,CAACM,IAAI,CAAC+C,iBAAiB,CAAC;IAE3C;IACA,IAAI,CAAC7C,cAAc,CAACC,QAAQ,EAAEkC,OAAO,CAAC,CAACW,SAAS,CAAC;MAC/ChD,IAAI,EAAGiD,KAAa,IAAI;QACtB;QACA,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,EAAE;QAEnB;QACAL,YAAY,IAAII,KAAK;QAErB;QACA,MAAME,cAAc,GAAGN,YAAY,GAAG,wCAAwC;QAE9E;QACA,MAAMO,cAAc,GAAgB;UAClCZ,EAAE,EAAEI,YAAY;UAChBF,IAAI,EAAES,cAAc;UACpBR,MAAM,EAAE,KAAK;UACb7B,SAAS,EAAE,IAAIC,IAAI;SACpB;QAED;QACAsC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;UAC1B;UACA,IAAI,CAAC7D,cAAc,CAACM,IAAI,CAACoD,cAAc,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDzC,QAAQ,EAAEA,CAAA,KAAK;QACbN,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA,IAAI,CAACuC,YAAY,CAACK,IAAI,EAAE,EAAE;UACxB,MAAMM,eAAe,GAAgB;YACnChB,EAAE,EAAEI,YAAY;YAChBF,IAAI,EAAE,8DAA8D;YACpEC,MAAM,EAAE,KAAK;YACb7B,SAAS,EAAE,IAAIC,IAAI;WACpB;UACDsC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC7D,cAAc,CAACM,IAAI,CAACwD,eAAe,CAAC;YAEzC;YACA;YACA,MAAMC,iBAAiB,GAAgB;cACrCjB,EAAE,EAAE,aAAa,GAAGI,YAAY;cAChCF,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE,QAAQ;cAChB7B,SAAS,EAAE,IAAIC,IAAI,CAAC+B,YAAY,CAAC9B,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;aACnD;;YACD,IAAI,CAACtB,cAAc,CAACM,IAAI,CAACyD,iBAAiB,CAAC;UAC7C,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMC,YAAY,GAAgB;YAChClB,EAAE,EAAEI,YAAY;YAChBF,IAAI,EAAEG,YAAY;YAClBF,MAAM,EAAE,KAAK;YACb7B,SAAS,EAAE,IAAIC,IAAI;WACpB;UACDsC,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC7D,cAAc,CAACM,IAAI,CAAC0D,YAAY,CAAC;YAEtC;YACA;YACA,MAAMD,iBAAiB,GAAgB;cACrCjB,EAAE,EAAE,aAAa,GAAGI,YAAY;cAChCF,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE,QAAQ;cAChB7B,SAAS,EAAE,IAAIC,IAAI,CAAC+B,YAAY,CAAC9B,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;aACnD;;YACD,IAAI,CAACtB,cAAc,CAACM,IAAI,CAACyD,iBAAiB,CAAC;UAC7C,CAAC,CAAC;;QAGJ;QACA,IAAI,IAAI,CAAChE,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,CAACe,KAAK,EAAE;UACxB,IAAI,CAACf,WAAW,GAAG,IAAI;;QAEzB,IAAI,CAACI,iBAAiB,GAAG,KAAK;QAE9B;QACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;UACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;MAEjC,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,MAAMkC,YAAY,GAAgB;UAChCnB,EAAE,EAAEI,YAAY;UAChBF,IAAI,EAAE,sEAAsE;UAC5EC,MAAM,EAAE,KAAK;UACb7B,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAACrB,cAAc,CAACM,IAAI,CAAC2D,YAAY,CAAC;QAEtC;QACA;QACA,MAAMF,iBAAiB,GAAgB;UACrCjB,EAAE,EAAE,aAAa,GAAGI,YAAY;UAChCF,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE,QAAQ;UAChB7B,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAACrB,cAAc,CAACM,IAAI,CAACyD,iBAAiB,CAAC;MAC7C;KACD,CAAC;IAEF;IACA,OAAOzE,EAAE,CAACuD,WAAW,CAAC;EACxB;EAEA;;;;;EAKAqB,oBAAoBA,CAACzD,QAAgB,EAAE0D,cAAmB;IACxD;IACA,IAAI,IAAI,CAAChE,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,OAAOtB,EAAE,CAAC,IAAI,CAAC;;IAGjB;IACA,MAAM8E,WAAW,GAAG,wDAAwD,GAC1E,aAAaD,cAAc,CAACE,QAAQ,IAAI,eAAe,IAAI,GAC3D,kBAAkBF,cAAc,CAACG,YAAY,IAAI,eAAe,IAAI,GACpE,iBAAiBH,cAAc,CAACI,WAAW,IAAI,eAAe,IAAI,GAClE,oBAAoBJ,cAAc,CAACK,cAAc,IAAI,eAAe,IAAI,GACxE,gBAAgBL,cAAc,CAACM,WAAW,GAAGN,cAAc,CAACM,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,IAAI,GACxG,iBAAiBP,cAAc,CAACQ,WAAW,IAAI,eAAe,EAAE;IAElE;IACA,OAAO,IAAI,CAACjC,WAAW,CAACjC,QAAQ,EAAE2D,WAAW,CAAC;EAChD;EAEA;;;EAGA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAAC5E,cAAc,CAACyC,YAAY,EAAE;EAC3C;EAEA;;;EAGA,IAAIlC,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACN,uBAAuB,CAACwC,YAAY,EAAE;EACpD;EAEA;;;EAGQM,UAAUA,CAAA;IAChB,OAAO8B,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;;EAIAC,sBAAsBA,CAACxE,QAAgB;IACrC,OAAO,IAAI,CAACb,IAAI,CAACsF,GAAG,CAAM,GAAG,IAAI,CAACrF,OAAO,sCAAsCY,QAAQ,EAAE,CAAC,CACvF0E,IAAI,CACH3F,GAAG,CAAC4F,QAAQ,IAAG;MACb,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;QACjC;QACA,OAAOD,QAAQ,CAACC,QAAQ,CAAC7F,GAAG,CAAE8F,GAAQ,KAAM;UAC1CxC,EAAE,EAAEwC,GAAG,CAACxC,EAAE,GAAGwC,GAAG,CAACxC,EAAE,CAACiC,QAAQ,EAAE,GAAG,IAAI,CAAChC,UAAU,EAAE;UAClDC,IAAI,EAAEsC,GAAG,CAACxD,OAAO;UACjBmB,MAAM,EAAEqC,GAAG,CAACzD,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK;UAC7CT,SAAS,EAAE,IAAIC,IAAI,CAACiE,GAAG,CAACC,UAAU;SACnC,CAAC,CAAC;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACFhG,UAAU,CAACwC,KAAK,IAAG;MACjBpB,OAAO,CAACoB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAOzC,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAkG,wBAAwBA,CAAC/E,QAAgB,EAAEgF,UAAA,GAAsB,IAAI,EAAEC,WAAA,GAAuB,KAAK;IACjG;IACA,IAAID,UAAU,EAAE;MACd,OAAO,IAAI,CAACvF,wBAAwB,CAACO,QAAQ,CAAC;;IAGhD;IACA,IAAIiF,WAAW,EAAE;MACf,OAAO,IAAI,CAAC9F,IAAI,CAAC+F,IAAI,CAAM,GAAG,IAAI,CAAC9F,OAAO,+BAA+BY,QAAQ,EAAE,EAAE,EAAE,CAAC,CACrF0E,IAAI,CACH5F,UAAU,CAACwC,KAAK,IAAG;QACjBpB,OAAO,CAACoB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,OAAO1C,UAAU,CAAC,MAAM,IAAI2C,KAAK,CAAC,gDAAgD,CAAC,CAAC;MACtF,CAAC,CAAC,CACH;;IAGL;IACA,OAAO1C,EAAE,CAAC;MAAEsG,MAAM,EAAE,IAAI;MAAEjD,OAAO,EAAE;IAAoC,CAAE,CAAC;EAC5E;EAIA;;;;;EAKAkD,uBAAuBA,CAACpF,QAAgB,EAAEqF,WAAA,GAAuB,IAAI;IACnE;IACA,IAAI,IAAI,CAAC5F,wBAAwB,CAACO,QAAQ,CAAC,EAAE;MAC3CE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEH,QAAQ,CAAC;MACrE,MAAM4E,QAAQ,GAAG,IAAI,CAACnF,wBAAwB,CAACO,QAAQ,CAAC;MAExD;MACA,IAAIqF,WAAW,EAAE;QACfT,QAAQ,CAACU,OAAO,CAACpD,OAAO,IAAG;UACzB,IAAI,CAAC3C,cAAc,CAACM,IAAI,CAACqC,OAAO,CAAC;QACnC,CAAC,CAAC;;MAGJ,OAAOrD,EAAE,CAAC+F,QAAQ,CAAC;;IAGrB;IACA,OAAO,IAAI,CAACJ,sBAAsB,CAACxE,QAAQ,CAAC,CAAC0E,IAAI,CAC/C3F,GAAG,CAAC6F,QAAQ,IAAG;MACb;MACA,IAAI,CAACnF,wBAAwB,CAACO,QAAQ,CAAC,GAAG4E,QAAQ;MAElD;MACA,IAAIS,WAAW,EAAE;QACf;QACAT,QAAQ,CAACU,OAAO,CAACpD,OAAO,IAAG;UACzB,IAAI,CAAC3C,cAAc,CAACM,IAAI,CAACqC,OAAO,CAAC;QACnC,CAAC,CAAC;;MAEJ,OAAO0C,QAAQ;IACjB,CAAC,CAAC,EACF9F,UAAU,CAACwC,KAAK,IAAG;MACjBpB,OAAO,CAACoB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOzC,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;;;uBA3fWI,UAAU,EAAAsG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVzG,UAAU;MAAA0G,OAAA,EAAV1G,UAAU,CAAA2G,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA;;SAEP5G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}