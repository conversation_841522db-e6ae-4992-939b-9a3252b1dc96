{"ast": null, "code": "// Code block (4 spaces padded)\n\n'use strict';\n\nmodule.exports = function code(state, startLine, endLine /*, silent*/) {\n  var nextLine, last, token;\n  if (state.sCount[startLine] - state.blkIndent < 4) {\n    return false;\n  }\n  last = nextLine = startLine + 1;\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      continue;\n    }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++;\n      last = nextLine;\n      continue;\n    }\n    break;\n  }\n  state.line = last;\n  token = state.push('code_block', 'code', 0);\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n';\n  token.map = [startLine, state.line];\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}