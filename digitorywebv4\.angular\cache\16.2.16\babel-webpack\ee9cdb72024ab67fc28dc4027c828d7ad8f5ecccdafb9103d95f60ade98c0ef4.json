{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/share-data.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/auth.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/toolbar\";\nimport * as i10 from \"@angular/material/menu\";\nconst _c0 = [\"allSelected\"];\nfunction DashboardToolbarComponent_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 20);\n    i0.ɵɵlistener(\"error\", function DashboardToolbarComponent_img_3_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.handleLogoError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", item_r8.path);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.title);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardToolbarComponent_ng_container_5_ng_container_1_Template, 6, 3, \"ng-container\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.menuItems)(\"ngForTrackBy\", ctx_r1.trackByPath);\n  }\n}\nfunction DashboardToolbarComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 23)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Dashboard\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 23)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"table_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Inventory Management\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"a\", 23)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"User Management\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"a\", 23)(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"fastfood\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Recipe Management\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"a\", 23)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"event_note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"Party Management\");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass DashboardToolbarComponent {\n  constructor(selectedBranchesService, dialog, router, auth, sharedData, cd, notify) {\n    this.selectedBranchesService = selectedBranchesService;\n    this.dialog = dialog;\n    this.router = router;\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.showNavbarToggleButton = false;\n    this.menuItems = null;\n    this.logoUrl = '';\n    this.toggleMenu = new EventEmitter();\n    this.branchList = [];\n    this.branches = new FormControl('');\n    this.globalLocation = new FormControl();\n    this.VendorBank = [];\n    this.vendorFilterCtrl = new FormControl();\n    this.vendorsBanks = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.cardDesc = '';\n    this.showBanner = false;\n    // No need to track if menu items are loaded - we use hardcoded placeholders\n    this.message = 'Update to latest version by pressing';\n    this.rolesList = [];\n    this.links = [];\n    this.filteredBranches = [];\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    if (this.user.restaurantAccess && this.user.restaurantAccess.length > 0) {\n      this.globalLocation.setValue(this.user.restaurantAccess[0]);\n      this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    }\n    this.sharedData.getVersionNumber.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this.versionNumber = data;\n      this.cd.markForCheck();\n    });\n    this.sharedData.checkSettingAvailable.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this.enableSettingBtn = data;\n      this.cd.markForCheck();\n    });\n  }\n  ngOnInit() {\n    // Initialize branch data\n    if (this.user && this.user.restaurantAccess) {\n      this.VendorBank = this.user.restaurantAccess.filter(branch => branch && branch.branchName);\n      this.vendorsBanks.next(this.VendorBank.slice());\n      this.selectedBranchesService.updateSelectedBranches(this.user.restaurantAccess);\n    }\n    // Initialize filter control\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    // Set up filter change subscription\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(newValue => {\n      this.vendorfilterBanks(newValue);\n    });\n    // Check if menu items are loaded\n    this.checkMenuItems();\n  }\n  // Simplified change detection\n  ngOnChanges(changes) {\n    // Always mark for check when menu items or logo changes\n    if (changes['menuItems'] || changes['logoUrl']) {\n      this.cd.markForCheck();\n    }\n  }\n  // Simplified method - no need to track loading state\n  checkMenuItems() {\n    // No complex logic needed - we use hardcoded placeholders\n    this.cd.markForCheck();\n  }\n  applyFilter(event) {\n    const filterValue = event.target.value;\n    this.filteredBranches = this.user.restaurantAccess.filter(branch => branch && branch.branchName.toLowerCase().includes(filterValue.toLowerCase()));\n  }\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n  vendorfilterBanks(newValue) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(this.VendorBank.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)));\n    } else {\n      const searchTerms = search.split(' ').filter(term => term.trim() !== '');\n      this.vendorsBanks.next(this.VendorBank.filter(branch => {\n        const branchNameLowerCase = branch.branchName.toLowerCase();\n        return searchTerms.every(term => branchNameLowerCase.includes(term));\n      }));\n    }\n  }\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n  /**\n   * Handle logo loading errors\n   */\n  handleLogoError(event) {\n    // Hide the broken image\n    event.target.style.display = 'none';\n    // Could set a default logo here if needed\n  }\n  /**\n   * Clean up subscriptions when component is destroyed\n   */\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  /**\n   * TrackBy function for menu items to optimize rendering\n   * This helps Angular identify which items have changed and only re-render those\n   */\n  trackByPath(_index, item) {\n    return item?.path || _index.toString();\n  }\n  static {\n    this.ɵfac = function DashboardToolbarComponent_Factory(t) {\n      return new (t || DashboardToolbarComponent)(i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardToolbarComponent,\n      selectors: [[\"app-dashboard-toolbar\"]],\n      viewQuery: function DashboardToolbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatSidenav, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allSelected = _t.first);\n        }\n      },\n      inputs: {\n        showNavbarToggleButton: \"showNavbarToggleButton\",\n        menuItems: \"menuItems\",\n        logoUrl: \"logoUrl\",\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      outputs: {\n        toggleMenu: \"toggleMenu\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 30,\n      vars: 8,\n      consts: [[1, \"toolbar-left\"], [1, \"logo-container\"], [\"alt\", \"Company Logo\", \"class\", \"company-logo\", 3, \"src\", \"error\", 4, \"ngIf\"], [1, \"nav-menu\"], [4, \"ngIf\", \"ngIfElse\"], [\"placeholderTabs\", \"\"], [1, \"example-spacer\"], [1, \"formal-text\"], [1, \"beta-tag\"], [1, \"user-info\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"], [\"xPosition\", \"before\"], [\"beforeMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-gear\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fa-solid\", \"fa-right-from-bracket\"], [\"alt\", \"Company Logo\", 1, \"company-logo\", 3, \"src\", \"error\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"], [\"mat-button\", \"\", 1, \"nav-item\", \"placeholder-tab\"]],\n      template: function DashboardToolbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵtemplate(3, DashboardToolbarComponent_img_3_Template, 1, 1, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtemplate(5, DashboardToolbarComponent_ng_container_5_Template, 2, 2, \"ng-container\", 4);\n          i0.ɵɵtemplate(6, DashboardToolbarComponent_ng_template_6_Template, 25, 0, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(8, \"span\", 6);\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementStart(11, \"span\", 8);\n          i0.ɵɵtext(12, \"Beta\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"button\", 10)(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"span\", 12);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 13);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"mat-menu\", 14, 15)(24, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_24_listener() {\n            return ctx.setting();\n          });\n          i0.ɵɵelement(25, \"i\", 17);\n          i0.ɵɵtext(26, \" \\u00A0 Setting \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_27_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(28, \"i\", 19);\n          i0.ɵɵtext(29, \" \\u00A0 Logout \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(7);\n          const _r4 = i0.ɵɵreference(23);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.menuItems == null ? null : ctx.menuItems.length) > 0)(\"ngIfElse\", _r2);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.versionNumber, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r4);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.user == null ? null : ctx.user.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.cardDesc);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.enableSettingBtn);\n        }\n      },\n      dependencies: [FormsModule, ReactiveFormsModule, MatDialogModule, CommonModule, i6.NgForOf, i6.NgIf, MatIconModule, i7.MatIcon, MatButtonModule, i8.MatAnchor, i8.MatButton, MatFormFieldModule, MatToolbarModule, i9.MatToolbar, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatSelectModule, MatTooltipModule, MatCardModule, RouterModule, i3.RouterLink, i3.RouterLinkActive],\n      styles: [\"mat-toolbar[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #2c3e50;\\n  padding: 0 20px;\\n  height: 44px;\\n  min-height: 44px;\\n  display: flex;\\n  align-items: flex-end;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  border-bottom: 1px solid #e2e8f0;\\n  position: relative;\\n  z-index: 1000;\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  flex: 1;\\n  gap: 0;\\n  height: 100%;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 16px;\\n  margin-left: 8px;\\n  height: 100%;\\n  min-width: 60px;\\n  flex-shrink: 0;\\n  align-self: center;\\n}\\n\\n.company-logo[_ngcontent-%COMP%] {\\n  height: 40px;\\n  max-height: 40px;\\n  width: auto;\\n  max-width: 150px;\\n  object-fit: contain;\\n  display: block;\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 1px;\\n  height: 100%;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  height: 34px;\\n  width: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 8px 8px 0 0;\\n  color: #475569;\\n  transition: all 0.2s ease;\\n  margin: 4px 0 2px 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-bottom: none;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mat-mdc-button-touch-target {\\n  height: 100%;\\n  width: 100%;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mdc-button__label {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  width: 100%;\\n  height: 100%;\\n  padding: 0 8px;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mat-mdc-button {\\n  padding: 0 !important;\\n  height: 34px !important;\\n  width: 100% !important;\\n  border-radius: 8px 8px 0 0 !important;\\n  min-width: 200px !important;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9100;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  transition: color 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: color 0.2s ease;\\n  flex-shrink: 1;\\n  min-width: 0;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n  white-space: nowrap;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background: #fff;\\n  color: #1e293b;\\n  border-color: #ff9100;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%] {\\n  color: #e55a2b;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0f172a;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #1e293b;\\n  border-color: #ff9100;\\n  border-bottom: 1px solid #ffffff;\\n  z-index: 10;\\n  position: relative;\\n  margin: 4px 0 -1px 0;\\n  height: 35px;\\n  box-shadow: 0 1px 3px rgba(255, 145, 0, 0.1);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6f00;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: default;\\n  pointer-events: none;\\n  background-color: #f1f5f9;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: rgba(255, 145, 0, 0.5);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #94a3b8;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]:hover {\\n  background-color: #f1f5f9;\\n  border-color: #e2e8f0;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%] {\\n  color: rgba(255, 145, 0, 0.5);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]:hover   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n  .mat-toolbar-row, .mat-toolbar-single-row[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n  height: 44px !important;\\n  max-height: 44px !important;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 8px;\\n  align-self: center;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n  height: 36px;\\n  background-color: rgba(255, 145, 0, 0.05);\\n  border: 1px solid rgba(255, 145, 0, 0.1);\\n}\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.1);\\n  border-color: rgba(255, 145, 0, 0.2);\\n}\\n.user-menu-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n  color: #ff9100;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  line-height: 1.1;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.formal-text[_ngcontent-%COMP%] {\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n  font-size: 12px;\\n  text-align: right;\\n  line-height: 1.4;\\n  margin: 0 16px 0 8px;\\n  color: #64748b;\\n  white-space: nowrap;\\n  font-weight: 500;\\n  align-self: center;\\n}\\n\\n.beta-tag[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  font-weight: 600;\\n  margin-left: 4px;\\n  font-size: 10px;\\n  background: linear-gradient(135deg, #ff9100, #ff6f00);\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  box-shadow: 0 1px 2px rgba(255, 145, 0, 0.3);\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%]    > .mat-icon[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mdc-text-field--filled:not(.mdc-text-field--disabled) {\\n  background-color: #ebebeb;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%] {\\n  width: 215px;\\n  height: 45px;\\n  margin-bottom: 10px;\\n  margin-right: 5px;\\n}\\n\\n.globalSelectInput[_ngcontent-%COMP%] {\\n  height: 30px;\\n  padding: 10px;\\n}\\n\\n.mdc-text-field--no-label[_ngcontent-%COMP%]:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea)   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding-top: 14px;\\n  padding-bottom: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardToolbarComponent };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "MatIconModule", "MatButtonModule", "MatToolbarModule", "MatDialogModule", "DialogComponent", "MatMenuModule", "RouterModule", "MatSelectModule", "MatFormFieldModule", "FormControl", "FormsModule", "ReactiveFormsModule", "MatTooltipModule", "MatCardModule", "<PERSON><PERSON><PERSON><PERSON>", "ReplaySubject", "Subject", "debounceTime", "distinctUntilChanged", "takeUntil", "Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "DashboardToolbarComponent_img_3_Template_img_error_0_listener", "$event", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "handleLogoError", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "logoUrl", "ɵɵsanitizeUrl", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "item_r8", "path", "ɵɵtextInterpolate", "icon", "title", "ɵɵtemplate", "DashboardToolbarComponent_ng_container_5_ng_container_1_Template", "ctx_r1", "menuItems", "trackByPath", "DashboardToolbarComponent", "constructor", "selectedBranchesService", "dialog", "router", "auth", "sharedData", "cd", "notify", "showNavbarToggleButton", "toggleMenu", "branchList", "branches", "globalLocation", "VendorBank", "vendorFilterCtrl", "vendorsBanks", "_onD<PERSON>roy", "cardDesc", "showBanner", "message", "rolesList", "links", "filteredBranches", "user", "getCurrentUser", "role", "restaurantAccess", "length", "setValue", "setGlLocation", "getVersionNumber", "pipe", "subscribe", "data", "versionNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkSettingAvailable", "enableSettingBtn", "ngOnInit", "filter", "branch", "branchName", "next", "slice", "updateSelectedBranches", "pattern", "valueChanges", "newValue", "vendorfilterBanks", "checkMenuItems", "ngOnChanges", "changes", "applyFilter", "event", "filterValue", "target", "value", "toLowerCase", "includes", "setting", "navigate", "search", "replace", "searchTerms", "split", "term", "trim", "branchNameLowerCase", "every", "logout", "dialogRef", "open", "autoFocus", "afterClosed", "result", "localStorage", "clear", "sessionStorage", "window", "location", "reload", "restaurantChange", "style", "display", "ngOnDestroy", "complete", "_index", "item", "toString", "ɵɵdirectiveInject", "i1", "ShareDataService", "i2", "MatDialog", "i3", "Router", "i4", "AuthService", "ChangeDetectorRef", "i5", "NotificationService", "selectors", "viewQuery", "DashboardToolbarComponent_Query", "rf", "ctx", "DashboardToolbarComponent_img_3_Template", "DashboardToolbarComponent_ng_container_5_Template", "DashboardToolbarComponent_ng_template_6_Template", "ɵɵtemplateRefExtractor", "ɵɵelement", "DashboardToolbarComponent_Template_button_click_24_listener", "DashboardToolbarComponent_Template_button_click_27_listener", "_r2", "ɵɵtextInterpolate1", "_r4", "name", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatIcon", "i8", "<PERSON><PERSON><PERSON><PERSON>", "MatButton", "i9", "MatToolbar", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "RouterLink", "RouterLinkActive", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  Output,\n  OnInit,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  EventEmitter,\n  ViewChild,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIcon, MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { first } from 'rxjs';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatOption } from '@angular/material/core';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\n@Component({\n  selector: 'app-dashboard-toolbar',\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    CommonModule,\n    MatIconModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatToolbarModule,\n    MatMenuModule,\n    MatSelectModule,\n    MatTooltipModule,\n    MatCardModule,\n    RouterModule,\n  ],\n  templateUrl: './dashboard-toolbar.component.html',\n  styleUrls: ['./dashboard-toolbar.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardToolbarComponent implements OnInit, OnChanges, OnDestroy {\n  user: any;\n  @Input() showNavbarToggleButton = false;\n  @Input() menuItems: any[] = null;\n  @Input() logoUrl: string = '';\n  @Output() toggleMenu = new EventEmitter();\n  public branchList = [];\n  public branches = new FormControl('');\n  public globalLocation: FormControl = new FormControl();\n  public VendorBank: any[] = [];\n  public vendorFilterCtrl: FormControl = new FormControl();\n  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  protected _onDestroy = new Subject<void>();\n  cardDesc: string = '';\n  enableSettingBtn: boolean;\n  @ViewChild(MatSidenav)\n  sidenav!: MatSidenav;\n  @Input() showBanner: boolean = false;\n  @ViewChild('allSelected') private allSelected: MatOption;\n\n  // No need to track if menu items are loaded - we use hardcoded placeholders\n  @Input() message: string = 'Update to latest version by pressing';\n  versionNumber: string;\n  rolesList: any = [];\n  links: any = [];\n  access: any;\n  filteredBranches: any[] = [];\n\n  constructor(\n    private selectedBranchesService: ShareDataService,\n    private dialog: MatDialog,\n    private router: Router,\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService\n  ) {\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n\n    if (this.user.restaurantAccess && this.user.restaurantAccess.length > 0) {\n      this.globalLocation.setValue(this.user.restaurantAccess[0]);\n      this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    }\n\n    this.sharedData.getVersionNumber\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe((data) => {\n        this.versionNumber = data;\n        this.cd.markForCheck();\n      });\n\n    this.sharedData.checkSettingAvailable\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe((data) => {\n        this.enableSettingBtn = data;\n        this.cd.markForCheck();\n      });\n  }\n  ngOnInit() {\n    // Initialize branch data\n    if (this.user && this.user.restaurantAccess) {\n      this.VendorBank = this.user.restaurantAccess.filter(\n        (branch: any) => branch && branch.branchName\n      );\n      this.vendorsBanks.next(this.VendorBank.slice());\n      this.selectedBranchesService.updateSelectedBranches(\n        this.user.restaurantAccess\n      );\n    }\n\n    // Initialize filter control\n    this.vendorFilterCtrl = new FormControl(\n      '',\n      Validators.pattern('[a-zA-Z0-9\\\\s]*')\n    );\n\n    // Set up filter change subscription\n    this.vendorFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged()\n      )\n      .subscribe((newValue) => {\n        this.vendorfilterBanks(newValue);\n      });\n\n    // Check if menu items are loaded\n    this.checkMenuItems();\n  }\n\n  // Simplified change detection\n  ngOnChanges(changes: SimpleChanges): void {\n    // Always mark for check when menu items or logo changes\n    if (changes['menuItems'] || changes['logoUrl']) {\n      this.cd.markForCheck();\n    }\n  }\n\n  // Simplified method - no need to track loading state\n  checkMenuItems() {\n    // No complex logic needed - we use hardcoded placeholders\n    this.cd.markForCheck();\n  }\n\n\n\n  applyFilter(event: Event) {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.filteredBranches = this.user.restaurantAccess.filter(\n      (branch) =>\n        branch &&\n        branch.branchName.toLowerCase().includes(filterValue.toLowerCase())\n    );\n  }\n\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n\n  protected vendorfilterBanks(newValue?: unknown) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) =>\n          branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)\n        )\n      );\n    } else {\n      const searchTerms = search\n        .split(' ')\n        .filter((term) => term.trim() !== '');\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) => {\n          const branchNameLowerCase = branch.branchName.toLowerCase();\n          return searchTerms.every((term) =>\n            branchNameLowerCase.includes(term)\n          );\n        })\n      );\n    }\n  }\n\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout',\n      },\n    });\n\n    dialogRef.afterClosed().subscribe((result) => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n\n  /**\n   * Handle logo loading errors\n   */\n  handleLogoError(event: any) {\n    // Hide the broken image\n    event.target.style.display = 'none';\n    // Could set a default logo here if needed\n  }\n\n  /**\n   * Clean up subscriptions when component is destroyed\n   */\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n\n  /**\n   * TrackBy function for menu items to optimize rendering\n   * This helps Angular identify which items have changed and only re-render those\n   */\n  trackByPath(_index: number, item: any): string {\n    return item?.path || _index.toString();\n  }\n}\n", "<mat-toolbar>\n  <div class=\"toolbar-left\">\n    <div class=\"logo-container\">\n      <img *ngIf=\"logoUrl\" [src]=\"logoUrl\" alt=\"Company Logo\" class=\"company-logo\" (error)=\"handleLogoError($event)\">\n    </div>\n\n    <!-- Main Navigation Menu -->\n    <div class=\"nav-menu\">\n      <!-- Use ngIf/else to ensure only one set of tabs is shown -->\n      <ng-container *ngIf=\"menuItems?.length > 0; else placeholderTabs\">\n        <ng-container *ngFor=\"let item of menuItems; trackBy: trackByPath\">\n          <a mat-button [routerLink]=\"item.path\" routerLinkActive=\"active\" class=\"nav-item\">\n            <mat-icon>{{item.icon}}</mat-icon>\n            <span>{{item.title}}</span>\n          </a>\n        </ng-container>\n      </ng-container>\n\n      <!-- Template for placeholder tabs -->\n      <ng-template #placeholderTabs>\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>dashboard</mat-icon>\n          <span>Dashboard</span>\n        </a>\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>table_chart</mat-icon>\n          <span>Inventory Management</span>\n        </a>\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>person</mat-icon>\n          <span>User Management</span>\n        </a>\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>fastfood</mat-icon>\n          <span>Recipe Management</span>\n        </a>\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>event_note</mat-icon>\n          <span>Party Management</span>\n        </a>\n      </ng-template>\n    </div>\n  </div>\n\n  <span class=\"example-spacer\"></span>\n\n  <p class=\"formal-text\">{{ versionNumber }} <span class=\"beta-tag\">Beta</span></p>\n  <div class=\"user-info\">\n    <button mat-button [matMenuTriggerFor]=\"beforeMenu\" class=\"user-menu-button\">\n      <mat-icon>account_circle</mat-icon>\n      <div class=\"user-details\">\n        <span class=\"user-name\">{{ user?.name }}</span>\n        <span class=\"user-role\">{{ cardDesc }}</span>\n      </div>\n    </button>\n  </div>\n\n  <mat-menu #beforeMenu=\"matMenu\" xPosition=\"before\">\n    <button mat-menu-item (click)=\"setting()\" [disabled]=\"!enableSettingBtn\">\n      <i class=\"fa-solid fa-gear\"></i> &nbsp; Setting\n    </button>\n    <button mat-menu-item (click)=\"logout()\">\n      <i class=\"fa-solid fa-right-from-bracket\"></i> &nbsp; Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>"], "mappings": "AAAA,SASEA,YAAY,QAGP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAkBC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAoBC,eAAe,QAAQ,0BAA0B;AAErE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAG9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAItD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IChCrCC,EAAA,CAAAC,cAAA,cAA+G;IAAlCD,EAAA,CAAAE,UAAA,mBAAAC,8DAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAA9GJ,EAAA,CAAAW,YAAA,EAA+G;;;;IAA1FX,EAAA,CAAAY,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAd,EAAA,CAAAe,aAAA,CAAe;;;;;IAOlCf,EAAA,CAAAgB,uBAAA,GAAmE;IACjEhB,EAAA,CAAAC,cAAA,YAAkF;IACtED,EAAA,CAAAiB,MAAA,GAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAClCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,GAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAE/BX,EAAA,CAAAkB,qBAAA,EAAe;;;;IAJClB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAY,UAAA,eAAAQ,OAAA,CAAAC,IAAA,CAAwB;IAC1BrB,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAsB,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAa;IACjBvB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAAsB,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAc;;;;;IAJ1BxB,EAAA,CAAAgB,uBAAA,GAAkE;IAChEhB,EAAA,CAAAyB,UAAA,IAAAC,gEAAA,2BAKe;IACjB1B,EAAA,CAAAkB,qBAAA,EAAe;;;;IANkBlB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAAY,UAAA,YAAAe,MAAA,CAAAC,SAAA,CAAc,iBAAAD,MAAA,CAAAE,WAAA;;;;;IAU7C7B,EAAA,CAAAC,cAAA,YAA+C;IACnCD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC9BX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAExBX,EAAA,CAAAC,cAAA,YAA+C;IACnCD,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAChCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,2BAAoB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAEnCX,EAAA,CAAAC,cAAA,aAA+C;IACnCD,EAAA,CAAAiB,MAAA,cAAM;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC3BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,uBAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAE9BX,EAAA,CAAAC,cAAA,aAA+C;IACnCD,EAAA,CAAAiB,MAAA,gBAAQ;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC7BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,yBAAiB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAEhCX,EAAA,CAAAC,cAAA,aAA+C;IACnCD,EAAA,CAAAiB,MAAA,kBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC/BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,wBAAgB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;ADFvC,MAsBamB,yBAAyB;EA4BpCC,YACUC,uBAAyC,EACzCC,MAAiB,EACjBC,MAAc,EACdC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B;IAN3B,KAAAN,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IAjCP,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAX,SAAS,GAAU,IAAI;IACvB,KAAAd,OAAO,GAAW,EAAE;IACnB,KAAA0B,UAAU,GAAG,IAAI/D,YAAY,EAAE;IAClC,KAAAgE,UAAU,GAAG,EAAE;IACf,KAAAC,QAAQ,GAAG,IAAItD,WAAW,CAAC,EAAE,CAAC;IAC9B,KAAAuD,cAAc,GAAgB,IAAIvD,WAAW,EAAE;IAC/C,KAAAwD,UAAU,GAAU,EAAE;IACtB,KAAAC,gBAAgB,GAAgB,IAAIzD,WAAW,EAAE;IACjD,KAAA0D,YAAY,GAAyB,IAAIpD,aAAa,CAAQ,CAAC,CAAC;IAC7D,KAAAqD,UAAU,GAAG,IAAIpD,OAAO,EAAQ;IAC1C,KAAAqD,QAAQ,GAAW,EAAE;IAIZ,KAAAC,UAAU,GAAY,KAAK;IAGpC;IACS,KAAAC,OAAO,GAAW,sCAAsC;IAEjE,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,KAAK,GAAQ,EAAE;IAEf,KAAAC,gBAAgB,GAAU,EAAE;IAW1B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACoB,cAAc,EAAE;IACtC,IAAI,CAACP,QAAQ,IAAI,IAAI,CAACM,IAAI,CAACE,IAAI;IAE/B,IAAI,IAAI,CAACF,IAAI,CAACG,gBAAgB,IAAI,IAAI,CAACH,IAAI,CAACG,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACvE,IAAI,CAACf,cAAc,CAACgB,QAAQ,CAAC,IAAI,CAACL,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACrB,UAAU,CAACwB,aAAa,CAAC,IAAI,CAACN,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC,CAAC;;IAG9D,IAAI,CAACrB,UAAU,CAACyB,gBAAgB,CAC7BC,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiD,UAAU,CAAC,CAAC,CAChCgB,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,CAACC,aAAa,GAAGD,IAAI;MACzB,IAAI,CAAC3B,EAAE,CAAC6B,YAAY,EAAE;IACxB,CAAC,CAAC;IAEJ,IAAI,CAAC9B,UAAU,CAAC+B,qBAAqB,CAClCL,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACiD,UAAU,CAAC,CAAC,CAChCgB,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,CAACI,gBAAgB,GAAGJ,IAAI;MAC5B,IAAI,CAAC3B,EAAE,CAAC6B,YAAY,EAAE;IACxB,CAAC,CAAC;EACN;EACAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACG,gBAAgB,EAAE;MAC3C,IAAI,CAACb,UAAU,GAAG,IAAI,CAACU,IAAI,CAACG,gBAAgB,CAACa,MAAM,CAChDC,MAAW,IAAKA,MAAM,IAAIA,MAAM,CAACC,UAAU,CAC7C;MACD,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CAAC,IAAI,CAAC7B,UAAU,CAAC8B,KAAK,EAAE,CAAC;MAC/C,IAAI,CAAC1C,uBAAuB,CAAC2C,sBAAsB,CACjD,IAAI,CAACrB,IAAI,CAACG,gBAAgB,CAC3B;;IAGH;IACA,IAAI,CAACZ,gBAAgB,GAAG,IAAIzD,WAAW,CACrC,EAAE,EACFW,UAAU,CAAC6E,OAAO,CAAC,iBAAiB,CAAC,CACtC;IAED;IACA,IAAI,CAAC/B,gBAAgB,CAACgC,YAAY,CAC/Bf,IAAI,CACHlE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACAkE,SAAS,CAAEe,QAAQ,IAAI;MACtB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACE,cAAc,EAAE;EACvB;EAEA;EACAC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,WAAW,CAAC,IAAIA,OAAO,CAAC,SAAS,CAAC,EAAE;MAC9C,IAAI,CAAC7C,EAAE,CAAC6B,YAAY,EAAE;;EAE1B;EAEA;EACAc,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC3C,EAAE,CAAC6B,YAAY,EAAE;EACxB;EAIAiB,WAAWA,CAACC,KAAY;IACtB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK;IAC5D,IAAI,CAAClC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACG,gBAAgB,CAACa,MAAM,CACtDC,MAAM,IACLA,MAAM,IACNA,MAAM,CAACC,UAAU,CAACgB,WAAW,EAAE,CAACC,QAAQ,CAACJ,WAAW,CAACG,WAAW,EAAE,CAAC,CACtE;EACH;EAEAE,OAAOA,CAAA;IACL,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEUZ,iBAAiBA,CAACD,QAAkB;IAC5C,IAAI,CAAC,IAAI,CAAClC,UAAU,EAAE;MACpB;;IAEF,IAAIgD,MAAM,GAAG,IAAI,CAAC/C,gBAAgB,CAAC0C,KAAK;IACxC,IAAI,CAACK,MAAM,EAAE;MACX,IAAI,CAAC9C,YAAY,CAAC2B,IAAI,CAAC,IAAI,CAAC7B,UAAU,CAAC8B,KAAK,EAAE,CAAC;MAC/C;KACD,MAAM;MACLkB,MAAM,GAAGA,MAAM,CAACJ,WAAW,EAAE;;IAE/B,IAAI,CAACI,MAAM,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC3C,YAAY,CAAC2B,IAAI,CACpB,IAAI,CAAC7B,UAAU,CAAC0B,MAAM,CAAEC,MAAM,IAC5BA,MAAM,CAACC,UAAU,CAACgB,WAAW,EAAE,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACJ,QAAQ,CAACG,MAAM,CAAC,CACpE,CACF;KACF,MAAM;MACL,MAAME,WAAW,GAAGF,MAAM,CACvBG,KAAK,CAAC,GAAG,CAAC,CACVzB,MAAM,CAAE0B,IAAI,IAAKA,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACvC,IAAI,CAACnD,YAAY,CAAC2B,IAAI,CACpB,IAAI,CAAC7B,UAAU,CAAC0B,MAAM,CAAEC,MAAM,IAAI;QAChC,MAAM2B,mBAAmB,GAAG3B,MAAM,CAACC,UAAU,CAACgB,WAAW,EAAE;QAC3D,OAAOM,WAAW,CAACK,KAAK,CAAEH,IAAI,IAC5BE,mBAAmB,CAACT,QAAQ,CAACO,IAAI,CAAC,CACnC;MACH,CAAC,CAAC,CACH;;EAEL;EAEAI,MAAMA,CAAA;IACJ,MAAMC,SAAS,GAAG,IAAI,CAACpE,MAAM,CAACqE,IAAI,CAACvH,eAAe,EAAE;MAClDwH,SAAS,EAAE,KAAK;MAChBvC,IAAI,EAAE;QACJd,OAAO,EAAE,kCAAkC;QAC3C1B,KAAK,EAAE;;KAEV,CAAC;IAEF6E,SAAS,CAACG,WAAW,EAAE,CAACzC,SAAS,CAAE0C,MAAM,IAAI;MAC3C,IAAIA,MAAM,EAAE;QACVC,YAAY,CAACC,KAAK,EAAE;QACpBC,cAAc,CAACD,KAAK,EAAE;QACtBE,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QACxB,IAAI,CAAC7E,MAAM,CAACyD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;IAErC,CAAC,CAAC;EACJ;EAEAqB,gBAAgBA,CAAC5B,KAAK;IACpB,IAAI,CAAChD,UAAU,CAACwB,aAAa,CAACwB,KAAK,CAAC;EACtC;EAEA;;;EAGA1E,eAAeA,CAAC0E,KAAU;IACxB;IACAA,KAAK,CAACE,MAAM,CAAC2B,KAAK,CAACC,OAAO,GAAG,MAAM;IACnC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAACpE,UAAU,CAAC0B,IAAI,EAAE;IACtB,IAAI,CAAC1B,UAAU,CAACqE,QAAQ,EAAE;EAC5B;EAEA;;;;EAIAvF,WAAWA,CAACwF,MAAc,EAAEC,IAAS;IACnC,OAAOA,IAAI,EAAEjG,IAAI,IAAIgG,MAAM,CAACE,QAAQ,EAAE;EACxC;;;uBAtMWzF,yBAAyB,EAAA9B,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA1H,EAAA,CAAAwH,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA5H,EAAA,CAAAwH,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9H,EAAA,CAAAwH,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA1H,EAAA,CAAAwH,iBAAA,CAAAxH,EAAA,CAAAiI,iBAAA,GAAAjI,EAAA,CAAAwH,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzBrG,yBAAyB;MAAAsG,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAezB9I,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;UCzEvBO,EAAA,CAAAC,cAAA,kBAAa;UAGPD,EAAA,CAAAyB,UAAA,IAAAgH,wCAAA,iBAA+G;UACjHzI,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAC,cAAA,aAAsB;UAEpBD,EAAA,CAAAyB,UAAA,IAAAiH,iDAAA,0BAOe;UAGf1I,EAAA,CAAAyB,UAAA,IAAAkH,gDAAA,iCAAA3I,EAAA,CAAA4I,sBAAA,CAqBc;UAChB5I,EAAA,CAAAW,YAAA,EAAM;UAGRX,EAAA,CAAA6I,SAAA,cAAoC;UAEpC7I,EAAA,CAAAC,cAAA,WAAuB;UAAAD,EAAA,CAAAiB,MAAA,IAAoB;UAAAjB,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAiB,MAAA,YAAI;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAC7EX,EAAA,CAAAC,cAAA,cAAuB;UAETD,EAAA,CAAAiB,MAAA,sBAAc;UAAAjB,EAAA,CAAAW,YAAA,EAAW;UACnCX,EAAA,CAAAC,cAAA,eAA0B;UACAD,EAAA,CAAAiB,MAAA,IAAgB;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAC/CX,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAiB,MAAA,IAAc;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAKnDX,EAAA,CAAAC,cAAA,wBAAmD;UAC3BD,EAAA,CAAAE,UAAA,mBAAA4I,4DAAA;YAAA,OAASN,GAAA,CAAA9C,OAAA,EAAS;UAAA,EAAC;UACvC1F,EAAA,CAAA6I,SAAA,aAAgC;UAAC7I,EAAA,CAAAiB,MAAA,wBACnC;UAAAjB,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAE,UAAA,mBAAA6I,4DAAA;YAAA,OAASP,GAAA,CAAApC,MAAA,EAAQ;UAAA,EAAC;UACtCpG,EAAA,CAAA6I,SAAA,aAA8C;UAAC7I,EAAA,CAAAiB,MAAA,uBACjD;UAAAjB,EAAA,CAAAW,YAAA,EAAS;;;;;UA5DDX,EAAA,CAAAmB,SAAA,GAAa;UAAbnB,EAAA,CAAAY,UAAA,SAAA4H,GAAA,CAAA1H,OAAA,CAAa;UAMJd,EAAA,CAAAmB,SAAA,GAA6B;UAA7BnB,EAAA,CAAAY,UAAA,UAAA4H,GAAA,CAAA5G,SAAA,kBAAA4G,GAAA,CAAA5G,SAAA,CAAA8B,MAAA,MAA6B,aAAAsF,GAAA;UAqCzBhJ,EAAA,CAAAmB,SAAA,GAAoB;UAApBnB,EAAA,CAAAiJ,kBAAA,KAAAT,GAAA,CAAAvE,aAAA,MAAoB;UAEtBjE,EAAA,CAAAmB,SAAA,GAAgC;UAAhCnB,EAAA,CAAAY,UAAA,sBAAAsI,GAAA,CAAgC;UAGvBlJ,EAAA,CAAAmB,SAAA,GAAgB;UAAhBnB,EAAA,CAAAsB,iBAAA,CAAAkH,GAAA,CAAAlF,IAAA,kBAAAkF,GAAA,CAAAlF,IAAA,CAAA6F,IAAA,CAAgB;UAChBnJ,EAAA,CAAAmB,SAAA,GAAc;UAAdnB,EAAA,CAAAsB,iBAAA,CAAAkH,GAAA,CAAAxF,QAAA,CAAc;UAMAhD,EAAA,CAAAmB,SAAA,GAA8B;UAA9BnB,EAAA,CAAAY,UAAA,cAAA4H,GAAA,CAAApE,gBAAA,CAA8B;;;qBDlBxE/E,WAAW,EACXC,mBAAmB,EACnBR,eAAe,EACfJ,YAAY,EAAA0K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ3K,aAAa,EAAA4K,EAAA,CAAAC,OAAA,EACb5K,eAAe,EAAA6K,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfxK,kBAAkB,EAClBN,gBAAgB,EAAA+K,EAAA,CAAAC,UAAA,EAChB7K,aAAa,EAAA8K,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACb/K,eAAe,EACfK,gBAAgB,EAChBC,aAAa,EACbP,YAAY,EAAA4I,EAAA,CAAAqC,UAAA,EAAArC,EAAA,CAAAsC,gBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMHvI,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}