{"ast": null, "code": "// Paragraph\n\n'use strict';\n\nmodule.exports = function paragraph(state, startLine /*, endLine*/) {\n  var content,\n    terminate,\n    i,\n    l,\n    token,\n    oldParentType,\n    nextLine = startLine + 1,\n    terminatorRules = state.md.block.ruler.getRules('paragraph'),\n    endLine = state.lineMax;\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph';\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) {\n      continue;\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) {\n      continue;\n    }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      break;\n    }\n  }\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  state.line = nextLine;\n  token = state.push('paragraph_open', 'p', 1);\n  token.map = [startLine, state.line];\n  token = state.push('inline', '', 0);\n  token.content = content;\n  token.map = [startLine, state.line];\n  token.children = [];\n  token = state.push('paragraph_close', 'p', -1);\n  state.parentType = oldParentType;\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}