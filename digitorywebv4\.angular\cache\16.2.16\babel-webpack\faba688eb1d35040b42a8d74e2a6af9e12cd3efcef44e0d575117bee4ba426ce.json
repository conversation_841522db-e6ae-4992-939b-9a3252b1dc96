{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { DashboardMenuLinksPanelComponent } from '../dashboard-menu-links-panel/dashboard-menu-links-panel.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/divider\";\nfunction DashboardMenuComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"h2\", 1);\n    i0.ɵɵtext(3, \"Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"app-dashboard-menu-links-panel\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"dashboardPanel\", ctx_r0.dashboardPanel);\n  }\n}\nclass DashboardMenuComponent {\n  constructor(auth) {\n    this.auth = auth;\n    this.isReady = false;\n    this.dashboardPanel = [];\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n  }\n  ngOnInit() {\n    let modules = {\n      \"Master Data\": [\"masterData\"],\n      \"Recipe Builder\": [\"recipe\"]\n    };\n    if (this.userRole) {\n      for (const [key, value] of Object.entries(modules)) {\n        let temp = {\n          icon: 'account_circle',\n          title: key,\n          links: value ? this.generateLinks(value) : []\n        };\n        this.dashboardPanel.push(temp);\n      }\n    }\n    this.isReady = true;\n  }\n  generateLinks(module) {\n    const links = module.map(label => {\n      return {\n        label,\n        routerLink: `/dashboard/${label}`\n      };\n    });\n    return links;\n  }\n  static {\n    this.ɵfac = function DashboardMenuComponent_Factory(t) {\n      return new (t || DashboardMenuComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardMenuComponent,\n      selectors: [[\"app-dashboard-menu\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 1,\n      consts: [[1, \"dashboard-menu\", \"dashboard-menu-background\"], [1, \"title\"], [\"appearance\", \"outlined\", 1, \"dashboard-menu-header-background\"], [\"mat-card-image\", \"\", \"src\", \"assets/images/100042.png\", \"alt\", \"the logo\", 2, \"padding\", \"1rem 4rem 1rem 4rem\"], [\"class\", \"main-section\", 4, \"ngIf\"], [1, \"main-section\"], [1, \"list-container\"], [3, \"dashboardPanel\"]],\n      template: function DashboardMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-card\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"mat-divider\");\n          i0.ɵɵtemplate(5, DashboardMenuComponent_div_5_Template, 6, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isReady);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, MatCardModule, i3.MatCard, i3.MatCardImage, MatDividerModule, i4.MatDivider, DashboardMenuLinksPanelComponent],\n      styles: [\".dashboard-menu[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 1px;\\n  height: 100%;\\n}\\n\\n.list-container[_ngcontent-%COMP%] {\\n  padding-top: 1rem;\\n}\\n.list-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-left: 1.5rem;\\n  padding: 0;\\n  font-weight: bold;\\n  text-transform: uppercase;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9kYXNoYm9hcmQtbWVudS9kYXNoYm9hcmQtbWVudS5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvX3ZhcmlhYmxlcy5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQ0Usa0JDSHFCO0VESXJCLFlBQUE7RUFDQSxZQUFBO0FBREY7O0FBSUE7RUFDRSxpQkFBQTtBQURGO0FBR0U7RUFDRSxTQUFBO0VBQ0EsbUJBQUE7RUFDQSxVQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtBQURKIiwic291cmNlc0NvbnRlbnQiOlsiQHVzZSAnLi4vLi4vLi4vc3R5bGVzL3ZhcmlhYmxlcycgYXMgdjtcblxuLmRhc2hib2FyZC1tZW51IHtcbiAgYm9yZGVyLXJhZGl1czogdi4kZ2xvYmFsLWJvcmRlci1yYWRpdXM7XG4gIHBhZGRpbmc6IDFweDtcbiAgaGVpZ2h0OiAxMDAlO1xufVxuXG4ubGlzdC1jb250YWluZXIge1xuICBwYWRkaW5nLXRvcDogMXJlbTtcblxuICAudGl0bGUge1xuICAgIG1hcmdpbjogMDtcbiAgICBtYXJnaW4tbGVmdDogMS41cmVtO1xuICAgIHBhZGRpbmc6IDA7XG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgfVxuXG59IiwiJGdsb2JhbC1ib3JkZXItcmFkaXVzOiA4cHg7XG4kZ2xvYmFsLXBhZ2UtcGFkZGluZy1sZzogMC41cmVtO1xuJGdsb2JhbC1wYWdlLXBhZGRpbmctc206IDAuNXJlbTtcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardMenuComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatDividerModule", "DashboardMenuLinksPanelComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "dashboardPanel", "DashboardMenuComponent", "constructor", "auth", "isReady", "user", "getCurrentUser", "userRole", "getCurrRole", "ngOnInit", "modules", "key", "value", "Object", "entries", "temp", "icon", "title", "links", "generateLinks", "push", "module", "map", "label", "routerLink", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardMenuComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardMenuComponent_div_5_Template", "i2", "NgIf", "i3", "MatCard", "MatCardImage", "i4", "<PERSON><PERSON><PERSON><PERSON>", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-menu/dashboard-menu.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-menu/dashboard-menu.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { DashboardMenuLinksPanelComponent } from '../dashboard-menu-links-panel/dashboard-menu-links-panel.component';\n\n@Component({\n  selector: 'app-dashboard-menu',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatDividerModule,\n    DashboardMenuLinksPanelComponent,\n  ],\n  templateUrl: './dashboard-menu.component.html',\n  styleUrls: ['./dashboard-menu.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardMenuComponent implements OnInit {\n  public isReady = false;\n  public dashboardPanel: {\n    icon: string;\n    title: string;\n    links: { label: string; routerLink: string }[];\n  }[] = [];\n  public user: any;\n  public userRole: any;\n  constructor(private auth: AuthService) {\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n  }\n\n  ngOnInit(): void {\n    let modules ={\n      \"Master Data\": [\n          \"masterData\"\n      ],\n      \"Recipe Builder\": [\n        \"recipe\"\n    ]\n  }\n    if (this.userRole) {\n      for (const [key, value] of Object.entries(modules)) {\n        let temp = {\n          icon: 'account_circle',\n          title: key,\n          links: value ? this.generateLinks(value) : [],\n        }\n        this.dashboardPanel.push(temp)\n      }\n    }\n    this.isReady = true;\n  }\n\n  generateLinks(module) {\n    const links = module.map(label => {\n      return { label, routerLink: `/dashboard/${label}` };\n    });\n    return links;\n  }\n}\n", "<div class=\"dashboard-menu dashboard-menu-background\">\n  <div class=\"title\">\n    <mat-card appearance=\"outlined\" class=\"dashboard-menu-header-background\">\n      <img mat-card-image src=\"assets/images/100042.png\" alt=\"the logo\" style=\"padding: 1rem 4rem 1rem 4rem;\" />\n    </mat-card>\n  </div>\n  <mat-divider> </mat-divider>\n  <div class=\"main-section\" *ngIf=\"isReady\">\n    <div class=\"list-container\">\n      <h2 class=\"title\">Dashboard</h2>\n      <app-dashboard-menu-links-panel [dashboardPanel]=\"dashboardPanel\"></app-dashboard-menu-links-panel>\n    </div>\n    <mat-divider> </mat-divider>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,gCAAgC,QAAQ,oEAAoE;;;;;;;;ICEnHC,EAAA,CAAAC,cAAA,aAA0C;IAEpBD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAI,SAAA,wCAAmG;IACrGJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,kBAA4B;IAC9BJ,EAAA,CAAAG,YAAA,EAAM;;;;IAH8BH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,mBAAAC,MAAA,CAAAC,cAAA,CAAiC;;;ADHvE,MAaaC,sBAAsB;EASjCC,YAAoBC,IAAiB;IAAjB,KAAAA,IAAI,GAAJA,IAAI;IARjB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAJ,cAAc,GAIf,EAAE;IAIN,IAAI,CAACK,IAAI,GAAG,IAAI,CAACF,IAAI,CAACG,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,IAAI,CAACK,WAAW,EAAE;EACzC;EAEAC,QAAQA,CAAA;IACN,IAAIC,OAAO,GAAE;MACX,aAAa,EAAE,CACX,YAAY,CACf;MACD,gBAAgB,EAAE,CAChB,QAAQ;KAEb;IACC,IAAI,IAAI,CAACH,QAAQ,EAAE;MACjB,KAAK,MAAM,CAACI,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;QAClD,IAAIK,IAAI,GAAG;UACTC,IAAI,EAAE,gBAAgB;UACtBC,KAAK,EAAEN,GAAG;UACVO,KAAK,EAAEN,KAAK,GAAG,IAAI,CAACO,aAAa,CAACP,KAAK,CAAC,GAAG;SAC5C;QACD,IAAI,CAACZ,cAAc,CAACoB,IAAI,CAACL,IAAI,CAAC;;;IAGlC,IAAI,CAACX,OAAO,GAAG,IAAI;EACrB;EAEAe,aAAaA,CAACE,MAAM;IAClB,MAAMH,KAAK,GAAGG,MAAM,CAACC,GAAG,CAACC,KAAK,IAAG;MAC/B,OAAO;QAAEA,KAAK;QAAEC,UAAU,EAAE,cAAcD,KAAK;MAAE,CAAE;IACrD,CAAC,CAAC;IACF,OAAOL,KAAK;EACd;;;uBAzCWjB,sBAAsB,EAAAT,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtB1B,sBAAsB;MAAA2B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtC,EAAA,CAAAuC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBnC7C,EAAA,CAAAC,cAAA,aAAsD;UAGhDD,EAAA,CAAAI,SAAA,aAA0G;UAC5GJ,EAAA,CAAAG,YAAA,EAAW;UAEbH,EAAA,CAAAI,SAAA,kBAA4B;UAC5BJ,EAAA,CAAA+C,UAAA,IAAAC,qCAAA,iBAMM;UACRhD,EAAA,CAAAG,YAAA,EAAM;;;UAPuBH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAwC,GAAA,CAAAlC,OAAA,CAAa;;;qBDItChB,YAAY,EAAAqD,EAAA,CAAAC,IAAA,EACZrD,aAAa,EAAAsD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,YAAA,EACbvD,gBAAgB,EAAAwD,EAAA,CAAAC,UAAA,EAChBxD,gCAAgC;MAAAyD,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMvBhD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}