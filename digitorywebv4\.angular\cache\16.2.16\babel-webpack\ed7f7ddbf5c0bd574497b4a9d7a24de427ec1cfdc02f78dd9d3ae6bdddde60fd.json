{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport { SmartDashboardUtility } from '../utilities/smart-dashboard.utility';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nclass SmartDashboardService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = environment.apiUrl;\n    this.apiEndpoint = '/api/smart-dashboard';\n    this.dashboardDataSubject = new BehaviorSubject(null);\n    this.dashboardData$ = this.dashboardDataSubject.asObservable();\n    this.loadingSubject = new BehaviorSubject(false);\n    this.loading$ = this.loadingSubject.asObservable();\n  }\n  getHeaders() {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  generateDashboard(request) {\n    this.loadingSubject.next(true);\n    const url = `${this.apiUrl}${this.apiEndpoint}/smart_ask`;\n    return new Observable(observer => {\n      this.http.post(url, request, {\n        headers: this.getHeaders()\n      }).subscribe({\n        next: response => {\n          this.loadingSubject.next(false);\n          this.dashboardDataSubject.next(response.data);\n          observer.next(response);\n          observer.complete();\n        },\n        error: error => {\n          this.loadingSubject.next(false);\n          this.dashboardDataSubject.next(null);\n          observer.error(error);\n        }\n      });\n    });\n  }\n  getDashboardConfig() {\n    const url = `${this.apiUrl}${this.apiEndpoint}/config`;\n    return this.http.get(url, {\n      headers: this.getHeaders()\n    });\n  }\n  clearDashboardData() {\n    this.dashboardDataSubject.next(null);\n  }\n  getCurrentDashboardData() {\n    return this.dashboardDataSubject.value;\n  }\n  isLoading() {\n    return this.loadingSubject.value;\n  }\n  getChartConfig(chartType) {\n    return SmartDashboardUtility.getChartConfiguration(chartType);\n  }\n  getChartColors() {\n    return SmartDashboardUtility.getDefaultChartColors();\n  }\n  getDefaultDashboardTabs() {\n    return SmartDashboardUtility.getDefaultDashboardTabs();\n  }\n  getDefaultBaseDateOptions() {\n    return SmartDashboardUtility.getDefaultBaseDateOptions();\n  }\n  getDefaultDashboardConfig() {\n    return SmartDashboardUtility.getDefaultDashboardConfig();\n  }\n  validateFilters(filters) {\n    return SmartDashboardUtility.validateFilters(filters);\n  }\n  getEmptyStateMessage(dashboardType) {\n    return SmartDashboardUtility.getEmptyStateMessage(dashboardType);\n  }\n  getLoadingMessage(dashboardType) {\n    return SmartDashboardUtility.getLoadingMessage(dashboardType);\n  }\n  formatDate(date, config) {\n    return SmartDashboardUtility.formatDate(date, config);\n  }\n  formatNumber(value, config) {\n    return SmartDashboardUtility.formatNumber(value, config);\n  }\n  static {\n    this.ɵfac = function SmartDashboardService_Factory(t) {\n      return new (t || SmartDashboardService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SmartDashboardService,\n      factory: SmartDashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SmartDashboardService };", "map": {"version": 3, "names": ["HttpHeaders", "Observable", "BehaviorSubject", "environment", "SmartDashboardUtility", "SmartDashboardService", "constructor", "http", "authService", "apiUrl", "apiEndpoint", "dashboardDataSubject", "dashboardData$", "asObservable", "loadingSubject", "loading$", "getHeaders", "token", "getToken", "generateDashboard", "request", "next", "url", "observer", "post", "headers", "subscribe", "response", "data", "complete", "error", "getDashboardConfig", "get", "clearDashboardData", "getCurrentDashboardData", "value", "isLoading", "getChartConfig", "chartType", "getChartConfiguration", "getChartColors", "getDefaultChartColors", "getDefaultDashboardTabs", "getDefaultBaseDateOptions", "getDefaultDashboardConfig", "validateFilters", "filters", "getEmptyStateMessage", "dashboardType", "getLoadingMessage", "formatDate", "date", "config", "formatNumber", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/smart-dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport { AuthService } from './auth.service';\nimport { SmartDashboardUtility } from '../utilities/smart-dashboard.utility';\n\nexport interface DashboardFilters {\n  locations: string[];\n  baseDate: string;\n  startDate: string;\n  endDate: string;\n}\n\nexport interface DashboardRequest {\n  filters: DashboardFilters;\n  user_query: string;\n  dashboard_type: string;\n  tenant_id: string;\n}\n\nexport interface ChartData {\n  id: string;\n  title: string;\n  type: string;\n  data: any;\n  options?: any;\n}\n\nexport interface SummaryItem {\n  icon: string;\n  value: string;\n  label: string;\n}\n\nexport interface DashboardMetadata {\n  total_charts: number;\n  total_summary_items: number;\n  generated_at: string;\n  currency: string;\n  currency_symbol: string;\n}\n\nexport interface DashboardData {\n  charts: ChartData[];\n  summary_items: SummaryItem[];\n  metadata: DashboardMetadata;\n}\n\nexport interface DashboardResponse {\n  status: string;\n  data: DashboardData;\n}\n\nexport interface DashboardTab {\n  label: string;\n  value: string;\n  description: string;\n  active: boolean;\n}\n\nexport interface BaseDate {\n  displayName: string;\n  value: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmartDashboardService {\n  private readonly apiUrl = environment.apiUrl;\n  private readonly apiEndpoint = '/api/smart-dashboard';\n\n  private dashboardDataSubject = new BehaviorSubject<any>(null);\n  public dashboardData$ = this.dashboardDataSubject.asObservable();\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  public loading$ = this.loadingSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  private getHeaders(): HttpHeaders {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n\n\n\n  generateDashboard(request: DashboardRequest): Observable<DashboardResponse> {\n    this.loadingSubject.next(true);\n    const url = `${this.apiUrl}${this.apiEndpoint}/smart_ask`;\n\n    return new Observable(observer => {\n      this.http.post<DashboardResponse>(url, request, { headers: this.getHeaders() })\n        .subscribe({\n          next: (response) => {\n            this.loadingSubject.next(false);\n            this.dashboardDataSubject.next(response.data);\n            observer.next(response);\n            observer.complete();\n          },\n          error: (error) => {\n            this.loadingSubject.next(false);\n            this.dashboardDataSubject.next(null);\n            observer.error(error);\n          }\n        });\n    });\n  }\n\n  getDashboardConfig(): Observable<any> {\n    const url = `${this.apiUrl}${this.apiEndpoint}/config`;\n    return this.http.get<any>(url, { headers: this.getHeaders() });\n  }\n\n  clearDashboardData(): void {\n    this.dashboardDataSubject.next(null);\n  }\n\n  getCurrentDashboardData(): any {\n    return this.dashboardDataSubject.value;\n  }\n\n  isLoading(): boolean {\n    return this.loadingSubject.value;\n  }\n\n  getChartConfig(chartType: string): any {\n    return SmartDashboardUtility.getChartConfiguration(chartType);\n  }\n\n  getChartColors(): string[] {\n    return SmartDashboardUtility.getDefaultChartColors();\n  }\n\n  getDefaultDashboardTabs(): DashboardTab[] {\n    return SmartDashboardUtility.getDefaultDashboardTabs();\n  }\n\n  getDefaultBaseDateOptions(): BaseDate[] {\n    return SmartDashboardUtility.getDefaultBaseDateOptions();\n  }\n\n  getDefaultDashboardConfig(): any {\n    return SmartDashboardUtility.getDefaultDashboardConfig();\n  }\n\n  validateFilters(filters: any): boolean {\n    return SmartDashboardUtility.validateFilters(filters);\n  }\n\n  getEmptyStateMessage(dashboardType: string): { title: string; description: string } {\n    return SmartDashboardUtility.getEmptyStateMessage(dashboardType);\n  }\n\n  getLoadingMessage(dashboardType: string): { title: string; description: string } {\n    return SmartDashboardUtility.getLoadingMessage(dashboardType);\n  }\n\n  formatDate(date: string | Date, config?: any): string {\n    return SmartDashboardUtility.formatDate(date, config);\n  }\n\n  formatNumber(value: number, config?: any): string {\n    return SmartDashboardUtility.formatNumber(value, config);\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,qBAAqB,QAAQ,sCAAsC;;;;AA6D5E,MAGaC,qBAAqB;EAShCC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAVJ,KAAAC,MAAM,GAAGN,WAAW,CAACM,MAAM;IAC3B,KAAAC,WAAW,GAAG,sBAAsB;IAE7C,KAAAC,oBAAoB,GAAG,IAAIT,eAAe,CAAM,IAAI,CAAC;IACtD,KAAAU,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IACxD,KAAAC,cAAc,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IACrD,KAAAa,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACD,YAAY,EAAE;EAKjD;EAEKG,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAG,IAAI,CAACT,WAAW,CAACU,QAAQ,EAAE;IACzC,OAAO,IAAIlB,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUiB,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAIAE,iBAAiBA,CAACC,OAAyB;IACzC,IAAI,CAACN,cAAc,CAACO,IAAI,CAAC,IAAI,CAAC;IAC9B,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACb,MAAM,GAAG,IAAI,CAACC,WAAW,YAAY;IAEzD,OAAO,IAAIT,UAAU,CAACsB,QAAQ,IAAG;MAC/B,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAoBF,GAAG,EAAEF,OAAO,EAAE;QAAEK,OAAO,EAAE,IAAI,CAACT,UAAU;MAAE,CAAE,CAAC,CAC5EU,SAAS,CAAC;QACTL,IAAI,EAAGM,QAAQ,IAAI;UACjB,IAAI,CAACb,cAAc,CAACO,IAAI,CAAC,KAAK,CAAC;UAC/B,IAAI,CAACV,oBAAoB,CAACU,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;UAC7CL,QAAQ,CAACF,IAAI,CAACM,QAAQ,CAAC;UACvBJ,QAAQ,CAACM,QAAQ,EAAE;QACrB,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChB,cAAc,CAACO,IAAI,CAAC,KAAK,CAAC;UAC/B,IAAI,CAACV,oBAAoB,CAACU,IAAI,CAAC,IAAI,CAAC;UACpCE,QAAQ,CAACO,KAAK,CAACA,KAAK,CAAC;QACvB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAAA;IAChB,MAAMT,GAAG,GAAG,GAAG,IAAI,CAACb,MAAM,GAAG,IAAI,CAACC,WAAW,SAAS;IACtD,OAAO,IAAI,CAACH,IAAI,CAACyB,GAAG,CAAMV,GAAG,EAAE;MAAEG,OAAO,EAAE,IAAI,CAACT,UAAU;IAAE,CAAE,CAAC;EAChE;EAEAiB,kBAAkBA,CAAA;IAChB,IAAI,CAACtB,oBAAoB,CAACU,IAAI,CAAC,IAAI,CAAC;EACtC;EAEAa,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACvB,oBAAoB,CAACwB,KAAK;EACxC;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACtB,cAAc,CAACqB,KAAK;EAClC;EAEAE,cAAcA,CAACC,SAAiB;IAC9B,OAAOlC,qBAAqB,CAACmC,qBAAqB,CAACD,SAAS,CAAC;EAC/D;EAEAE,cAAcA,CAAA;IACZ,OAAOpC,qBAAqB,CAACqC,qBAAqB,EAAE;EACtD;EAEAC,uBAAuBA,CAAA;IACrB,OAAOtC,qBAAqB,CAACsC,uBAAuB,EAAE;EACxD;EAEAC,yBAAyBA,CAAA;IACvB,OAAOvC,qBAAqB,CAACuC,yBAAyB,EAAE;EAC1D;EAEAC,yBAAyBA,CAAA;IACvB,OAAOxC,qBAAqB,CAACwC,yBAAyB,EAAE;EAC1D;EAEAC,eAAeA,CAACC,OAAY;IAC1B,OAAO1C,qBAAqB,CAACyC,eAAe,CAACC,OAAO,CAAC;EACvD;EAEAC,oBAAoBA,CAACC,aAAqB;IACxC,OAAO5C,qBAAqB,CAAC2C,oBAAoB,CAACC,aAAa,CAAC;EAClE;EAEAC,iBAAiBA,CAACD,aAAqB;IACrC,OAAO5C,qBAAqB,CAAC6C,iBAAiB,CAACD,aAAa,CAAC;EAC/D;EAEAE,UAAUA,CAACC,IAAmB,EAAEC,MAAY;IAC1C,OAAOhD,qBAAqB,CAAC8C,UAAU,CAACC,IAAI,EAAEC,MAAM,CAAC;EACvD;EAEAC,YAAYA,CAAClB,KAAa,EAAEiB,MAAY;IACtC,OAAOhD,qBAAqB,CAACiD,YAAY,CAAClB,KAAK,EAAEiB,MAAM,CAAC;EAC1D;;;uBArGW/C,qBAAqB,EAAAiD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAArBtD,qBAAqB;MAAAuD,OAAA,EAArBvD,qBAAqB,CAAAwD,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA;;SAEPzD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}