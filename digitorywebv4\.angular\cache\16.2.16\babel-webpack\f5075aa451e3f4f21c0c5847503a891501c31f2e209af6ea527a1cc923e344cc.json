{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r8);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r7.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r8));\n  }\n}\nfunction SmartDashboardComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r9.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r10.displayName, \" \");\n  }\n}\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.selectedTab = 0;\n    // GRN Filter options\n    this.locations = [];\n    this.baseDates = [{\n      displayName: \"GRN Date (System Entry)\",\n      value: \"deliveryDate\"\n    }, {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    }, {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'Purchase Dashboard',\n      active: true\n    }, {\n      label: 'Sales Dashboard',\n      active: false\n    }];\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: false\n            }));\n          } else {\n            console.warn('No locations found for user');\n            this.locations = [];\n          }\n        },\n        error: err => {\n          console.error('Error loading locations:', err);\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  getReportIcon(index) {\n    const icons = ['receipt_long', 'shopping_cart', 'store', 'inventory'];\n    return icons[index] || 'dashboard';\n  }\n  getDashboardDescription(index) {\n    const descriptions = ['Purchase analytics and vendor insights', 'Sales performance and revenue tracking', 'POS transactions and customer trends', 'Inventory management and stock analysis'];\n    return descriptions[index] || 'Business analytics dashboard';\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 99,\n      vars: 21,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask me about your business data...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"charts-container\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"filter-badge\"], [1, \"selection-count\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, SmartDashboardComponent_span_18_Template, 2, 1, \"span\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16)(21, \"label\", 17)(22, \"mat-icon\", 18);\n          i0.ɵɵtext(23, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 19);\n          i0.ɵɵtext(25, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, SmartDashboardComponent_span_26_Template, 2, 1, \"span\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-form-field\", 21)(28, \"mat-select\", 22);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_28_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(29, SmartDashboardComponent_mat_option_29_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 16)(31, \"label\", 17)(32, \"mat-icon\", 18);\n          i0.ɵɵtext(33, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 19);\n          i0.ɵɵtext(35, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"mat-form-field\", 21)(37, \"mat-select\", 23);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_37_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(38, SmartDashboardComponent_mat_option_38_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 16)(40, \"label\", 17)(41, \"mat-icon\", 18);\n          i0.ɵɵtext(42, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\", 19);\n          i0.ɵɵtext(44, \"Start Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"mat-form-field\", 21)(46, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"mat-datepicker-toggle\", 25)(48, \"mat-datepicker\", null, 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 16)(51, \"label\", 17)(52, \"mat-icon\", 18);\n          i0.ɵɵtext(53, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 19);\n          i0.ɵɵtext(55, \"End Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"mat-form-field\", 21)(57, \"input\", 27);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_57_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"mat-datepicker-toggle\", 25)(59, \"mat-datepicker\", null, 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"div\", 29)(62, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_62_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_66_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(70, \"div\", 32)(71, \"div\", 33)(72, \"div\", 34)(73, \"div\", 35)(74, \"mat-icon\", 36);\n          i0.ɵɵtext(75, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\", 37);\n          i0.ɵɵtext(77, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 38);\n          i0.ɵɵtext(79);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 39)(81, \"div\", 40)(82, \"mat-form-field\", 41)(83, \"input\", 42);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_83_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_83_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_84_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(85, \"mat-icon\");\n          i0.ɵɵtext(86, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(87, \"div\", 44)(88, \"div\", 45)(89, \"div\", 46)(90, \"div\", 47)(91, \"div\", 48)(92, \"mat-icon\");\n          i0.ɵɵtext(93, \"analytics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"h4\", 49);\n          i0.ɵɵtext(95, \"Ready to Analyze Your Data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"p\", 50);\n          i0.ɵɵtext(97, \" Configure your filters above and ask the AI assistant to generate insights and visualizations from your business data. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(98, \"div\", 51);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(49);\n          const _r6 = i0.ɵɵreference(60);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(20);\n          i0.ɵɵclassProp(\"ready\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, MatCardModule, MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, MatFormFieldModule, i6.MatFormField, i6.MatSuffix, MatSelectModule, i7.MatSelect, i7.MatSelectTrigger, i8.MatOption, MatInputModule, i9.MatInput, MatDatepickerModule, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, MatNativeDateModule, FormsModule, i11.DefaultValueAccessor, i11.NgControlStatus, i11.NgModel],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 270px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem !important;\\n  margin: 0.25rem 0;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n  border-radius: 0.375rem !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 34px;\\n  border-radius: 0.375rem;\\n  color: #4b5563;\\n  border-color: #d1d5db;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 34px;\\n  border-radius: 0.375rem;\\n  background: #ff6b35;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: #e55a2b;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0.5rem 0;\\n  gap: 0.5rem;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(90deg, rgba(255, 145, 0, 0.08) 0%, rgba(255, 107, 53, 0.12) 100%);\\n  border-bottom: 2px solid rgba(255, 145, 0, 0.2);\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #4b5563;\\n  padding: 4px 10px;\\n  border-radius: 0.5rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  width: 100%;\\n  max-width: 600px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  height: 44px !important;\\n  min-height: 44px !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0 0.5rem 0 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    min-width: auto;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.85rem !important;\\n  min-height: 48px !important;\\n  line-height: 1.2 !important;\\n  padding: 8px 12px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "first", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r8", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r7", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r9", "value", "baseDate_r10", "displayName", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "user", "getCurrentUser", "loadLocations", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "tenantId", "getLocations", "pipe", "subscribe", "next", "res", "result", "branches", "map", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "console", "warn", "error", "err", "resetFilters", "location", "count", "getReportIcon", "icons", "descriptions", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "ɵɵtemplate", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_18_Template", "SmartDashboardComponent_span_26_Template", "SmartDashboardComponent_Template_mat_select_valueChange_28_listener", "SmartDashboardComponent_mat_option_29_Template", "SmartDashboardComponent_Template_mat_select_valueChange_37_listener", "SmartDashboardComponent_mat_option_38_Template", "SmartDashboardComponent_Template_input_ngModelChange_46_listener", "ɵɵelement", "SmartDashboardComponent_Template_input_ngModelChange_57_listener", "SmartDashboardComponent_Template_button_click_62_listener", "SmartDashboardComponent_Template_button_click_66_listener", "SmartDashboardComponent_Template_input_ngModelChange_83_listener", "SmartDashboardComponent_Template_input_keydown_83_listener", "SmartDashboardComponent_Template_button_click_84_listener", "_r5", "_r6", "ɵɵclassProp", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "i6", "MatFormField", "MatSuffix", "i7", "MatSelect", "MatSelectTrigger", "i8", "MatOption", "i9", "MatInput", "i10", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i11", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { first } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  selectedTab = 0;\n  user: any;\n\n  // GRN Filter options\n  locations: any[] = [];\n\n  baseDates = [\n    {\n      displayName: \"GRN Date (System Entry)\",\n      value: \"deliveryDate\"\n    },\n    {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    },\n    {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'deliveryDate';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'Purchase Dashboard', active: true },\n    { label: 'Sales Dashboard', active: false },\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n  }\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: false\n              }));\n            } else {\n              console.warn('No locations found for user');\n              this.locations = [];\n            }\n          },\n          error: (err) => {\n            console.error('Error loading locations:', err);\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n\n  getReportIcon(index: number): string {\n    const icons = ['receipt_long', 'shopping_cart', 'store', 'inventory'];\n    return icons[index] || 'dashboard';\n  }\n\n  getDashboardDescription(index: number): string {\n    const descriptions = [\n      'Purchase analytics and vendor insights',\n      'Sales performance and revenue tracking',\n      'POS transactions and customer trends',\n      'Inventory management and stock analysis'\n    ];\n    return descriptions[index] || 'Business analytics dashboard';\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Smart Dashboard Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask me about your business data...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Ready to Analyze Your Data</h4>\n            <p class=\"empty-state-description\">\n              Configure your filters above and ask the AI assistant to generate insights and visualizations from your business data.\n            </p>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICC5BC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,IAAA,EAAgC;;;;;IAenEL,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAC,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAG,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEjB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,WAAA,CAAAC,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,WAAA,CAAAT,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAgB,YAAA,CAAAD,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,YAAA,CAAAC,WAAA,MACF;;;ADrDd,MAkBaC,uBAAuB;EAmClCC,YACUC,WAAwB,EACxBC,gBAAkC;IADlC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IApC1B,KAAAC,WAAW,GAAG,CAAC;IAGf;IACA,KAAAC,SAAS,GAAU,EAAE;IAErB,KAAAC,SAAS,GAAG,CACV;MACEP,WAAW,EAAE,yBAAyB;MACtCF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,CACF;IAED;IACA,KAAAH,iBAAiB,GAAa,EAAE;IAChC,KAAAa,gBAAgB,GAAG,cAAc;IACjC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAExB,KAAK,EAAE,oBAAoB;MAAEyB,MAAM,EAAE;IAAI,CAAE,EAC7C;MAAEzB,KAAK,EAAE,iBAAiB;MAAEyB,MAAM,EAAE;IAAK,CAAE,CAC5C;EAKG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,WAAW,CAACa,cAAc,EAAE;IAC7C,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACd,WAAW,GAAGc,KAAK;IACxB,IAAI,CAACP,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACR,MAAM,GAAGS,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;MAC3B;MACA,IAAI,CAACb,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;EAAA;EAGFb,aAAaA,CAAA;IACX,IAAI,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACgB,QAAQ,EAAE;MACnC,IAAI,CAAC3B,gBAAgB,CAAC4B,YAAY,CAAC,IAAI,CAACjB,IAAI,CAACgB,QAAQ,CAAC,CACnDE,IAAI,CAACvD,KAAK,EAAE,CAAC,CACbwD,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAChC,SAAS,GAAG8B,GAAG,CAACE,QAAQ,CAACC,GAAG,CAAEC,MAAW,KAAM;cAClD1C,KAAK,EAAE0C,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEvD,KAAK,EAAEoD,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;WACJ,MAAM;YACLC,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;YAC3C,IAAI,CAAC1C,SAAS,GAAG,EAAE;;QAEvB,CAAC;QACD2C,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;UAC9C,IAAI,CAAC5C,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEA6C,YAAYA,CAAA;IACV;IACA,IAAI,CAACxD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACa,gBAAgB,GAAG,cAAc;IACtC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACJ,SAAS,CAACc,OAAO,CAACgC,QAAQ,IAAIA,QAAQ,CAACN,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEArD,qBAAqBA,CAAA;IACnB,IAAI4D,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1D,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAEyD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC7C,gBAAgB,KAAK,cAAc,EAAE6C,KAAK,EAAE;IACrD,IAAI,IAAI,CAAC5C,SAAS,EAAE4C,KAAK,EAAE;IAC3B,IAAI,IAAI,CAAC3C,OAAO,EAAE2C,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;EAEAC,aAAaA,CAACnC,KAAa;IACzB,MAAMoC,KAAK,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC;IACrE,OAAOA,KAAK,CAACpC,KAAK,CAAC,IAAI,WAAW;EACpC;EAEA7B,uBAAuBA,CAAC6B,KAAa;IACnC,MAAMqC,YAAY,GAAG,CACnB,wCAAwC,EACxC,wCAAwC,EACxC,sCAAsC,EACtC,yCAAyC,CAC1C;IACD,OAAOA,YAAY,CAACrC,KAAK,CAAC,IAAI,8BAA8B;EAC9D;;;uBA9HWlB,uBAAuB,EAAAtB,EAAA,CAAA8E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhF,EAAA,CAAA8E,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvB5D,uBAAuB;MAAA6D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArF,EAAA,CAAAsF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCpC5F,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAA8F,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAnE,WAAA,GAAAsE,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBH,GAAA,CAAAtD,WAAA,CAAAyD,MAAA,CAAA7E,KAAA,CAAyB;UAAA,EAA7C;UACjCnB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAAkG,UAAA,KAAAC,8CAAA,wBAOa;UACfnG,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAkG,UAAA,KAAAE,wCAAA,mBAEO;UACTpG,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAkG,UAAA,KAAAG,wCAAA,mBAEO;UACTrG,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA8F,UAAA,yBAAAQ,oEAAAN,MAAA;YAAA,OAAAH,GAAA,CAAA7E,iBAAA,GAAAgF,MAAA;UAAA,EAA6B;UACvChG,EAAA,CAAAkG,UAAA,KAAAK,8CAAA,wBAEa;UACfvG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA8F,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAH,GAAA,CAAAhE,gBAAA,GAAAmE,MAAA;UAAA,EAA4B;UACtChG,EAAA,CAAAkG,UAAA,KAAAO,8CAAA,wBAEa;UACfzG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA8F,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAH,GAAA,CAAA/D,SAAA,GAAAkE,MAAA;UAAA,EAAuB;UAHzBhG,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAA2G,SAAA,iCAA6E;UAE/E3G,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA8F,UAAA,2BAAAc,iEAAAZ,MAAA;YAAA,OAAAH,GAAA,CAAA9D,OAAA,GAAAiE,MAAA;UAAA,EAAqB;UAHvBhG,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAA2G,SAAA,iCAA2E;UAE7E3G,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA8F,UAAA,mBAAAe,0DAAA;YAAA,OAAShB,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC;UACnExE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAA8F,UAAA,mBAAAgB,0DAAA;YAAA,OAASjB,GAAA,CAAA1C,YAAA,EAAc;UAAA,EAAC;UAClFnD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA2E;UACzED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,eAA6B;UAOrBD,EAAA,CAAA8F,UAAA,2BAAAiB,iEAAAf,MAAA;YAAA,OAAAH,GAAA,CAAA7D,WAAA,GAAAgE,MAAA;UAAA,EAAyB,qBAAAgB,2DAAAhB,MAAA;YAAA,OACdH,GAAA,CAAA/C,UAAA,CAAAkD,MAAA,CAAkB;UAAA,EADJ;UAJ3BhG,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAMC;UAFCD,EAAA,CAAA8F,UAAA,mBAAAmB,0DAAA;YAAA,OAASpB,GAAA,CAAAjD,WAAA,EAAa;UAAA,EAAC;UAGvB5C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAOXD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,kCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,aAAmC;UACjCD,EAAA,CAAAE,MAAA,gIACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAA2G,SAAA,eAEM;UACR3G,EAAA,CAAAG,YAAA,EAAM;;;;;UAnLQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAyF,GAAA,CAAAnE,WAAA,CAAuB;UAIC1B,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAAsF,GAAA,CAAA5D,IAAA,CAAA4D,GAAA,CAAAnE,WAAA,mBAAAmE,GAAA,CAAA5D,IAAA,CAAA4D,GAAA,CAAAnE,WAAA,EAAAjB,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAA5D,IAAA,CAAS;UAmBTjC,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAAyF,GAAA,CAAA/E,qBAAA,OAAiC;UAY5Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAyF,GAAA,CAAA7E,iBAAA,CAAAC,MAAA,KAAkC;UAKrDjB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAyF,GAAA,CAAA7E,iBAAA,CAA6B;UACNhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAAlE,SAAA,CAAY;UAcnC3B,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAyF,GAAA,CAAAhE,gBAAA,CAA4B;UACL7B,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAAjE,SAAA,CAAY;UAgB7C5B,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAA6B,YAAArB,GAAA,CAAA/D,SAAA;UAKE9B,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA8G,GAAA,CAAmB;UAclDlH,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA+G,GAAA,CAA2B,YAAAtB,GAAA,CAAA9D,OAAA;UAKI/B,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA+G,GAAA,CAAiB;UA4BrBnH,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAoH,WAAA,UAAAvB,GAAA,CAAA/E,qBAAA,OAA2C;UACxEd,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAAiF,GAAA,CAAA/E,qBAAA,6DACF;UASMd,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAA7D,WAAA,CAAyB,aAAA6D,GAAA,CAAA/E,qBAAA;UAU3Bd,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAAyF,GAAA,CAAA7D,WAAA,CAAAa,IAAA,MAAAgD,GAAA,CAAA/E,qBAAA,SAAiE;;;qBDxI3EzB,YAAY,EAAAgI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjI,aAAa,EACbC,eAAe,EAAAiI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflI,aAAa,EAAAmI,EAAA,CAAAC,OAAA,EACbnI,kBAAkB,EAAAoI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBrI,eAAe,EAAAsI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACfzI,cAAc,EAAA0I,EAAA,CAAAC,QAAA,EACd1I,mBAAmB,EAAA2I,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB7I,mBAAmB,EACnBC,WAAW,EAAA6I,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFzH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}