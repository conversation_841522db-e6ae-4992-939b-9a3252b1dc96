{"ast": null, "code": "import { inject } from '@angular/core';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nclass ResponsiveService {\n  constructor() {\n    this.breakpointObserver = inject(BreakpointObserver);\n  }\n  get isSmallDevice$() {\n    return this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).pipe(map(obs => obs.matches));\n  }\n  static {\n    this.ɵfac = function ResponsiveService_Factory(t) {\n      return new (t || ResponsiveService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsiveService,\n      factory: ResponsiveService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { ResponsiveService };", "map": {"version": 3, "names": ["inject", "BreakpointObserver", "Breakpoints", "map", "ResponsiveService", "constructor", "breakpointObserver", "isSmallDevice$", "observe", "XSmall", "Small", "pipe", "obs", "matches", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/responsive-service.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\nimport {\n\n  BreakpointObserver,\n  Breakpoints,\n} from '@angular/cdk/layout';\nimport { map } from 'rxjs';\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsiveService {\n  breakpointObserver = inject(BreakpointObserver);\n  constructor() {}\n  get isSmallDevice$() {\n    return this.breakpointObserver\n      .observe([Breakpoints.XSmall,Breakpoints.Small])\n      .pipe(map((obs) => obs.matches));\n  }\n}\n"], "mappings": "AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAClD,SAEEC,kBAAkB,EAClBC,WAAW,QACN,qBAAqB;AAC5B,SAASC,GAAG,QAAQ,MAAM;;AAC1B,MAGaC,iBAAiB;EAE5BC,YAAA;IADA,KAAAC,kBAAkB,GAAGN,MAAM,CAACC,kBAAkB,CAAC;EAChC;EACf,IAAIM,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACD,kBAAkB,CAC3BE,OAAO,CAAC,CAACN,WAAW,CAACO,MAAM,EAACP,WAAW,CAACQ,KAAK,CAAC,CAAC,CAC/CC,IAAI,CAACR,GAAG,CAAES,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC;EACpC;;;uBAPWT,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAU,OAAA,EAAjBV,iBAAiB,CAAAW,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA;;SAEPZ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}