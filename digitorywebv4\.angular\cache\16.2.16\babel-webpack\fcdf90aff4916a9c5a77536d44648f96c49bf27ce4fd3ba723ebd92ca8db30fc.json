{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass DialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    this.backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    this.closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    this.closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    this.closeOnOverlayDetachments = true;\n  }\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nlet CdkDialogContainer = /*#__PURE__*/(() => {\n  class CdkDialogContainer extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n      super();\n      this._elementRef = _elementRef;\n      this._focusTrapFactory = _focusTrapFactory;\n      this._config = _config;\n      this._interactivityChecker = _interactivityChecker;\n      this._ngZone = _ngZone;\n      this._overlayRef = _overlayRef;\n      this._focusMonitor = _focusMonitor;\n      /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n      this._elementFocusedBeforeDialogWasOpened = null;\n      /**\n       * Type of interaction that led to the dialog being closed. This is used to determine\n       * whether the focus style will be applied when returning focus to its original location\n       * after the dialog is closed.\n       */\n      this._closeInteractionType = null;\n      /**\n       * Queue of the IDs of the dialog's label element, based on their definition order. The first\n       * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n       * where there are two or more titles in the DOM at a time and the first one is destroyed while\n       * the rest are present.\n       */\n      this._ariaLabelledByQueue = [];\n      /**\n       * Attaches a DOM portal to the dialog container.\n       * @param portal Portal to be attached.\n       * @deprecated To be turned into a method.\n       * @breaking-change 10.0.0\n       */\n      this.attachDomPortal = portal => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._contentAttached();\n        return result;\n      };\n      this._document = _document;\n      if (this._config.ariaLabelledBy) {\n        this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n      }\n    }\n    _contentAttached() {\n      this._initializeFocusTrap();\n      this._handleBackdropClicks();\n      this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n      this._trapFocus();\n    }\n    ngOnDestroy() {\n      this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachComponentPortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n      if (!this._containsFocus()) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1;\n        // The tabindex attribute should be removed to avoid navigating to that element again\n        this._ngZone.runOutsideAngular(() => {\n          const callback = () => {\n            element.removeEventListener('blur', callback);\n            element.removeEventListener('mousedown', callback);\n            element.removeAttribute('tabindex');\n          };\n          element.addEventListener('blur', callback);\n          element.addEventListener('mousedown', callback);\n        });\n      }\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus() {\n      const element = this._elementRef.nativeElement;\n      // If were to attempt to focus immediately, then the content of the dialog would not yet be\n      // ready in instances where change detection has to run first. To deal with this, we simply\n      // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n      // dialog. If the element inside the dialog can't be focused, then the container is focused\n      // so the user can't tab into other elements behind it.\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus();\n          }\n          break;\n        case true:\n        case 'first-tabbable':\n          this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n            // If we weren't able to find a focusable element in the dialog, then focus the dialog\n            // container instead.\n            if (!focusedSuccessfully) {\n              this._focusDialogContainer();\n            }\n          });\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n          break;\n        default:\n          this._focusByCssSelector(this._config.autoFocus);\n          break;\n      }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n      const focusConfig = this._config.restoreFocus;\n      let focusTargetElement = null;\n      if (typeof focusConfig === 'string') {\n        focusTargetElement = this._document.querySelector(focusConfig);\n      } else if (typeof focusConfig === 'boolean') {\n        focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n      } else if (focusConfig) {\n        focusTargetElement = focusConfig;\n      }\n      // We need the extra check, because IE can set the `activeElement` to null in some cases.\n      if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n        const activeElement = _getFocusedElementPierceShadowDom();\n        const element = this._elementRef.nativeElement;\n        // Make sure that focus is still inside the dialog or is on the body (usually because a\n        // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n        // the consumer moved it themselves before the animation was done, in which case we shouldn't\n        // do anything.\n        if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n          if (this._focusMonitor) {\n            this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n            this._closeInteractionType = null;\n          } else {\n            focusTargetElement.focus();\n          }\n        }\n      }\n      if (this._focusTrap) {\n        this._focusTrap.destroy();\n      }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer() {\n      // Note that there is no focus method when rendering on the server.\n      if (this._elementRef.nativeElement.focus) {\n        this._elementRef.nativeElement.focus();\n      }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n      const element = this._elementRef.nativeElement;\n      const activeElement = _getFocusedElementPierceShadowDom();\n      return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n      // Clicking on the backdrop will move focus out of dialog.\n      // Recapture it if closing via the backdrop is disabled.\n      this._overlayRef.backdropClick().subscribe(() => {\n        if (this._config.disableClose) {\n          this._recaptureFocus();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function CdkDialogContainer_Factory(t) {\n        return new (t || CdkDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(DialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkDialogContainer,\n        selectors: [[\"cdk-dialog-container\"]],\n        viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n          }\n        },\n        hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n        hostVars: 6,\n        hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n          }\n        },\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 1,\n        vars: 0,\n        consts: [[\"cdkPortalOutlet\", \"\"]],\n        template: function CdkDialogContainer_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n          }\n        },\n        dependencies: [i3.CdkPortalOutlet],\n        styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return CdkDialogContainer;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    /** Emits when the dialog has been closed. */\n    this.closed = new Subject();\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = /*#__PURE__*/new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = /*#__PURE__*/new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nlet Dialog = /*#__PURE__*/(() => {\n  class Dialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n      this._overlay = _overlay;\n      this._injector = _injector;\n      this._defaultOptions = _defaultOptions;\n      this._parentDialog = _parentDialog;\n      this._overlayContainer = _overlayContainer;\n      this._openDialogsAtThisLevel = [];\n      this._afterAllClosedAtThisLevel = new Subject();\n      this._afterOpenedAtThisLevel = new Subject();\n      this._ariaHiddenElements = new Map();\n      /**\n       * Stream that emits when all open dialog have finished closing.\n       * Will emit on subscribe if there are no open dialogs to begin with.\n       */\n      this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n      this._scrollStrategy = scrollStrategy;\n    }\n    open(componentOrTemplateRef, config) {\n      const defaults = this._defaultOptions || new DialogConfig();\n      config = {\n        ...defaults,\n        ...config\n      };\n      config.id = config.id || `cdk-dialog-${uniqueId++}`;\n      if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n      }\n      const overlayConfig = this._getOverlayConfig(config);\n      const overlayRef = this._overlay.create(overlayConfig);\n      const dialogRef = new DialogRef(overlayRef, config);\n      const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n      dialogRef.containerInstance = dialogContainer;\n      this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n      // If this is the first dialog that we're opening, hide all the non-overlay content.\n      if (!this.openDialogs.length) {\n        this._hideNonDialogContentFromAssistiveTechnology();\n      }\n      this.openDialogs.push(dialogRef);\n      dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n      this.afterOpened.next(dialogRef);\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n      reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n      // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n      // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n      // determines when `aria-hidden` is removed from elements outside the dialog.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => {\n        // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n        if (dialog.config.closeOnDestroy === false) {\n          this._removeOpenDialog(dialog, false);\n        }\n      });\n      // Make a second pass and close the remaining dialogs. We do this second pass in order to\n      // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n      // that should be closed and dialogs that should not.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n      this._afterAllClosedAtThisLevel.complete();\n      this._afterOpenedAtThisLevel.complete();\n      this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n      const state = new OverlayConfig({\n        positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n        scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n        panelClass: config.panelClass,\n        hasBackdrop: config.hasBackdrop,\n        direction: config.direction,\n        minWidth: config.minWidth,\n        minHeight: config.minHeight,\n        maxWidth: config.maxWidth,\n        maxHeight: config.maxHeight,\n        width: config.width,\n        height: config.height,\n        disposeOnNavigation: config.closeOnNavigation\n      });\n      if (config.backdropClass) {\n        state.backdropClass = config.backdropClass;\n      }\n      return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DialogConfig,\n        useValue: config\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }, {\n        provide: OverlayRef,\n        useValue: overlay\n      }];\n      let containerType;\n      if (config.container) {\n        if (typeof config.container === 'function') {\n          containerType = config.container;\n        } else {\n          containerType = config.container.type;\n          providers.push(...config.container.providers(config));\n        }\n      } else {\n        containerType = CdkDialogContainer;\n      }\n      const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n        parent: userInjector || this._injector,\n        providers\n      }), config.componentFactoryResolver);\n      const containerRef = overlay.attach(containerPortal);\n      return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n      if (componentOrTemplateRef instanceof TemplateRef) {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n        let context = {\n          $implicit: config.data,\n          dialogRef\n        };\n        if (config.templateContext) {\n          context = {\n            ...context,\n            ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n          };\n        }\n        dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n      } else {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n        const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n        dialogRef.componentRef = contentRef;\n        dialogRef.componentInstance = contentRef.instance;\n      }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DIALOG_DATA,\n        useValue: config.data\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }];\n      if (config.providers) {\n        if (typeof config.providers === 'function') {\n          providers.push(...config.providers(dialogRef, config, dialogContainer));\n        } else {\n          providers.push(...config.providers);\n        }\n      }\n      if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n        optional: true\n      }))) {\n        providers.push({\n          provide: Directionality,\n          useValue: {\n            value: config.direction,\n            change: of()\n          }\n        });\n      }\n      return Injector.create({\n        parent: userInjector || fallbackInjector,\n        providers\n      });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        // If all the dialogs were closed, remove/restore the `aria-hidden`\n        // to a the siblings and emit to the `afterAllClosed` stream.\n        if (!this.openDialogs.length) {\n          this._ariaHiddenElements.forEach((previousValue, element) => {\n            if (previousValue) {\n              element.setAttribute('aria-hidden', previousValue);\n            } else {\n              element.removeAttribute('aria-hidden');\n            }\n          });\n          this._ariaHiddenElements.clear();\n          if (emitEvent) {\n            this._getAfterAllClosed().next();\n          }\n        }\n      }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n      const overlayContainer = this._overlayContainer.getContainerElement();\n      // Ensure that the overlay container is attached to the DOM.\n      if (overlayContainer.parentElement) {\n        const siblings = overlayContainer.parentElement.children;\n        for (let i = siblings.length - 1; i > -1; i--) {\n          const sibling = siblings[i];\n          if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n            this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n            sibling.setAttribute('aria-hidden', 'true');\n          }\n        }\n      }\n    }\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static {\n      this.ɵfac = function Dialog_Factory(t) {\n        return new (t || Dialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DEFAULT_DIALOG_CONFIG, 8), i0.ɵɵinject(Dialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(DIALOG_SCROLL_STRATEGY));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: Dialog,\n        factory: Dialog.ɵfac\n      });\n    }\n  }\n  return Dialog;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nlet DialogModule = /*#__PURE__*/(() => {\n  class DialogModule {\n    static {\n      this.ɵfac = function DialogModule_Factory(t) {\n        return new (t || DialogModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: DialogModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n        imports: [OverlayModule, PortalModule, A11yModule,\n        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n        // don't have to remember to import it or be faced with an unhelpful error.\n        PortalModule]\n      });\n    }\n  }\n  return DialogModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n//# sourceMappingURL=dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}