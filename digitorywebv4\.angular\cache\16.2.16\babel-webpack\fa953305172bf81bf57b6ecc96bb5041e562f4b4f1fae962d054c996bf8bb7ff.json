{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Start\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Resume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.startConversation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 17)(2, \"mat-icon\", 18);\n    i0.ɵɵtext(3, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Restaurant Onboarding Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"I'll help you collect information about your restaurant outlets, cuisines, and more.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 19)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ChatBotComponent_div_1_span_11_Template, 2, 0, \"span\", 20);\n    i0.ɵɵtemplate(12, ChatBotComponent_div_1_span_12_Template, 2, 0, \"span\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n  }\n}\nfunction ChatBotComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.previewRestaurantData());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Preview Data \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r10.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"div\", 25);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_14_div_5_Template, 3, 4, \"div\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r10.sender === \"user\", message_r10.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r10.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r10.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 31);\n    i0.ɵɵtext(7, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    this.hasRestaurantData = false;\n    this.restaurantData = null;\n    this.conversationStarted = false;\n    this.pendingQuestions = [];\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    this.dataUpdateSubscription = null;\n    // Flag to track if conversation history has been loaded\n    this.conversationHistoryLoaded = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n      this.loadConversationHistory();\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        }\n      }, 500); // Small delay to ensure history is loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n          console.log('Updated existing message');\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          if (!duplicateMessage) {\n            // Add new bot message\n            this.messages.push(message);\n            console.log('Added new bot message');\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n        // If this is a complete message (no blinking cursor), mark response as complete\n        if (!message.text.includes('blinking-cursor')) {\n          this.isWaitingForResponse = false;\n        }\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(data => {\n      console.log('Received data update:', data);\n      if (data && data.type === 'restaurant_data' && data.data) {\n        console.log('Setting restaurant data:', data.data);\n        this.restaurantData = data.data;\n        this.hasRestaurantData = true;\n        console.log('hasRestaurantData set to:', this.hasRestaurantData);\n        this.cd.detectChanges();\n        // Check if we need to ask more questions based on the data\n        this.updatePendingQuestions();\n        // If we have all the data, show a completion message\n        if (this.isDataComplete()) {\n          // Add a completion message\n          const completionMessage = {\n            id: this.generateId(),\n            text: 'Great! I have all the information I need. You can click the Preview button to see a summary of your restaurant data.',\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          this.messages.push(completionMessage);\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // No need to update restaurant summary\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n        // Set a timer to ask the next question after the response is received\n        setTimeout(() => {\n          // Check if we're still waiting for a response\n          if (!this.isWaitingForResponse && this.pendingQuestions.length > 0) {\n            // Ask the next pending question\n            const nextQuestion = this.pendingQuestions.shift();\n            if (nextQuestion) {\n              // Add the question as a bot message\n              const questionMessage = {\n                id: this.generateId(),\n                text: nextQuestion,\n                sender: 'bot',\n                timestamp: new Date()\n              };\n              this.messages.push(questionMessage);\n              this.cd.detectChanges();\n              this.scrollToBottom();\n            }\n          }\n        }, 2000); // Wait 2 seconds after response before asking next question\n      },\n\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  // Restaurant summary methods removed\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      console.log('Not loading conversation history: tenantId =', this.tenantId, 'conversationHistoryLoaded =', this.conversationHistoryLoaded);\n      return;\n    }\n    console.log('Loading conversation history for tenant:', this.tenantId);\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m => m.sender === message.sender && m.text === message.text);\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n            // No need to update restaurant summary\n          });\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n    // No need to reset restaurant summary\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to just the welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  // ngOnDestroy is already defined above\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Mark conversation as started\n    this.conversationStarted = true;\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    // Add initial welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant.',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Ask the first question after a short delay\n    setTimeout(() => {\n      this.askPendingQuestions();\n    }, 1000);\n  }\n  /**\n   * Ask questions about pending information\n   */\n  askPendingQuestions() {\n    if (!this.tenantId || !this.conversationStarted) {\n      return;\n    }\n    // Define the questions to ask based on what information we need\n    this.pendingQuestions = ['How many restaurant outlets do you have?', 'What are the names and addresses of your outlets?', 'What work areas do you have in each outlet (e.g., kitchen, bar, dining area)?', 'What cuisines do you serve across all your outlets?', 'What are your signature dishes?', 'Do you serve alcohol? If yes, what type of alcohol service do you offer?', 'Do you allow tobacco use in any of your outlets?'];\n    // Ask the first question\n    if (this.pendingQuestions.length > 0) {\n      const question = this.pendingQuestions.shift();\n      // Add the question as a bot message\n      const questionMessage = {\n        id: this.generateId(),\n        text: question,\n        sender: 'bot',\n        timestamp: new Date()\n      };\n      this.messages.push(questionMessage);\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  /**\n   * Check if all required restaurant data is complete\n   */\n  isDataComplete() {\n    if (!this.restaurantData) {\n      return false;\n    }\n    // Check if we have all the required data\n    const data = this.restaurantData;\n    // Check basic fields\n    if (!data.totalOutlets || data.totalOutlets <= 0) {\n      return false;\n    }\n    // Check outlet details\n    if (!data.outletDetails || data.outletDetails.length === 0) {\n      return false;\n    }\n    // Check if each outlet has required fields\n    for (const outlet of data.outletDetails) {\n      if (!outlet.outletName || !outlet.outletAddress || !outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0) {\n        return false;\n      }\n    }\n    // Check cuisines\n    if (!data.commonCuisinesAcrossOutlets || data.commonCuisinesAcrossOutlets.length === 0) {\n      return false;\n    }\n    // Check signature dishes\n    if (!data.signatureElements || !data.signatureElements.signatureDishes || data.signatureElements.signatureDishes.length === 0) {\n      return false;\n    }\n    // Check beverage info\n    if (!data.beverageInfo || !data.beverageInfo.alcoholService) {\n      return false;\n    }\n    // Check tobacco info\n    if (!data.tobaccoInfo || !data.tobaccoInfo.tobaccoService) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Update the list of pending questions based on the current data\n   */\n  updatePendingQuestions() {\n    if (!this.restaurantData) {\n      return;\n    }\n    // Clear existing questions\n    this.pendingQuestions = [];\n    const data = this.restaurantData;\n    // Check what information is missing and add relevant questions\n    if (!data.totalOutlets || data.totalOutlets <= 0) {\n      this.pendingQuestions.push('How many restaurant outlets do you have?');\n    }\n    if (!data.outletDetails || data.outletDetails.length === 0) {\n      this.pendingQuestions.push('What are the names and addresses of your outlets?');\n    } else {\n      // Check if any outlet is missing work areas\n      for (const outlet of data.outletDetails) {\n        if (!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0) {\n          this.pendingQuestions.push(`What work areas do you have in your ${outlet.outletName} outlet?`);\n        }\n      }\n    }\n    if (!data.commonCuisinesAcrossOutlets || data.commonCuisinesAcrossOutlets.length === 0) {\n      this.pendingQuestions.push('What cuisines do you serve across all your outlets?');\n    }\n    if (!data.signatureElements || !data.signatureElements.signatureDishes || data.signatureElements.signatureDishes.length === 0) {\n      this.pendingQuestions.push('What are your signature dishes?');\n    }\n    if (!data.beverageInfo || !data.beverageInfo.alcoholService) {\n      this.pendingQuestions.push('Do you serve alcohol? If yes, what type of alcohol service do you offer?');\n    }\n    if (!data.tobaccoInfo || !data.tobaccoInfo.tobaccoService) {\n      this.pendingQuestions.push('Do you allow tobacco use in any of your outlets?');\n    }\n    // If we have pending questions and we're not waiting for a response, ask the next question\n    if (this.pendingQuestions.length > 0 && !this.isWaitingForResponse) {\n      setTimeout(() => {\n        const nextQuestion = this.pendingQuestions.shift();\n        if (nextQuestion) {\n          // Add the question as a bot message\n          const questionMessage = {\n            id: this.generateId(),\n            text: nextQuestion,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n          this.messages.push(questionMessage);\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      }, 1000);\n    }\n  }\n  /**\n   * Preview restaurant data in a dialog\n   */\n  previewRestaurantData() {\n    if (!this.restaurantData) {\n      this.snackBar.open('No restaurant data available', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Create a formatted preview of the restaurant data\n    let previewContent = `# Restaurant Information\\n\\n`;\n    // Basic information\n    previewContent += `## Summary\\n`;\n    previewContent += `- **Total Outlets**: ${this.restaurantData.totalOutlets}\\n`;\n    previewContent += `- **Cuisines**: ${this.restaurantData.commonCuisinesAcrossOutlets.join(', ')}\\n`;\n    previewContent += `- **Signature Dishes**: ${this.restaurantData.signatureElements.signatureDishes.join(', ')}\\n`;\n    previewContent += `- **Alcohol Service**: ${this.restaurantData.beverageInfo.alcoholService}\\n`;\n    previewContent += `- **Tobacco Service**: ${this.restaurantData.tobaccoInfo.tobaccoService}\\n\\n`;\n    // Outlet details\n    previewContent += `## Outlet Details\\n`;\n    this.restaurantData.outletDetails.forEach((outlet, index) => {\n      previewContent += `### Outlet ${index + 1}: ${outlet.outletName}\\n`;\n      previewContent += `- **Address**: ${outlet.outletAddress}\\n`;\n      previewContent += `- **Work Areas**: ${outlet.outletWorkAreas.join(', ')}\\n\\n`;\n    });\n    // Display the preview in a snackbar or dialog\n    // For simplicity, we'll use a snackbar with a longer duration\n    this.snackBar.open('Restaurant data preview is available in the chat', 'Close', {\n      duration: 5000\n    });\n    // Add the preview as a system message in the chat\n    const previewMessage = {\n      id: this.generateId(),\n      text: previewContent,\n      sender: 'bot',\n      timestamp: new Date()\n    };\n    this.messages.push(previewMessage);\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 22,\n      vars: 8,\n      consts: [[1, \"chat-container\"], [\"class\", \"chat-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"Preview Restaurant Data\", \"class\", \"preview-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"chat-overlay\", 3, \"click\"], [1, \"overlay-content\"], [1, \"overlay-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-button\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"Preview Restaurant Data\", 1, \"preview-btn\", 3, \"click\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-text\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ChatBotComponent_div_1_Template, 13, 2, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6);\n          i0.ɵɵtemplate(9, ChatBotComponent_button_9_Template, 4, 0, \"button\", 7);\n          i0.ɵɵelementStart(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_10_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 6, 8, \"div\", 10);\n          i0.ɵɵtemplate(15, ChatBotComponent_div_15_Template, 8, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"mat-form-field\", 13)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_18_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_19_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"send\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.conversationStarted);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasRestaurantData);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n  position: relative; \\n\\n}\\n\\n\\n\\n.chat-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 80%;\\n}\\n\\n.overlay-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f57c00; \\n\\n  margin-bottom: 1rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-size: 1rem;\\n  background-color: #f57c00; \\n\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  transition: background-color 0.3s;\\n}\\n\\n.start-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800; \\n\\n}\\n\\n.start-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%], .preview-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  line-height: 32px;\\n  color: #333;\\n  min-width: 32px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 8px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%], .preview-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover, .preview-btn[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n  background-color: #e0e0e0;\\n  border-color: #ccc;\\n}\\n\\n.preview-btn[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #f57c00;\\n  margin-right: 8px;\\n  display: flex;\\n  align-items: center;\\n  padding: 0 12px;\\n  height: 36px;\\n}\\n\\n.preview-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800;\\n}\\n\\n.preview-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 10px 16px;\\n  border-radius: 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  margin-left: 8px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 2px;\\n  background-color: #999;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n  transform: translateY(0);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.9999s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 12px;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink-bounce {\\n  0%, 100% {\\n    opacity: 0.4;\\n    transform: translateY(0);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateY(-4px);\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.summary-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 15px;\\n  color: #555;\\n  font-weight: 500;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  color: #616161;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: auto;\\n  justify-content: flex-start;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 3px;\\n  background-color: #57705d;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  font-size: 13px;\\n  color: #555;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-4px) scale(1.2);\\n    opacity: 0.9;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatBotComponent_div_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "startConversation", "ɵɵtemplate", "ChatBotComponent_div_1_span_11_Template", "ChatBotComponent_div_1_span_12_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "messages", "length", "ChatBotComponent_button_9_Template_button_click_0_listener", "_r9", "ctx_r8", "previewRestaurantData", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r10", "timestamp", "ɵɵelement", "ChatBotComponent_div_14_div_5_Template", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "currentMessage", "isConnecting", "isWaitingForResponse", "hasRestaurantData", "restaurantData", "conversationStarted", "pendingQuestions", "messageSubscription", "connectionSubscription", "dataUpdateSubscription", "conversationHistoryLoaded", "ngOnChanges", "changes", "newTenantId", "currentValue", "prevTenantId", "previousValue", "console", "log", "loadConversationHistory", "ngOnInit", "conversationStarted<PERSON><PERSON>", "localStorage", "getItem", "setTimeout", "setItem", "messages$", "subscribe", "message", "id", "startsWith", "detectChanges", "substring", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "push", "sort", "a", "b", "getTime", "scrollToBottom", "isDuplicate", "some", "Math", "abs", "dataUpdates$", "data", "type", "updatePendingQuestions", "isDataComplete", "completionMessage", "generateId", "Date", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "userMessage", "next", "nextQuestion", "shift", "questionMessage", "error", "extractInformation", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "extractListItems", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "trackById", "_index", "for<PERSON>ach", "existingMessage", "clearConversationHistory", "askPendingQuestions", "question", "totalOutlets", "outletDetails", "outlet", "outletName", "outletAddress", "outletWorkAreas", "commonCuisinesAcrossOutlets", "signatureElements", "signatureDishes", "beverageInfo", "alcoholService", "tobaccoInfo", "tobaccoService", "previewContent", "join", "index", "previewMessage", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_div_1_Template", "ChatBotComponent_button_9_Template", "ChatBotComponent_Template_button_click_10_listener", "ChatBotComponent_div_14_Template", "ChatBotComponent_div_15_Template", "ChatBotComponent_Template_input_ngModelChange_18_listener", "$event", "ChatBotComponent_Template_input_keydown_18_listener", "ChatBotComponent_Template_button_click_19_listener", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n  hasRestaurantData: boolean = false;\n  restaurantData: any = null;\n  conversationStarted: boolean = false;\n  pendingQuestions: string[] = [];\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n  private dataUpdateSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if conversation history has been loaded\n  private conversationHistoryLoaded = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n      this.loadConversationHistory();\n\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        }\n      }, 500); // Small delay to ensure history is loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n            console.log('Updated existing message');\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            if (!duplicateMessage) {\n              // Add new bot message\n              this.messages.push(message);\n              console.log('Added new bot message');\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n\n          // If this is a complete message (no blinking cursor), mark response as complete\n          if (!message.text.includes('blinking-cursor')) {\n            this.isWaitingForResponse = false;\n          }\n\n          // Force change detection to update the UI immediately\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(\n      (data: any) => {\n        console.log('Received data update:', data);\n        if (data && data.type === 'restaurant_data' && data.data) {\n          console.log('Setting restaurant data:', data.data);\n          this.restaurantData = data.data;\n          this.hasRestaurantData = true;\n          console.log('hasRestaurantData set to:', this.hasRestaurantData);\n          this.cd.detectChanges();\n\n          // Check if we need to ask more questions based on the data\n          this.updatePendingQuestions();\n\n          // If we have all the data, show a completion message\n          if (this.isDataComplete()) {\n            // Add a completion message\n            const completionMessage: ChatMessage = {\n              id: this.generateId(),\n              text: 'Great! I have all the information I need. You can click the Preview button to see a summary of your restaurant data.',\n              sender: 'bot',\n              timestamp: new Date()\n            };\n\n            this.messages.push(completionMessage);\n            this.cd.detectChanges();\n            this.scrollToBottom();\n          }\n        }\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // No need to update restaurant summary\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n\n        // Set a timer to ask the next question after the response is received\n        setTimeout(() => {\n          // Check if we're still waiting for a response\n          if (!this.isWaitingForResponse && this.pendingQuestions.length > 0) {\n            // Ask the next pending question\n            const nextQuestion = this.pendingQuestions.shift();\n            if (nextQuestion) {\n              // Add the question as a bot message\n              const questionMessage: ChatMessage = {\n                id: this.generateId(),\n                text: nextQuestion,\n                sender: 'bot',\n                timestamp: new Date()\n              };\n\n              this.messages.push(questionMessage);\n              this.cd.detectChanges();\n              this.scrollToBottom();\n            }\n          }\n        }, 2000); // Wait 2 seconds after response before asking next question\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  // Restaurant summary methods removed\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      console.log('Not loading conversation history: tenantId =', this.tenantId, 'conversationHistoryLoaded =', this.conversationHistoryLoaded);\n      return;\n    }\n\n    console.log('Loading conversation history for tenant:', this.tenantId);\n\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m =>\n              m.sender === message.sender && m.text === message.text\n            );\n\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n\n            // No need to update restaurant summary\n          });\n\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n\n    // No need to reset restaurant summary\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to just the welcome message\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n  // ngOnDestroy is already defined above\n\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Mark conversation as started\n    this.conversationStarted = true;\n\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n\n    // Add initial welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant.',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Ask the first question after a short delay\n    setTimeout(() => {\n      this.askPendingQuestions();\n    }, 1000);\n  }\n\n  /**\n   * Ask questions about pending information\n   */\n  askPendingQuestions(): void {\n    if (!this.tenantId || !this.conversationStarted) {\n      return;\n    }\n\n    // Define the questions to ask based on what information we need\n    this.pendingQuestions = [\n      'How many restaurant outlets do you have?',\n      'What are the names and addresses of your outlets?',\n      'What work areas do you have in each outlet (e.g., kitchen, bar, dining area)?',\n      'What cuisines do you serve across all your outlets?',\n      'What are your signature dishes?',\n      'Do you serve alcohol? If yes, what type of alcohol service do you offer?',\n      'Do you allow tobacco use in any of your outlets?'\n    ];\n\n    // Ask the first question\n    if (this.pendingQuestions.length > 0) {\n      const question = this.pendingQuestions.shift();\n\n      // Add the question as a bot message\n      const questionMessage: ChatMessage = {\n        id: this.generateId(),\n        text: question,\n        sender: 'bot',\n        timestamp: new Date()\n      };\n\n      this.messages.push(questionMessage);\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n  /**\n   * Check if all required restaurant data is complete\n   */\n  private isDataComplete(): boolean {\n    if (!this.restaurantData) {\n      return false;\n    }\n\n    // Check if we have all the required data\n    const data = this.restaurantData;\n\n    // Check basic fields\n    if (!data.totalOutlets || data.totalOutlets <= 0) {\n      return false;\n    }\n\n    // Check outlet details\n    if (!data.outletDetails || data.outletDetails.length === 0) {\n      return false;\n    }\n\n    // Check if each outlet has required fields\n    for (const outlet of data.outletDetails) {\n      if (!outlet.outletName || !outlet.outletAddress || !outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0) {\n        return false;\n      }\n    }\n\n    // Check cuisines\n    if (!data.commonCuisinesAcrossOutlets || data.commonCuisinesAcrossOutlets.length === 0) {\n      return false;\n    }\n\n    // Check signature dishes\n    if (!data.signatureElements || !data.signatureElements.signatureDishes || data.signatureElements.signatureDishes.length === 0) {\n      return false;\n    }\n\n    // Check beverage info\n    if (!data.beverageInfo || !data.beverageInfo.alcoholService) {\n      return false;\n    }\n\n    // Check tobacco info\n    if (!data.tobaccoInfo || !data.tobaccoInfo.tobaccoService) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Update the list of pending questions based on the current data\n   */\n  private updatePendingQuestions(): void {\n    if (!this.restaurantData) {\n      return;\n    }\n\n    // Clear existing questions\n    this.pendingQuestions = [];\n\n    const data = this.restaurantData;\n\n    // Check what information is missing and add relevant questions\n    if (!data.totalOutlets || data.totalOutlets <= 0) {\n      this.pendingQuestions.push('How many restaurant outlets do you have?');\n    }\n\n    if (!data.outletDetails || data.outletDetails.length === 0) {\n      this.pendingQuestions.push('What are the names and addresses of your outlets?');\n    } else {\n      // Check if any outlet is missing work areas\n      for (const outlet of data.outletDetails) {\n        if (!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0) {\n          this.pendingQuestions.push(`What work areas do you have in your ${outlet.outletName} outlet?`);\n        }\n      }\n    }\n\n    if (!data.commonCuisinesAcrossOutlets || data.commonCuisinesAcrossOutlets.length === 0) {\n      this.pendingQuestions.push('What cuisines do you serve across all your outlets?');\n    }\n\n    if (!data.signatureElements || !data.signatureElements.signatureDishes || data.signatureElements.signatureDishes.length === 0) {\n      this.pendingQuestions.push('What are your signature dishes?');\n    }\n\n    if (!data.beverageInfo || !data.beverageInfo.alcoholService) {\n      this.pendingQuestions.push('Do you serve alcohol? If yes, what type of alcohol service do you offer?');\n    }\n\n    if (!data.tobaccoInfo || !data.tobaccoInfo.tobaccoService) {\n      this.pendingQuestions.push('Do you allow tobacco use in any of your outlets?');\n    }\n\n    // If we have pending questions and we're not waiting for a response, ask the next question\n    if (this.pendingQuestions.length > 0 && !this.isWaitingForResponse) {\n      setTimeout(() => {\n        const nextQuestion = this.pendingQuestions.shift();\n        if (nextQuestion) {\n          // Add the question as a bot message\n          const questionMessage: ChatMessage = {\n            id: this.generateId(),\n            text: nextQuestion,\n            sender: 'bot',\n            timestamp: new Date()\n          };\n\n          this.messages.push(questionMessage);\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      }, 1000);\n    }\n  }\n\n  /**\n   * Preview restaurant data in a dialog\n   */\n  previewRestaurantData(): void {\n    if (!this.restaurantData) {\n      this.snackBar.open('No restaurant data available', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Create a formatted preview of the restaurant data\n    let previewContent = `# Restaurant Information\\n\\n`;\n\n    // Basic information\n    previewContent += `## Summary\\n`;\n    previewContent += `- **Total Outlets**: ${this.restaurantData.totalOutlets}\\n`;\n    previewContent += `- **Cuisines**: ${this.restaurantData.commonCuisinesAcrossOutlets.join(', ')}\\n`;\n    previewContent += `- **Signature Dishes**: ${this.restaurantData.signatureElements.signatureDishes.join(', ')}\\n`;\n    previewContent += `- **Alcohol Service**: ${this.restaurantData.beverageInfo.alcoholService}\\n`;\n    previewContent += `- **Tobacco Service**: ${this.restaurantData.tobaccoInfo.tobaccoService}\\n\\n`;\n\n    // Outlet details\n    previewContent += `## Outlet Details\\n`;\n    this.restaurantData.outletDetails.forEach((outlet: any, index: number) => {\n      previewContent += `### Outlet ${index + 1}: ${outlet.outletName}\\n`;\n      previewContent += `- **Address**: ${outlet.outletAddress}\\n`;\n      previewContent += `- **Work Areas**: ${outlet.outletWorkAreas.join(', ')}\\n\\n`;\n    });\n\n    // Display the preview in a snackbar or dialog\n    // For simplicity, we'll use a snackbar with a longer duration\n    this.snackBar.open('Restaurant data preview is available in the chat', 'Close', {\n      duration: 5000\n    });\n\n    // Add the preview as a system message in the chat\n    const previewMessage: ChatMessage = {\n      id: this.generateId(),\n      text: previewContent,\n      sender: 'bot',\n      timestamp: new Date()\n    };\n\n    this.messages.push(previewMessage);\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n}\n", "<div class=\"chat-container\">\n  <!-- Start/Resume Overlay -->\n  <div class=\"chat-overlay\" *ngIf=\"!conversationStarted\" (click)=\"startConversation()\">\n    <div class=\"overlay-content\">\n      <mat-icon class=\"overlay-icon\">restaurant</mat-icon>\n      <h2>Restaurant Onboarding Assistant</h2>\n      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>\n      <button mat-raised-button color=\"primary\" class=\"start-button\">\n        <mat-icon>play_arrow</mat-icon>\n        <span *ngIf=\"messages.length === 0\">Click to Start</span>\n        <span *ngIf=\"messages.length > 0\">Click to Resume</span>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n      <span class=\"assistant-title\">Restaurant details</span>\n    </div>\n    <div class=\"chat-actions\">\n      <button mat-raised-button color=\"primary\" matTooltip=\"Preview Restaurant Data\" (click)=\"previewRestaurantData()\" class=\"preview-btn\" *ngIf=\"hasRestaurantData\">\n        <mat-icon>visibility</mat-icon>\n        Preview Data\n      </button>\n      <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n        <mat-icon>clear</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n      <div class=\"message-content\">\n        <div class=\"message-wrapper\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n          <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Improved loading indicator when waiting for a response -->\n    <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n      <div class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <div class=\"typing-text\">AI is thinking...</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"isConnecting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICHhDC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAR9DH,EAAA,CAAAC,cAAA,cAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,qDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAClFX,EAAA,CAAAC,cAAA,cAA6B;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2FAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3FH,EAAA,CAAAC,cAAA,iBAA+D;IACnDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAY,UAAA,KAAAC,uCAAA,mBAAyD;IACzDb,EAAA,CAAAY,UAAA,KAAAE,uCAAA,mBAAwD;IAC1Dd,EAAA,CAAAG,YAAA,EAAS;;;;IAFAH,EAAA,CAAAe,SAAA,IAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAA2B;IAC3BnB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAAyB;;;;;;IAWlCnB,EAAA,CAAAC,cAAA,iBAA+J;IAAhFD,EAAA,CAAAI,UAAA,mBAAAgB,2DAAA;MAAApB,EAAA,CAAAM,aAAA,CAAAe,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAY,MAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAC9GvB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYLH,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,OAAAC,WAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnH3B,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAA4B,SAAA,cAAsE;;IACtE5B,EAAA,CAAAY,UAAA,IAAAiB,sCAAA,kBAAmH;IACrH7B,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAL,WAAA,CAAAM,MAAA,aAAAN,WAAA,CAAAM,MAAA,YAAgG;IAGpJhC,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,cAAAhB,EAAA,CAAAiC,WAAA,OAAAP,WAAA,CAAAQ,IAAA,GAAAlC,EAAA,CAAAmC,cAAA,CAAqC;IAC/BnC,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,SAAAU,WAAA,CAAAM,MAAA,cAAiC;;;;;IAMvEhC,EAAA,CAAAC,cAAA,cAAwE;IAGlED,EAAA,CAAA4B,SAAA,WAAa;IAGf5B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADhCxD,MAqBaiC,gBAAgB;EAiB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAnBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAxB,QAAQ,GAAkB,EAAE;IAC5B,KAAAyB,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,gBAAgB,GAAa,EAAE;IAEvB,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,sBAAsB,GAAwB,IAAI;IAyB1D;IACQ,KAAAC,yBAAyB,GAAG,KAAK;EApBtC;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAGD,OAAO,CAAC,UAAU,CAAC,CAACE,YAAY;MACpD,MAAMC,YAAY,GAAGH,OAAO,CAAC,UAAU,CAAC,CAACI,aAAa;MAEtDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,YAAY,EAAE,IAAI,EAAEF,WAAW,CAAC;MAErE;MACA,IAAIA,WAAW,IAAIE,YAAY,KAAKF,WAAW,EAAE;QAC/CI,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAEL,WAAW,CAAC;QACnG,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC,IAAI,CAACS,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC7C,QAAQ,GAAG,EAAE;IAElB0C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAEzE;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBmB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI,CAACC,uBAAuB,EAAE;MAE9B;MACA,MAAME,sBAAsB,GAAG,wBAAwB,IAAI,CAACvB,QAAQ,EAAE;MACtE,IAAI,CAACO,mBAAmB,GAAGiB,YAAY,CAACC,OAAO,CAACF,sBAAsB,CAAC,KAAK,MAAM;MAElF;MACAG,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACjD,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAAC6B,mBAAmB,GAAG,IAAI;UAC/BiB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;;MAExD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;KACV,MAAM;MACLJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAAC3C,QAAQ,GAAG,EAAE;;IAGpB;IACA,IAAI,CAACgC,mBAAmB,GAAG,IAAI,CAACZ,UAAU,CAAC+B,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAACvC,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAIuC,OAAO,CAACC,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAC5B,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACN,EAAE,CAACmC,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAIH,OAAO,CAACvC,MAAM,KAAK,KAAK,EAAE;QAC5B4B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEU,OAAO,CAACC,EAAE,EAAED,OAAO,CAACrC,IAAI,CAACyC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAEvF;QACA,MAAMC,oBAAoB,GAAG,IAAI,CAAC1D,QAAQ,CAAC2D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC;QAE9E,IAAII,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAAC1D,QAAQ,CAAC0D,oBAAoB,CAAC,GAAGL,OAAO;UAC7CX,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;SACxC,MAAM;UACL;UACA,MAAMkB,gBAAgB,GAAG,IAAI,CAAC7D,QAAQ,CAAC8D,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAAC9C,MAAM,KAAK,KAAK,IAAI8C,CAAC,CAAC5C,IAAI,KAAKqC,OAAO,CAACrC,IAAI,IAAI,CAAC4C,CAAC,CAAC5C,IAAI,CAAC+C,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,IAAI,CAACF,gBAAgB,EAAE;YACrB;YACA,IAAI,CAAC7D,QAAQ,CAACgE,IAAI,CAACX,OAAO,CAAC;YAC3BX,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;YAEpC;YACA,IAAI,CAAC3C,QAAQ,CAACiE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzD,SAAS,CAAC2D,OAAO,EAAE,GAAGD,CAAC,CAAC1D,SAAS,CAAC2D,OAAO,EAAE,CAAC;;;QAI/E;QACA,IAAI,CAACf,OAAO,CAACrC,IAAI,CAAC+C,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C,IAAI,CAACpC,oBAAoB,GAAG,KAAK;;QAGnC;QACA,IAAI,CAACN,EAAE,CAACmC,aAAa,EAAE;QACvB,IAAI,CAACa,cAAc,EAAE;OACtB,MAAM,IAAIhB,OAAO,CAACvC,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAMwD,WAAW,GAAG,IAAI,CAACtE,QAAQ,CAACuE,IAAI,CAACX,CAAC,IACtCA,CAAC,CAAC9C,MAAM,KAAK,MAAM,IACnB8C,CAAC,CAAC5C,IAAI,KAAKqC,OAAO,CAACrC,IAAI,IACvBwD,IAAI,CAACC,GAAG,CAACb,CAAC,CAACnD,SAAS,CAAC2D,OAAO,EAAE,GAAGf,OAAO,CAAC5C,SAAS,CAAC2D,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACE,WAAW,EAAE;UAChB5B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,OAAO,CAACrC,IAAI,CAAC;UACjD;UACA,IAAI,CAAChB,QAAQ,CAACgE,IAAI,CAACX,OAAO,CAAC;UAE3B;UACA,IAAI,CAACrD,QAAQ,CAACiE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzD,SAAS,CAAC2D,OAAO,EAAE,GAAGD,CAAC,CAAC1D,SAAS,CAAC2D,OAAO,EAAE,CAAC;SAC5E,MAAM;UACL1B,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEU,OAAO,CAACrC,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACK,EAAE,CAACmC,aAAa,EAAE;MAEvB;MACA,IAAI,CAACa,cAAc,EAAE;IACvB,CAAC,CACF;IAED;IACA,IAAI,CAACnC,sBAAsB,GAAG,IAAI,CAACd,UAAU,CAACsD,YAAY,CAACtB,SAAS,CACjEuB,IAAS,IAAI;MACZjC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgC,IAAI,CAAC;MAC1C,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,IAAID,IAAI,CAACA,IAAI,EAAE;QACxDjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgC,IAAI,CAACA,IAAI,CAAC;QAClD,IAAI,CAAC9C,cAAc,GAAG8C,IAAI,CAACA,IAAI;QAC/B,IAAI,CAAC/C,iBAAiB,GAAG,IAAI;QAC7Bc,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACf,iBAAiB,CAAC;QAChE,IAAI,CAACP,EAAE,CAACmC,aAAa,EAAE;QAEvB;QACA,IAAI,CAACqB,sBAAsB,EAAE;QAE7B;QACA,IAAI,IAAI,CAACC,cAAc,EAAE,EAAE;UACzB;UACA,MAAMC,iBAAiB,GAAgB;YACrCzB,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;YACrBhE,IAAI,EAAE,sHAAsH;YAC5HF,MAAM,EAAE,KAAK;YACbL,SAAS,EAAE,IAAIwE,IAAI;WACpB;UAED,IAAI,CAACjF,QAAQ,CAACgE,IAAI,CAACe,iBAAiB,CAAC;UACrC,IAAI,CAAC1D,EAAE,CAACmC,aAAa,EAAE;UACvB,IAAI,CAACa,cAAc,EAAE;;;IAG3B,CAAC,CACF;EACH;EAEAa,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAClD,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACmD,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAAClD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACkD,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAACjD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACiD,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC/D,UAAU,CAACgE,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5D,cAAc,CAAC6D,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC/D,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACiE,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC9D,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM8D,aAAa,GAAG,IAAI,CAAChE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACJ,EAAE,CAACmC,aAAa,EAAE;IAEvB;IACA;IAEA;IAEA;IACA,MAAMkC,WAAW,GAAgB;MAC/BpC,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;MACrBhE,IAAI,EAAEyE,aAAa;MACnB3E,MAAM,EAAE,MAAM;MACdL,SAAS,EAAE,IAAIwE,IAAI;KACpB;IAED;IACA,MAAMX,WAAW,GAAG,IAAI,CAACtE,QAAQ,CAACuE,IAAI,CAACX,CAAC,IACtCA,CAAC,CAAC9C,MAAM,KAAK,MAAM,IACnB8C,CAAC,CAAC5C,IAAI,KAAKyE,aAAa,CACzB;IAED,IAAI,CAACnB,WAAW,EAAE;MAChB,IAAI,CAACtE,QAAQ,CAACgE,IAAI,CAAC0B,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC/D,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAACmC,aAAa,EAAE;IACvB,IAAI,CAACa,cAAc,EAAE;IAErB;IACA,IAAI,CAACjD,UAAU,CAACiE,WAAW,CAAC,IAAI,CAAC9D,QAAQ,EAAEkE,aAAa,CAAC,CAACrC,SAAS,CAAC;MAClEuC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAACjE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;QAEvB;QACAP,UAAU,CAAC,MAAK;UACd;UACA,IAAI,CAAC,IAAI,CAACtB,oBAAoB,IAAI,IAAI,CAACI,gBAAgB,CAAC9B,MAAM,GAAG,CAAC,EAAE;YAClE;YACA,MAAM2F,YAAY,GAAG,IAAI,CAAC7D,gBAAgB,CAAC8D,KAAK,EAAE;YAClD,IAAID,YAAY,EAAE;cAChB;cACA,MAAME,eAAe,GAAgB;gBACnCxC,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;gBACrBhE,IAAI,EAAE4E,YAAY;gBAClB9E,MAAM,EAAE,KAAK;gBACbL,SAAS,EAAE,IAAIwE,IAAI;eACpB;cAED,IAAI,CAACjF,QAAQ,CAACgE,IAAI,CAAC8B,eAAe,CAAC;cACnC,IAAI,CAACzE,EAAE,CAACmC,aAAa,EAAE;cACvB,IAAI,CAACa,cAAc,EAAE;;;QAG3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC;;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACrE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACL,QAAQ,CAACiE,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACnE,EAAE,CAACmC,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EAEA;;;;EAIQwC,kBAAkBA,CAAC3C,OAAe;IACxC;IACA,MAAM4C,cAAc,GAAG5C,OAAO,CAC3B6C,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQC,gBAAgBA,CAACjD,OAAe;IACtC;IACA,IAAIA,OAAO,CAACU,QAAQ,CAAC,GAAG,CAAC,IAAIV,OAAO,CAACU,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOV,OAAO,CACXkD,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnB,IAAI,EAAE,CAAC,CACxBoB,MAAM,CAACD,IAAI,IAAIA,IAAI,CAACxG,MAAM,GAAG,CAAC,CAAC,CAC/BuG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGK,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACL,kBAAkB,CAAC3C,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAsD,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAC1B,WAAW,EAAE;;EAEtB;EAEA;;;EAGQhB,cAAcA,CAAA;IACpB;IACA2C,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQrC,UAAUA,CAAA;IAChB,OAAOR,IAAI,CAAC8C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGe,IAAI,CAAC8C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGA+D,SAASA,CAACC,MAAc,EAAEpE,OAAoB;IAC5C,OAAOA,OAAO,CAACC,EAAE;EACnB;EAEA;;;EAGAV,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACrB,QAAQ,IAAI,IAAI,CAACY,yBAAyB,EAAE;MACpDO,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,IAAI,CAACpB,QAAQ,EAAE,6BAA6B,EAAE,IAAI,CAACY,yBAAyB,CAAC;MACzI;;IAGFO,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACY,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAACT,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACN,UAAU,CAACwB,uBAAuB,CAAC,IAAI,CAACrB,QAAQ,EAAE,KAAK,CAAC,CAAC6B,SAAS,CAAC;MACtEuC,IAAI,EAAG3F,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,IAAI,CAACD,QAAQ,GAAG,EAAE;UAElB;UACAA,QAAQ,CAAC0H,OAAO,CAACrE,OAAO,IAAG;YACzB;YACA,MAAMsE,eAAe,GAAG,IAAI,CAAC3H,QAAQ,CAAC8D,IAAI,CAACF,CAAC,IAC1CA,CAAC,CAAC9C,MAAM,KAAKuC,OAAO,CAACvC,MAAM,IAAI8C,CAAC,CAAC5C,IAAI,KAAKqC,OAAO,CAACrC,IAAI,CACvD;YAED,IAAI,CAAC2G,eAAe,EAAE;cACpB,IAAI,CAAC3H,QAAQ,CAACgE,IAAI,CAACX,OAAO,CAAC;;YAG7B;UACF,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAACrD,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACD,QAAQ,CAACgE,IAAI,CAAC;cACjBV,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;cACrBhE,IAAI,EAAE,2KAA2K;cACjLF,MAAM,EAAE,KAAK;cACbL,SAAS,EAAE,IAAIwE,IAAI;aACpB,CAAC;;SAEL,MAAM;UACL;UACA,IAAI,CAACjF,QAAQ,GAAG,CAAC;YACfsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;YACrBhE,IAAI,EAAE,2KAA2K;YACjLF,MAAM,EAAE,KAAK;YACbL,SAAS,EAAE,IAAIwE,IAAI;WACpB,CAAC;;QAGJ,IAAI,CAACvD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAACmC,aAAa,EAAE;QACvB,IAAI,CAACa,cAAc,EAAE;MACvB,CAAC;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACrE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAAC3B,QAAQ,GAAG,CAAC;UACfsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;UACrBhE,IAAI,EAAE,2KAA2K;UACjLF,MAAM,EAAE,KAAK;UACbL,SAAS,EAAE,IAAIwE,IAAI;SACpB,CAAC;QAEF,IAAI,CAAC5D,EAAE,CAACmC,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAoE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAAClG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;IAEvBd,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAEvE;IAEA;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBmB,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAACpB,QAAQ,CAAC;MAC3F,IAAI,CAACH,UAAU,CAACwG,wBAAwB,CAAC,IAAI,CAACrG,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC6B,SAAS,CAAC;QAC5EuC,IAAI,EAAEA,CAAA,KAAK;UACTjD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAAC3C,QAAQ,GAAG,CACd;YACEsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;YACrBhE,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbL,SAAS,EAAE,IAAIwE,IAAI;WACpB,CACF;UAED;UACA,IAAI,CAAC9C,yBAAyB,GAAG,KAAK;UAEtC,IAAI,CAACT,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;UACvB,IAAI,CAACa,cAAc,EAAE;QACvB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACfrD,OAAO,CAACqD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAAC/F,QAAQ,GAAG,CACd;YACEsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;YACrBhE,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbL,SAAS,EAAE,IAAIwE,IAAI;WACpB,CACF;UAED,IAAI,CAAC9C,yBAAyB,GAAG,KAAK;UACtC,IAAI,CAACT,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;UACvB,IAAI,CAACa,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACrE,QAAQ,GAAG,CACd;QACEsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;QACrBhE,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAIwE,IAAI;OACpB,CACF;MAED,IAAI,CAAC9C,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACT,YAAY,GAAG,KAAK;MACzB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;MACvB,IAAI,CAACa,cAAc,EAAE;;EAEzB;EAEA;EAEA;;;EAGA5E,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC8B,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACiE,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;QACzEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC1D,mBAAmB,GAAG,IAAI;IAE/B;IACA,MAAMgB,sBAAsB,GAAG,wBAAwB,IAAI,CAACvB,QAAQ,EAAE;IACtEwB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;IAEpD;IACA,IAAI,CAAC9C,QAAQ,GAAG,CACd;MACEsD,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;MACrBhE,IAAI,EAAE,2GAA2G;MACjHF,MAAM,EAAE,KAAK;MACbL,SAAS,EAAE,IAAIwE,IAAI;KACpB,CACF;IAED;IACA,IAAI,CAAC5D,EAAE,CAACmC,aAAa,EAAE;IACvB,IAAI,CAACa,cAAc,EAAE;IAErB;IACApB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC4E,mBAAmB,EAAE;IAC5B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGAA,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACtG,QAAQ,IAAI,CAAC,IAAI,CAACO,mBAAmB,EAAE;MAC/C;;IAGF;IACA,IAAI,CAACC,gBAAgB,GAAG,CACtB,0CAA0C,EAC1C,mDAAmD,EACnD,+EAA+E,EAC/E,qDAAqD,EACrD,iCAAiC,EACjC,0EAA0E,EAC1E,kDAAkD,CACnD;IAED;IACA,IAAI,IAAI,CAACA,gBAAgB,CAAC9B,MAAM,GAAG,CAAC,EAAE;MACpC,MAAM6H,QAAQ,GAAG,IAAI,CAAC/F,gBAAgB,CAAC8D,KAAK,EAAE;MAE9C;MACA,MAAMC,eAAe,GAAgB;QACnCxC,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;QACrBhE,IAAI,EAAE8G,QAAQ;QACdhH,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAIwE,IAAI;OACpB;MAED,IAAI,CAACjF,QAAQ,CAACgE,IAAI,CAAC8B,eAAe,CAAC;MACnC,IAAI,CAACzE,EAAE,CAACmC,aAAa,EAAE;MACvB,IAAI,CAACa,cAAc,EAAE;;EAEzB;EAEA;;;EAGQS,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACjD,cAAc,EAAE;MACxB,OAAO,KAAK;;IAGd;IACA,MAAM8C,IAAI,GAAG,IAAI,CAAC9C,cAAc;IAEhC;IACA,IAAI,CAAC8C,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAACoD,YAAY,IAAI,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,IAAI,CAACpD,IAAI,CAACqD,aAAa,IAAIrD,IAAI,CAACqD,aAAa,CAAC/H,MAAM,KAAK,CAAC,EAAE;MAC1D,OAAO,KAAK;;IAGd;IACA,KAAK,MAAMgI,MAAM,IAAItD,IAAI,CAACqD,aAAa,EAAE;MACvC,IAAI,CAACC,MAAM,CAACC,UAAU,IAAI,CAACD,MAAM,CAACE,aAAa,IAAI,CAACF,MAAM,CAACG,eAAe,IAAIH,MAAM,CAACG,eAAe,CAACnI,MAAM,KAAK,CAAC,EAAE;QACjH,OAAO,KAAK;;;IAIhB;IACA,IAAI,CAAC0E,IAAI,CAAC0D,2BAA2B,IAAI1D,IAAI,CAAC0D,2BAA2B,CAACpI,MAAM,KAAK,CAAC,EAAE;MACtF,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC0E,IAAI,CAAC2D,iBAAiB,IAAI,CAAC3D,IAAI,CAAC2D,iBAAiB,CAACC,eAAe,IAAI5D,IAAI,CAAC2D,iBAAiB,CAACC,eAAe,CAACtI,MAAM,KAAK,CAAC,EAAE;MAC7H,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC0E,IAAI,CAAC6D,YAAY,IAAI,CAAC7D,IAAI,CAAC6D,YAAY,CAACC,cAAc,EAAE;MAC3D,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC9D,IAAI,CAAC+D,WAAW,IAAI,CAAC/D,IAAI,CAAC+D,WAAW,CAACC,cAAc,EAAE;MACzD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;;;EAGQ9D,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAAChD,cAAc,EAAE;MACxB;;IAGF;IACA,IAAI,CAACE,gBAAgB,GAAG,EAAE;IAE1B,MAAM4C,IAAI,GAAG,IAAI,CAAC9C,cAAc;IAEhC;IACA,IAAI,CAAC8C,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAACoD,YAAY,IAAI,CAAC,EAAE;MAChD,IAAI,CAAChG,gBAAgB,CAACiC,IAAI,CAAC,0CAA0C,CAAC;;IAGxE,IAAI,CAACW,IAAI,CAACqD,aAAa,IAAIrD,IAAI,CAACqD,aAAa,CAAC/H,MAAM,KAAK,CAAC,EAAE;MAC1D,IAAI,CAAC8B,gBAAgB,CAACiC,IAAI,CAAC,mDAAmD,CAAC;KAChF,MAAM;MACL;MACA,KAAK,MAAMiE,MAAM,IAAItD,IAAI,CAACqD,aAAa,EAAE;QACvC,IAAI,CAACC,MAAM,CAACG,eAAe,IAAIH,MAAM,CAACG,eAAe,CAACnI,MAAM,KAAK,CAAC,EAAE;UAClE,IAAI,CAAC8B,gBAAgB,CAACiC,IAAI,CAAC,uCAAuCiE,MAAM,CAACC,UAAU,UAAU,CAAC;;;;IAKpG,IAAI,CAACvD,IAAI,CAAC0D,2BAA2B,IAAI1D,IAAI,CAAC0D,2BAA2B,CAACpI,MAAM,KAAK,CAAC,EAAE;MACtF,IAAI,CAAC8B,gBAAgB,CAACiC,IAAI,CAAC,qDAAqD,CAAC;;IAGnF,IAAI,CAACW,IAAI,CAAC2D,iBAAiB,IAAI,CAAC3D,IAAI,CAAC2D,iBAAiB,CAACC,eAAe,IAAI5D,IAAI,CAAC2D,iBAAiB,CAACC,eAAe,CAACtI,MAAM,KAAK,CAAC,EAAE;MAC7H,IAAI,CAAC8B,gBAAgB,CAACiC,IAAI,CAAC,iCAAiC,CAAC;;IAG/D,IAAI,CAACW,IAAI,CAAC6D,YAAY,IAAI,CAAC7D,IAAI,CAAC6D,YAAY,CAACC,cAAc,EAAE;MAC3D,IAAI,CAAC1G,gBAAgB,CAACiC,IAAI,CAAC,0EAA0E,CAAC;;IAGxG,IAAI,CAACW,IAAI,CAAC+D,WAAW,IAAI,CAAC/D,IAAI,CAAC+D,WAAW,CAACC,cAAc,EAAE;MACzD,IAAI,CAAC5G,gBAAgB,CAACiC,IAAI,CAAC,kDAAkD,CAAC;;IAGhF;IACA,IAAI,IAAI,CAACjC,gBAAgB,CAAC9B,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC0B,oBAAoB,EAAE;MAClEsB,UAAU,CAAC,MAAK;QACd,MAAM2C,YAAY,GAAG,IAAI,CAAC7D,gBAAgB,CAAC8D,KAAK,EAAE;QAClD,IAAID,YAAY,EAAE;UAChB;UACA,MAAME,eAAe,GAAgB;YACnCxC,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;YACrBhE,IAAI,EAAE4E,YAAY;YAClB9E,MAAM,EAAE,KAAK;YACbL,SAAS,EAAE,IAAIwE,IAAI;WACpB;UAED,IAAI,CAACjF,QAAQ,CAACgE,IAAI,CAAC8B,eAAe,CAAC;UACnC,IAAI,CAACzE,EAAE,CAACmC,aAAa,EAAE;UACvB,IAAI,CAACa,cAAc,EAAE;;MAEzB,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGAhE,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACwB,cAAc,EAAE;MACxB,IAAI,CAACP,QAAQ,CAACiE,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;QAC1DC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAIoD,cAAc,GAAG,8BAA8B;IAEnD;IACAA,cAAc,IAAI,cAAc;IAChCA,cAAc,IAAI,wBAAwB,IAAI,CAAC/G,cAAc,CAACkG,YAAY,IAAI;IAC9Ea,cAAc,IAAI,mBAAmB,IAAI,CAAC/G,cAAc,CAACwG,2BAA2B,CAACQ,IAAI,CAAC,IAAI,CAAC,IAAI;IACnGD,cAAc,IAAI,2BAA2B,IAAI,CAAC/G,cAAc,CAACyG,iBAAiB,CAACC,eAAe,CAACM,IAAI,CAAC,IAAI,CAAC,IAAI;IACjHD,cAAc,IAAI,0BAA0B,IAAI,CAAC/G,cAAc,CAAC2G,YAAY,CAACC,cAAc,IAAI;IAC/FG,cAAc,IAAI,0BAA0B,IAAI,CAAC/G,cAAc,CAAC6G,WAAW,CAACC,cAAc,MAAM;IAEhG;IACAC,cAAc,IAAI,qBAAqB;IACvC,IAAI,CAAC/G,cAAc,CAACmG,aAAa,CAACN,OAAO,CAAC,CAACO,MAAW,EAAEa,KAAa,KAAI;MACvEF,cAAc,IAAI,cAAcE,KAAK,GAAG,CAAC,KAAKb,MAAM,CAACC,UAAU,IAAI;MACnEU,cAAc,IAAI,kBAAkBX,MAAM,CAACE,aAAa,IAAI;MAC5DS,cAAc,IAAI,qBAAqBX,MAAM,CAACG,eAAe,CAACS,IAAI,CAAC,IAAI,CAAC,MAAM;IAChF,CAAC,CAAC;IAEF;IACA;IACA,IAAI,CAACvH,QAAQ,CAACiE,IAAI,CAAC,kDAAkD,EAAE,OAAO,EAAE;MAC9EC,QAAQ,EAAE;KACX,CAAC;IAEF;IACA,MAAMuD,cAAc,GAAgB;MAClCzF,EAAE,EAAE,IAAI,CAAC0B,UAAU,EAAE;MACrBhE,IAAI,EAAE4H,cAAc;MACpB9H,MAAM,EAAE,KAAK;MACbL,SAAS,EAAE,IAAIwE,IAAI;KACpB;IAED,IAAI,CAACjF,QAAQ,CAACgE,IAAI,CAAC+E,cAAc,CAAC;IAClC,IAAI,CAAC1H,EAAE,CAACmC,aAAa,EAAE;IACvB,IAAI,CAACa,cAAc,EAAE;EACvB;;;uBA3vBWnD,gBAAgB,EAAApC,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAlK,EAAA,CAAAqK,iBAAA,GAAArK,EAAA,CAAAkK,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBnI,gBAAgB;MAAAoI,SAAA;MAAAC,MAAA;QAAAhI,QAAA;QAAAC,UAAA;MAAA;MAAAgI,UAAA;MAAAC,QAAA,GAAA3K,EAAA,CAAA4K,oBAAA,EAAA5K,EAAA,CAAA6K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtC7BnL,EAAA,CAAAC,cAAA,aAA4B;UAE1BD,EAAA,CAAAY,UAAA,IAAAyK,+BAAA,kBAWM;UAENrL,EAAA,CAAAC,cAAA,aAAyB;UAEOD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAY,UAAA,IAAA0K,kCAAA,oBAGS;UACTtL,EAAA,CAAAC,cAAA,iBAAuG;UAAvDD,EAAA,CAAAI,UAAA,mBAAAmL,mDAAA;YAAA,OAASH,GAAA,CAAAtC,wBAAA,EAA0B;UAAA,EAAC;UAClF9I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKhCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAY,UAAA,KAAA4K,gCAAA,kBAOM;UAGNxL,EAAA,CAAAY,UAAA,KAAA6K,gCAAA,kBASM;UACRzL,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAI,UAAA,2BAAAsL,0DAAAC,MAAA;YAAA,OAAAP,GAAA,CAAAzI,cAAA,GAAAgJ,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBP,GAAA,CAAAvD,UAAA,CAAA8D,MAAA,CAAkB;UAAA,EAFD;UADnC3L,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAI,UAAA,mBAAAyL,mDAAA;YAAA,OAAST,GAAA,CAAA7E,WAAA,EAAa;UAAA,EAAC;UAC1DvG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;;;UA7DFH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,UAAAoK,GAAA,CAAApI,mBAAA,CAA0B;UAmBqFhD,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAAtI,iBAAA,CAAuB;UAWtI9C,EAAA,CAAAe,SAAA,GAAa;UAAbf,EAAA,CAAAgB,UAAA,YAAAoK,GAAA,CAAAlK,QAAA,CAAa,iBAAAkK,GAAA,CAAA1C,SAAA;UAUhC1I,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAAoK,GAAA,CAAAvI,oBAAA,CAA0B;UAevB7C,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,YAAAoK,GAAA,CAAAzI,cAAA,CAA4B,aAAAyI,GAAA,CAAAxI,YAAA;UAKwB5C,EAAA,CAAAe,SAAA,GAAmD;UAAnDf,EAAA,CAAAgB,UAAA,cAAAoK,GAAA,CAAAzI,cAAA,CAAA6D,IAAA,MAAA4E,GAAA,CAAAxI,YAAA,CAAmD;;;qBDzChHvD,YAAY,EAAAyM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ5M,WAAW,EAAA6M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX/M,mBAAmB,EACnBC,eAAe,EAAA+M,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACfjN,aAAa,EACbC,kBAAkB,EAAAiN,EAAA,CAAAC,YAAA,EAClBjN,aAAa,EAAAkN,EAAA,CAAAC,OAAA,EACblN,cAAc,EAAAmN,EAAA,CAAAC,QAAA,EACdnN,wBAAwB,EACxBC,gBAAgB,EAAAmN,EAAA,CAAAC,UAAA,EAEhBnN,YAAY;MAAAoN,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMHhL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}