{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDrawerModule } from '@angular/material/sidenav';\nimport { FormsModule } from '@angular/forms';\nlet SmartDashboardComponent = class SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    // Filter options\n    this.timePeriods = [{\n      value: 'last30days',\n      label: 'Last 30 days'\n    }, {\n      value: 'last60days',\n      label: 'Last 60 days'\n    }, {\n      value: 'last90days',\n      label: 'Last 90 days'\n    }, {\n      value: 'custom',\n      label: 'Custom Range'\n    }];\n    this.categories = [{\n      value: 'ecommerce',\n      label: 'E-commerce',\n      checked: false\n    }, {\n      value: 'saas',\n      label: 'SaaS',\n      checked: false\n    }, {\n      value: 'mobile',\n      label: 'Mobile Apps',\n      checked: false\n    }, {\n      value: 'marketing',\n      label: 'Marketing',\n      checked: false\n    }, {\n      value: 'support',\n      label: 'Support',\n      checked: false\n    }];\n    this.regions = [{\n      value: 'north-america',\n      label: 'North America',\n      checked: false\n    }, {\n      value: 'europe',\n      label: 'Europe',\n      checked: false\n    }, {\n      value: 'asia-pacific',\n      label: 'Asia Pacific',\n      checked: false\n    }, {\n      value: 'latin-america',\n      label: 'Latin America',\n      checked: false\n    }, {\n      value: 'africa',\n      label: 'Africa',\n      checked: false\n    }];\n    this.keyMetrics = [{\n      value: 'revenue',\n      label: 'Revenue',\n      checked: true\n    }, {\n      value: 'users',\n      label: 'Users',\n      checked: true\n    }, {\n      value: 'conversions',\n      label: 'Conversions',\n      checked: false\n    }, {\n      value: 'engagement',\n      label: 'Engagement',\n      checked: false\n    }, {\n      value: 'retention',\n      label: 'Retention',\n      checked: false\n    }];\n    // Selected values\n    this.selectedTimePeriod = 'last30days';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message\n      console.log('Chat message:', this.chatMessage);\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset all filters to default\n    this.selectedTimePeriod = 'last30days';\n    this.categories.forEach(cat => cat.checked = false);\n    this.regions.forEach(region => region.checked = false);\n    this.keyMetrics.forEach(metric => {\n      metric.checked = metric.value === 'revenue' || metric.value === 'users';\n    });\n  }\n};\nSmartDashboardComponent = __decorate([Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatSelectModule, MatInputModule, MatCheckboxModule, MatTabsModule, MatDrawerModule, FormsModule],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})], SmartDashboardComponent);\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatDrawerModule", "FormsModule", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "timePeriods", "value", "label", "categories", "checked", "regions", "keyMetrics", "selectedTimePeriod", "chatMessage", "tabs", "active", "ngOnInit", "toggleFilters", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "console", "log", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "cat", "region", "metric", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDrawerModule } from '@angular/material/sidenav';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatDrawerModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  \n  // Filter options\n  timePeriods = [\n    { value: 'last30days', label: 'Last 30 days' },\n    { value: 'last60days', label: 'Last 60 days' },\n    { value: 'last90days', label: 'Last 90 days' },\n    { value: 'custom', label: 'Custom Range' }\n  ];\n  \n  categories = [\n    { value: 'ecommerce', label: 'E-commerce', checked: false },\n    { value: 'saas', label: 'SaaS', checked: false },\n    { value: 'mobile', label: 'Mobile Apps', checked: false },\n    { value: 'marketing', label: 'Marketing', checked: false },\n    { value: 'support', label: 'Support', checked: false }\n  ];\n  \n  regions = [\n    { value: 'north-america', label: 'North America', checked: false },\n    { value: 'europe', label: 'Europe', checked: false },\n    { value: 'asia-pacific', label: 'Asia Pacific', checked: false },\n    { value: 'latin-america', label: 'Latin America', checked: false },\n    { value: 'africa', label: 'Africa', checked: false }\n  ];\n  \n  keyMetrics = [\n    { value: 'revenue', label: 'Revenue', checked: true },\n    { value: 'users', label: 'Users', checked: true },\n    { value: 'conversions', label: 'Conversions', checked: false },\n    { value: 'engagement', label: 'Engagement', checked: false },\n    { value: 'retention', label: 'Retention', checked: false }\n  ];\n  \n  // Selected values\n  selectedTimePeriod = 'last30days';\n  chatMessage = '';\n  \n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message\n      console.log('Chat message:', this.chatMessage);\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset all filters to default\n    this.selectedTimePeriod = 'last30days';\n    this.categories.forEach(cat => cat.checked = false);\n    this.regions.forEach(region => region.checked = false);\n    this.keyMetrics.forEach(metric => {\n      metric.checked = metric.value === 'revenue' || metric.value === 'users';\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,WAAW,QAAQ,gBAAgB;AAqB5C,IAAaC,uBAAuB,GAApC,MAAaA,uBAAuB;EAgDlCC,YAAA;IA/CA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,WAAW,GAAG,CACZ;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC9C;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC9C;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,CAC3C;IAED,KAAAC,UAAU,GAAG,CACX;MAAEF,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE,YAAY;MAAEE,OAAO,EAAE;IAAK,CAAE,EAC3D;MAAEH,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEE,OAAO,EAAE;IAAK,CAAE,EAChD;MAAEH,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,aAAa;MAAEE,OAAO,EAAE;IAAK,CAAE,EACzD;MAAEH,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEE,OAAO,EAAE;IAAK,CAAE,EAC1D;MAAEH,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEE,OAAO,EAAE;IAAK,CAAE,CACvD;IAED,KAAAC,OAAO,GAAG,CACR;MAAEJ,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE,eAAe;MAAEE,OAAO,EAAE;IAAK,CAAE,EAClE;MAAEH,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEE,OAAO,EAAE;IAAK,CAAE,EACpD;MAAEH,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE,cAAc;MAAEE,OAAO,EAAE;IAAK,CAAE,EAChE;MAAEH,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE,eAAe;MAAEE,OAAO,EAAE;IAAK,CAAE,EAClE;MAAEH,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEE,OAAO,EAAE;IAAK,CAAE,CACrD;IAED,KAAAE,UAAU,GAAG,CACX;MAAEL,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEE,OAAO,EAAE;IAAI,CAAE,EACrD;MAAEH,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAEE,OAAO,EAAE;IAAI,CAAE,EACjD;MAAEH,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAEE,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAEH,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE,YAAY;MAAEE,OAAO,EAAE;IAAK,CAAE,EAC5D;MAAEH,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEE,OAAO,EAAE;IAAK,CAAE,CAC3D;IAED;IACA,KAAAG,kBAAkB,GAAG,YAAY;IACjC,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAEP,KAAK,EAAE,YAAY;MAAEQ,MAAM,EAAE;IAAI,CAAE,EACrC;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAER,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAK,CAAE,EACxC;MAAER,KAAK,EAAE,kBAAkB;MAAEQ,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBC,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACd,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAe,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACf,WAAW,GAAGe,KAAK;IACxB,IAAI,CAACL,IAAI,CAACM,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACN,MAAM,GAAGO,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACV,WAAW,CAACW,IAAI,EAAE,EAAE;MAC3B;MACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,WAAW,CAAC;MAC9C,IAAI,CAACA,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAEAS,YAAYA,CAAA;IACV;IACAP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACvB,aAAa,GAAG,KAAK;EAC5B;EAEA8B,YAAYA,CAAA;IACV;IACA,IAAI,CAACrB,kBAAkB,GAAG,YAAY;IACtC,IAAI,CAACJ,UAAU,CAACY,OAAO,CAACc,GAAG,IAAIA,GAAG,CAACzB,OAAO,GAAG,KAAK,CAAC;IACnD,IAAI,CAACC,OAAO,CAACU,OAAO,CAACe,MAAM,IAAIA,MAAM,CAAC1B,OAAO,GAAG,KAAK,CAAC;IACtD,IAAI,CAACE,UAAU,CAACS,OAAO,CAACgB,MAAM,IAAG;MAC/BA,MAAM,CAAC3B,OAAO,GAAG2B,MAAM,CAAC9B,KAAK,KAAK,SAAS,IAAI8B,MAAM,CAAC9B,KAAK,KAAK,OAAO;IACzE,CAAC,CAAC;EACJ;CACD;AA9FYL,uBAAuB,GAAAoC,UAAA,EAnBnChD,SAAS,CAAC;EACTiD,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlD,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,WAAW,CACZ;EACDyC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWzC,uBAAuB,CA8FnC;SA9FYA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}