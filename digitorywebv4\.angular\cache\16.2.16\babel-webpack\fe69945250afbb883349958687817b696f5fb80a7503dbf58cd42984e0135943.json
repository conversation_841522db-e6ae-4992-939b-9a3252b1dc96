{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r12.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"div\", 28);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_13_div_5_Template, 3, 4, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r12.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r12.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵtext(6, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_37_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r16);\n  }\n}\nfunction ChatBotComponent_ul_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_37_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_42_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r18 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r18);\n  }\n}\nfunction ChatBotComponent_ul_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_42_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 37)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    // Flag to track if conversation history has been loaded\n    this.conversationHistoryLoaded = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          if (!duplicateMessage) {\n            // Add new bot message\n            this.messages.push(message);\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m => m.sender === message.sender && m.text === message.text);\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to just the welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 16,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-title\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"compact-list\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-text\"], [1, \"placeholder-text\"], [1, \"compact-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_9_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵtemplate(13, ChatBotComponent_div_13_Template, 6, 8, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 7, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17)(24, \"mat-icon\", 18);\n          i0.ɵɵtext(25, \"summarize\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Summary\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"h4\");\n          i0.ɵɵtext(31, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ChatBotComponent_p_32_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(33, ChatBotComponent_p_33_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 20)(35, \"h4\");\n          i0.ɵɵtext(36, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, ChatBotComponent_ul_37_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(38, ChatBotComponent_p_38_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 20)(40, \"h4\");\n          i0.ɵɵtext(41, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ChatBotComponent_ul_42_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(43, ChatBotComponent_p_43_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 20)(45, \"h4\");\n          i0.ɵɵtext(46, \"Menu Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ChatBotComponent_table_47_Template, 11, 2, \"table\", 24);\n          i0.ɵɵtemplate(48, ChatBotComponent_p_48_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 20)(50, \"h4\");\n          i0.ɵɵtext(51, \"Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, ChatBotComponent_p_52_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(53, ChatBotComponent_p_53_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 450px;\\n  gap: 15px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  line-height: 32px;\\n  color: #333;\\n  min-width: 32px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 8px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n  background-color: #e0e0e0;\\n  border-color: #ccc;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 10px 16px;\\n  border-radius: 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  margin-left: 8px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 2px;\\n  background-color: #999;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n  transform: translateY(0);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.9999s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 12px;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink-bounce {\\n  0%, 100% {\\n    opacity: 0.4;\\n    transform: translateY(0);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateY(-4px);\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.summary-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 15px;\\n  color: #555;\\n  font-weight: 500;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  color: #616161;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 3px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 16px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n  font-size: 13px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 12px;\\n  margin: 0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e0e0e0;\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  width: 40px;\\n  justify-content: center;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 6px;\\n  width: 6px;\\n  margin: 0 2px;\\n  background-color: #555;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n  50% {\\n    transform: translateY(-2px) scale(1.05);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r12", "timestamp", "ɵɵelement", "ɵɵtemplate", "ChatBotComponent_div_13_div_5_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ctx_r2", "restaurantSummary", "location", "cuisine_r16", "ChatBotComponent_ul_37_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r18", "ChatBotComponent_ul_42_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "conversationHistoryLoaded", "ngOnChanges", "changes", "currentValue", "previousValue", "loadConversationHistory", "ngOnInit", "id", "generateId", "Date", "messages$", "subscribe", "message", "startsWith", "detectChanges", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "push", "sort", "a", "b", "getTime", "isDuplicate", "some", "Math", "abs", "console", "log", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "updateRestaurantSummary", "userMessage", "next", "error", "lowerMessage", "toLowerCase", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "for<PERSON>ach", "existingMessage", "clearConversationHistory", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_9_listener", "ChatBotComponent_div_13_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_p_32_Template", "ChatBotComponent_p_33_Template", "ChatBotComponent_ul_37_Template", "ChatBotComponent_p_38_Template", "ChatBotComponent_ul_42_Template", "ChatBotComponent_p_43_Template", "ChatBotComponent_table_47_Template", "ChatBotComponent_p_48_Template", "ChatBotComponent_p_52_Template", "ChatBotComponent_p_53_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if conversation history has been loaded\n  private conversationHistoryLoaded = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            if (!duplicateMessage) {\n              // Add new bot message\n              this.messages.push(message);\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m =>\n              m.sender === message.sender && m.text === message.text\n            );\n\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to just the welcome message\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Chat interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n        <span class=\"assistant-title\">Restaurant details</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n          <mat-icon>clear</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-wrapper\">\n            <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n            <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Improved loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n          <div class=\"typing-text\">AI is thinking...</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <div class=\"summary-title\">\n        <mat-icon class=\"summary-icon\">summarize</mat-icon>\n        <span>Summary</span>\n      </div>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Info</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Pending</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICQ5CC,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnHR,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAAS,SAAA,cAAsE;;IACtET,EAAA,CAAAU,UAAA,IAAAC,sCAAA,kBAAmH;IACrHX,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAP,WAAA,CAAAQ,MAAA,aAAAR,WAAA,CAAAQ,MAAA,YAAgG;IAGpJf,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,UAAA,cAAAZ,EAAA,CAAAgB,WAAA,OAAAT,WAAA,CAAAU,IAAA,GAAAjB,EAAA,CAAAkB,cAAA,CAAqC;IAC/BlB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,SAAAL,WAAA,CAAAQ,MAAA,cAAiC;;;;;IAMvEf,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAS,SAAA,WAAa;IAGbT,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA+BlDH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAc,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtErB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzEH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAiB,WAAA,CAAa;;;;;IAD1EtB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,UAAA,IAAAa,oCAAA,iBAA6E;IAC/EvB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,YAAAY,MAAA,CAAAJ,iBAAA,CAAAK,YAAA,CAAiC;;;;;IAE3DzB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzFH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAqB,aAAA,CAAe;;;;;IAD7E1B,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAU,UAAA,IAAAiB,oCAAA,iBAAgF;IAClF3B,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAR,iBAAA,CAAAS,WAAA,CAAgC;;;;;IAE5D7B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAK1FH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAW,SAAA,CAAiC;IAIjC/B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAY,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrDjC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAA6B,OAAA,CAAAd,iBAAA,CAAAe,cAAA,CAAsC;;;;;IAClFnC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADlFzF,MAqBaiC,gBAAgB;EAsB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAxBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA1B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAY,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAmB1D;IACQ,KAAAC,yBAAyB,GAAG,KAAK;EAdtC;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;MAC3D;MACA,IAAID,OAAO,CAAC,UAAU,CAAC,CAACE,aAAa,KAAKF,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;QAC1E,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC,IAAI,CAACK,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,QAAQ,GAAG,EAAE;IAElB;IACA,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACa,uBAAuB,EAAE;KAC/B,MAAM;MACL;MACA,IAAI,CAACX,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,2KAA2K;QACjLF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;;IAGH;IACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAACT,UAAU,CAACqB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAI8C,OAAO,CAACL,EAAE,CAACM,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAChB,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAIF,OAAO,CAAC9C,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMiD,oBAAoB,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKK,OAAO,CAACL,EAAE,CAAC;QAE9E,IAAIQ,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAACrB,QAAQ,CAACqB,oBAAoB,CAAC,GAAGH,OAAO;SAC9C,MAAM;UACL;UACA,MAAMM,gBAAgB,GAAG,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACnD,MAAM,KAAK,KAAK,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IAAI,CAACiD,CAAC,CAACjD,IAAI,CAACoD,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,IAAI,CAACF,gBAAgB,EAAE;YACrB;YACA,IAAI,CAACxB,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;YAE3B;YACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;;;OAGhF,MAAM,IAAIb,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAM4D,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IACvB4D,IAAI,CAACC,GAAG,CAACZ,CAAC,CAAC1D,SAAS,CAACkE,OAAO,EAAE,GAAGb,OAAO,CAACrD,SAAS,CAACkE,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACC,WAAW,EAAE;UAChBI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;UACjD;UACA,IAAI,CAAC0B,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;UAE3B;UACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;SAC5E,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACsB,EAAE,CAACwB,aAAa,EAAE;MAEvB;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACnC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACoC,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACnC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACmC,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC7C,UAAU,CAAC8C,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzC,cAAc,CAAC0C,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC7C,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC+C,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC3C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM2C,aAAa,GAAG,IAAI,CAAC7C,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA;IAEA;IACA,IAAI,CAAC2B,uBAAuB,CAACD,aAAa,CAAC;IAE3C;IACA,MAAME,WAAW,GAAgB;MAC/BnC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBxC,IAAI,EAAEwE,aAAa;MACnB1E,MAAM,EAAE,MAAM;MACdP,SAAS,EAAE,IAAIkD,IAAI;KACpB;IAED;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAKwE,aAAa,CACzB;IAED,IAAI,CAACd,WAAW,EAAE;MAChB,IAAI,CAAChC,QAAQ,CAAC2B,IAAI,CAACqB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC7C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;IACvB,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAAC3C,UAAU,CAAC+C,WAAW,CAAC,IAAI,CAAC5C,QAAQ,EAAEgD,aAAa,CAAC,CAAC7B,SAAS,CAAC;MAClEgC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAAC/C,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACzB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAAC+C,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjD,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQ2B,uBAAuBA,CAAC7B,OAAe;IAC7C,MAAMiC,YAAY,GAAGjC,OAAO,CAACkC,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACzB,QAAQ,CAAC,UAAU,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IACrEyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IACjEyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAACjD,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAAC2E,kBAAkB,CAACF,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAM4B,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACJ,YAAY,CAAC;MACpD,IAAIG,QAAQ,CAAChE,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACb,iBAAiB,CAACK,YAAY,GAAG,CAAC,GAAG,IAAI0E,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACK,YAAY,EAAE,GAAGwE,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIH,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,YAAY,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAMxC,WAAW,GAAG,IAAI,CAACqE,gBAAgB,CAACJ,YAAY,CAAC;MACvD,IAAIjE,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACb,iBAAiB,CAACS,WAAW,GAAG,CAAC,GAAG,IAAIsE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACS,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIiE,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAM+B,cAAc,GAAGN,YAAY,CAACO,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAAChF,iBAAiB,CAACW,SAAS,GAAGuE,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIN,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMkC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACJ,YAAY,CAAC;QACtD,IAAIS,UAAU,CAACtE,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACb,iBAAiB,CAACY,cAAc,GAAG,CAAC,GAAG,IAAImE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACY,cAAc,EAAE,GAAGuE,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIT,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAACjD,iBAAiB,CAACe,cAAc,GAAG,IAAI,CAAC6D,kBAAkB,CAACF,YAAY,CAAC;;IAG/E,IAAI,CAACvD,EAAE,CAACwB,aAAa,EAAE;EACzB;EAEA;;;;EAIQiC,kBAAkBA,CAACnC,OAAe;IACxC;IACA,MAAM2C,cAAc,GAAG3C,OAAO,CAC3B4C,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAACrC,OAAe;IACtC;IACA,IAAIA,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,IAAIR,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOR,OAAO,CACXgD,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzB,IAAI,EAAE,CAAC,CACxB0B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC9E,MAAM,GAAG,CAAC,CAAC,CAC/B6E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACnC,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAoD,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAChC,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAqC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQlE,UAAUA,CAAA;IAChB,OAAOoB,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGjD,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,MAAc,EAAEnE,OAAoB;IAC5C,OAAOA,OAAO,CAACL,EAAE;EACnB;EAEA;;;EAGAF,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACQ,yBAAyB,EAAE;MACpD;;IAGF;IACA,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAACJ,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACP,UAAU,CAACgB,uBAAuB,CAAC,IAAI,CAACb,QAAQ,EAAE,KAAK,CAAC,CAACmB,SAAS,CAAC;MACtEgC,IAAI,EAAGjD,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,IAAI,CAACU,QAAQ,GAAG,EAAE;UAElB;UACAA,QAAQ,CAACsF,OAAO,CAACpE,OAAO,IAAG;YACzB;YACA,MAAMqE,eAAe,GAAG,IAAI,CAACvF,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC1CA,CAAC,CAACnD,MAAM,KAAK8C,OAAO,CAAC9C,MAAM,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,CACvD;YAED,IAAI,CAACiH,eAAe,EAAE;cACpB,IAAI,CAACvF,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;;YAG7B;YACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;cAC7B,IAAI,CAAC2E,uBAAuB,CAAC7B,OAAO,CAAC5C,IAAI,CAAC;;UAE9C,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAAC0B,QAAQ,CAACV,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACU,QAAQ,CAAC2B,IAAI,CAAC;cACjBd,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;cACrBxC,IAAI,EAAE,2KAA2K;cACjLF,MAAM,EAAE,KAAK;cACbP,SAAS,EAAE,IAAIkD,IAAI;aACpB,CAAC;;SAEL,MAAM;UACL;UACA,IAAI,CAACf,QAAQ,GAAG,CAAC;YACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,2KAA2K;YACjLF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CAAC;;QAGJ,IAAI,CAACb,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;QACvB,IAAI,CAACkB,cAAc,EAAE;MACvB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACH,QAAQ,GAAG,CAAC;UACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBxC,IAAI,EAAE,2KAA2K;UACjLF,MAAM,EAAE,KAAK;UACbP,SAAS,EAAE,IAAIkD,IAAI;SACpB,CAAC;QAEF,IAAI,CAACnB,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAoE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACtF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA,IAAI,CAAC3C,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED;IACA,IAAI,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,UAAU,CAAC6F,wBAAwB,CAAC,IAAI,CAAC1F,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAACmB,SAAS,CAAC;QAC5EgC,IAAI,EAAEA,CAAA,KAAK;UACTb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAACrC,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED;UACA,IAAI,CAACT,yBAAyB,GAAG,KAAK;UAEtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAAClD,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;UACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACtC,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;MAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACvB,IAAI,CAACkB,cAAc,EAAE;;EAEzB;;;uBA1fW7C,gBAAgB,EAAApC,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtI,EAAA,CAAAoI,iBAAA,CAAApI,EAAA,CAAAuI,iBAAA,GAAAvI,EAAA,CAAAoI,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBrG,gBAAgB;MAAAsG,SAAA;MAAAC,MAAA;QAAAlG,QAAA;QAAAC,UAAA;MAAA;MAAAkG,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,oBAAA,EAAA9I,EAAA,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC7BrJ,EAAA,CAAAC,cAAA,aAAyB;UAKWD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAuJ,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAClFnI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKhCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAU,UAAA,KAAA+I,gCAAA,iBAOM;UAGNzJ,EAAA,CAAAU,UAAA,KAAAgJ,gCAAA,kBAOM;UACR1J,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAuJ,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA1G,cAAA,GAAAgH,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAArC,UAAA,CAAA2C,MAAA,CAAkB;UAAA,EAFD;UADnC5J,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAuJ,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAC1DrF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAGKD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIxBH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAU,UAAA,KAAAqJ,8BAAA,gBAA0E;UAC1E/J,EAAA,CAAAU,UAAA,KAAAsJ,8BAAA,gBAA2E;UAC7EhK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAU,UAAA,KAAAuJ,+BAAA,iBAEK;UACLjK,EAAA,CAAAU,UAAA,KAAAwJ,8BAAA,gBAA2F;UAC7FlK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAU,UAAA,KAAAyJ,+BAAA,iBAEK;UACLnK,EAAA,CAAAU,UAAA,KAAA0J,8BAAA,gBAA0F;UAC5FpK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAU,UAAA,KAAA2J,kCAAA,qBASQ;UACRrK,EAAA,CAAAU,UAAA,KAAA4J,8BAAA,gBAAiF;UACnFtK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAU,UAAA,KAAA6J,8BAAA,gBAAsF;UACtFvK,EAAA,CAAAU,UAAA,KAAA8J,8BAAA,gBAAiF;UACnFxK,EAAA,CAAAG,YAAA,EAAM;;;UArFmBH,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA3G,QAAA,CAAa,iBAAA2G,GAAA,CAAAvB,SAAA;UAUhC/H,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAxG,oBAAA,CAA0B;UAavB9C,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA1G,cAAA,CAA4B,aAAA0G,GAAA,CAAAzG,YAAA;UAKwB7C,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAY,UAAA,cAAA0I,GAAA,CAAA1G,cAAA,CAAA0C,IAAA,MAAAgE,GAAA,CAAAzG,YAAA,CAAmD;UAkB1G7C,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAgC;UAChCrB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAiC;UAKhCrB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,KAA+C;UAGhDjC,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,OAAiD;UAKhDjC,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,KAA8C;UAG/CjC,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,OAAgD;UAKtBjC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,KAAqC;UAU/D/B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,OAAuC;UAKvC/B,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAsC;UACtCnC,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAuC;;;qBD9E/C9C,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZvL,WAAW,EAAAwL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1L,mBAAmB,EACnBC,eAAe,EAAA0L,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACf3L,aAAa,EACbC,kBAAkB,EAAA2L,EAAA,CAAAC,YAAA,EAClB3L,aAAa,EAAA4L,EAAA,CAAAC,OAAA,EACb5L,cAAc,EAAA6L,EAAA,CAAAC,QAAA,EACd7L,wBAAwB,EACxBC,gBAAgB,EAAA6L,EAAA,CAAAC,UAAA,EAEhB7L,YAAY;MAAA8L,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH1J,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}