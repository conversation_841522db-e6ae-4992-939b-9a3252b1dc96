{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"../../services/smart-dashboard.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/material/tooltip\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"ngx-mat-select-search\";\nconst _c0 = [\"chartsContainer\"];\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 55);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r10.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r11));\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.displayName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"div\", 59)(3, \"mat-icon\", 60);\n    i0.ɵɵtext(4, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 61);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 62);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.getLoadingMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getLoadingMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"div\", 65)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 66);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 67);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r7.getEmptyStateMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getEmptyStateMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_92_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 73)(5, \"div\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.label);\n  }\n}\nfunction SmartDashboardComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_92_div_2_Template, 9, 3, \"div\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getSummaryItems());\n  }\n}\nfunction SmartDashboardComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 76, 77);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService, smartDashboardService, cdr) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.smartDashboardService = smartDashboardService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.tabs = [];\n    this.selectedTab = 0;\n    this.locations = [];\n    this.baseDates = [];\n    this.selectedLocations = [];\n    this.selectedBaseDate = '';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    this.dashboardData = null;\n    this.charts = [];\n    this.isLoading = false;\n    this.dashboardConfig = null;\n    this.useDefaultCharts = false;\n    this.locationFilterCtrl = new FormControl();\n    this.allLocationsSelected = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n  initializeComponent() {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n    this.smartDashboardService.dashboardData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.dashboardData = data;\n      if (data) {\n        this.renderCharts();\n      }\n      this.cdr.detectChanges();\n    });\n    this.smartDashboardService.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.detectChanges();\n    });\n  }\n  loadDashboardConfig() {\n    this.smartDashboardService.getDashboardConfig().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.dashboardConfig = response.data;\n        }\n      },\n      error: () => {}\n    });\n  }\n  setDefaultTabs() {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n  setDefaultBaseDates() {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n  setDefaultDates() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n  onDefaultChartsToggle() {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n    }\n  }\n  setMode(useDefault) {\n    if (this.useDefaultCharts !== useDefault) {\n      this.useDefaultCharts = useDefault;\n      this.onDefaultChartsToggle();\n    }\n  }\n  sendMessage() {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: true\n            }));\n            this.selectedLocations = this.locations.map(location => location.value);\n            this.setupLocationFilter();\n          } else {\n            this.locations = [];\n            this.selectedLocations = [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n  getDashboardDescription(index) {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n  areAllFiltersValid() {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n  setupLocationFilter() {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(startWith(''), map(value => this.filterLocations(value || '')));\n  }\n  filterLocations(value) {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location => location.label.toLowerCase().includes(filterValue));\n  }\n  toggleAllLocations() {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  onLocationSelectionChange(selectedValues) {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  updateLocationStates() {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n  isAllSelected() {\n    return this.selectedLocations.length === this.locations.length;\n  }\n  getLoadingMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n  getEmptyStateMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n  generateDashboard() {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n    // Helper function to convert Date to string format\n    const formatDateValue = dateValue => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n    this.clearCharts();\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n    this.smartDashboardService.generateDashboard(request).pipe(first()).subscribe({\n      next: () => {},\n      error: () => {}\n    });\n  }\n  clearCharts() {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n  renderCharts() {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n    this.clearCharts();\n    setTimeout(() => {\n      this.dashboardData.charts.forEach(chartConfig => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n  getSummaryItems() {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n    return this.dashboardData.summary_items;\n  }\n  createChart(chartConfig) {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n      const chart = new Chart(ctx, {\n        type: chartConfig.type,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n      this.charts.push(chart);\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.SmartDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartsContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 94,\n      vars: 33,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search restaurants...\", \"noEntriesFoundLabel\", \"No restaurants found\", 3, \"formControl\"], [1, \"select-all-controls\"], [\"mat-button\", \"\", 1, \"select-toggle-btn\", 3, \"click\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", 3, \"placeholder\", \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"matTooltip\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"dashboard-summary\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"selection-count\"], [1, \"loading-state\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"dashboard-summary\"], [1, \"summary-cards\"], [\"class\", \"summary-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-value\"], [1, \"summary-label\"], [1, \"charts-container\"], [\"chartsContainer\", \"\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_27_listener($event) {\n            return ctx.onLocationSelectionChange($event.value);\n          });\n          i0.ɵɵelementStart(28, \"mat-option\");\n          i0.ɵɵelement(29, \"ngx-mat-select-search\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_31_listener() {\n            return ctx.toggleAllLocations();\n          });\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_option_33_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵpipe(34, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"label\", 16)(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 18);\n          i0.ɵɵtext(40, \"Base Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 20)(42, \"mat-select\", 25);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_42_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(43, SmartDashboardComponent_mat_option_43_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"label\", 16)(46, \"mat-icon\", 17);\n          i0.ɵɵtext(47, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\", 18);\n          i0.ɵɵtext(49, \"Start Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"mat-form-field\", 20)(51, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_51_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"mat-datepicker-toggle\", 27)(53, \"mat-datepicker\", null, 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 15)(56, \"label\", 16)(57, \"mat-icon\", 17);\n          i0.ɵɵtext(58, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 18);\n          i0.ɵɵtext(60, \"End Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 20)(62, \"input\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_62_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"mat-datepicker-toggle\", 27)(64, \"mat-datepicker\", null, 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 31)(67, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_67_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Reset \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"div\", 33)(72, \"div\", 34)(73, \"div\", 35)(74, \"div\", 36)(75, \"mat-icon\", 37);\n          i0.ɵɵtext(76, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"span\", 38);\n          i0.ɵɵtext(78, \"Digi AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\", 39);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"div\", 41)(83, \"mat-form-field\", 42)(84, \"input\", 43);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_84_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_84_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_85_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(86, \"mat-icon\");\n          i0.ɵɵtext(87);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(88, \"div\", 45)(89, \"div\", 46);\n          i0.ɵɵtemplate(90, SmartDashboardComponent_div_90_Template, 9, 2, \"div\", 47);\n          i0.ɵɵtemplate(91, SmartDashboardComponent_div_91_Template, 9, 2, \"div\", 48);\n          i0.ɵɵtemplate(92, SmartDashboardComponent_div_92_Template, 3, 1, \"div\", 49);\n          i0.ɵɵtemplate(93, SmartDashboardComponent_div_93_Template, 2, 0, \"div\", 50);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(54);\n          const _r5 = i0.ɵɵreference(65);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isAllSelected() ? \"Deselect All\" : \"Select All\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(34, 31, ctx.filteredLocations));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(16);\n          i0.ɵɵclassProp(\"ready\", ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.areAllFiltersValid() ? \"Ready to analyze\" : \"Please fill all required filters\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled-mode\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.useDefaultCharts ? \"Default charts mode - input disabled\" : \"Ask me about your business data...\")(\"ngModel\", ctx.chatMessage)(\"disabled\", !ctx.areAllFiltersValid() || ctx.useDefaultCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.useDefaultCharts ? !ctx.areAllFiltersValid() : !ctx.chatMessage.trim() || !ctx.areAllFiltersValid())(\"matTooltip\", ctx.useDefaultCharts ? \"Generate Default Dashboard\" : \"Send Custom Query\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.useDefaultCharts ? \"auto_awesome\" : \"send\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.dashboardData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.dashboardData == null ? null : ctx.dashboardData.summary_items));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatSuffix, MatSelectModule, i8.MatSelect, i8.MatSelectTrigger, i9.MatOption, MatInputModule, i10.MatInput, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, MatSlideToggleModule, MatTooltipModule, i12.MatTooltip, FormsModule, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel, ReactiveFormsModule, i13.FormControlDirective, NgxMatSelectSearchModule, i14.MatSelectSearchComponent],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 270px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem !important;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n  padding: 0.25rem;\\n  background: #f9fafb;\\n  border-radius: 0.5rem;\\n  border: 1px solid #e5e7eb;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem;\\n  border-radius: 0.375rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease-out;\\n  border: 1px solid transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 53, 0.1);\\n  border-color: rgba(255, 107, 53, 0.3);\\n  color: #ff6b35;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-option-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  transform: scale(1.1);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-option-title[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 600;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-option-desc[_ngcontent-%COMP%] {\\n  color: #ff4602;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]:not(.active) {\\n  opacity: 0.7;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]:not(.active):hover {\\n  opacity: 1;\\n  background: rgba(243, 244, 246, 0.5);\\n  border-color: #d1d5db;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-option-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 20px;\\n  height: 20px;\\n  color: #6b7280;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-option-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-option-content[_ngcontent-%COMP%]   .mode-option-title[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  color: #374151;\\n  line-height: 1.2;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-option-content[_ngcontent-%COMP%]   .mode-option-desc[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #6b7280;\\n  line-height: 1.1;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 0.125rem 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .dashboard-mode-toggle[_ngcontent-%COMP%]     .mat-mdc-slide-toggle {\\n  --mdc-switch-selected-track-color: #ff6b35;\\n  --mdc-switch-selected-handle-color: #ffffff;\\n  --mdc-switch-selected-hover-track-color: #e55a2b;\\n  --mdc-switch-selected-focus-track-color: #e55a2b;\\n  --mdc-switch-selected-pressed-track-color: #e55a2b;\\n  --mdc-switch-unselected-track-color: #d1d5db;\\n  --mdc-switch-unselected-handle-color: #ffffff;\\n  --mdc-switch-unselected-hover-track-color: #9ca3af;\\n  --mdc-switch-track-height: 18px;\\n  --mdc-switch-track-width: 36px;\\n  --mdc-switch-handle-height: 14px;\\n  --mdc-switch-handle-width: 14px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .dashboard-mode-toggle[_ngcontent-%COMP%]     .mdc-switch {\\n  width: 36px;\\n  height: 18px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .dashboard-mode-toggle[_ngcontent-%COMP%]     .mdc-switch__track {\\n  border-radius: 9px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-toggle-wrapper[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .dashboard-mode-toggle[_ngcontent-%COMP%]     .mdc-switch__handle {\\n  border-radius: 50%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.5rem;\\n  max-height: calc(100vh - 280px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #374151;\\n  border-color: #d1d5db;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n  background: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #ff6b35;\\n  color: #ff6b35;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0;\\n  gap: 0;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4b5563;\\n  padding: 3px 8px;\\n  border-radius: 0.375rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex: 1;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #f9fafb !important;\\n  border-color: #d1d5db !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #d1d5db !important;\\n  box-shadow: none !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 53, 0.6) !important;\\n  border-color: rgba(255, 107, 53, 0.6) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 53, 0.7) !important;\\n  border-color: rgba(255, 107, 53, 0.7) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n  border: 2px solid #ff6b35;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  border-color: #e55a2b;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15);\\n  transform: translateY(-1px);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled])::before {\\n  left: 100%;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  background: #d1d5db;\\n  border-color: #d1d5db;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (max-width: 768px) {\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n    width: 100%;\\n  }\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #ff6b35;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%] {\\n  padding: 1rem 1rem 0.75rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #4b5563;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  align-content: start;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header {\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header h3 {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container canvas {\\n  max-width: 100%;\\n  height: 300px !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    align-self: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 6px 8px !important;\\n  min-height: 18px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 2px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 36px !important;\\n  line-height: 1.2 !important;\\n  padding: 6px 8px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:first-child {\\n  margin-top: 0 !important;\\n  padding-top: 4px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-select-panel {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .select-all-controls {\\n  padding: 4px 8px 6px 8px !important;\\n  border-bottom: 1px solid #e0e0e0 !important;\\n  background: #f8f9fa !important;\\n  margin: 0 !important;\\n  border-radius: 0 !important;\\n}\\n  .select-all-controls .select-toggle-btn {\\n  width: 100% !important;\\n  height: 26px !important;\\n  font-size: 0.75rem !important;\\n  padding: 0 !important;\\n  border-radius: 3px !important;\\n  font-weight: 600 !important;\\n  background: rgba(255, 107, 53, 0.1) !important;\\n  color: #ff6b35 !important;\\n  border: 1px solid rgba(255, 107, 53, 0.2) !important;\\n  transition: all 0.2s ease !important;\\n  min-height: 26px !important;\\n  line-height: 26px !important;\\n}\\n  .select-all-controls .select-toggle-btn:hover {\\n  background: rgba(255, 107, 53, 0.15) !important;\\n  border-color: rgba(255, 107, 53, 0.3) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYWktZGFzaGJvYXJkL3NtYXJ0LWRhc2hib2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrREE7RUFDRSxhQUFBO0VBQ0EsMEJBQUE7RUFDQSw4QkFBQTtFQUNBLDZEQUFBO0VBQ0EsK0VBQUE7RUFDQSxjQXhDUztFQXlDVCxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7QUFqREY7QUFtREU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsNEZBQUE7RUFDQSxvQkFBQTtFQUNBLFVBQUE7QUFqREo7O0FBc0RBO0VBQ0UsWUFBQTtFQUNBLG1CQXRFTTtFQXVFTiwrQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLDJDQWxEVTtFQW1EVixnQkFBQTtBQW5ERjtBQXFERTtFQUNFLGVBbEVTO0FBZWI7QUFxREk7RUFDRSxnQ0FBQTtFQUNBLGdCQUFBO0FBbkROO0FBc0RJO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQTdFTztBQXlCYjtBQXVESTtFQUNFLG1CQWhGTztFQWlGUCxzQkFqRk87QUE0QmI7QUF1RE07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQXhGSztFQXlGTCxrQkFBQTtBQXJEUjtBQXVEUTtFQUNFLGNBL0dBO0VBZ0hBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtBQXJEVjtBQXdEUTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQTlHQztFQStHRCxxQkFBQTtBQXREVjtBQXlEUTtFQUNFLG1CQWxJQTtFQW1JQSxjQTdIRjtFQThIRSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsMkNBN0dFO0FBc0RaO0FBOERFO0VBQ0UsdUZBQUE7RUFDQSwwQ0FBQTtFQUNBLHNCQTFIUTtFQTJIUiwwQkFBQTtFQUNBLGNBcklTO0VBc0lULGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSw4Q0FBQTtBQTVESjtBQThESTtFQUNFLHVGQUFBO0VBQ0EscUNBQUE7RUFDQSwrQ0FBQTtBQTVETjtBQWlFRTtFQUNFLGdDQUFBO0VBQ0EsZ0JBQUE7QUEvREo7QUFrRU07RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxZQTNKSztFQTRKTCxnQkE1Sks7RUE2SkwsbUJBektFO0VBMEtGLHFCQXJKSTtFQXNKSix5QkFBQTtBQWhFUjtBQWtFUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBcEtHO0VBcUtILGdCQXJLRztFQXNLSCx1QkE5SkU7RUErSkYsZUFBQTtFQUNBLDZCQUFBO0VBQ0EsNkJBQUE7QUFoRVY7QUFrRVU7RUFDRSxtQ0FBQTtFQUNBLHFDQUFBO0VBQ0EsY0FqTUY7QUFpSVY7QUFrRVk7RUFDRSxjQXBNSjtFQXFNSSxxQkFBQTtBQWhFZDtBQW1FWTtFQUNFLGNBek1KO0VBME1JLGdCQUFBO0FBakVkO0FBb0VZO0VBQ0UsY0FBQTtBQWxFZDtBQXNFVTtFQUNFLFlBQUE7QUFwRVo7QUFzRVk7RUFDRSxVQUFBO0VBQ0Esb0NBQUE7RUFDQSxxQkE5TUg7QUEwSVg7QUF3RVU7RUFDRSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsY0FwTkQ7RUFxTkMsNkJBQUE7RUFDQSxjQUFBO0FBdEVaO0FBeUVVO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFFBQUE7QUF2RVo7QUF5RVk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FoT0g7RUFpT0csZ0JBQUE7RUFDQSw2QkFBQTtBQXZFZDtBQTBFWTtFQUNFLGtCQUFBO0VBQ0EsY0F6T0g7RUEwT0csZ0JBQUE7RUFDQSw2QkFBQTtBQXhFZDtBQTZFUTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBM0VWO0FBK0VjO0VBQ0UsMENBQUE7RUFDQSwyQ0FBQTtFQUNBLGdEQUFBO0VBQ0EsZ0RBQUE7RUFDQSxrREFBQTtFQUNBLDRDQUFBO0VBQ0EsNkNBQUE7RUFDQSxrREFBQTtFQUNBLCtCQUFBO0VBQ0EsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLCtCQUFBO0FBN0VoQjtBQWdGYztFQUNFLFdBQUE7RUFDQSxZQUFBO0FBOUVoQjtBQWlGYztFQUNFLGtCQUFBO0FBL0VoQjtBQWtGYztFQUNFLGtCQUFBO0FBaEZoQjtBQXdGSTtFQUNFLFdBQUE7QUF0Rk47QUF5Rk07RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EseURBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx3Q0FBQTtBQXZGUjtBQXlGUTtFQUNFLGdDQUFBO0VBQ0EsMERBQUE7QUF2RlY7QUEyRk07RUFDRSxnQ0FBQTtFQUNBLDZGQUFBO0FBekZSO0FBNEZNO0VBQ0UsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7QUExRlI7QUFpR0k7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0F2VE87RUF3VFAsZ0JBQUE7RUFDQSxzQkExVE87RUEyVFAscUJBMVRPO0VBMlRQLCtCQUFBO0FBL0ZOO0FBa0dNO0VBQ0UsVUFBQTtBQWhHUjtBQW1HTTtFQUNFLHVCQUFBO0FBakdSO0FBb0dNO0VBQ0UsbUNBQUE7RUFDQSxrQkFBQTtBQWxHUjtBQW9HUTtFQUNFLG1DQUFBO0FBbEdWO0FBdUdRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFwVkc7RUFxVkgsdUJBdFZHO0VBdVZILGdCQUFBO0VBQ0EsY0E1VkM7RUE2VkQsaUJBQUE7RUFDQSxxQkFBQTtBQXJHVjtBQXVHVTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQWxYRjtFQW1YRSxjQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUFyR1o7QUF3R1U7RUFDRSxPQUFBO0VBQ0EsY0FBQTtBQXRHWjtBQXlHVTtFQUNFLGtCQUFBO0VBQ0EsY0FsWUY7RUFtWUUsZ0JBQUE7RUFDQSxtQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBM1dBO0VBNFdBLGNBQUE7QUF2R1o7QUEyR1E7RUFDRSxXQUFBO0FBekdWO0FBOEdJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBaFlPO0VBaVlQLDZCQUFBO0VBQ0EsZ0JBQUE7QUE1R047QUE4R007RUFDRSxZQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQS9YSTtFQWdZSixjQTlZRztFQStZSCxxQkFuWkc7RUFvWkgsa0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBM1pBO0VBNFpBLDJDQWxZSTtBQXNSWjtBQThHUTtFQUNFLG1CQTlaQTtFQStaQSxxQkF0YUE7RUF1YUEsY0F2YUE7RUF3YUEsaUZBdllFO0FBMlJaO0FBK0dRO0VBQ0UsZUFBQTtFQUNBLHFCQTFaRztBQTZTYjs7QUFxSEE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsVUFBQTtFQUNBLE1BQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxvQkFBQTtFQUNBLGdCQUFBO0FBbEhGO0FBcUhFO0VBQ0UsY0FBQTtFQUNBLFNBQUE7RUFDQSx1RkFBQTtFQUNBLDBDQUFBO0VBQ0Esc0JBemFRO0VBMGFSLGNBbmJTO0VBb2JULGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSw4Q0FBQTtBQW5ISjtBQXFISTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBM2JPO0VBNGJQLGVBNWJPO0VBNmJQLGdCQUFBO0FBbkhOO0FBcUhNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFuY0s7RUFvY0wsY0FBQTtFQUNBLGdCQUFBO0FBbkhSO0FBcUhRO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtFQUNBLGNBOWRBO0VBK2RBLHNCQTdjRztBQTBWYjtBQXNIUTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXRkQztFQXVkRCxtQkFBQTtFQUNBLHNCQXJkRztBQWlXYjtBQXVIUTtFQUNFLGtCQUFBO0VBQ0EsY0EvZEM7RUFnZUQsZ0JBQUE7RUFDQSx1QkFuZEU7RUFvZEYsbUNBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0FBckhWO0FBdUhVO0VBQ0UsbUNBQUE7RUFDQSxjQWxmRjtBQTZYVjtBQTBITTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQXhIUjtBQTBIUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBL2VHO0VBZ2ZILE9BQUE7RUFDQSw2QkFBQTtBQXhIVjtBQTBIVTtFQUNFLFlBQUE7QUF4SFo7QUEwSFk7RUFDRSw4QkFBQTtFQUNBLGdDQUFBO0FBeEhkO0FBMEhjO0VBQ0UsZ0NBQUE7RUFDQSwyQkFBQTtBQXhIaEI7QUE0SFk7RUFDRSw4Q0FBQTtFQUNBLGdEQUFBO0FBMUhkO0FBNEhjO0VBQ0UsOENBQUE7RUFDQSxnREFBQTtBQTFIaEI7QUErSFU7RUFDRSxPQUFBO0VBQ0EsWUFBQTtBQTdIWjtBQWdJWTtFQUNFLDhCQUFBO0VBQ0Esb0NBQUE7RUFDQSx5REFBQTtFQUNBLHVCQUFBO0VBQ0EsMkJBQUE7RUFDQSxnQ0FBQTtFQUNBLHdDQUFBO0FBOUhkO0FBZ0ljO0VBQ0UsZ0NBQUE7RUFDQSwwREFBQTtBQTlIaEI7QUFrSVk7RUFDRSxnQ0FBQTtFQUNBLDZGQUFBO0FBaElkO0FBbUlZO0VBQ0UsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7QUFqSWQ7QUFvSVk7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUFsSWQ7QUFzSVU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQXprQkY7RUEwa0JFLGNBcGtCSjtFQXFrQkksNkJBQUE7RUFDQSxjQUFBO0VBQ0EscUJBampCQTtFQWtqQkEseUJBQUE7RUFDQSw4Q0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFwSVo7QUFzSVk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esc0ZBQUE7RUFDQSxxQkFBQTtBQXBJZDtBQXVJWTtFQUNFLG1CQTdsQkM7RUE4bEJELHFCQTlsQkM7RUErbEJELCtDQUFBO0VBQ0EsMkJBQUE7QUFySWQ7QUF1SWM7RUFDRSxVQUFBO0FBckloQjtBQXlJWTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQWxtQkg7RUFtbUJHLHFCQW5tQkg7RUFvbUJHLGVBQUE7RUFDQSxnQkFBQTtBQXZJZDtBQTBJWTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBeElkO0FBZ0pJO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLFdBNW1CSztJQTZtQkwsb0JBQUE7RUE5SU47RUFnSk07SUFDRSxlQUFBO0lBQ0EsdUJBQUE7RUE5SVI7RUFpSk07SUFDRSx1QkFBQTtFQS9JUjtFQWlKUTtJQUNFLGVBQUE7SUFDQSxXQUFBO0VBL0lWO0FBQ0Y7QUFzSkU7RUFDRSxPQUFBO0VBNW1CRixtQkFwQ007RUFxQ04sc0JBZFU7RUFlVixpRkFYVTtFQVlWLDBDQUFBO0VBMm1CRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBdm9CUztBQXNmYjtBQW1KSTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQWpKTjtBQW9KTTtFQUNFLFVBQUE7QUFsSlI7QUFxSk07RUFDRSxtQkFscUJFO0FBK2dCVjtBQXNKTTtFQUNFLG1DQUFBO0VBQ0Esa0JBQUE7QUFwSlI7QUFzSlE7RUFDRSxtQ0FBQTtBQXBKVjtBQXlKTTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQXBxQk07QUE2Z0JkO0FBeUpRO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQXZKVjtBQXlKVTtFQUNFLHNCQTdxQkM7QUFzaEJiO0FBeUpZO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsY0E5ckJIO0FBdWlCWDtBQTJKVTtFQUNFLHFCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBanNCRDtBQXdpQlg7QUE0SlU7RUFDRSxTQUFBO0VBQ0EsY0F4c0JEO0VBeXNCQyxlQUFBO0VBQ0EsZ0JBQUE7QUExSlo7QUFnS007RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkEzc0JNO0FBNmlCZDtBQWdLUTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUE5SlY7QUFnS1U7RUFDRSxzQkFwdEJDO0FBc2pCYjtBQWdLWTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBL3VCSjtFQWd2Qkksb0NBQUE7QUE5SmQ7QUFrS1U7RUFDRSxxQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXp1QkQ7QUF5a0JYO0FBbUtVO0VBQ0UsU0FBQTtFQUNBLGNBaHZCRDtFQWl2QkMsZUFBQTtFQUNBLGdCQUFBO0FBaktaO0FBdUtNO0VBQ0UsK0JBQUE7RUFDQSxnQ0FBQTtBQXJLUjtBQXVLUTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFlBdnZCRztBQWtsQmI7QUF1S1U7RUFDRSxtQkF6d0JKO0VBMHdCSSx5QkFBQTtFQUNBLHNCQXB2QkE7RUFxdkJBLGdCQTd2QkM7RUE4dkJELGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBandCQztFQWt3QkQsNkJBQUE7RUFDQSwyQ0F2dkJBO0FBa2xCWjtBQXVLWTtFQUNFLGlGQXp2QkY7RUEwdkJFLHFDQUFBO0FBcktkO0FBd0tZO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxxQkFyd0JGO0VBc3dCRSxzRkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtBQXRLZDtBQXdLYztFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLGNBQUE7RUFDQSxjQTV5Qk47QUFzb0JWO0FBMEtZO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUF4S2Q7QUEwS2M7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0F4eUJMO0VBeXlCSyxrQkFBQTtFQUNBLGdCQUFBO0FBeEtoQjtBQTJLYztFQUNFLGtCQUFBO0VBQ0EsY0FqekJMO0VBa3pCSyxnQkFBQTtFQUNBLGdCQUFBO0FBektoQjtBQWlMTTtFQUNFLE9BQUE7RUFDQSxhQXB6Qks7RUFxekJMLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBdnpCSztFQXd6Qkwsb0JBQUE7QUEvS1I7QUFpTFE7RUFDRSxtQkEzMEJGO0VBNDBCRSx5QkFBQTtFQUNBLHNCQXR6QkU7RUF1ekJGLGdCQS96Qkc7RUFnMEJILDJDQXJ6QkU7RUFzekJGLDZCQUFBO0FBL0tWO0FBaUxVO0VBQ0UsaUZBeHpCQTtFQXl6QkEscUNBQUE7QUEvS1o7QUFrTFU7RUFDRSxzQkF6MEJDO0VBMDBCRCxzQkEzMEJDO0VBNDBCRCxnQ0FBQTtBQWhMWjtBQWtMWTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0F2MUJIO0FBdXFCWDtBQW9MVTtFQUNFLGVBQUE7RUFDQSx3QkFBQTtBQWxMWjs7QUEyTEE7RUFDRTtJQUFLLHVCQUFBO0VBdkxMO0VBd0xBO0lBQU8seUJBQUE7RUFyTFA7QUFDRjtBQXdMQTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSwwQkFBQTtJQUNBLDhCQUFBO0VBdExGO0VBeUxBO0lBQ0UsV0FBQTtJQUNBLGtCQUFBO0lBQ0EsZ0NBQUE7SUFDQSxjQUFBO0lBQ0EsZ0JBQUE7SUFDQSxnQkFBQTtFQXZMRjtFQXlMRTtJQUNFLHVCQUFBO0VBdkxKO0VBMExFO0lBQ0UsZUE1M0JPO0VBb3NCWDtFQTRMSTtJQUNFLGlCQUFBO0lBQ0EsWUFsNEJLO0VBd3NCWDtFQStMQTtJQUNFLGdCQXg0QlM7SUF5NEJULE9BQUE7SUFDQSxhQUFBO0VBN0xGO0FBQ0Y7QUFnTUE7RUFFSTtJQUNFLGdCQWo1Qk87RUFrdEJYO0VBbU1JO0lBQ0UsV0FyNUJLO0VBb3RCWDtFQW9NSTtJQUNFLHNCQUFBO0lBQ0EsWUEzNUJLO0VBeXRCWDtFQW9NTTs7SUFFRSxVQUFBO0lBQ0EsWUFBQTtFQWxNUjtFQXdNQTtJQUNFLGdCQXY2QlM7SUF3NkJULFlBeDZCUztFQWt1Qlg7RUF5TUk7SUFDRSxzQkFBQTtJQUNBLFdBNTZCSztJQTY2QkwsZUE3NkJLO0lBODZCTCxvQkFBQTtFQXZNTjtFQXlNTTtJQUNFLHVCQUFBO0lBQ0Esa0JBQUE7RUF2TVI7RUEwTU07SUFDRSxXQUFBO0lBQ0EsdUJBQUE7RUF4TVI7RUEwTVE7SUFDRSxlQUFBO0VBeE1WO0VBME1VO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUF4TVo7RUFpTk07SUFDRSxnQkF2OEJHO0VBd3ZCWDtBQUNGO0FBd05FO0VBQ0UsV0FBQTtBQXROSjtBQXdOSTtFQUNFLDhCQUFBO0VBQ0Esa0NBQUE7RUFDQSxvQ0FBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0FBdE5OO0FBd05NO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHNEQUFBO0FBdE5SO0FBME5JO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHdEQUFBO0FBeE5OO0FBMk5JO0VBQ0UsMkJBQUE7RUFDQSwyQkFBQTtBQXpOTjtBQTROSTtFQUNFLHdCQUFBO0FBMU5OO0FBNk5JO0VBQ0Usd0JBQUE7QUEzTk47QUErTkk7RUFDRSw2QkFBQTtFQUNBLDRCQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtBQTdOTjtBQStOTTtFQUNFLHlCQUFBO0VBQ0EsMkJBQUE7QUE3TlI7QUFnT007RUFDRSx5QkFBQTtFQUNBLGtDQUFBO0FBOU5SO0FBbU9JO0VBQ0UsNkJBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseUJBQUE7QUFqT047QUFxT0k7RUFDRSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0EsMkJBQUE7RUFDQSxxQkFBQTtBQW5PTjtBQXNPSTtFQUNFLDBCQUFBO0FBcE9OO0FBdU9JO0VBQ0UseUJBQUE7RUFDQSwwQkFBQTtFQUNBLDBDQUFBO0FBck9OO0FBd09JO0VBQ0UseUJBQUE7QUF0T047QUF5T0k7RUFDRSx5QkFBQTtBQXZPTjtBQTRPTTtFQUNFLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSxnQ0FBQTtBQTFPUjtBQWdQRTtFQUNFLGtDQUFBO0VBQ0Esd0JBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7RUFDQSwyQkFBQTtFQUNBLDJCQUFBO0VBQ0EseUNBQUE7QUE5T0o7QUFpUEk7RUFDRSx3QkFBQTtFQUNBLDJCQUFBO0FBL09OO0FBa1BJO0VBQ0UsK0NBQUE7RUFDQSxxQ0FBQTtBQWhQTjtBQW1QSTtFQUNFLCtDQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtBQWpQTjtBQW9QSTtFQUNFLGdDQUFBO0VBQ0EsNEJBQUE7RUFDQSx5QkFBQTtBQWxQTjtBQXNQSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0FBcFBOO0FBc1BNO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtFQUNBLE9BQUE7QUFwUFI7QUFzUFE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0E3bUNDO0VBOG1DRCxnQkFBQTtBQXBQVjtBQXVQUTtFQUNFLGtCQUFBO0VBQ0EsY0F0bkNDO0VBdW5DRCxnQkFBQTtFQUNBLGdCQUFBO0FBclBWO0FBMlBJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLFVBQUE7QUF6UE47QUEyUE07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxPQUFBO0VBQ0EsWUFBQTtBQXpQUjtBQTJQUTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQTFvQ0M7RUEyb0NELGdCQUFBO0FBelBWO0FBZ1FFO0VBQ0UseUJBQUE7RUFDQSx3QkFBQTtBQTlQSjtBQWtRRTs7O0VBR0Usa0NBQUE7RUFDQSwyQkFBQTtFQUNBLCtCQUFBO0VBQ0EsMEJBQUE7RUFDQSw2QkFBQTtBQWhRSjtBQW1RRTtFQUNFLHNEQUFBO0FBalFKO0FBbVFJO0VBQ0UsNEZBQUE7QUFqUU47QUFxUUU7RUFDRSw0QkFBQTtBQW5RSjtBQXVRRTtFQUNFLDhCQUFBO0FBclFKO0FBd1FFO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0FBdFFKO0FBd1FJO0VBQ0UseUJBQUE7QUF0UU47QUEwUUU7RUFDRSxzQkFBQTtFQUNBLHVCQUFBO0FBeFFKO0FBNFFFO0VBQ0UsbUNBQUE7RUFDQSwyQ0FBQTtFQUNBLDhCQUFBO0VBQ0Esb0JBQUE7RUFDQSwyQkFBQTtBQTFRSjtBQTRRSTtFQUNFLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2QkFBQTtFQUNBLHFCQUFBO0VBQ0EsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLDhDQUFBO0VBQ0EseUJBQUE7RUFDQSxvREFBQTtFQUNBLG9DQUFBO0VBQ0EsMkJBQUE7RUFDQSw0QkFBQTtBQTFRTjtBQTRRTTtFQUNFLCtDQUFBO0VBQ0EsZ0RBQUE7QUExUVIiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb2xvciBQYWxldHRlIC0gV2hpdGUgJiBNaWxkIE9yYW5nZSBUaGVtZVxuJHByaW1hcnk6ICNmZjZiMzU7XG4kcHJpbWFyeS1saWdodDogI2ZmOGM1YTtcbiRwcmltYXJ5LWRhcms6ICNlNTVhMmI7XG4kc3VjY2VzczogIzEwYjk4MTtcblxuLy8gTmV1dHJhbCBDb2xvcnNcbiR3aGl0ZTogI2ZmZmZmZjtcbiRncmF5LTUwOiAjZjlmYWZiO1xuJGdyYXktMTAwOiAjZjNmNGY2O1xuJGdyYXktMjAwOiAjZTVlN2ViO1xuJGdyYXktMzAwOiAjZDFkNWRiO1xuJGdyYXktNDAwOiAjOWNhM2FmO1xuJGdyYXktNTAwOiAjNmI3MjgwO1xuJGdyYXktNjAwOiAjNGI1NTYzO1xuJGdyYXktNzAwOiAjMzc0MTUxO1xuJGdyYXktODAwOiAjMWYyOTM3O1xuXG4vLyBTcGFjaW5nIC0gUmVkdWNlZCBmb3IgcHJvZmVzc2lvbmFsIGNvbXBhY3QgbG9va1xuJHNwYWNpbmcteHM6IDAuMTI1cmVtO1xuJHNwYWNpbmctc206IDAuMjVyZW07XG4kc3BhY2luZy1tZDogMC41cmVtO1xuJHNwYWNpbmctbGc6IDAuNzVyZW07XG4kc3BhY2luZy14bDogMXJlbTtcbiRzcGFjaW5nLTJ4bDogMS4yNXJlbTtcbiRzcGFjaW5nLTN4bDogMS41cmVtO1xuXG4vLyBCb3JkZXIgUmFkaXVzXG4kcmFkaXVzLXNtOiAwLjM3NXJlbTtcbiRyYWRpdXMtbWQ6IDAuNXJlbTtcbiRyYWRpdXMtbGc6IDAuNzVyZW07XG5cbi8vIFNoYWRvd3NcbiRzaGFkb3ctc206IDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4kc2hhZG93LW1kOiAwIDRweCA2cHggLTFweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMnB4IDRweCAtMXB4IHJnYmEoMCwgMCwgMCwgMC4wNik7XG4kc2hhZG93LWxnOiAwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA0cHggNnB4IC0ycHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcblxuLy8gVHJhbnNpdGlvbnNcbiR0cmFuc2l0aW9uLWZhc3Q6IDAuMTVzIGVhc2Utb3V0O1xuJHRyYW5zaXRpb24tbm9ybWFsOiAwLjNzIGVhc2Utb3V0O1xuXG4vLyBNaXhpbnNcbkBtaXhpbiBjYXJkLXN0eWxlIHtcbiAgYmFja2dyb3VuZDogJHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLWxnO1xuICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKCRncmF5LTIwMCwgMC44KTtcbn1cblxuLy8gTWFpbiBDb250YWluZXJcbi5zbWFydC1kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODZweCk7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NnB4KTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJGdyYXktNTAgMCUsICR3aGl0ZSAxMDAlKTtcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgc2Fucy1zZXJpZjtcbiAgY29sb3I6ICRncmF5LTgwMDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhbGlnbi1pdGVtczogc3RyZXRjaDtcblxuICAmOjpiZWZvcmUge1xuICAgIGNvbnRlbnQ6ICcnO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgMjAlLCByZ2JhKCRwcmltYXJ5LCAwLjA4KSAwJSwgdHJhbnNwYXJlbnQgNTAlKTtcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgICB6LWluZGV4OiAwO1xuICB9XG59XG5cbi8vIExlZnQgU2lkZWJhclxuLmxlZnQtc2lkZWJhciB7XG4gIHdpZHRoOiAyNzBweDtcbiAgYmFja2dyb3VuZDogJHdoaXRlO1xuICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAkZ3JheS0yMDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTA7XG4gIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgLnNpZGViYXItc2VjdGlvbiB7XG4gICAgcGFkZGluZzogJHNwYWNpbmctbWQ7XG5cbiAgICAmLmRhc2hib2FyZC1zZWxlY3Rvci1zZWN0aW9uIHtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0xMDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cblxuICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBwYWRkaW5nLXRvcDogJHNwYWNpbmctbWQ7XG4gICAgfVxuXG4gICAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICAgIG1hcmdpbi10b3A6ICRzcGFjaW5nLWxnO1xuICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbGc7XG5cbiAgICAgIC5oZWFkZXItY29udGVudCB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGdhcDogJHNwYWNpbmctc207XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAuc2VjdGlvbi1pY29uIHtcbiAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgIHdpZHRoOiAxOHB4O1xuICAgICAgICAgIGhlaWdodDogMThweDtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIH1cblxuICAgICAgICAuc2VjdGlvbi10aXRsZSB7XG4gICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLmZpbHRlci1iYWRnZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJHByaW1hcnk7XG4gICAgICAgICAgY29sb3I6ICR3aGl0ZTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgd2lkdGg6IDEuMTI1cmVtO1xuICAgICAgICAgIGhlaWdodDogMS4xMjVyZW07XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC42NXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBEYXNoYm9hcmQgU2VsZWN0b3IgU2VjdGlvblxuICAuZGFzaGJvYXJkLXNlbGVjdG9yLXNlY3Rpb24ge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoJHByaW1hcnksIDAuMDYpLCByZ2JhKCRwcmltYXJ5LCAwLjAyKSk7XG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgkcHJpbWFyeSwgMC4xNSk7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1sZztcbiAgICBwYWRkaW5nOiAkc3BhY2luZy1tZCAhaW1wb3J0YW50O1xuICAgIG1hcmdpbjogJHNwYWNpbmctbWQ7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoJHByaW1hcnksIDAuMDgpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKCRwcmltYXJ5LCAwLjA4KSwgcmdiYSgkcHJpbWFyeSwgMC4wNCkpO1xuICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjIpO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKCRwcmltYXJ5LCAwLjEyKTtcbiAgICB9XG4gIH1cblxuICAvLyBNb2RlIFNlbGVjdGlvbiBTZWN0aW9uXG4gIC5tb2RlLXNlY3Rpb24ge1xuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0xMDA7XG4gICAgbWFyZ2luLWJvdHRvbTogMDtcblxuICAgIC5tb2RlLXNlbGVjdGlvbi1jb250YWluZXIge1xuICAgICAgLm1vZGUtdG9nZ2xlLXdyYXBwZXIge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1zbTtcbiAgICAgICAgYmFja2dyb3VuZDogJGdyYXktNTA7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbWQ7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRncmF5LTIwMDtcblxuICAgICAgICAubW9kZS1vcHRpb24ge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc207XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdHJhbnNwYXJlbnQ7XG5cbiAgICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjEpO1xuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjMpO1xuICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuXG4gICAgICAgICAgICAubW9kZS1vcHRpb24taWNvbiB7XG4gICAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAubW9kZS1vcHRpb24tdGl0bGUge1xuICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5tb2RlLW9wdGlvbi1kZXNjIHtcbiAgICAgICAgICAgICAgY29sb3I6IGRhcmtlbigkcHJpbWFyeSwgMTAlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmOm5vdCguYWN0aXZlKSB7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjc7XG5cbiAgICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgICBvcGFjaXR5OiAxO1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRncmF5LTEwMCwgMC41KTtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLm1vZGUtb3B0aW9uLWljb24ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgICB3aWR0aDogMjBweDtcbiAgICAgICAgICAgIGhlaWdodDogMjBweDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS01MDA7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLm1vZGUtb3B0aW9uLWNvbnRlbnQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgZ2FwOiAxcHg7XG5cbiAgICAgICAgICAgIC5tb2RlLW9wdGlvbi10aXRsZSB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICBjb2xvcjogJGdyYXktNzAwO1xuICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAubW9kZS1vcHRpb24tZGVzYyB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC42NXJlbTtcbiAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTUwMDtcbiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMTtcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAudG9nZ2xlLXN3aXRjaCB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy14cyAwO1xuXG4gICAgICAgICAgLmRhc2hib2FyZC1tb2RlLXRvZ2dsZSB7XG4gICAgICAgICAgICA6Om5nLWRlZXAge1xuICAgICAgICAgICAgICAubWF0LW1kYy1zbGlkZS10b2dnbGUge1xuICAgICAgICAgICAgICAgIC0tbWRjLXN3aXRjaC1zZWxlY3RlZC10cmFjay1jb2xvcjogI3skcHJpbWFyeX07XG4gICAgICAgICAgICAgICAgLS1tZGMtc3dpdGNoLXNlbGVjdGVkLWhhbmRsZS1jb2xvcjogI3skd2hpdGV9O1xuICAgICAgICAgICAgICAgIC0tbWRjLXN3aXRjaC1zZWxlY3RlZC1ob3Zlci10cmFjay1jb2xvcjogI3skcHJpbWFyeS1kYXJrfTtcbiAgICAgICAgICAgICAgICAtLW1kYy1zd2l0Y2gtc2VsZWN0ZWQtZm9jdXMtdHJhY2stY29sb3I6ICN7JHByaW1hcnktZGFya307XG4gICAgICAgICAgICAgICAgLS1tZGMtc3dpdGNoLXNlbGVjdGVkLXByZXNzZWQtdHJhY2stY29sb3I6ICN7JHByaW1hcnktZGFya307XG4gICAgICAgICAgICAgICAgLS1tZGMtc3dpdGNoLXVuc2VsZWN0ZWQtdHJhY2stY29sb3I6ICN7JGdyYXktMzAwfTtcbiAgICAgICAgICAgICAgICAtLW1kYy1zd2l0Y2gtdW5zZWxlY3RlZC1oYW5kbGUtY29sb3I6ICN7JHdoaXRlfTtcbiAgICAgICAgICAgICAgICAtLW1kYy1zd2l0Y2gtdW5zZWxlY3RlZC1ob3Zlci10cmFjay1jb2xvcjogI3skZ3JheS00MDB9O1xuICAgICAgICAgICAgICAgIC0tbWRjLXN3aXRjaC10cmFjay1oZWlnaHQ6IDE4cHg7XG4gICAgICAgICAgICAgICAgLS1tZGMtc3dpdGNoLXRyYWNrLXdpZHRoOiAzNnB4O1xuICAgICAgICAgICAgICAgIC0tbWRjLXN3aXRjaC1oYW5kbGUtaGVpZ2h0OiAxNHB4O1xuICAgICAgICAgICAgICAgIC0tbWRjLXN3aXRjaC1oYW5kbGUtd2lkdGg6IDE0cHg7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAubWRjLXN3aXRjaCB7XG4gICAgICAgICAgICAgICAgd2lkdGg6IDM2cHg7XG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAxOHB4O1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLm1kYy1zd2l0Y2hfX3RyYWNrIHtcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA5cHg7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAubWRjLXN3aXRjaF9faGFuZGxlIHtcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZGFzaGJvYXJkLXNlbGVjdG9yIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuXG4gICAgICAvLyBFbmhhbmNlZCBzdHlsaW5nIGZvciB0aGUgZGFzaGJvYXJkIHNlbGVjdG9yIGZpZWxkXG4gICAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGUgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoJHByaW1hcnksIDAuMDgpICFpbXBvcnRhbnQ7XG4gICAgICAgIGhlaWdodDogNDRweCAhaW1wb3J0YW50O1xuICAgICAgICBtaW4taGVpZ2h0OiA0NHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbWQgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyayAhaW1wb3J0YW50O1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEwcHggcmdiYSgkcHJpbWFyeSwgMC4xNSkgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmLm1hdC1mb2N1c2VkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyayAhaW1wb3J0YW50O1xuICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgkcHJpbWFyeSwgMC4xNSksIDAgNHB4IDEycHggcmdiYSgkcHJpbWFyeSwgMC4yKSAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAubWF0LW1kYy1mb3JtLWZpZWxkLWluZml4IHtcbiAgICAgICAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIG1pbi1oZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gRmlsdGVycyBTZWN0aW9uXG4gIC5maWx0ZXJzLXNlY3Rpb24ge1xuICAgIC5maWx0ZXJzLWNvbnRlbnQge1xuICAgICAgZmxleDogMTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgIG92ZXJmbG93LXk6IGF1dG87XG4gICAgICBwYWRkaW5nLXJpZ2h0OiAkc3BhY2luZy1zbTtcbiAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLW1kO1xuICAgICAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDI4MHB4KTtcblxuICAgICAgLy8gQ3VzdG9tIHNjcm9sbGJhclxuICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICAgICAgICB3aWR0aDogNHB4O1xuICAgICAgfVxuXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgfVxuXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMik7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjMpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5maWx0ZXItZ3JvdXAge1xuICAgICAgICAuZmlsdGVyLWxhYmVsIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAkc3BhY2luZy14cztcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS03MDA7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMnB4O1xuXG4gICAgICAgICAgLmxhYmVsLWljb24ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgICB3aWR0aDogMTZweDtcbiAgICAgICAgICAgIGhlaWdodDogMTZweDtcbiAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5sYWJlbC10ZXh0IHtcbiAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuc2VsZWN0aW9uLWNvdW50IHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC42NXJlbTtcbiAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjEpO1xuICAgICAgICAgICAgcGFkZGluZzogMXB4IDRweDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc207XG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAuZmlsdGVyLWZpZWxkIHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5maWx0ZXJzLWFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgcGFkZGluZy10b3A6ICRzcGFjaW5nLW1kO1xuICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICRncmF5LTEwMDtcbiAgICAgIG1hcmdpbi10b3A6IGF1dG87XG5cbiAgICAgIC5yZXNldC1idG4ge1xuICAgICAgICB3aWR0aDogMTQwcHg7XG4gICAgICAgIGhlaWdodDogMzZweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZDtcbiAgICAgICAgY29sb3I6ICRncmF5LTcwMDtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XG4gICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlO1xuICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XG4gICAgICAgIH1cblxuICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogJHNwYWNpbmctc207XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gTWFpbiBDb250ZW50IEFyZWFcbi5tYWluLWNvbnRlbnQge1xuICBmbGV4OiAxO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBwYWRkaW5nOiAwO1xuICBnYXA6IDA7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbiAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgLy8gR3JhZGllbnQgSGlnaGxpZ2h0IEJhclxuICAuZ3JhZGllbnQtaGlnaGxpZ2h0LWJhciB7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gICAgbWFyZ2luOiAwO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoJHByaW1hcnksIDAuMDYpLCByZ2JhKCRwcmltYXJ5LCAwLjAyKSk7XG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgkcHJpbWFyeSwgMC4xNSk7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1sZztcbiAgICBtYXJnaW46ICRzcGFjaW5nLW1kO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKCRwcmltYXJ5LCAwLjA4KTtcblxuICAgIC5oaWdobGlnaHQtY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcbiAgICAgIG1pbi1oZWlnaHQ6IDYwcHg7XG5cbiAgICAgIC5oaWdobGlnaHQtbGVmdCB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGdhcDogJHNwYWNpbmctc207XG4gICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICBtaW4td2lkdGg6IDI4MHB4O1xuXG4gICAgICAgIC5oaWdobGlnaHQtaWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgd2lkdGg6IDEuMXJlbTtcbiAgICAgICAgICBoZWlnaHQ6IDEuMXJlbTtcbiAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy14cztcbiAgICAgICAgfVxuXG4gICAgICAgIC5oaWdobGlnaHQtdGl0bGUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6ICRzcGFjaW5nLXhzO1xuICAgICAgICB9XG5cbiAgICAgICAgLmhpZ2hsaWdodC1zdGF0dXMge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICAgICAgICBjb2xvcjogJGdyYXktNjAwO1xuICAgICAgICAgIHBhZGRpbmc6IDNweCA4cHg7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbTtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjEpO1xuICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcblxuICAgICAgICAgICYucmVhZHkge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkc3VjY2VzcywgMC4xKTtcbiAgICAgICAgICAgIGNvbG9yOiAkc3VjY2VzcztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmhpZ2hsaWdodC1yaWdodCB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XG5cbiAgICAgICAgLmFpLWlucHV0LWNvbnRhaW5lciB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuXG4gICAgICAgICAgJi5kaXNhYmxlZC1tb2RlIHtcbiAgICAgICAgICAgIG9wYWNpdHk6IDAuNjtcblxuICAgICAgICAgICAgLmFpLWlucHV0LWZpZWxkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnNlbmQtYnRuIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC42KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuNikgIWltcG9ydGFudDtcblxuICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjcpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjcpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuYWktaW5wdXQtZmllbGQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgICAgICAgLy8gRW5oYW5jZWQgc3R5bGluZyB0byBtYXRjaCBkYXNoYm9hcmQgc2VsZWN0b3JcbiAgICAgICAgICAgIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0NHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDQ0cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSwgMCA0cHggMTJweCByZ2JhKCRwcmltYXJ5LCAwLjIpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5tYXQtbWRjLWZvcm0tZmllbGQtaW5maXgge1xuICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDE2cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgbWluLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaW5wdXQge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XG4gICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLnNlbmQtYnRuIHtcbiAgICAgICAgICAgIHdpZHRoOiA0NHB4O1xuICAgICAgICAgICAgaGVpZ2h0OiA0NHB4O1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJHByaW1hcnk7XG4gICAgICAgICAgICBjb2xvcjogJHdoaXRlO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZDtcbiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICRwcmltYXJ5O1xuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoJHByaW1hcnksIDAuMDgpO1xuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgICAgdG9wOiAwO1xuICAgICAgICAgICAgICBsZWZ0OiAtMTAwJTtcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgkd2hpdGUsIDAuMiksIHRyYW5zcGFyZW50KTtcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogbGVmdCAwLjVzO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAmOmhvdmVyOm5vdChbZGlzYWJsZWRdKSB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRwcmltYXJ5LWRhcms7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyaztcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKCRwcmltYXJ5LCAwLjE1KTtcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuXG4gICAgICAgICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgICAgICAgbGVmdDogMTAwJTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAmW2Rpc2FibGVkXSB7XG4gICAgICAgICAgICAgIG9wYWNpdHk6IDAuNDtcbiAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJGdyYXktMzAwO1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICRncmF5LTMwMDtcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiBub25lO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgICAgIHotaW5kZXg6IDE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUmVzcG9uc2l2ZSBEZXNpZ25cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIC5oaWdobGlnaHQtY29udGVudCB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoO1xuXG4gICAgICAgIC5oaWdobGlnaHQtbGVmdCB7XG4gICAgICAgICAgbWluLXdpZHRoOiBhdXRvO1xuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICB9XG5cbiAgICAgICAgLmhpZ2hsaWdodC1yaWdodCB7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAgICAgICAuYWktaW5wdXQtY29udGFpbmVyIHtcbiAgICAgICAgICAgIG1heC13aWR0aDogbm9uZTtcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIERhc2hib2FyZCBTZWN0aW9uXG4gIC5kYXNoYm9hcmQtc2VjdGlvbiB7XG4gICAgZmxleDogMTtcbiAgICBAaW5jbHVkZSBjYXJkLXN0eWxlO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIG1hcmdpbjogJHNwYWNpbmctbWQ7XG5cbiAgICAuZGFzaGJvYXJkLWNvbnRlbnQge1xuICAgICAgZmxleDogMTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgb3ZlcmZsb3cteTogYXV0bztcblxuICAgICAgLy8gQ3VzdG9tIHNjcm9sbGJhclxuICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICAgICAgICB3aWR0aDogNnB4O1xuICAgICAgfVxuXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwO1xuICAgICAgfVxuXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMik7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjMpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIEVtcHR5IFN0YXRlXG4gICAgICAuZW1wdHktc3RhdGUge1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmctMnhsO1xuXG4gICAgICAgIC5lbXB0eS1zdGF0ZS1jb250ZW50IHtcbiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgICAgbWF4LXdpZHRoOiA0NTBweDtcblxuICAgICAgICAgIC5lbXB0eS1zdGF0ZS1pY29uIHtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLWxnO1xuXG4gICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogM3JlbTtcbiAgICAgICAgICAgICAgd2lkdGg6IDNyZW07XG4gICAgICAgICAgICAgIGhlaWdodDogM3JlbTtcbiAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTMwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuZW1wdHktc3RhdGUtdGl0bGUge1xuICAgICAgICAgICAgbWFyZ2luOiAwIDAgJHNwYWNpbmctc20gMDtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4zNXJlbTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5lbXB0eS1zdGF0ZS1kZXNjcmlwdGlvbiB7XG4gICAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgICBjb2xvcjogJGdyYXktNjAwO1xuICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gTG9hZGluZyBTdGF0ZVxuICAgICAgLmxvYWRpbmctc3RhdGUge1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmctMnhsO1xuXG4gICAgICAgIC5sb2FkaW5nLWNvbnRlbnQge1xuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICBtYXgtd2lkdGg6IDQwMHB4O1xuXG4gICAgICAgICAgLmxvYWRpbmctc3Bpbm5lciB7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAkc3BhY2luZy1sZztcblxuICAgICAgICAgICAgLnNwaW4ge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDNyZW07XG4gICAgICAgICAgICAgIHdpZHRoOiAzcmVtO1xuICAgICAgICAgICAgICBoZWlnaHQ6IDNyZW07XG4gICAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiBzcGluIDEuNXMgbGluZWFyIGluZmluaXRlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5sb2FkaW5nLXRpdGxlIHtcbiAgICAgICAgICAgIG1hcmdpbjogMCAwICRzcGFjaW5nLXNtIDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDEuMzVyZW07XG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAubG9hZGluZy1kZXNjcmlwdGlvbiB7XG4gICAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgICBjb2xvcjogJGdyYXktNjAwO1xuICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRGFzaGJvYXJkIFN1bW1hcnlcbiAgICAgIC5kYXNoYm9hcmQtc3VtbWFyeSB7XG4gICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLXhsICRzcGFjaW5nLXhsICRzcGFjaW5nLWxnICRzcGFjaW5nLXhsO1xuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGdyYXktMjAwO1xuXG4gICAgICAgIC5zdW1tYXJ5LWNhcmRzIHtcbiAgICAgICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpO1xuICAgICAgICAgIGdhcDogJHNwYWNpbmctbGc7XG5cbiAgICAgICAgICAuc3VtbWFyeS1jYXJkIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZTtcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRncmF5LTIwMDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbGc7XG4gICAgICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1sZztcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xuXG4gICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZDtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjIpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAuc3VtbWFyeS1pY29uIHtcbiAgICAgICAgICAgICAgd2lkdGg6IDQ4cHg7XG4gICAgICAgICAgICAgIGhlaWdodDogNDhweDtcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZDtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgkcHJpbWFyeSwgMC4xKSwgcmdiYSgkcHJpbWFyeSwgMC4wNSkpO1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG5cbiAgICAgICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgICAgICAgICAgICAgIHdpZHRoOiAxLjVyZW07XG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAxLjVyZW07XG4gICAgICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5zdW1tYXJ5LWNvbnRlbnQge1xuICAgICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgICBtaW4td2lkdGg6IDA7XG5cbiAgICAgICAgICAgICAgLnN1bW1hcnktdmFsdWUge1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMnB4O1xuICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAuc3VtbWFyeS1sYWJlbCB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgICAgICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIENoYXJ0cyBDb250YWluZXJcbiAgICAgIC5jaGFydHMtY29udGFpbmVyIHtcbiAgICAgICAgZmxleDogMTtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmcteGw7XG4gICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoNDAwcHgsIDFmcikpO1xuICAgICAgICBnYXA6ICRzcGFjaW5nLXhsO1xuICAgICAgICBhbGlnbi1jb250ZW50OiBzdGFydDtcblxuICAgICAgICA6Om5nLWRlZXAgLmNoYXJ0LWNvbnRhaW5lciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRncmF5LTIwMDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLWxnO1xuICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLWxnO1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZDtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgkcHJpbWFyeSwgMC4yKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuY2hhcnQtaGVhZGVyIHtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLWxnO1xuICAgICAgICAgICAgcGFkZGluZy1ib3R0b206ICRzcGFjaW5nLW1kO1xuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRncmF5LTEwMDtcblxuICAgICAgICAgICAgaDMge1xuICAgICAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGNhbnZhcyB7XG4gICAgICAgICAgICBtYXgtd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICBoZWlnaHQ6IDMwMHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIEFuaW1hdGlvbnNcbkBrZXlmcmFtZXMgc3BpbiB7XG4gIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cbiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cbn1cblxuLy8gUmVzcG9uc2l2ZSBEZXNpZ25cbkBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIHtcbiAgLnNtYXJ0LWRhc2hib2FyZC1jb250YWluZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDRweCk7XG4gICAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDQ0cHgpO1xuICB9XG5cbiAgLmxlZnQtc2lkZWJhciB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgYm9yZGVyLXJpZ2h0OiBub25lO1xuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0yMDA7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gICAgbWF4LWhlaWdodDogNDV2aDtcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xuXG4gICAgLnNpZGViYXItc2VjdGlvbiB7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1zbSAkc3BhY2luZy1tZDtcbiAgICB9XG5cbiAgICAuZGFzaGJvYXJkLXNlbGVjdG9yLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luOiAkc3BhY2luZy1zbTtcbiAgICB9XG5cbiAgICAuZmlsdGVycy1zZWN0aW9uIHtcbiAgICAgIC5maWx0ZXJzLWNvbnRlbnQge1xuICAgICAgICBtYXgtaGVpZ2h0OiAyMDBweDtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAubWFpbi1jb250ZW50IHtcbiAgICBwYWRkaW5nOiAkc3BhY2luZy1zbTtcbiAgICBmbGV4OiAxO1xuICAgIG1pbi1oZWlnaHQ6IDA7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5sZWZ0LXNpZGViYXIge1xuICAgIC5zaWRlYmFyLXNlY3Rpb24ge1xuICAgICAgcGFkZGluZzogJHNwYWNpbmctc207XG4gICAgfVxuXG4gICAgLmZpbHRlcnMtc2VjdGlvbiB7XG4gICAgICAuZmlsdGVycy1jb250ZW50IHtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgIH1cblxuICAgICAgLmZpbHRlcnMtYWN0aW9ucyB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogJHNwYWNpbmctc207XG5cbiAgICAgICAgLnJlc2V0LWJ0bixcbiAgICAgICAgLmFwcGx5LWJ0biB7XG4gICAgICAgICAgZmxleDogbm9uZTtcbiAgICAgICAgICBoZWlnaHQ6IDMycHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAubWFpbi1jb250ZW50IHtcbiAgICBwYWRkaW5nOiAkc3BhY2luZy1zbTtcbiAgICBnYXA6ICRzcGFjaW5nLXNtO1xuXG4gICAgLmdyYWRpZW50LWhpZ2hsaWdodC1iYXIge1xuICAgICAgLmhpZ2hsaWdodC1jb250ZW50IHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmctbWQ7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoO1xuXG4gICAgICAgIC5oaWdobGlnaHQtbGVmdCB7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgYWxpZ24tc2VsZjogY2VudGVyO1xuICAgICAgICB9XG5cbiAgICAgICAgLmhpZ2hsaWdodC1yaWdodCB7XG4gICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAgICAgICAuYWktaW5wdXQtY29udGFpbmVyIHtcbiAgICAgICAgICAgIG1heC13aWR0aDogbm9uZTtcblxuICAgICAgICAgICAgLnNlbmQtYnRuIHtcbiAgICAgICAgICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICAgICAgICAgIGhlaWdodDogNDBweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZGFzaGJvYXJkLXNlY3Rpb24ge1xuICAgICAgLmRhc2hib2FyZC1jb250ZW50IHtcbiAgICAgICAgLmVtcHR5LXN0YXRlIHtcbiAgICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1sZztcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBBbmd1bGFyIE1hdGVyaWFsIE92ZXJyaWRlc1xuOjpuZy1kZWVwIHtcbiAgLy8gRm9ybSBGaWVsZCBTdHlsaW5nXG4gIC5tYXQtbWRjLWZvcm0tZmllbGQge1xuICAgIHdpZHRoOiAxMDAlO1xuXG4gICAgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLXNtICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAkZ3JheS0yMDAgIWltcG9ydGFudDtcbiAgICAgIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWwgIWltcG9ydGFudDtcbiAgICAgIGhlaWdodDogMzhweCAhaW1wb3J0YW50O1xuICAgICAgbWluLWhlaWdodDogMzhweCAhaW1wb3J0YW50O1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwICFpbXBvcnRhbnQ7XG4gICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc20gIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAmLm1hdC1mb2N1c2VkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICBiYWNrZ3JvdW5kOiAkd2hpdGUgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKCRwcmltYXJ5LCAwLjEpICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtZm9ybS1maWVsZC1pbmZpeCB7XG4gICAgICBwYWRkaW5nOiA2cHggOHB4ICFpbXBvcnRhbnQ7XG4gICAgICBtaW4taGVpZ2h0OiAxOHB4ICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtZm9ybS1maWVsZC1zdWJzY3JpcHQtd3JhcHBlciB7XG4gICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1kYy1ub3RjaGVkLW91dGxpbmUge1xuICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC8vIElucHV0IHN0eWxpbmdcbiAgICAubWF0LW1kYy1pbnB1dC1lbGVtZW50IHtcbiAgICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkZ3JheS04MDAgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDtcblxuICAgICAgJjo6cGxhY2Vob2xkZXIge1xuICAgICAgICBjb2xvcjogJGdyYXktNDAwICFpbXBvcnRhbnQ7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDAgIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgJjpkaXNhYmxlZCB7XG4gICAgICAgIGNvbG9yOiAkZ3JheS00MDAgIWltcG9ydGFudDtcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTZWxlY3Qgc3R5bGluZ1xuICAgIC5tYXQtbWRjLXNlbGVjdCB7XG4gICAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogJGdyYXktODAwICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLy8gU2VsZWN0IHRyaWdnZXIgc3R5bGluZyBmb3IgZGFzaGJvYXJkIHNlbGVjdG9yXG4gICAgJi5kYXNoYm9hcmQtc2VsZWN0b3IgLm1hdC1tZGMtc2VsZWN0LXRyaWdnZXIge1xuICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgbWluLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5tYXQtbWRjLXNlbGVjdC1hcnJvdy13cmFwcGVyIHtcbiAgICAgIHRyYW5zZm9ybTogbm9uZSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5tYXQtbWRjLXNlbGVjdC1hcnJvdyB7XG4gICAgICBjb2xvcjogJGdyYXktNTAwICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXNpemU6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtc2VsZWN0LWFycm93IHtcbiAgICAgIGNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICY6aG92ZXIgLm1hdC1tZGMtc2VsZWN0LWFycm93IHtcbiAgICAgIGNvbG9yOiAkZ3JheS02MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAvLyBQcmVmaXggaWNvbiBzdHlsaW5nXG4gICAgLm1hdC1tZGMtZm9ybS1maWVsZC1pY29uLXByZWZpeCB7XG4gICAgICAuaW5wdXQtcHJlZml4LWljb24ge1xuICAgICAgICBjb2xvcjogJGdyYXktNTAwICFpbXBvcnRhbnQ7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW0gIWltcG9ydGFudDtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy1zbSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFNlbGVjdCBPcHRpb25zXG4gIC5tYXQtbWRjLW9wdGlvbiB7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbSAhaW1wb3J0YW50O1xuICAgIG1hcmdpbjogMCAycHggIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuOHJlbSAhaW1wb3J0YW50O1xuICAgIG1pbi1oZWlnaHQ6IDM2cHggIWltcG9ydGFudDtcbiAgICBsaW5lLWhlaWdodDogMS4yICFpbXBvcnRhbnQ7XG4gICAgcGFkZGluZzogNnB4IDhweCAhaW1wb3J0YW50O1xuICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDtcbiAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tZmFzdCAhaW1wb3J0YW50O1xuXG4gICAgLy8gUmVtb3ZlIHNwYWNpbmcgZm9yIHNlYXJjaCBvcHRpb25cbiAgICAmOmZpcnN0LWNoaWxkIHtcbiAgICAgIG1hcmdpbi10b3A6IDAgIWltcG9ydGFudDtcbiAgICAgIHBhZGRpbmctdG9wOiA0cHggIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMDgpICFpbXBvcnRhbnQ7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMnB4KSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICYubWF0LW1kYy1vcHRpb24tYWN0aXZlIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMTIpICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAub3B0aW9uLWljb24ge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy1zbSAhaW1wb3J0YW50O1xuICAgICAgZm9udC1zaXplOiAwLjlyZW0gIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkZ3JheS01MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAvLyBEYXNoYm9hcmQgb3B0aW9uIHN0eWxpbmdcbiAgICAuZGFzaGJvYXJkLW9wdGlvbiB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgcGFkZGluZzogNHB4IDA7XG5cbiAgICAgIC5kYXNoYm9hcmQtaW5mbyB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogM3B4O1xuICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgIC5kYXNoYm9hcmQtbmFtZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjM7XG4gICAgICAgIH1cblxuICAgICAgICAuZGFzaGJvYXJkLWRlc2Mge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICAgICAgICBjb2xvcjogJGdyYXktNTAwO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjM7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFNlbGVjdGVkIGRhc2hib2FyZCBzdHlsaW5nIChjbG9zZWQgc3RhdGUpXG4gICAgLnNlbGVjdGVkLWRhc2hib2FyZCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgcGFkZGluZzogMDtcblxuICAgICAgLnNlbGVjdGVkLWluZm8ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBoZWlnaHQ6IDEwMCU7XG5cbiAgICAgICAgLnNlbGVjdGVkLW5hbWUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFJlbW92ZSB0b3Agc3BhY2luZyBmcm9tIG1hdC1zZWxlY3QgcGFuZWxcbiAgLm1hdC1tZGMtc2VsZWN0LXBhbmVsIHtcbiAgICBwYWRkaW5nLXRvcDogMCAhaW1wb3J0YW50O1xuICAgIG1hcmdpbi10b3A6IDAgIWltcG9ydGFudDtcbiAgfVxuXG4gIC8vIEJ1dHRvbiBTdHlsaW5nXG4gIC5tYXQtbWRjLWJ1dHRvbixcbiAgLm1hdC1tZGMtcmFpc2VkLWJ1dHRvbixcbiAgLm1hdC1tZGMtc3Ryb2tlZC1idXR0b24ge1xuICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc20gIWltcG9ydGFudDtcbiAgICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7XG4gICAgdGV4dC10cmFuc2Zvcm06IG5vbmUgIWltcG9ydGFudDtcbiAgICBtaW4td2lkdGg6IGF1dG8gIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgfVxuXG4gIC5tYXQtbWRjLXJhaXNlZC1idXR0b24ge1xuICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc20gIWltcG9ydGFudDtcblxuICAgICY6aG92ZXIge1xuICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZCAhaW1wb3J0YW50O1xuICAgIH1cbiAgfVxuXG4gIC5tYXQtbWRjLXN0cm9rZWQtYnV0dG9uIHtcbiAgICBib3JkZXItd2lkdGg6IDFweCAhaW1wb3J0YW50O1xuICB9XG5cbiAgLy8gRGF0ZXBpY2tlclxuICAubWF0LWRhdGVwaWNrZXItaW5wdXQge1xuICAgIGZvbnQtc2l6ZTogMC44NzVyZW0gIWltcG9ydGFudDtcbiAgfVxuXG4gIC5tYXQtZGF0ZXBpY2tlci10b2dnbGUge1xuICAgIHdpZHRoOiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgY29sb3I6ICRncmF5LTUwMCAhaW1wb3J0YW50O1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICB9XG4gIH1cblxuICAubWF0LWRhdGVwaWNrZXItdG9nZ2xlLWRlZmF1bHQtaWNvbiB7XG4gICAgd2lkdGg6IDE2cHggIWltcG9ydGFudDtcbiAgICBoZWlnaHQ6IDE2cHggIWltcG9ydGFudDtcbiAgfVxuXG4gIC8vIFNlbGVjdCBhbGwgY29udHJvbHMgc3R5bGluZ1xuICAuc2VsZWN0LWFsbC1jb250cm9scyB7XG4gICAgcGFkZGluZzogNHB4IDhweCA2cHggOHB4ICFpbXBvcnRhbnQ7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTAgIWltcG9ydGFudDtcbiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhICFpbXBvcnRhbnQ7XG4gICAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7XG4gICAgYm9yZGVyLXJhZGl1czogMCAhaW1wb3J0YW50O1xuXG4gICAgLnNlbGVjdC10b2dnbGUtYnRuIHtcbiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7XG4gICAgICBoZWlnaHQ6IDI2cHggIWltcG9ydGFudDtcbiAgICAgIGZvbnQtc2l6ZTogMC43NXJlbSAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLXJhZGl1czogM3B4ICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjEpICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoJHByaW1hcnksIDAuMikgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UgIWltcG9ydGFudDtcbiAgICAgIG1pbi1oZWlnaHQ6IDI2cHggIWltcG9ydGFudDtcbiAgICAgIGxpbmUtaGVpZ2h0OiAyNnB4ICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjE1KSAhaW1wb3J0YW50O1xuICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuMykgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatSlideToggleModule", "MatTooltipModule", "FormsModule", "ReactiveFormsModule", "FormControl", "first", "takeUntil", "startWith", "map", "Subject", "Chart", "registerables", "NgxMatSelectSearchModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r11", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r10", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "selectedLocations", "length", "location_r12", "value", "baseDate_r13", "displayName", "ctx_r6", "getLoadingMessage", "title", "description", "ctx_r7", "getEmptyStateMessage", "item_r15", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_92_div_2_Template", "ctx_r8", "getSummaryItems", "ɵɵelement", "register", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "smartDashboardService", "cdr", "destroy$", "tabs", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "dashboardData", "charts", "isLoading", "dashboardConfig", "useDefaultCharts", "locationFilterCtrl", "allLocationsSelected", "ngOnInit", "user", "getCurrentUser", "setDefaultDates", "initializeComponent", "setupLocationFilter", "detectChanges", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "setDefaultTabs", "setDefaultBaseDates", "loadDashboardConfig", "loadLocations", "dashboardData$", "pipe", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON>", "loading$", "loading", "getDashboardConfig", "response", "status", "error", "getDefaultDashboardTabs", "active", "getDefaultBaseDateOptions", "today", "Date", "year", "getFullYear", "month", "getMonth", "firstDayOfMonth", "formatDateForInput", "date", "String", "padStart", "day", "getDate", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "clearDashboardData", "onDefaultChartsToggle", "setMode", "useDefault", "sendMessage", "areAllFiltersValid", "generateDashboard", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "tenantId", "getLocations", "res", "result", "branches", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "location", "resetFilters", "updateLocationStates", "setValue", "filters", "baseDate", "validateFilters", "filteredLocations", "valueChanges", "filterLocations", "filterValue", "toLowerCase", "filter", "includes", "toggleAllLocations", "isAllSelected", "onLocationSelectionChange", "<PERSON><PERSON><PERSON><PERSON>", "currentTab", "dashboardType", "formatDateValue", "dateValue", "request", "user_query", "dashboard_type", "tenant_id", "use_default_charts", "chart", "destroy", "chartsContainer", "nativeElement", "innerHTML", "setTimeout", "chartConfig", "createChart", "summary_items", "canvas", "document", "createElement", "width", "height", "chartContainer", "className", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "chartOptions", "getChartConfig", "type", "mergedOptions", "options", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "i3", "SmartDashboardService", "ChangeDetectorRef", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_27_listener", "SmartDashboardComponent_Template_button_click_31_listener", "SmartDashboardComponent_mat_option_33_Template", "SmartDashboardComponent_Template_mat_select_valueChange_42_listener", "SmartDashboardComponent_mat_option_43_Template", "SmartDashboardComponent_Template_input_ngModelChange_51_listener", "SmartDashboardComponent_Template_input_ngModelChange_62_listener", "SmartDashboardComponent_Template_button_click_67_listener", "SmartDashboardComponent_Template_input_ngModelChange_84_listener", "SmartDashboardComponent_Template_input_keydown_84_listener", "SmartDashboardComponent_Template_button_click_85_listener", "SmartDashboardComponent_div_90_Template", "SmartDashboardComponent_div_91_Template", "SmartDashboardComponent_div_92_Template", "SmartDashboardComponent_div_93_Template", "ɵɵpipeBind1", "_r4", "_r5", "ɵɵclassProp", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "MatSuffix", "i8", "MatSelect", "MatSelectTrigger", "i9", "MatOption", "i10", "MatInput", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "MatTooltip", "i13", "DefaultValueAccessor", "NgControlStatus", "NgModel", "FormControlDirective", "i14", "MatSelectSearchComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, OnDestroy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { SmartDashboardService, DashboardTab, BaseDate, DashboardFilters, DashboardData, SummaryItem } from '../../services/smart-dashboard.service';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, ChartType, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatSlideToggleModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NgxMatSelectSearchModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('chartsContainer', { static: false }) chartsContainer!: ElementRef;\n\n  private destroy$ = new Subject<void>();\n\n  tabs: DashboardTab[] = [];\n  selectedTab = 0;\n  user: any;\n  locations: any[] = [];\n  baseDates: BaseDate[] = [];\n  selectedLocations: string[] = [];\n  selectedBaseDate = '';\n  startDate: string | Date = '';\n  endDate: string | Date = '';\n  chatMessage = '';\n  dashboardData: DashboardData | null = null;\n  charts: Chart[] = [];\n  isLoading = false;\n  dashboardConfig: any = null;\n  useDefaultCharts = false;\n\n  locationFilterCtrl = new FormControl();\n  filteredLocations: Observable<any[]>;\n  allLocationsSelected = true;\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService,\n    private smartDashboardService: SmartDashboardService,\n    private cdr: ChangeDetectorRef\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n\n  ngAfterViewInit(): void {}\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n\n  private initializeComponent(): void {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n\n    this.smartDashboardService.dashboardData$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.dashboardData = data;\n        if (data) {\n          this.renderCharts();\n        }\n        this.cdr.detectChanges();\n      });\n\n    this.smartDashboardService.loading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n        this.cdr.detectChanges();\n      });\n  }\n\n\n\n  private loadDashboardConfig(): void {\n    this.smartDashboardService.getDashboardConfig()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.dashboardConfig = response.data;\n          }\n        },\n        error: () => {}\n      });\n  }\n\n  private setDefaultTabs(): void {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n\n  private setDefaultBaseDates(): void {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n\n  private setDefaultDates(): void {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n\n  onDefaultChartsToggle(): void {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n    }\n  }\n\n  setMode(useDefault: boolean): void {\n    if (this.useDefaultCharts !== useDefault) {\n      this.useDefaultCharts = useDefault;\n      this.onDefaultChartsToggle();\n    }\n  }\n\n  sendMessage(): void {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: true\n              }));\n              this.selectedLocations = this.locations.map(location => location.value);\n              this.setupLocationFilter();\n            } else {\n              this.locations = [];\n              this.selectedLocations = [];\n            }\n            this.cdr.detectChanges();\n          },\n          error: () => {\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n\n\n\n  getDashboardDescription(index: number): string {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n\n  areAllFiltersValid(): boolean {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n\n  setupLocationFilter(): void {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(\n      startWith(''),\n      map(value => this.filterLocations(value || ''))\n    );\n  }\n\n  private filterLocations(value: string): any[] {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location =>\n      location.label.toLowerCase().includes(filterValue)\n    );\n  }\n\n  toggleAllLocations(): void {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  onLocationSelectionChange(selectedValues: any[]): void {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  private updateLocationStates(): void {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n\n  isAllSelected(): boolean {\n    return this.selectedLocations.length === this.locations.length;\n  }\n\n  getLoadingMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n\n  getEmptyStateMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n\n  generateDashboard(): void {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n\n    // Helper function to convert Date to string format\n    const formatDateValue = (dateValue: any): string => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n\n    this.clearCharts();\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n\n    this.smartDashboardService.generateDashboard(request)\n      .pipe(first())\n      .subscribe({\n        next: () => {},\n        error: () => {}\n      });\n  }\n\n  clearCharts(): void {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n\n  renderCharts(): void {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n\n    this.clearCharts();\n\n    setTimeout(() => {\n      this.dashboardData.charts.forEach((chartConfig: any) => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n\n  getSummaryItems(): SummaryItem[] {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n\n    return this.dashboardData.summary_items;\n  }\n\n  createChart(chartConfig: any): void {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n\n      const chart = new Chart(ctx, {\n        type: chartConfig.type as ChartType,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n\n      this.charts.push(chart);\n    }\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants *</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select\n              [(ngModel)]=\"selectedLocations\"\n              multiple\n              placeholder=\"Select restaurants\"\n              (selectionChange)=\"onLocationSelectionChange($event.value)\">\n\n              <!-- Search -->\n              <mat-option>\n                <ngx-mat-select-search\n                  placeholderLabel=\"Search restaurants...\"\n                  noEntriesFoundLabel=\"No restaurants found\"\n                  [formControl]=\"locationFilterCtrl\">\n                </ngx-mat-select-search>\n              </mat-option>\n\n              <!-- Select All Control -->\n              <div class=\"select-all-controls\">\n                <button\n                  mat-button\n                  class=\"select-toggle-btn\"\n                  (click)=\"toggleAllLocations()\">\n                  {{ isAllSelected() ? 'Deselect All' : 'Select All' }}\n                </button>\n              </div>\n\n              <!-- Location Options -->\n              <mat-option\n                *ngFor=\"let location of filteredLocations | async\"\n                [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Digi AI Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"areAllFiltersValid()\">\n            {{ areAllFiltersValid() ? 'Ready to analyze' : 'Please fill all required filters' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <!-- AI Input Container -->\n          <div class=\"ai-input-container\" [class.disabled-mode]=\"useDefaultCharts\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                [placeholder]=\"useDefaultCharts ? 'Default charts mode - input disabled' : 'Ask me about your business data...'\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"!areAllFiltersValid() || useDefaultCharts\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"useDefaultCharts ? !areAllFiltersValid() : (!chatMessage.trim() || !areAllFiltersValid())\"\n              [matTooltip]=\"useDefaultCharts ? 'Generate Default Dashboard' : 'Send Custom Query'\"\n            >\n              <mat-icon>{{ useDefaultCharts ? 'auto_awesome' : 'send' }}</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-content\">\n        <!-- Loading State -->\n        <div class=\"loading-state\" *ngIf=\"isLoading\">\n          <div class=\"loading-content\">\n            <div class=\"loading-spinner\">\n              <mat-icon class=\"spin\">refresh</mat-icon>\n            </div>\n            <h4 class=\"loading-title\">{{ getLoadingMessage().title }}</h4>\n            <p class=\"loading-description\">\n              {{ getLoadingMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!isLoading && !dashboardData\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">{{ getEmptyStateMessage().title }}</h4>\n            <p class=\"empty-state-description\">\n              {{ getEmptyStateMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Dashboard Summary -->\n        <div class=\"dashboard-summary\" *ngIf=\"!isLoading && dashboardData?.summary_items\">\n          <div class=\"summary-cards\">\n            <div class=\"summary-card\" *ngFor=\"let item of getSummaryItems()\">\n              <div class=\"summary-icon\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n              </div>\n              <div class=\"summary-content\">\n                <div class=\"summary-value\">{{ item.value }}</div>\n                <div class=\"summary-label\">{{ item.label }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"!isLoading && dashboardData\" #chartsContainer>\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAI9E,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjE,SAASC,OAAO,QAAoB,MAAM;AAC1C,SAASC,KAAK,EAAaC,aAAa,QAAQ,UAAU;AAC1D,SAASC,wBAAwB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;;ICLtDC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,KAAA,EAAgC;;;;;IAwBjEL,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IA6BEf,EAAA,CAAAC,cAAA,qBAE2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFXH,EAAA,CAAAI,UAAA,UAAAY,YAAA,CAAAC,KAAA,CAAwB;IACxBjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAI,YAAA,CAAAP,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAD,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,YAAA,CAAAC,WAAA,MACF;;;;;IAkGNnB,EAAA,CAAAC,cAAA,cAA6C;IAGhBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHsBH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,iBAAA,CAAAa,MAAA,CAAAC,iBAAA,GAAAC,KAAA,CAA+B;IAEvDtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,MAAA,CAAAC,iBAAA,GAAAE,WAAA,MACF;;;;;IAKJvB,EAAA,CAAAC,cAAA,cAA8D;IAG9CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAH0BH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,iBAAA,CAAAiB,MAAA,CAAAC,oBAAA,GAAAH,KAAA,CAAkC;IAE9DtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAY,MAAA,CAAAC,oBAAA,GAAAF,WAAA,MACF;;;;;IAOAvB,EAAA,CAAAC,cAAA,cAAiE;IAEnDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJvCH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAC,IAAA,CAAe;IAGE3B,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAT,KAAA,CAAgB;IAChBjB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAjB,KAAA,CAAgB;;;;;IARnDT,EAAA,CAAAC,cAAA,cAAkF;IAE9ED,EAAA,CAAA4B,UAAA,IAAAC,6CAAA,kBAQM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IATuCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAC,eAAA,GAAoB;;;;;IAanE/B,EAAA,CAAAgC,SAAA,kBAEM;;;ADpNdnC,KAAK,CAACoC,QAAQ,CAAC,GAAGnC,aAAa,CAAC;AAEhC,MAsBaoC,uBAAuB;EAyBlCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,qBAA4C,EAC5CC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IA1BL,KAAAC,QAAQ,GAAG,IAAI5C,OAAO,EAAQ;IAEtC,KAAA6C,IAAI,GAAmB,EAAE;IACzB,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA9B,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,EAAE;IACrB,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,OAAO,GAAkB,EAAE;IAC3B,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IAExB,KAAAC,kBAAkB,GAAG,IAAI/D,WAAW,EAAE;IAEtC,KAAAgE,oBAAoB,GAAG,IAAI;EAOvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IAC7C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACtB,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA,GAAU;EAEzBC,WAAWA,CAAA;IACT,IAAI,CAACxB,QAAQ,CAACyB,IAAI,EAAE;IACpB,IAAI,CAACzB,QAAQ,CAAC0B,QAAQ,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,cAAc,EAAE;IACrB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAACjC,qBAAqB,CAACkC,cAAc,CACtCC,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAC1B,aAAa,GAAG0B,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,YAAY,EAAE;;MAErB,IAAI,CAACrC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACxB,qBAAqB,CAACuC,QAAQ,CAChCJ,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC3B,SAAS,GAAG2B,OAAO;MACxB,IAAI,CAACvC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACN;EAIQQ,mBAAmBA,CAAA;IACzB,IAAI,CAAChC,qBAAqB,CAACyC,kBAAkB,EAAE,CAC5CN,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC7B,eAAe,GAAG4B,QAAQ,CAACL,IAAI;;MAExC,CAAC;MACDO,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEQd,cAAcA,CAAA;IACpB,IAAI,CAAC3B,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAAC6C,uBAAuB,EAAE;IAChE,IAAI,IAAI,CAAC1C,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC2C,MAAM,GAAG,IAAI;;EAE9B;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAACzB,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAAC+C,yBAAyB,EAAE;IACvE,IAAI,IAAI,CAACzC,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;EAEnD;EAEQ0C,eAAeA,CAAA;IACrB,MAAM2B,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,EAAE;IAChC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,QAAQ,EAAE;IAC9B,MAAMC,eAAe,GAAG,IAAIL,IAAI,CAACC,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC5C,SAAS,GAAG,IAAI,CAAC+C,kBAAkB,CAACD,eAAe,CAAC;IACzD,IAAI,CAAC7C,OAAO,GAAG,IAAI,CAAC8C,kBAAkB,CAACP,KAAK,CAAC;EAC/C;EAEQO,kBAAkBA,CAACC,IAAU;IACnC,MAAMN,IAAI,GAAGM,IAAI,CAACL,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGK,MAAM,CAACD,IAAI,CAACH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGR,IAAI,IAAIE,KAAK,IAAIO,GAAG,EAAE;EAClC;EAEAE,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC1D,WAAW,GAAG0D,KAAK;IACxB,IAAI,CAAC3D,IAAI,CAAC4D,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAAClB,MAAM,GAAGmB,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC9D,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;EACpB;EAEAsC,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACnE,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAElB;IACA,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACzB,IAAI,CAACL,WAAW,GAAG,EAAE;;EAEzB;EAEA0D,OAAOA,CAACC,UAAmB;IACzB,IAAI,IAAI,CAACtD,gBAAgB,KAAKsD,UAAU,EAAE;MACxC,IAAI,CAACtD,gBAAgB,GAAGsD,UAAU;MAClC,IAAI,CAACF,qBAAqB,EAAE;;EAEhC;EAEAG,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvD,gBAAgB,EAAE;MACzB;MACA,IAAI,IAAI,CAACwD,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,MAAM;MACL;MACA,IAAI,IAAI,CAAC9D,WAAW,CAAC+D,IAAI,EAAE,IAAI,IAAI,CAACF,kBAAkB,EAAE,EAAE;QACxD,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAAC9D,WAAW,GAAG,EAAE;;;EAG3B;EAEAgE,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAIArC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACd,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4D,QAAQ,EAAE;MACnC,IAAI,CAAChF,gBAAgB,CAACiF,YAAY,CAAC,IAAI,CAAC7D,IAAI,CAAC4D,QAAQ,CAAC,CACnD5C,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;QACTT,IAAI,EAAGsD,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAC9E,SAAS,GAAG4E,GAAG,CAACE,QAAQ,CAAC9H,GAAG,CAAE+H,MAAW,KAAM;cAClDzG,KAAK,EAAEyG,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEpH,KAAK,EAAEiH,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;YACH,IAAI,CAAClH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;YACvE,IAAI,CAAC4C,mBAAmB,EAAE;WAC3B,MAAM;YACL,IAAI,CAAClB,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC7B,iBAAiB,GAAG,EAAE;;UAE7B,IAAI,CAACyB,GAAG,CAACuB,aAAa,EAAE;QAC1B,CAAC;QACDoB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvC,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEAuF,YAAYA,CAAA;IACV,IAAI,CAACpH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;IACvE,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC7B,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK,GAAG,EAAE;IAChF,IAAI,CAAC0C,eAAe,EAAE;IACtB,IAAI,CAACwE,oBAAoB,EAAE;IAC3B,IAAI,CAAC7E,kBAAkB,CAAC8E,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAAC9F,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAClB,IAAI,CAAC5B,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAIAnD,uBAAuBA,CAACyF,KAAa;IACnC,IAAI,IAAI,CAAC3D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC2D,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI,CAAC3D,IAAI,CAAC2D,KAAK,CAAC,CAAC7E,WAAW;;IAErC,OAAO,8BAA8B;EACvC;EAEAsF,kBAAkBA,CAAA;IAChB,MAAMwB,OAAO,GAAG;MACd1F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCwH,QAAQ,EAAE,IAAI,CAACzF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IACD,OAAO,IAAI,CAACT,qBAAqB,CAACiG,eAAe,CAACF,OAAO,CAAC;EAC5D;EAEAxE,mBAAmBA,CAAA;IACjB,IAAI,CAAC2E,iBAAiB,GAAG,IAAI,CAAClF,kBAAkB,CAACmF,YAAY,CAAChE,IAAI,CAChE/E,SAAS,CAAC,EAAE,CAAC,EACbC,GAAG,CAACsB,KAAK,IAAI,IAAI,CAACyH,eAAe,CAACzH,KAAK,IAAI,EAAE,CAAC,CAAC,CAChD;EACH;EAEQyH,eAAeA,CAACzH,KAAa;IACnC,IAAI,CAAC,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClD,OAAO,EAAE;;IAEX,MAAM4H,WAAW,GAAG1H,KAAK,CAAC2H,WAAW,EAAE;IACvC,OAAO,IAAI,CAACjG,SAAS,CAACkG,MAAM,CAACZ,QAAQ,IACnCA,QAAQ,CAACxH,KAAK,CAACmI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACnD;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,aAAa,EAAE,EAAE;MACxB,IAAI,CAAClI,iBAAiB,GAAG,EAAE;KAC5B,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC,CAAC;;IAE9E,IAAI,CAACkH,oBAAoB,EAAE;IAC3B,IAAI,CAAC5F,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAmF,yBAAyBA,CAACC,cAAqB;IAC7C,IAAI,CAACpI,iBAAiB,GAAGoI,cAAc;IACvC,IAAI,CAACf,oBAAoB,EAAE;IAC3B,IAAI,CAAC5F,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEQqE,oBAAoBA,CAAA;IAC1B,IAAI,CAACxF,SAAS,CAAC0D,OAAO,CAAC4B,QAAQ,IAAG;MAChCA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAAClH,iBAAiB,CAACgI,QAAQ,CAACb,QAAQ,CAAChH,KAAK,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACsC,oBAAoB,GAAG,IAAI,CAACzC,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EACrF;EAEAiI,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClI,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EAChE;EAEAM,iBAAiBA,CAAA;IACf,MAAM8H,UAAU,GAAG,IAAI,CAAC1G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM0G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAClI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACjB,iBAAiB,CAAC+H,aAAa,CAAC;EACpE;EAEA3H,oBAAoBA,CAAA;IAClB,MAAM0H,UAAU,GAAG,IAAI,CAAC1G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM0G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAClI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACb,oBAAoB,CAAC2H,aAAa,CAAC;EACvE;EAEAtC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE,EAAE;MAC9B;;IAGF;IACA,MAAMwC,eAAe,GAAIC,SAAc,IAAY;MACjD,IAAIA,SAAS,YAAY/D,IAAI,EAAE;QAC7B,OAAO,IAAI,CAACM,kBAAkB,CAACyD,SAAS,CAAC;;MAE3C,OAAOA,SAAS,IAAI,EAAE;IACxB,CAAC;IAED,MAAMjB,OAAO,GAAqB;MAChC1F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCwH,QAAQ,EAAE,IAAI,CAACzF,gBAAgB;MAC/BC,SAAS,EAAEuG,eAAe,CAAC,IAAI,CAACvG,SAAS,CAAC;MAC1CC,OAAO,EAAEsG,eAAe,CAAC,IAAI,CAACtG,OAAO;KACtC;IAED,IAAI,CAACoB,WAAW,EAAE;IAElB,MAAMgF,UAAU,GAAG,IAAI,CAAC1G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM0G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAClI,KAAK,GAAG,UAAU;IAEhE,MAAMsI,OAAO,GAAG;MACdlB,OAAO,EAAEA,OAAO;MAChBmB,UAAU,EAAE,IAAI,CAACxG,WAAW;MAC5ByG,cAAc,EAAEL,aAAa;MAC7BM,SAAS,EAAE,IAAI,CAACjG,IAAI,EAAE4D,QAAQ,IAAI,EAAE;MACpCsC,kBAAkB,EAAE,IAAI,CAACtG;KAC1B;IAED,IAAI,CAACf,qBAAqB,CAACwE,iBAAiB,CAACyC,OAAO,CAAC,CAClD9E,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdiB,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEAf,WAAWA,CAAA;IACT,IAAI,CAACjB,MAAM,CAACmD,OAAO,CAACuD,KAAK,IAAG;MAC1B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;IACF,IAAI,CAAC3G,MAAM,GAAG,EAAE;IAEhB,IAAI,IAAI,CAAC4G,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;;EAErD;EAEApF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC3B,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,EAAE;MACrD;;IAGF,IAAI,CAACiB,WAAW,EAAE;IAElB8F,UAAU,CAAC,MAAK;MACd,IAAI,CAAChH,aAAa,CAACC,MAAM,CAACmD,OAAO,CAAE6D,WAAgB,IAAI;QACrD,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAnI,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAEmH,aAAa,EAAE;MACtC,OAAO,EAAE;;IAGX,OAAO,IAAI,CAACnH,aAAa,CAACmH,aAAa;EACzC;EAEAD,WAAWA,CAACD,WAAgB;IAC1B,MAAMG,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACxC,EAAE,GAAG,SAASqC,WAAW,CAACrC,EAAE,EAAE;IACrCwC,MAAM,CAACG,KAAK,GAAG,GAAG;IAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;IAEnB,MAAMC,cAAc,GAAGJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACpDG,cAAc,CAACC,SAAS,GAAG,iBAAiB;IAC5CD,cAAc,CAACV,SAAS,GAAG;;cAEjBE,WAAW,CAAC5I,KAAK;;KAE1B;IACDoJ,cAAc,CAACE,WAAW,CAACP,MAAM,CAAC;IAElC,IAAI,IAAI,CAACP,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACa,WAAW,CAACF,cAAc,CAAC;;IAGhE,MAAMG,GAAG,GAAGR,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;IACnC,IAAID,GAAG,EAAE;MACP,MAAME,YAAY,GAAG,IAAI,CAACzI,qBAAqB,CAAC0I,cAAc,CAACd,WAAW,CAACe,IAAI,CAAC;MAEhF,MAAMC,aAAa,GAAG;QACpB,GAAGH,YAAY;QACf,IAAIb,WAAW,CAACiB,OAAO,IAAI,EAAE;OAC9B;MAED,MAAMvB,KAAK,GAAG,IAAI/J,KAAK,CAACgL,GAAG,EAAE;QAC3BI,IAAI,EAAEf,WAAW,CAACe,IAAiB;QACnCtG,IAAI,EAAEuF,WAAW,CAACvF,IAAI;QACtBwG,OAAO,EAAED;OACV,CAAC;MAEF,IAAI,CAAChI,MAAM,CAACkI,IAAI,CAACxB,KAAK,CAAC;;EAE3B;;;uBAzYW1H,uBAAuB,EAAAlC,EAAA,CAAAqL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvL,EAAA,CAAAqL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAzL,EAAA,CAAAqL,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAA3L,EAAA,CAAAqL,iBAAA,CAAArL,EAAA,CAAA4L,iBAAA;IAAA;EAAA;;;YAAvB1J,uBAAuB;MAAA2J,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAnB,GAAA;QAAA,IAAAmB,EAAA;;;;;;;;;;;;;;;UC7CpChM,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAAiM,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAtB,GAAA,CAAAnI,WAAA,GAAAyJ,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBtB,GAAA,CAAA1E,WAAA,CAAAgG,MAAA,CAAAlL,KAAA,CAAyB;UAAA,EAA7C;UACjCjB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAA4B,UAAA,KAAAyK,8CAAA,wBAOa;UACfrM,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIhDH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAA4B,UAAA,KAAA0K,wCAAA,mBAEO;UACTtM,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAEtDD,EAAA,CAAAiM,UAAA,2BAAAM,sEAAAJ,MAAA;YAAA,OAAAtB,GAAA,CAAA/J,iBAAA,GAAAqL,MAAA;UAAA,EAA+B,6BAAAK,wEAAAL,MAAA;YAAA,OAGZtB,GAAA,CAAA5B,yBAAA,CAAAkD,MAAA,CAAAlL,KAAA,CAAuC;UAAA,EAH3B;UAM/BjB,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAgC,SAAA,iCAIwB;UAC1BhC,EAAA,CAAAG,YAAA,EAAa;UAGbH,EAAA,CAAAC,cAAA,eAAiC;UAI7BD,EAAA,CAAAiM,UAAA,mBAAAQ,0DAAA;YAAA,OAAS5B,GAAA,CAAA9B,kBAAA,EAAoB;UAAA,EAAC;UAC9B/I,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAA4B,UAAA,KAAA8K,8CAAA,wBAIa;;UACf1M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAiM,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAtB,GAAA,CAAAhI,gBAAA,GAAAsJ,MAAA;UAAA,EAA4B;UACtCnM,EAAA,CAAA4B,UAAA,KAAAgL,8CAAA,wBAEa;UACf5M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAiM,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAtB,GAAA,CAAA/H,SAAA,GAAAqJ,MAAA;UAAA,EAAuB;UAHzBnM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA6E;UAE/EhC,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAiM,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAtB,GAAA,CAAA9H,OAAA,GAAAoJ,MAAA;UAAA,EAAqB;UAHvBnM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA2E;UAE7EhC,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAAiM,UAAA,mBAAAc,0DAAA;YAAA,OAASlC,GAAA,CAAA3C,YAAA,EAAc;UAAA,EAAC;UACnElI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtDH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,eAA6B;UAQrBD,EAAA,CAAAiM,UAAA,2BAAAe,iEAAAb,MAAA;YAAA,OAAAtB,GAAA,CAAA7H,WAAA,GAAAmJ,MAAA;UAAA,EAAyB,qBAAAc,2DAAAd,MAAA;YAAA,OACdtB,GAAA,CAAA7D,UAAA,CAAAmF,MAAA,CAAkB;UAAA,EADJ;UAJ3BnM,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAiM,UAAA,mBAAAiB,0DAAA;YAAA,OAASrC,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAIvB5G,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQ/EH,EAAA,CAAAC,cAAA,eAA+B;UAG3BD,EAAA,CAAA4B,UAAA,KAAAuL,uCAAA,kBAUM;UAGNnN,EAAA,CAAA4B,UAAA,KAAAwL,uCAAA,kBAUM;UAGNpN,EAAA,CAAA4B,UAAA,KAAAyL,uCAAA,kBAYM;UAGNrN,EAAA,CAAA4B,UAAA,KAAA0L,uCAAA,kBAEM;UACRtN,EAAA,CAAAG,YAAA,EAAM;;;;;UApOQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAyK,GAAA,CAAAnI,WAAA,CAAuB;UAIC1C,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAAsK,GAAA,CAAApI,IAAA,CAAAoI,GAAA,CAAAnI,WAAA,mBAAAmI,GAAA,CAAApI,IAAA,CAAAoI,GAAA,CAAAnI,WAAA,EAAAjC,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAApI,IAAA,CAAS;UA4BJzC,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAyK,GAAA,CAAA/J,iBAAA,CAAAC,MAAA,KAAkC;UAM/Df,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAA/J,iBAAA,CAA+B;UAU3Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAyK,GAAA,CAAAvH,kBAAA,CAAkC;UAUlCtD,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAAiK,GAAA,CAAA7B,aAAA,wCACF;UAKqBhJ,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuN,WAAA,SAAA1C,GAAA,CAAArC,iBAAA,EAA4B;UAezCxI,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAyK,GAAA,CAAAhI,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAAjI,SAAA,CAAY;UAgB7C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAoN,GAAA,CAA6B,YAAA3C,GAAA,CAAA/H,SAAA;UAKE9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAoN,GAAA,CAAmB;UAclDxN,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAqN,GAAA,CAA2B,YAAA5C,GAAA,CAAA9H,OAAA;UAKI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAqN,GAAA,CAAiB;UAwBrBzN,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAA0N,WAAA,UAAA7C,GAAA,CAAAhE,kBAAA,GAAoC;UACjE7G,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAAiK,GAAA,CAAAhE,kBAAA,kEACF;UAIgC7G,EAAA,CAAAM,SAAA,GAAwC;UAAxCN,EAAA,CAAA0N,WAAA,kBAAA7C,GAAA,CAAAxH,gBAAA,CAAwC;UAKlErD,EAAA,CAAAM,SAAA,GAAgH;UAAhHN,EAAA,CAAAI,UAAA,gBAAAyK,GAAA,CAAAxH,gBAAA,iFAAgH,YAAAwH,GAAA,CAAA7H,WAAA,eAAA6H,GAAA,CAAAhE,kBAAA,MAAAgE,GAAA,CAAAxH,gBAAA;UAWlHrD,EAAA,CAAAM,SAAA,GAAsG;UAAtGN,EAAA,CAAAI,UAAA,aAAAyK,GAAA,CAAAxH,gBAAA,IAAAwH,GAAA,CAAAhE,kBAAA,MAAAgE,GAAA,CAAA7H,WAAA,CAAA+D,IAAA,OAAA8D,GAAA,CAAAhE,kBAAA,GAAsG,eAAAgE,GAAA,CAAAxH,gBAAA;UAG5FrD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAO,iBAAA,CAAAsK,GAAA,CAAAxH,gBAAA,2BAAgD;UAWpCrD,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAAyK,GAAA,CAAA1H,SAAA,CAAe;UAajBnD,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,UAAAyK,GAAA,CAAA1H,SAAA,KAAA0H,GAAA,CAAA5H,aAAA,CAAkC;UAa5BjD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAI,UAAA,UAAAyK,GAAA,CAAA1H,SAAA,KAAA0H,GAAA,CAAA5H,aAAA,kBAAA4H,GAAA,CAAA5H,aAAA,CAAAmH,aAAA,EAAgD;UAejDpK,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAAyK,GAAA,CAAA1H,SAAA,IAAA0H,GAAA,CAAA5H,aAAA,CAAiC;;;qBD5MpEvE,YAAY,EAAAiP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,SAAA,EACZnP,aAAa,EACbC,eAAe,EAAAmP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfpP,aAAa,EAAAqP,EAAA,CAAAC,OAAA,EACbrP,kBAAkB,EAAAsP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBvP,eAAe,EAAAwP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACf3P,cAAc,EAAA4P,GAAA,CAAAC,QAAA,EACd5P,mBAAmB,EAAA6P,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB/P,mBAAmB,EACnBC,oBAAoB,EACpBC,gBAAgB,EAAA8P,GAAA,CAAAC,UAAA,EAChB9P,WAAW,EAAA+P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACXjQ,mBAAmB,EAAA8P,GAAA,CAAAI,oBAAA,EACnBzP,wBAAwB,EAAA0P,GAAA,CAAAC,wBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKfzN,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}