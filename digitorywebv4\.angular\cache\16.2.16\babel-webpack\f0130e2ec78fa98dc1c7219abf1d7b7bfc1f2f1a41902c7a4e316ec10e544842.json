{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"../../services/smart-dashboard.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"ngx-mat-select-search\";\nconst _c0 = [\"chartsContainer\"];\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r10.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r11));\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.displayName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"mat-icon\", 61);\n    i0.ɵɵtext(4, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 62);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.getLoadingMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getLoadingMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"div\", 66)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 67);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r7.getEmptyStateMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getEmptyStateMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_98_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 74)(5, \"div\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.label);\n  }\n}\nfunction SmartDashboardComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_98_div_2_Template, 9, 3, \"div\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getSummaryItems());\n  }\n}\nfunction SmartDashboardComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 77, 78);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService, smartDashboardService, cdr) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.smartDashboardService = smartDashboardService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.tabs = [];\n    this.selectedTab = 0;\n    this.locations = [];\n    this.baseDates = [];\n    this.selectedLocations = [];\n    this.selectedBaseDate = '';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    this.dashboardData = null;\n    this.charts = [];\n    this.isLoading = false;\n    this.dashboardConfig = null;\n    this.locationFilterCtrl = new FormControl();\n    this.allLocationsSelected = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n  initializeComponent() {\n    this.loadDashboardTabs();\n    this.loadBaseDateOptions();\n    this.loadDashboardConfig();\n    this.loadLocations();\n    this.smartDashboardService.dashboardData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.dashboardData = data;\n      if (data) {\n        this.renderCharts();\n      }\n      this.cdr.detectChanges();\n    });\n    this.smartDashboardService.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.detectChanges();\n    });\n  }\n  loadDashboardTabs() {\n    this.smartDashboardService.getDashboardTabs().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.tabs = response.data;\n          if (this.tabs.length > 0) {\n            this.tabs[0].active = true;\n          }\n        }\n      },\n      error: () => {\n        this.setDefaultTabs();\n      }\n    });\n  }\n  loadBaseDateOptions() {\n    this.smartDashboardService.getBaseDateOptions().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.baseDates = response.data;\n          if (this.baseDates.length > 0) {\n            this.selectedBaseDate = this.baseDates[0].value;\n          }\n        }\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setDefaultBaseDates();\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadDashboardConfig() {\n    this.smartDashboardService.getDashboardConfig().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.dashboardConfig = response.data;\n        }\n      },\n      error: () => {}\n    });\n  }\n  setDefaultTabs() {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n  setDefaultBaseDates() {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n  setDefaultDates() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    // Create first day of current month\n    const firstDayOfMonth = new Date(year, month, 1);\n    // Format dates properly\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n  sendMessage() {\n    if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n      this.generateDashboard();\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: true\n            }));\n            this.selectedLocations = this.locations.map(location => location.value);\n            this.setupLocationFilter();\n          } else {\n            this.locations = [];\n            this.selectedLocations = [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n  getDashboardDescription(index) {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n  areAllFiltersValid() {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n  setupLocationFilter() {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(startWith(''), map(value => this.filterLocations(value || '')));\n  }\n  filterLocations(value) {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location => location.label.toLowerCase().includes(filterValue));\n  }\n  selectAllLocations() {\n    this.selectedLocations = [...this.locations.map(location => location.value)];\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  deselectAllLocations() {\n    this.selectedLocations = [];\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  onLocationSelectionChange(selectedValues) {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  updateLocationStates() {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n  isAllSelected() {\n    return this.selectedLocations.length === this.locations.length;\n  }\n  getLoadingMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n  getEmptyStateMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n  generateDashboard() {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    this.clearCharts();\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || ''\n    };\n    this.smartDashboardService.generateDashboard(request).pipe(first()).subscribe({\n      next: () => {},\n      error: () => {}\n    });\n  }\n  clearCharts() {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n  renderCharts() {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n    this.clearCharts();\n    setTimeout(() => {\n      this.dashboardData.charts.forEach(chartConfig => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n  getSummaryItems() {\n    if (!this.dashboardData?.summary) {\n      return [];\n    }\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getSummaryItems(this.dashboardData.summary, dashboardType);\n  }\n  createChart(chartConfig) {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n      const chart = new Chart(ctx, {\n        type: chartConfig.type,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n      this.charts.push(chart);\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.SmartDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartsContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 100,\n      vars: 29,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search restaurants...\", \"noEntriesFoundLabel\", \"No restaurants found\", 3, \"formControl\"], [1, \"select-all-controls\"], [\"mat-button\", \"\", 1, \"select-all-btn\", 3, \"disabled\", \"click\"], [\"mat-button\", \"\", 1, \"deselect-all-btn\", 3, \"disabled\", \"click\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask me about your business data...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"dashboard-summary\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"selection-count\"], [1, \"loading-state\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"dashboard-summary\"], [1, \"summary-cards\"], [\"class\", \"summary-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-value\"], [1, \"summary-label\"], [1, \"charts-container\"], [\"chartsContainer\", \"\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_27_listener($event) {\n            return ctx.onLocationSelectionChange($event.value);\n          });\n          i0.ɵɵelementStart(28, \"mat-option\");\n          i0.ɵɵelement(29, \"ngx-mat-select-search\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_31_listener() {\n            return ctx.selectAllLocations();\n          });\n          i0.ɵɵelementStart(32, \"mat-icon\");\n          i0.ɵɵtext(33, \"check_box\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" Select All \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_35_listener() {\n            return ctx.deselectAllLocations();\n          });\n          i0.ɵɵelementStart(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"check_box_outline_blank\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Deselect All \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵpipe(40, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 15)(42, \"label\", 16)(43, \"mat-icon\", 17);\n          i0.ɵɵtext(44, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 18);\n          i0.ɵɵtext(46, \"Base Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 20)(48, \"mat-select\", 26);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_48_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(49, SmartDashboardComponent_mat_option_49_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 15)(51, \"label\", 16)(52, \"mat-icon\", 17);\n          i0.ɵɵtext(53, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 18);\n          i0.ɵɵtext(55, \"Start Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"mat-form-field\", 20)(57, \"input\", 27);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_57_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"mat-datepicker-toggle\", 28)(59, \"mat-datepicker\", null, 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 15)(62, \"label\", 16)(63, \"mat-icon\", 17);\n          i0.ɵɵtext(64, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\", 18);\n          i0.ɵɵtext(66, \"End Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"mat-form-field\", 20)(68, \"input\", 30);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_68_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"mat-datepicker-toggle\", 28)(70, \"mat-datepicker\", null, 31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 32)(73, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_73_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(74, \"mat-icon\");\n          i0.ɵɵtext(75, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(76, \" Reset Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(77, \"div\", 34)(78, \"div\", 35)(79, \"div\", 36)(80, \"div\", 37)(81, \"mat-icon\", 38);\n          i0.ɵɵtext(82, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\", 39);\n          i0.ɵɵtext(84, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 40);\n          i0.ɵɵtext(86);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 41)(88, \"div\", 42)(89, \"mat-form-field\", 43)(90, \"input\", 44);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_90_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_90_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_91_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(92, \"mat-icon\");\n          i0.ɵɵtext(93, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(94, \"div\", 46)(95, \"div\", 47);\n          i0.ɵɵtemplate(96, SmartDashboardComponent_div_96_Template, 9, 2, \"div\", 48);\n          i0.ɵɵtemplate(97, SmartDashboardComponent_div_97_Template, 9, 2, \"div\", 49);\n          i0.ɵɵtemplate(98, SmartDashboardComponent_div_98_Template, 3, 1, \"div\", 50);\n          i0.ɵɵtemplate(99, SmartDashboardComponent_div_99_Template, 2, 0, \"div\", 51);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(60);\n          const _r5 = i0.ɵɵreference(71);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isAllSelected());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.selectedLocations.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(40, 27, ctx.filteredLocations));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(16);\n          i0.ɵɵclassProp(\"ready\", ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.areAllFiltersValid() ? \"Ready to analyze\" : \"Please fill all required filters\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", !ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || !ctx.areAllFiltersValid());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.dashboardData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.dashboardData == null ? null : ctx.dashboardData.summary));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatSuffix, MatSelectModule, i8.MatSelect, i8.MatSelectTrigger, i9.MatOption, MatInputModule, i10.MatInput, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, FormsModule, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, ReactiveFormsModule, i12.FormControlDirective, NgxMatSelectSearchModule, i13.MatSelectSearchComponent],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 270px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem !important;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.5rem;\\n  max-height: calc(100vh - 280px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #374151;\\n  border-color: #d1d5db;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n  background: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #ff6b35;\\n  color: #ff6b35;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0;\\n  gap: 0;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4b5563;\\n  padding: 3px 8px;\\n  border-radius: 0.375rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  width: 100%;\\n  max-width: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n  border: 2px solid #ff6b35;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  border-color: #e55a2b;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  background: #d1d5db;\\n  border-color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #ff6b35;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%] {\\n  padding: 1rem 1rem 0.75rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #4b5563;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  align-content: start;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header {\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header h3 {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container canvas {\\n  max-width: 100%;\\n  height: 300px !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    align-self: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.85rem !important;\\n  min-height: 48px !important;\\n  line-height: 1.2 !important;\\n  padding: 8px 12px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .select-all-controls {\\n  display: flex !important;\\n  gap: 4px !important;\\n  padding: 8px 12px !important;\\n  border-bottom: 1px solid #e0e0e0 !important;\\n  background: #f8f9fa !important;\\n  margin: 0 4px !important;\\n  border-radius: 4px !important;\\n}\\n  .select-all-controls .select-all-btn,   .select-all-controls .deselect-all-btn {\\n  flex: 1 !important;\\n  min-width: 0 !important;\\n  height: 32px !important;\\n  font-size: 0.75rem !important;\\n  padding: 0 8px !important;\\n  border-radius: 4px !important;\\n  font-weight: 500 !important;\\n}\\n  .select-all-controls .select-all-btn .mat-icon,   .select-all-controls .deselect-all-btn .mat-icon {\\n  font-size: 16px !important;\\n  width: 16px !important;\\n  height: 16px !important;\\n  margin-right: 4px !important;\\n}\\n  .select-all-controls .select-all-btn:disabled,   .select-all-controls .deselect-all-btn:disabled {\\n  opacity: 0.5 !important;\\n  cursor: not-allowed !important;\\n}\\n  .select-all-controls .select-all-btn {\\n  background: rgba(255, 107, 53, 0.1) !important;\\n  color: #ff6b35 !important;\\n  border: 1px solid rgba(255, 107, 53, 0.2) !important;\\n}\\n  .select-all-controls .select-all-btn:hover:not(:disabled) {\\n  background: rgba(255, 107, 53, 0.15) !important;\\n}\\n  .select-all-controls .deselect-all-btn {\\n  background: rgba(107, 114, 128, 0.1) !important;\\n  color: #4b5563 !important;\\n  border: 1px solid rgba(107, 114, 128, 0.2) !important;\\n}\\n  .select-all-controls .deselect-all-btn:hover:not(:disabled) {\\n  background: rgba(107, 114, 128, 0.15) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "ReactiveFormsModule", "FormControl", "first", "takeUntil", "startWith", "map", "Subject", "Chart", "registerables", "NgxMatSelectSearchModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r11", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r10", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "selectedLocations", "length", "location_r12", "value", "baseDate_r13", "displayName", "ctx_r6", "getLoadingMessage", "title", "description", "ctx_r7", "getEmptyStateMessage", "item_r15", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_98_div_2_Template", "ctx_r8", "getSummaryItems", "ɵɵelement", "register", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "smartDashboardService", "cdr", "destroy$", "tabs", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "dashboardData", "charts", "isLoading", "dashboardConfig", "locationFilterCtrl", "allLocationsSelected", "ngOnInit", "user", "getCurrentUser", "setDefaultDates", "initializeComponent", "setupLocationFilter", "detectChanges", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "loadDashboardTabs", "loadBaseDateOptions", "loadDashboardConfig", "loadLocations", "dashboardData$", "pipe", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON>", "loading$", "loading", "getDashboardTabs", "response", "status", "active", "error", "setDefaultTabs", "getBaseDateOptions", "setDefaultBaseDates", "getDashboardConfig", "getDefaultDashboardTabs", "getDefaultBaseDateOptions", "today", "Date", "year", "getFullYear", "month", "getMonth", "firstDayOfMonth", "formatDateForInput", "date", "String", "padStart", "day", "getDate", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "clearDashboardData", "sendMessage", "trim", "areAllFiltersValid", "generateDashboard", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "tenantId", "getLocations", "res", "result", "branches", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "location", "resetFilters", "updateLocationStates", "setValue", "filters", "baseDate", "validateFilters", "filteredLocations", "valueChanges", "filterLocations", "filterValue", "toLowerCase", "filter", "includes", "selectAllLocations", "deselectAllLocations", "onLocationSelectionChange", "<PERSON><PERSON><PERSON><PERSON>", "isAllSelected", "currentTab", "dashboardType", "request", "user_query", "dashboard_type", "tenant_id", "chart", "destroy", "chartsContainer", "nativeElement", "innerHTML", "setTimeout", "chartConfig", "createChart", "summary", "canvas", "document", "createElement", "width", "height", "chartContainer", "className", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "chartOptions", "getChartConfig", "type", "mergedOptions", "options", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "i3", "SmartDashboardService", "ChangeDetectorRef", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_valueChange_27_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_27_listener", "SmartDashboardComponent_Template_button_click_31_listener", "SmartDashboardComponent_Template_button_click_35_listener", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_mat_select_valueChange_48_listener", "SmartDashboardComponent_mat_option_49_Template", "SmartDashboardComponent_Template_input_ngModelChange_57_listener", "SmartDashboardComponent_Template_input_ngModelChange_68_listener", "SmartDashboardComponent_Template_button_click_73_listener", "SmartDashboardComponent_Template_input_ngModelChange_90_listener", "SmartDashboardComponent_Template_input_keydown_90_listener", "SmartDashboardComponent_Template_button_click_91_listener", "SmartDashboardComponent_div_96_Template", "SmartDashboardComponent_div_97_Template", "SmartDashboardComponent_div_98_Template", "SmartDashboardComponent_div_99_Template", "ɵɵpipeBind1", "_r4", "_r5", "ɵɵclassProp", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "MatSuffix", "i8", "MatSelect", "MatSelectTrigger", "i9", "MatOption", "i10", "MatInput", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "DefaultValueAccessor", "NgControlStatus", "NgModel", "FormControlDirective", "i13", "MatSelectSearchComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, OnDestroy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { SmartDashboardService, DashboardTab, BaseDate, DashboardFilters } from '../../services/smart-dashboard.service';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, ChartType, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NgxMatSelectSearchModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('chartsContainer', { static: false }) chartsContainer!: ElementRef;\n\n  private destroy$ = new Subject<void>();\n\n  tabs: DashboardTab[] = [];\n  selectedTab = 0;\n  user: any;\n  locations: any[] = [];\n  baseDates: BaseDate[] = [];\n  selectedLocations: string[] = [];\n  selectedBaseDate = '';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n  dashboardData: any = null;\n  charts: Chart[] = [];\n  isLoading = false;\n  dashboardConfig: any = null;\n\n  locationFilterCtrl = new FormControl();\n  filteredLocations: Observable<any[]>;\n  allLocationsSelected = true;\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService,\n    private smartDashboardService: SmartDashboardService,\n    private cdr: ChangeDetectorRef\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n\n  ngAfterViewInit(): void {}\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n\n  private initializeComponent(): void {\n    this.loadDashboardTabs();\n    this.loadBaseDateOptions();\n    this.loadDashboardConfig();\n    this.loadLocations();\n\n    this.smartDashboardService.dashboardData$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.dashboardData = data;\n        if (data) {\n          this.renderCharts();\n        }\n        this.cdr.detectChanges();\n      });\n\n    this.smartDashboardService.loading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n        this.cdr.detectChanges();\n      });\n  }\n\n  private loadDashboardTabs(): void {\n    this.smartDashboardService.getDashboardTabs()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.tabs = response.data;\n            if (this.tabs.length > 0) {\n              this.tabs[0].active = true;\n            }\n          }\n        },\n        error: () => {\n          this.setDefaultTabs();\n        }\n      });\n  }\n\n  private loadBaseDateOptions(): void {\n    this.smartDashboardService.getBaseDateOptions()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.baseDates = response.data;\n            if (this.baseDates.length > 0) {\n              this.selectedBaseDate = this.baseDates[0].value;\n            }\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.setDefaultBaseDates();\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private loadDashboardConfig(): void {\n    this.smartDashboardService.getDashboardConfig()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.dashboardConfig = response.data;\n          }\n        },\n        error: () => {}\n      });\n  }\n\n  private setDefaultTabs(): void {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n\n  private setDefaultBaseDates(): void {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n\n  private setDefaultDates(): void {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n\n    // Create first day of current month\n    const firstDayOfMonth = new Date(year, month, 1);\n\n    // Format dates properly\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n      this.generateDashboard();\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: true\n              }));\n              this.selectedLocations = this.locations.map(location => location.value);\n              this.setupLocationFilter();\n            } else {\n              this.locations = [];\n              this.selectedLocations = [];\n            }\n            this.cdr.detectChanges();\n          },\n          error: () => {\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n\n\n\n  getDashboardDescription(index: number): string {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n\n  areAllFiltersValid(): boolean {\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n\n  setupLocationFilter(): void {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(\n      startWith(''),\n      map(value => this.filterLocations(value || ''))\n    );\n  }\n\n  private filterLocations(value: string): any[] {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location =>\n      location.label.toLowerCase().includes(filterValue)\n    );\n  }\n\n  selectAllLocations(): void {\n    this.selectedLocations = [...this.locations.map(location => location.value)];\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  deselectAllLocations(): void {\n    this.selectedLocations = [];\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  onLocationSelectionChange(selectedValues: any[]): void {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  private updateLocationStates(): void {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n\n  isAllSelected(): boolean {\n    return this.selectedLocations.length === this.locations.length;\n  }\n\n  getLoadingMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n\n  getEmptyStateMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n\n  generateDashboard(): void {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n\n    this.clearCharts();\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || ''\n    };\n\n    this.smartDashboardService.generateDashboard(request)\n      .pipe(first())\n      .subscribe({\n        next: () => {},\n        error: () => {}\n      });\n  }\n\n  clearCharts(): void {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n\n  renderCharts(): void {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n\n    this.clearCharts();\n\n    setTimeout(() => {\n      this.dashboardData.charts.forEach((chartConfig: any) => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n\n  getSummaryItems(): any[] {\n    if (!this.dashboardData?.summary) {\n      return [];\n    }\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    return this.smartDashboardService.getSummaryItems(\n      this.dashboardData.summary,\n      dashboardType\n    );\n  }\n\n  createChart(chartConfig: any): void {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n\n      const chart = new Chart(ctx, {\n        type: chartConfig.type as ChartType,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n\n      this.charts.push(chart);\n    }\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants *</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select\n              [(value)]=\"selectedLocations\"\n              multiple\n              placeholder=\"Select restaurants\"\n              (selectionChange)=\"onLocationSelectionChange($event.value)\">\n\n              <!-- Search -->\n              <mat-option>\n                <ngx-mat-select-search\n                  placeholderLabel=\"Search restaurants...\"\n                  noEntriesFoundLabel=\"No restaurants found\"\n                  [formControl]=\"locationFilterCtrl\">\n                </ngx-mat-select-search>\n              </mat-option>\n\n              <!-- Select All Controls -->\n              <div class=\"select-all-controls\">\n                <button\n                  mat-button\n                  class=\"select-all-btn\"\n                  (click)=\"selectAllLocations()\"\n                  [disabled]=\"isAllSelected()\">\n                  <mat-icon>check_box</mat-icon>\n                  Select All\n                </button>\n                <button\n                  mat-button\n                  class=\"deselect-all-btn\"\n                  (click)=\"deselectAllLocations()\"\n                  [disabled]=\"selectedLocations.length === 0\">\n                  <mat-icon>check_box_outline_blank</mat-icon>\n                  Deselect All\n                </button>\n              </div>\n\n              <!-- Location Options -->\n              <mat-option\n                *ngFor=\"let location of filteredLocations | async\"\n                [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Smart Dashboard Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"areAllFiltersValid()\">\n            {{ areAllFiltersValid() ? 'Ready to analyze' : 'Please fill all required filters' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask me about your business data...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"!areAllFiltersValid()\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || !areAllFiltersValid()\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-content\">\n        <!-- Loading State -->\n        <div class=\"loading-state\" *ngIf=\"isLoading\">\n          <div class=\"loading-content\">\n            <div class=\"loading-spinner\">\n              <mat-icon class=\"spin\">refresh</mat-icon>\n            </div>\n            <h4 class=\"loading-title\">{{ getLoadingMessage().title }}</h4>\n            <p class=\"loading-description\">\n              {{ getLoadingMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!isLoading && !dashboardData\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">{{ getEmptyStateMessage().title }}</h4>\n            <p class=\"empty-state-description\">\n              {{ getEmptyStateMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Dashboard Summary -->\n        <div class=\"dashboard-summary\" *ngIf=\"!isLoading && dashboardData?.summary\">\n          <div class=\"summary-cards\">\n            <div class=\"summary-card\" *ngFor=\"let item of getSummaryItems()\">\n              <div class=\"summary-icon\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n              </div>\n              <div class=\"summary-content\">\n                <div class=\"summary-value\">{{ item.value }}</div>\n                <div class=\"summary-label\">{{ item.label }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"!isLoading && dashboardData\" #chartsContainer>\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAI9E,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjE,SAASC,OAAO,QAAoB,MAAM;AAC1C,SAASC,KAAK,EAAaC,aAAa,QAAQ,UAAU;AAC1D,SAASC,wBAAwB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;ICHtDC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,KAAA,EAAgC;;;;;IAwBjEL,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAuCEf,EAAA,CAAAC,cAAA,qBAE2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFXH,EAAA,CAAAI,UAAA,UAAAY,YAAA,CAAAC,KAAA,CAAwB;IACxBjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAI,YAAA,CAAAP,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAD,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,YAAA,CAAAC,WAAA,MACF;;;;;IAgGNnB,EAAA,CAAAC,cAAA,cAA6C;IAGhBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHsBH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,iBAAA,CAAAa,MAAA,CAAAC,iBAAA,GAAAC,KAAA,CAA+B;IAEvDtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,MAAA,CAAAC,iBAAA,GAAAE,WAAA,MACF;;;;;IAKJvB,EAAA,CAAAC,cAAA,cAA8D;IAG9CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAH0BH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,iBAAA,CAAAiB,MAAA,CAAAC,oBAAA,GAAAH,KAAA,CAAkC;IAE9DtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAY,MAAA,CAAAC,oBAAA,GAAAF,WAAA,MACF;;;;;IAOAvB,EAAA,CAAAC,cAAA,cAAiE;IAEnDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJvCH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAC,IAAA,CAAe;IAGE3B,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAT,KAAA,CAAgB;IAChBjB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAjB,KAAA,CAAgB;;;;;IARnDT,EAAA,CAAAC,cAAA,cAA4E;IAExED,EAAA,CAAA4B,UAAA,IAAAC,6CAAA,kBAQM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IATuCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAC,eAAA,GAAoB;;;;;IAanE/B,EAAA,CAAAgC,SAAA,kBAEM;;;AD9NdnC,KAAK,CAACoC,QAAQ,CAAC,GAAGnC,aAAa,CAAC;AAEhC,MAoBaoC,uBAAuB;EAwBlCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,qBAA4C,EAC5CC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IAzBL,KAAAC,QAAQ,GAAG,IAAI5C,OAAO,EAAQ;IAEtC,KAAA6C,IAAI,GAAmB,EAAE;IACzB,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA9B,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,eAAe,GAAQ,IAAI;IAE3B,KAAAC,kBAAkB,GAAG,IAAI9D,WAAW,EAAE;IAEtC,KAAA+D,oBAAoB,GAAG,IAAI;EAOvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACpB,WAAW,CAACqB,cAAc,EAAE;IAC7C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACrB,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA,GAAU;EAEzBC,WAAWA,CAAA;IACT,IAAI,CAACvB,QAAQ,CAACwB,IAAI,EAAE;IACpB,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAAChC,qBAAqB,CAACiC,cAAc,CACtCC,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACzB,aAAa,GAAGyB,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,YAAY,EAAE;;MAErB,IAAI,CAACpC,GAAG,CAACsB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACvB,qBAAqB,CAACsC,QAAQ,CAChCJ,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC1B,SAAS,GAAG0B,OAAO;MACxB,IAAI,CAACtC,GAAG,CAACsB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACN;EAEQM,iBAAiBA,CAAA;IACvB,IAAI,CAAC7B,qBAAqB,CAACwC,gBAAgB,EAAE,CAC1CN,IAAI,CAAChF,KAAK,EAAE,CAAC,CACbiF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACvC,IAAI,GAAGsC,QAAQ,CAACL,IAAI;UACzB,IAAI,IAAI,CAACjC,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAACwC,MAAM,GAAG,IAAI;;;MAGhC,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACC,cAAc,EAAE;MACvB;KACD,CAAC;EACN;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAAC9B,qBAAqB,CAAC8C,kBAAkB,EAAE,CAC5CZ,IAAI,CAAChF,KAAK,EAAE,CAAC,CACbiF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACpC,SAAS,GAAGmC,QAAQ,CAACL,IAAI;UAC9B,IAAI,IAAI,CAAC9B,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;;QAGnD,IAAI,CAACsB,GAAG,CAACsB,aAAa,EAAE;MAC1B,CAAC;MACDqB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACG,mBAAmB,EAAE;QAC1B,IAAI,CAAC9C,GAAG,CAACsB,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQQ,mBAAmBA,CAAA;IACzB,IAAI,CAAC/B,qBAAqB,CAACgD,kBAAkB,EAAE,CAC5Cd,IAAI,CAAChF,KAAK,EAAE,CAAC,CACbiF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC5B,eAAe,GAAG2B,QAAQ,CAACL,IAAI;;MAExC,CAAC;MACDQ,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEQC,cAAcA,CAAA;IACpB,IAAI,CAAC1C,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAACiD,uBAAuB,EAAE;IAChE,IAAI,IAAI,CAAC9C,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAACwC,MAAM,GAAG,IAAI;;EAE9B;EAEQI,mBAAmBA,CAAA;IACzB,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAACkD,yBAAyB,EAAE;IACvE,IAAI,IAAI,CAAC5C,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;EAEnD;EAEQyC,eAAeA,CAAA;IACrB,MAAM+B,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,EAAE;IAChC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,QAAQ,EAAE;IAE9B;IACA,MAAMC,eAAe,GAAG,IAAIL,IAAI,CAACC,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC/C,SAAS,GAAG,IAAI,CAACkD,kBAAkB,CAACD,eAAe,CAAC;IACzD,IAAI,CAAChD,OAAO,GAAG,IAAI,CAACiD,kBAAkB,CAACP,KAAK,CAAC;EAC/C;EAEQO,kBAAkBA,CAACC,IAAU;IACnC,MAAMN,IAAI,GAAGM,IAAI,CAACL,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGK,MAAM,CAACD,IAAI,CAACH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGR,IAAI,IAAIE,KAAK,IAAIO,GAAG,EAAE;EAClC;EAEAE,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC7D,WAAW,GAAG6D,KAAK;IACxB,IAAI,CAAC9D,IAAI,CAAC+D,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACxB,MAAM,GAAGyB,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF,IAAI,CAACjE,qBAAqB,CAACqE,kBAAkB,EAAE;IAC/C,IAAI,CAACzC,WAAW,EAAE;EACpB;EAEA0C,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5D,WAAW,CAAC6D,IAAI,EAAE,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;MACxD,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAAC/D,WAAW,GAAG,EAAE;;EAEzB;EAEAgE,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAIAtC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACd,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC6D,QAAQ,EAAE;MACnC,IAAI,CAAChF,gBAAgB,CAACiF,YAAY,CAAC,IAAI,CAAC9D,IAAI,CAAC6D,QAAQ,CAAC,CACnD7C,IAAI,CAAChF,KAAK,EAAE,CAAC,CACbiF,SAAS,CAAC;QACTT,IAAI,EAAGuD,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAC9E,SAAS,GAAG4E,GAAG,CAACE,QAAQ,CAAC9H,GAAG,CAAE+H,MAAW,KAAM;cAClDzG,KAAK,EAAEyG,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEpH,KAAK,EAAEiH,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;YACH,IAAI,CAAClH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;YACvE,IAAI,CAAC2C,mBAAmB,EAAE;WAC3B,MAAM;YACL,IAAI,CAACjB,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC7B,iBAAiB,GAAG,EAAE;;UAE7B,IAAI,CAACyB,GAAG,CAACsB,aAAa,EAAE;QAC1B,CAAC;QACDqB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvC,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEAuF,YAAYA,CAAA;IACV,IAAI,CAACpH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;IACvE,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC7B,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK,GAAG,EAAE;IAChF,IAAI,CAACyC,eAAe,EAAE;IACtB,IAAI,CAACyE,oBAAoB,EAAE;IAC3B,IAAI,CAAC9E,kBAAkB,CAAC+E,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAAC9F,qBAAqB,CAACqE,kBAAkB,EAAE;IAC/C,IAAI,CAACzC,WAAW,EAAE;IAClB,IAAI,CAAC3B,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAIAlD,uBAAuBA,CAAC4F,KAAa;IACnC,IAAI,IAAI,CAAC9D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8D,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI,CAAC9D,IAAI,CAAC8D,KAAK,CAAC,CAAChF,WAAW;;IAErC,OAAO,8BAA8B;EACvC;EAEAuF,kBAAkBA,CAAA;IAChB,MAAMuB,OAAO,GAAqB;MAChC1F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCwH,QAAQ,EAAE,IAAI,CAACzF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IACD,OAAO,IAAI,CAACT,qBAAqB,CAACiG,eAAe,CAACF,OAAO,CAAC;EAC5D;EAEAzE,mBAAmBA,CAAA;IACjB,IAAI,CAAC4E,iBAAiB,GAAG,IAAI,CAACnF,kBAAkB,CAACoF,YAAY,CAACjE,IAAI,CAChE9E,SAAS,CAAC,EAAE,CAAC,EACbC,GAAG,CAACsB,KAAK,IAAI,IAAI,CAACyH,eAAe,CAACzH,KAAK,IAAI,EAAE,CAAC,CAAC,CAChD;EACH;EAEQyH,eAAeA,CAACzH,KAAa;IACnC,IAAI,CAAC,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClD,OAAO,EAAE;;IAEX,MAAM4H,WAAW,GAAG1H,KAAK,CAAC2H,WAAW,EAAE;IACvC,OAAO,IAAI,CAACjG,SAAS,CAACkG,MAAM,CAACZ,QAAQ,IACnCA,QAAQ,CAACxH,KAAK,CAACmI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACnD;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,CAACjI,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACsI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC,CAAC;IAC5E,IAAI,CAACkH,oBAAoB,EAAE;IAC3B,IAAI,CAAC5F,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAmF,oBAAoBA,CAAA;IAClB,IAAI,CAAClI,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACqH,oBAAoB,EAAE;IAC3B,IAAI,CAAC5F,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAoF,yBAAyBA,CAACC,cAAqB;IAC7C,IAAI,CAACpI,iBAAiB,GAAGoI,cAAc;IACvC,IAAI,CAACf,oBAAoB,EAAE;IAC3B,IAAI,CAAC5F,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEQsE,oBAAoBA,CAAA;IAC1B,IAAI,CAACxF,SAAS,CAAC6D,OAAO,CAACyB,QAAQ,IAAG;MAChCA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAAClH,iBAAiB,CAACgI,QAAQ,CAACb,QAAQ,CAAChH,KAAK,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACqC,oBAAoB,GAAG,IAAI,CAACxC,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EACrF;EAEAoI,aAAaA,CAAA;IACX,OAAO,IAAI,CAACrI,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EAChE;EAEAM,iBAAiBA,CAAA;IACf,MAAM+H,UAAU,GAAG,IAAI,CAAC3G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM2G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACnI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACjB,iBAAiB,CAACgI,aAAa,CAAC;EACpE;EAEA5H,oBAAoBA,CAAA;IAClB,MAAM2H,UAAU,GAAG,IAAI,CAAC3G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM2G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACnI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACb,oBAAoB,CAAC4H,aAAa,CAAC;EACvE;EAEAtC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE,EAAE;MAC9B;;IAGF,MAAMuB,OAAO,GAAqB;MAChC1F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCwH,QAAQ,EAAE,IAAI,CAACzF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IAED,IAAI,CAACmB,WAAW,EAAE;IAElB,MAAMkF,UAAU,GAAG,IAAI,CAAC3G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM2G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACnI,KAAK,GAAG,UAAU;IAEhE,MAAMqI,OAAO,GAAG;MACdjB,OAAO,EAAEA,OAAO;MAChBkB,UAAU,EAAE,IAAI,CAACvG,WAAW;MAC5BwG,cAAc,EAAEH,aAAa;MAC7BI,SAAS,EAAE,IAAI,CAACjG,IAAI,EAAE6D,QAAQ,IAAI;KACnC;IAED,IAAI,CAAC/E,qBAAqB,CAACyE,iBAAiB,CAACuC,OAAO,CAAC,CAClD9E,IAAI,CAAChF,KAAK,EAAE,CAAC,CACbiF,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdkB,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEAhB,WAAWA,CAAA;IACT,IAAI,CAAChB,MAAM,CAACsD,OAAO,CAACkD,KAAK,IAAG;MAC1B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;IACF,IAAI,CAACzG,MAAM,GAAG,EAAE;IAEhB,IAAI,IAAI,CAAC0G,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;;EAErD;EAEAnF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC1B,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,EAAE;MACrD;;IAGF,IAAI,CAACgB,WAAW,EAAE;IAElB6F,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9G,aAAa,CAACC,MAAM,CAACsD,OAAO,CAAEwD,WAAgB,IAAI;QACrD,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAjI,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAEiH,OAAO,EAAE;MAChC,OAAO,EAAE;;IAGX,MAAMd,UAAU,GAAG,IAAI,CAAC3G,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAM2G,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACnI,KAAK,GAAG,UAAU;IAEhE,OAAO,IAAI,CAACqB,qBAAqB,CAACP,eAAe,CAC/C,IAAI,CAACkB,aAAa,CAACiH,OAAO,EAC1Bb,aAAa,CACd;EACH;EAEAY,WAAWA,CAACD,WAAgB;IAC1B,MAAMG,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACtC,EAAE,GAAG,SAASmC,WAAW,CAACnC,EAAE,EAAE;IACrCsC,MAAM,CAACG,KAAK,GAAG,GAAG;IAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;IAEnB,MAAMC,cAAc,GAAGJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACpDG,cAAc,CAACC,SAAS,GAAG,iBAAiB;IAC5CD,cAAc,CAACV,SAAS,GAAG;;cAEjBE,WAAW,CAAC1I,KAAK;;KAE1B;IACDkJ,cAAc,CAACE,WAAW,CAACP,MAAM,CAAC;IAElC,IAAI,IAAI,CAACP,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACa,WAAW,CAACF,cAAc,CAAC;;IAGhE,MAAMG,GAAG,GAAGR,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;IACnC,IAAID,GAAG,EAAE;MACP,MAAME,YAAY,GAAG,IAAI,CAACvI,qBAAqB,CAACwI,cAAc,CAACd,WAAW,CAACe,IAAI,CAAC;MAEhF,MAAMC,aAAa,GAAG;QACpB,GAAGH,YAAY;QACf,IAAIb,WAAW,CAACiB,OAAO,IAAI,EAAE;OAC9B;MAED,MAAMvB,KAAK,GAAG,IAAI7J,KAAK,CAAC8K,GAAG,EAAE;QAC3BI,IAAI,EAAEf,WAAW,CAACe,IAAiB;QACnCrG,IAAI,EAAEsF,WAAW,CAACtF,IAAI;QACtBuG,OAAO,EAAED;OACV,CAAC;MAEF,IAAI,CAAC9H,MAAM,CAACgI,IAAI,CAACxB,KAAK,CAAC;;EAE3B;;;uBArZWxH,uBAAuB,EAAAlC,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAvL,EAAA,CAAAmL,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAAzL,EAAA,CAAAmL,iBAAA,CAAAnL,EAAA,CAAA0L,iBAAA;IAAA;EAAA;;;YAAvBxJ,uBAAuB;MAAAyJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAnB,GAAA;QAAA,IAAAmB,EAAA;;;;;;;;;;;;;;;UCzCpC9L,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAA+L,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAtB,GAAA,CAAAjI,WAAA,GAAAuJ,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBtB,GAAA,CAAArE,WAAA,CAAA2F,MAAA,CAAAhL,KAAA,CAAyB;UAAA,EAA7C;UACjCjB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAA4B,UAAA,KAAAuK,8CAAA,wBAOa;UACfnM,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIhDH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAA4B,UAAA,KAAAwK,wCAAA,mBAEO;UACTpM,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAEtDD,EAAA,CAAA+L,UAAA,yBAAAM,oEAAAJ,MAAA;YAAA,OAAAtB,GAAA,CAAA7J,iBAAA,GAAAmL,MAAA;UAAA,EAA6B,6BAAAK,wEAAAL,MAAA;YAAA,OAGVtB,GAAA,CAAA1B,yBAAA,CAAAgD,MAAA,CAAAhL,KAAA,CAAuC;UAAA,EAH7B;UAM7BjB,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAgC,SAAA,iCAIwB;UAC1BhC,EAAA,CAAAG,YAAA,EAAa;UAGbH,EAAA,CAAAC,cAAA,eAAiC;UAI7BD,EAAA,CAAA+L,UAAA,mBAAAQ,0DAAA;YAAA,OAAS5B,GAAA,CAAA5B,kBAAA,EAAoB;UAAA,EAAC;UAE9B/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAI8C;UAD5CD,EAAA,CAAA+L,UAAA,mBAAAS,0DAAA;YAAA,OAAS7B,GAAA,CAAA3B,oBAAA,EAAsB;UAAA,EAAC;UAEhChJ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5CH,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAA4B,UAAA,KAAA6K,8CAAA,wBAIa;;UACfzM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA+L,UAAA,yBAAAW,oEAAAT,MAAA;YAAA,OAAAtB,GAAA,CAAA9H,gBAAA,GAAAoJ,MAAA;UAAA,EAA4B;UACtCjM,EAAA,CAAA4B,UAAA,KAAA+K,8CAAA,wBAEa;UACf3M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA+L,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAtB,GAAA,CAAA7H,SAAA,GAAAmJ,MAAA;UAAA,EAAuB;UAHzBjM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA6E;UAE/EhC,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA+L,UAAA,2BAAAc,iEAAAZ,MAAA;YAAA,OAAAtB,GAAA,CAAA5H,OAAA,GAAAkJ,MAAA;UAAA,EAAqB;UAHvBjM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA2E;UAE7EhC,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA+L,UAAA,mBAAAe,0DAAA;YAAA,OAASnC,GAAA,CAAAzC,YAAA,EAAc;UAAA,EAAC;UACnElI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,eAA6B;UAOrBD,EAAA,CAAA+L,UAAA,2BAAAgB,iEAAAd,MAAA;YAAA,OAAAtB,GAAA,CAAA3H,WAAA,GAAAiJ,MAAA;UAAA,EAAyB,qBAAAe,2DAAAf,MAAA;YAAA,OACdtB,GAAA,CAAA3D,UAAA,CAAAiF,MAAA,CAAkB;UAAA,EADJ;UAJ3BjM,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAMC;UAFCD,EAAA,CAAA+L,UAAA,mBAAAkB,0DAAA;YAAA,OAAStC,GAAA,CAAA/D,WAAA,EAAa;UAAA,EAAC;UAGvB5G,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAG3BD,EAAA,CAAA4B,UAAA,KAAAsL,uCAAA,kBAUM;UAGNlN,EAAA,CAAA4B,UAAA,KAAAuL,uCAAA,kBAUM;UAGNnN,EAAA,CAAA4B,UAAA,KAAAwL,uCAAA,kBAYM;UAGNpN,EAAA,CAAA4B,UAAA,KAAAyL,uCAAA,kBAEM;UACRrN,EAAA,CAAAG,YAAA,EAAM;;;;;UA5OQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAjI,WAAA,CAAuB;UAIC1C,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAAoK,GAAA,CAAAlI,IAAA,CAAAkI,GAAA,CAAAjI,WAAA,mBAAAiI,GAAA,CAAAlI,IAAA,CAAAkI,GAAA,CAAAjI,WAAA,EAAAjC,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAAlI,IAAA,CAAS;UA4BJzC,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAA7J,iBAAA,CAAAC,MAAA,KAAkC;UAM/Df,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAA7J,iBAAA,CAA6B;UAUzBd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAuK,GAAA,CAAAtH,kBAAA,CAAkC;UAUlCrD,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,aAAAuK,GAAA,CAAAxB,aAAA,GAA4B;UAQ5BnJ,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAI,UAAA,aAAAuK,GAAA,CAAA7J,iBAAA,CAAAC,MAAA,OAA2C;UAQxBf,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsN,WAAA,SAAA3C,GAAA,CAAAnC,iBAAA,EAA4B;UAezCxI,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAA9H,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAA/H,SAAA,CAAY;UAgB7C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAmN,GAAA,CAA6B,YAAA5C,GAAA,CAAA7H,SAAA;UAKE9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAmN,GAAA,CAAmB;UAclDvN,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAoN,GAAA,CAA2B,YAAA7C,GAAA,CAAA5H,OAAA;UAKI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAoN,GAAA,CAAiB;UAwBrBxN,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAAyN,WAAA,UAAA9C,GAAA,CAAA7D,kBAAA,GAAoC;UACjE9G,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAA+J,GAAA,CAAA7D,kBAAA,kEACF;UASM9G,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAA3H,WAAA,CAAyB,cAAA2H,GAAA,CAAA7D,kBAAA;UAU3B9G,EAAA,CAAAM,SAAA,GAAyD;UAAzDN,EAAA,CAAAI,UAAA,cAAAuK,GAAA,CAAA3H,WAAA,CAAA6D,IAAA,OAAA8D,GAAA,CAAA7D,kBAAA,GAAyD;UAanC9G,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAAxH,SAAA,CAAe;UAajBnD,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,KAAAwH,GAAA,CAAA1H,aAAA,CAAkC;UAa5BjD,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,KAAAwH,GAAA,CAAA1H,aAAA,kBAAA0H,GAAA,CAAA1H,aAAA,CAAAiH,OAAA,EAA0C;UAe3ClK,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,IAAAwH,GAAA,CAAA1H,aAAA,CAAiC;;;qBDtNpErE,YAAY,EAAA8O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,SAAA,EACZhP,aAAa,EACbC,eAAe,EAAAgP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjP,aAAa,EAAAkP,EAAA,CAAAC,OAAA,EACblP,kBAAkB,EAAAmP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBpP,eAAe,EAAAqP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACfxP,cAAc,EAAAyP,GAAA,CAAAC,QAAA,EACdzP,mBAAmB,EAAA0P,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB5P,mBAAmB,EACnBC,WAAW,EAAA4P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACX9P,mBAAmB,EAAA2P,GAAA,CAAAI,oBAAA,EACnBtP,wBAAwB,EAAAuP,GAAA,CAAAC,wBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKftN,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}