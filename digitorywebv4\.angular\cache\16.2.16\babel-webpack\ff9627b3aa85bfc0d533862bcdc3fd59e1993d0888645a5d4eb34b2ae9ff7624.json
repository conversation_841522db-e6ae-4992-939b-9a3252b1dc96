{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { EmptyStateComponent } from '../empty-state/empty-state.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_2_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Start\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Resume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startConversation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"mat-icon\", 25);\n    i0.ɵɵtext(3, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Restaurant Onboarding Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"I'll help you collect information about your restaurant outlets, cuisines, and more.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 26)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ChatBotComponent_div_2_span_11_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(12, ChatBotComponent_div_2_span_12_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n  }\n}\nfunction ChatBotComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r9.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1,\n    \"number-container\": a2,\n    \"short-answer-container\": a3,\n    \"system-message-container\": a4\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"number-message\": a0,\n    \"short-answer-message\": a1,\n    \"system-message\": a2\n  };\n};\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"div\", 30);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_14_div_5_Template, 3, 4, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, message_r9.sender === \"user\", message_r9.sender === \"bot\", message_r9.messageType === \"number\", message_r9.messageType === \"short-answer\", message_r9.messageType === \"system-message\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c1, message_r9.messageType === \"number\", message_r9.messageType === \"short-answer\", message_r9.messageType === \"system-message\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 4, message_r9.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r9.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 36);\n    i0.ɵɵtext(7, \"DIGI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-empty-state\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r20.getCuisineItemCount(cuisine_r18), \") \");\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_15_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_2_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵtemplate(3, ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_3_Template, 2, 0, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r18 = ctx.$implicit;\n    const last_r19 = ctx.last;\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", cuisine_r18, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.getCuisineItemCount(cuisine_r18) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r19);\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ng_container_35_span_15_ng_container_1_Template, 4, 3, \"ng-container\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.restaurantData.commonCuisinesAcrossOutlets);\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.restaurantData.signatureElements.signatureDishes.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r28 = ctx.$implicit;\n    const last_r29 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", area_r28, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r29);\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template, 3, 2, \"span\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const outlet_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", outlet_r23.outletWorkAreas);\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"h3\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"span\", 40);\n    i0.ɵɵtext(7, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 41);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"span\", 40);\n    i0.ɵɵtext(12, \"Work Areas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 41);\n    i0.ɵɵtemplate(14, ChatBotComponent_ng_container_35_div_33_span_14_Template, 2, 1, \"span\", 22);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_35_div_33_span_15_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const outlet_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" Outlet \", i_r24 + 1, \": \", outlet_r23.outletName, \" \", outlet_r23.outletAbbreviation ? \"(\" + outlet_r23.outletAbbreviation + \")\" : \"\", \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(outlet_r23.outletAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", outlet_r23.outletWorkAreas && outlet_r23.outletWorkAreas.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !outlet_r23.outletWorkAreas || outlet_r23.outletWorkAreas.length === 0);\n  }\n}\nfunction ChatBotComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"h3\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"span\", 40);\n    i0.ɵɵtext(8, \"Total Outlets:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 39)(12, \"span\", 40);\n    i0.ɵɵtext(13, \"Cuisines:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 41);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_35_span_15_Template, 2, 1, \"span\", 22);\n    i0.ɵɵtemplate(16, ChatBotComponent_ng_container_35_span_16_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"span\", 40);\n    i0.ɵɵtext(19, \"Signature Dishes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 41);\n    i0.ɵɵtemplate(21, ChatBotComponent_ng_container_35_span_21_Template, 2, 1, \"span\", 22);\n    i0.ɵɵtemplate(22, ChatBotComponent_ng_container_35_span_22_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 39)(24, \"span\", 40);\n    i0.ɵɵtext(25, \"Alcohol Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 41);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 39)(29, \"span\", 40);\n    i0.ɵɵtext(30, \"Tobacco Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 41);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ChatBotComponent_ng_container_35_div_33_Template, 16, 6, \"div\", 42);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.restaurantData.totalOutlets);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.commonCuisinesAcrossOutlets && ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.commonCuisinesAcrossOutlets || ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.signatureElements && ctx_r4.restaurantData.signatureElements.signatureDishes && ctx_r4.restaurantData.signatureElements.signatureDishes.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.signatureElements || !ctx_r4.restaurantData.signatureElements.signatureDishes || ctx_r4.restaurantData.signatureElements.signatureDishes.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.beverageInfo == null ? null : ctx_r4.restaurantData.beverageInfo.alcoholService) || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.tobaccoInfo == null ? null : ctx_r4.restaurantData.tobaccoInfo.tobaccoService) || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantData.outletDetails);\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    this.restaurantData = null;\n    this.conversationStarted = false;\n    this.isRefreshing = false;\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    this.dataUpdateSubscription = null;\n    this.loadingHistory = false;\n  }\n  ngOnChanges(changes) {\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      if (newTenantId && prevTenantId !== newTenantId) {\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    this.messages = [];\n    if (this.tenantId) {\n      this.loadingHistory = false;\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      this.loadConversationHistory();\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else if (this.conversationStarted) {\n          this.initiateConversation();\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000);\n    } else {\n      this.messages = [];\n    }\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      if (message.sender === 'system') {\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        return;\n      }\n      if (message.sender === 'bot') {\n        if (!message.text.trim()) {\n          return;\n        }\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          if (message.text !== 'DIGI is thinking...') {\n            this.messages[existingMessageIndex] = message;\n          }\n        } else {\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          const thinkingIndex = this.messages.findIndex(m => m.sender === 'bot' && (m.text === 'DIGI is thinking...' || m.text.includes('thinking')) && m.id === message.id);\n          if (thinkingIndex !== -1) {\n            this.messages[thinkingIndex] = message;\n          } else if (!duplicateMessage && !message.text.includes('thinking')) {\n            this.messages.push(message);\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n        if (!message.text.includes('blinking-cursor')) {\n          this.isWaitingForResponse = false;\n        }\n        if (message.text.includes('thinking')) {\n          this.isWaitingForResponse = true;\n          this.cd.detectChanges();\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      } else if (message.sender === 'user') {\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000);\n        if (!isDuplicate) {\n          this.messages.push(message);\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {}\n      }\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    });\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(data => {\n      if (data && data.type === 'restaurant_data') {\n        if (data.data) {\n          this.restaurantData = data.data;\n        } else {\n          this.restaurantData = null;\n        }\n        setTimeout(() => {\n          this.cd.detectChanges();\n        }, 0);\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    this.sseService.disconnect();\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    let messageType = 'text';\n    if (/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'number';\n    } else if (messageToSend.trim().length <= 3 && !/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'short-answer';\n    }\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date(),\n      messageType: messageType\n    };\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    setTimeout(() => {\n      this.isWaitingForResponse = true;\n      this.cd.detectChanges();\n    }, 0);\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: _error => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  scrollToBottom() {\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  trackById(_index, message) {\n    return message.id;\n  }\n  loadRestaurantData() {\n    if (!this.tenantId) {\n      console.log('Not loading restaurant data: no tenantId');\n      return;\n    }\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data loaded:', data);\n        if (data) {\n          this.restaurantData = data;\n        } else {\n          console.warn('No restaurant data available');\n          this.restaurantData = null;\n        }\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading restaurant data:', error);\n        this.restaurantData = null;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  loadConversationHistory() {\n    if (!this.tenantId || this.loadingHistory) {\n      return;\n    }\n    this.loadingHistory = true;\n    this.isConnecting = true;\n    this.messages = [];\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        if (messages && messages.length > 0) {\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          this.messages = messages;\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          this.messages = [];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n        this.loadRestaurantData();\n      },\n      error: _error => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.messages = [];\n        this.cd.detectChanges();\n        this.loadRestaurantData();\n      }\n    });\n  }\n  clearConversationHistory() {\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: _error => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  startConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    this.conversationStarted = true;\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    this.initiateConversation();\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n  initiateConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {},\n      error: _error => {\n        console.error('Error initiating conversation');\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  refreshRestaurantData() {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: _error => {\n        console.error('Error refreshing restaurant data');\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  getCuisineItemCount(cuisine) {\n    if (!this.restaurantData || !this.restaurantData.cuisineMenuCounts) {\n      return 0;\n    }\n    const cuisineCount = this.restaurantData.cuisineMenuCounts.find(count => count.cuisine.toLowerCase() === cuisine.toLowerCase());\n    return cuisineCount ? cuisineCount.menuItemCount : 0;\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 12,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [\"class\", \"chat-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-data-panel\"], [1, \"panel-header\"], [1, \"header-left\"], [1, \"header-right\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh restaurant data\", 1, \"action-button\", \"refresh-button\", 3, \"disabled\", \"click\"], [1, \"panel-content\"], [4, \"ngIf\"], [1, \"chat-overlay\", 3, \"click\"], [1, \"overlay-content\"], [1, \"overlay-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-button\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\", 3, \"ngClass\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-text\"], [\"icon\", \"restaurant\", \"title\", \"No Restaurant Data Yet\", \"message\", \"As you provide information about your restaurant through the chat, it will appear here.\", \"customClass\", \"restaurant-empty-state\"], [1, \"data-section\", \"summary-section\"], [1, \"data-item\"], [1, \"label\"], [1, \"value\"], [\"class\", \"data-section outlet-section\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"data-section\", \"outlet-section\"], [\"class\", \"work-area-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"work-area-tag\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ChatBotComponent_div_2_Template, 13, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Time to cook up the details of your restaurant!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_10_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 6, 16, \"div\", 10);\n          i0.ɵɵtemplate(15, ChatBotComponent_div_15_Template, 8, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"mat-form-field\", 13)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_18_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_19_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"div\", 18)(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"restaurant_menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h2\");\n          i0.ɵɵtext(28, \"Restaurant Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_30_listener() {\n            return ctx.refreshRestaurantData();\n          });\n          i0.ɵɵelementStart(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"div\", 21);\n          i0.ɵɵtemplate(34, ChatBotComponent_div_34_Template, 2, 0, \"div\", 22);\n          i0.ɵɵtemplate(35, ChatBotComponent_ng_container_35_Template, 34, 8, \"ng-container\", 22);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.conversationStarted);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"rotating\", ctx.isRefreshing);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantData);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MatDialogModule, MarkdownPipe, EmptyStateComponent],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  height: 100%;\\n  gap: 20px;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 0.6; \\n\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n  position: relative; \\n\\n}\\n\\n.restaurant-data-panel[_ngcontent-%COMP%] {\\n  flex: 0.4; \\n\\n  width: auto; \\n\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 80%;\\n}\\n\\n.overlay-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f57c00; \\n\\n  margin-bottom: 1rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-size: 1rem;\\n  background-color: #f57c00; \\n\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  transition: background-color 0.3s;\\n}\\n\\n.start-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800; \\n\\n}\\n\\n.start-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: auto; \\n\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n  flex-wrap: wrap; \\n\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n  display: flex;\\n  justify-content: flex-end; \\n\\n  flex-direction: row; \\n\\n  max-width: 85%; \\n\\n  width: auto; \\n\\n  flex-wrap: wrap; \\n\\n}\\n\\n\\n\\n.user-message.number-container[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  width: auto !important;\\n  min-width: 40px !important;\\n  padding: 8px 16px !important;\\n  border-radius: 16px !important;\\n  background-color: #e8e8e8;\\n  text-align: center;\\n  display: inline-flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n\\n\\n.user-message.short-answer-container[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  width: auto !important;\\n  min-width: 40px !important;\\n  padding: 8px 16px !important;\\n  border-radius: 16px !important;\\n  background-color: #e8e8e8;\\n  text-align: center;\\n  display: inline-flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 10px 14px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  width: auto; \\n\\n  word-wrap: break-word;\\n  word-break: break-word; \\n\\n  overflow-wrap: break-word; \\n\\n  line-height: 1.5;\\n  display: inline-block; \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap; \\n\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n  width: 100%; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.6rem;\\n  margin-top: 2px;\\n  margin-left: 0 !important;\\n  text-align: center;\\n  opacity: 0.7;\\n  align-self: center;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  flex: 1;\\n  width: 100%; \\n\\n  max-width: 100%; \\n\\n  display: inline-block; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 16px;\\n  text-align: center;\\n  padding: 0 !important;\\n  margin: 0 !important;\\n  flex: none;\\n  width: auto;\\n  display: inline-block;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.8em; \\n\\n  font-size: 16px; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.8em; \\n\\n  margin-bottom: 0.8em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5em; \\n\\n  font-size: 16px; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n  white-space: pre-wrap; \\n\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  max-width: 100%;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n  white-space: pre-wrap; \\n\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  max-width: 100%;\\n  width: 100%;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  line-height: 1.5;\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  white-space: normal;\\n  width: 100%;\\n  max-width: 100%;\\n  display: inline-block; \\n\\n}\\n\\n\\n\\n.number-message[_ngcontent-%COMP%] {\\n  min-width: 40px !important;\\n  max-width: none !important;\\n  min-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 8px 16px !important;\\n  border-radius: 18px !important;\\n}\\n\\n\\n\\n.short-answer-message[_ngcontent-%COMP%] {\\n  min-width: 40px !important;\\n  max-width: none !important;\\n  min-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 8px 16px !important;\\n  border-radius: 18px !important;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.restaurant-empty-state[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background-color: white;\\n  margin-bottom: 20px;\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container {\\n  height: 100%;\\n  min-height: 250px;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container .icon-container {\\n  width: 80px;\\n  height: 80px;\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container .empty-state-icon {\\n  font-size: 40px;\\n  height: 40px;\\n  width: 40px;\\n  color: #f57c00; \\n\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container .empty-state-title {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n}\\n.restaurant-empty-state[_ngcontent-%COMP%]     .empty-state-container .empty-state-message {\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n  max-width: 240px;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  transition: all 0.3s ease;\\n  margin-left: 4px;\\n}\\n.panel-header[_ngcontent-%COMP%]   .refresh-button[_ngcontent-%COMP%]:hover {\\n  color: #2196f3;\\n}\\n.panel-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\\n  color: #4caf50;\\n}\\n.panel-header[_ngcontent-%COMP%]   .delete-button[_ngcontent-%COMP%]:hover {\\n  color: #f44336;\\n}\\n.panel-header[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1.5s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n  color: #f57c00; \\n\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  overflow-y: auto;\\n  flex: 1;\\n  background-color: #f5f5f5;\\n  gap: 20px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n}\\n.data-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.data-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n}\\n\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n  border-bottom: 1px solid #f0f0f0;\\n  padding-bottom: 12px;\\n}\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #f57c00;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  background-color: #fff9f0;\\n}\\n\\n.outlet-section[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n}\\n\\n.work-area-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  padding: 6px 0;\\n  border-bottom: 1px dashed #f0f0f0;\\n}\\n.data-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #444;\\n  min-width: 140px;\\n  padding-right: 12px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #333;\\n  flex: 1;\\n  word-break: break-word;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: auto;\\n  justify-content: flex-start;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 3px;\\n  background-color: #57705d;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  font-size: 13px;\\n  color: #555;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-4px) scale(1.2);\\n    opacity: 0.9;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MatDialogModule", "MarkdownPipe", "EmptyStateComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatBotComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "startConversation", "ɵɵtemplate", "ChatBotComponent_div_2_span_11_Template", "ChatBotComponent_div_2_span_12_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "messages", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r9", "timestamp", "ɵɵelement", "ChatBotComponent_div_14_div_5_Template", "ɵɵpureFunction5", "_c0", "sender", "messageType", "ɵɵpureFunction3", "_c1", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵtextInterpolate1", "ctx_r20", "getCuisineItemCount", "cuisine_r18", "ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_2_Template", "ChatBotComponent_ng_container_35_span_15_ng_container_1_ng_container_3_Template", "ctx_r17", "last_r19", "ChatBotComponent_ng_container_35_span_15_ng_container_1_Template", "ctx_r12", "restaurantData", "commonCuisinesAcrossOutlets", "ctx_r14", "signatureElements", "signatureDishes", "join", "ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template", "area_r28", "last_r29", "ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template", "outlet_r23", "outletWorkAreas", "ChatBotComponent_ng_container_35_div_33_span_14_Template", "ChatBotComponent_ng_container_35_div_33_span_15_Template", "ɵɵtextInterpolate3", "i_r24", "outletName", "outletAbbreviation", "outletAddress", "ChatBotComponent_ng_container_35_span_15_Template", "ChatBotComponent_ng_container_35_span_16_Template", "ChatBotComponent_ng_container_35_span_21_Template", "ChatBotComponent_ng_container_35_span_22_Template", "ChatBotComponent_ng_container_35_div_33_Template", "ctx_r4", "totalOutlets", "beverageInfo", "alcoholService", "tobaccoInfo", "tobaccoService", "outletDetails", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "currentMessage", "isConnecting", "isWaitingForResponse", "conversationStarted", "isRefreshing", "messageSubscription", "connectionSubscription", "dataUpdateSubscription", "loadingHistory", "ngOnChanges", "changes", "newTenantId", "currentValue", "prevTenantId", "previousValue", "loadConversationHistory", "ngOnInit", "conversationStarted<PERSON><PERSON>", "localStorage", "getItem", "setTimeout", "setItem", "initiateConversation", "detectChanges", "scrollToBottom", "messages$", "subscribe", "message", "id", "startsWith", "trim", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "thinkingIndex", "push", "sort", "a", "b", "getTime", "isDuplicate", "some", "Math", "abs", "dataUpdates$", "data", "type", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "open", "duration", "messageToSend", "test", "userMessage", "generateId", "Date", "next", "error", "_error", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "loadRestaurantData", "console", "log", "fetchRestaurantData", "warn", "clearConversationHistory", "removeItem", "refreshRestaurantData", "Object", "keys", "panelClass", "onAction", "cuisine", "cuisineMenuCounts", "cuisineCount", "count", "toLowerCase", "menuItemCount", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_div_2_Template", "ChatBotComponent_Template_button_click_10_listener", "ChatBotComponent_div_14_Template", "ChatBotComponent_div_15_Template", "ChatBotComponent_Template_input_ngModelChange_18_listener", "$event", "ChatBotComponent_Template_input_keydown_18_listener", "ChatBotComponent_Template_button_click_19_listener", "ChatBotComponent_Template_button_click_30_listener", "ChatBotComponent_div_34_Template", "ChatBotComponent_ng_container_35_Template", "ɵɵclassProp", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\nimport { EmptyStateComponent } from '../empty-state/empty-state.component';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    MatDialogModule,\n    SafeHtmlPipe,\n    MarkdownPipe,\n    ConfirmDialogComponent,\n    EmptyStateComponent\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n  restaurantData: any = null;\n  conversationStarted: boolean = false;\n  isRefreshing: boolean = false;\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n  private dataUpdateSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar,\n  ) { }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      if (newTenantId && prevTenantId !== newTenantId) {\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  private loadingHistory = false;\n\n  ngOnInit(): void {\n    this.messages = [];\n    if (this.tenantId) {\n      this.loadingHistory = false;\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      this.loadConversationHistory();\n\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else if (this.conversationStarted) {\n          this.initiateConversation();\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000);\n    } else {\n      this.messages = [];\n    }\n\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        if (message.sender === 'system') {\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          return;\n        }\n\n        if (message.sender === 'bot') {\n          if (!message.text.trim()) {\n            return;\n          }\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            if (message.text !== 'DIGI is thinking...') {\n              this.messages[existingMessageIndex] = message;\n            }\n          } else {\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            const thinkingIndex = this.messages.findIndex(m =>\n              m.sender === 'bot' && (m.text === 'DIGI is thinking...' || m.text.includes('thinking')) && m.id === message.id\n            );\n\n            if (thinkingIndex !== -1) {\n              this.messages[thinkingIndex] = message;\n            } else if (!duplicateMessage && !message.text.includes('thinking')) {\n              this.messages.push(message);\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n\n          if (!message.text.includes('blinking-cursor')) {\n            this.isWaitingForResponse = false;\n          }\n\n          if (message.text.includes('thinking')) {\n            this.isWaitingForResponse = true;\n            this.cd.detectChanges();\n          }\n\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        } else if (message.sender === 'user') {\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000\n          );\n\n          if (!isDuplicate) {\n            this.messages.push(message);\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n          }\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }\n    );\n\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(\n      (data: any) => {\n        if (data && data.type === 'restaurant_data') {\n          if (data.data) {\n            this.restaurantData = data.data;\n          } else {\n            this.restaurantData = null;\n          }\n          setTimeout(() => {\n            this.cd.detectChanges();\n          }, 0);\n        }\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    this.sseService.disconnect();\n  }\n\n\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    let messageType: 'text' | 'number' | 'short-answer' | 'system-message' = 'text';\n\n    if (/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'number';\n    }\n    else if (messageToSend.trim().length <= 3 && !/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'short-answer';\n    }\n\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date(),\n      messageType: messageType\n    };\n\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    setTimeout(() => {\n      this.isWaitingForResponse = true;\n      this.cd.detectChanges();\n    }, 0);\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (_error) => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  private scrollToBottom(): void {\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n\n  loadRestaurantData(): void {\n    if (!this.tenantId) {\n      console.log('Not loading restaurant data: no tenantId');\n      return;\n    }\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data loaded:', data);\n        if (data) {\n          this.restaurantData = data;\n        } else {\n          console.warn('No restaurant data available');\n          this.restaurantData = null;\n        }\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error loading restaurant data:', error);\n        this.restaurantData = null;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.loadingHistory) {\n      return;\n    }\n\n    this.loadingHistory = true;\n    this.isConnecting = true;\n    this.messages = [];\n\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        if (messages && messages.length > 0) {\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          this.messages = messages;\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          this.messages = [];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n        this.loadRestaurantData();\n      },\n      error: (_error) => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.messages = [];\n        this.cd.detectChanges();\n        this.loadRestaurantData();\n      }\n    });\n  }\n\n  clearConversationHistory(): void {\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (_error) => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n\n  startConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n    this.conversationStarted = true;\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    this.initiateConversation();\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n\n  private initiateConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n      },\n      error: (_error) => {\n        console.error('Error initiating conversation');\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n\n\n  refreshRestaurantData(): void {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: (_error) => {\n        console.error('Error refreshing restaurant data');\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  getCuisineItemCount(cuisine: string): number {\n    if (!this.restaurantData || !this.restaurantData.cuisineMenuCounts) {\n      return 0;\n    }\n\n    const cuisineCount = this.restaurantData.cuisineMenuCounts.find(\n      (count: any) => count.cuisine.toLowerCase() === cuisine.toLowerCase()\n    );\n\n    return cuisineCount ? cuisineCount.menuItemCount : 0;\n  }\n}\n", "<div class=\"chat-layout\">\n  <div class=\"chat-container\">\n  <!-- Start/Resume Overlay -->\n  <div class=\"chat-overlay\" *ngIf=\"!conversationStarted\" (click)=\"startConversation()\">\n    <div class=\"overlay-content\">\n      <mat-icon class=\"overlay-icon\">restaurant</mat-icon>\n      <h2>Restaurant Onboarding Assistant</h2>\n      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>\n      <button mat-raised-button color=\"primary\" class=\"start-button\">\n        <mat-icon>play_arrow</mat-icon>\n        <span *ngIf=\"messages.length === 0\">Click to Start</span>\n        <span *ngIf=\"messages.length > 0\">Click to Resume</span>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n      <span class=\"assistant-title\">Time to cook up the details of your restaurant!</span>\n    </div>\n    <div class=\"chat-actions\">\n      <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n        <mat-icon>clear</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{\n        'user-message': message.sender === 'user',\n        'bot-message': message.sender === 'bot',\n        'number-container': message.messageType === 'number',\n        'short-answer-container': message.messageType === 'short-answer',\n        'system-message-container': message.messageType === 'system-message'\n      }\">\n      <div class=\"message-content\" [ngClass]=\"{\n        'number-message': message.messageType === 'number',\n        'short-answer-message': message.messageType === 'short-answer',\n        'system-message': message.messageType === 'system-message'\n      }\">\n        <div class=\"message-wrapper\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n          <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Improved loading indicator when waiting for a response -->\n    <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n      <div class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <div class=\"typing-text\">DIGI is thinking...</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"isConnecting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n</div>\n\n  <!-- Restaurant Data Panel -->\n  <div class=\"restaurant-data-panel\">\n    <div class=\"panel-header\">\n      <div class=\"header-left\">\n        <mat-icon>restaurant_menu</mat-icon>\n        <h2>Restaurant Information</h2>\n      </div>\n      <div class=\"header-right\">\n        <button mat-icon-button class=\"action-button refresh-button\" (click)=\"refreshRestaurantData()\" matTooltip=\"Refresh restaurant data\" [disabled]=\"isRefreshing\">\n          <mat-icon [class.rotating]=\"isRefreshing\">refresh</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"panel-content\">\n      <!-- No Data Message -->\n      <div *ngIf=\"!restaurantData\">\n        <app-empty-state\n          icon=\"restaurant\"\n          title=\"No Restaurant Data Yet\"\n          message=\"As you provide information about your restaurant through the chat, it will appear here.\"\n          customClass=\"restaurant-empty-state\"\n        ></app-empty-state>\n      </div>\n\n      <!-- Restaurant Data (shown when available) -->\n      <ng-container *ngIf=\"restaurantData\">\n        <!-- Summary Section -->\n        <div class=\"data-section summary-section\">\n          <h3><mat-icon>info</mat-icon> Restaurant Summary</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Total Outlets:</span>\n            <span class=\"value\">{{restaurantData.totalOutlets}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Cuisines:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.commonCuisinesAcrossOutlets && restaurantData.commonCuisinesAcrossOutlets.length > 0\">\n                <ng-container *ngFor=\"let cuisine of restaurantData.commonCuisinesAcrossOutlets; let last = last\">\n                  {{cuisine}}\n                  <ng-container *ngIf=\"getCuisineItemCount(cuisine) > 0\">\n                    ({{getCuisineItemCount(cuisine)}})\n                  </ng-container>\n                  <ng-container *ngIf=\"!last\">, </ng-container>\n                </ng-container>\n              </span>\n              <span *ngIf=\"!restaurantData.commonCuisinesAcrossOutlets || restaurantData.commonCuisinesAcrossOutlets.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Signature Dishes:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.signatureElements && restaurantData.signatureElements.signatureDishes && restaurantData.signatureElements.signatureDishes.length > 0\">\n                {{restaurantData.signatureElements.signatureDishes.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.signatureElements || !restaurantData.signatureElements.signatureDishes || restaurantData.signatureElements.signatureDishes.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Alcohol Service:</span>\n            <span class=\"value\">{{restaurantData.beverageInfo?.alcoholService || 'Not specified'}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Tobacco Service:</span>\n            <span class=\"value\">{{restaurantData.tobaccoInfo?.tobaccoService || 'Not specified'}}</span>\n          </div>\n        </div>\n\n        <!-- Outlets Section -->\n        <div class=\"data-section outlet-section\" *ngFor=\"let outlet of restaurantData.outletDetails; let i = index\">\n          <h3><mat-icon>store</mat-icon> Outlet {{i+1}}: {{outlet.outletName}} {{outlet.outletAbbreviation ? '(' + outlet.outletAbbreviation + ')' : ''}}</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Address:</span>\n            <span class=\"value\">{{outlet.outletAddress}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Work Areas:</span>\n            <span class=\"value\">\n              <span *ngIf=\"outlet.outletWorkAreas && outlet.outletWorkAreas.length > 0\">\n                <span *ngFor=\"let area of outlet.outletWorkAreas; let last = last\" class=\"work-area-tag\">\n                  {{area}}<span *ngIf=\"!last\">, </span>\n                </span>\n              </span>\n              <span *ngIf=\"!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,eAAe,QAAQ,0BAA0B;AAG1D,SAASC,YAAY,QAAQ,2BAA2B;AAIxD,SAASC,mBAAmB,QAAQ,sCAAsC;;;;;;;;;;;;;ICRlEC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAR9DH,EAAA,CAAAC,cAAA,cAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,qDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAClFX,EAAA,CAAAC,cAAA,cAA6B;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2FAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3FH,EAAA,CAAAC,cAAA,iBAA+D;IACnDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAY,UAAA,KAAAC,uCAAA,mBAAyD;IACzDb,EAAA,CAAAY,UAAA,KAAAE,uCAAA,mBAAwD;IAC1Dd,EAAA,CAAAG,YAAA,EAAS;;;;IAFAH,EAAA,CAAAe,SAAA,IAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAA2B;IAC3BnB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAAyB;;;;;IAgC9BnB,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAC,UAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;;;;;;;;;;;IAdnHvB,EAAA,CAAAC,cAAA,cAMK;IAOCD,EAAA,CAAAwB,SAAA,cAAsE;;IACtExB,EAAA,CAAAY,UAAA,IAAAa,sCAAA,kBAAmH;IACrHzB,EAAA,CAAAG,YAAA,EAAM;;;;IAf0EH,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAM,MAAA,aAAAN,UAAA,CAAAM,MAAA,YAAAN,UAAA,CAAAO,WAAA,eAAAP,UAAA,CAAAO,WAAA,qBAAAP,UAAA,CAAAO,WAAA,uBAMhF;IAC2B7B,EAAA,CAAAe,SAAA,GAI3B;IAJ2Bf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA8B,eAAA,KAAAC,GAAA,EAAAT,UAAA,CAAAO,WAAA,eAAAP,UAAA,CAAAO,WAAA,qBAAAP,UAAA,CAAAO,WAAA,uBAI3B;IAE4B7B,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,cAAAhB,EAAA,CAAAgC,WAAA,OAAAV,UAAA,CAAAW,IAAA,GAAAjC,EAAA,CAAAkC,cAAA,CAAqC;IAC/BlC,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,SAAAM,UAAA,CAAAM,MAAA,cAAiC;;;;;IAMvE5B,EAAA,CAAAC,cAAA,cAAwE;IAGlED,EAAA,CAAAwB,SAAA,WAAa;IAGfxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAmCpDH,EAAA,CAAAC,cAAA,UAA6B;IAC3BD,EAAA,CAAAwB,SAAA,0BAKmB;IACrBxB,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBMH,EAAA,CAAAmC,uBAAA,GAAuD;IACrDnC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAoC,qBAAA,EAAe;;;;;IADbpC,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqC,kBAAA,OAAAC,OAAA,CAAAC,mBAAA,CAAAC,WAAA,QACF;;;;;IACAxC,EAAA,CAAAmC,uBAAA,GAA4B;IAAAnC,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAoC,qBAAA,EAAe;;;;;IAL/CpC,EAAA,CAAAmC,uBAAA,GAAkG;IAChGnC,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAY,UAAA,IAAA6B,+EAAA,2BAEe;IACfzC,EAAA,CAAAY,UAAA,IAAA8B,+EAAA,2BAA6C;IAC/C1C,EAAA,CAAAoC,qBAAA,EAAe;;;;;;IALbpC,EAAA,CAAAe,SAAA,GACA;IADAf,EAAA,CAAAqC,kBAAA,MAAAG,WAAA,MACA;IAAexC,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAAgB,UAAA,SAAA2B,OAAA,CAAAJ,mBAAA,CAAAC,WAAA,MAAsC;IAGtCxC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAA4B,QAAA,CAAW;;;;;IAN9B5C,EAAA,CAAAC,cAAA,WAAkH;IAChHD,EAAA,CAAAY,UAAA,IAAAiC,gEAAA,2BAMe;IACjB7C,EAAA,CAAAG,YAAA,EAAO;;;;IAP6BH,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAgB,UAAA,YAAA8B,OAAA,CAAAC,cAAA,CAAAC,2BAAA,CAA+C;;;;;IAQnFhD,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMPH,EAAA,CAAAC,cAAA,WAAkK;IAChKD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqC,kBAAA,MAAAY,OAAA,CAAAF,cAAA,CAAAG,iBAAA,CAAAC,eAAA,CAAAC,IAAA,YACF;;;;;IACApD,EAAA,CAAAC,cAAA,WAAsK;IACpKD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAyBKH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADvCH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAY,UAAA,IAAAyC,sEAAA,mBAA6B;IACvCrD,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAe,SAAA,GAAQ;IAARf,EAAA,CAAAqC,kBAAA,MAAAiB,QAAA,KAAQ;IAAOtD,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAAuC,QAAA,CAAW;;;;;IAF9BvD,EAAA,CAAAC,cAAA,WAA0E;IACxED,EAAA,CAAAY,UAAA,IAAA4C,+DAAA,mBAEO;IACTxD,EAAA,CAAAG,YAAA,EAAO;;;;IAHkBH,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,YAAAyC,UAAA,CAAAC,eAAA,CAA2B;;;;;IAIpD1D,EAAA,CAAAC,cAAA,WAA6E;IAC3ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBbH,EAAA,CAAAC,cAAA,cAA4G;IAC5FD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,GAAgH;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpJH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAA+C,wDAAA,mBAIO;IACP3D,EAAA,CAAAY,UAAA,KAAAgD,wDAAA,mBAEO;IACT5D,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBsBH,EAAA,CAAAe,SAAA,GAAgH;IAAhHf,EAAA,CAAA6D,kBAAA,aAAAC,KAAA,YAAAL,UAAA,CAAAM,UAAA,OAAAN,UAAA,CAAAO,kBAAA,SAAAP,UAAA,CAAAO,kBAAA,gBAAgH;IAGzHhE,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAoB,iBAAA,CAAAqC,UAAA,CAAAQ,aAAA,CAAwB;IAKnCjE,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAgB,UAAA,SAAAyC,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAAvC,MAAA,KAAiE;IAKjEnB,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,UAAA,UAAAyC,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAAvC,MAAA,OAAoE;;;;;IA7DnFnB,EAAA,CAAAmC,uBAAA,GAAqC;IAEnCnC,EAAA,CAAAC,cAAA,cAA0C;IAC1BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAsD,iDAAA,mBAQO;IACPlE,EAAA,CAAAY,UAAA,KAAAuD,iDAAA,mBAEO;IACTnE,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAwD,iDAAA,mBAEO;IACPpE,EAAA,CAAAY,UAAA,KAAAyD,iDAAA,mBAEO;IACTrE,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/FH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKhGH,EAAA,CAAAY,UAAA,KAAA0D,gDAAA,mBAmBM;IACRtE,EAAA,CAAAoC,qBAAA,EAAe;;;;IA7DWpC,EAAA,CAAAe,SAAA,IAA+B;IAA/Bf,EAAA,CAAAoB,iBAAA,CAAAmD,MAAA,CAAAxB,cAAA,CAAAyB,YAAA,CAA+B;IAK1CxE,EAAA,CAAAe,SAAA,GAAyG;IAAzGf,EAAA,CAAAgB,UAAA,SAAAuD,MAAA,CAAAxB,cAAA,CAAAC,2BAAA,IAAAuB,MAAA,CAAAxB,cAAA,CAAAC,2BAAA,CAAA7B,MAAA,KAAyG;IASzGnB,EAAA,CAAAe,SAAA,GAA4G;IAA5Gf,EAAA,CAAAgB,UAAA,UAAAuD,MAAA,CAAAxB,cAAA,CAAAC,2BAAA,IAAAuB,MAAA,CAAAxB,cAAA,CAAAC,2BAAA,CAAA7B,MAAA,OAA4G;IAQ5GnB,EAAA,CAAAe,SAAA,GAAyJ;IAAzJf,EAAA,CAAAgB,UAAA,SAAAuD,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,IAAAqB,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,CAAAC,eAAA,CAAAhC,MAAA,KAAyJ;IAGzJnB,EAAA,CAAAe,SAAA,GAA6J;IAA7Jf,EAAA,CAAAgB,UAAA,UAAAuD,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,KAAAqB,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAxB,cAAA,CAAAG,iBAAA,CAAAC,eAAA,CAAAhC,MAAA,OAA6J;IAOlJnB,EAAA,CAAAe,SAAA,GAAkE;IAAlEf,EAAA,CAAAoB,iBAAA,EAAAmD,MAAA,CAAAxB,cAAA,CAAA0B,YAAA,kBAAAF,MAAA,CAAAxB,cAAA,CAAA0B,YAAA,CAAAC,cAAA,qBAAkE;IAIlE1E,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAoB,iBAAA,EAAAmD,MAAA,CAAAxB,cAAA,CAAA4B,WAAA,kBAAAJ,MAAA,CAAAxB,cAAA,CAAA4B,WAAA,CAAAC,cAAA,qBAAiE;IAK7B5E,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,YAAAuD,MAAA,CAAAxB,cAAA,CAAA8B,aAAA,CAAiC;;;ADhIrG,MAwBaC,gBAAgB;EAgB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAlBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAlE,QAAQ,GAAkB,EAAE;IAC5B,KAAAmE,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAxC,cAAc,GAAQ,IAAI;IAC1B,KAAAyC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,YAAY,GAAY,KAAK;IAErB,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,sBAAsB,GAAwB,IAAI;IAmBlD,KAAAC,cAAc,GAAG,KAAK;EAb1B;EAEJC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAGD,OAAO,CAAC,UAAU,CAAC,CAACE,YAAY;MACpD,MAAMC,YAAY,GAAGH,OAAO,CAAC,UAAU,CAAC,CAACI,aAAa;MACtD,IAAIH,WAAW,IAAIE,YAAY,KAAKF,WAAW,EAAE;QAC/C,IAAI,CAACH,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACO,uBAAuB,EAAE;;;EAGpC;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACnF,QAAQ,GAAG,EAAE;IAClB,IAAI,IAAI,CAACiE,QAAQ,EAAE;MACjB,IAAI,CAACU,cAAc,GAAG,KAAK;MAC3B,MAAMS,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;MACtE,IAAI,CAACK,mBAAmB,GAAGe,YAAY,CAACC,OAAO,CAACF,sBAAsB,CAAC,KAAK,MAAM;MAClF,IAAI,CAACF,uBAAuB,EAAE;MAE9BK,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACvF,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACqE,mBAAmB,GAAG,IAAI;UAC/Be,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM,IAAI,IAAI,CAACd,mBAAmB,EAAE;UACnC,IAAI,CAACmB,oBAAoB,EAAE;;QAE7B,IAAI,CAAC1B,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC,EAAE,IAAI,CAAC;KACT,MAAM;MACL,IAAI,CAAC3F,QAAQ,GAAG,EAAE;;IAGpB,IAAI,CAACwE,mBAAmB,GAAG,IAAI,CAACV,UAAU,CAAC8B,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB,IAAIA,OAAO,CAACpF,MAAM,KAAK,QAAQ,EAAE;QAC/B,IAAIoF,OAAO,CAACC,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAC3B,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;UACvB;;QAEF;;MAGF,IAAII,OAAO,CAACpF,MAAM,KAAK,KAAK,EAAE;QAC5B,IAAI,CAACoF,OAAO,CAAC/E,IAAI,CAACkF,IAAI,EAAE,EAAE;UACxB;;QAEF,MAAMC,oBAAoB,GAAG,IAAI,CAAClG,QAAQ,CAACmG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC;QAE9E,IAAIG,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B,IAAIJ,OAAO,CAAC/E,IAAI,KAAK,qBAAqB,EAAE;YAC1C,IAAI,CAACf,QAAQ,CAACkG,oBAAoB,CAAC,GAAGJ,OAAO;;SAEhD,MAAM;UACL,MAAMO,gBAAgB,GAAG,IAAI,CAACrG,QAAQ,CAACsG,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAAC1F,MAAM,KAAK,KAAK,IAAI0F,CAAC,CAACrF,IAAI,KAAK+E,OAAO,CAAC/E,IAAI,IAAI,CAACqF,CAAC,CAACrF,IAAI,CAACwF,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,MAAMC,aAAa,GAAG,IAAI,CAACxG,QAAQ,CAACmG,SAAS,CAACC,CAAC,IAC7CA,CAAC,CAAC1F,MAAM,KAAK,KAAK,KAAK0F,CAAC,CAACrF,IAAI,KAAK,qBAAqB,IAAIqF,CAAC,CAACrF,IAAI,CAACwF,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAIH,CAAC,CAACL,EAAE,KAAKD,OAAO,CAACC,EAAE,CAC/G;UAED,IAAIS,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB,IAAI,CAACxG,QAAQ,CAACwG,aAAa,CAAC,GAAGV,OAAO;WACvC,MAAM,IAAI,CAACO,gBAAgB,IAAI,CAACP,OAAO,CAAC/E,IAAI,CAACwF,QAAQ,CAAC,UAAU,CAAC,EAAE;YAClE,IAAI,CAACvG,QAAQ,CAACyG,IAAI,CAACX,OAAO,CAAC;YAC3B,IAAI,CAAC9F,QAAQ,CAAC0G,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtG,SAAS,CAACwG,OAAO,EAAE,GAAGD,CAAC,CAACvG,SAAS,CAACwG,OAAO,EAAE,CAAC;;;QAI/E,IAAI,CAACf,OAAO,CAAC/E,IAAI,CAACwF,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C,IAAI,CAAClC,oBAAoB,GAAG,KAAK;;QAGnC,IAAIyB,OAAO,CAAC/E,IAAI,CAACwF,QAAQ,CAAC,UAAU,CAAC,EAAE;UACrC,IAAI,CAAClC,oBAAoB,GAAG,IAAI;UAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;;QAGzB,IAAI,CAAC3B,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM,IAAIG,OAAO,CAACpF,MAAM,KAAK,MAAM,EAAE;QACpC,MAAMoG,WAAW,GAAG,IAAI,CAAC9G,QAAQ,CAAC+G,IAAI,CAACX,CAAC,IACtCA,CAAC,CAAC1F,MAAM,KAAK,MAAM,IACnB0F,CAAC,CAACrF,IAAI,KAAK+E,OAAO,CAAC/E,IAAI,IACvBiG,IAAI,CAACC,GAAG,CAACb,CAAC,CAAC/F,SAAS,CAACwG,OAAO,EAAE,GAAGf,OAAO,CAACzF,SAAS,CAACwG,OAAO,EAAE,CAAC,GAAG,IAAI,CACrE;QAED,IAAI,CAACC,WAAW,EAAE;UAChB,IAAI,CAAC9G,QAAQ,CAACyG,IAAI,CAACX,OAAO,CAAC;UAC3B,IAAI,CAAC9F,QAAQ,CAAC0G,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtG,SAAS,CAACwG,OAAO,EAAE,GAAGD,CAAC,CAACvG,SAAS,CAACwG,OAAO,EAAE,CAAC;SAC5E,MAAM,C;;MAGT,IAAI,CAAC9C,EAAE,CAAC2B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED,IAAI,CAACjB,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAACoD,YAAY,CAACrB,SAAS,CACjEsB,IAAS,IAAI;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;QAC3C,IAAID,IAAI,CAACA,IAAI,EAAE;UACb,IAAI,CAACtF,cAAc,GAAGsF,IAAI,CAACA,IAAI;SAChC,MAAM;UACL,IAAI,CAACtF,cAAc,GAAG,IAAI;;QAE5B0D,UAAU,CAAC,MAAK;UACd,IAAI,CAACxB,EAAE,CAAC2B,aAAa,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC;;IAET,CAAC,CACF;EACH;EAEA2B,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC7C,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC8C,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAAC7C,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAAC6C,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAAC5C,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAAC4C,WAAW,EAAE;;IAE3C,IAAI,CAACxD,UAAU,CAACyD,UAAU,EAAE;EAC9B;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACrD,cAAc,CAAC8B,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACyD,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,CAACtD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,MAAMsD,aAAa,GAAG,IAAI,CAACxD,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACJ,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI/E,WAAW,GAA0D,MAAM;IAE/E,IAAI,OAAO,CAACiH,IAAI,CAACD,aAAa,CAAC1B,IAAI,EAAE,CAAC,EAAE;MACtCtF,WAAW,GAAG,QAAQ;KACvB,MACI,IAAIgH,aAAa,CAAC1B,IAAI,EAAE,CAAChG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC2H,IAAI,CAACD,aAAa,CAAC1B,IAAI,EAAE,CAAC,EAAE;MAChFtF,WAAW,GAAG,cAAc;;IAG9B,MAAMkH,WAAW,GAAgB;MAC/B9B,EAAE,EAAE,IAAI,CAAC+B,UAAU,EAAE;MACrB/G,IAAI,EAAE4G,aAAa;MACnBjH,MAAM,EAAE,MAAM;MACdL,SAAS,EAAE,IAAI0H,IAAI,EAAE;MACrBpH,WAAW,EAAEA;KACd;IAED,MAAMmG,WAAW,GAAG,IAAI,CAAC9G,QAAQ,CAAC+G,IAAI,CAACX,CAAC,IACtCA,CAAC,CAAC1F,MAAM,KAAK,MAAM,IACnB0F,CAAC,CAACrF,IAAI,KAAK4G,aAAa,CACzB;IAED,IAAI,CAACb,WAAW,EAAE;MAChB,IAAI,CAAC9G,QAAQ,CAACyG,IAAI,CAACoB,WAAW,CAAC;;IAGjC,IAAI,CAACxD,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IAErBJ,UAAU,CAAC,MAAK;MACd,IAAI,CAAClB,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;IACzB,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAAC5B,UAAU,CAAC0D,WAAW,CAAC,IAAI,CAACvD,QAAQ,EAAE0D,aAAa,CAAC,CAAC9B,SAAS,CAAC;MAClEmC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC5D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;MACzB,CAAC;MACDuC,KAAK,EAAGC,MAAM,IAAI;QAChB,IAAI,CAAC9D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACL,QAAQ,CAACyD,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAC3D,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAGAyC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACf,WAAW,EAAE;;EAEtB;EAEQ7B,cAAcA,CAAA;IACpB6C,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAGQf,UAAUA,CAAA;IAChB,OAAOd,IAAI,CAAC8B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGhC,IAAI,CAAC8B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEAC,SAASA,CAACC,MAAc,EAAEpD,OAAoB;IAC5C,OAAOA,OAAO,CAACC,EAAE;EACnB;EAGAoD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAClF,QAAQ,EAAE;MAClBmF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAACvF,UAAU,CAACwF,mBAAmB,CAAC,IAAI,CAACrF,QAAQ,CAAC,CAAC4B,SAAS,CAAC;MAC3DmC,IAAI,EAAGb,IAAI,IAAI;QACbiC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAElC,IAAI,CAAC;QAC5C,IAAIA,IAAI,EAAE;UACR,IAAI,CAACtF,cAAc,GAAGsF,IAAI;SAC3B,MAAM;UACLiC,OAAO,CAACG,IAAI,CAAC,8BAA8B,CAAC;UAC5C,IAAI,CAAC1H,cAAc,GAAG,IAAI;;QAE5B,IAAI,CAACkC,EAAE,CAAC2B,aAAa,EAAE;MACzB,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACfmB,OAAO,CAACnB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACkC,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAGAR,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACjB,QAAQ,IAAI,IAAI,CAACU,cAAc,EAAE;MACzC;;IAGF,IAAI,CAACA,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACP,YAAY,GAAG,IAAI;IACxB,IAAI,CAACpE,QAAQ,GAAG,EAAE;IAElB,IAAI,CAAC8D,UAAU,CAACoB,uBAAuB,CAAC,IAAI,CAACjB,QAAQ,EAAE,KAAK,CAAC,CAAC4B,SAAS,CAAC;MACtEmC,IAAI,EAAGhI,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACnCD,QAAQ,CAAC0G,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACrB,OAAO,IAAImB,IAAI,CAACpB,CAAC,CAACtG,SAAS,CAAC,CAACwG,OAAO,EAAE,GAAG,IAAIkB,IAAI,CAACnB,CAAC,CAACvG,SAAS,CAAC,CAACwG,OAAO,EAAE;UAC1E,CAAC,CAAC;UACF,IAAI,CAAC7G,QAAQ,GAAGA,QAAQ;UACxB,IAAI,CAACsE,mBAAmB,GAAG,IAAI;UAC/B,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM;UACL,IAAI,CAACpF,QAAQ,GAAG,EAAE;;QAGpB,IAAI,CAACoE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACwD,kBAAkB,EAAE;MAC3B,CAAC;MACDlB,KAAK,EAAGC,MAAM,IAAI;QAChB,IAAI,CAAC9D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACrE,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC+D,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACyD,kBAAkB,EAAE;MAC3B;KACD,CAAC;EACJ;EAEAK,wBAAwBA,CAAA;IACtB,IAAI,CAACpF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;IAEvB,IAAI,IAAI,CAACzB,QAAQ,EAAE;MACjBmF,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAACpF,QAAQ,CAAC;MAC3F,IAAI,CAACH,UAAU,CAAC0F,wBAAwB,CAAC,IAAI,CAACvF,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC4B,SAAS,CAAC;QAC5EmC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAChI,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACsE,mBAAmB,GAAG,KAAK;UAChC,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACoE,UAAU,CAACrE,sBAAsB,CAAC;UAC/C,IAAI,CAACvD,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC8C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC;QACDsC,KAAK,EAAGC,MAAM,IAAI;UAChB,IAAI,CAAClI,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACsE,mBAAmB,GAAG,KAAK;UAChC,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACoE,UAAU,CAACrE,sBAAsB,CAAC;UAC/C,IAAI,CAACvD,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC8C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC3F,QAAQ,GAAG,CACd;QACE+F,EAAE,EAAE,IAAI,CAAC+B,UAAU,EAAE;QACrB/G,IAAI,EAAE,0DAA0D;QAChEL,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAI0H,IAAI;OACpB,CACF;MAED,IAAI,CAACpD,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;MACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;;EAEzB;EAGAlG,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACwE,QAAQ,EAAE;MAClB;;IAEF,IAAI,CAACK,mBAAmB,GAAG,IAAI;IAC/B,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;IACtEoB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;IACpD,IAAI,CAACK,oBAAoB,EAAE;IAC3B,IAAI,CAAC1B,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQF,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE;MAClB;;IAEF,IAAI,CAACI,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAAC5B,UAAU,CAAC0D,WAAW,CAAC,IAAI,CAACvD,QAAQ,EAAE,2BAA2B,CAAC,CAAC4B,SAAS,CAAC;MAChFmC,IAAI,EAAEA,CAAA,KAAK,CACX,CAAC;MACDC,KAAK,EAAGC,MAAM,IAAI;QAChBkB,OAAO,CAACnB,KAAK,CAAC,+BAA+B,CAAC;QAC9C,IAAI,CAAC5D,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAAC1B,QAAQ,CAACyD,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;MACJ;KACD,CAAC;EACJ;EAGAgC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACzF,QAAQ,EAAE;MAClBmF,OAAO,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC5D,IAAI,CAACvF,QAAQ,CAACyD,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;QAC1DC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,IAAI,CAACnD,YAAY,EAAE;MACrB6E,OAAO,CAACG,IAAI,CAAC,oCAAoC,CAAC;MAClD;;IAGF,IAAI,CAAChF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;IAEvB0D,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACpF,QAAQ,CAAC;IAEpE,IAAI,CAACH,UAAU,CAACwF,mBAAmB,CAAC,IAAI,CAACrF,QAAQ,CAAC,CAAC4B,SAAS,CAAC;MAC3DmC,IAAI,EAAGb,IAAI,IAAI;QACbiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAElC,IAAI,CAAC;QAC/C,IAAIA,IAAI,EAAE;UACRiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,MAAM,CAACC,IAAI,CAACzC,IAAI,CAAC,CAAC;UAC5D,IAAI,CAACtF,cAAc,GAAGsF,IAAI;UAC1B,IAAI,CAACnD,QAAQ,CAACyD,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;YACpEC,QAAQ,EAAE,IAAI;YACdmC,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACLT,OAAO,CAACG,IAAI,CAAC,sCAAsC,CAAC;UACpD,IAAI,CAACvF,QAAQ,CAACyD,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;;QAEJ,IAAI,CAACnD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;QACvB0D,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACxH,cAAc,CAAC;MACrE,CAAC;MACDoG,KAAK,EAAGC,MAAM,IAAI;QAChBkB,OAAO,CAACnB,KAAK,CAAC,kCAAkC,CAAC;QACjD,IAAI,CAACjE,QAAQ,CAACyD,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAC/DC,QAAQ,EAAE,IAAI;UACdmC,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAACjE,SAAS,CAAC,MAAK;UAC3B,IAAI,CAAC6D,qBAAqB,EAAE;QAC9B,CAAC,CAAC;QACF,IAAI,CAACnF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEArE,mBAAmBA,CAAC0I,OAAe;IACjC,IAAI,CAAC,IAAI,CAAClI,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACmI,iBAAiB,EAAE;MAClE,OAAO,CAAC;;IAGV,MAAMC,YAAY,GAAG,IAAI,CAACpI,cAAc,CAACmI,iBAAiB,CAAC1D,IAAI,CAC5D4D,KAAU,IAAKA,KAAK,CAACH,OAAO,CAACI,WAAW,EAAE,KAAKJ,OAAO,CAACI,WAAW,EAAE,CACtE;IAED,OAAOF,YAAY,GAAGA,YAAY,CAACG,aAAa,GAAG,CAAC;EACtD;;;uBA3cWxG,gBAAgB,EAAA9E,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAvL,EAAA,CAAA0L,iBAAA,GAAA1L,EAAA,CAAAuL,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB9G,gBAAgB;MAAA+G,SAAA;MAAAC,MAAA;QAAA3G,QAAA;QAAAC,UAAA;MAAA;MAAA2G,UAAA;MAAAC,QAAA,GAAAhM,EAAA,CAAAiM,oBAAA,EAAAjM,EAAA,CAAAkM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5C7BxM,EAAA,CAAAC,cAAA,aAAyB;UAGvBD,EAAA,CAAAY,UAAA,IAAA8L,+BAAA,kBAWM;UAEN1M,EAAA,CAAAC,cAAA,aAAyB;UAEOD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sDAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtFH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAI,UAAA,mBAAAuM,mDAAA;YAAA,OAASF,GAAA,CAAA/B,wBAAA,EAA0B;UAAA,EAAC;UAClF1K,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKhCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAY,UAAA,KAAAgM,gCAAA,mBAiBM;UAGN5M,EAAA,CAAAY,UAAA,KAAAiM,gCAAA,kBASM;UACR7M,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAI,UAAA,2BAAA0M,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAApH,cAAA,GAAA0H,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAApD,UAAA,CAAA0D,MAAA,CAAkB;UAAA,EAFD;UADnC/M,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAI,UAAA,mBAAA6M,mDAAA;YAAA,OAASR,GAAA,CAAA/D,WAAA,EAAa;UAAA,EAAC;UAC1D1I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7BH,EAAA,CAAAC,cAAA,eAAmC;UAGnBD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjCH,EAAA,CAAAC,cAAA,eAA0B;UACqCD,EAAA,CAAAI,UAAA,mBAAA8M,mDAAA;YAAA,OAAST,GAAA,CAAA7B,qBAAA,EAAuB;UAAA,EAAC;UAC5F5K,EAAA,CAAAC,cAAA,gBAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKlEH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAY,UAAA,KAAAuM,gCAAA,kBAOM;UAGNnN,EAAA,CAAAY,UAAA,KAAAwM,yCAAA,4BAmEe;UACjBpN,EAAA,CAAAG,YAAA,EAAM;;;UAtKmBH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,UAAAyL,GAAA,CAAAjH,mBAAA,CAA0B;UA0B1BxF,EAAA,CAAAe,SAAA,IAAa;UAAbf,EAAA,CAAAgB,UAAA,YAAAyL,GAAA,CAAAvL,QAAA,CAAa,iBAAAuL,GAAA,CAAAtC,SAAA;UAoBhCnK,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAAyL,GAAA,CAAAlH,oBAAA,CAA0B;UAevBvF,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,YAAAyL,GAAA,CAAApH,cAAA,CAA4B,aAAAoH,GAAA,CAAAnH,YAAA;UAKwBtF,EAAA,CAAAe,SAAA,GAAmD;UAAnDf,EAAA,CAAAgB,UAAA,cAAAyL,GAAA,CAAApH,cAAA,CAAA8B,IAAA,MAAAsF,GAAA,CAAAnH,YAAA,CAAmD;UAcwBtF,EAAA,CAAAe,SAAA,IAAyB;UAAzBf,EAAA,CAAAgB,UAAA,aAAAyL,GAAA,CAAAhH,YAAA,CAAyB;UACjJzF,EAAA,CAAAe,SAAA,GAA+B;UAA/Bf,EAAA,CAAAqN,WAAA,aAAAZ,GAAA,CAAAhH,YAAA,CAA+B;UAOvCzF,EAAA,CAAAe,SAAA,GAAqB;UAArBf,EAAA,CAAAgB,UAAA,UAAAyL,GAAA,CAAA1J,cAAA,CAAqB;UAUZ/C,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAAgB,UAAA,SAAAyL,GAAA,CAAA1J,cAAA,CAAoB;;;qBD7ErC5D,YAAY,EAAAmO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZtO,WAAW,EAAAuO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXzO,mBAAmB,EACnBC,eAAe,EAAAyO,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACf3O,aAAa,EACbC,kBAAkB,EAAA2O,EAAA,CAAAC,YAAA,EAClB3O,aAAa,EAAA4O,EAAA,CAAAC,OAAA,EACb5O,cAAc,EAAA6O,EAAA,CAAAC,QAAA,EACd7O,wBAAwB,EACxBC,gBAAgB,EAAA6O,EAAA,CAAAC,UAAA,EAChB7O,eAAe,EAEfC,YAAY,EAEZC,mBAAmB;MAAA4O,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMV9J,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}