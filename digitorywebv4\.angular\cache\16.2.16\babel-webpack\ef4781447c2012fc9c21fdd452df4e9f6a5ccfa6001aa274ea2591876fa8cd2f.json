{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23);\n    i0.ɵɵpipe(3, \"markdown\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 2, message_r12.text), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_p_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Information will appear as you chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_36_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r14 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r14);\n  }\n}\nfunction ChatBotComponent_ul_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_36_li_1_Template, 2, 1, \"li\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Information will appear as you chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_41_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r16);\n  }\n}\nfunction ChatBotComponent_ul_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_41_li_1_Template, 2, 1, \"li\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Information will appear as you chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 28)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Expected Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Menu Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Information will appear as you chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \"Information will appear as you chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n  }\n  ngOnChanges(changes) {\n    // No need to load conversation history on changes\n  }\n  ngOnInit() {\n    // Initialize with a welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // No need to load conversation history\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Add new bot message\n          this.messages.push(message);\n        }\n      } else {\n        // For user messages, add them if they don't exist already\n        if (!this.messages.some(m => m.id === message.id)) {\n          this.messages.push(message);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // Add user message to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    this.messages.push(userMessage);\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(index, message) {\n    return message.id;\n  }\n  // Removed loadConversationHistory method\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Reset to just the welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Conversation has been cleared. How can I help you today?',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.cd.detectChanges();\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 53,\n      vars: 17,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear conversation\", 3, \"disabled\", \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"bot-typing\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"bot-typing\"], [1, \"typing-indicator\"], [1, \"placeholder-text\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"Restaurant Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_9_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"delete_sweep\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 7);\n          i0.ɵɵtemplate(13, ChatBotComponent_div_13_Template, 4, 7, \"div\", 8);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 5, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"mat-form-field\", 11)(17, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"h3\");\n          i0.ɵɵtext(26, \"Restaurant Summary\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 16)(28, \"div\", 17)(29, \"h4\");\n          i0.ɵɵtext(30, \"Location & Work Areas\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, ChatBotComponent_p_31_Template, 2, 1, \"p\", 18);\n          i0.ɵɵtemplate(32, ChatBotComponent_p_32_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 17)(34, \"h4\");\n          i0.ɵɵtext(35, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, ChatBotComponent_ul_36_Template, 2, 1, \"ul\", 18);\n          i0.ɵɵtemplate(37, ChatBotComponent_p_37_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 17)(39, \"h4\");\n          i0.ɵɵtext(40, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, ChatBotComponent_ul_41_Template, 2, 1, \"ul\", 18);\n          i0.ɵɵtemplate(42, ChatBotComponent_p_42_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 17)(44, \"h4\");\n          i0.ɵɵtext(45, \"Menu Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, ChatBotComponent_table_46_Template, 11, 2, \"table\", 20);\n          i0.ɵɵtemplate(47, ChatBotComponent_p_47_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 17)(49, \"h4\");\n          i0.ɵɵtext(50, \"Operating Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(51, ChatBotComponent_p_51_Template, 2, 1, \"p\", 18);\n          i0.ɵɵtemplate(52, ChatBotComponent_p_52_Template, 2, 0, \"p\", 19);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 500px;\\n  gap: 20px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 700px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px; \\n\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 48px; \\n\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f1f1f1;\\n  padding: 12px 16px;\\n  border-radius: 18px;\\n  margin-left: 44px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 1px;\\n  background-color: #9E9EA1;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink infinite 0.9999s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink {\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 10px; \\n\\n  background-color: #f8f9fa;\\n  background-image: none; \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 4px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 8px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 10px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 10px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 8px 12px; \\n\\n  border-radius: 18px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%; \\n\\n  word-wrap: break-word; \\n\\n  line-height: 1.4; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n  color: white;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4; \\n\\n  word-break: break-word;\\n  white-space: normal; \\n\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px; \\n\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05); \\n\\n  min-height: 60px; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 350px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background-color: #57705d;\\n  color: white;\\n}\\n\\n.summary-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\n.summary-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 5px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 13px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 8px 0 8px 16px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e8f5e9;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: 60px;\\n  justify-content: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 2px;\\n  background-color: #388e3c;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.4;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r12", "sender", "ɵɵadvance", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ɵɵtext", "ɵɵtextInterpolate", "ctx_r2", "restaurantSummary", "location", "cuisine_r14", "ɵɵtemplate", "ChatBotComponent_ul_36_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r16", "ChatBotComponent_ul_41_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "ngOnChanges", "changes", "ngOnInit", "id", "generateId", "timestamp", "Date", "messages$", "subscribe", "message", "existingMessageIndex", "findIndex", "m", "push", "some", "detectChanges", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "userMessage", "updateRestaurantSummary", "next", "error", "console", "lowerMessage", "toLowerCase", "includes", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "Math", "random", "toString", "substring", "trackById", "index", "clearConversationHistory", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_9_listener", "ChatBotComponent_div_13_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_p_31_Template", "ChatBotComponent_p_32_Template", "ChatBotComponent_ul_36_Template", "ChatBotComponent_p_37_Template", "ChatBotComponent_ul_41_Template", "ChatBotComponent_p_42_Template", "ChatBotComponent_table_46_Template", "ChatBotComponent_p_47_Template", "ChatBotComponent_p_51_Template", "ChatBotComponent_p_52_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // No need to load conversation history on changes\n  }\n\n  ngOnInit(): void {\n    // Initialize with a welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // No need to load conversation history\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Add new bot message\n            this.messages.push(message);\n          }\n        } else {\n          // For user messages, add them if they don't exist already\n          if (!this.messages.some(m => m.id === message.id)) {\n            this.messages.push(message);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // Add user message to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    this.messages.push(userMessage);\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  // Removed loadConversationHistory method\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Reset to just the welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    this.cd.detectChanges();\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Chat interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <mat-icon class=\"chat-icon\">chat</mat-icon>\n        <span>Restaurant Assistant</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear conversation\" (click)=\"clearConversationHistory()\" [disabled]=\"isConnecting\">\n          <mat-icon>delete_sweep</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n        </div>\n      </div>\n\n      <!-- Loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"bot-typing\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <mat-icon>restaurant</mat-icon>\n      <h3>Restaurant Summary</h3>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location & Work Areas</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Information will appear as you chat</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Information will appear as you chat</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Information will appear as you chat</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Information</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Expected Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Menu Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Information will appear as you chat</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Operating Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Information will appear as you chat</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;;ICIlDC,EAAA,CAAAC,cAAA,cAAqL;IAEjLD,EAAA,CAAAE,SAAA,cAAsE;;IACxEF,EAAA,CAAAG,YAAA,EAAM;;;;IAH4EH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,MAAA,aAAAD,WAAA,CAAAC,MAAA,YAAgG;IAEtJR,EAAA,CAAAS,SAAA,GAAqC;IAArCT,EAAA,CAAAI,UAAA,cAAAJ,EAAA,CAAAU,WAAA,OAAAH,WAAA,CAAAI,IAAA,GAAAX,EAAA,CAAAY,cAAA,CAAqC;;;;;IAKnEZ,EAAA,CAAAC,cAAA,cAAqD;IAEjDD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;IA4BNH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAa,MAAA,GAAgC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtEjB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAa,MAAA,0CAAmC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAMrGH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAa,MAAA,GAAa;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAc,iBAAA,CAAAI,WAAA,CAAa;;;;;IAD1ElB,EAAA,CAAAC,cAAA,SAAsD;IACpDD,EAAA,CAAAmB,UAAA,IAAAC,oCAAA,iBAA6E;IAC/EpB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAL,iBAAA,CAAAM,YAAA,CAAiC;;;;;IAE3DtB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAa,MAAA,0CAAmC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAMrHH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAa,MAAA,GAAe;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAc,iBAAA,CAAAS,aAAA,CAAe;;;;;IAD7EvB,EAAA,CAAAC,cAAA,SAAqD;IACnDD,EAAA,CAAAmB,UAAA,IAAAK,oCAAA,iBAAgF;IAClFxB,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAAT,iBAAA,CAAAU,WAAA,CAAgC;;;;;IAE5D1B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAa,MAAA,0CAAmC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAKtHH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAa,MAAA,2BAAoB;IAAAb,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAiC;IAAAb,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAa,MAAA,uBAAgB;IAAAb,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAAAb,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAc,iBAAA,CAAAa,MAAA,CAAAX,iBAAA,CAAAY,SAAA,CAAiC;IAIjC5B,EAAA,CAAAS,SAAA,GAA6C;IAA7CT,EAAA,CAAAc,iBAAA,CAAAa,MAAA,CAAAX,iBAAA,CAAAa,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrD9B,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAa,MAAA,0CAAmC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;;IAK7GH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAa,MAAA,GAAsC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAS,SAAA,GAAsC;IAAtCT,EAAA,CAAAc,iBAAA,CAAAiB,OAAA,CAAAf,iBAAA,CAAAgB,cAAA,CAAsC;;;;;IAClFhC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAa,MAAA,0CAAmC;IAAAb,EAAA,CAAAG,YAAA,EAAI;;;AD5ErH,MAqBa8B,gBAAgB;EAsB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAxBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA3B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZK,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAY,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;EAMvD;EAEHC,WAAWA,CAACC,OAAsB;IAChC;EAAA;EAGFC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBvC,IAAI,EAAE,2KAA2K;MACjLH,MAAM,EAAE,KAAK;MACb2C,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IAED;IAEA;IACA,IAAI,CAACR,mBAAmB,GAAG,IAAI,CAACT,UAAU,CAACkB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAC/C,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMgD,oBAAoB,GAAG,IAAI,CAAChB,QAAQ,CAACiB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,CAAC;QAE9E,IAAIO,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAAChB,QAAQ,CAACgB,oBAAoB,CAAC,GAAGD,OAAO;SAC9C,MAAM;UACL;UACA,IAAI,CAACf,QAAQ,CAACmB,IAAI,CAACJ,OAAO,CAAC;;OAE9B,MAAM;QACL;QACA,IAAI,CAAC,IAAI,CAACf,QAAQ,CAACoB,IAAI,CAACF,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,CAAC,EAAE;UACjD,IAAI,CAACT,QAAQ,CAACmB,IAAI,CAACJ,OAAO,CAAC;;;MAI/B;MACA,IAAI,CAACnB,EAAE,CAACyB,aAAa,EAAE;MAEvB;MACA,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACnB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACoB,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACnB,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACmB,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC7B,UAAU,CAAC8B,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzB,cAAc,CAAC0B,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC+B,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC3B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM2B,aAAa,GAAG,IAAI,CAAC7B,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAACyB,aAAa,EAAE;IAEvB;IACA,MAAMU,WAAW,GAAgB;MAC/BtB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBvC,IAAI,EAAE2D,aAAa;MACnB9D,MAAM,EAAE,MAAM;MACd2C,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAACZ,QAAQ,CAACmB,IAAI,CAACY,WAAW,CAAC;IAE/B;IACA,IAAI,CAACC,uBAAuB,CAACF,aAAa,CAAC;IAE3C;IACA,IAAI,CAACnC,UAAU,CAAC+B,WAAW,CAAC,IAAI,CAAC5B,QAAQ,EAAEgC,aAAa,CAAC,CAAChB,SAAS,CAAC;MAClEmB,IAAI,EAAEA,CAAA,KAAK;QACT;QACA,IAAI,CAAC/B,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAACyB,aAAa,EAAE;MACzB,CAAC;MACDa,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAAC+B,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjC,EAAE,CAACyB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQW,uBAAuBA,CAACjB,OAAe;IAC7C,MAAMqB,YAAY,GAAGrB,OAAO,CAACsB,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IACrEF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IACjEF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAAC9D,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAAC8D,kBAAkB,CAACH,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,IACtEF,YAAY,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAME,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACL,YAAY,CAAC;MACpD,IAAII,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACd,iBAAiB,CAACM,YAAY,GAAG,CAAC,GAAG,IAAI4D,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClE,iBAAiB,CAACM,YAAY,EAAE,GAAG0D,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIJ,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,IACtEF,YAAY,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAMpD,WAAW,GAAG,IAAI,CAACuD,gBAAgB,CAACL,YAAY,CAAC;MACvD,IAAIlD,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACd,iBAAiB,CAACU,WAAW,GAAG,CAAC,GAAG,IAAIwD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClE,iBAAiB,CAACU,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIkD,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC9DF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAMK,cAAc,GAAGP,YAAY,CAACQ,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAACnE,iBAAiB,CAACY,SAAS,GAAGyD,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIP,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMQ,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACL,YAAY,CAAC;QACtD,IAAIU,UAAU,CAACxD,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACd,iBAAiB,CAACa,cAAc,GAAG,CAAC,GAAG,IAAIqD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClE,iBAAiB,CAACa,cAAc,EAAE,GAAGyD,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIV,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,IAC9DF,YAAY,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAAC9D,iBAAiB,CAACgB,cAAc,GAAG,IAAI,CAAC+C,kBAAkB,CAACH,YAAY,CAAC;;IAG/E,IAAI,CAACxC,EAAE,CAACyB,aAAa,EAAE;EACzB;EAEA;;;;EAIQkB,kBAAkBA,CAACxB,OAAe;IACxC;IACA,MAAMgC,cAAc,GAAGhC,OAAO,CAC3BiC,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAAC1B,OAAe;IACtC;IACA,IAAIA,OAAO,CAACuB,QAAQ,CAAC,GAAG,CAAC,IAAIvB,OAAO,CAACuB,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOvB,OAAO,CACXqC,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC3B,IAAI,EAAE,CAAC,CACxB4B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAChE,MAAM,GAAG,CAAC,CAAC,CAC/B+D,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACxB,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAyC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAClC,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAuC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQxD,UAAUA,CAAA;IAChB,OAAOyD,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,KAAa,EAAEzD,OAAoB;IAC3C,OAAOA,OAAO,CAACN,EAAE;EACnB;EAEA;EAEA;;;EAGAgE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACzE,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBvC,IAAI,EAAE,0DAA0D;MAChEH,MAAM,EAAE,KAAK;MACb2C,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IAED;IACA,IAAI,CAACpC,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZK,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED,IAAI,CAACI,EAAE,CAACyB,aAAa,EAAE;EACzB;;;uBAnTW5B,gBAAgB,EAAAjC,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAlH,EAAA,CAAAqH,iBAAA,GAAArH,EAAA,CAAAkH,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBtF,gBAAgB;MAAAuF,SAAA;MAAAC,MAAA;QAAAnF,QAAA;QAAAC,UAAA;MAAA;MAAAmF,UAAA;MAAAC,QAAA,GAAA3H,EAAA,CAAA4H,oBAAA,EAAA5H,EAAA,CAAA6H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC7BnI,EAAA,CAAAC,cAAA,aAAyB;UAKWD,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAa,MAAA,2BAAoB;UAAAb,EAAA,CAAAG,YAAA,EAAO;UAEnCH,EAAA,CAAAC,cAAA,aAA0B;UACgCD,EAAA,CAAAqI,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAC1FjH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,oBAAY;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAKvCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAmB,UAAA,KAAAoH,gCAAA,iBAIM;UAGNvI,EAAA,CAAAmB,UAAA,KAAAqH,gCAAA,iBAMM;UACRxI,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAqI,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA3F,cAAA,GAAAiG,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAApC,UAAA,CAAA0C,MAAA,CAAkB;UAAA,EAFD;UADnC1I,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAqI,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAAlE,WAAA,EAAa;UAAA,EAAC;UAC1DlE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAElBD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,0BAAkB;UAAAb,EAAA,CAAAG,YAAA,EAAK;UAG7BH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAa,MAAA,6BAAqB;UAAAb,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAmB,UAAA,KAAA0H,8BAAA,gBAA0E;UAC1E7I,EAAA,CAAAmB,UAAA,KAAA2H,8BAAA,gBAAuG;UACzG9I,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAmB,UAAA,KAAA4H,+BAAA,iBAEK;UACL/I,EAAA,CAAAmB,UAAA,KAAA6H,8BAAA,gBAAuH;UACzHhJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAmB,UAAA,KAAA8H,+BAAA,iBAEK;UACLjJ,EAAA,CAAAmB,UAAA,KAAA+H,8BAAA,gBAAsH;UACxHlJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAmB,UAAA,KAAAgI,kCAAA,qBASQ;UACRnJ,EAAA,CAAAmB,UAAA,KAAAiI,8BAAA,gBAA6G;UAC/GpJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAmB,UAAA,KAAAkI,8BAAA,gBAAsF;UACtFrJ,EAAA,CAAAmB,UAAA,KAAAmI,8BAAA,gBAA6G;UAC/GtJ,EAAA,CAAAG,YAAA,EAAM;;;UAtFyFH,EAAA,CAAAS,SAAA,GAAyB;UAAzBT,EAAA,CAAAI,UAAA,aAAAgI,GAAA,CAAA1F,YAAA,CAAyB;UAO/F1C,EAAA,CAAAS,SAAA,GAAa;UAAbT,EAAA,CAAAI,UAAA,YAAAgI,GAAA,CAAA5F,QAAA,CAAa,iBAAA4F,GAAA,CAAArB,SAAA;UAOhC/G,EAAA,CAAAS,SAAA,GAA0B;UAA1BT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAAzF,oBAAA,CAA0B;UAYvB3C,EAAA,CAAAS,SAAA,GAA4B;UAA5BT,EAAA,CAAAI,UAAA,YAAAgI,GAAA,CAAA3F,cAAA,CAA4B,aAAA2F,GAAA,CAAA1F,YAAA;UAKwB1C,EAAA,CAAAS,SAAA,GAAmD;UAAnDT,EAAA,CAAAI,UAAA,cAAAgI,GAAA,CAAA3F,cAAA,CAAA0B,IAAA,MAAAiE,GAAA,CAAA1F,YAAA,CAAmD;UAgB1G1C,EAAA,CAAAS,SAAA,IAAgC;UAAhCT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAC,QAAA,CAAgC;UAChCjB,EAAA,CAAAS,SAAA,GAAiC;UAAjCT,EAAA,CAAAI,UAAA,UAAAgI,GAAA,CAAApH,iBAAA,CAAAC,QAAA,CAAiC;UAKhCjB,EAAA,CAAAS,SAAA,GAA+C;UAA/CT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAM,YAAA,CAAAQ,MAAA,KAA+C;UAGhD9B,EAAA,CAAAS,SAAA,GAAiD;UAAjDT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAM,YAAA,CAAAQ,MAAA,OAAiD;UAKhD9B,EAAA,CAAAS,SAAA,GAA8C;UAA9CT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAU,WAAA,CAAAI,MAAA,KAA8C;UAG/C9B,EAAA,CAAAS,SAAA,GAAgD;UAAhDT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAU,WAAA,CAAAI,MAAA,OAAgD;UAKtB9B,EAAA,CAAAS,SAAA,GAAqC;UAArCT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAY,SAAA,KAAqC;UAU/D5B,EAAA,CAAAS,SAAA,GAAuC;UAAvCT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAY,SAAA,OAAuC;UAKvC5B,EAAA,CAAAS,SAAA,GAAsC;UAAtCT,EAAA,CAAAI,UAAA,SAAAgI,GAAA,CAAApH,iBAAA,CAAAgB,cAAA,CAAsC;UACtChC,EAAA,CAAAS,SAAA,GAAuC;UAAvCT,EAAA,CAAAI,UAAA,UAAAgI,GAAA,CAAApH,iBAAA,CAAAgB,cAAA,CAAuC;;;qBDxE/C3C,YAAY,EAAAkK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZpK,WAAW,EAAAqK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXvK,mBAAmB,EACnBC,eAAe,EAAAuK,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACfxK,aAAa,EACbC,kBAAkB,EAAAwK,EAAA,CAAAC,YAAA,EAClBxK,aAAa,EAAAyK,EAAA,CAAAC,OAAA,EACbzK,cAAc,EAAA0K,EAAA,CAAAC,QAAA,EACd1K,wBAAwB,EACxBC,gBAAgB,EAAA0K,EAAA,CAAAC,UAAA,EAEhB1K,YAAY;MAAA2K,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH1I,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}