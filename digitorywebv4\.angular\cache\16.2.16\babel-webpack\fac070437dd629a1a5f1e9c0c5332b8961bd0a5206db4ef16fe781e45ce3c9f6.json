{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_button_9_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const i_r9 = restoredCtx.index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onTabChange(i_r9));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r8 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tab_r8.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r8.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.getActiveFiltersCount());\n  }\n}\nfunction SmartDashboardComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"mat-icon\", 44);\n    i0.ɵɵtext(3, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Ready to Generate Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Configure your filters and ask the AI assistant to generate visualizations for your data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 45)(9, \"div\", 46)(10, \"mat-icon\", 47);\n    i0.ɵɵtext(11, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h5\");\n    i0.ɵɵtext(13, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 46)(15, \"mat-icon\", 47);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h5\");\n    i0.ɵɵtext(18, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"mat-icon\", 47);\n    i0.ɵɵtext(21, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h5\");\n    i0.ɵɵtext(23, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 46)(25, \"mat-icon\", 47);\n    i0.ɵɵtext(26, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"h5\");\n    i0.ɵɵtext(28, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 48);\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 87,\n      vars: 16,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-tabs\"], [1, \"tabs-header\"], [1, \"tabs-list\"], [\"class\", \"tab-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-filters\"], [1, \"filters-header\"], [1, \"filters-title\"], [\"class\", \"filter-count\", 4, \"ngIf\"], [1, \"filters-actions\"], [\"mat-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", 3, \"value\", \"valueChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-header\"], [1, \"ai-icon\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me sales trends for the last quarter by region\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-charts\"], [1, \"charts-header\"], [1, \"charts-icon\"], [1, \"charts-content\"], [\"class\", \"charts-placeholder\", 4, \"ngIf\"], [\"class\", \"generated-charts\", 4, \"ngIf\"], [1, \"tab-button\", 3, \"click\"], [1, \"filter-count\"], [3, \"value\"], [1, \"charts-placeholder\"], [1, \"placeholder-content\"], [1, \"placeholder-icon\"], [1, \"feature-cards\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"generated-charts\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h3\");\n          i0.ɵɵtext(7, \"Reports\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4);\n          i0.ɵɵtemplate(9, SmartDashboardComponent_button_9_Template, 2, 3, \"button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h3\");\n          i0.ɵɵtext(16, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, SmartDashboardComponent_span_17_Template, 2, 1, \"span\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_19_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_22_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"check\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"div\", 14)(27, \"label\", 15)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-form-field\", 16)(32, \"mat-select\", 17);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_32_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_option_33_Template, 2, 2, \"mat-option\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 14)(35, \"label\", 15)(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 16)(40, \"mat-select\", 19);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_40_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(41, SmartDashboardComponent_mat_option_41_Template, 2, 2, \"mat-option\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 14)(43, \"label\", 15)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 16)(48, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_48_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"mat-datepicker-toggle\", 21)(50, \"mat-datepicker\", null, 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 14)(53, \"label\", 15)(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"mat-form-field\", 16)(58, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_58_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"mat-datepicker-toggle\", 21)(60, \"mat-datepicker\", null, 24);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(62, \"div\", 25)(63, \"div\", 26)(64, \"div\", 27)(65, \"mat-icon\", 28);\n          i0.ɵɵtext(66, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"h3\");\n          i0.ɵɵtext(68, \"Ask AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"Ask questions about your data or request specific visualizations\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 29)(72, \"mat-form-field\", 30)(73, \"input\", 31);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_73_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_73_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_74_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(75, \"mat-icon\");\n          i0.ɵɵtext(76, \"send\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \" Generate \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"div\", 33)(79, \"div\", 34)(80, \"mat-icon\", 35);\n          i0.ɵɵtext(81, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"h3\");\n          i0.ɵɵtext(83, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 36);\n          i0.ɵɵtemplate(85, SmartDashboardComponent_div_85_Template, 29, 0, \"div\", 37);\n          i0.ɵɵtemplate(86, SmartDashboardComponent_div_86_Template, 1, 0, \"div\", 38);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(51);\n          const _r5 = i0.ɵɵreference(61);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim());\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  background-color: #f8f9fa;\\n  \\n\\n}\\n[_nghost-%COMP%]   *[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  min-height: 100vh;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 320px;\\n  min-width: 320px;\\n  background-color: white;\\n  border-right: 1px solid #e0e0e0;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n\\n\\n.sidebar-tabs[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px;\\n  flex-shrink: 0;\\n}\\n\\n.tabs-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.tabs-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.tabs-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.tabs-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  padding: 10px 12px;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  font-size: 13px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n  text-align: left;\\n  width: 100%;\\n  border-left: 3px solid transparent;\\n}\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: #f8f9fa;\\n}\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  background-color: #e3f2fd;\\n  border-left-color: #2196f3;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.sidebar-filters[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 16px;\\n  overflow-y: auto;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.filters-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n  border-radius: 10px;\\n  padding: 2px 6px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  min-width: 16px;\\n  text-align: center;\\n  margin-left: 4px;\\n  line-height: 1.2;\\n}\\n\\n.filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n  height: 28px;\\n  padding: 0 8px;\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  flex: 1;\\n}\\n.reset-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #d0d0d0;\\n}\\n.reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.apply-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n  height: 28px;\\n  padding: 0 8px;\\n  background-color: #2196f3;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  border-radius: 4px;\\n  flex: 1;\\n}\\n.apply-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #444;\\n  margin-bottom: 2px;\\n}\\n.filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  color: #666;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  max-height: 32px;\\n  overflow: hidden;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-datepicker-toggle {\\n  color: #666;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-icon-suffix {\\n  padding-left: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.checkbox-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 8px;\\n}\\n\\n.filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox {\\n  --mdc-checkbox-state-layer-size: 40px;\\n}\\n.filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  font-size: 14px;\\n  color: #333;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px 20px;\\n  flex-shrink: 0;\\n}\\n\\n.ai-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.ai-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.ai-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0 auto;\\n  font-size: 12px;\\n  color: #666;\\n  line-height: 1.4;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: flex-start;\\n}\\n\\n.ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  height: 40px;\\n  padding: 0 16px;\\n  background-color: #2196f3;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n}\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #e0e0e0;\\n  color: #999;\\n}\\n.send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1976d2;\\n}\\n.send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  padding: 16px 20px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.charts-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n  flex-shrink: 0;\\n}\\n.charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.charts-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n  flex: 1;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex: 1;\\n  padding: 32px 20px;\\n}\\n\\n.placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #ddd;\\n  margin-bottom: 16px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-top: 16px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e8e8e8;\\n  border-radius: 6px;\\n  padding: 12px 8px;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n  border-color: #ddd;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #ff9800;\\n  margin-bottom: 6px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.generated-charts[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  min-height: 350px;\\n  \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 12px;\\n  }\\n  .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 8px;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n  .tabs-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    -webkit-overflow-scrolling: touch;\\n    padding-bottom: 2px;\\n  }\\n  .tab-button[_ngcontent-%COMP%] {\\n    white-space: nowrap;\\n    min-width: 100px;\\n    font-size: 12px;\\n    padding: 6px 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .filters-section[_ngcontent-%COMP%], .ai-section[_ngcontent-%COMP%], .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .reset-btn[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 36px;\\n  }\\n  .charts-placeholder[_ngcontent-%COMP%] {\\n    min-height: 280px;\\n    padding: 20px 16px;\\n  }\\n}\\n\\n\\n.tab-button[_ngcontent-%COMP%]:focus, .send-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n.ai-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n\\n\\n.reset-btn[_ngcontent-%COMP%]:focus, .apply-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.loading[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.feature-card[_ngcontent-%COMP%], .charts-content[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SmartDashboardComponent_button_9_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "i_r9", "index", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "onTabChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "tab_r8", "active", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtextInterpolate", "ctx_r1", "getActiveFiltersCount", "ɵɵproperty", "location_r12", "value", "baseDate_r13", "ɵɵelement", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "checked", "baseDates", "selectedLocations", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "ngOnInit", "toggleFilters", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "console", "log", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "length", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "SmartDashboardComponent_button_9_Template", "SmartDashboardComponent_span_17_Template", "SmartDashboardComponent_Template_button_click_19_listener", "SmartDashboardComponent_Template_button_click_22_listener", "SmartDashboardComponent_Template_mat_select_valueChange_32_listener", "$event", "SmartDashboardComponent_mat_option_33_Template", "SmartDashboardComponent_Template_mat_select_valueChange_40_listener", "SmartDashboardComponent_mat_option_41_Template", "SmartDashboardComponent_Template_input_ngModelChange_48_listener", "SmartDashboardComponent_Template_input_ngModelChange_58_listener", "SmartDashboardComponent_Template_input_ngModelChange_73_listener", "SmartDashboardComponent_Template_input_keydown_73_listener", "SmartDashboardComponent_Template_button_click_74_listener", "SmartDashboardComponent_div_85_Template", "SmartDashboardComponent_div_86_Template", "_r4", "_r5", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "i3", "MatIcon", "i4", "MatFormField", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Tabs + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Tabs (Vertical) -->\n    <div class=\"sidebar-tabs\">\n      <div class=\"tabs-header\">\n        <mat-icon>assessment</mat-icon>\n        <h3>Reports</h3>\n      </div>\n      <div class=\"tabs-list\">\n        <button\n          *ngFor=\"let tab of tabs; let i = index\"\n          class=\"tab-button\"\n          [class.active]=\"tab.active\"\n          (click)=\"onTabChange(i)\"\n        >\n          {{ tab.label }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Smart Filters Section (Vertical) -->\n    <div class=\"sidebar-filters\">\n      <div class=\"filters-header\">\n        <div class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          <h3>Smart Filters</h3>\n          <span class=\"filter-count\" *ngIf=\"getActiveFiltersCount() > 0\">{{getActiveFiltersCount()}}</span>\n        </div>\n        <div class=\"filters-actions\">\n          <button mat-button class=\"reset-btn\" (click)=\"resetFilters()\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n          <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n            <mat-icon>check</mat-icon>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- 1. Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>location_on</mat-icon>\n            Restaurants\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple>\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- 2. Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- 3. Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- 4. End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Right Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Input Section (Top Right) -->\n    <div class=\"ai-section\">\n      <div class=\"ai-header\">\n        <mat-icon class=\"ai-icon\">psychology</mat-icon>\n        <h3>Ask AI Assistant</h3>\n        <p>Ask questions about your data or request specific visualizations</p>\n      </div>\n      <div class=\"ai-input-container\">\n        <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n          <input\n            matInput\n            type=\"text\"\n            placeholder=\"e.g., Show me sales trends for the last quarter by region\"\n            [(ngModel)]=\"chatMessage\"\n            (keydown)=\"onKeyPress($event)\"\n          >\n        </mat-form-field>\n        <button mat-raised-button color=\"primary\" class=\"send-btn\" (click)=\"sendMessage()\" [disabled]=\"!chatMessage.trim()\">\n          <mat-icon>send</mat-icon>\n          Generate\n        </button>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area (Bottom Right) -->\n    <div class=\"dashboard-charts\">\n      <div class=\"charts-header\">\n        <mat-icon class=\"charts-icon\">dashboard</mat-icon>\n        <h3>Dashboard</h3>\n      </div>\n\n      <!-- Charts will be generated here based on AI input -->\n      <div class=\"charts-content\">\n        <!-- Placeholder content when no charts are generated -->\n        <div class=\"charts-placeholder\" *ngIf=\"!hasGeneratedCharts\">\n          <div class=\"placeholder-content\">\n            <mat-icon class=\"placeholder-icon\">insights</mat-icon>\n            <h4>Ready to Generate Insights</h4>\n            <p>Configure your filters and ask the AI assistant to generate visualizations for your data.</p>\n\n            <!-- Feature Cards -->\n            <div class=\"feature-cards\">\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n                <h5>Interactive Charts</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n                <h5>Smart Insights</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">chat</mat-icon>\n                <h5>Natural Language Queries</h5>\n              </div>\n              <div class=\"feature-card\">\n                <mat-icon class=\"feature-icon\">schedule</mat-icon>\n                <h5>Real-time Analysis</h5>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated charts will appear here -->\n        <div class=\"generated-charts\" *ngIf=\"hasGeneratedCharts\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICHpCC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAExBP,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAJPd,EAAA,CAAAe,WAAA,WAAAC,MAAA,CAAAC,MAAA,CAA2B;IAG3BjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACF;;;;;IAUEpB,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAAlCd,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAqB,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA2B;;;;;IAqBtFvB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAC,YAAA,CAAAC,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAM,YAAA,CAAAL,KAAA,MACF;;;;;IAaApB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAG,YAAA,CAAAD,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAQ,YAAA,CAAAP,KAAA,MACF;;;;;IAkFNpB,EAAA,CAAAC,cAAA,cAA4D;IAErBD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACtDd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,iCAA0B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,gGAAyF;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAGhGd,EAAA,CAAAC,cAAA,cAA2B;IAEQD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACnDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAE7Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACrDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEzBd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,YAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC9Cd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,gCAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;;;;;IAOnCd,EAAA,CAAA4B,SAAA,cAEM;;;ADnKd,MAqBaC,uBAAuB;EAqClCC,YAAA;IApCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAER,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,wBAAwB;MAAEe,OAAO,EAAE;IAAK,CAAE,EACzE;MAAET,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,iBAAiB;MAAEe,OAAO,EAAE;IAAK,CAAE,EAClE;MAAET,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,aAAa;MAAEe,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAET,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,gBAAgB;MAAEe,OAAO,EAAE;IAAK,CAAE,EACjE;MAAET,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,oBAAoB;MAAEe,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAC,SAAS,GAAG,CACV;MAAEV,KAAK,EAAE,OAAO;MAAEN,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,YAAY;MAAEN,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEM,KAAK,EAAE,QAAQ;MAAEN,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAiB,iBAAiB,GAAa,EAAE;IAChC,KAAAC,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAEtB,KAAK,EAAE,YAAY;MAAEH,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEG,KAAK,EAAE,iBAAiB;MAAEH,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEG,KAAK,EAAE,cAAc;MAAEH,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEG,KAAK,EAAE,kBAAkB;MAAEH,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhB0B,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACb,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAnB,WAAWA,CAACJ,KAAa;IACvB,IAAI,CAACwB,WAAW,GAAGxB,KAAK;IACxB,IAAI,CAACkC,IAAI,CAACG,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAAC7B,MAAM,GAAG8B,CAAC,KAAKvC,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAwC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACP,WAAW,CAACQ,IAAI,EAAE,EAAE;MAC3B;MACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACV,WAAW,CAAC;MAE9C;MACA,IAAI,CAACR,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACQ,WAAW,GAAG,EAAE;;EAEzB;EAEAW,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAEAS,YAAYA,CAAA;IACV;IACAP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACpB,aAAa,GAAG,KAAK;EAC5B;EAEA2B,YAAYA,CAAA;IACV;IACA,IAAI,CAACrB,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACN,SAAS,CAACW,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAACxB,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEAZ,qBAAqBA,CAAA;IACnB,IAAIqC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACvB,iBAAiB,CAACwB,MAAM,GAAG,CAAC,EAAED,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACtB,gBAAgB,KAAK,OAAO,EAAEsB,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACrB,SAAS,EAAEqB,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACpB,OAAO,EAAEoB,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;;;uBA9FW/B,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAiC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhE,EAAA,CAAAiE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCpCvE,EAAA,CAAAC,cAAA,aAAuC;UAMrBD,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAa,MAAA,cAAO;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAElBd,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAyE,UAAA,IAAAC,yCAAA,oBAOS;UACX1E,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,cAA6B;UAGbD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtBd,EAAA,CAAAyE,UAAA,KAAAE,wCAAA,kBAAiG;UACnG3E,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,eAA6B;UACUD,EAAA,CAAAE,UAAA,mBAAA0E,0DAAA;YAAA,OAASJ,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAC3D1D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAE9Bd,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAE,UAAA,mBAAA2E,0DAAA;YAAA,OAASL,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAClFzD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAKhCd,EAAA,CAAAC,cAAA,eAA6B;UAIbD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAChCd,EAAA,CAAAa,MAAA,qBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAE,UAAA,yBAAA4E,oEAAAC,MAAA;YAAA,OAAAP,GAAA,CAAAnC,iBAAA,GAAA0C,MAAA;UAAA,EAA6B;UACvC/E,EAAA,CAAAyE,UAAA,KAAAO,8CAAA,yBAEa;UACfhF,EAAA,CAAAc,YAAA,EAAa;UAKjBd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC1Bd,EAAA,CAAAa,MAAA,mBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAE,UAAA,yBAAA+E,oEAAAF,MAAA;YAAA,OAAAP,GAAA,CAAAlC,gBAAA,GAAAyC,MAAA;UAAA,EAA4B;UACtC/E,EAAA,CAAAyE,UAAA,KAAAS,8CAAA,yBAEa;UACflF,EAAA,CAAAc,YAAA,EAAa;UAKjBd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAa,MAAA,oBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAE,UAAA,2BAAAiF,iEAAAJ,MAAA;YAAA,OAAAP,GAAA,CAAAjC,SAAA,GAAAwC,MAAA;UAAA,EAAuB;UAHzB/E,EAAA,CAAAc,YAAA,EAMC;UACDd,EAAA,CAAA4B,SAAA,iCAA6E;UAE/E5B,EAAA,CAAAc,YAAA,EAAiB;UAInBd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAa,MAAA,kBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAE,UAAA,2BAAAkF,iEAAAL,MAAA;YAAA,OAAAP,GAAA,CAAAhC,OAAA,GAAAuC,MAAA;UAAA,EAAqB;UAHvB/E,EAAA,CAAAc,YAAA,EAMC;UACDd,EAAA,CAAA4B,SAAA,iCAA2E;UAE7E5B,EAAA,CAAAc,YAAA,EAAiB;UAOzBd,EAAA,CAAAC,cAAA,eAA0B;UAIMD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Cd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAa,MAAA,wEAAgE;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAEzEd,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAAE,UAAA,2BAAAmF,iEAAAN,MAAA;YAAA,OAAAP,GAAA,CAAA/B,WAAA,GAAAsC,MAAA;UAAA,EAAyB,qBAAAO,2DAAAP,MAAA;YAAA,OACdP,GAAA,CAAApB,UAAA,CAAA2B,MAAA,CAAkB;UAAA,EADJ;UAJ3B/E,EAAA,CAAAc,YAAA,EAMC;UAEHd,EAAA,CAAAC,cAAA,kBAAoH;UAAzDD,EAAA,CAAAE,UAAA,mBAAAqF,0DAAA;YAAA,OAASf,GAAA,CAAAxB,WAAA,EAAa;UAAA,EAAC;UAChFhD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,kBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAKbd,EAAA,CAAAC,cAAA,eAA8B;UAEID,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAClDd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAIpBd,EAAA,CAAAC,cAAA,eAA4B;UAE1BD,EAAA,CAAAyE,UAAA,KAAAe,uCAAA,mBA0BM;UAGNxF,EAAA,CAAAyE,UAAA,KAAAgB,uCAAA,kBAEM;UACRzF,EAAA,CAAAc,YAAA,EAAM;;;;;UAxKcd,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAwB,UAAA,YAAAgD,GAAA,CAAA9B,IAAA,CAAS;UAgBG1C,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAwB,UAAA,SAAAgD,GAAA,CAAAjD,qBAAA,OAAiC;UAoB/CvB,EAAA,CAAAkB,SAAA,IAA6B;UAA7BlB,EAAA,CAAAwB,UAAA,UAAAgD,GAAA,CAAAnC,iBAAA,CAA6B;UACNrC,EAAA,CAAAkB,SAAA,GAAY;UAAZlB,EAAA,CAAAwB,UAAA,YAAAgD,GAAA,CAAAtC,SAAA,CAAY;UAcnClC,EAAA,CAAAkB,SAAA,GAA4B;UAA5BlB,EAAA,CAAAwB,UAAA,UAAAgD,GAAA,CAAAlC,gBAAA,CAA4B;UACLtC,EAAA,CAAAkB,SAAA,GAAY;UAAZlB,EAAA,CAAAwB,UAAA,YAAAgD,GAAA,CAAApC,SAAA,CAAY;UAgB7CpC,EAAA,CAAAkB,SAAA,GAA6B;UAA7BlB,EAAA,CAAAwB,UAAA,kBAAAkE,GAAA,CAA6B,YAAAlB,GAAA,CAAAjC,SAAA;UAKEvC,EAAA,CAAAkB,SAAA,GAAmB;UAAnBlB,EAAA,CAAAwB,UAAA,QAAAkE,GAAA,CAAmB;UAclD1F,EAAA,CAAAkB,SAAA,GAA2B;UAA3BlB,EAAA,CAAAwB,UAAA,kBAAAmE,GAAA,CAA2B,YAAAnB,GAAA,CAAAhC,OAAA;UAKIxC,EAAA,CAAAkB,SAAA,GAAiB;UAAjBlB,EAAA,CAAAwB,UAAA,QAAAmE,GAAA,CAAiB;UAuBlD3F,EAAA,CAAAkB,SAAA,IAAyB;UAAzBlB,EAAA,CAAAwB,UAAA,YAAAgD,GAAA,CAAA/B,WAAA,CAAyB;UAIsDzC,EAAA,CAAAkB,SAAA,GAAgC;UAAhClB,EAAA,CAAAwB,UAAA,cAAAgD,GAAA,CAAA/B,WAAA,CAAAQ,IAAA,GAAgC;UAiBlFjD,EAAA,CAAAkB,SAAA,IAAyB;UAAzBlB,EAAA,CAAAwB,UAAA,UAAAgD,GAAA,CAAAvC,kBAAA,CAAyB;UA6B3BjC,EAAA,CAAAkB,SAAA,GAAwB;UAAxBlB,EAAA,CAAAwB,UAAA,SAAAgD,GAAA,CAAAvC,kBAAA,CAAwB;;;qBD7J3D9C,YAAY,EAAAyG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1G,aAAa,EACbC,eAAe,EAAA0G,EAAA,CAAAC,SAAA,EACf1G,aAAa,EAAA2G,EAAA,CAAAC,OAAA,EACb3G,kBAAkB,EAAA4G,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClB7G,eAAe,EAAA8G,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfhH,cAAc,EAAAiH,EAAA,CAAAC,QAAA,EACdjH,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAA+G,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBjH,mBAAmB,EACnBC,WAAW,EAAAiH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFvF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}