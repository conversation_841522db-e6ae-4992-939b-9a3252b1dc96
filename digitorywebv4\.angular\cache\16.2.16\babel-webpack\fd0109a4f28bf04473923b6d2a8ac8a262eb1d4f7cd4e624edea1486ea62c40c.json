{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';\nimport { Chart, registerables } from 'chart.js';\nimport { NgChartsModule } from 'ng2-charts';\nimport { interval } from 'rxjs';\nimport { startWith, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/purchase-dashboard.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/datepicker\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/tabs\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"ng2-charts\";\nfunction PurchaseDashboardComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PurchaseDashboardComponent_mat_card_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 28)(1, \"mat-card-content\")(2, \"div\", 29)(3, \"mat-icon\", 30);\n    i0.ɵɵtext(4, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function PurchaseDashboardComponent_mat_card_56_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.refreshData());\n    });\n    i0.ɵɵtext(8, \"Retry\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.error);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"GRN ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r23.grnId);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Vendor\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r24.vendorName);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Location\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r25 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r25.location);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Amount\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r26 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.formatCurrency(element_r26.total));\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r27 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.formatDate(element_r27.date));\n  }\n}\nfunction PurchaseDashboardComponent_div_57_th_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Items\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_td_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r28 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r28.itemCount);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_tr_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 66);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_tr_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 67);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Location\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r42 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r42.Location);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Vendor\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r43 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r43[\"Vendor Name\"]);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"GRN ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r44 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r44[\"GRN Id\"]);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Item\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r45 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(element_r45[\"Item Name\"]);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Total\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r46 = ctx.$implicit;\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r39.formatCurrency(element_r46[\"Total(incl.tax,etc)\"]));\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 66);\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_104_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 67);\n  }\n}\nconst _c0 = function () {\n  return [\"Location\", \"Vendor Name\", \"GRN Id\", \"Item Name\", \"Total(incl.tax,etc)\"];\n};\nfunction PurchaseDashboardComponent_div_57_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"table\", 68);\n    i0.ɵɵelementContainerStart(2, 69);\n    i0.ɵɵtemplate(3, PurchaseDashboardComponent_div_57_div_104_th_3_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(4, PurchaseDashboardComponent_div_57_div_104_td_4_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 70);\n    i0.ɵɵtemplate(6, PurchaseDashboardComponent_div_57_div_104_th_6_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(7, PurchaseDashboardComponent_div_57_div_104_td_7_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 71);\n    i0.ɵɵtemplate(9, PurchaseDashboardComponent_div_57_div_104_th_9_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(10, PurchaseDashboardComponent_div_57_div_104_td_10_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 72);\n    i0.ɵɵtemplate(12, PurchaseDashboardComponent_div_57_div_104_th_12_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(13, PurchaseDashboardComponent_div_57_div_104_td_13_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 73);\n    i0.ɵɵtemplate(15, PurchaseDashboardComponent_div_57_div_104_th_15_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(16, PurchaseDashboardComponent_div_57_div_104_td_16_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(17, PurchaseDashboardComponent_div_57_div_104_tr_17_Template, 1, 0, \"tr\", 59);\n    i0.ɵɵtemplate(18, PurchaseDashboardComponent_div_57_div_104_tr_18_Template, 1, 0, \"tr\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r21.detailedData);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", i0.ɵɵpureFunction0(4, _c0));\n  }\n}\nfunction PurchaseDashboardComponent_div_57_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No detailed data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PurchaseDashboardComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"mat-card\", 34)(3, \"mat-card-content\")(4, \"div\", 35)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8, \"Total Amount\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38)(12, \"mat-icon\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"mat-card\", 34)(18, \"mat-card-content\")(19, \"div\", 35)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 36);\n    i0.ɵɵtext(23, \"Total Transactions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 37);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 39);\n    i0.ɵɵtext(28, \"Purchase Orders\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"mat-card\", 34)(30, \"mat-card-content\")(31, \"div\", 35)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 36);\n    i0.ɵɵtext(35, \"Avg Transaction\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 37);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 39);\n    i0.ɵɵtext(39, \"Per Order\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"mat-card\", 34)(41, \"mat-card-content\")(42, \"div\", 35)(43, \"mat-icon\");\n    i0.ɵɵtext(44, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 36);\n    i0.ɵɵtext(46, \"Recent Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 37);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 39);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(51, \"div\", 40)(52, \"div\", 41)(53, \"mat-card\", 42)(54, \"mat-card-header\")(55, \"mat-card-title\");\n    i0.ɵɵtext(56, \"Daily Purchase Trend\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"mat-card-content\")(58, \"div\", 43);\n    i0.ɵɵelement(59, \"canvas\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"mat-card\", 42)(61, \"mat-card-header\")(62, \"mat-card-title\");\n    i0.ɵɵtext(63, \"Top Vendors\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 43);\n    i0.ɵɵelement(66, \"canvas\", 44);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(67, \"div\", 41)(68, \"mat-card\", 45)(69, \"mat-card-header\")(70, \"mat-card-title\");\n    i0.ɵɵtext(71, \"Purchase by Category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"mat-card-content\")(73, \"div\", 43);\n    i0.ɵɵelement(74, \"canvas\", 46);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(75, \"mat-tab-group\", 47)(76, \"mat-tab\", 48)(77, \"mat-card\")(78, \"mat-card-content\")(79, \"div\", 49)(80, \"table\", 50);\n    i0.ɵɵelementContainerStart(81, 51);\n    i0.ɵɵtemplate(82, PurchaseDashboardComponent_div_57_th_82_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(83, PurchaseDashboardComponent_div_57_td_83_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(84, 54);\n    i0.ɵɵtemplate(85, PurchaseDashboardComponent_div_57_th_85_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(86, PurchaseDashboardComponent_div_57_td_86_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(87, 55);\n    i0.ɵɵtemplate(88, PurchaseDashboardComponent_div_57_th_88_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(89, PurchaseDashboardComponent_div_57_td_89_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(90, 56);\n    i0.ɵɵtemplate(91, PurchaseDashboardComponent_div_57_th_91_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(92, PurchaseDashboardComponent_div_57_td_92_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(93, 57);\n    i0.ɵɵtemplate(94, PurchaseDashboardComponent_div_57_th_94_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(95, PurchaseDashboardComponent_div_57_td_95_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(96, 58);\n    i0.ɵɵtemplate(97, PurchaseDashboardComponent_div_57_th_97_Template, 2, 0, \"th\", 52);\n    i0.ɵɵtemplate(98, PurchaseDashboardComponent_div_57_td_98_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(99, PurchaseDashboardComponent_div_57_tr_99_Template, 1, 0, \"tr\", 59);\n    i0.ɵɵtemplate(100, PurchaseDashboardComponent_div_57_tr_100_Template, 1, 0, \"tr\", 60);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(101, \"mat-tab\", 61)(102, \"mat-card\")(103, \"mat-card-content\");\n    i0.ɵɵtemplate(104, PurchaseDashboardComponent_div_57_div_104_Template, 19, 5, \"div\", 62);\n    i0.ɵɵtemplate(105, PurchaseDashboardComponent_div_57_div_105_Template, 5, 0, \"div\", 63);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.formatCurrency(ctx_r4.dashboardData.summary.totalAmount));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getTrendColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getTrendIcon());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(16, 21, ctx_r4.dashboardData.summary.trendPercentage, \"1.1-1\"), \"%\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 24, ctx_r4.dashboardData.summary.totalTransactions));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r4.formatCurrency(ctx_r4.dashboardData.summary.avgTransactionValue));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r4.dashboardData.recentPurchases.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Last \", ctx_r4.filterForm.value.days, \" days\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"data\", ctx_r4.dailyChartData)(\"options\", ctx_r4.chartOptions)(\"type\", \"line\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"data\", ctx_r4.vendorChartData)(\"options\", ctx_r4.chartOptions)(\"type\", \"doughnut\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"data\", ctx_r4.categoryChartData)(\"options\", ctx_r4.chartOptions);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"dataSource\", ctx_r4.dashboardData.recentPurchases);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.detailedData.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.detailedData.length === 0);\n  }\n}\nChart.register(...registerables);\nclass PurchaseDashboardComponent {\n  constructor(purchaseDashboardService, cdr) {\n    this.purchaseDashboardService = purchaseDashboardService;\n    this.cdr = cdr;\n    this.dashboardData = null;\n    this.detailedData = [];\n    this.loading = false;\n    this.error = null;\n    // Form controls\n    this.filterForm = new FormGroup({\n      restaurants: new FormControl(['all']),\n      days: new FormControl(7),\n      startDate: new FormControl(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),\n      endDate: new FormControl(new Date())\n    });\n    // Chart configurations\n    this.dailyChartData = {\n      labels: [],\n      datasets: [{\n        label: 'Daily Purchase Amount',\n        data: [],\n        borderColor: '#ff6b35',\n        backgroundColor: 'rgba(255, 107, 53, 0.1)',\n        tension: 0.4,\n        fill: true\n      }]\n    };\n    this.vendorChartData = {\n      labels: [],\n      datasets: [{\n        data: [],\n        backgroundColor: ['#ff6b35', '#f7931e', '#ffd23f', '#06d6a0', '#118ab2', '#073b4c', '#ef476f', '#ffd166', '#06ffa5', '#4ecdc4']\n      }]\n    };\n    this.categoryChartData = {\n      labels: [],\n      datasets: [{\n        label: 'Purchase Amount by Category',\n        data: [],\n        backgroundColor: '#ff6b35',\n        borderColor: '#e55a2b',\n        borderWidth: 1\n      }]\n    };\n    this.chartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          position: 'top'\n        }\n      }\n    };\n    // Table configuration\n    this.displayedColumns = ['grnId', 'vendorName', 'location', 'total', 'date', 'itemCount'];\n    // Subscriptions\n    this.subscriptions = [];\n    this.refreshInterval = 30000; // 30 seconds\n  }\n\n  ngOnInit() {\n    this.loadDashboardData();\n    this.setupAutoRefresh();\n    this.setupFormSubscription();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  setupFormSubscription() {\n    const formSub = this.filterForm.valueChanges.subscribe(() => {\n      this.loadDashboardData();\n    });\n    this.subscriptions.push(formSub);\n  }\n  setupAutoRefresh() {\n    const refreshSub = interval(this.refreshInterval).pipe(startWith(0), switchMap(() => this.loadDashboardData())).subscribe();\n    this.subscriptions.push(refreshSub);\n  }\n  loadDashboardData() {\n    return new Promise(resolve => {\n      this.loading = true;\n      this.error = null;\n      const formValue = this.filterForm.value;\n      const restaurants = formValue.restaurants?.includes('all') ? 'R001,R002,R003' :\n      // Default restaurants - should be dynamic\n      formValue.restaurants?.join(',') || 'R001';\n      const tenantId = 'T001'; // Should be from auth service\n      const days = formValue.days || 7;\n      this.purchaseDashboardService.getPurchaseSummary(tenantId, restaurants, days).subscribe({\n        next: response => {\n          if (response.status === 'success') {\n            this.dashboardData = response.data;\n            this.updateCharts();\n            this.loadDetailedData();\n          }\n          this.loading = false;\n          this.cdr.detectChanges();\n          resolve();\n        },\n        error: error => {\n          this.error = 'Failed to load dashboard data';\n          this.loading = false;\n          console.error('Dashboard error:', error);\n          this.cdr.detectChanges();\n          resolve();\n        }\n      });\n    });\n  }\n  loadDetailedData() {\n    if (!this.dashboardData) return;\n    const formValue = this.filterForm.value;\n    const restaurants = formValue.restaurants?.includes('all') ? 'R001,R002,R003' : formValue.restaurants?.join(',') || 'R001';\n    const tenantId = 'T001';\n    const startDate = this.dashboardData.dateRange.startDate;\n    const endDate = this.dashboardData.dateRange.endDate;\n    this.purchaseDashboardService.getPurchaseDetails(tenantId, restaurants, startDate, endDate).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.detailedData = response.grnStatus || [];\n        }\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Detailed data error:', error);\n      }\n    });\n  }\n  updateCharts() {\n    if (!this.dashboardData) return;\n    // Update daily chart\n    const dailyData = this.dashboardData.dailyTotals;\n    this.dailyChartData.labels = Object.keys(dailyData).sort();\n    this.dailyChartData.datasets[0].data = this.dailyChartData.labels.map(date => dailyData[date] || 0);\n    // Update vendor chart\n    const vendorData = this.dashboardData.vendorTotals;\n    this.vendorChartData.labels = Object.keys(vendorData);\n    this.vendorChartData.datasets[0].data = Object.values(vendorData);\n    // Update category chart\n    const categoryData = this.dashboardData.categoryTotals;\n    this.categoryChartData.labels = Object.keys(categoryData);\n    this.categoryChartData.datasets[0].data = Object.values(categoryData);\n    this.cdr.detectChanges();\n  }\n  refreshData() {\n    this.loadDashboardData();\n  }\n  exportData() {\n    // Implement export functionality\n    console.log('Export data functionality to be implemented');\n  }\n  getTrendIcon() {\n    if (!this.dashboardData) return 'trending_flat';\n    return this.dashboardData.summary.trendPercentage > 0 ? 'trending_up' : this.dashboardData.summary.trendPercentage < 0 ? 'trending_down' : 'trending_flat';\n  }\n  getTrendColor() {\n    if (!this.dashboardData) return 'text-gray-500';\n    return this.dashboardData.summary.trendPercentage > 0 ? 'text-green-500' : this.dashboardData.summary.trendPercentage < 0 ? 'text-red-500' : 'text-gray-500';\n  }\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR'\n    }).format(amount);\n  }\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleDateString('en-IN');\n  }\n  static {\n    this.ɵfac = function PurchaseDashboardComponent_Factory(t) {\n      return new (t || PurchaseDashboardComponent)(i0.ɵɵdirectiveInject(i1.PurchaseDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PurchaseDashboardComponent,\n      selectors: [[\"app-purchase-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 58,\n      vars: 9,\n      consts: [[1, \"purchase-dashboard\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"dashboard-title\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-form\", 3, \"formGroup\"], [\"appearance\", \"outline\"], [\"formControlName\", \"restaurants\", \"multiple\", \"\"], [\"value\", \"all\"], [\"value\", \"R001\"], [\"value\", \"R002\"], [\"value\", \"R003\"], [\"formControlName\", \"days\"], [\"value\", \"7\"], [\"value\", \"30\"], [\"value\", \"90\"], [\"matInput\", \"\", \"formControlName\", \"startDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"endDate\", 3, \"matDatepicker\"], [\"endPicker\", \"\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-card\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-card\"], [1, \"error-content\"], [\"color\", \"warn\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"dashboard-content\"], [1, \"summary-cards\"], [1, \"summary-card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-value\"], [1, \"card-trend\", 3, \"ngClass\"], [1, \"card-subtitle\"], [1, \"charts-section\"], [1, \"chart-row\"], [1, \"chart-card\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"data\", \"options\", \"type\"], [1, \"chart-card\", \"full-width\"], [\"baseChart\", \"\", \"type\", \"bar\", 3, \"data\", \"options\"], [1, \"tables-section\"], [\"label\", \"Recent Purchases\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"purchase-table\", 3, \"dataSource\"], [\"matColumnDef\", \"grnId\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"vendorName\"], [\"matColumnDef\", \"location\"], [\"matColumnDef\", \"total\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"itemCount\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"label\", \"Detailed Purchases\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [\"mat-table\", \"\", 1, \"detailed-table\", 3, \"dataSource\"], [\"matColumnDef\", \"Location\"], [\"matColumnDef\", \"Vendor Name\"], [\"matColumnDef\", \"GRN Id\"], [\"matColumnDef\", \"Item Name\"], [\"matColumnDef\", \"Total(incl.tax,etc)\"], [1, \"no-data\"]],\n      template: function PurchaseDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"shopping_cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Purchase Dashboard \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function PurchaseDashboardComponent_Template_button_click_8_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Refresh \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function PurchaseDashboardComponent_Template_button_click_12_listener() {\n            return ctx.exportData();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" Export \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"mat-card\", 7)(17, \"mat-card-content\")(18, \"form\", 8)(19, \"mat-form-field\", 9)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-select\", 10)(23, \"mat-option\", 11);\n          i0.ɵɵtext(24, \"All Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-option\", 12);\n          i0.ɵɵtext(26, \"Restaurant 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-option\", 13);\n          i0.ɵɵtext(28, \"Restaurant 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-option\", 14);\n          i0.ɵɵtext(30, \"Restaurant 3\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-form-field\", 9)(32, \"mat-label\");\n          i0.ɵɵtext(33, \"Time Period\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-select\", 15)(35, \"mat-option\", 16);\n          i0.ɵɵtext(36, \"Last 7 Days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-option\", 17);\n          i0.ɵɵtext(38, \"Last 30 Days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-option\", 18);\n          i0.ɵɵtext(40, \"Last 90 Days\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 9)(42, \"mat-label\");\n          i0.ɵɵtext(43, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 19)(45, \"mat-datepicker-toggle\", 20)(46, \"mat-datepicker\", null, 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"mat-form-field\", 9)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 22)(52, \"mat-datepicker-toggle\", 20)(53, \"mat-datepicker\", null, 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(55, PurchaseDashboardComponent_div_55_Template, 4, 0, \"div\", 24);\n          i0.ɵɵtemplate(56, PurchaseDashboardComponent_mat_card_56_Template, 9, 1, \"mat-card\", 25);\n          i0.ɵɵtemplate(57, PurchaseDashboardComponent_div_57_Template, 106, 26, \"div\", 26);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(47);\n          const _r1 = i0.ɵɵreference(54);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"matDatepicker\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", _r1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.dashboardData && !ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgIf, i2.DecimalPipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatButtonModule, i4.MatButton, MatIconModule, i5.MatIcon, MatSelectModule, i6.MatFormField, i6.MatLabel, i6.MatSuffix, i7.MatSelect, i8.MatOption, MatDatepickerModule, i9.MatDatepicker, i9.MatDatepickerInput, i9.MatDatepickerToggle, MatInputModule, i10.MatInput, MatFormFieldModule, MatTableModule, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, MatPaginatorModule, MatSortModule, MatProgressSpinnerModule, i12.MatProgressSpinner, MatTabsModule, i13.MatTab, i13.MatTabGroup, ReactiveFormsModule, i14.ɵNgNoValidate, i14.DefaultValueAccessor, i14.NgControlStatus, i14.NgControlStatusGroup, i14.FormGroupDirective, i14.FormControlName, NgChartsModule, i15.BaseChartDirective],\n      styles: [\".purchase-dashboard[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  flex-wrap: wrap;\\n  align-items: center;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #666;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  color: #d32f2f;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 15px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 24px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-trend.text-green-500[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-trend.text-red-500[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-trend.text-gray-500[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-trend[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 14px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%]   .chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  padding-bottom: 10px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 300px;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow: auto;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .purchase-table[_ngcontent-%COMP%], .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .detailed-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .purchase-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .detailed-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .purchase-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .detailed-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px 8px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .purchase-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover, .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .detailed-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  color: #666;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 10px;\\n  color: #ccc;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .tables-section[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .purchase-dashboard[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    align-items: stretch;\\n  }\\n  .purchase-dashboard[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .purchase-dashboard[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .purchase-dashboard[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n  .purchase-dashboard[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .mat-mdc-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .mat-mdc-button[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n.purchase-dashboard[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  max-height: 300px !important;\\n}\\n\\n  .mat-mdc-tab-group .mat-mdc-tab-header {\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n  .mat-mdc-tab-group .mat-mdc-tab {\\n  min-width: 120px;\\n}\\n  .mat-mdc-table .mat-mdc-header-row {\\n  background-color: #f8f9fa;\\n}\\n  .mat-mdc-table .mat-mdc-row:hover {\\n  background-color: #f5f5f5;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcHVyY2hhc2UtZGFzaGJvYXJkL3B1cmNoYXNlLWRhc2hib2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSx5QkFBQTtFQUNBLGlCQUFBO0FBQ0Y7QUFDRTtFQUNFLG1CQUFBO0FBQ0o7QUFDSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSx3Q0FBQTtBQUNOO0FBQ007RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFDUjtBQUNRO0VBQ0UsY0FBQTtBQUNWO0FBR007RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQURSO0FBR1E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBRFY7QUFPRTtFQUNFLG1CQUFBO0FBTEo7QUFPSTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0FBTE47QUFPTTtFQUNFLGdCQUFBO0FBTFI7QUFVRTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLHdDQUFBO0FBUko7QUFVSTtFQUNFLGdCQUFBO0VBQ0EsV0FBQTtBQVJOO0FBWUU7RUFDRSxtQkFBQTtBQVZKO0FBWUk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsY0FBQTtBQVZOO0FBWU07RUFDRSxlQUFBO0FBVlI7QUFnQkk7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFkTjtBQWlCUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQWZWO0FBaUJVO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFmWjtBQWtCVTtFQUNFLGdCQUFBO0VBQ0EsV0FBQTtBQWhCWjtBQW9CUTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQWxCVjtBQXFCUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFuQlY7QUFxQlU7RUFDRSxjQUFBO0FBbkJaO0FBc0JVO0VBQ0UsY0FBQTtBQXBCWjtBQXVCVTtFQUNFLGNBQUE7QUFyQlo7QUF3QlU7RUFDRSxlQUFBO0FBdEJaO0FBMEJRO0VBQ0UsV0FBQTtFQUNBLGVBQUE7QUF4QlY7QUE2Qkk7RUFDRSxtQkFBQTtBQTNCTjtBQTZCTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQTNCUjtBQTZCUTtFQUNFLGdCQUFBO0FBM0JWO0FBK0JVO0VBQ0UsaUJBQUE7QUE3Qlo7QUFnQ1U7RUFDRSxvQkFBQTtBQTlCWjtBQWdDWTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUE5QmQ7QUFrQ1U7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7QUFoQ1o7QUFxQ007RUFDRTtJQUNFLDBCQUFBO0VBbkNSO0FBQ0Y7QUF3Q007RUFDRSxpQkFBQTtFQUNBLGNBQUE7QUF0Q1I7QUF3Q1E7O0VBRUUsV0FBQTtBQXRDVjtBQXdDVTs7RUFDRSx5QkFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtBQXJDWjtBQXdDVTs7RUFDRSxpQkFBQTtBQXJDWjtBQXdDVTs7RUFDRSx5QkFBQTtBQXJDWjtBQTBDTTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0VBQ0EsV0FBQTtBQXhDUjtBQTBDUTtFQUNFLGVBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUF4Q1Y7QUEyQ1E7RUFDRSxTQUFBO0VBQ0EsZUFBQTtBQXpDVjtBQWdERTtFQW5QRjtJQW9QSSxhQUFBO0VBN0NGO0VBK0NFO0lBQ0Usc0JBQUE7SUFDQSxTQUFBO0lBQ0Esb0JBQUE7RUE3Q0o7RUErQ0k7SUFDRSx1QkFBQTtFQTdDTjtFQWlERTtJQUNFLHNCQUFBO0lBQ0Esb0JBQUE7RUEvQ0o7RUFpREk7SUFDRSxlQUFBO0lBQ0EsV0FBQTtFQS9DTjtFQW1ERTtJQUNFLDBCQUFBO0VBakRKO0FBQ0Y7QUFxREU7RUFDRSxrQkFBQTtFQUNBLHdDQUFBO0FBbkRKO0FBc0RFO0VBQ0Usa0JBQUE7QUFwREo7QUF3REk7RUFDRSxrQkFBQTtBQXRETjtBQTJERTtFQUNFLDRCQUFBO0FBekRKOztBQWdFSTtFQUNFLGdDQUFBO0FBN0ROO0FBZ0VJO0VBQ0UsZ0JBQUE7QUE5RE47QUFtRUk7RUFDRSx5QkFBQTtBQWpFTjtBQW9FSTtFQUNFLHlCQUFBO0FBbEVOIiwic291cmNlc0NvbnRlbnQiOlsiLnB1cmNoYXNlLWRhc2hib2FyZCB7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuXG4gIC5kYXNoYm9hcmQtaGVhZGVyIHtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuXG4gICAgLmhlYWRlci1jb250ZW50IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgICAgLmRhc2hib2FyZC10aXRsZSB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGdhcDogMTBweDtcbiAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiAjMzMzO1xuXG4gICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICBjb2xvcjogI2ZmNmIzNTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDEwcHg7XG5cbiAgICAgICAgYnV0dG9uIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgZ2FwOiA1cHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuZmlsdGVycy1jYXJkIHtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuXG4gICAgLmZpbHRlcnMtZm9ybSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiAyMHB4O1xuICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAgICAgbWF0LWZvcm0tZmllbGQge1xuICAgICAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5sb2FkaW5nLWNvbnRhaW5lciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgcGFkZGluZzogNDBweDtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgIHAge1xuICAgICAgbWFyZ2luLXRvcDogMjBweDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgfVxuXG4gIC5lcnJvci1jYXJkIHtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuXG4gICAgLmVycm9yLWNvbnRlbnQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEwcHg7XG4gICAgICBjb2xvcjogI2QzMmYyZjtcblxuICAgICAgbWF0LWljb24ge1xuICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLmRhc2hib2FyZC1jb250ZW50IHtcbiAgICAuc3VtbWFyeS1jYXJkcyB7XG4gICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XG4gICAgICBnYXA6IDIwcHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuXG4gICAgICAuc3VtbWFyeS1jYXJkIHtcbiAgICAgICAgLmNhcmQtaGVhZGVyIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgZ2FwOiAxMHB4O1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XG5cbiAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICBjb2xvcjogI2ZmNmIzNTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuY2FyZC10aXRsZSB7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLmNhcmQtdmFsdWUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMjhweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICAgIGNvbG9yOiAjMzMzO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gICAgICAgIH1cblxuICAgICAgICAuY2FyZC10cmVuZCB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogNXB4O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuXG4gICAgICAgICAgJi50ZXh0LWdyZWVuLTUwMCB7XG4gICAgICAgICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmLnRleHQtcmVkLTUwMCB7XG4gICAgICAgICAgICBjb2xvcjogI2Y0NDMzNjtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmLnRleHQtZ3JheS01MDAge1xuICAgICAgICAgICAgY29sb3I6ICM5ZTllOWU7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5jYXJkLXN1YnRpdGxlIHtcbiAgICAgICAgICBjb2xvcjogIzk5OTtcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuY2hhcnRzLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcblxuICAgICAgLmNoYXJ0LXJvdyB7XG4gICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjtcbiAgICAgICAgZ2FwOiAyMHB4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuXG4gICAgICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5jaGFydC1jYXJkIHtcbiAgICAgICAgICAmLmZ1bGwtd2lkdGgge1xuICAgICAgICAgICAgZ3JpZC1jb2x1bW46IDEgLyAtMTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBtYXQtY2FyZC1oZWFkZXIge1xuICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7XG5cbiAgICAgICAgICAgIG1hdC1jYXJkLXRpdGxlIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgICBjb2xvcjogIzMzMztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuY2hhcnQtY29udGFpbmVyIHtcbiAgICAgICAgICAgIGhlaWdodDogMzAwcHg7XG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICAuY2hhcnQtcm93IHtcbiAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC50YWJsZXMtc2VjdGlvbiB7XG4gICAgICAudGFibGUtY29udGFpbmVyIHtcbiAgICAgICAgbWF4LWhlaWdodDogNTAwcHg7XG4gICAgICAgIG92ZXJmbG93OiBhdXRvO1xuXG4gICAgICAgIC5wdXJjaGFzZS10YWJsZSxcbiAgICAgICAgLmRldGFpbGVkLXRhYmxlIHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcblxuICAgICAgICAgIHRoIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgY29sb3I6ICMzMzM7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdGQge1xuICAgICAgICAgICAgcGFkZGluZzogMTJweCA4cHg7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdHI6aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLm5vLWRhdGEge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgcGFkZGluZzogNDBweDtcbiAgICAgICAgY29sb3I6ICM2NjY7XG5cbiAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgICAgICAgIGNvbG9yOiAjY2NjO1xuICAgICAgICB9XG5cbiAgICAgICAgcCB7XG4gICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFJlc3BvbnNpdmUgZGVzaWduXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIHBhZGRpbmc6IDEwcHg7XG5cbiAgICAuZGFzaGJvYXJkLWhlYWRlciAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMTVweDtcbiAgICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoO1xuXG4gICAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZmlsdGVycy1mb3JtIHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcblxuICAgICAgbWF0LWZvcm0tZmllbGQge1xuICAgICAgICBtaW4td2lkdGg6IGF1dG87XG4gICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5zdW1tYXJ5LWNhcmRzIHtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIH1cbiAgfVxuXG4gIC8vIE1hdGVyaWFsIHRoZW1lIGN1c3RvbWl6YXRpb25zXG4gIC5tYXQtbWRjLWNhcmQge1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB9XG5cbiAgLm1hdC1tZGMtYnV0dG9uIHtcbiAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gIH1cblxuICAubWF0LW1kYy1mb3JtLWZpZWxkIHtcbiAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgIH1cbiAgfVxuXG4gIC8vIENoYXJ0LmpzIGN1c3RvbWl6YXRpb25zXG4gIGNhbnZhcyB7XG4gICAgbWF4LWhlaWdodDogMzAwcHggIWltcG9ydGFudDtcbiAgfVxufVxuXG4vLyBHbG9iYWwgc3R5bGVzIGZvciBiZXR0ZXIgYXBwZWFyYW5jZVxuOjpuZy1kZWVwIHtcbiAgLm1hdC1tZGMtdGFiLWdyb3VwIHtcbiAgICAubWF0LW1kYy10YWItaGVhZGVyIHtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTBlMGUwO1xuICAgIH1cblxuICAgIC5tYXQtbWRjLXRhYiB7XG4gICAgICBtaW4td2lkdGg6IDEyMHB4O1xuICAgIH1cbiAgfVxuXG4gIC5tYXQtbWRjLXRhYmxlIHtcbiAgICAubWF0LW1kYy1oZWFkZXItcm93IHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtcm93OmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}\nexport { PurchaseDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatDatepickerModule", "MatInputModule", "MatFormFieldModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatProgressSpinnerModule", "MatTabsModule", "FormControl", "FormGroup", "ReactiveFormsModule", "Chart", "registerables", "NgChartsModule", "interval", "startWith", "switchMap", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PurchaseDashboardComponent_mat_card_56_Template_button_click_7_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "refreshData", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r3", "error", "element_r23", "grnId", "element_r24", "vendorName", "element_r25", "location", "ctx_r14", "formatCurrency", "element_r26", "total", "ctx_r16", "formatDate", "element_r27", "date", "element_r28", "itemCount", "element_r42", "Location", "element_r43", "element_r44", "element_r45", "ctx_r39", "element_r46", "ɵɵelementContainerStart", "ɵɵtemplate", "PurchaseDashboardComponent_div_57_div_104_th_3_Template", "PurchaseDashboardComponent_div_57_div_104_td_4_Template", "ɵɵelementContainerEnd", "PurchaseDashboardComponent_div_57_div_104_th_6_Template", "PurchaseDashboardComponent_div_57_div_104_td_7_Template", "PurchaseDashboardComponent_div_57_div_104_th_9_Template", "PurchaseDashboardComponent_div_57_div_104_td_10_Template", "PurchaseDashboardComponent_div_57_div_104_th_12_Template", "PurchaseDashboardComponent_div_57_div_104_td_13_Template", "PurchaseDashboardComponent_div_57_div_104_th_15_Template", "PurchaseDashboardComponent_div_57_div_104_td_16_Template", "PurchaseDashboardComponent_div_57_div_104_tr_17_Template", "PurchaseDashboardComponent_div_57_div_104_tr_18_Template", "ɵɵproperty", "ctx_r21", "detailedData", "ɵɵpureFunction0", "_c0", "PurchaseDashboardComponent_div_57_th_82_Template", "PurchaseDashboardComponent_div_57_td_83_Template", "PurchaseDashboardComponent_div_57_th_85_Template", "PurchaseDashboardComponent_div_57_td_86_Template", "PurchaseDashboardComponent_div_57_th_88_Template", "PurchaseDashboardComponent_div_57_td_89_Template", "PurchaseDashboardComponent_div_57_th_91_Template", "PurchaseDashboardComponent_div_57_td_92_Template", "PurchaseDashboardComponent_div_57_th_94_Template", "PurchaseDashboardComponent_div_57_td_95_Template", "PurchaseDashboardComponent_div_57_th_97_Template", "PurchaseDashboardComponent_div_57_td_98_Template", "PurchaseDashboardComponent_div_57_tr_99_Template", "PurchaseDashboardComponent_div_57_tr_100_Template", "PurchaseDashboardComponent_div_57_div_104_Template", "PurchaseDashboardComponent_div_57_div_105_Template", "ctx_r4", "dashboardData", "summary", "totalAmount", "getTrendColor", "getTrendIcon", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "trendPercentage", "ɵɵpipeBind1", "totalTransactions", "avgTransactionValue", "recentPurchases", "length", "filterForm", "value", "days", "dailyChartData", "chartOptions", "vendorChartData", "categoryChartData", "displayedColumns", "register", "PurchaseDashboardComponent", "constructor", "purchaseDashboardService", "cdr", "loading", "restaurants", "startDate", "Date", "now", "endDate", "labels", "datasets", "label", "data", "borderColor", "backgroundColor", "tension", "fill", "borderWidth", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "subscriptions", "refreshInterval", "ngOnInit", "loadDashboardData", "setupAutoRefresh", "setupFormSubscription", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "formSub", "valueChanges", "subscribe", "push", "refreshSub", "pipe", "Promise", "resolve", "formValue", "includes", "join", "tenantId", "getPurchaseSummary", "next", "response", "status", "updateCharts", "loadDetailedData", "detectChanges", "console", "date<PERSON><PERSON><PERSON>", "getPurchaseDetails", "grnStatus", "dailyData", "dailyTotals", "Object", "keys", "sort", "map", "vendorData", "vendorTotals", "values", "categoryData", "categoryTotals", "exportData", "log", "amount", "Intl", "NumberFormat", "style", "currency", "format", "dateString", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "PurchaseDashboardService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PurchaseDashboardComponent_Template", "rf", "ctx", "PurchaseDashboardComponent_Template_button_click_8_listener", "PurchaseDashboardComponent_Template_button_click_12_listener", "PurchaseDashboardComponent_div_55_Template", "PurchaseDashboardComponent_mat_card_56_Template", "PurchaseDashboardComponent_div_57_Template", "_r0", "_r1", "i2", "Ng<PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatButton", "i5", "MatIcon", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatSelect", "i8", "MatOption", "i9", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i10", "MatInput", "i11", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i12", "MatProgressSpinner", "i13", "Mat<PERSON><PERSON>", "MatTabGroup", "i14", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i15", "BaseChartDirective", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/purchase-dashboard/purchase-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/purchase-dashboard/purchase-dashboard.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';\nimport { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';\nimport { NgChartsModule } from 'ng2-charts';\nimport { PurchaseDashboardService } from '../../services/purchase-dashboard.service';\nimport { Subscription, interval } from 'rxjs';\nimport { startWith, switchMap } from 'rxjs/operators';\n\nChart.register(...registerables);\n\nexport interface PurchaseSummary {\n  totalAmount: number;\n  totalTransactions: number;\n  avgTransactionValue: number;\n  trendPercentage: number;\n}\n\nexport interface DashboardData {\n  summary: PurchaseSummary;\n  dailyTotals: { [key: string]: number };\n  vendorTotals: { [key: string]: number };\n  locationTotals: { [key: string]: number };\n  categoryTotals: { [key: string]: number };\n  recentPurchases: any[];\n  dateRange: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\n@Component({\n  selector: 'app-purchase-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatProgressSpinnerModule,\n    MatTabsModule,\n    ReactiveFormsModule,\n    NgChartsModule\n  ],\n  templateUrl: './purchase-dashboard.component.html',\n  styleUrls: ['./purchase-dashboard.component.scss']\n})\nexport class PurchaseDashboardComponent implements OnInit, OnDestroy {\n  dashboardData: DashboardData | null = null;\n  detailedData: any[] = [];\n  loading = false;\n  error: string | null = null;\n\n  // Form controls\n  filterForm = new FormGroup({\n    restaurants: new FormControl(['all']),\n    days: new FormControl(7),\n    startDate: new FormControl(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),\n    endDate: new FormControl(new Date())\n  });\n\n  // Chart configurations\n  dailyChartData: ChartConfiguration<'line'>['data'] = {\n    labels: [],\n    datasets: [{\n      label: 'Daily Purchase Amount',\n      data: [],\n      borderColor: '#ff6b35',\n      backgroundColor: 'rgba(255, 107, 53, 0.1)',\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  vendorChartData: ChartConfiguration<'doughnut'>['data'] = {\n    labels: [],\n    datasets: [{\n      data: [],\n      backgroundColor: [\n        '#ff6b35', '#f7931e', '#ffd23f', '#06d6a0', '#118ab2',\n        '#073b4c', '#ef476f', '#ffd166', '#06ffa5', '#4ecdc4'\n      ]\n    }]\n  };\n\n  categoryChartData: ChartConfiguration<'bar'>['data'] = {\n    labels: [],\n    datasets: [{\n      label: 'Purchase Amount by Category',\n      data: [],\n      backgroundColor: '#ff6b35',\n      borderColor: '#e55a2b',\n      borderWidth: 1\n    }]\n  };\n\n  chartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n      }\n    }\n  };\n\n  // Table configuration\n  displayedColumns: string[] = [\n    'grnId', 'vendorName', 'location', 'total', 'date', 'itemCount'\n  ];\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n  private refreshInterval = 30000; // 30 seconds\n\n  constructor(\n    private purchaseDashboardService: PurchaseDashboardService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n    this.setupAutoRefresh();\n    this.setupFormSubscription();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private setupFormSubscription(): void {\n    const formSub = this.filterForm.valueChanges.subscribe(() => {\n      this.loadDashboardData();\n    });\n    this.subscriptions.push(formSub);\n  }\n\n  private setupAutoRefresh(): void {\n    const refreshSub = interval(this.refreshInterval)\n      .pipe(\n        startWith(0),\n        switchMap(() => this.loadDashboardData())\n      )\n      .subscribe();\n\n    this.subscriptions.push(refreshSub);\n  }\n\n  private loadDashboardData(): Promise<void> {\n    return new Promise((resolve) => {\n      this.loading = true;\n      this.error = null;\n\n      const formValue = this.filterForm.value;\n      const restaurants = formValue.restaurants?.includes('all') ?\n        'R001,R002,R003' : // Default restaurants - should be dynamic\n        formValue.restaurants?.join(',') || 'R001';\n\n      const tenantId = 'T001'; // Should be from auth service\n      const days = formValue.days || 7;\n\n      this.purchaseDashboardService.getPurchaseSummary(tenantId, restaurants, days)\n        .subscribe({\n          next: (response) => {\n            if (response.status === 'success') {\n              this.dashboardData = response.data;\n              this.updateCharts();\n              this.loadDetailedData();\n            }\n            this.loading = false;\n            this.cdr.detectChanges();\n            resolve();\n          },\n          error: (error) => {\n            this.error = 'Failed to load dashboard data';\n            this.loading = false;\n            console.error('Dashboard error:', error);\n            this.cdr.detectChanges();\n            resolve();\n          }\n        });\n    });\n  }\n\n  private loadDetailedData(): void {\n    if (!this.dashboardData) return;\n\n    const formValue = this.filterForm.value;\n    const restaurants = formValue.restaurants?.includes('all') ?\n      'R001,R002,R003' :\n      formValue.restaurants?.join(',') || 'R001';\n\n    const tenantId = 'T001';\n    const startDate = this.dashboardData.dateRange.startDate;\n    const endDate = this.dashboardData.dateRange.endDate;\n\n    this.purchaseDashboardService.getPurchaseDetails(tenantId, restaurants, startDate, endDate)\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.detailedData = response.grnStatus || [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Detailed data error:', error);\n        }\n      });\n  }\n\n  private updateCharts(): void {\n    if (!this.dashboardData) return;\n\n    // Update daily chart\n    const dailyData = this.dashboardData.dailyTotals;\n    this.dailyChartData.labels = Object.keys(dailyData).sort();\n    this.dailyChartData.datasets[0].data = this.dailyChartData.labels.map(\n      (date: string) => dailyData[date] || 0\n    );\n\n    // Update vendor chart\n    const vendorData = this.dashboardData.vendorTotals;\n    this.vendorChartData.labels = Object.keys(vendorData);\n    this.vendorChartData.datasets[0].data = Object.values(vendorData);\n\n    // Update category chart\n    const categoryData = this.dashboardData.categoryTotals;\n    this.categoryChartData.labels = Object.keys(categoryData);\n    this.categoryChartData.datasets[0].data = Object.values(categoryData);\n\n    this.cdr.detectChanges();\n  }\n\n  refreshData(): void {\n    this.loadDashboardData();\n  }\n\n  exportData(): void {\n    // Implement export functionality\n    console.log('Export data functionality to be implemented');\n  }\n\n  getTrendIcon(): string {\n    if (!this.dashboardData) return 'trending_flat';\n    return this.dashboardData.summary.trendPercentage > 0 ? 'trending_up' :\n           this.dashboardData.summary.trendPercentage < 0 ? 'trending_down' : 'trending_flat';\n  }\n\n  getTrendColor(): string {\n    if (!this.dashboardData) return 'text-gray-500';\n    return this.dashboardData.summary.trendPercentage > 0 ? 'text-green-500' :\n           this.dashboardData.summary.trendPercentage < 0 ? 'text-red-500' : 'text-gray-500';\n  }\n\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR'\n    }).format(amount);\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString('en-IN');\n  }\n}\n", "<div class=\"purchase-dashboard\">\n  <!-- Header Section -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <h1 class=\"dashboard-title\">\n        <mat-icon>shopping_cart</mat-icon>\n        Purchase Dashboard\n      </h1>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" (click)=\"refreshData()\" [disabled]=\"loading\">\n          <mat-icon>refresh</mat-icon>\n          Refresh\n        </button>\n        <button mat-raised-button (click)=\"exportData()\">\n          <mat-icon>download</mat-icon>\n          Export\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Filters Section -->\n  <mat-card class=\"filters-card\">\n    <mat-card-content>\n      <form [formGroup]=\"filterForm\" class=\"filters-form\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Restaurants</mat-label>\n          <mat-select formControlName=\"restaurants\" multiple>\n            <mat-option value=\"all\">All Restaurants</mat-option>\n            <mat-option value=\"R001\">Restaurant 1</mat-option>\n            <mat-option value=\"R002\">Restaurant 2</mat-option>\n            <mat-option value=\"R003\">Restaurant 3</mat-option>\n          </mat-select>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Time Period</mat-label>\n          <mat-select formControlName=\"days\">\n            <mat-option value=\"7\">Last 7 Days</mat-option>\n            <mat-option value=\"30\">Last 30 Days</mat-option>\n            <mat-option value=\"90\">Last 90 Days</mat-option>\n          </mat-select>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Start Date</mat-label>\n          <input matInput [matDatepicker]=\"startPicker\" formControlName=\"startDate\">\n          <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #startPicker></mat-datepicker>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\">\n          <mat-label>End Date</mat-label>\n          <input matInput [matDatepicker]=\"endPicker\" formControlName=\"endDate\">\n          <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #endPicker></mat-datepicker>\n        </mat-form-field>\n      </form>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Loading Spinner -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Loading dashboard data...</p>\n  </div>\n\n  <!-- Error Message -->\n  <mat-card *ngIf=\"error && !loading\" class=\"error-card\">\n    <mat-card-content>\n      <div class=\"error-content\">\n        <mat-icon color=\"warn\">error</mat-icon>\n        <span>{{ error }}</span>\n        <button mat-button color=\"primary\" (click)=\"refreshData()\">Retry</button>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"dashboardData && !loading\" class=\"dashboard-content\">\n\n    <!-- Summary Cards -->\n    <div class=\"summary-cards\">\n      <mat-card class=\"summary-card\">\n        <mat-card-content>\n          <div class=\"card-header\">\n            <mat-icon>attach_money</mat-icon>\n            <span class=\"card-title\">Total Amount</span>\n          </div>\n          <div class=\"card-value\">{{ formatCurrency(dashboardData.summary.totalAmount) }}</div>\n          <div class=\"card-trend\" [ngClass]=\"getTrendColor()\">\n            <mat-icon>{{ getTrendIcon() }}</mat-icon>\n            <span>{{ dashboardData.summary.trendPercentage | number:'1.1-1' }}%</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"summary-card\">\n        <mat-card-content>\n          <div class=\"card-header\">\n            <mat-icon>receipt</mat-icon>\n            <span class=\"card-title\">Total Transactions</span>\n          </div>\n          <div class=\"card-value\">{{ dashboardData.summary.totalTransactions | number }}</div>\n          <div class=\"card-subtitle\">Purchase Orders</div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"summary-card\">\n        <mat-card-content>\n          <div class=\"card-header\">\n            <mat-icon>trending_up</mat-icon>\n            <span class=\"card-title\">Avg Transaction</span>\n          </div>\n          <div class=\"card-value\">{{ formatCurrency(dashboardData.summary.avgTransactionValue) }}</div>\n          <div class=\"card-subtitle\">Per Order</div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"summary-card\">\n        <mat-card-content>\n          <div class=\"card-header\">\n            <mat-icon>schedule</mat-icon>\n            <span class=\"card-title\">Recent Orders</span>\n          </div>\n          <div class=\"card-value\">{{ dashboardData.recentPurchases.length }}</div>\n          <div class=\"card-subtitle\">Last {{ filterForm.value.days }} days</div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"charts-section\">\n      <div class=\"chart-row\">\n        <!-- Daily Trend Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Daily Purchase Trend</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                [data]=\"dailyChartData\"\n                [options]=\"chartOptions\"\n                [type]=\"'line'\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Vendor Distribution Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Top Vendors</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                [data]=\"vendorChartData\"\n                [options]=\"chartOptions\"\n                [type]=\"'doughnut'\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <div class=\"chart-row\">\n        <!-- Category Distribution Chart -->\n        <mat-card class=\"chart-card full-width\">\n          <mat-card-header>\n            <mat-card-title>Purchase by Category</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                [data]=\"categoryChartData\"\n                [options]=\"chartOptions\"\n                type=\"bar\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n\n    <!-- Tables Section -->\n    <mat-tab-group class=\"tables-section\">\n      <!-- Recent Purchases Tab -->\n      <mat-tab label=\"Recent Purchases\">\n        <mat-card>\n          <mat-card-content>\n            <div class=\"table-container\">\n              <table mat-table [dataSource]=\"dashboardData.recentPurchases\" class=\"purchase-table\">\n\n                <ng-container matColumnDef=\"grnId\">\n                  <th mat-header-cell *matHeaderCellDef>GRN ID</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element.grnId }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"vendorName\">\n                  <th mat-header-cell *matHeaderCellDef>Vendor</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element.vendorName }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"location\">\n                  <th mat-header-cell *matHeaderCellDef>Location</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element.location }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"total\">\n                  <th mat-header-cell *matHeaderCellDef>Amount</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ formatCurrency(element.total) }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"date\">\n                  <th mat-header-cell *matHeaderCellDef>Date</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ formatDate(element.date) }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"itemCount\">\n                  <th mat-header-cell *matHeaderCellDef>Items</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element.itemCount }}</td>\n                </ng-container>\n\n                <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n                <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n              </table>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </mat-tab>\n\n      <!-- Detailed Data Tab -->\n      <mat-tab label=\"Detailed Purchases\">\n        <mat-card>\n          <mat-card-content>\n            <div class=\"table-container\" *ngIf=\"detailedData.length > 0\">\n              <table mat-table [dataSource]=\"detailedData\" class=\"detailed-table\">\n                <!-- Add columns based on detailed data structure -->\n                <ng-container matColumnDef=\"Location\">\n                  <th mat-header-cell *matHeaderCellDef>Location</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element.Location }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"Vendor Name\">\n                  <th mat-header-cell *matHeaderCellDef>Vendor</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element['Vendor Name'] }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"GRN Id\">\n                  <th mat-header-cell *matHeaderCellDef>GRN ID</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element['GRN Id'] }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"Item Name\">\n                  <th mat-header-cell *matHeaderCellDef>Item</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ element['Item Name'] }}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"Total(incl.tax,etc)\">\n                  <th mat-header-cell *matHeaderCellDef>Total</th>\n                  <td mat-cell *matCellDef=\"let element\">{{ formatCurrency(element['Total(incl.tax,etc)']) }}</td>\n                </ng-container>\n\n                <tr mat-header-row *matHeaderRowDef=\"['Location', 'Vendor Name', 'GRN Id', 'Item Name', 'Total(incl.tax,etc)']\"></tr>\n                <tr mat-row *matRowDef=\"let row; columns: ['Location', 'Vendor Name', 'GRN Id', 'Item Name', 'Total(incl.tax,etc)'];\"></tr>\n              </table>\n            </div>\n            <div *ngIf=\"detailedData.length === 0\" class=\"no-data\">\n              <mat-icon>info</mat-icon>\n              <p>No detailed data available</p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,KAAK,EAAiCC,aAAa,QAAQ,UAAU;AAC9E,SAASC,cAAc,QAAQ,YAAY;AAE3C,SAAuBC,QAAQ,QAAQ,MAAM;AAC7C,SAASC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;IC2CnDC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAIlCJ,EAAA,CAAAC,cAAA,mBAAuD;IAG1BD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACvCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAC,cAAA,iBAA2D;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,wEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IADnEJ,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IA4HPhB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAAxBJ,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAc,iBAAA,CAAAG,WAAA,CAAAC,KAAA,CAAmB;;;;;IAI1DlB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA7BJ,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAc,iBAAA,CAAAK,WAAA,CAAAC,UAAA,CAAwB;;;;;IAI/DpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACnDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA3BJ,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,iBAAA,CAAAO,WAAA,CAAAC,QAAA,CAAsB;;;;;IAI7DtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAAxCJ,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAc,iBAAA,CAAAS,OAAA,CAAAC,cAAA,CAAAC,WAAA,CAAAC,KAAA,EAAmC;;;;;IAI1E1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAC/CJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAAnCJ,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,iBAAA,CAAAa,OAAA,CAAAC,UAAA,CAAAC,WAAA,CAAAC,IAAA,EAA8B;;;;;IAIrE9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAChDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA5BJ,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAiB,WAAA,CAAAC,SAAA,CAAuB;;;;;IAGhEhC,EAAA,CAAAE,SAAA,aAA4D;;;;;IAC5DF,EAAA,CAAAE,SAAA,aAAkE;;;;;IAehEF,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACnDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA3BJ,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,iBAAA,CAAAmB,WAAA,CAAAC,QAAA,CAAsB;;;;;IAI7DlC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAAjCJ,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAqB,WAAA,gBAA4B;;;;;IAInEnC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IACjDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA5BJ,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAsB,WAAA,WAAuB;;;;;IAI9DpC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAC/CJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA/BJ,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAc,iBAAA,CAAAuB,WAAA,cAA0B;;;;;IAIjErC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAChDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,GAAoD;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAAzDJ,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAc,iBAAA,CAAAwB,OAAA,CAAAd,cAAA,CAAAe,WAAA,yBAAoD;;;;;IAG7FvC,EAAA,CAAAE,SAAA,aAAqH;;;;;IACrHF,EAAA,CAAAE,SAAA,aAA2H;;;;;;;;IA7B/HF,EAAA,CAAAC,cAAA,cAA6D;IAGzDD,EAAA,CAAAwC,uBAAA,OAAsC;IACpCxC,EAAA,CAAAyC,UAAA,IAAAC,uDAAA,iBAAmD;IACnD1C,EAAA,CAAAyC,UAAA,IAAAE,uDAAA,iBAAkE;IACpE3C,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,OAAyC;IACvCxC,EAAA,CAAAyC,UAAA,IAAAI,uDAAA,iBAAiD;IACjD7C,EAAA,CAAAyC,UAAA,IAAAK,uDAAA,iBAAwE;IAC1E9C,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,OAAoC;IAClCxC,EAAA,CAAAyC,UAAA,IAAAM,uDAAA,iBAAiD;IACjD/C,EAAA,CAAAyC,UAAA,KAAAO,wDAAA,iBAAmE;IACrEhD,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAuC;IACrCxC,EAAA,CAAAyC,UAAA,KAAAQ,wDAAA,iBAA+C;IAC/CjD,EAAA,CAAAyC,UAAA,KAAAS,wDAAA,iBAAsE;IACxElD,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAiD;IAC/CxC,EAAA,CAAAyC,UAAA,KAAAU,wDAAA,iBAAgD;IAChDnD,EAAA,CAAAyC,UAAA,KAAAW,wDAAA,iBAAgG;IAClGpD,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAyC,UAAA,KAAAY,wDAAA,iBAAqH;IACrHrD,EAAA,CAAAyC,UAAA,KAAAa,wDAAA,iBAA2H;IAC7HtD,EAAA,CAAAI,YAAA,EAAQ;;;;IA7BSJ,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuD,UAAA,eAAAC,OAAA,CAAAC,YAAA,CAA2B;IA2BtBzD,EAAA,CAAAa,SAAA,IAA0F;IAA1Fb,EAAA,CAAAuD,UAAA,oBAAAvD,EAAA,CAAA0D,eAAA,IAAAC,GAAA,EAA0F;IAC7E3D,EAAA,CAAAa,SAAA,GAAmF;IAAnFb,EAAA,CAAAuD,UAAA,qBAAAvD,EAAA,CAAA0D,eAAA,IAAAC,GAAA,EAAmF;;;;;IAGxH3D,EAAA,CAAAC,cAAA,cAAuD;IAC3CD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAhM7CJ,EAAA,CAAAC,cAAA,cAAiE;IAO7CD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjCJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9CJ,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAuD;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACrFJ,EAAA,CAAAC,cAAA,eAAoD;IACxCD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA6D;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKhFJ,EAAA,CAAAC,cAAA,oBAA+B;IAGfD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAsD;;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpFJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIpDJ,EAAA,CAAAC,cAAA,oBAA+B;IAGfD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjDJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA+D;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7FJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAI9CJ,EAAA,CAAAC,cAAA,oBAA+B;IAGfD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/CJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxEJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAM5EJ,EAAA,CAAAC,cAAA,eAA4B;IAKJD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEvDJ,EAAA,CAAAC,cAAA,wBAAkB;IAEdD,EAAA,CAAAE,SAAA,kBAIS;IACXF,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,oBAA6B;IAETD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAE9CJ,EAAA,CAAAC,cAAA,wBAAkB;IAEdD,EAAA,CAAAE,SAAA,kBAIS;IACXF,EAAA,CAAAI,YAAA,EAAM;IAKZJ,EAAA,CAAAC,cAAA,eAAuB;IAIDD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAEvDJ,EAAA,CAAAC,cAAA,wBAAkB;IAEdD,EAAA,CAAAE,SAAA,kBAIS;IACXF,EAAA,CAAAI,YAAA,EAAM;IAOdJ,EAAA,CAAAC,cAAA,yBAAsC;IAQ1BD,EAAA,CAAAwC,uBAAA,QAAmC;IACjCxC,EAAA,CAAAyC,UAAA,KAAAmB,gDAAA,iBAAiD;IACjD5D,EAAA,CAAAyC,UAAA,KAAAoB,gDAAA,iBAA+D;IACjE7D,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAwC;IACtCxC,EAAA,CAAAyC,UAAA,KAAAqB,gDAAA,iBAAiD;IACjD9D,EAAA,CAAAyC,UAAA,KAAAsB,gDAAA,iBAAoE;IACtE/D,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAsC;IACpCxC,EAAA,CAAAyC,UAAA,KAAAuB,gDAAA,iBAAmD;IACnDhE,EAAA,CAAAyC,UAAA,KAAAwB,gDAAA,iBAAkE;IACpEjE,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAmC;IACjCxC,EAAA,CAAAyC,UAAA,KAAAyB,gDAAA,iBAAiD;IACjDlE,EAAA,CAAAyC,UAAA,KAAA0B,gDAAA,iBAA+E;IACjFnE,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAkC;IAChCxC,EAAA,CAAAyC,UAAA,KAAA2B,gDAAA,iBAA+C;IAC/CpE,EAAA,CAAAyC,UAAA,KAAA4B,gDAAA,iBAA0E;IAC5ErE,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAwC,uBAAA,QAAuC;IACrCxC,EAAA,CAAAyC,UAAA,KAAA6B,gDAAA,iBAAgD;IAChDtE,EAAA,CAAAyC,UAAA,KAAA8B,gDAAA,iBAAmE;IACrEvE,EAAA,CAAA4C,qBAAA,EAAe;IAEf5C,EAAA,CAAAyC,UAAA,KAAA+B,gDAAA,iBAA4D;IAC5DxE,EAAA,CAAAyC,UAAA,MAAAgC,iDAAA,iBAAkE;IACpEzE,EAAA,CAAAI,YAAA,EAAQ;IAOhBJ,EAAA,CAAAC,cAAA,oBAAoC;IAG9BD,EAAA,CAAAyC,UAAA,MAAAiC,kDAAA,mBA+BM;IACN1E,EAAA,CAAAyC,UAAA,MAAAkC,kDAAA,kBAGM;IACR3E,EAAA,CAAAI,YAAA,EAAmB;;;;IAxLKJ,EAAA,CAAAa,SAAA,IAAuD;IAAvDb,EAAA,CAAAc,iBAAA,CAAA8D,MAAA,CAAApD,cAAA,CAAAoD,MAAA,CAAAC,aAAA,CAAAC,OAAA,CAAAC,WAAA,EAAuD;IACvD/E,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuD,UAAA,YAAAqB,MAAA,CAAAI,aAAA,GAA2B;IACvChF,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAc,iBAAA,CAAA8D,MAAA,CAAAK,YAAA,GAAoB;IACxBjF,EAAA,CAAAa,SAAA,GAA6D;IAA7Db,EAAA,CAAAkF,kBAAA,KAAAlF,EAAA,CAAAmF,WAAA,SAAAP,MAAA,CAAAC,aAAA,CAAAC,OAAA,CAAAM,eAAA,gBAA6D;IAW7CpF,EAAA,CAAAa,SAAA,IAAsD;IAAtDb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqF,WAAA,SAAAT,MAAA,CAAAC,aAAA,CAAAC,OAAA,CAAAQ,iBAAA,EAAsD;IAWtDtF,EAAA,CAAAa,SAAA,IAA+D;IAA/Db,EAAA,CAAAc,iBAAA,CAAA8D,MAAA,CAAApD,cAAA,CAAAoD,MAAA,CAAAC,aAAA,CAAAC,OAAA,CAAAS,mBAAA,EAA+D;IAW/DvF,EAAA,CAAAa,SAAA,IAA0C;IAA1Cb,EAAA,CAAAc,iBAAA,CAAA8D,MAAA,CAAAC,aAAA,CAAAW,eAAA,CAAAC,MAAA,CAA0C;IACvCzF,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAkF,kBAAA,UAAAN,MAAA,CAAAc,UAAA,CAAAC,KAAA,CAAAC,IAAA,UAAqC;IAgB1D5F,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAuD,UAAA,SAAAqB,MAAA,CAAAiB,cAAA,CAAuB,YAAAjB,MAAA,CAAAkB,YAAA;IAgBvB9F,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAuD,UAAA,SAAAqB,MAAA,CAAAmB,eAAA,CAAwB,YAAAnB,MAAA,CAAAkB,YAAA;IAkBxB9F,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAuD,UAAA,SAAAqB,MAAA,CAAAoB,iBAAA,CAA0B,YAAApB,MAAA,CAAAkB,YAAA;IAiBX9F,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAuD,UAAA,eAAAqB,MAAA,CAAAC,aAAA,CAAAW,eAAA,CAA4C;IAgCvCxF,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAAuD,UAAA,oBAAAqB,MAAA,CAAAqB,gBAAA,CAAiC;IACpBjG,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAuD,UAAA,qBAAAqB,MAAA,CAAAqB,gBAAA,CAA0B;IAWjCjG,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAuD,UAAA,SAAAqB,MAAA,CAAAnB,YAAA,CAAAgC,MAAA,KAA6B;IAgCrDzF,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAuD,UAAA,SAAAqB,MAAA,CAAAnB,YAAA,CAAAgC,MAAA,OAA+B;;;ADxPjD/F,KAAK,CAACwG,QAAQ,CAAC,GAAGvG,aAAa,CAAC;AAsBhC,MAuBawG,0BAA0B;EAoErCC,YACUC,wBAAkD,EAClDC,GAAsB;IADtB,KAAAD,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,GAAG,GAAHA,GAAG;IArEb,KAAAzB,aAAa,GAAyB,IAAI;IAC1C,KAAApB,YAAY,GAAU,EAAE;IACxB,KAAA8C,OAAO,GAAG,KAAK;IACf,KAAAvF,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAA0E,UAAU,GAAG,IAAIlG,SAAS,CAAC;MACzBgH,WAAW,EAAE,IAAIjH,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC;MACrCqG,IAAI,EAAE,IAAIrG,WAAW,CAAC,CAAC,CAAC;MACxBkH,SAAS,EAAE,IAAIlH,WAAW,CAAC,IAAImH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;MAC1EC,OAAO,EAAE,IAAIrH,WAAW,CAAC,IAAImH,IAAI,EAAE;KACpC,CAAC;IAEF;IACA,KAAAb,cAAc,GAAuC;MACnDgB,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QACTC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,yBAAyB;QAC1CC,OAAO,EAAE,GAAG;QACZC,IAAI,EAAE;OACP;KACF;IAED,KAAArB,eAAe,GAA2C;MACxDc,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QACTE,IAAI,EAAE,EAAE;QACRE,eAAe,EAAE,CACf,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;OAExD;KACF;IAED,KAAAlB,iBAAiB,GAAsC;MACrDa,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;QACTC,KAAK,EAAE,6BAA6B;QACpCC,IAAI,EAAE,EAAE;QACRE,eAAe,EAAE,SAAS;QAC1BD,WAAW,EAAE,SAAS;QACtBI,WAAW,EAAE;OACd;KACF;IAED,KAAAvB,YAAY,GAAkC;MAC5CwB,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,QAAQ,EAAE;;;KAGf;IAED;IACA,KAAAzB,gBAAgB,GAAa,CAC3B,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,CAChE;IAED;IACQ,KAAA0B,aAAa,GAAmB,EAAE;IAClC,KAAAC,eAAe,GAAG,KAAK,CAAC,CAAC;EAK9B;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQJ,qBAAqBA,CAAA;IAC3B,MAAMK,OAAO,GAAG,IAAI,CAAC3C,UAAU,CAAC4C,YAAY,CAACC,SAAS,CAAC,MAAK;MAC1D,IAAI,CAACT,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IACF,IAAI,CAACH,aAAa,CAACa,IAAI,CAACH,OAAO,CAAC;EAClC;EAEQN,gBAAgBA,CAAA;IACtB,MAAMU,UAAU,GAAG5I,QAAQ,CAAC,IAAI,CAAC+H,eAAe,CAAC,CAC9Cc,IAAI,CACH5I,SAAS,CAAC,CAAC,CAAC,EACZC,SAAS,CAAC,MAAM,IAAI,CAAC+H,iBAAiB,EAAE,CAAC,CAC1C,CACAS,SAAS,EAAE;IAEd,IAAI,CAACZ,aAAa,CAACa,IAAI,CAACC,UAAU,CAAC;EACrC;EAEQX,iBAAiBA,CAAA;IACvB,OAAO,IAAIa,OAAO,CAAEC,OAAO,IAAI;MAC7B,IAAI,CAACrC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACvF,KAAK,GAAG,IAAI;MAEjB,MAAM6H,SAAS,GAAG,IAAI,CAACnD,UAAU,CAACC,KAAK;MACvC,MAAMa,WAAW,GAAGqC,SAAS,CAACrC,WAAW,EAAEsC,QAAQ,CAAC,KAAK,CAAC,GACxD,gBAAgB;MAAG;MACnBD,SAAS,CAACrC,WAAW,EAAEuC,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM;MAE5C,MAAMC,QAAQ,GAAG,MAAM,CAAC,CAAC;MACzB,MAAMpD,IAAI,GAAGiD,SAAS,CAACjD,IAAI,IAAI,CAAC;MAEhC,IAAI,CAACS,wBAAwB,CAAC4C,kBAAkB,CAACD,QAAQ,EAAExC,WAAW,EAAEZ,IAAI,CAAC,CAC1E2C,SAAS,CAAC;QACTW,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;YACjC,IAAI,CAACvE,aAAa,GAAGsE,QAAQ,CAACnC,IAAI;YAClC,IAAI,CAACqC,YAAY,EAAE;YACnB,IAAI,CAACC,gBAAgB,EAAE;;UAEzB,IAAI,CAAC/C,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,GAAG,CAACiD,aAAa,EAAE;UACxBX,OAAO,EAAE;QACX,CAAC;QACD5H,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAG,+BAA+B;UAC5C,IAAI,CAACuF,OAAO,GAAG,KAAK;UACpBiD,OAAO,CAACxI,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UACxC,IAAI,CAACsF,GAAG,CAACiD,aAAa,EAAE;UACxBX,OAAO,EAAE;QACX;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEQU,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACzE,aAAa,EAAE;IAEzB,MAAMgE,SAAS,GAAG,IAAI,CAACnD,UAAU,CAACC,KAAK;IACvC,MAAMa,WAAW,GAAGqC,SAAS,CAACrC,WAAW,EAAEsC,QAAQ,CAAC,KAAK,CAAC,GACxD,gBAAgB,GAChBD,SAAS,CAACrC,WAAW,EAAEuC,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM;IAE5C,MAAMC,QAAQ,GAAG,MAAM;IACvB,MAAMvC,SAAS,GAAG,IAAI,CAAC5B,aAAa,CAAC4E,SAAS,CAAChD,SAAS;IACxD,MAAMG,OAAO,GAAG,IAAI,CAAC/B,aAAa,CAAC4E,SAAS,CAAC7C,OAAO;IAEpD,IAAI,CAACP,wBAAwB,CAACqD,kBAAkB,CAACV,QAAQ,EAAExC,WAAW,EAAEC,SAAS,EAAEG,OAAO,CAAC,CACxF2B,SAAS,CAAC;MACTW,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC3F,YAAY,GAAG0F,QAAQ,CAACQ,SAAS,IAAI,EAAE;;QAE9C,IAAI,CAACrD,GAAG,CAACiD,aAAa,EAAE;MAC1B,CAAC;MACDvI,KAAK,EAAGA,KAAK,IAAI;QACfwI,OAAO,CAACxI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQqI,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACxE,aAAa,EAAE;IAEzB;IACA,MAAM+E,SAAS,GAAG,IAAI,CAAC/E,aAAa,CAACgF,WAAW;IAChD,IAAI,CAAChE,cAAc,CAACgB,MAAM,GAAGiD,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,IAAI,EAAE;IAC1D,IAAI,CAACnE,cAAc,CAACiB,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,GAAG,IAAI,CAACnB,cAAc,CAACgB,MAAM,CAACoD,GAAG,CAClEnI,IAAY,IAAK8H,SAAS,CAAC9H,IAAI,CAAC,IAAI,CAAC,CACvC;IAED;IACA,MAAMoI,UAAU,GAAG,IAAI,CAACrF,aAAa,CAACsF,YAAY;IAClD,IAAI,CAACpE,eAAe,CAACc,MAAM,GAAGiD,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC;IACrD,IAAI,CAACnE,eAAe,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,GAAG8C,MAAM,CAACM,MAAM,CAACF,UAAU,CAAC;IAEjE;IACA,MAAMG,YAAY,GAAG,IAAI,CAACxF,aAAa,CAACyF,cAAc;IACtD,IAAI,CAACtE,iBAAiB,CAACa,MAAM,GAAGiD,MAAM,CAACC,IAAI,CAACM,YAAY,CAAC;IACzD,IAAI,CAACrE,iBAAiB,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,GAAG8C,MAAM,CAACM,MAAM,CAACC,YAAY,CAAC;IAErE,IAAI,CAAC/D,GAAG,CAACiD,aAAa,EAAE;EAC1B;EAEA3I,WAAWA,CAAA;IACT,IAAI,CAACkH,iBAAiB,EAAE;EAC1B;EAEAyC,UAAUA,CAAA;IACR;IACAf,OAAO,CAACgB,GAAG,CAAC,6CAA6C,CAAC;EAC5D;EAEAvF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACJ,aAAa,EAAE,OAAO,eAAe;IAC/C,OAAO,IAAI,CAACA,aAAa,CAACC,OAAO,CAACM,eAAe,GAAG,CAAC,GAAG,aAAa,GAC9D,IAAI,CAACP,aAAa,CAACC,OAAO,CAACM,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,eAAe;EAC3F;EAEAJ,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACH,aAAa,EAAE,OAAO,eAAe;IAC/C,OAAO,IAAI,CAACA,aAAa,CAACC,OAAO,CAACM,eAAe,GAAG,CAAC,GAAG,gBAAgB,GACjE,IAAI,CAACP,aAAa,CAACC,OAAO,CAACM,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe;EAC1F;EAEA5D,cAAcA,CAACiJ,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB;EAEA7I,UAAUA,CAACmJ,UAAkB;IAC3B,OAAO,IAAIrE,IAAI,CAACqE,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;EACzD;;;uBAxNW7E,0BAA0B,EAAAnG,EAAA,CAAAiL,iBAAA,CAAAC,EAAA,CAAAC,wBAAA,GAAAnL,EAAA,CAAAiL,iBAAA,CAAAjL,EAAA,CAAAoL,iBAAA;IAAA;EAAA;;;YAA1BjF,0BAA0B;MAAAkF,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvL,EAAA,CAAAwL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClEvC9L,EAAA,CAAAC,cAAA,aAAgC;UAKdD,EAAA,CAAAG,MAAA,oBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAClCJ,EAAA,CAAAG,MAAA,2BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAA4B;UACgBD,EAAA,CAAAK,UAAA,mBAAA2L,4DAAA;YAAA,OAASD,GAAA,CAAAnL,WAAA,EAAa;UAAA,EAAC;UAC/DZ,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC5BJ,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,iBAAiD;UAAvBD,EAAA,CAAAK,UAAA,mBAAA4L,6DAAA;YAAA,OAASF,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAC9CvK,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC7BJ,EAAA,CAAAG,MAAA,gBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAMfJ,EAAA,CAAAC,cAAA,mBAA+B;UAIZD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAY;UAClCJ,EAAA,CAAAC,cAAA,sBAAmD;UACzBD,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAa;UACpDJ,EAAA,CAAAC,cAAA,sBAAyB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAClDJ,EAAA,CAAAC,cAAA,sBAAyB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAClDJ,EAAA,CAAAC,cAAA,sBAAyB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAItDJ,EAAA,CAAAC,cAAA,yBAAqC;UACxBD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAY;UAClCJ,EAAA,CAAAC,cAAA,sBAAmC;UACXD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAC9CJ,EAAA,CAAAC,cAAA,sBAAuB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAChDJ,EAAA,CAAAC,cAAA,sBAAuB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAa;UAIpDJ,EAAA,CAAAC,cAAA,yBAAqC;UACxBD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAY;UACjCJ,EAAA,CAAAE,SAAA,iBAA0E;UAG5EF,EAAA,CAAAI,YAAA,EAAiB;UAEjBJ,EAAA,CAAAC,cAAA,yBAAqC;UACxBD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAY;UAC/BJ,EAAA,CAAAE,SAAA,iBAAsE;UAGxEF,EAAA,CAAAI,YAAA,EAAiB;UAMvBJ,EAAA,CAAAyC,UAAA,KAAAyJ,0CAAA,kBAGM;UAGNlM,EAAA,CAAAyC,UAAA,KAAA0J,+CAAA,uBAQW;UAGXnM,EAAA,CAAAyC,UAAA,KAAA2J,0CAAA,qBAsMM;UACRpM,EAAA,CAAAI,YAAA,EAAM;;;;;UA7QoEJ,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAAuD,UAAA,aAAAwI,GAAA,CAAAxF,OAAA,CAAoB;UAelFvG,EAAA,CAAAa,SAAA,IAAwB;UAAxBb,EAAA,CAAAuD,UAAA,cAAAwI,GAAA,CAAArG,UAAA,CAAwB;UAsBV1F,EAAA,CAAAa,SAAA,IAA6B;UAA7Bb,EAAA,CAAAuD,UAAA,kBAAA8I,GAAA,CAA6B;UACZrM,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAAuD,UAAA,QAAA8I,GAAA,CAAmB;UAMpCrM,EAAA,CAAAa,SAAA,GAA2B;UAA3Bb,EAAA,CAAAuD,UAAA,kBAAA+I,GAAA,CAA2B;UACVtM,EAAA,CAAAa,SAAA,GAAiB;UAAjBb,EAAA,CAAAuD,UAAA,QAAA+I,GAAA,CAAiB;UAQpDtM,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAuD,UAAA,SAAAwI,GAAA,CAAAxF,OAAA,CAAa;UAMRvG,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAuD,UAAA,SAAAwI,GAAA,CAAA/K,KAAA,KAAA+K,GAAA,CAAAxF,OAAA,CAAuB;UAW5BvG,EAAA,CAAAa,SAAA,GAA+B;UAA/Bb,EAAA,CAAAuD,UAAA,SAAAwI,GAAA,CAAAlH,aAAA,KAAAkH,GAAA,CAAAxF,OAAA,CAA+B;;;qBDhCnC7H,YAAY,EAAA6N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZ/N,aAAa,EAAAgO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbnO,eAAe,EAAAoO,EAAA,CAAAC,SAAA,EACfpO,aAAa,EAAAqO,EAAA,CAAAC,OAAA,EACbrO,eAAe,EAAAsO,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAC,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACf5O,mBAAmB,EAAA6O,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnB/O,cAAc,EAAAgP,GAAA,CAAAC,QAAA,EACdhP,kBAAkB,EAClBC,cAAc,EAAAgP,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdzP,kBAAkB,EAClBC,aAAa,EACbC,wBAAwB,EAAAwP,GAAA,CAAAC,kBAAA,EACxBxP,aAAa,EAAAyP,GAAA,CAAAC,MAAA,EAAAD,GAAA,CAAAE,WAAA,EACbxP,mBAAmB,EAAAyP,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,oBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,oBAAA,EAAAJ,GAAA,CAAAK,kBAAA,EAAAL,GAAA,CAAAM,eAAA,EACnB5P,cAAc,EAAA6P,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKLxJ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}