{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nclass MarkdownPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.initializeRenderer();\n  }\n  initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n    this.markedRenderer.paragraph = text => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = ordered && start !== 1 ? ` start=\"${start}\"` : '';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em;\">${body}</${type}>`;\n    };\n    this.markedRenderer.listitem = text => {\n      return `<li style=\"margin-bottom: 0.5em;\">${text}</li>`;\n    };\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - level * 0.1}em;\">${text}</h${level}>`;\n    };\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true,\n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false\n    });\n  }\n  transform(value) {\n    if (!value) {\n      return '';\n    }\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n      const html = marked(preprocessedMarkdown);\n      const processedHtml = html.replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n      .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n      .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n      .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n      .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n      .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n      .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (match, tag, attrs, content) => {\n        const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n        return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n      });\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      const safeText = value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n  preprocessMarkdown(text) {\n    if (!text) return '';\n    let processed = text.replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Ensure proper spacing for list items\n    .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Ensure proper spacing for code blocks\n    .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n    // Fix incomplete code blocks\n    .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```')\n    // Ensure proper line breaks before and after headers\n    .replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n')\n    // Ensure proper line breaks before and after lists\n    .replace(/(\\n|^)([-*+]\\s.+?)\\n(?![-*+]\\s|\\n)/g, '$1$2\\n\\n')\n    // Ensure proper line breaks before and after paragraphs\n    .replace(/(\\n|^)([^\\n#>*+\\-].+?)\\n(?!\\n|#|>|\\*|\\+|\\-)/g, '$1$2\\n\\n')\n    // Fix special characters that might break markdown\n    .replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>')\n    // Fix hash symbols that might be interpreted as headers\n    .replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n    // Check if the text ends with an incomplete code block and fix it\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n    // Trim extra whitespace but preserve necessary line breaks\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n    return processed;\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport { MarkdownPipe };", "map": {"version": 3, "names": ["marked", "MarkdownPipe", "constructor", "sanitizer", "initialize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "paragraph", "text", "list", "body", "ordered", "start", "type", "startAttr", "listitem", "code", "language", "heading", "level", "setOptions", "renderer", "gfm", "breaks", "pedantic", "smartLists", "smartypants", "xhtml", "headerIds", "transform", "value", "hasCursor", "includes", "textWithoutCursor", "replace", "preprocessedMarkdown", "preprocessMarkdown", "html", "processedHtml", "match", "tag", "attrs", "content", "cleanAttrs", "finalHtml", "bypassSecurityTrustHtml", "error", "console", "safeText", "processed", "codeBlockMatches", "length", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pipes/markdown.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { marked } from 'marked';\n\n@Pipe({\n  name: 'markdown',\n  standalone: true\n})\nexport class MarkdownPipe implements PipeTransform {\n  private markedRenderer: marked.Renderer;\n\n  constructor(private sanitizer: DomSanitizer) {\n    this.initializeRenderer();\n  }\n\n  private initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n\n    this.markedRenderer.paragraph = (text) => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = (ordered && start !== 1) ? ` start=\"${start}\"` : '';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em;\">${body}</${type}>`;\n    };\n\n    this.markedRenderer.listitem = (text) => {\n      return `<li style=\"margin-bottom: 0.5em;\">${text}</li>`;\n    };\n\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - (level * 0.1)}em;\">${text}</h${level}>`;\n    };\n\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true, \n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false \n    });\n  }\n\n  transform(value: string): SafeHtml {\n    if (!value) {\n      return '';\n    }\n\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n      const html = marked(preprocessedMarkdown);\n      const processedHtml = html\n        .replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n        .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n        .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n        .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n        .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n        .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n        .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (match, tag, attrs, content) => {\n          const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n          return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n        });\n\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      const safeText = value\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n\n\n  private preprocessMarkdown(text: string): string {\n    if (!text) return '';\n\n    let processed = text\n      .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n      // Ensure proper spacing for list items\n      .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n      // Ensure proper spacing for code blocks\n      .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n      // Fix incomplete code blocks\n      .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```')\n      // Ensure proper line breaks before and after headers\n      .replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n')\n      // Ensure proper line breaks before and after lists\n      .replace(/(\\n|^)([-*+]\\s.+?)\\n(?![-*+]\\s|\\n)/g, '$1$2\\n\\n')\n      // Ensure proper line breaks before and after paragraphs\n      .replace(/(\\n|^)([^\\n#>*+\\-].+?)\\n(?!\\n|#|>|\\*|\\+|\\-)/g, '$1$2\\n\\n')\n      // Fix special characters that might break markdown\n      .replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>')\n      // Fix hash symbols that might be interpreted as headers\n      .replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n\n    // Check if the text ends with an incomplete code block and fix it\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n\n    // Trim extra whitespace but preserve necessary line breaks\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n\n    return processed;\n  }\n}\n"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;AAE/B,MAIaC,YAAY;EAGvBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIL,MAAM,CAACM,QAAQ,EAAE;IAE3C,IAAI,CAACD,cAAc,CAACE,SAAS,GAAIC,IAAI,IAAK,kCAAkCA,IAAI,MAAM;IACtF,IAAI,CAACH,cAAc,CAACI,IAAI,GAAG,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,KAAI;MAClD,MAAMC,IAAI,GAAGF,OAAO,GAAG,IAAI,GAAG,IAAI;MAClC,MAAMG,SAAS,GAAIH,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAI,WAAWA,KAAK,GAAG,GAAG,EAAE;MACrE,OAAO,IAAIC,IAAI,GAAGC,SAAS,mDAAmDJ,IAAI,KAAKG,IAAI,GAAG;IAChG,CAAC;IAED,IAAI,CAACR,cAAc,CAACU,QAAQ,GAAIP,IAAI,IAAI;MACtC,OAAO,qCAAqCA,IAAI,OAAO;IACzD,CAAC;IAED,IAAI,CAACH,cAAc,CAACW,IAAI,GAAG,CAACA,IAAI,EAAEC,QAAQ,KAAI;MAC5C,OAAO,+NAA+NA,QAAQ,IAAI,MAAM,6BAA6BD,IAAI,eAAe;IAC1S,CAAC;IAED,IAAI,CAACX,cAAc,CAACa,OAAO,GAAG,CAACV,IAAI,EAAEW,KAAK,KAAI;MAC5C,OAAO,KAAKA,KAAK,+EAA+E,GAAG,GAAIA,KAAK,GAAG,GAAI,QAAQX,IAAI,MAAMW,KAAK,GAAG;IAC/I,CAAC;IAEDnB,MAAM,CAACoB,UAAU,CAAC;MAChBC,QAAQ,EAAE,IAAI,CAAChB,cAAc;MAC7BiB,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX,IAAI;MACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,QAAQ,CAAC,wCAAwC,CAAC;MAC1E,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,OAAO,CAAC,2CAA2C,EAAE,EAAE,CAAC;MACxF,MAAMC,oBAAoB,GAAG,IAAI,CAACC,kBAAkB,CAACH,iBAAiB,CAAC;MACvE,MAAMI,IAAI,GAAGrC,MAAM,CAACmC,oBAAoB,CAAC;MACzC,MAAMG,aAAa,GAAGD,IAAI,CACvBH,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;MAAA,CACnCA,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;MAAA,CACvCA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;MAAA,CACzBA,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;MAAA,CAC7BA,OAAO,CAAC,iCAAiC,EAAE,CAACK,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,KAAI;QACzE,MAAMC,UAAU,GAAGF,KAAK,CAACP,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC;QAC7F,OAAO,IAAIM,GAAG,GAAGG,UAAU,IAAID,OAAO,KAAKF,GAAG,GAAG;MACnD,CAAC,CAAC;MAEJ,MAAMI,SAAS,GAAGb,SAAS,GAAGO,aAAa,GAAG,wCAAwC,GAAGA,aAAa;MAEtG,OAAO,IAAI,CAACnC,SAAS,CAAC0C,uBAAuB,CAACD,SAAS,CAAC;KACzD,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAME,QAAQ,GAAGlB,KAAK,CACnBI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACzB,OAAO,IAAI,CAAC/B,SAAS,CAAC0C,uBAAuB,CAACG,QAAQ,CAAC;;EAE3D;EAGQZ,kBAAkBA,CAAC5B,IAAY;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAIyC,SAAS,GAAGzC,IAAI,CACjB0B,OAAO,CAAC,yBAAyB,EAAE,OAAO;IAC3C;IAAA,CACCA,OAAO,CAAC,2BAA2B,EAAE,OAAO;IAC7C;IAAA,CACCA,OAAO,CAAC,gBAAgB,EAAE,SAAS;IACpC;IAAA,CACCA,OAAO,CAAC,sBAAsB,EAAE,gBAAgB;IACjD;IAAA,CACCA,OAAO,CAAC,8BAA8B,EAAE,UAAU;IACnD;IAAA,CACCA,OAAO,CAAC,qCAAqC,EAAE,UAAU;IAC1D;IAAA,CACCA,OAAO,CAAC,8CAA8C,EAAE,UAAU;IACnE;IAAA,CACCA,OAAO,CAAC,sBAAsB,EAAE,8BAA8B;IAC/D;IAAA,CACCA,OAAO,CAAC,+BAA+B,EAAE,YAAY,CAAC;IAEzD;IACA,MAAMgB,gBAAgB,GAAGD,SAAS,CAACV,KAAK,CAAC,YAAY,CAAC;IACtD,IAAIW,gBAAgB,IAAIA,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACnDF,SAAS,IAAI,OAAO;;IAGtB;IACAA,SAAS,GAAGA,SAAS,CAACf,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IAEhD,OAAOe,SAAS;EAClB;;;uBA7GWhD,YAAY,EAAAmD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAAZtD,YAAY;MAAAuD,IAAA;MAAAC,UAAA;IAAA;EAAA;;SAAZxD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}