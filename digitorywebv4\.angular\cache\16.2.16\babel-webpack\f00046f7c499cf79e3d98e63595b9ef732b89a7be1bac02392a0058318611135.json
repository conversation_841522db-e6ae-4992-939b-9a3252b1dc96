{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Spanish (United States) [es-us]\n//! author : bustta : https://github.com/bustta\n//! author : chrisrodz : https://github.com/chrisrodz\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsShortDot = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_'),\n    monthsShort = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_'),\n    monthsParse = [/^ene/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i],\n    monthsRegex = /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i;\n  var esUs = moment.defineLocale('es-us', {\n    months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortDot;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShort[m.month()];\n      } else {\n        return monthsShortDot[m.month()];\n      }\n    },\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n    monthsShortStrictRegex: /^(ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n    weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n    weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'MM/DD/YYYY',\n      LL: 'D [de] MMMM [de] YYYY',\n      LLL: 'D [de] MMMM [de] YYYY h:mm A',\n      LLLL: 'dddd, D [de] MMMM [de] YYYY h:mm A'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[hoy a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      nextDay: function () {\n        return '[mañana a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      lastDay: function () {\n        return '[ayer a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      lastWeek: function () {\n        return '[el] dddd [pasado a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'en %s',\n      past: 'hace %s',\n      s: 'unos segundos',\n      ss: '%d segundos',\n      m: 'un minuto',\n      mm: '%d minutos',\n      h: 'una hora',\n      hh: '%d horas',\n      d: 'un día',\n      dd: '%d días',\n      w: 'una semana',\n      ww: '%d semanas',\n      M: 'un mes',\n      MM: '%d meses',\n      y: 'un año',\n      yy: '%d años'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n\n  return esUs;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}