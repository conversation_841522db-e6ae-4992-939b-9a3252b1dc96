{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r12.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"div\", 28);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_13_div_5_Template, 3, 4, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r12.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r12.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵtext(6, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_37_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r16);\n  }\n}\nfunction ChatBotComponent_ul_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_37_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_42_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r18 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r18);\n  }\n}\nfunction ChatBotComponent_ul_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_42_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 37)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    // Flag to track if conversation history has been loaded\n    this.conversationHistoryLoaded = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          if (!duplicateMessage) {\n            // Add new bot message\n            this.messages.push(message);\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m => m.sender === message.sender && m.text === message.text);\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to just the welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 16,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-title\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"compact-list\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-text\"], [1, \"placeholder-text\"], [1, \"compact-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Tell us about your restaurant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_9_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"delete_sweep\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵtemplate(13, ChatBotComponent_div_13_Template, 6, 8, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 7, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17)(24, \"mat-icon\", 18);\n          i0.ɵɵtext(25, \"summarize\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Summary\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"h4\");\n          i0.ɵɵtext(31, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ChatBotComponent_p_32_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(33, ChatBotComponent_p_33_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 20)(35, \"h4\");\n          i0.ɵɵtext(36, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, ChatBotComponent_ul_37_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(38, ChatBotComponent_p_38_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 20)(40, \"h4\");\n          i0.ɵɵtext(41, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ChatBotComponent_ul_42_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(43, ChatBotComponent_p_43_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 20)(45, \"h4\");\n          i0.ɵɵtext(46, \"Menu Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ChatBotComponent_table_47_Template, 11, 2, \"table\", 24);\n          i0.ɵɵtemplate(48, ChatBotComponent_p_48_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 20)(50, \"h4\");\n          i0.ɵɵtext(51, \"Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, ChatBotComponent_p_52_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(53, ChatBotComponent_p_53_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 450px;\\n  gap: 15px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  min-height: 36px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  line-height: 32px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 10px 16px;\\n  border-radius: 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  margin-left: 8px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 2px;\\n  background-color: #999;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n  transform: translateY(0);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.9999s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 12px;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink-bounce {\\n  0%, 100% {\\n    opacity: 0.4;\\n    transform: translateY(0);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateY(-4px);\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  min-height: 36px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.summary-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 15px;\\n  color: #555;\\n  font-weight: 500;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  color: #616161;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 3px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 16px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n  font-size: 13px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 12px;\\n  margin: 0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e0e0e0;\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  width: 40px;\\n  justify-content: center;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 6px;\\n  width: 6px;\\n  margin: 0 2px;\\n  background-color: #555;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n  50% {\\n    transform: translateY(-2px) scale(1.05);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r12", "timestamp", "ɵɵelement", "ɵɵtemplate", "ChatBotComponent_div_13_div_5_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ctx_r2", "restaurantSummary", "location", "cuisine_r16", "ChatBotComponent_ul_37_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r18", "ChatBotComponent_ul_42_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "conversationHistoryLoaded", "ngOnChanges", "changes", "currentValue", "previousValue", "loadConversationHistory", "ngOnInit", "id", "generateId", "Date", "messages$", "subscribe", "message", "startsWith", "detectChanges", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "push", "sort", "a", "b", "getTime", "isDuplicate", "some", "Math", "abs", "console", "log", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "updateRestaurantSummary", "userMessage", "next", "error", "lowerMessage", "toLowerCase", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "for<PERSON>ach", "existingMessage", "clearConversationHistory", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_9_listener", "ChatBotComponent_div_13_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_p_32_Template", "ChatBotComponent_p_33_Template", "ChatBotComponent_ul_37_Template", "ChatBotComponent_p_38_Template", "ChatBotComponent_ul_42_Template", "ChatBotComponent_p_43_Template", "ChatBotComponent_table_47_Template", "ChatBotComponent_p_48_Template", "ChatBotComponent_p_52_Template", "ChatBotComponent_p_53_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if conversation history has been loaded\n  private conversationHistoryLoaded = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            if (!duplicateMessage) {\n              // Add new bot message\n              this.messages.push(message);\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m =>\n              m.sender === message.sender && m.text === message.text\n            );\n\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to just the welcome message\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Chat interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n        <span class=\"assistant-title\">Tell us about your restaurant</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n          <mat-icon>delete_sweep</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-wrapper\">\n            <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n            <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Improved loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n          <div class=\"typing-text\">AI is thinking...</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <div class=\"summary-title\">\n        <mat-icon class=\"summary-icon\">summarize</mat-icon>\n        <span>Summary</span>\n      </div>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Info</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Pending</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICQ5CC,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnHR,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAAS,SAAA,cAAsE;;IACtET,EAAA,CAAAU,UAAA,IAAAC,sCAAA,kBAAmH;IACrHX,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAP,WAAA,CAAAQ,MAAA,aAAAR,WAAA,CAAAQ,MAAA,YAAgG;IAGpJf,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,UAAA,cAAAZ,EAAA,CAAAgB,WAAA,OAAAT,WAAA,CAAAU,IAAA,GAAAjB,EAAA,CAAAkB,cAAA,CAAqC;IAC/BlB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,SAAAL,WAAA,CAAAQ,MAAA,cAAiC;;;;;IAMvEf,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAS,SAAA,WAAa;IAGbT,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA+BlDH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAc,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtErB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzEH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAiB,WAAA,CAAa;;;;;IAD1EtB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,UAAA,IAAAa,oCAAA,iBAA6E;IAC/EvB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,YAAAY,MAAA,CAAAJ,iBAAA,CAAAK,YAAA,CAAiC;;;;;IAE3DzB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzFH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAqB,aAAA,CAAe;;;;;IAD7E1B,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAU,UAAA,IAAAiB,oCAAA,iBAAgF;IAClF3B,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAR,iBAAA,CAAAS,WAAA,CAAgC;;;;;IAE5D7B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAK1FH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAW,SAAA,CAAiC;IAIjC/B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAY,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrDjC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAA6B,OAAA,CAAAd,iBAAA,CAAAe,cAAA,CAAsC;;;;;IAClFnC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADlFzF,MAqBaiC,gBAAgB;EAsB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAxBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA1B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAY,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAmB1D;IACQ,KAAAC,yBAAyB,GAAG,KAAK;EAdtC;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;MAC3D;MACA,IAAID,OAAO,CAAC,UAAU,CAAC,CAACE,aAAa,KAAKF,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;QAC1E,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC,IAAI,CAACK,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,QAAQ,GAAG,EAAE;IAElB;IACA,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACa,uBAAuB,EAAE;KAC/B,MAAM;MACL;MACA,IAAI,CAACX,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,2KAA2K;QACjLF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;;IAGH;IACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAACT,UAAU,CAACqB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAI8C,OAAO,CAACL,EAAE,CAACM,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAChB,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAIF,OAAO,CAAC9C,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMiD,oBAAoB,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKK,OAAO,CAACL,EAAE,CAAC;QAE9E,IAAIQ,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAACrB,QAAQ,CAACqB,oBAAoB,CAAC,GAAGH,OAAO;SAC9C,MAAM;UACL;UACA,MAAMM,gBAAgB,GAAG,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACnD,MAAM,KAAK,KAAK,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IAAI,CAACiD,CAAC,CAACjD,IAAI,CAACoD,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,IAAI,CAACF,gBAAgB,EAAE;YACrB;YACA,IAAI,CAACxB,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;YAE3B;YACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;;;OAGhF,MAAM,IAAIb,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAM4D,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,IACvB4D,IAAI,CAACC,GAAG,CAACZ,CAAC,CAAC1D,SAAS,CAACkE,OAAO,EAAE,GAAGb,OAAO,CAACrD,SAAS,CAACkE,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACC,WAAW,EAAE;UAChBI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;UACjD;UACA,IAAI,CAAC0B,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;UAE3B;UACA,IAAI,CAAClB,QAAQ,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChE,SAAS,CAACkE,OAAO,EAAE,GAAGD,CAAC,CAACjE,SAAS,CAACkE,OAAO,EAAE,CAAC;SAC5E,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEnB,OAAO,CAAC5C,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACsB,EAAE,CAACwB,aAAa,EAAE;MAEvB;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACnC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACoC,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACnC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACmC,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC7C,UAAU,CAAC8C,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzC,cAAc,CAAC0C,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC7C,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC+C,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC3C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM2C,aAAa,GAAG,IAAI,CAAC7C,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA;IAEA;IACA,IAAI,CAAC2B,uBAAuB,CAACD,aAAa,CAAC;IAE3C;IACA,MAAME,WAAW,GAAgB;MAC/BnC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBxC,IAAI,EAAEwE,aAAa;MACnB1E,MAAM,EAAE,MAAM;MACdP,SAAS,EAAE,IAAIkD,IAAI;KACpB;IAED;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACnD,MAAM,KAAK,MAAM,IACnBmD,CAAC,CAACjD,IAAI,KAAKwE,aAAa,CACzB;IAED,IAAI,CAACd,WAAW,EAAE;MAChB,IAAI,CAAChC,QAAQ,CAAC2B,IAAI,CAACqB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC7C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;IACvB,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAAC3C,UAAU,CAAC+C,WAAW,CAAC,IAAI,CAAC5C,QAAQ,EAAEgD,aAAa,CAAC,CAAC7B,SAAS,CAAC;MAClEgC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAAC/C,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACzB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAAC+C,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACjD,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQ2B,uBAAuBA,CAAC7B,OAAe;IAC7C,MAAMiC,YAAY,GAAGjC,OAAO,CAACkC,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACzB,QAAQ,CAAC,UAAU,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IACrEyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IACjEyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAACjD,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAAC2E,kBAAkB,CAACF,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAM4B,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACJ,YAAY,CAAC;MACpD,IAAIG,QAAQ,CAAChE,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACb,iBAAiB,CAACK,YAAY,GAAG,CAAC,GAAG,IAAI0E,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACK,YAAY,EAAE,GAAGwE,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIH,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,YAAY,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAMxC,WAAW,GAAG,IAAI,CAACqE,gBAAgB,CAACJ,YAAY,CAAC;MACvD,IAAIjE,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACb,iBAAiB,CAACS,WAAW,GAAG,CAAC,GAAG,IAAIsE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACS,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIiE,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAM+B,cAAc,GAAGN,YAAY,CAACO,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAAChF,iBAAiB,CAACW,SAAS,GAAGuE,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIN,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMkC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACJ,YAAY,CAAC;QACtD,IAAIS,UAAU,CAACtE,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACb,iBAAiB,CAACY,cAAc,GAAG,CAAC,GAAG,IAAImE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/E,iBAAiB,CAACY,cAAc,EAAE,GAAGuE,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIT,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAACjD,iBAAiB,CAACe,cAAc,GAAG,IAAI,CAAC6D,kBAAkB,CAACF,YAAY,CAAC;;IAG/E,IAAI,CAACvD,EAAE,CAACwB,aAAa,EAAE;EACzB;EAEA;;;;EAIQiC,kBAAkBA,CAACnC,OAAe;IACxC;IACA,MAAM2C,cAAc,GAAG3C,OAAO,CAC3B4C,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAACrC,OAAe;IACtC;IACA,IAAIA,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,IAAIR,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOR,OAAO,CACXgD,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzB,IAAI,EAAE,CAAC,CACxB0B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC9E,MAAM,GAAG,CAAC,CAAC,CAC/B6E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACnC,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAoD,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAChC,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAqC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQlE,UAAUA,CAAA;IAChB,OAAOoB,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGjD,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,MAAc,EAAEnE,OAAoB;IAC5C,OAAOA,OAAO,CAACL,EAAE;EACnB;EAEA;;;EAGAF,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACQ,yBAAyB,EAAE;MACpD;;IAGF;IACA,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAACJ,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACP,UAAU,CAACgB,uBAAuB,CAAC,IAAI,CAACb,QAAQ,EAAE,KAAK,CAAC,CAACmB,SAAS,CAAC;MACtEgC,IAAI,EAAGjD,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,IAAI,CAACU,QAAQ,GAAG,EAAE;UAElB;UACAA,QAAQ,CAACsF,OAAO,CAACpE,OAAO,IAAG;YACzB;YACA,MAAMqE,eAAe,GAAG,IAAI,CAACvF,QAAQ,CAACyB,IAAI,CAACF,CAAC,IAC1CA,CAAC,CAACnD,MAAM,KAAK8C,OAAO,CAAC9C,MAAM,IAAImD,CAAC,CAACjD,IAAI,KAAK4C,OAAO,CAAC5C,IAAI,CACvD;YAED,IAAI,CAACiH,eAAe,EAAE;cACpB,IAAI,CAACvF,QAAQ,CAAC2B,IAAI,CAACT,OAAO,CAAC;;YAG7B;YACA,IAAIA,OAAO,CAAC9C,MAAM,KAAK,MAAM,EAAE;cAC7B,IAAI,CAAC2E,uBAAuB,CAAC7B,OAAO,CAAC5C,IAAI,CAAC;;UAE9C,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAAC0B,QAAQ,CAACV,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACU,QAAQ,CAAC2B,IAAI,CAAC;cACjBd,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;cACrBxC,IAAI,EAAE,2KAA2K;cACjLF,MAAM,EAAE,KAAK;cACbP,SAAS,EAAE,IAAIkD,IAAI;aACpB,CAAC;;SAEL,MAAM;UACL;UACA,IAAI,CAACf,QAAQ,GAAG,CAAC;YACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,2KAA2K;YACjLF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CAAC;;QAGJ,IAAI,CAACb,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAACwB,aAAa,EAAE;QACvB,IAAI,CAACkB,cAAc,EAAE;MACvB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACH,QAAQ,GAAG,CAAC;UACfa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBxC,IAAI,EAAE,2KAA2K;UACjLF,MAAM,EAAE,KAAK;UACbP,SAAS,EAAE,IAAIkD,IAAI;SACpB,CAAC;QAEF,IAAI,CAACnB,EAAE,CAACwB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAoE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACtF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;IAEvB;IACA,IAAI,CAAC3C,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED;IACA,IAAI,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,UAAU,CAAC6F,wBAAwB,CAAC,IAAI,CAAC1F,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAACmB,SAAS,CAAC;QAC5EgC,IAAI,EAAEA,CAAA,KAAK;UACTb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAACrC,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED;UACA,IAAI,CAACT,yBAAyB,GAAG,KAAK;UAEtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAAClD,QAAQ,GAAG,CACd;YACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrBxC,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIkD,IAAI;WACpB,CACF;UAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;UACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACtC,QAAQ,GAAG,CACd;QACEa,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBxC,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIkD,IAAI;OACpB,CACF;MAED,IAAI,CAACT,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACJ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACN,EAAE,CAACwB,aAAa,EAAE;MACvB,IAAI,CAACkB,cAAc,EAAE;;EAEzB;;;uBA1fW7C,gBAAgB,EAAApC,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtI,EAAA,CAAAoI,iBAAA,CAAApI,EAAA,CAAAuI,iBAAA,GAAAvI,EAAA,CAAAoI,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBrG,gBAAgB;MAAAsG,SAAA;MAAAC,MAAA;QAAAlG,QAAA;QAAAC,UAAA;MAAA;MAAAkG,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,oBAAA,EAAA9I,EAAA,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC7BrJ,EAAA,CAAAC,cAAA,aAAyB;UAKWD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEpEH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAuJ,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAClFnI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKvCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAU,UAAA,KAAA+I,gCAAA,iBAOM;UAGNzJ,EAAA,CAAAU,UAAA,KAAAgJ,gCAAA,kBAOM;UACR1J,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAuJ,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA1G,cAAA,GAAAgH,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAArC,UAAA,CAAA2C,MAAA,CAAkB;UAAA,EAFD;UADnC5J,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAuJ,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAC1DrF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAGKD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIxBH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAU,UAAA,KAAAqJ,8BAAA,gBAA0E;UAC1E/J,EAAA,CAAAU,UAAA,KAAAsJ,8BAAA,gBAA2E;UAC7EhK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAU,UAAA,KAAAuJ,+BAAA,iBAEK;UACLjK,EAAA,CAAAU,UAAA,KAAAwJ,8BAAA,gBAA2F;UAC7FlK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAU,UAAA,KAAAyJ,+BAAA,iBAEK;UACLnK,EAAA,CAAAU,UAAA,KAAA0J,8BAAA,gBAA0F;UAC5FpK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAU,UAAA,KAAA2J,kCAAA,qBASQ;UACRrK,EAAA,CAAAU,UAAA,KAAA4J,8BAAA,gBAAiF;UACnFtK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAU,UAAA,KAAA6J,8BAAA,gBAAsF;UACtFvK,EAAA,CAAAU,UAAA,KAAA8J,8BAAA,gBAAiF;UACnFxK,EAAA,CAAAG,YAAA,EAAM;;;UArFmBH,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA3G,QAAA,CAAa,iBAAA2G,GAAA,CAAAvB,SAAA;UAUhC/H,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAxG,oBAAA,CAA0B;UAavB9C,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAY,UAAA,YAAA0I,GAAA,CAAA1G,cAAA,CAA4B,aAAA0G,GAAA,CAAAzG,YAAA;UAKwB7C,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAY,UAAA,cAAA0I,GAAA,CAAA1G,cAAA,CAAA0C,IAAA,MAAAgE,GAAA,CAAAzG,YAAA,CAAmD;UAkB1G7C,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAgC;UAChCrB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAC,QAAA,CAAiC;UAKhCrB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,KAA+C;UAGhDjC,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,OAAiD;UAKhDjC,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,KAA8C;UAG/CjC,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAS,WAAA,CAAAI,MAAA,OAAgD;UAKtBjC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,KAAqC;UAU/D/B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAW,SAAA,OAAuC;UAKvC/B,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAY,UAAA,SAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAsC;UACtCnC,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,UAAA0I,GAAA,CAAAlI,iBAAA,CAAAe,cAAA,CAAuC;;;qBD9E/C9C,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZvL,WAAW,EAAAwL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1L,mBAAmB,EACnBC,eAAe,EAAA0L,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACf3L,aAAa,EACbC,kBAAkB,EAAA2L,EAAA,CAAAC,YAAA,EAClB3L,aAAa,EAAA4L,EAAA,CAAAC,OAAA,EACb5L,cAAc,EAAA6L,EAAA,CAAAC,QAAA,EACd7L,wBAAwB,EACxBC,gBAAgB,EAAA6L,EAAA,CAAAC,UAAA,EAEhB7L,YAAY;MAAA8L,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH1J,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}