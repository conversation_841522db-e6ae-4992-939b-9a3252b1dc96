"""
Smart Dashboard Router - LLM-powered dashboard generation
"""
from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any
import os
from datetime import datetime
from app.utility.report import grnStatusReport
from app.utility.dashboard_agents import smart_ask_dashboard
from dotenv import load_dotenv
load_dotenv()

router = APIRouter()
security = HTTPBearer()

async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

@router.get("/config")
async def get_dashboard_config(_: str = Depends(authenticate)):
    return {
        "status": "success",
        "data": {
            "chart_colors": ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],
            "chart_types": {"bar": "Bar Chart", "line": "Line Chart", "doughnut": "Doughnut Chart", "pie": "Pie Chart"},
            "currency": {"code": "INR", "symbol": "₹"},
            "dashboard_types": ["purchase", "grn"]
        }
    }

@router.post("/smart_ask")
async def smart_ask(request: Dict[str, Any], _: str = Depends(authenticate)):
    filters = request.get('filters', {})
    user_query = request.get('user_query', '')
    tenant_id = request.get('tenant_id', '')
    use_default_charts = request.get('use_default_charts', False)

    job = {
        'tenantId': tenant_id,
        'details': {
            'selectedRestaurants': filters.get('locations', []),
            'selectedBaseDate': filters.get('baseDate', 'deliveryDate'),
            'startDate': datetime.strptime(filters.get('startDate'), '%Y-%m-%d'),
            'endDate': datetime.strptime(filters.get('endDate'), '%Y-%m-%d')
        }
    }
    grn_df = grnStatusReport(job)
    dashboard_data = smart_ask_dashboard(grn_df, user_query, use_default_charts)
    return {"status": "success", "data": dashboard_data}


