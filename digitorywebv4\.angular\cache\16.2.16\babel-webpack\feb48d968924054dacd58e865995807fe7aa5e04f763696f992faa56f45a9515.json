{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nlet LoginGuard = /*#__PURE__*/(() => {\n  class LoginGuard {\n    constructor(router) {\n      this.router = router;\n    }\n    canActivate() {\n      const currentUser = JSON.parse(sessionStorage.getItem('user'));\n      if (currentUser && currentUser.token) {\n        this.router.navigate(['/dashboard/home']);\n        return false;\n      }\n      return true;\n    }\n    static {\n      this.ɵfac = function LoginGuard_Factory(t) {\n        return new (t || LoginGuard)(i0.ɵɵinject(i1.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LoginGuard,\n        factory: LoginGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LoginGuard;\n})();\nexport { LoginGuard };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}