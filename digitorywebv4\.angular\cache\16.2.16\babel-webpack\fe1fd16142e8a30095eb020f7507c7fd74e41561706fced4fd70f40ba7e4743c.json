{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 49)(1, \"mat-icon\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getReportIcon(i_r8));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r7.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r9.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r10.displayName, \" \");\n  }\n}\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.selectedTab = 0;\n    // GRN Filter options\n    this.locations = [];\n    this.baseDates = [{\n      displayName: \"GRN Date(System Entry Date)\",\n      value: \"deliveryDate\"\n    }, {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    }, {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'Purchase Dashboard',\n      active: true\n    }, {\n      label: 'Sales Dashboard',\n      active: false\n    }];\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: false\n            }));\n          } else {\n            console.warn('No locations found for user');\n            this.locations = [];\n          }\n        },\n        error: err => {\n          console.error('Error loading locations:', err);\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  getReportIcon(index) {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 100,\n      vars: 20,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"reports-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"appearance\", \"outline\", 1, \"report-dropdown\"], [\"placeholder\", \"Select Report Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-input-row\"], [1, \"ai-title-compact\"], [1, \"ai-icon\"], [1, \"ai-title\"], [1, \"ai-status\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask questions about your data in natural language...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"charts-container\"], [3, \"value\"], [1, \"option-icon\"], [1, \"filter-badge\"], [1, \"selection-count\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"Reports\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-form-field\", 7)(10, \"mat-select\", 8);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_10_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_10_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(11, SmartDashboardComponent_mat_option_11_Template, 4, 3, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 3)(14, \"div\", 4)(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"h3\", 6);\n          i0.ɵɵtext(18, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, SmartDashboardComponent_span_19_Template, 2, 1, \"span\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 13)(22, \"label\", 14)(23, \"mat-icon\", 15);\n          i0.ɵɵtext(24, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 16);\n          i0.ɵɵtext(26, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, SmartDashboardComponent_span_27_Template, 2, 1, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-form-field\", 18)(29, \"mat-select\", 19);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_29_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(30, SmartDashboardComponent_mat_option_30_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 14)(33, \"mat-icon\", 15);\n          i0.ɵɵtext(34, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 16);\n          i0.ɵɵtext(36, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 18)(38, \"mat-select\", 20);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_38_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 13)(41, \"label\", 14)(42, \"mat-icon\", 15);\n          i0.ɵɵtext(43, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 16);\n          i0.ɵɵtext(45, \"Start Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 18)(47, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_47_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"mat-datepicker-toggle\", 22)(49, \"mat-datepicker\", null, 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 13)(52, \"label\", 14)(53, \"mat-icon\", 15);\n          i0.ɵɵtext(54, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 16);\n          i0.ɵɵtext(56, \"End Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"mat-form-field\", 18)(58, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_58_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"mat-datepicker-toggle\", 22)(60, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(62, \"div\", 26)(63, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_63_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(64, \"mat-icon\");\n          i0.ɵɵtext(65, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_67_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"div\", 29)(72, \"div\", 30)(73, \"div\", 31)(74, \"div\", 32)(75, \"div\", 33)(76, \"mat-icon\", 34);\n          i0.ɵɵtext(77, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 35);\n          i0.ɵɵtext(79, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"span\", 36);\n          i0.ɵɵtext(81);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 37)(83, \"mat-form-field\", 38)(84, \"input\", 39);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_84_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_84_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_85_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(86, \"mat-icon\");\n          i0.ɵɵtext(87, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(88, \"div\", 41)(89, \"div\", 42)(90, \"div\", 43)(91, \"div\", 44)(92, \"div\", 45)(93, \"mat-icon\");\n          i0.ɵɵtext(94, \"insights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"h4\", 46);\n          i0.ɵɵtext(96, \"Your Dashboard Awaits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"p\", 47);\n          i0.ɵɵtext(98, \" Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(99, \"div\", 48);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(50);\n          const _r6 = i0.ɵɵreference(61);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(21);\n          i0.ɵɵclassProp(\"ready\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, MatCardModule, MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, MatFormFieldModule, i6.MatFormField, i6.MatSuffix, MatSelectModule, i7.MatSelect, i8.MatOption, MatInputModule, i9.MatInput, MatDatepickerModule, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, MatNativeDateModule, FormsModule, i11.DefaultValueAccessor, i11.NgControlStatus, i11.NgModel],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.75rem 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:first-child {\\n  border-bottom: 1px solid #f3f4f6;\\n  padding-bottom: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reports-section[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.25rem;\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.75rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  padding-top: 0.75rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #4b5563;\\n  border-color: #d1d5db;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n  transform: translateY(-1px);\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e55a2b, #ff6b35);\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0.5rem;\\n  gap: 0.5rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  padding: 0.5rem 0.75rem;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #ff6b35, #ff8c5a);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6b7280;\\n  padding: 1px 6px;\\n  border-radius: 0.375rem;\\n  background: #f3f4f6;\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 86px);\\n    min-height: calc(100vh - 86px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 50vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n    margin-left: auto;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n  position: relative !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.5rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n  position: relative !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 32px 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.8rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.8rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n  position: absolute !important;\\n  right: 8px !important;\\n  top: 50% !important;\\n  transform: translateY(-50%) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 18px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-value {\\n  padding-right: 24px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-placeholder {\\n  padding-right: 24px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 36px !important;\\n  line-height: 36px !important;\\n  padding: 0 12px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.5rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.875rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.8rem !important;\\n  padding-right: 32px !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 18px !important;\\n  height: 18px !important;\\n  color: #6b7280 !important;\\n  position: absolute !important;\\n  right: 8px !important;\\n  top: 50% !important;\\n  transform: translateY(-50%) !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 14px !important;\\n  height: 14px !important;\\n}\\n  .mat-mdc-form-field-icon-suffix {\\n  position: relative !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "first", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r8", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getReportIcon", "ɵɵtextInterpolate1", "tab_r7", "label", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r9", "value", "baseDate_r10", "displayName", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "user", "getCurrentUser", "loadLocations", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "tenantId", "getLocations", "pipe", "subscribe", "next", "res", "result", "branches", "map", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "console", "warn", "error", "err", "resetFilters", "location", "count", "icons", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_10_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_10_listener", "ɵɵtemplate", "SmartDashboardComponent_mat_option_11_Template", "SmartDashboardComponent_span_19_Template", "SmartDashboardComponent_span_27_Template", "SmartDashboardComponent_Template_mat_select_valueChange_29_listener", "SmartDashboardComponent_mat_option_30_Template", "SmartDashboardComponent_Template_mat_select_valueChange_38_listener", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_input_ngModelChange_47_listener", "ɵɵelement", "SmartDashboardComponent_Template_input_ngModelChange_58_listener", "SmartDashboardComponent_Template_button_click_63_listener", "SmartDashboardComponent_Template_button_click_67_listener", "SmartDashboardComponent_Template_input_ngModelChange_84_listener", "SmartDashboardComponent_Template_input_keydown_84_listener", "SmartDashboardComponent_Template_button_click_85_listener", "_r5", "_r6", "ɵɵclassProp", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "i6", "MatFormField", "MatSuffix", "i7", "MatSelect", "i8", "MatOption", "i9", "MatInput", "i10", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i11", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { first } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  selectedTab = 0;\n  user: any;\n\n  // GRN Filter options\n  locations: any[] = [];\n\n  baseDates = [\n    {\n      displayName: \"GRN Date(System Entry Date)\",\n      value: \"deliveryDate\"\n    },\n    {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    },\n    {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'deliveryDate';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'Purchase Dashboard', active: true },\n    { label: 'Sales Dashboard', active: false },\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n  }\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: false\n              }));\n            } else {\n              console.warn('No locations found for user');\n              this.locations = [];\n            }\n          },\n          error: (err) => {\n            console.error('Error loading locations:', err);\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n\n  getReportIcon(index: number): string {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Selection -->\n    <div class=\"sidebar-section reports-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">assessment</mat-icon>\n          <h3 class=\"section-title\">Reports</h3>\n        </div>\n      </div>\n      <mat-form-field appearance=\"outline\" class=\"report-dropdown\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Select Report Type\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <mat-icon class=\"option-icon\">{{ getReportIcon(i) }}</mat-icon>\n            {{ tab.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Assistant Section - Compact -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-input-row\">\n          <div class=\"ai-title-compact\">\n            <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n            <span class=\"ai-title\">Smart Dashboard Assistant</span>\n            <span class=\"ai-status\" [class.ready]=\"getActiveFiltersCount() > 0\">\n              {{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}\n            </span>\n          </div>\n\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask questions about your data in natural language...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Your Dashboard Awaits</h4>\n            <p class=\"empty-state-description\">\n              Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data.\n            </p>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICA5BC,EAAA,CAAAC,cAAA,qBAAgE;IAChCD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IAHuCH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAW;IAC/BL,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,IAAA,EAAsB;IACpDL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAWAZ,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAG,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,OAAAK,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEjB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,WAAA,CAAAC,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAQ,WAAA,CAAAN,KAAA,MACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAgB,YAAA,CAAAD,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAU,YAAA,CAAAC,WAAA,MACF;;;AD/Cd,MAkBaC,uBAAuB;EAmClCC,YACUC,WAAwB,EACxBC,gBAAkC;IADlC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IApC1B,KAAAC,WAAW,GAAG,CAAC;IAGf;IACA,KAAAC,SAAS,GAAU,EAAE;IAErB,KAAAC,SAAS,GAAG,CACV;MACEP,WAAW,EAAE,6BAA6B;MAC1CF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,CACF;IAED;IACA,KAAAH,iBAAiB,GAAa,EAAE;IAChC,KAAAa,gBAAgB,GAAG,cAAc;IACjC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAErB,KAAK,EAAE,oBAAoB;MAAEsB,MAAM,EAAE;IAAI,CAAE,EAC7C;MAAEtB,KAAK,EAAE,iBAAiB;MAAEsB,MAAM,EAAE;IAAK,CAAE,CAC5C;EAKG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,WAAW,CAACa,cAAc,EAAE;IAC7C,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACd,WAAW,GAAGc,KAAK;IACxB,IAAI,CAACP,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACR,MAAM,GAAGS,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;MAC3B;MACA,IAAI,CAACb,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;EAAA;EAGFb,aAAaA,CAAA;IACX,IAAI,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACgB,QAAQ,EAAE;MACnC,IAAI,CAAC3B,gBAAgB,CAAC4B,YAAY,CAAC,IAAI,CAACjB,IAAI,CAACgB,QAAQ,CAAC,CACnDE,IAAI,CAACvD,KAAK,EAAE,CAAC,CACbwD,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAChC,SAAS,GAAG8B,GAAG,CAACE,QAAQ,CAACC,GAAG,CAAEC,MAAW,KAAM;cAClD1C,KAAK,EAAE0C,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEpD,KAAK,EAAEiD,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;WACJ,MAAM;YACLC,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;YAC3C,IAAI,CAAC1C,SAAS,GAAG,EAAE;;QAEvB,CAAC;QACD2C,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;UAC9C,IAAI,CAAC5C,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEA6C,YAAYA,CAAA;IACV;IACA,IAAI,CAACxD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACa,gBAAgB,GAAG,cAAc;IACtC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACJ,SAAS,CAACc,OAAO,CAACgC,QAAQ,IAAIA,QAAQ,CAACN,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEArD,qBAAqBA,CAAA;IACnB,IAAI4D,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1D,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAEyD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC7C,gBAAgB,KAAK,cAAc,EAAE6C,KAAK,EAAE;IACrD,IAAI,IAAI,CAAC5C,SAAS,EAAE4C,KAAK,EAAE;IAC3B,IAAI,IAAI,CAAC3C,OAAO,EAAE2C,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;EAEAjE,aAAaA,CAAC+B,KAAa;IACzB,MAAMmC,KAAK,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;IAC7E,OAAOA,KAAK,CAACnC,KAAK,CAAC,IAAI,YAAY;EACrC;;;uBApHWlB,uBAAuB,EAAAtB,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvB1D,uBAAuB;MAAA2D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnF,EAAA,CAAAoF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCpC1F,EAAA,CAAAC,cAAA,aAAuC;UAOED,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAAA,CAAAC,cAAA,wBAA6D;UAC/CD,EAAA,CAAA4F,UAAA,yBAAAC,oEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAjE,WAAA,GAAAoE,MAAA;UAAA,EAAuB,6BAAAC,wEAAAD,MAAA;YAAA,OAAoBH,GAAA,CAAApD,WAAA,CAAAuD,MAAA,CAAA3E,KAAA,CAAyB;UAAA,EAA7C;UACjCnB,EAAA,CAAAgG,UAAA,KAAAC,8CAAA,wBAGa;UACfjG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAgG,UAAA,KAAAE,wCAAA,mBAEO;UACTlG,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAgG,UAAA,KAAAG,wCAAA,mBAEO;UACTnG,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA4F,UAAA,yBAAAQ,oEAAAN,MAAA;YAAA,OAAAH,GAAA,CAAA3E,iBAAA,GAAA8E,MAAA;UAAA,EAA6B;UACvC9F,EAAA,CAAAgG,UAAA,KAAAK,8CAAA,wBAEa;UACfrG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA4F,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAH,GAAA,CAAA9D,gBAAA,GAAAiE,MAAA;UAAA,EAA4B;UACtC9F,EAAA,CAAAgG,UAAA,KAAAO,8CAAA,wBAEa;UACfvG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA4F,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAH,GAAA,CAAA7D,SAAA,GAAAgE,MAAA;UAAA,EAAuB;UAHzB9F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAyG,SAAA,iCAA6E;UAE/EzG,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA4F,UAAA,2BAAAc,iEAAAZ,MAAA;YAAA,OAAAH,GAAA,CAAA5D,OAAA,GAAA+D,MAAA;UAAA,EAAqB;UAHvB9F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAyG,SAAA,iCAA2E;UAE7EzG,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA4F,UAAA,mBAAAe,0DAAA;YAAA,OAAShB,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UACnExE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAA4F,UAAA,mBAAAgB,0DAAA;YAAA,OAASjB,GAAA,CAAAxC,YAAA,EAAc;UAAA,EAAC;UAClFnD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvDH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGTH,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAA4F,UAAA,2BAAAiB,iEAAAf,MAAA;YAAA,OAAAH,GAAA,CAAA3D,WAAA,GAAA8D,MAAA;UAAA,EAAyB,qBAAAgB,2DAAAhB,MAAA;YAAA,OACdH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,CAAkB;UAAA,EADJ;UAJ3B9F,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAMC;UAFCD,EAAA,CAAA4F,UAAA,mBAAAmB,0DAAA;YAAA,OAASpB,GAAA,CAAA/C,WAAA,EAAa;UAAA,EAAC;UAGvB5C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAOXD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/BH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,aAAmC;UACjCD,EAAA,CAAAE,MAAA,sHACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAyG,SAAA,eAEM;UACRzG,EAAA,CAAAG,YAAA,EAAM;;;;;UAxKQH,EAAA,CAAAM,SAAA,IAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAuF,GAAA,CAAAjE,WAAA,CAAuB;UACL1B,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAuF,GAAA,CAAA1D,IAAA,CAAS;UAcTjC,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAAuF,GAAA,CAAA7E,qBAAA,OAAiC;UAY5Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAuF,GAAA,CAAA3E,iBAAA,CAAAC,MAAA,KAAkC;UAKrDjB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAuF,GAAA,CAAA3E,iBAAA,CAA6B;UACNhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAuF,GAAA,CAAAhE,SAAA,CAAY;UAcnC3B,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAuF,GAAA,CAAA9D,gBAAA,CAA4B;UACL7B,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAuF,GAAA,CAAA/D,SAAA,CAAY;UAgB7C5B,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA4G,GAAA,CAA6B,YAAArB,GAAA,CAAA7D,SAAA;UAKE9B,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA4G,GAAA,CAAmB;UAclDhH,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA6G,GAAA,CAA2B,YAAAtB,GAAA,CAAA5D,OAAA;UAKI/B,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA6G,GAAA,CAAiB;UA6B1BjH,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAkH,WAAA,UAAAvB,GAAA,CAAA7E,qBAAA,OAA2C;UACjEd,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAU,kBAAA,MAAAiF,GAAA,CAAA7E,qBAAA,6DACF;UASId,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAuF,GAAA,CAAA3D,WAAA,CAAyB,aAAA2D,GAAA,CAAA7E,qBAAA;UAU3Bd,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAAuF,GAAA,CAAA3D,WAAA,CAAAa,IAAA,MAAA8C,GAAA,CAAA7E,qBAAA,SAAiE;;;qBDnI3EzB,YAAY,EAAA8H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,aAAa,EACbC,eAAe,EAAA+H,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfhI,aAAa,EAAAiI,EAAA,CAAAC,OAAA,EACbjI,kBAAkB,EAAAkI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBnI,eAAe,EAAAoI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACftI,cAAc,EAAAuI,EAAA,CAAAC,QAAA,EACdvI,mBAAmB,EAAAwI,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB1I,mBAAmB,EACnBC,WAAW,EAAA0I,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFtH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}