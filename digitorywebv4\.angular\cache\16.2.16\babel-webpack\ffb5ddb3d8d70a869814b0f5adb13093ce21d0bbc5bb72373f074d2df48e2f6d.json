{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/share-data.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/auth.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/toolbar\";\nimport * as i10 from \"@angular/material/menu\";\nconst _c0 = [\"allSelected\"];\nfunction DashboardToolbarComponent_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 19);\n    i0.ɵɵlistener(\"error\", function DashboardToolbarComponent_img_3_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.handleLogoError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 21)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", item_r7.path);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.title);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardToolbarComponent_ng_container_5_ng_container_1_Template, 6, 3, \"ng-container\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.menuItems);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass DashboardToolbarComponent {\n  constructor(selectedBranchesService, dialog, router, auth, sharedData, cd, notify) {\n    this.selectedBranchesService = selectedBranchesService;\n    this.dialog = dialog;\n    this.router = router;\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.showNavbarToggleButton = false;\n    this.menuItems = [];\n    this.logoUrl = '';\n    this.toggleMenu = new EventEmitter();\n    this.branchList = [];\n    this.branches = new FormControl('');\n    this.globalLocation = new FormControl();\n    this.VendorBank = [];\n    this.vendorFilterCtrl = new FormControl();\n    this.vendorsBanks = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.cardDesc = '';\n    this.showBanner = false;\n    // Track if menu items are loaded\n    this.menuItemsLoaded = false;\n    this.message = 'Update to latest version by pressing';\n    this.rolesList = [];\n    this.links = [];\n    this.filteredBranches = [];\n    // Get user data once\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    // Only set location if available\n    if (this.user.restaurantAccess && this.user.restaurantAccess.length > 0) {\n      this.globalLocation.setValue(this.user.restaurantAccess[0]);\n      this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    }\n    // Use takeUntil pattern for subscriptions to avoid memory leaks\n    this.sharedData.getVersionNumber.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this.versionNumber = data;\n      // Only mark for check if in OnPush mode\n      this.cd.markForCheck();\n    });\n    this.sharedData.checkSettingAvailable.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this.enableSettingBtn = data;\n      // Only mark for check if in OnPush mode\n      this.cd.markForCheck();\n    });\n    // Only make API call once during initialization\n    // Version check is now handled by the parent component\n  }\n\n  ngOnInit() {\n    // Initialize branch data\n    if (this.user && this.user.restaurantAccess) {\n      this.VendorBank = this.user.restaurantAccess.filter(branch => branch && branch.branchName);\n      this.vendorsBanks.next(this.VendorBank.slice());\n      this.selectedBranchesService.updateSelectedBranches(this.user.restaurantAccess);\n    }\n    // Initialize filter control\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    // Set up filter change subscription\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(newValue => {\n      this.vendorfilterBanks(newValue);\n    });\n    // Check if menu items are loaded\n    this.checkMenuItems();\n  }\n  // Detect changes to inputs - optimized to prevent unnecessary re-renders\n  ngOnChanges(changes) {\n    // Only check menuItems if they've actually changed and it's not the first change\n    if (changes['menuItems'] && !changes['menuItems'].firstChange) {\n      // Only update if the new value is different from the current state\n      const newHasItems = Array.isArray(this.menuItems) && this.menuItems.length > 0;\n      if (this.menuItemsLoaded !== newHasItems) {\n        this.menuItemsLoaded = newHasItems;\n        // Only trigger change detection if needed\n        this.cd.markForCheck(); // Use markForCheck instead of detectChanges for OnPush\n      }\n    }\n    // Only update logo if it's changed and not the first change\n    if (changes['logoUrl'] && !changes['logoUrl'].firstChange) {\n      this.cd.markForCheck(); // Use markForCheck instead of detectChanges\n    }\n  }\n  // Optimized method to check if menu items are loaded\n  checkMenuItems() {\n    // Set menuItemsLoaded flag based on whether menuItems has content\n    const hasItems = Array.isArray(this.menuItems) && this.menuItems.length > 0;\n    // Only update if the state has changed\n    if (this.menuItemsLoaded !== hasItems) {\n      this.menuItemsLoaded = hasItems;\n      this.cd.markForCheck(); // Use markForCheck instead of detectChanges\n    }\n  }\n\n  applyFilter(event) {\n    const filterValue = event.target.value;\n    this.filteredBranches = this.user.restaurantAccess.filter(branch => branch && branch.branchName.toLowerCase().includes(filterValue.toLowerCase()));\n  }\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n  vendorfilterBanks(newValue) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(this.VendorBank.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)));\n    } else {\n      const searchTerms = search.split(' ').filter(term => term.trim() !== '');\n      this.vendorsBanks.next(this.VendorBank.filter(branch => {\n        const branchNameLowerCase = branch.branchName.toLowerCase();\n        return searchTerms.every(term => branchNameLowerCase.includes(term));\n      }));\n    }\n  }\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n  /**\n   * Handle logo loading errors\n   */\n  handleLogoError(event) {\n    // Hide the broken image\n    event.target.style.display = 'none';\n    // Could set a default logo here if needed\n  }\n\n  static {\n    this.ɵfac = function DashboardToolbarComponent_Factory(t) {\n      return new (t || DashboardToolbarComponent)(i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardToolbarComponent,\n      selectors: [[\"app-dashboard-toolbar\"]],\n      viewQuery: function DashboardToolbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatSidenav, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allSelected = _t.first);\n        }\n      },\n      inputs: {\n        showNavbarToggleButton: \"showNavbarToggleButton\",\n        menuItems: \"menuItems\",\n        logoUrl: \"logoUrl\",\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      outputs: {\n        toggleMenu: \"toggleMenu\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 8,\n      consts: [[1, \"toolbar-left\"], [1, \"logo-container\"], [\"alt\", \"Company Logo\", \"class\", \"company-logo\", 3, \"src\", \"error\", 4, \"ngIf\"], [1, \"nav-menu\"], [4, \"ngIf\"], [1, \"example-spacer\"], [1, \"formal-text\"], [1, \"beta-tag\"], [1, \"user-info\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"], [\"xPosition\", \"before\"], [\"beforeMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-gear\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fa-solid\", \"fa-right-from-bracket\"], [\"alt\", \"Company Logo\", 1, \"company-logo\", 3, \"src\", \"error\"], [4, \"ngFor\", \"ngForOf\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"], [\"mat-button\", \"\", 1, \"nav-item\", \"placeholder-tab\"]],\n      template: function DashboardToolbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵtemplate(3, DashboardToolbarComponent_img_3_Template, 1, 1, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtemplate(5, DashboardToolbarComponent_ng_container_5_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵtemplate(6, DashboardToolbarComponent_ng_container_6_Template, 6, 0, \"ng-container\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(7, \"span\", 5);\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11, \"Beta\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 12);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"mat-menu\", 13, 14)(23, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_23_listener() {\n            return ctx.setting();\n          });\n          i0.ɵɵelement(24, \"i\", 16);\n          i0.ɵɵtext(25, \" \\u00A0 Setting \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_26_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(27, \"i\", 18);\n          i0.ɵɵtext(28, \" \\u00A0 Logout \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r3 = i0.ɵɵreference(22);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.menuItems && ctx.menuItems.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.menuItems || ctx.menuItems.length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.versionNumber, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.user == null ? null : ctx.user.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.cardDesc);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.enableSettingBtn);\n        }\n      },\n      dependencies: [FormsModule, ReactiveFormsModule, MatDialogModule, CommonModule, i6.NgForOf, i6.NgIf, MatIconModule, i7.MatIcon, MatButtonModule, i8.MatAnchor, i8.MatButton, MatFormFieldModule, MatToolbarModule, i9.MatToolbar, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatSelectModule, MatTooltipModule, MatCardModule, RouterModule, i3.RouterLink, i3.RouterLinkActive],\n      styles: [\"mat-toolbar[_ngcontent-%COMP%] {\\n  background-color: white; \\n\\n  color: #333;\\n  padding: 0 10px; \\n\\n  height: 50px; \\n\\n  min-height: 50px;\\n  display: flex;\\n  align-items: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-bottom: 1px solid #e0e0e0; \\n\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  padding-left: 0; \\n\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 12px;\\n  margin-left: 12px;\\n  height: 100%;\\n  min-width: 40px; \\n\\n}\\n\\n.company-logo[_ngcontent-%COMP%] {\\n  height: 36px;\\n  max-height: 36px;\\n  width: auto;\\n  object-fit: contain;\\n  display: block; \\n\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 8px; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  padding: 0 12px; \\n\\n  height: 50px; \\n\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0;\\n  color: #333;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  margin: 0 1px; \\n\\n  \\n\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:first-child {\\n  padding-left: 12px; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mat-mdc-button-touch-target {\\n  height: 100%;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #ff9100; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  background-color: rgba(255, 145, 0, 0.05);\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -1px; \\n\\n  left: 0;\\n  width: 100%;\\n  height: 2px; \\n\\n  background-color: #ff9100;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  cursor: default;\\n  pointer-events: none;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.placeholder-tab[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 145, 0, 0.2), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n\\n  .mat-toolbar-row, .mat-toolbar-single-row[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n  height: 50px !important;\\n  max-height: 50px !important;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 12px;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0 10px;\\n  border-radius: 4px;\\n  transition: background-color 0.2s ease;\\n  height: 42px;\\n}\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.user-menu-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n  color: #ff9100; \\n\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  line-height: 1.2;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-size: 14px; \\n\\n  font-weight: 500;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  font-size: 12px; \\n\\n  opacity: 0.8;\\n}\\n\\n.formal-text[_ngcontent-%COMP%] {\\n  font-family: Arial, sans-serif;\\n  font-size: 14px; \\n\\n  text-align: right;\\n  line-height: 1.5;\\n  margin: 0 12px;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n\\n.beta-tag[_ngcontent-%COMP%] {\\n  color: #ff9100;\\n  font-weight: bold;\\n  margin-left: 3px;\\n  font-size: 12px; \\n\\n  background-color: rgba(255, 145, 0, 0.1);\\n  padding: 2px 6px; \\n\\n  border-radius: 3px;\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%]    > .mat-icon[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mdc-text-field--filled:not(.mdc-text-field--disabled) {\\n  background-color: #ebebeb;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%] {\\n  width: 215px;\\n  height: 45px;\\n  margin-bottom: 10px;\\n  margin-right: 5px;\\n}\\n\\n.globalSelectInput[_ngcontent-%COMP%] {\\n  height: 30px;\\n  padding: 10px;\\n}\\n\\n.mdc-text-field--no-label[_ngcontent-%COMP%]:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea)   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding-top: 14px;\\n  padding-bottom: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardToolbarComponent };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "MatIconModule", "MatButtonModule", "MatToolbarModule", "MatDialogModule", "DialogComponent", "MatMenuModule", "RouterModule", "MatSelectModule", "MatFormFieldModule", "FormControl", "FormsModule", "ReactiveFormsModule", "MatTooltipModule", "MatCardModule", "<PERSON><PERSON><PERSON><PERSON>", "ReplaySubject", "Subject", "debounceTime", "distinctUntilChanged", "takeUntil", "Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "DashboardToolbarComponent_img_3_Template_img_error_0_listener", "$event", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "handleLogoError", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "logoUrl", "ɵɵsanitizeUrl", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "item_r7", "path", "ɵɵtextInterpolate", "icon", "title", "ɵɵtemplate", "DashboardToolbarComponent_ng_container_5_ng_container_1_Template", "ctx_r1", "menuItems", "DashboardToolbarComponent", "constructor", "selectedBranchesService", "dialog", "router", "auth", "sharedData", "cd", "notify", "showNavbarToggleButton", "toggleMenu", "branchList", "branches", "globalLocation", "VendorBank", "vendorFilterCtrl", "vendorsBanks", "_onD<PERSON>roy", "cardDesc", "showBanner", "menuItemsLoaded", "message", "rolesList", "links", "filteredBranches", "user", "getCurrentUser", "role", "restaurantAccess", "length", "setValue", "setGlLocation", "getVersionNumber", "pipe", "subscribe", "data", "versionNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkSettingAvailable", "enableSettingBtn", "ngOnInit", "filter", "branch", "branchName", "next", "slice", "updateSelectedBranches", "pattern", "valueChanges", "newValue", "vendorfilterBanks", "checkMenuItems", "ngOnChanges", "changes", "firstChange", "newHasItems", "Array", "isArray", "hasItems", "applyFilter", "event", "filterValue", "target", "value", "toLowerCase", "includes", "setting", "navigate", "search", "replace", "searchTerms", "split", "term", "trim", "branchNameLowerCase", "every", "logout", "dialogRef", "open", "autoFocus", "afterClosed", "result", "localStorage", "clear", "sessionStorage", "window", "location", "reload", "restaurantChange", "style", "display", "ɵɵdirectiveInject", "i1", "ShareDataService", "i2", "MatDialog", "i3", "Router", "i4", "AuthService", "ChangeDetectorRef", "i5", "NotificationService", "selectors", "viewQuery", "DashboardToolbarComponent_Query", "rf", "ctx", "DashboardToolbarComponent_img_3_Template", "DashboardToolbarComponent_ng_container_5_Template", "DashboardToolbarComponent_ng_container_6_Template", "ɵɵelement", "DashboardToolbarComponent_Template_button_click_23_listener", "DashboardToolbarComponent_Template_button_click_26_listener", "ɵɵtextInterpolate1", "_r3", "name", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatIcon", "i8", "<PERSON><PERSON><PERSON><PERSON>", "MatButton", "i9", "MatToolbar", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "RouterLink", "RouterLinkActive", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  Output,\n  OnInit,\n  OnChanges,\n  SimpleChanges,\n  EventEmitter,\n  ViewChild,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIcon, MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { first } from 'rxjs';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatOption } from '@angular/material/core';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\n@Component({\n  selector: 'app-dashboard-toolbar',\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    CommonModule,\n    MatIconModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatToolbarModule,\n    MatMenuModule,\n    MatSelectModule,\n    MatTooltipModule,\n    MatCardModule,\n    RouterModule,\n  ],\n  templateUrl: './dashboard-toolbar.component.html',\n  styleUrls: ['./dashboard-toolbar.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardToolbarComponent implements OnInit, OnChanges {\n  user: any;\n  @Input() showNavbarToggleButton = false;\n  @Input() menuItems: any[] = [];\n  @Input() logoUrl: string = '';\n  @Output() toggleMenu = new EventEmitter();\n  public branchList = [];\n  public branches = new FormControl('');\n  public globalLocation: FormControl = new FormControl();\n  public VendorBank: any[] = [];\n  public vendorFilterCtrl: FormControl = new FormControl();\n  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  protected _onDestroy = new Subject<void>();\n  cardDesc: string = '';\n  enableSettingBtn: boolean;\n  @ViewChild(MatSidenav)\n  sidenav!: MatSidenav;\n  @Input() showBanner: boolean = false;\n  @ViewChild('allSelected') private allSelected: MatOption;\n\n  // Track if menu items are loaded\n  public menuItemsLoaded = false;\n  @Input() message: string = 'Update to latest version by pressing';\n  versionNumber: string;\n  rolesList: any = [];\n  links: any = [];\n  access: any;\n  filteredBranches: any[] = [];\n\n  constructor(\n    private selectedBranchesService: ShareDataService,\n    private dialog: MatDialog,\n    private router: Router,\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService\n  ) {\n    // Get user data once\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n\n    // Only set location if available\n    if (this.user.restaurantAccess && this.user.restaurantAccess.length > 0) {\n      this.globalLocation.setValue(this.user.restaurantAccess[0]);\n      this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    }\n\n    // Use takeUntil pattern for subscriptions to avoid memory leaks\n    this.sharedData.getVersionNumber\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe((data) => {\n        this.versionNumber = data;\n        // Only mark for check if in OnPush mode\n        this.cd.markForCheck();\n      });\n\n    this.sharedData.checkSettingAvailable\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe((data) => {\n        this.enableSettingBtn = data;\n        // Only mark for check if in OnPush mode\n        this.cd.markForCheck();\n      });\n\n    // Only make API call once during initialization\n    // Version check is now handled by the parent component\n  }\n  ngOnInit() {\n    // Initialize branch data\n    if (this.user && this.user.restaurantAccess) {\n      this.VendorBank = this.user.restaurantAccess.filter(\n        (branch: any) => branch && branch.branchName\n      );\n      this.vendorsBanks.next(this.VendorBank.slice());\n      this.selectedBranchesService.updateSelectedBranches(\n        this.user.restaurantAccess\n      );\n    }\n\n    // Initialize filter control\n    this.vendorFilterCtrl = new FormControl(\n      '',\n      Validators.pattern('[a-zA-Z0-9\\\\s]*')\n    );\n\n    // Set up filter change subscription\n    this.vendorFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged()\n      )\n      .subscribe((newValue) => {\n        this.vendorfilterBanks(newValue);\n      });\n\n    // Check if menu items are loaded\n    this.checkMenuItems();\n  }\n\n  // Detect changes to inputs - optimized to prevent unnecessary re-renders\n  ngOnChanges(changes: SimpleChanges): void {\n    // Only check menuItems if they've actually changed and it's not the first change\n    if (changes['menuItems'] && !changes['menuItems'].firstChange) {\n      // Only update if the new value is different from the current state\n      const newHasItems = Array.isArray(this.menuItems) && this.menuItems.length > 0;\n      if (this.menuItemsLoaded !== newHasItems) {\n        this.menuItemsLoaded = newHasItems;\n        // Only trigger change detection if needed\n        this.cd.markForCheck(); // Use markForCheck instead of detectChanges for OnPush\n      }\n    }\n\n    // Only update logo if it's changed and not the first change\n    if (changes['logoUrl'] && !changes['logoUrl'].firstChange) {\n      this.cd.markForCheck(); // Use markForCheck instead of detectChanges\n    }\n  }\n\n  // Optimized method to check if menu items are loaded\n  checkMenuItems() {\n    // Set menuItemsLoaded flag based on whether menuItems has content\n    const hasItems = Array.isArray(this.menuItems) && this.menuItems.length > 0;\n\n    // Only update if the state has changed\n    if (this.menuItemsLoaded !== hasItems) {\n      this.menuItemsLoaded = hasItems;\n      this.cd.markForCheck(); // Use markForCheck instead of detectChanges\n    }\n  }\n\n\n\n  applyFilter(event: Event) {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.filteredBranches = this.user.restaurantAccess.filter(\n      (branch) =>\n        branch &&\n        branch.branchName.toLowerCase().includes(filterValue.toLowerCase())\n    );\n  }\n\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n\n  protected vendorfilterBanks(newValue?: unknown) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) =>\n          branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)\n        )\n      );\n    } else {\n      const searchTerms = search\n        .split(' ')\n        .filter((term) => term.trim() !== '');\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) => {\n          const branchNameLowerCase = branch.branchName.toLowerCase();\n          return searchTerms.every((term) =>\n            branchNameLowerCase.includes(term)\n          );\n        })\n      );\n    }\n  }\n\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout',\n      },\n    });\n\n    dialogRef.afterClosed().subscribe((result) => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n\n  /**\n   * Handle logo loading errors\n   */\n  handleLogoError(event: any) {\n    // Hide the broken image\n    event.target.style.display = 'none';\n    // Could set a default logo here if needed\n  }\n}\n", "<mat-toolbar>\n  <div class=\"toolbar-left\">\n    <div class=\"logo-container\">\n      <img *ngIf=\"logoUrl\" [src]=\"logoUrl\" alt=\"Company Logo\" class=\"company-logo\" (error)=\"handleLogoError($event)\">\n    </div>\n\n    <!-- Main Navigation Menu -->\n    <div class=\"nav-menu\">\n      <!-- Show navigation items when loaded -->\n      <ng-container *ngIf=\"menuItems && menuItems.length > 0\">\n        <ng-container *ngFor=\"let item of menuItems\">\n          <a mat-button [routerLink]=\"item.path\" routerLinkActive=\"active\" class=\"nav-item\">\n            <mat-icon>{{item.icon}}</mat-icon>\n            <span>{{item.title}}</span>\n          </a>\n        </ng-container>\n      </ng-container>\n\n      <!-- Show placeholder tabs when menu items are not loaded -->\n      <ng-container *ngIf=\"!menuItems || menuItems.length === 0\">\n        <a mat-button class=\"nav-item placeholder-tab\">\n          <mat-icon>dashboard</mat-icon>\n          <span>Loading...</span>\n        </a>\n      </ng-container>\n    </div>\n  </div>\n\n  <span class=\"example-spacer\"></span>\n\n  <p class=\"formal-text\">{{ versionNumber }} <span class=\"beta-tag\">Beta</span></p>\n  <div class=\"user-info\">\n    <button mat-button [matMenuTriggerFor]=\"beforeMenu\" class=\"user-menu-button\">\n      <mat-icon>account_circle</mat-icon>\n      <div class=\"user-details\">\n        <span class=\"user-name\">{{ user?.name }}</span>\n        <span class=\"user-role\">{{ cardDesc }}</span>\n      </div>\n    </button>\n  </div>\n\n  <mat-menu #beforeMenu=\"matMenu\" xPosition=\"before\">\n    <button mat-menu-item (click)=\"setting()\" [disabled]=\"!enableSettingBtn\">\n      <i class=\"fa-solid fa-gear\"></i> &nbsp; Setting\n    </button>\n    <button mat-menu-item (click)=\"logout()\">\n      <i class=\"fa-solid fa-right-from-bracket\"></i> &nbsp; Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>"], "mappings": "AAAA,SAQEA,YAAY,QAGP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAkBC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAoBC,eAAe,QAAQ,0BAA0B;AAErE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAG9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAItD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IC/BrCC,EAAA,CAAAC,cAAA,cAA+G;IAAlCD,EAAA,CAAAE,UAAA,mBAAAC,8DAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAA9GJ,EAAA,CAAAW,YAAA,EAA+G;;;;IAA1FX,EAAA,CAAAY,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAd,EAAA,CAAAe,aAAA,CAAe;;;;;IAOlCf,EAAA,CAAAgB,uBAAA,GAA6C;IAC3ChB,EAAA,CAAAC,cAAA,YAAkF;IACtED,EAAA,CAAAiB,MAAA,GAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAClCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,GAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAE/BX,EAAA,CAAAkB,qBAAA,EAAe;;;;IAJClB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAY,UAAA,eAAAQ,OAAA,CAAAC,IAAA,CAAwB;IAC1BrB,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAsB,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAa;IACjBvB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAAsB,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAc;;;;;IAJ1BxB,EAAA,CAAAgB,uBAAA,GAAwD;IACtDhB,EAAA,CAAAyB,UAAA,IAAAC,gEAAA,2BAKe;IACjB1B,EAAA,CAAAkB,qBAAA,EAAe;;;;IANkBlB,EAAA,CAAAmB,SAAA,GAAY;IAAZnB,EAAA,CAAAY,UAAA,YAAAe,MAAA,CAAAC,SAAA,CAAY;;;;;IAS7C5B,EAAA,CAAAgB,uBAAA,GAA2D;IACzDhB,EAAA,CAAAC,cAAA,YAA+C;IACnCD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC9BX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,iBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAE3BX,EAAA,CAAAkB,qBAAA,EAAe;;;ADWrB,MAsBaW,yBAAyB;EA6BpCC,YACUC,uBAAyC,EACzCC,MAAiB,EACjBC,MAAc,EACdC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B;IAN3B,KAAAN,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IAlCP,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAV,SAAS,GAAU,EAAE;IACrB,KAAAd,OAAO,GAAW,EAAE;IACnB,KAAAyB,UAAU,GAAG,IAAI9D,YAAY,EAAE;IAClC,KAAA+D,UAAU,GAAG,EAAE;IACf,KAAAC,QAAQ,GAAG,IAAIrD,WAAW,CAAC,EAAE,CAAC;IAC9B,KAAAsD,cAAc,GAAgB,IAAItD,WAAW,EAAE;IAC/C,KAAAuD,UAAU,GAAU,EAAE;IACtB,KAAAC,gBAAgB,GAAgB,IAAIxD,WAAW,EAAE;IACjD,KAAAyD,YAAY,GAAyB,IAAInD,aAAa,CAAQ,CAAC,CAAC;IAC7D,KAAAoD,UAAU,GAAG,IAAInD,OAAO,EAAQ;IAC1C,KAAAoD,QAAQ,GAAW,EAAE;IAIZ,KAAAC,UAAU,GAAY,KAAK;IAGpC;IACO,KAAAC,eAAe,GAAG,KAAK;IACrB,KAAAC,OAAO,GAAW,sCAAsC;IAEjE,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,KAAK,GAAQ,EAAE;IAEf,KAAAC,gBAAgB,GAAU,EAAE;IAW1B;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACpB,IAAI,CAACqB,cAAc,EAAE;IACtC,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACO,IAAI,CAACE,IAAI;IAE/B;IACA,IAAI,IAAI,CAACF,IAAI,CAACG,gBAAgB,IAAI,IAAI,CAACH,IAAI,CAACG,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACvE,IAAI,CAAChB,cAAc,CAACiB,QAAQ,CAAC,IAAI,CAACL,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACtB,UAAU,CAACyB,aAAa,CAAC,IAAI,CAACN,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,IAAI,CAACtB,UAAU,CAAC0B,gBAAgB,CAC7BC,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC,CAChCiB,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,CAACC,aAAa,GAAGD,IAAI;MACzB;MACA,IAAI,CAAC5B,EAAE,CAAC8B,YAAY,EAAE;IACxB,CAAC,CAAC;IAEJ,IAAI,CAAC/B,UAAU,CAACgC,qBAAqB,CAClCL,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACgD,UAAU,CAAC,CAAC,CAChCiB,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,CAACI,gBAAgB,GAAGJ,IAAI;MAC5B;MACA,IAAI,CAAC5B,EAAE,CAAC8B,YAAY,EAAE;IACxB,CAAC,CAAC;IAEJ;IACA;EACF;;EACAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACG,gBAAgB,EAAE;MAC3C,IAAI,CAACd,UAAU,GAAG,IAAI,CAACW,IAAI,CAACG,gBAAgB,CAACa,MAAM,CAChDC,MAAW,IAAKA,MAAM,IAAIA,MAAM,CAACC,UAAU,CAC7C;MACD,IAAI,CAAC3B,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;MAC/C,IAAI,CAAC3C,uBAAuB,CAAC4C,sBAAsB,CACjD,IAAI,CAACrB,IAAI,CAACG,gBAAgB,CAC3B;;IAGH;IACA,IAAI,CAACb,gBAAgB,GAAG,IAAIxD,WAAW,CACrC,EAAE,EACFW,UAAU,CAAC6E,OAAO,CAAC,iBAAiB,CAAC,CACtC;IAED;IACA,IAAI,CAAChC,gBAAgB,CAACiC,YAAY,CAC/Bf,IAAI,CACHlE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACAkE,SAAS,CAAEe,QAAQ,IAAI;MACtB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACE,cAAc,EAAE;EACvB;EAEA;EACAC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,WAAW,CAAC,IAAI,CAACA,OAAO,CAAC,WAAW,CAAC,CAACC,WAAW,EAAE;MAC7D;MACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC1D,SAAS,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC8B,MAAM,GAAG,CAAC;MAC9E,IAAI,IAAI,CAACT,eAAe,KAAKmC,WAAW,EAAE;QACxC,IAAI,CAACnC,eAAe,GAAGmC,WAAW;QAClC;QACA,IAAI,CAAChD,EAAE,CAAC8B,YAAY,EAAE,CAAC,CAAC;;;IAI5B;IACA,IAAIgB,OAAO,CAAC,SAAS,CAAC,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,CAACC,WAAW,EAAE;MACzD,IAAI,CAAC/C,EAAE,CAAC8B,YAAY,EAAE,CAAC,CAAC;;EAE5B;EAEA;EACAc,cAAcA,CAAA;IACZ;IACA,MAAMO,QAAQ,GAAGF,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC1D,SAAS,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC8B,MAAM,GAAG,CAAC;IAE3E;IACA,IAAI,IAAI,CAACT,eAAe,KAAKsC,QAAQ,EAAE;MACrC,IAAI,CAACtC,eAAe,GAAGsC,QAAQ;MAC/B,IAAI,CAACnD,EAAE,CAAC8B,YAAY,EAAE,CAAC,CAAC;;EAE5B;;EAIAsB,WAAWA,CAACC,KAAY;IACtB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK;IAC5D,IAAI,CAACvC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACG,gBAAgB,CAACa,MAAM,CACtDC,MAAM,IACLA,MAAM,IACNA,MAAM,CAACC,UAAU,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAACJ,WAAW,CAACG,WAAW,EAAE,CAAC,CACtE;EACH;EAEAE,OAAOA,CAAA;IACL,IAAI,CAAC9D,MAAM,CAAC+D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEUjB,iBAAiBA,CAACD,QAAkB;IAC5C,IAAI,CAAC,IAAI,CAACnC,UAAU,EAAE;MACpB;;IAEF,IAAIsD,MAAM,GAAG,IAAI,CAACrD,gBAAgB,CAACgD,KAAK;IACxC,IAAI,CAACK,MAAM,EAAE;MACX,IAAI,CAACpD,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;MAC/C;KACD,MAAM;MACLuB,MAAM,GAAGA,MAAM,CAACJ,WAAW,EAAE;;IAE/B,IAAI,CAACI,MAAM,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzB,IAAI,CAACjD,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAC5BA,MAAM,CAACC,UAAU,CAACqB,WAAW,EAAE,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACJ,QAAQ,CAACG,MAAM,CAAC,CACpE,CACF;KACF,MAAM;MACL,MAAME,WAAW,GAAGF,MAAM,CACvBG,KAAK,CAAC,GAAG,CAAC,CACV9B,MAAM,CAAE+B,IAAI,IAAKA,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACvC,IAAI,CAACzD,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAAI;QAChC,MAAMgC,mBAAmB,GAAGhC,MAAM,CAACC,UAAU,CAACqB,WAAW,EAAE;QAC3D,OAAOM,WAAW,CAACK,KAAK,CAAEH,IAAI,IAC5BE,mBAAmB,CAACT,QAAQ,CAACO,IAAI,CAAC,CACnC;MACH,CAAC,CAAC,CACH;;EAEL;EAEAI,MAAMA,CAAA;IACJ,MAAMC,SAAS,GAAG,IAAI,CAAC1E,MAAM,CAAC2E,IAAI,CAAC5H,eAAe,EAAE;MAClD6H,SAAS,EAAE,KAAK;MAChB5C,IAAI,EAAE;QACJd,OAAO,EAAE,kCAAkC;QAC3C1B,KAAK,EAAE;;KAEV,CAAC;IAEFkF,SAAS,CAACG,WAAW,EAAE,CAAC9C,SAAS,CAAE+C,MAAM,IAAI;MAC3C,IAAIA,MAAM,EAAE;QACVC,YAAY,CAACC,KAAK,EAAE;QACpBC,cAAc,CAACD,KAAK,EAAE;QACtBE,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QACxB,IAAI,CAACnF,MAAM,CAAC+D,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;IAErC,CAAC,CAAC;EACJ;EAEAqB,gBAAgBA,CAAC5B,KAAK;IACpB,IAAI,CAACtD,UAAU,CAACyB,aAAa,CAAC6B,KAAK,CAAC;EACtC;EAEA;;;EAGA/E,eAAeA,CAAC+E,KAAU;IACxB;IACAA,KAAK,CAACE,MAAM,CAAC2B,KAAK,CAACC,OAAO,GAAG,MAAM;IACnC;EACF;;;;uBAhNW1F,yBAAyB,EAAA7B,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA1H,EAAA,CAAAwH,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA5H,EAAA,CAAAwH,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9H,EAAA,CAAAwH,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAAwH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA1H,EAAA,CAAAwH,iBAAA,CAAAxH,EAAA,CAAAiI,iBAAA,GAAAjI,EAAA,CAAAwH,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzBtG,yBAAyB;MAAAuG,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAezB9I,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;UCxEvBO,EAAA,CAAAC,cAAA,kBAAa;UAGPD,EAAA,CAAAyB,UAAA,IAAAgH,wCAAA,iBAA+G;UACjHzI,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAC,cAAA,aAAsB;UAEpBD,EAAA,CAAAyB,UAAA,IAAAiH,iDAAA,0BAOe;UAGf1I,EAAA,CAAAyB,UAAA,IAAAkH,iDAAA,0BAKe;UACjB3I,EAAA,CAAAW,YAAA,EAAM;UAGRX,EAAA,CAAA4I,SAAA,cAAoC;UAEpC5I,EAAA,CAAAC,cAAA,WAAuB;UAAAD,EAAA,CAAAiB,MAAA,GAAoB;UAAAjB,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAiB,MAAA,YAAI;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAC7EX,EAAA,CAAAC,cAAA,cAAuB;UAETD,EAAA,CAAAiB,MAAA,sBAAc;UAAAjB,EAAA,CAAAW,YAAA,EAAW;UACnCX,EAAA,CAAAC,cAAA,eAA0B;UACAD,EAAA,CAAAiB,MAAA,IAAgB;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAC/CX,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAiB,MAAA,IAAc;UAAAjB,EAAA,CAAAW,YAAA,EAAO;UAKnDX,EAAA,CAAAC,cAAA,wBAAmD;UAC3BD,EAAA,CAAAE,UAAA,mBAAA2I,4DAAA;YAAA,OAASL,GAAA,CAAAzC,OAAA,EAAS;UAAA,EAAC;UACvC/F,EAAA,CAAA4I,SAAA,aAAgC;UAAC5I,EAAA,CAAAiB,MAAA,wBACnC;UAAAjB,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAE,UAAA,mBAAA4I,4DAAA;YAAA,OAASN,GAAA,CAAA/B,MAAA,EAAQ;UAAA,EAAC;UACtCzG,EAAA,CAAA4I,SAAA,aAA8C;UAAC5I,EAAA,CAAAiB,MAAA,uBACjD;UAAAjB,EAAA,CAAAW,YAAA,EAAS;;;;UA5CDX,EAAA,CAAAmB,SAAA,GAAa;UAAbnB,EAAA,CAAAY,UAAA,SAAA4H,GAAA,CAAA1H,OAAA,CAAa;UAMJd,EAAA,CAAAmB,SAAA,GAAuC;UAAvCnB,EAAA,CAAAY,UAAA,SAAA4H,GAAA,CAAA5G,SAAA,IAAA4G,GAAA,CAAA5G,SAAA,CAAA8B,MAAA,KAAuC;UAUvC1D,EAAA,CAAAmB,SAAA,GAA0C;UAA1CnB,EAAA,CAAAY,UAAA,UAAA4H,GAAA,CAAA5G,SAAA,IAAA4G,GAAA,CAAA5G,SAAA,CAAA8B,MAAA,OAA0C;UAWtC1D,EAAA,CAAAmB,SAAA,GAAoB;UAApBnB,EAAA,CAAA+I,kBAAA,KAAAP,GAAA,CAAAvE,aAAA,MAAoB;UAEtBjE,EAAA,CAAAmB,SAAA,GAAgC;UAAhCnB,EAAA,CAAAY,UAAA,sBAAAoI,GAAA,CAAgC;UAGvBhJ,EAAA,CAAAmB,SAAA,GAAgB;UAAhBnB,EAAA,CAAAsB,iBAAA,CAAAkH,GAAA,CAAAlF,IAAA,kBAAAkF,GAAA,CAAAlF,IAAA,CAAA2F,IAAA,CAAgB;UAChBjJ,EAAA,CAAAmB,SAAA,GAAc;UAAdnB,EAAA,CAAAsB,iBAAA,CAAAkH,GAAA,CAAAzF,QAAA,CAAc;UAMA/C,EAAA,CAAAmB,SAAA,GAA8B;UAA9BnB,EAAA,CAAAY,UAAA,cAAA4H,GAAA,CAAApE,gBAAA,CAA8B;;;qBDHxE/E,WAAW,EACXC,mBAAmB,EACnBR,eAAe,EACfJ,YAAY,EAAAwK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZzK,aAAa,EAAA0K,EAAA,CAAAC,OAAA,EACb1K,eAAe,EAAA2K,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACftK,kBAAkB,EAClBN,gBAAgB,EAAA6K,EAAA,CAAAC,UAAA,EAChB3K,aAAa,EAAA4K,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACb7K,eAAe,EACfK,gBAAgB,EAChBC,aAAa,EACbP,YAAY,EAAA4I,EAAA,CAAAmC,UAAA,EAAAnC,EAAA,CAAAoC,gBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMHtI,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}