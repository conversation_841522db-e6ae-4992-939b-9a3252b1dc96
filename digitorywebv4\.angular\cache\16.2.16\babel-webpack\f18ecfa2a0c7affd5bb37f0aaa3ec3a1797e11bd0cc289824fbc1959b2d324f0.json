{"ast": null, "code": "import { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { ReplaySubject, Subject, map, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';\nimport { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/share-data.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"src/app/services/inventory.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/auth.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/services/master-data.service\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/material/tabs\";\nimport * as i13 from \"@angular/material/button\";\nimport * as i14 from \"@angular/material/icon\";\nimport * as i15 from \"@angular/material/form-field\";\nimport * as i16 from \"@angular/material/list\";\nimport * as i17 from \"@angular/material/radio\";\nimport * as i18 from \"@angular/material/input\";\nimport * as i19 from \"@angular/material/card\";\nimport * as i20 from \"@angular/material/select\";\nimport * as i21 from \"@angular/material/core\";\nimport * as i22 from \"ngx-mat-select-search\";\nimport * as i23 from \"@angular/material/expansion\";\nimport * as i24 from \"@angular/material/table\";\nimport * as i25 from \"@angular/material/paginator\";\nimport * as i26 from \"ngx-skeleton-loader\";\nconst _c0 = [\"salesPaginator\"];\nconst _c1 = [\"wacPaginator\"];\nconst _c2 = [\"forecastPaginator\"];\nconst _c3 = [\"showDialog\"];\nconst _c4 = [\"tabGroup\"];\nfunction DashboardProfileComponent_form_58_div_20_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 125);\n    i0.ɵɵtext(2, \"Invalid IP address\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardProfileComponent_form_58_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"label\", 123);\n    i0.ɵɵtext(2, \"This IP address or subnet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_form_58_div_20_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.ipOrSubnet = $event);\n    })(\"ngModelChange\", function DashboardProfileComponent_form_58_div_20_Template_input_ngModelChange_3_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.validateInput());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DashboardProfileComponent_form_58_div_20_div_4_Template, 3, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r51.ipOrSubnet);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r51.isIPValid);\n  }\n}\nfunction DashboardProfileComponent_form_58_div_21_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 125);\n    i0.ɵɵtext(2, \"Invalid IP address\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardProfileComponent_form_58_div_21_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 125);\n    i0.ɵɵtext(2, \"Invalid IP address\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardProfileComponent_form_58_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"label\", 126);\n    i0.ɵɵtext(3, \"From:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 127);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.startIP = $event);\n    })(\"ngModelChange\", function DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.validateInput());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, DashboardProfileComponent_form_58_div_21_div_5_Template, 3, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 29)(7, \"label\", 128);\n    i0.ɵɵtext(8, \"To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 129);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.endIP = $event);\n    })(\"ngModelChange\", function DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_9_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.validateInput());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, DashboardProfileComponent_form_58_div_21_div_10_Template, 3, 0, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r52.startIP);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r52.ipPattern.test(ctx_r52.startIP));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r52.endIP);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r52.ipPattern.test(ctx_r52.endIP));\n  }\n}\nfunction DashboardProfileComponent_form_58_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 130);\n    i0.ɵɵtext(1, \"Submit\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r53.isIPValid);\n  }\n}\nfunction DashboardProfileComponent_form_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 112);\n    i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_form_58_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 113)(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \" Your current IP -\");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-title\", 114)(7, \"strong\");\n    i0.ɵɵtext(8, \"Choose an option \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 115)(10, \"div\", 116)(11, \"div\", 117)(12, \"input\", 118);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_form_58_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.selectedOption = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"label\", 119);\n    i0.ɵɵtext(14, \"This IP address or subnet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 116)(16, \"div\", 117)(17, \"input\", 120);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_form_58_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.selectedOption = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"label\", 121);\n    i0.ɵɵtext(19, \"This IP address range\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(20, DashboardProfileComponent_form_58_div_20_Template, 5, 2, \"div\", 84);\n    i0.ɵɵtemplate(21, DashboardProfileComponent_form_58_div_21_Template, 11, 4, \"div\", 27);\n    i0.ɵɵtemplate(22, DashboardProfileComponent_form_58_button_22_Template, 2, 1, \"button\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentIp, \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectedOption);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectedOption);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedOption === \"individual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedOption === \"range\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedOption === \"individual\" || ctx_r1.selectedOption === \"range\");\n  }\n}\nfunction DashboardProfileComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_59_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.onSubmit());\n    });\n    i0.ɵɵtext(2, \"Submit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardProfileComponent_div_65_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r72 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r72.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r72.branchName), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"mat-form-field\", 79)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-select\", 80);\n    i0.ɵɵlistener(\"selectionChange\", function DashboardProfileComponent_div_65_Template_mat_select_selectionChange_5_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.getPriceTires());\n    });\n    i0.ɵɵtemplate(6, DashboardProfileComponent_div_65_mat_option_6_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formControl\", ctx_r3.branchData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredBranches);\n  }\n}\nfunction DashboardProfileComponent_mat_option_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r75 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r75);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r75[\"name\"]), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_option_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 133);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r76 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r76)(\"disabled\", option_r76 === \"superadmin\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, option_r76), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_option_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 133);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r77 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r77)(\"disabled\", option_r77 === \"superadmin\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, option_r77), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_116_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r79 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r79);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r79), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Inventory Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 134);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_116_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_116_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.toggleSelectAllInventory());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_116_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r7.inventoryFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r7.inventory));\n  }\n}\nfunction DashboardProfileComponent_div_130_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r84 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r84);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r84), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"User Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 137);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_130_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r86);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_130_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r86);\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.toggleSelectAllUser());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_130_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r8.userFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r8.users));\n  }\n}\nfunction DashboardProfileComponent_div_144_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r89 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r89);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r89), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_144_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r91 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Recipe Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 138);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_144_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r90.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_144_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r91);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r92.toggleSelectAllRecipe());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_144_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r9.recipeFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r9.recipe));\n  }\n}\nfunction DashboardProfileComponent_div_158_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r94 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r94);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r94), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Party Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 139);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_158_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_158_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.toggleSelectAllParty());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_158_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r10.partyFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r10.party));\n  }\n}\nfunction DashboardProfileComponent_div_173_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r99 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r99);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r99), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_173_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Delete Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 140);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_173_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_173_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r102 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r102.toggleSelectAllDeleteGRN());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_173_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r11.deleteGRNFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r11.deleteGRN));\n  }\n}\nfunction DashboardProfileComponent_div_183_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r104 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r104);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r104), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_183_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r106 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Edit Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 141);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_183_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r106);\n      const ctx_r105 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r105.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_183_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r106);\n      const ctx_r107 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r107.toggleSelectAllEditGRN());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_183_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r12.editGRNFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r12.editGRN));\n  }\n}\nfunction DashboardProfileComponent_div_193_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r109 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r109);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r109), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_193_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r111 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"RTV Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 142);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_193_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r111);\n      const ctx_r110 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r110.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_193_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r111);\n      const ctx_r112 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r112.toggleSelectAllCloseGRN());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_193_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r13.closeGRNFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r13.closeGRN));\n  }\n}\nfunction DashboardProfileComponent_div_208_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r114 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r114);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r114), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_208_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Delete Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_208_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r115 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r115.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_208_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r117.toggleSelectAllDeletePO());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_208_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r14.deletePOFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r14.deletePO));\n  }\n}\nfunction DashboardProfileComponent_div_218_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r119 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r119);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r119), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_218_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r121 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Edit Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 144);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_218_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r121);\n      const ctx_r120 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r120.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_218_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r121);\n      const ctx_r122 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r122.toggleSelectAllEditPO());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_218_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r15.editPOFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r15.editPO));\n  }\n}\nfunction DashboardProfileComponent_div_228_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r124 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r124);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r124), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_228_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Close Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_228_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r125.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_228_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r127 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r127.toggleSelectAllClosePO());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_228_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r16.closePOFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r16.closePO));\n  }\n}\nfunction DashboardProfileComponent_div_243_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r129 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r129);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r129), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_243_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Delete Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 146);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_243_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_243_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r132 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r132.toggleSelectAllDeleteIndent());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_243_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r17.deleteIndentFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r17.deleteIndent));\n  }\n}\nfunction DashboardProfileComponent_div_253_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r134 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r134);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r134), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_253_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r136 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 30)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Close Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 147);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_253_Template_mat_select_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r135 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r135.validateSales());\n    });\n    i0.ɵɵelementStart(5, \"mat-option\");\n    i0.ɵɵelement(6, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_div_253_Template_mat_option_click_7_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r137 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r137.toggleSelectAllCloseIndent());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\", 136);\n    i0.ɵɵtext(9, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_253_mat_option_11_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formControl\", ctx_r18.closeIndentFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(12, 2, ctx_r18.closeIndent));\n  }\n}\nfunction DashboardProfileComponent_mat_option_333_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r138 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r138);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r138), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_expansion_panel_336_mat_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r141 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r141);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r141), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_expansion_panel_336_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\")(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-form-field\", 30)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Select Roles\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-select\", 148)(9, \"mat-option\");\n    i0.ɵɵelement(10, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_mat_expansion_panel_336_Template_mat_option_click_11_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r142 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r142.selectAllRolesForReport());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\", 136);\n    i0.ɵɵtext(13, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, DashboardProfileComponent_mat_expansion_panel_336_mat_option_15_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(16, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const report_r139 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, report_r139.displayName), \" REPORT \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formControl\", ctx_r20.report_FilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(16, 5, ctx_r20.reports));\n  }\n}\nfunction DashboardProfileComponent_mat_option_349_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r144 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r144);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r144), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_accordion_351_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r146 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r146);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r146), \" \");\n  }\n}\nfunction DashboardProfileComponent_mat_accordion_351_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r148 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-accordion\", 72)(1, \"mat-expansion-panel\")(2, \"mat-expansion-panel-header\")(3, \"mat-panel-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 30)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Select Report\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 149)(10, \"mat-option\");\n    i0.ɵɵelement(11, \"ngx-mat-select-search\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-option\", 135);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_mat_accordion_351_Template_mat_option_click_12_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r147 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r147.selectAllReportsForRoles());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\", 136);\n    i0.ɵɵtext(14, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Select All / Deselect All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, DashboardProfileComponent_mat_accordion_351_mat_option_16_Template, 3, 4, \"mat-option\", 32);\n    i0.ɵɵpipe(17, \"async\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, ctx_r22.filteredRole), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formControl\", ctx_r22.reportFilterCtrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(17, 5, ctx_r22.report));\n  }\n}\nfunction DashboardProfileComponent_mat_label_366_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-label\", 150);\n    i0.ɵɵtext(1, \"Search ....\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_mat_option_369_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 132);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r149 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r149.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, option_r149.branchName), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_370_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r151 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 151)(2, \"label\", 19);\n    i0.ɵɵtext(3, \"Do you want to pick a date?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-radio-group\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_370_Template_mat_radio_group_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r151);\n      const ctx_r150 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r150.pickDateOption = $event);\n    });\n    i0.ɵɵelementStart(5, \"mat-radio-button\", 153);\n    i0.ɵɵtext(6, \"Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-radio-button\", 154);\n    i0.ɵɵtext(8, \"No\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r26.pickDateOption);\n  }\n}\nfunction DashboardProfileComponent_div_371_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r153 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"label\", 155);\n    i0.ɵɵtext(2, \"Select a monthly closing date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 79)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"Monthly Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-select\", 156);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_div_371_Template_mat_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r153);\n      const ctx_r152 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r152.selectedDateOption = $event);\n    })(\"selectionChange\", function DashboardProfileComponent_div_371_Template_mat_select_selectionChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r153);\n      const ctx_r154 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r154.onDateSelectionChange($event.value));\n    });\n    i0.ɵɵelementStart(7, \"mat-option\", 132);\n    i0.ɵɵtext(8, \"--Select--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-option\", 157);\n    i0.ɵɵtext(10, \"Starting of the Month\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.selectedDateOption);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", null);\n  }\n}\nfunction DashboardProfileComponent_div_372_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 158)(1, \"button\", 159);\n    i0.ɵɵtext(2, \"Submit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_header_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 172);\n    i0.ɵɵtext(1, \" S.No \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 172);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r166 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r166 + 1, \" \");\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_header_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Sales Date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r167 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, element_r167.createTs, \"dd-MM-yyyy\"), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_header_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Created At \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const element_r168 = i0.ɵɵnextContext().$implicit;\n    const ctx_r169 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", element_r168.triggeredTs ? i0.ɵɵpipeBind2(2, 2, element_r168.triggeredTs, \"dd-MM-yyyy\") : i0.ɵɵpipeBind2(3, 5, element_r168.modTs, \"dd-MM-yyyy\"), \" \", element_r168.triggeredTs ? ctx_r169.convertToIST(element_r168.triggeredTs) : ctx_r169.convertToIST(element_r168.modTs), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" - \");\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtemplate(1, DashboardProfileComponent_div_401_mat_cell_10_ng_container_1_Template, 4, 8, \"ng-container\", 175);\n    i0.ɵɵtemplate(2, DashboardProfileComponent_div_401_mat_cell_10_ng_template_2_Template, 1, 0, \"ng-template\", null, 176, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r168 = ctx.$implicit;\n    const _r170 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r168.triggeredTs || element_r168.modTs)(\"ngIfElse\", _r170);\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_header_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_cell_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r173 = ctx.$implicit;\n    const ctx_r162 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r162.getJobStatus(element_r173), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_401_mat_header_row_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    \"highlighted-row\": a0\n  };\n};\nfunction DashboardProfileComponent_div_401_mat_row_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 177);\n  }\n  if (rf & 2) {\n    const row_r174 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, row_r174.Discontinued === \"yes\"));\n  }\n}\nfunction DashboardProfileComponent_div_401_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"mat-table\", 161);\n    i0.ɵɵelementContainerStart(2, 162);\n    i0.ɵɵtemplate(3, DashboardProfileComponent_div_401_mat_header_cell_3_Template, 2, 0, \"mat-header-cell\", 163);\n    i0.ɵɵtemplate(4, DashboardProfileComponent_div_401_mat_cell_4_Template, 2, 1, \"mat-cell\", 164);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 165);\n    i0.ɵɵtemplate(6, DashboardProfileComponent_div_401_mat_header_cell_6_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(7, DashboardProfileComponent_div_401_mat_cell_7_Template, 3, 4, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 168);\n    i0.ɵɵtemplate(9, DashboardProfileComponent_div_401_mat_header_cell_9_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(10, DashboardProfileComponent_div_401_mat_cell_10_Template, 4, 2, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 169);\n    i0.ɵɵtemplate(12, DashboardProfileComponent_div_401_mat_header_cell_12_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(13, DashboardProfileComponent_div_401_mat_cell_13_Template, 2, 1, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(14, DashboardProfileComponent_div_401_mat_header_row_14_Template, 1, 0, \"mat-header-row\", 170);\n    i0.ɵɵtemplate(15, DashboardProfileComponent_div_401_mat_row_15_Template, 1, 3, \"mat-row\", 171);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r33.dataSourceSales);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r33.salesColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r33.salesColumns);\n  }\n}\nconst _c6 = function () {\n  return {\n    \"border-radius\": \"5px\",\n    height: \"30px\"\n  };\n};\nfunction DashboardProfileComponent_div_402_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 178);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\nfunction DashboardProfileComponent_div_403_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179);\n    i0.ɵɵtext(1, \" Data Not Found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_header_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 172);\n    i0.ɵɵtext(1, \" S.No \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 172);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r184 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r184 + 1, \" \");\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_header_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Created At \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_cell_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r185 = ctx.$implicit;\n    const ctx_r178 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(2, 2, element_r185.createTs, \"dd-MM-yyyy\"), \" \", ctx_r178.convertToIST(element_r185.createTs), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_header_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r186 = ctx.$implicit;\n    const ctx_r180 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r180.getJobStatus(element_r186), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_header_row_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction DashboardProfileComponent_div_416_mat_row_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 177);\n  }\n  if (rf & 2) {\n    const row_r187 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, row_r187.Discontinued === \"yes\"));\n  }\n}\nfunction DashboardProfileComponent_div_416_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"mat-table\", 161);\n    i0.ɵɵelementContainerStart(2, 162);\n    i0.ɵɵtemplate(3, DashboardProfileComponent_div_416_mat_header_cell_3_Template, 2, 0, \"mat-header-cell\", 163);\n    i0.ɵɵtemplate(4, DashboardProfileComponent_div_416_mat_cell_4_Template, 2, 1, \"mat-cell\", 164);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 168);\n    i0.ɵɵtemplate(6, DashboardProfileComponent_div_416_mat_header_cell_6_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(7, DashboardProfileComponent_div_416_mat_cell_7_Template, 3, 5, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 169);\n    i0.ɵɵtemplate(9, DashboardProfileComponent_div_416_mat_header_cell_9_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(10, DashboardProfileComponent_div_416_mat_cell_10_Template, 2, 1, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_416_mat_header_row_11_Template, 1, 0, \"mat-header-row\", 170);\n    i0.ɵɵtemplate(12, DashboardProfileComponent_div_416_mat_row_12_Template, 1, 3, \"mat-row\", 171);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r39.dataSourceWeightedAvg);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r39.weightedAvgColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r39.weightedAvgColumns);\n  }\n}\nfunction DashboardProfileComponent_div_417_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 178);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\nfunction DashboardProfileComponent_div_418_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179);\n    i0.ɵɵtext(1, \" Data Not Found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_header_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 172);\n    i0.ɵɵtext(1, \" S.No \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 172);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r197 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i_r197 + 1, \" \");\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_header_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Created At \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_cell_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r198 = ctx.$implicit;\n    const ctx_r191 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(2, 2, element_r198.createTs, \"dd-MM-yyyy\"), \" \", ctx_r191.convertToIST(element_r198.createTs), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_header_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 173);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 174);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r199 = ctx.$implicit;\n    const ctx_r193 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r193.getJobStatus(element_r199), \" \");\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_header_row_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction DashboardProfileComponent_div_431_mat_row_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 177);\n  }\n  if (rf & 2) {\n    const row_r200 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, row_r200.Discontinued === \"yes\"));\n  }\n}\nfunction DashboardProfileComponent_div_431_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"mat-table\", 161);\n    i0.ɵɵelementContainerStart(2, 162);\n    i0.ɵɵtemplate(3, DashboardProfileComponent_div_431_mat_header_cell_3_Template, 2, 0, \"mat-header-cell\", 163);\n    i0.ɵɵtemplate(4, DashboardProfileComponent_div_431_mat_cell_4_Template, 2, 1, \"mat-cell\", 164);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 168);\n    i0.ɵɵtemplate(6, DashboardProfileComponent_div_431_mat_header_cell_6_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(7, DashboardProfileComponent_div_431_mat_cell_7_Template, 3, 5, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 169);\n    i0.ɵɵtemplate(9, DashboardProfileComponent_div_431_mat_header_cell_9_Template, 2, 0, \"mat-header-cell\", 166);\n    i0.ɵɵtemplate(10, DashboardProfileComponent_div_431_mat_cell_10_Template, 2, 1, \"mat-cell\", 167);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(11, DashboardProfileComponent_div_431_mat_header_row_11_Template, 1, 0, \"mat-header-row\", 170);\n    i0.ɵɵtemplate(12, DashboardProfileComponent_div_431_mat_row_12_Template, 1, 3, \"mat-row\", 171);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r45.dataSourceForecast);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r45.forecastColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r45.forecastColumns);\n  }\n}\nfunction DashboardProfileComponent_div_432_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 178);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\nfunction DashboardProfileComponent_div_433_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179);\n    i0.ɵɵtext(1, \" Data Not Found \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardProfileComponent_ng_template_436_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r202 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 180)(1, \"div\")(2, \"div\", 181)(3, \"div\", 182)(4, \"input\", 183);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_ng_template_436_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r202);\n      const ctx_r201 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r201.IpType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 119);\n    i0.ɵɵtext(6, \"use Without IP\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 182)(8, \"input\", 184);\n    i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_ng_template_436_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r202);\n      const ctx_r203 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r203.IpType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"label\", 121);\n    i0.ɵɵtext(10, \"Use With IP\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 185)(12, \"button\", 186);\n    i0.ɵɵlistener(\"click\", function DashboardProfileComponent_ng_template_436_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r202);\n      const ctx_r204 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r204.submitIp());\n    });\n    i0.ɵɵtext(13, \" Ok \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r50.IpType);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r50.IpType);\n  }\n}\nconst _c7 = function () {\n  return [5, 10, 25, 50, 100];\n};\nexport const MY_DATE_FORMATS = {\n  parse: {\n    dateInput: 'DD/MM/YYYY'\n  },\n  display: {\n    dateInput: 'DD/MM/YYYY',\n    monthYearLabel: 'MMM YYYY',\n    dateA11yLabel: 'DD/MM/YYYY',\n    monthYearA11yLabel: 'MMMM YYYY'\n  }\n};\nclass DashboardProfileComponent {\n  constructor(shareDataService, formBuilder, ngZone, http, api, notify, cd, sharedData, auth, dialog, masterDataService, router, fb) {\n    this.shareDataService = shareDataService;\n    this.formBuilder = formBuilder;\n    this.ngZone = ngZone;\n    this.http = http;\n    this.api = api;\n    this.notify = notify;\n    this.cd = cd;\n    this.sharedData = sharedData;\n    this.auth = auth;\n    this.dialog = dialog;\n    this.masterDataService = masterDataService;\n    this.router = router;\n    this.fb = fb;\n    this.monthlyClosingDatesArray = [];\n    this.engineUrl = environment.engineUrl;\n    // selectedDateOption: string; // Tracks selected date option\n    this.startingDates = {};\n    this.showLocationSelection = true;\n    this.selectedLocation = '';\n    this.selectedDateOption = null;\n    this.monthlyClosingDates = [];\n    this.selectedMonthDates = [];\n    this.selectedDay = null;\n    this.selectedDays = {};\n    this.pickDateOption = 'no';\n    this.closingType = '';\n    this.selectedDate = '';\n    this.monthlyDate = '';\n    this.fifteenDaysDate = '';\n    this.isReadOnly = false;\n    this.salesLocation = false;\n    this.breakpointObserver = inject(BreakpointObserver);\n    this.ipPattern = new RegExp(\"(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\");\n    this.ipOrSubnet = '';\n    this.startIP = '';\n    this.endIP = '';\n    this.selectedOption = 'individual';\n    this.profitTarget = 1;\n    this.isIPValid = false;\n    this.priceData = new FormControl();\n    this.branchData = new FormControl(null);\n    this.IpType = 'withoutIp';\n    this.showIpProcess = false;\n    this.showIpBtn = false;\n    this.isSalesDataReady = false;\n    this.isWacDataReady = false;\n    this.isForecastReady = false;\n    this.selectedSettingRoles = [];\n    this.selectedExcelRoles = [];\n    this.showPlaceholderLabel = true;\n    this.roles = [];\n    this.rolesIndent = [];\n    this.rolesGRN = [];\n    this.rolesPO = [];\n    this.inventory_Access = [];\n    this.user_Access = [];\n    this.delete_GRN = [];\n    this.edit_GRN = [];\n    this.close_GRN = [];\n    this.delete_PO = [];\n    this.edit_PO = [];\n    this.close_PO = [];\n    this.delete_Indent = [];\n    this.edit_Indent = [];\n    this.close_Indent = [];\n    this.report_Access = [];\n    this.reportAccess = [];\n    this.showInventoryStatusField = false;\n    this.showUserStatusField = false;\n    this.showRecipeStatusField = false;\n    this.showPartyStatusField = false;\n    this.showDeleteGRNField = false;\n    this.showEditGRNField = false;\n    this.showCloseGRNField = false;\n    this.showDeletePOField = false;\n    this.showEditPOField = false;\n    this.showClosePOField = false;\n    this.showDeleteIndentField = false;\n    this.showEditIndentField = false;\n    this.showCloseIndentField = false;\n    this.showPartialIndentField = false;\n    this.filteredReports = [];\n    this.updatedReportAccess = {};\n    this.salesColumns = ['position', 'createdDate', 'status', 'createdTime'];\n    this.dataSourceSales = new MatTableDataSource([]);\n    this.weightedAvgColumns = ['position', 'status', 'createdTime'];\n    this.dataSourceWeightedAvg = new MatTableDataSource([]);\n    this.forecastColumns = ['position', 'status', 'createdTime'];\n    this.dataSourceForecast = new MatTableDataSource([]);\n    this.inventoryBank = [];\n    this.inventoryFilterCtrl = new FormControl();\n    this.inventory = new ReplaySubject(1);\n    this.userBank = [];\n    this.userFilterCtrl = new FormControl();\n    this.users = new ReplaySubject(1);\n    this.recipeBank = [];\n    this.recipeFilterCtrl = new FormControl();\n    this.recipe = new ReplaySubject(1);\n    this.partyBank = [];\n    this.partyFilterCtrl = new FormControl();\n    this.party = new ReplaySubject(1);\n    this.grnBank = [];\n    this.deleteGRNFilterCtrl = new FormControl();\n    this.deleteGRN = new ReplaySubject(1);\n    this.editGRNFilterCtrl = new FormControl();\n    this.editGRN = new ReplaySubject(1);\n    this.closeGRNFilterCtrl = new FormControl();\n    this.closeGRN = new ReplaySubject(1);\n    this.poBank = [];\n    this.deletePOFilterCtrl = new FormControl();\n    this.deletePO = new ReplaySubject(1);\n    this.editPOFilterCtrl = new FormControl();\n    this.editPO = new ReplaySubject(1);\n    this.closePOFilterCtrl = new FormControl();\n    this.closePO = new ReplaySubject(1);\n    this.indentBank = [];\n    this.deleteIndentFilterCtrl = new FormControl();\n    this.deleteIndent = new ReplaySubject(1);\n    this.editIndentFilterCtrl = new FormControl();\n    this.editIndent = new ReplaySubject(1);\n    this.closeIndentFilterCtrl = new FormControl();\n    this.closeIndent = new ReplaySubject(1);\n    this.reportBank = [];\n    this.reportFilterCtrl = new FormControl();\n    this.report = new ReplaySubject(1);\n    this.report_Bank = [];\n    this.report_FilterCtrl = new FormControl();\n    this.reports = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.accessForm = this.fb.group({\n      selectedSettingRoles: [''],\n      selectedExcelRoles: ['']\n    });\n    this.moduleForm = this.fb.group({\n      inventoryStatusButton: [''],\n      userStatusButton: [''],\n      recipeStatusButton: [''],\n      partyStatusButton: [''],\n      inventory_Access: [''],\n      user_Access: [''],\n      recipe_Access: [''],\n      party_Access: ['']\n    });\n    this.salesForm = this.fb.group({\n      startDate: ['', Validators.required],\n      endDate: ['', Validators.required]\n    });\n    this.wacForm = this.fb.group({\n      // startDate: [''],\n    });\n    this.forecastForm = this.fb.group({\n      startDate: [''],\n      endDate: ['']\n    });\n    this.grnForm = this.fb.group({\n      delete_GRN: [''],\n      edit_GRN: [''],\n      close_GRN: [''],\n      formType: ['grnAccess'],\n      deleteButtonGRN: [''],\n      editButtonGRN: [''],\n      closeButtonGRN: ['']\n    });\n    this.poForm = this.fb.group({\n      delete_PO: [''],\n      edit_PO: [''],\n      close_PO: [''],\n      formType: ['POAccess'],\n      deleteButtonPO: [''],\n      editButtonPO: [''],\n      closeButtonPO: ['']\n    });\n    this.indentForm = this.fb.group({\n      delete_Indent: [''],\n      edit_Indent: [''],\n      close_Indent: [''],\n      formType: ['indentAccess'],\n      deleteButtonIndent: [''],\n      editButtonIndent: [''],\n      closeButtonIndent: [''],\n      partialButtonIndent: ['']\n    });\n    this.emailForm = this.fb.group({\n      purchaseRequestButton: [''],\n      postGRNStatusButton: [''],\n      indentApprovalButton: [''],\n      purchaseApprovalButton: [''],\n      purchaseOrderButton: [''],\n      reportFailedButton: [''],\n      errorLogButton: [''],\n      approvedButton: [''],\n      rejectedButton: [''],\n      reportButton: [''],\n      piApprovalButton: ['']\n    });\n    this.reportForm = this.fb.group({\n      report_Access: [''],\n      reportAccess: ['']\n    });\n    this.roleForm = this.fb.group({\n      report_Access: [''],\n      reportAccess: ['']\n    });\n    this.user = this.auth.getCurrentUser();\n    const accessData = sessionStorage.getItem('access');\n    this.access = accessData ? JSON.parse(accessData) : {};\n    this.userRole = this.auth.getCurrRole();\n    this.readIPConfig();\n    this.getIPAddress();\n    this.getPriceTires();\n    this.getRoles();\n    this.getReportData();\n    if (this.access.settings) {\n      this.selectedSettingRoles.push(...this.access.settings);\n    }\n    if (this.access.bulkExcel) {\n      this.selectedExcelRoles.push(...this.access.bulkExcel);\n    }\n    this.isSmallDevice$ = this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).pipe(map(result => {\n      return result.matches;\n    }));\n  }\n  onTabChange(event) {\n    if (event.index === 2) {\n      this.getRoles();\n    }\n  }\n  togglePlaceholderLabel(inputValue) {\n    this.showPlaceholderLabel = inputValue.length === 0;\n    this.startingDate = new Date(); // or set a default value if needed\n  }\n\n  ngOnInit() {\n    // this.initializeMonthlyClosingDates();\n    this.monthlyClosingDates = this.getMonthlyClosingDates();\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged()).subscribe(newValue => {\n      this.vendorFilterBanks(newValue);\n    });\n    this.shareDataService.selectedBranchesSource.subscribe(data => {\n      this.branches = data;\n      this.filteredBranches = this.branches;\n      if (this.branches.length === 1) {\n        this.branchData.setValue(this.branches[0].restaurantIdOld);\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      } else {\n        this.branchData.setValue(null);\n        this.selectedLocation = null;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    this.dataSourceSales.paginator = this.salesPaginator;\n    this.dataSourceWeightedAvg.paginator = this.wacPaginator;\n    this.dataSourceForecast.paginator = this.forecastPaginator;\n    this.salesTab();\n    if (this.sharedData.checkMenuNavigate() === true) {\n      this.tabGroup.selectedIndex = 2;\n    }\n    // this.dataSource.sort = this.sort;\n  }\n\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  vendorFilterBanks(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = this.branches;\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  convertToIST(createTs) {\n    const date = new Date(createTs);\n    const offset = 6.5 * 60 * 60 * 1000;\n    const istTime = new Date(date.getTime() + offset);\n    let hours = istTime.getHours();\n    if (hours >= 12) {\n      hours = hours - 12;\n    } else {\n      hours = hours + 12;\n    }\n    const updatedTime = new Date(istTime);\n    updatedTime.setHours(hours);\n    return updatedTime.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n      hour12: true\n    });\n  }\n  getRoles() {\n    this.api.getRoles(this.user.tenantId).subscribe({\n      next: res => {\n        if (res['success'] == true) {\n          this.processRoles(res['roles'], 'GRN');\n          this.processRoles(res['roles'], 'PO');\n          this.processRoles(res['roles'], 'Indent');\n          this.roles = res['roles'];\n          this.inventoryBank = this.roles.map(role => role);\n          this.inventory.next(this.inventoryBank.slice());\n          this.inventoryFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.inventoryBank, this.inventoryFilterCtrl, this.inventory);\n          });\n          this.roles = res['roles'];\n          this.userBank = this.roles.map(role => role);\n          this.users.next(this.userBank.slice());\n          this.userFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.userBank, this.userFilterCtrl, this.users);\n          });\n          this.roles = res['roles'];\n          this.recipeBank = this.roles.map(role => role);\n          this.recipe.next(this.recipeBank.slice());\n          this.recipeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.recipeBank, this.recipeFilterCtrl, this.recipe);\n          });\n          this.roles = res['roles'];\n          this.partyBank = this.roles.map(role => role);\n          this.party.next(this.partyBank.slice());\n          this.partyFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.partyBank, this.partyFilterCtrl, this.party);\n          });\n          this.roles = res['roles'];\n          this.report_Bank = this.roles.map(role => role);\n          this.reports.next(this.report_Bank.slice());\n          this.report_FilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.report_Bank, this.report_FilterCtrl, this.reports);\n          });\n          let superAdminIndex = this.roles.indexOf(\"superAdmin\");\n          if (superAdminIndex !== -1) {\n            this.roles.unshift(this.roles.splice(superAdminIndex, 1)[0]);\n          }\n        }\n      },\n      error: err => {}\n    });\n  }\n  Filter(bank, form, data) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    data.next(bank.filter(data => data.toLowerCase().indexOf(search) > -1));\n  }\n  processRoles(roles, type) {\n    const bankName = `${type.toLowerCase()}Bank`;\n    const deleteCtrl = `delete${type}`;\n    const editCtrl = `edit${type}`;\n    const closeCtrl = `close${type}`;\n    const deleteFilterCtrl = `${deleteCtrl}FilterCtrl`;\n    const editFilterCtrl = `${editCtrl}FilterCtrl`;\n    const closeFilterCtrl = `${closeCtrl}FilterCtrl`;\n    this[bankName] = roles.map(area => area);\n    this[deleteCtrl].next(this[bankName].slice());\n    this[deleteFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[deleteFilterCtrl], this[deleteCtrl]);\n    });\n    this[editCtrl].next(this[bankName].slice());\n    this[editFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[editFilterCtrl], this[editCtrl]);\n    });\n    this[closeCtrl].next(this[bankName].slice());\n    this[closeFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[closeFilterCtrl], this[closeCtrl]);\n    });\n  }\n  toggleSelectAll(formType, action) {\n    const control = this[`${formType.toLowerCase()}Form`].controls[`${action}_${formType}`];\n    const bankName = `${formType.toLowerCase()}Bank`;\n    if (control.value.length - 1 === this[bankName].length) {\n      control.setValue([]);\n    } else {\n      control.setValue(this[bankName]);\n    }\n    this.validateSales(control.value);\n  }\n  toggleSelectAllDeleteGRN() {\n    this.toggleSelectAll('GRN', 'delete');\n  }\n  toggleSelectAllEditGRN() {\n    this.toggleSelectAll('GRN', 'edit');\n  }\n  toggleSelectAllCloseGRN() {\n    this.toggleSelectAll('GRN', 'close');\n  }\n  toggleSelectAllDeletePO() {\n    this.toggleSelectAll('PO', 'delete');\n  }\n  toggleSelectAllEditPO() {\n    this.toggleSelectAll('PO', 'edit');\n  }\n  toggleSelectAllClosePO() {\n    this.toggleSelectAll('PO', 'close');\n  }\n  toggleSelectAllDeleteIndent() {\n    this.toggleSelectAll('Indent', 'delete');\n  }\n  // toggleSelectAllEditIndent() {\n  //   this.toggleSelectAll('Indent', 'edit');\n  // }\n  toggleSelectAllCloseIndent() {\n    this.toggleSelectAll('Indent', 'close');\n  }\n  toggleSelectAllInventory() {\n    const control = this.moduleForm.controls['inventory_Access'];\n    if (control.value.length - 1 === this.inventoryBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.inventory_Access);\n    } else {\n      control.setValue(this.inventoryBank);\n      this.validateSales(this.moduleForm.value.inventory_Access);\n    }\n  }\n  toggleSelectAllUser() {\n    const control = this.moduleForm.controls['user_Access'];\n    if (control.value.length - 1 === this.userBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.user_Access);\n    } else {\n      control.setValue(this.userBank);\n      this.validateSales(this.moduleForm.value.user_Access);\n    }\n  }\n  toggleSelectAllRecipe() {\n    const control = this.moduleForm.controls['recipe_Access'];\n    if (control.value.length - 1 === this.recipeBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.recipe_Access);\n    } else {\n      control.setValue(this.recipeBank);\n      this.validateSales(this.moduleForm.value.recipe_Access);\n    }\n  }\n  toggleSelectAllParty() {\n    const control = this.moduleForm.controls['party_Access'];\n    if (control.value.length - 1 === this.partyBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.party_Access);\n    } else {\n      control.setValue(this.partyBank);\n      this.validateSales(this.moduleForm.value.party_Access);\n    }\n  }\n  selectAllRolesForReport() {\n    const control = this.reportForm.controls['report_Access'];\n    if (control.value.length - 1 === this.report_Bank.length) {\n      control.setValue([]);\n      this.validateSales(this.reportForm.value.report_Access);\n    } else {\n      control.setValue(this.report_Bank);\n      this.validateSales(this.reportForm.value.report_Access);\n    }\n  }\n  selectAllReportsForRoles() {\n    const control = this.roleForm.controls['reportAccess'];\n    if (control.value.length - 1 === this.reportBank.length) {\n      control.setValue([]);\n      this.validateSales(this.roleForm.value.reportAccess);\n    } else {\n      control.setValue(this.reportBank);\n      this.validateSales(this.roleForm.value.reportAccess);\n    }\n  }\n  openDialog() {\n    this.dialog.open(this.dialogRef, {\n      autoFocus: false,\n      disableClose: true,\n      minWidth: '40vw'\n    });\n  }\n  closeDialog() {\n    this.dialog.closeAll();\n  }\n  validateInput() {\n    if (this.selectedOption == \"individual\") {\n      this.isIPValid = this.ipPattern.test(this.ipOrSubnet);\n    } else {\n      let start = this.ipPattern.test(this.startIP);\n      let end = this.ipPattern.test(this.endIP);\n      if (start && end) {\n        this.isIPValid = true;\n      } else {\n        this.isIPValid = false;\n      }\n    }\n  }\n  validateSales(delete_GRN) {\n    this.profitTarget < 1 ? this.profitTarget = 1 : undefined;\n    this.cd.detectChanges();\n  }\n  roleChange(selectedRole) {\n    this.filteredRole = this.roles.find(role => role === selectedRole);\n    const requiredReports = this.responseReport.filter(report => report.access.includes(selectedRole)).map(report => report.displayName);\n    const control = this.roleForm.controls['reportAccess'];\n    control.setValue(requiredReports);\n    this.validateSales(requiredReports);\n    this.cd.detectChanges();\n  }\n  reportChange(selectedReport) {\n    this.filteredReports = this.responseReport.filter(report => report.displayName === selectedReport);\n    if (this.filteredReports.length > 0) {\n      const reportAccess = this.updatedReportAccess[selectedReport] || this.filteredReports[0].access;\n      this.reportForm.patchValue({\n        report_Access: reportAccess\n      });\n    }\n    this.cd.detectChanges();\n  }\n  getPriceTires() {\n    this.api.getPOSPriceTires(this.user.tenantId, this.branchData.value).subscribe({\n      next: res => {\n        if (Array.isArray(res)) {\n          this.priceTierList = res.map(({\n            id,\n            name\n          }) => ({\n            id,\n            name\n          }));\n          let decorationPackage;\n          if (this.object && this.object[this.branchData.value]) {\n            decorationPackage = this.priceTierList.find(item => item.name === this.object[this.branchData.value].priceTierName || item.id === this.object[this.branchData.value].defaultPriceTier);\n          }\n          this.priceData.setValue(decorationPackage || null);\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  readIPConfig() {\n    this.api.readIPConfig(this.user.tenantId).subscribe({\n      next: res => {\n        if (res?.['success']) {\n          const data = res['data'];\n          const permissions = data?.['permission'] || {};\n          this.responseGRN = permissions['grnAccess'] || [];\n          this.responsePO = permissions['POAccess'] || [];\n          this.responseIndent = permissions['indentAccess'] || [];\n          this.responseEmail = permissions['emailConfiguration'] || [];\n          this.responseUIAccess = permissions['UIAccess'] || [];\n          this.salesLocation = data['multiPosUser'] || [];\n          const defaultPriceTier = data['defaultPriceTier'] || {};\n          if (defaultPriceTier && typeof defaultPriceTier === 'object') {\n            const keys = Object.keys(defaultPriceTier);\n            if (keys.length > 0) {\n              const lastKey = keys[keys.length - 1];\n              this.branchData.setValue(lastKey);\n              this.object = defaultPriceTier;\n              this.getPriceTires();\n            }\n          } else {\n            console.error('defaultPriceTier is either undefined or not an object');\n          }\n          if (permissions.hasOwnProperty('sales') && permissions['sales'].hasOwnProperty('profitTarget')) {\n            this.profitTarget = permissions['sales']['profitTarget'];\n          } else {\n            this.profitTarget = 1;\n          }\n          this.isIPValid = true;\n          this.selectedOption = data['ip_type'];\n          if (this.selectedOption == 'range') {\n            this.startIP = data['ips_range']?.['start'] || '0.0.0.0';\n            this.endIP = data['ips_range']?.['end'] || '0.0.0.0';\n            if (this.startIP == '0.0.0.0') {\n              this.IpType = 'withoutIp';\n            } else {\n              this.IpType = 'withIp';\n              this.showIpProcess = true;\n            }\n          } else {\n            this.ipOrSubnet = data['allowed_ips']?.[0] || '';\n            this.IpType = 'withIp';\n            this.showIpProcess = true;\n          }\n          this.cd.detectChanges();\n          this.initializeFormFields();\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  getReportData() {\n    this.api.getReportData(this.user.tenantId).subscribe({\n      next: res => {\n        if (res?.['success']) {\n          this.responseReport = res['data']['types'];\n          this.displayNames = this.responseReport.map(report => report.displayName);\n          this.reportBank = this.responseReport.map(report => report.displayName);\n          this.report.next(this.reportBank.slice());\n          this.reportFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.reportBank, this.reportFilterCtrl, this.report);\n          });\n          this.cd.detectChanges();\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  initializeFormFields() {\n    if (this.responseGRN) {\n      this.grnForm.patchValue({\n        delete_GRN: this.responseGRN.deleteAccess,\n        edit_GRN: this.responseGRN.editAccess,\n        close_GRN: this.responseGRN.rtvAccess,\n        deleteButtonGRN: this.responseGRN.delete ? 'Yes' : 'No',\n        editButtonGRN: this.responseGRN.edit ? 'Yes' : 'No',\n        closeButtonGRN: this.responseGRN.rtv ? 'Yes' : 'No'\n      });\n      this.showDeleteGRNField = this.responseGRN.delete;\n      this.showEditGRNField = this.responseGRN.edit;\n      this.showCloseGRNField = this.responseGRN.rtv;\n    }\n    if (this.responsePO) {\n      this.poForm.patchValue({\n        delete_PO: this.responsePO.deleteAccess,\n        edit_PO: this.responsePO.editAccess,\n        close_PO: this.responsePO.closeAccess,\n        deleteButtonPO: this.responsePO.delete ? 'Yes' : 'No',\n        editButtonPO: this.responsePO.edit ? 'Yes' : 'No',\n        closeButtonPO: this.responsePO.close ? 'Yes' : 'No'\n      });\n      this.showDeletePOField = this.responsePO.delete;\n      this.showEditPOField = this.responsePO.edit;\n      this.showClosePOField = this.responsePO.close;\n    }\n    if (this.responseIndent) {\n      this.indentForm.patchValue({\n        delete_Indent: this.responseIndent.deleteAccess,\n        // edit_Indent: this.responseIndent.editAccess,\n        close_Indent: this.responseIndent.closeAccess,\n        deleteButtonIndent: this.responseIndent.delete ? 'Yes' : 'No',\n        // editButtonIndent: this.responseIndent.edit ? 'Yes' : 'No',\n        closeButtonIndent: this.responseIndent.close ? 'Yes' : 'No',\n        partialButtonIndent: this.responseIndent.partial ? 'Yes' : 'No'\n      });\n      this.showDeleteIndentField = this.responseIndent.delete;\n      // this.showEditIndentField = this.responseIndent.edit;\n      this.showCloseIndentField = this.responseIndent.close;\n    }\n    if (this.responseEmail) {\n      this.emailForm.patchValue({\n        purchaseRequestButton: this.responseEmail.purchaseRequest ? 'Yes' : 'No',\n        postGRNStatusButton: this.responseEmail.postGrnStatus ? 'Yes' : 'No',\n        indentApprovalButton: this.responseEmail.indentApproval ? 'Yes' : 'No',\n        purchaseApprovalButton: this.responseEmail.purchaseApproval ? 'Yes' : 'No',\n        purchaseOrderButton: this.responseEmail.purchaseOrder ? 'Yes' : 'No',\n        // reportFailedButton: this.responseEmail.reportFailed ? 'Yes' : 'No',\n        // errorLogButton: this.responseEmail.systemErrorLog ? 'Yes' : 'No',\n        // approvedButton: this.responseEmail.approved ? 'Yes' : 'No',\n        // rejectedButton: this.responseEmail.rejected ? 'Yes' : 'No',\n        // reportButton: this.responseEmail.report ? 'Yes' : 'No',\n        piApprovalButton: this.responseEmail.piApproval ? 'Yes' : 'No'\n      });\n    }\n    if (this.responseUIAccess) {\n      this.moduleForm.patchValue({\n        inventory_Access: this.responseUIAccess.inventory.access,\n        user_Access: this.responseUIAccess.user.access,\n        recipe_Access: this.responseUIAccess.recipe.access,\n        // party_Access: this.responseUIAccess.party.access,\n        inventoryStatusButton: this.responseUIAccess.inventory.status ? 'Yes' : 'No',\n        userStatusButton: this.responseUIAccess.user.status ? 'Yes' : 'No',\n        recipeStatusButton: this.responseUIAccess.recipe.status ? 'Yes' : 'No'\n        // partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No',\n      });\n\n      this.showInventoryStatusField = this.responseUIAccess.inventory.status;\n      this.showUserStatusField = this.responseUIAccess.user.status;\n      this.showRecipeStatusField = this.responseUIAccess.recipe.status;\n      // this.showPartyStatusField = this.responseUIAccess.party.status;\n    }\n\n    if (this.responseUIAccess.party) {\n      this.moduleForm.patchValue({\n        party_Access: this.responseUIAccess.party.access,\n        partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No'\n      });\n      this.showPartyStatusField = this.responseUIAccess.party.status;\n    } else {\n      this.moduleForm.patchValue({\n        partyStatusButton: 'No'\n      });\n    }\n  }\n  onSubmit() {\n    let obj;\n    if (this.IpType == \"withoutIp\") {\n      obj = {\n        \"tenantId\": this.user.tenantId,\n        \"email\": this.user.userEmail,\n        \"name\": this.user.name,\n        \"ip_type\": 'range',\n        \"ips_range\": {\n          \"start\": '0.0.0.0',\n          \"end\": '***************'\n        },\n        \"allowed_ips\": [this.ipOrSubnet]\n      };\n    } else {\n      obj = {\n        \"tenantId\": this.user.tenantId,\n        \"email\": this.user.userEmail,\n        \"name\": this.user.name,\n        \"ip_type\": this.selectedOption,\n        \"ips_range\": {\n          \"start\": this.startIP,\n          \"end\": this.endIP\n        },\n        \"allowed_ips\": [this.ipOrSubnet]\n      };\n    }\n    this.api.updateIPConfig(obj).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully!');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.cd.detectChanges();\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  addConfig() {\n    let obj = {};\n    obj['branch'] = this.branchData.value;\n    obj['tenantId'] = this.user.tenantId;\n    obj['priceTierData'] = this.priceData.value.id;\n    obj['priceTierName'] = this.priceData.value.name;\n    obj['salesConfig'] = true;\n    this.api.updateIPConfig(obj).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully!');\n          const defaultPriceTier = res['data']['defaultPriceTier'] || {};\n          if (defaultPriceTier && typeof defaultPriceTier === 'object') {\n            this.object = defaultPriceTier;\n          }\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.cd.detectChanges();\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  getIPAddress() {\n    this.api.getIPAddress().subscribe(res => {\n      this.currentIp = res['ipString'];\n      this.cd.detectChanges();\n    });\n  }\n  submitIp() {\n    if (this.IpType === 'withoutIp') {\n      this.startIP;\n      this.endIP;\n      this.onSubmit();\n      this.masterDataService.setNavigation('inventoryList');\n      this.router.navigate(['/dashboard/home']);\n      this.dialog.closeAll();\n    } else if (this.IpType === 'withIp') {\n      this.dialog.closeAll();\n    } else {\n      this.notify.snackBarShowInfo('select any of one IP type');\n    }\n  }\n  radioChange() {\n    if (this.IpType == \"withIp\") {\n      this.showIpProcess = true;\n    } else {\n      this.showIpProcess = false;\n      this.startIP = '0.0.0.0';\n      this.endIP = '***************';\n      this.selectedOption = 'range';\n    }\n  }\n  getAccess() {\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      if ('access' in data) {\n        this.sharedData.setAccess(data['access']);\n        const accessString = JSON.stringify(data['access']);\n        sessionStorage.setItem(GlobalsService.accessData, accessString);\n        sessionStorage.setItem('access', accessString);\n        this.access = data['access'];\n        if (this.access.settings) {\n          this.selectedSettingRoles.push(...this.access.settings);\n        }\n        if (this.access.bulkExcel) {\n          this.selectedExcelRoles.push(...this.access.bulkExcel);\n        }\n      }\n    });\n  }\n  submitPermission() {\n    let settingRole = [];\n    let bulkExcelRole = [];\n    settingRole = this.accessForm.value.selectedSettingRoles;\n    bulkExcelRole = this.accessForm.value.selectedExcelRoles;\n    settingRole.push('superAdmin');\n    bulkExcelRole.push('superAdmin');\n    let obj = {};\n    let access = {\n      settings: Array.from(new Set(settingRole)),\n      bulkExcel: Array.from(new Set(bulkExcelRole))\n    };\n    obj['tenantId'] = this.user.tenantId;\n    obj['access'] = access;\n    this.api.updateAccess(obj).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  updateGRN() {\n    this.updatePermissions('GRN', this.grnForm, this.showDeleteGRNField, this.showEditGRNField, this.showCloseGRNField);\n  }\n  updatePO() {\n    this.updatePermissions('PO', this.poForm, this.showDeletePOField, this.showEditPOField, this.showClosePOField);\n  }\n  updateIndent() {\n    this.updatePermissions('Indent', this.indentForm, this.showDeleteIndentField, this.showEditIndentField, this.showCloseIndentField, this.showPartialIndentField);\n  }\n  refreshForecast() {\n    this.forecastTab();\n  }\n  refreshWac() {\n    this.wacTab();\n  }\n  refreshSales() {\n    this.salesTab();\n  }\n  tabChange(event) {\n    if (event.tab.textLabel === 'SALES') {\n      this.salesTab();\n    }\n    if (event.tab.textLabel === 'WAC') {\n      this.wacTab();\n    }\n    if (event.tab.textLabel === 'FORECAST') {\n      this.forecastTab();\n    }\n  }\n  salesTab() {\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['event'] = 'sales';\n    this.api.salesRetrigger(obj).subscribe({\n      next: res => {\n        const sales = res['data'];\n        this.dataSourceSales.data = res['data'];\n        this.dataSourceSales.paginator = this.salesPaginator;\n        this.isSalesDataReady = true;\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  wacTab() {\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['event'] = 'weightedAverage';\n    this.api.wacRetrigger(obj).subscribe({\n      next: res => {\n        const wac = res['data'];\n        this.dataSourceWeightedAvg.data = res['data'];\n        this.dataSourceWeightedAvg.paginator = this.wacPaginator;\n        this.isWacDataReady = true;\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  forecastTab() {\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['event'] = 'forecast';\n    this.api.forecastRetrigger(obj).subscribe({\n      next: res => {\n        const wac = res['data'];\n        this.dataSourceForecast.data = res['data'];\n        this.dataSourceForecast.paginator = this.forecastPaginator;\n        this.isForecastReady = true;\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  dateCorrection(date) {\n    const changedDate = new Date(date);\n    changedDate.setHours(changedDate.getHours() + 5, changedDate.getMinutes() + 30);\n    return changedDate.toISOString();\n  }\n  salesRerun() {\n    if (this.salesForm.invalid) {\n      this.salesForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let obj = {\n        tenantId: this.user.tenantId,\n        event: 'sales',\n        createdBy: this.user.email,\n        startDate: this.dateCorrection(this.salesForm.get('startDate').value),\n        endDate: this.dateCorrection(this.salesForm.get('endDate').value)\n      };\n      this.api.salesRerun(obj).subscribe({\n        next: res => {\n          if (res.result === 'success') {\n            this.notify.snackBarShowSuccess('Submitted Successfully');\n            this.salesForm.reset();\n            this.refreshSales();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  weightedAvg() {\n    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1) + 'Z';\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['event'] = 'weightedAverage';\n    obj['createdBy'] = this.user.email;\n    obj['currentDate'] = istDate;\n    this.api.weightedAvg(obj).subscribe({\n      next: res => {\n        if (res.result === 'success') {\n          this.notify.snackBarShowSuccess('Submitted Successfully');\n          this.refreshWac();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  forecast() {\n    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1) + 'Z';\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['event'] = 'forecast';\n    obj['createdBy'] = this.user.email;\n    obj['currentDate'] = istDate;\n    // startDate: this.dateCorrection(this.forecastForm.get('startDate').value),\n    // endDate: this.dateCorrection(this.forecastForm.get('endDate').value)\n    this.api.forecastData(obj).subscribe({\n      next: res => {\n        if (res.result === 'success') {\n          this.refreshForecast();\n          this.notify.snackBarShowSuccess('Submitted Successfully');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  updateModule() {\n    let module = this.moduleForm.value;\n    let obj = {\n      accessPermission: {\n        inventory: {\n          status: module.inventoryStatusButton === 'Yes',\n          access: module.inventory_Access\n        },\n        user: {\n          status: module.userStatusButton === 'Yes',\n          access: module.user_Access\n        },\n        recipe: {\n          status: module.recipeStatusButton === 'Yes',\n          access: module.recipe_Access\n        },\n        party: {\n          status: module.partyStatusButton === 'Yes',\n          access: module.party_Access\n        }\n      },\n      tenantId: this.user.tenantId\n    };\n    this.api.updateConfigAccess(obj).subscribe({\n      next: res => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  updateEmail() {\n    let emailValues = this.emailForm.value;\n    let obj = {\n      purchaseRequest: emailValues.purchaseRequestButton === 'Yes',\n      postGrnStatus: emailValues.postGRNStatusButton === 'Yes',\n      indentApproval: emailValues.indentApprovalButton === 'Yes',\n      purchaseApproval: emailValues.purchaseApprovalButton === 'Yes',\n      purchaseOrder: emailValues.purchaseOrderButton === 'Yes',\n      piApproval: emailValues.piApprovalButton === 'Yes',\n      // reportFailed: emailValues.reportFailedButton === 'Yes',\n      // systemErrorLog: emailValues.errorLogButton === 'Yes',\n      // approved: emailValues.approvedButton === 'Yes',\n      // rejected: emailValues.rejectedButton === 'Yes',\n      // report: emailValues.reportButton === 'Yes',\n      reportFailed: false,\n      systemErrorLog: false,\n      approved: false,\n      rejected: false,\n      report: false,\n      tenantId: this.user.tenantId\n    };\n    this.api.updateConfigAccess(obj).subscribe({\n      next: res => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  getBackendName(reportAccess) {\n    const report = this.responseReport.find(report => report.displayName === reportAccess);\n    return report ? report.backendName : '';\n  }\n  updateReport(reportBased = true) {\n    let report = this.reportForm.value;\n    let backendName = this.getBackendName(report.reportAccess);\n    let obj = {};\n    if (reportBased) {\n      obj = {\n        tenantId: this.user.tenantId,\n        reportName: backendName,\n        access: report.report_Access,\n        reportBased: true\n      };\n    } else {\n      obj = {\n        tenantId: this.user.tenantId,\n        role: this.roleForm.get('report_Access').value,\n        reports: this.roleForm.get('reportAccess').value,\n        reportBased: false\n      };\n    }\n    this.api.updateReport(obj).subscribe({\n      next: res => {\n        if (res.success) {\n          this.updatedReportAccess[report.reportAccess] = report.report_Access;\n          this.getReportData();\n          reportBased ? this.roleForm.patchValue({\n            report_Access: undefined\n          }) : undefined;\n          this.notify.snackBarShowSuccess('Updated Successfully');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  updatePermissions(formType, formGroup, showDeleteField, showEditField, showCloseField, showPartialIndentField) {\n    let deleteRole = formGroup.value[`delete_${formType}`] || [];\n    let editRole = formGroup.value[`edit_${formType}`] || [];\n    let closeRole = formGroup.value[`close_${formType}`] || [];\n    let obj = {\n      delete: showDeleteField,\n      edit: showEditField,\n      close: showCloseField,\n      formType: formGroup.value.formType,\n      tenantId: this.user.tenantId,\n      deleteAccess: Array.from(new Set(deleteRole)),\n      editAccess: Array.from(new Set(editRole)),\n      closeAccess: Array.from(new Set(closeRole))\n    };\n    if (formGroup.contains('partialButtonIndent')) {\n      obj.partial = formGroup.value['partialButtonIndent'] === 'Yes';\n    }\n    this.api.updateConfigAccess(obj).subscribe({\n      next: res => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  inventoryStatusShown(event) {\n    this.showInventoryStatusField = event.value === 'Yes';\n  }\n  userStatusShown(event) {\n    this.showUserStatusField = event.value === 'Yes';\n  }\n  recipeStatusShown(event) {\n    this.showRecipeStatusField = event.value === 'Yes';\n  }\n  partyStatusShown(event) {\n    this.showPartyStatusField = event.value === 'Yes';\n  }\n  isDeleteGrnShown(event) {\n    this.showDeleteGRNField = event.value === 'Yes';\n  }\n  isEditGrnShown(event) {\n    this.showEditGRNField = event.value === 'Yes';\n  }\n  isCloseGrnShown(event) {\n    this.showCloseGRNField = event.value === 'Yes';\n  }\n  isDeletePoShown(event) {\n    this.showDeletePOField = event.value === 'Yes';\n  }\n  isEditPoShown(event) {\n    this.showEditPOField = event.value === 'Yes';\n  }\n  isClosePoShown(event) {\n    this.showClosePOField = event.value === 'Yes';\n  }\n  isDeleteIndentShown(event) {\n    this.showDeleteIndentField = event.value === 'Yes';\n  }\n  isCloseIndentShown(event) {\n    this.showCloseIndentField = event.value === 'Yes';\n  }\n  onLocationSubmit() {}\n  isPartialIndentShown(event) {\n    this.showPartialIndentField = event.value === 'Yes';\n  }\n  onDateSelectionChange(value) {\n    if (value === 'startOfMonth') {\n      this.monthlyClosingDates.forEach(month => {\n        this.startingDates[month.month] = this.getStartOfMonthDate(month);\n      });\n    } else {\n      this.startingDates = {};\n    }\n    this.selectedDateOption = value;\n  }\n  goBackToLocationSelection() {\n    this.selectedDateOption = null;\n    this.showLocationSelection = true;\n  }\n  getMonthlyClosingDates() {\n    return [{\n      month: 'January',\n      dates: [new Date()]\n    }, {\n      month: 'February',\n      dates: [new Date()]\n    }, {\n      month: 'March',\n      dates: [new Date()]\n    }, {\n      month: 'April',\n      dates: [new Date()]\n    }, {\n      month: 'May',\n      dates: [new Date()]\n    }, {\n      month: 'June',\n      dates: [new Date()]\n    }, {\n      month: 'July',\n      dates: [new Date()]\n    }, {\n      month: 'August',\n      dates: [new Date()]\n    }, {\n      month: 'September',\n      dates: [new Date()]\n    }, {\n      month: 'October',\n      dates: [new Date()]\n    }, {\n      month: 'November',\n      dates: [new Date()]\n    }, {\n      month: 'December',\n      dates: [new Date()]\n    }];\n  }\n  onLocationChange(event) {\n    this.selectedLocation = event.value;\n    this.showLocationSelection = false;\n  }\n  submit() {\n    if (!this.selectedLocation) {\n      alert('Please select a location.');\n      return;\n    }\n    const closingDetails = {};\n    if (this.pickDateOption === 'no') {\n      closingDetails[this.selectedLocation] = {\n        status: false,\n        selectedClosingDates: {}\n      };\n    } else if (this.pickDateOption === 'yes' && this.selectedDateOption === 'startOfMonth') {\n      closingDetails[this.selectedLocation] = {\n        status: true,\n        selectedClosingDates: {}\n      };\n      this.monthlyClosingDates.forEach(month => {\n        const startOfMonthDate = this.getStartOfMonthDate(month).toISOString().split('T')[0];\n        closingDetails[this.selectedLocation].selectedClosingDates[month.month.toLowerCase()] = startOfMonthDate;\n      });\n    } else {\n      closingDetails[this.selectedLocation] = {\n        status: true,\n        selectedClosingDates: {}\n      };\n    }\n    const payload = {\n      location: this.selectedLocation,\n      closingDetails: closingDetails\n    };\n    const headers = new HttpHeaders({\n      'accept': 'application/json',\n      'Content-Type': 'application/json'\n    });\n    this.http.post(`${this.engineUrl}master_data/update-closing-dates`, payload, {\n      headers\n    }).subscribe(response => {\n      this.notify.snackBarShowSuccess('Closing dates have been successfully updated.');\n    }, error => {\n      console.error('Error:', error);\n      this.notify.snackBarShowError('Failed to update closing dates. Please try again.');\n    });\n  }\n  closeMessage() {\n    this.submissionMessage = null;\n  }\n  getStartOfMonthDate(month) {\n    const monthIndex = new Date(Date.parse(month.month + \" 2, 2024\")).getMonth(); // Get the month index\n    return new Date(2024, monthIndex, 2);\n  }\n  isFinalStep() {\n    if (!this.selectedLocation) {\n      return false;\n    }\n    if (this.pickDateOption === 'no') {\n      return true;\n    }\n    return this.pickDateOption === 'yes' && !!this.selectedDateOption;\n  }\n  getJobStatus(element) {\n    return element.error ? 'Something Went Wrong' : element.pssi ? 'Completed' : 'In-Progress';\n  }\n  static {\n    this.ɵfac = function DashboardProfileComponent_Factory(t) {\n      return new (t || DashboardProfileComponent)(i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.InventoryService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i6.AuthService), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.MasterDataService), i0.ɵɵdirectiveInject(i9.Router), i0.ɵɵdirectiveInject(i2.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardProfileComponent,\n      selectors: [[\"app-dashboard-profile\"]],\n      viewQuery: function DashboardProfileComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.salesPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wacPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.forecastPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialogRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabGroup = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: DateAdapter,\n        useClass: MomentDateAdapter,\n        deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]\n      }, {\n        provide: MAT_DATE_FORMATS,\n        useValue: MY_DATE_FORMATS\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 438,\n      vars: 74,\n      consts: [[1, \"main-content\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"card\"], [1, \"card-header\", \"card-header-danger\"], [1, \"header-content\"], [1, \"icon\"], [1, \"mat-headline-6\", \"title\"], [1, \"card-body\"], [\"mat-stretch-tabs\", \"false\", \"mat-align-tabs\", \"start\", 3, \"selectedTabChange\"], [\"tabGroup\", \"\"], [\"label\", \"PERSONAL INFO\"], [\"appearance\", \"outlined\"], [1, \"mat-card-content\"], [1, \"list-item-content\"], [1, \"bold\"], [\"label\", \"IP CONFIG\"], [1, \"col\"], [1, \"ipText\"], [3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"withIp\"], [\"value\", \"withoutIp\"], [\"class\", \"my-4 mx-auto\", 3, \"ngSubmit\", 4, \"ngIf\"], [4, \"ngIf\"], [\"label\", \"SALES\"], [1, \"my-4\", \"mx-auto\", 2, \"max-width\", \"500px\", 3, \"ngSubmit\"], [\"class\", \"row mb-3 mt-3\", 4, \"ngIf\"], [1, \"row\", \"mb-3\", \"mt-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\"], [\"ng\", \"\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"float-end\", \"mb-3\"], [\"label\", \"ACCESS\"], [1, \"my-4\", \"mx-auto\", 2, \"max-width\", \"500px\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"selectedSettingRoles\", \"multiple\", \"\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"selectedExcelRoles\", \"multiple\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"label\", \"PERMISSION\"], [\"label\", \"Module Access\"], [\"formControlName\", \"inventoryStatusButton\", 1, \"col\", 3, \"change\"], [\"value\", \"Yes\"], [\"value\", \"No\"], [\"formControlName\", \"userStatusButton\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"recipeStatusButton\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"partyStatusButton\", 1, \"col\", 3, \"change\"], [\"label\", \"GRN Access\"], [\"formControlName\", \"deleteButtonGRN\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"editButtonGRN\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"closeButtonGRN\", 1, \"col\", 3, \"change\"], [\"label\", \"PO Access\"], [\"formControlName\", \"deleteButtonPO\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"editButtonPO\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"closeButtonPO\", 1, \"col\", 3, \"change\"], [\"label\", \"Indent Access\"], [\"formControlName\", \"deleteButtonIndent\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"closeButtonIndent\", 1, \"col\", 3, \"change\"], [\"formControlName\", \"partialButtonIndent\", 1, \"col\", 3, \"change\"], [\"label\", \"Email Access\"], [1, \"emailAccessIpText\"], [\"formControlName\", \"purchaseRequestButton\", 1, \"col\"], [\"formControlName\", \"postGRNStatusButton\", 1, \"col\"], [\"formControlName\", \"indentApprovalButton\", 1, \"col\"], [\"formControlName\", \"purchaseApprovalButton\", 1, \"col\"], [\"formControlName\", \"purchaseOrderButton\", 1, \"col\"], [\"formControlName\", \"piApprovalButton\", 1, \"col\"], [\"label\", \"Report Access\"], [1, \"tabGroup\"], [\"label\", \"Report Based\"], [\"formControlName\", \"reportAccess\", 3, \"ngModelChange\"], [\"placeholderLabel\", \"Search...\", 3, \"formControl\"], [\"multi\", \"true\"], [4, \"ngFor\", \"ngForOf\"], [\"label\", \"Role Based\"], [\"formControlName\", \"report_Access\", 3, \"ngModelChange\"], [\"multi\", \"true\", 4, \"ngIf\"], [\"label\", \"FREEZE CLOSING OPERATIONS\"], [1, \"row\", \"mb-3\"], [\"appearance\", \"outline\", 1, \"custom-form-field\"], [\"panelClass\", \"custom-dropdown\", 3, \"formControl\", \"selectionChange\"], [\"class\", \"center-label\", 4, \"ngIf\"], [\"matInput\", \"\", 2, \"z-index\", \"1\", \"padding-left\", \"10px\", 3, \"formControl\", \"keydown.space\", \"input\"], [\"inputField\", \"\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [\"class\", \"btn btn-primary float-end mb-3\", 4, \"ngIf\"], [\"label\", \"RETRIGGER\"], [1, \"tabGroup\", 3, \"selectedTabChange\"], [1, \"cardContent\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"refresh\", \"end\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"datePicker\"], [\"matInput\", \"\", \"formControlName\", \"startDate\", 3, \"matDatepicker\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"touchUi\", \"\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"endDate\", 3, \"matDatepicker\", \"disabled\", \"min\"], [\"endPicker\", \"\"], [\"type\", \"submit\", \"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"refresh\"], [1, \"section\"], [\"section\", \"\", \"widgetsContent\", \"\"], [\"class\", \"tableDiv\", 4, \"ngIf\"], [\"class\", \"text-center m-3\", 4, \"ngIf\"], [1, \"mat-paginator-sticky\", 3, \"pageSizeOptions\"], [\"salesPaginator\", \"\"], [\"label\", \"WAC\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"refresh\", \"float-end\", \"gap\", 3, \"click\"], [\"type\", \"submit\", \"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"refresh\", \"float-end\", \"gap\"], [\"wacPaginator\", \"\"], [\"label\", \"FORECAST\"], [\"forecastPaginator\", \"\"], [\"showDialog\", \"\"], [1, \"my-4\", \"mx-auto\", 3, \"ngSubmit\"], [1, \"mb-3\"], [1, \"mt-3\"], [1, \"row\", \"mt-3\"], [1, \"col-md-4\"], [1, \"form-check\"], [\"type\", \"radio\", \"name\", \"option\", \"id\", \"option1\", \"value\", \"individual\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"option1\", 1, \"form-check-label\", \"fw-normal\", \"cursor-pointer\"], [\"type\", \"radio\", \"name\", \"option\", \"id\", \"option2\", \"value\", \"range\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"option2\", 1, \"form-check-label\", \"fw-normal\", \"cursor-pointer\"], [\"type\", \"submit\", \"class\", \"btn btn-primary float-end mb-3\", \"matTooltip\", \"Invalid IP Address\", 3, \"disabled\", 4, \"ngIf\"], [\"for\", \"individual\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"individual\", \"name\", \"individual\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"error-message\"], [\"for\", \"startIP\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"startIP\", \"name\", \"startIP\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"endIP\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"endIP\", \"name\", \"endIP\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", \"matTooltip\", \"Invalid IP Address\", 1, \"btn\", \"btn-primary\", \"float-end\", \"mb-3\", 3, \"disabled\"], [\"type\", \"submit\", \"matTooltip\", \"Invalid IP Address\", 1, \"btn\", \"btn-primary\", \"float-end\", \"mb-3\", 3, \"click\"], [3, \"value\"], [3, \"value\", \"disabled\"], [\"formControlName\", \"inventory_Access\", \"multiple\", \"\", 3, \"ngModelChange\"], [1, \"hide-checkbox\", 3, \"click\"], [\"matSuffix\", \"\"], [\"formControlName\", \"user_Access\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"recipe_Access\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"party_Access\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"delete_GRN\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"edit_GRN\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"close_GRN\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"delete_PO\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"edit_PO\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"close_PO\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"delete_Indent\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"close_Indent\", \"multiple\", \"\", 3, \"ngModelChange\"], [\"formControlName\", \"report_Access\", \"multiple\", \"\"], [\"formControlName\", \"reportAccess\", \"multiple\", \"\"], [1, \"center-label\"], [1, \"col\", \"mb-3\"], [\"name\", \"pickDateOption\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"yes\"], [\"value\", \"no\"], [\"for\", \"monthlyDate\", 1, \"form-label\"], [\"name\", \"monthlyDate\", 3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"value\", \"startOfMonth\"], [1, \"btn\", \"btn-primary\", \"float-end\", \"mb-3\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"float-end\"], [1, \"tableDiv\"], [\"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"position\"], [\"class\", \"tableSnoCol\", 4, \"matHeaderCellDef\"], [\"class\", \"tableSnoCol\", 4, \"matCellDef\"], [\"matColumnDef\", \"createdDate\"], [\"class\", \"custom-header\", 4, \"matHeaderCellDef\"], [\"class\", \"custom-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"createdTime\"], [\"matColumnDef\", \"status\"], [4, \"matHeaderRowDef\"], [3, \"ngClass\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"tableSnoCol\"], [1, \"custom-header\"], [1, \"custom-cell\"], [4, \"ngIf\", \"ngIfElse\"], [\"noDate\", \"\"], [3, \"ngClass\"], [\"count\", \"5\", \"animation\", \"progress-dark\", 3, \"theme\"], [1, \"text-center\", \"m-3\"], [1, \"registration-form\", \"m-3\", \"py-2\", \"px-3\"], [1, \"row\", \"my-3\"], [1, \"form-check\", \"col-6\", \"justify-content-center\"], [\"type\", \"radio\", \"name\", \"option\", \"id\", \"option1\", \"value\", \"withoutIp\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"radio\", \"name\", \"option\", \"id\", \"option2\", \"value\", \"withIp\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"d-flex\", \"justify-content-end\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"update\", 3, \"click\"]],\n      template: function DashboardProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r205 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"mat-icon\", 7);\n          i0.ɵɵtext(8, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"h3\", 8);\n          i0.ɵɵtext(10, \"Profile Setting\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"mat-tab-group\", 10, 11);\n          i0.ɵɵlistener(\"selectedTabChange\", function DashboardProfileComponent_Template_mat_tab_group_selectedTabChange_12_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementStart(14, \"mat-tab\", 12)(15, \"mat-card\", 13)(16, \"mat-card-content\", 14);\n          i0.ɵɵelement(17, \"br\");\n          i0.ɵɵelementStart(18, \"mat-list\")(19, \"mat-list-item\")(20, \"div\", 15)(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\")(24, \"span\", 16);\n          i0.ɵɵtext(25, \"Name: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"mat-list-item\")(29, \"div\", 15)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"bookmark\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\")(33, \"span\", 16);\n          i0.ɵɵtext(34, \"Role: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"mat-list-item\")(38, \"div\", 15)(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"alternate_email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\")(42, \"span\", 16);\n          i0.ɵɵtext(43, \"Email: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(46, \"mat-tab\", 17)(47, \"mat-card\", 13)(48, \"mat-card-content\", 14);\n          i0.ɵɵelement(49, \"br\");\n          i0.ɵɵelementStart(50, \"div\", 18)(51, \"label\", 19);\n          i0.ɵɵtext(52, \"Do you want to enable IP restriction ?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-radio-group\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_Template_mat_radio_group_ngModelChange_53_listener($event) {\n            return ctx.IpType = $event;\n          })(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_53_listener() {\n            return ctx.radioChange();\n          });\n          i0.ɵɵelementStart(54, \"mat-radio-button\", 21);\n          i0.ɵɵtext(55, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"mat-radio-button\", 22);\n          i0.ɵɵtext(57, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(58, DashboardProfileComponent_form_58_Template, 23, 6, \"form\", 23);\n          i0.ɵɵtemplate(59, DashboardProfileComponent_div_59_Template, 3, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"mat-tab\", 25)(61, \"mat-card\", 13)(62, \"mat-card-content\", 14);\n          i0.ɵɵelement(63, \"br\");\n          i0.ɵɵelementStart(64, \"form\", 26);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_64_listener() {\n            return ctx.addConfig();\n          });\n          i0.ɵɵtemplate(65, DashboardProfileComponent_div_65_Template, 7, 2, \"div\", 27);\n          i0.ɵɵelementStart(66, \"div\", 28)(67, \"div\", 29)(68, \"mat-form-field\", 30)(69, \"mat-label\");\n          i0.ɵɵtext(70, \"Price Tier\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"mat-select\", 31);\n          i0.ɵɵtemplate(72, DashboardProfileComponent_mat_option_72_Template, 3, 4, \"mat-option\", 32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(73, \"button\", 33);\n          i0.ɵɵtext(74, \"Submit\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(75, \"mat-tab\", 34)(76, \"mat-card\", 13)(77, \"mat-card-content\", 14);\n          i0.ɵɵelement(78, \"br\");\n          i0.ɵɵelementStart(79, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_79_listener() {\n            return ctx.submitPermission();\n          });\n          i0.ɵɵelementStart(80, \"div\")(81, \"h4\");\n          i0.ɵɵtext(82, \" Setting \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"mat-form-field\", 30)(84, \"mat-label\");\n          i0.ɵɵtext(85, \"Roles for setting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"mat-select\", 36);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_86_listener($event) {\n            return ctx.selectedSettingRoles = $event;\n          })(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_86_listener() {\n            return ctx.validateSales();\n          });\n          i0.ɵɵtemplate(87, DashboardProfileComponent_mat_option_87_Template, 3, 5, \"mat-option\", 37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(88, \"div\")(89, \"h4\");\n          i0.ɵɵtext(90, \" Excel Upload \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"mat-form-field\", 30)(92, \"mat-label\");\n          i0.ɵɵtext(93, \"Roles for Excel Upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"mat-select\", 38);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_94_listener($event) {\n            return ctx.selectedExcelRoles = $event;\n          })(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_94_listener() {\n            return ctx.validateSales();\n          });\n          i0.ɵɵtemplate(95, DashboardProfileComponent_mat_option_95_Template, 3, 5, \"mat-option\", 37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"button\", 33);\n          i0.ɵɵtext(97, \"Submit\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"mat-tab\", 39)(99, \"mat-card\", 13)(100, \"mat-tab-group\")(101, \"mat-tab\", 40)(102, \"mat-card-content\", 14)(103, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_103_listener() {\n            return ctx.updateModule();\n          });\n          i0.ɵɵelementStart(104, \"h1\")(105, \"b\");\n          i0.ɵɵtext(106, \"Inventory Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 18)(108, \"label\", 19);\n          i0.ɵɵtext(109, \"Inventory Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"mat-radio-group\", 41);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_110_listener($event) {\n            return ctx.inventoryStatusShown($event);\n          });\n          i0.ɵɵelementStart(111, \"mat-radio-button\", 42);\n          i0.ɵɵtext(112, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"mat-radio-button\", 43);\n          i0.ɵɵtext(114, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(115, \"br\");\n          i0.ɵɵtemplate(116, DashboardProfileComponent_div_116_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelement(117, \"br\");\n          i0.ɵɵelementStart(118, \"h1\")(119, \"b\");\n          i0.ɵɵtext(120, \"User Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(121, \"div\", 18)(122, \"label\", 19);\n          i0.ɵɵtext(123, \"User Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"mat-radio-group\", 44);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_124_listener($event) {\n            return ctx.userStatusShown($event);\n          });\n          i0.ɵɵelementStart(125, \"mat-radio-button\", 42);\n          i0.ɵɵtext(126, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"mat-radio-button\", 43);\n          i0.ɵɵtext(128, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(129, \"br\");\n          i0.ɵɵtemplate(130, DashboardProfileComponent_div_130_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelement(131, \"br\");\n          i0.ɵɵelementStart(132, \"h1\")(133, \"b\");\n          i0.ɵɵtext(134, \"Recipe Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(135, \"div\", 18)(136, \"label\", 19);\n          i0.ɵɵtext(137, \"Recipe Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"mat-radio-group\", 45);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_138_listener($event) {\n            return ctx.recipeStatusShown($event);\n          });\n          i0.ɵɵelementStart(139, \"mat-radio-button\", 42);\n          i0.ɵɵtext(140, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"mat-radio-button\", 43);\n          i0.ɵɵtext(142, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(143, \"br\");\n          i0.ɵɵtemplate(144, DashboardProfileComponent_div_144_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelement(145, \"br\");\n          i0.ɵɵelementStart(146, \"h1\")(147, \"b\");\n          i0.ɵɵtext(148, \"Party Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(149, \"div\", 18)(150, \"label\", 19);\n          i0.ɵɵtext(151, \"Party Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"mat-radio-group\", 46);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_152_listener($event) {\n            return ctx.partyStatusShown($event);\n          });\n          i0.ɵɵelementStart(153, \"mat-radio-button\", 42);\n          i0.ɵɵtext(154, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"mat-radio-button\", 43);\n          i0.ɵɵtext(156, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(157, \"br\");\n          i0.ɵɵtemplate(158, DashboardProfileComponent_div_158_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(159, \"button\", 33);\n          i0.ɵɵtext(160, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(161, \"mat-tab\", 47)(162, \"mat-card-content\", 14)(163, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_163_listener() {\n            return ctx.updateGRN();\n          });\n          i0.ɵɵelementStart(164, \"div\", 18)(165, \"label\", 19);\n          i0.ɵɵtext(166, \"Delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(167, \"mat-radio-group\", 48);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_167_listener($event) {\n            return ctx.isDeleteGrnShown($event);\n          });\n          i0.ɵɵelementStart(168, \"mat-radio-button\", 42);\n          i0.ɵɵtext(169, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"mat-radio-button\", 43);\n          i0.ɵɵtext(171, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(172, \"br\");\n          i0.ɵɵtemplate(173, DashboardProfileComponent_div_173_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(174, \"div\", 18)(175, \"label\", 19);\n          i0.ɵɵtext(176, \"Edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(177, \"mat-radio-group\", 49);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_177_listener($event) {\n            return ctx.isEditGrnShown($event);\n          });\n          i0.ɵɵelementStart(178, \"mat-radio-button\", 42);\n          i0.ɵɵtext(179, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"mat-radio-button\", 43);\n          i0.ɵɵtext(181, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(182, \"br\");\n          i0.ɵɵtemplate(183, DashboardProfileComponent_div_183_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(184, \"div\", 18)(185, \"label\", 19);\n          i0.ɵɵtext(186, \"RTV\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(187, \"mat-radio-group\", 50);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_187_listener($event) {\n            return ctx.isCloseGrnShown($event);\n          });\n          i0.ɵɵelementStart(188, \"mat-radio-button\", 42);\n          i0.ɵɵtext(189, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"mat-radio-button\", 43);\n          i0.ɵɵtext(191, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(192, \"br\");\n          i0.ɵɵtemplate(193, DashboardProfileComponent_div_193_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(194, \"button\", 33);\n          i0.ɵɵtext(195, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(196, \"mat-tab\", 51)(197, \"mat-card-content\", 14)(198, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_198_listener() {\n            return ctx.updatePO();\n          });\n          i0.ɵɵelementStart(199, \"div\", 18)(200, \"label\", 19);\n          i0.ɵɵtext(201, \"Delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(202, \"mat-radio-group\", 52);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_202_listener($event) {\n            return ctx.isDeletePoShown($event);\n          });\n          i0.ɵɵelementStart(203, \"mat-radio-button\", 42);\n          i0.ɵɵtext(204, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(205, \"mat-radio-button\", 43);\n          i0.ɵɵtext(206, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(207, \"br\");\n          i0.ɵɵtemplate(208, DashboardProfileComponent_div_208_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(209, \"div\", 18)(210, \"label\", 19);\n          i0.ɵɵtext(211, \"Edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(212, \"mat-radio-group\", 53);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_212_listener($event) {\n            return ctx.isEditPoShown($event);\n          });\n          i0.ɵɵelementStart(213, \"mat-radio-button\", 42);\n          i0.ɵɵtext(214, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(215, \"mat-radio-button\", 43);\n          i0.ɵɵtext(216, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(217, \"br\");\n          i0.ɵɵtemplate(218, DashboardProfileComponent_div_218_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(219, \"div\", 18)(220, \"label\", 19);\n          i0.ɵɵtext(221, \"Close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(222, \"mat-radio-group\", 54);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_222_listener($event) {\n            return ctx.isClosePoShown($event);\n          });\n          i0.ɵɵelementStart(223, \"mat-radio-button\", 42);\n          i0.ɵɵtext(224, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(225, \"mat-radio-button\", 43);\n          i0.ɵɵtext(226, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(227, \"br\");\n          i0.ɵɵtemplate(228, DashboardProfileComponent_div_228_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(229, \"button\", 33);\n          i0.ɵɵtext(230, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(231, \"mat-tab\", 55)(232, \"mat-card-content\", 14)(233, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_233_listener() {\n            return ctx.updateIndent();\n          });\n          i0.ɵɵelementStart(234, \"div\", 18)(235, \"label\", 19);\n          i0.ɵɵtext(236, \"Delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(237, \"mat-radio-group\", 56);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_237_listener($event) {\n            return ctx.isDeleteIndentShown($event);\n          });\n          i0.ɵɵelementStart(238, \"mat-radio-button\", 42);\n          i0.ɵɵtext(239, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(240, \"mat-radio-button\", 43);\n          i0.ɵɵtext(241, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(242, \"br\");\n          i0.ɵɵtemplate(243, DashboardProfileComponent_div_243_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(244, \"div\", 18)(245, \"label\", 19);\n          i0.ɵɵtext(246, \"Close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(247, \"mat-radio-group\", 57);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_247_listener($event) {\n            return ctx.isCloseIndentShown($event);\n          });\n          i0.ɵɵelementStart(248, \"mat-radio-button\", 42);\n          i0.ɵɵtext(249, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(250, \"mat-radio-button\", 43);\n          i0.ɵɵtext(251, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(252, \"br\");\n          i0.ɵɵtemplate(253, DashboardProfileComponent_div_253_Template, 13, 4, \"div\", 24);\n          i0.ɵɵelementStart(254, \"div\", 18)(255, \"label\", 19);\n          i0.ɵɵtext(256, \"Partial Indent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(257, \"mat-radio-group\", 58);\n          i0.ɵɵlistener(\"change\", function DashboardProfileComponent_Template_mat_radio_group_change_257_listener($event) {\n            return ctx.isPartialIndentShown($event);\n          });\n          i0.ɵɵelementStart(258, \"mat-radio-button\", 42);\n          i0.ɵɵtext(259, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(260, \"mat-radio-button\", 43);\n          i0.ɵɵtext(261, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(262, \"button\", 33);\n          i0.ɵɵtext(263, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(264, \"mat-tab\", 59)(265, \"mat-card-content\", 14)(266, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_266_listener() {\n            return ctx.updateEmail();\n          });\n          i0.ɵɵelementStart(267, \"div\", 18)(268, \"label\", 60);\n          i0.ɵɵtext(269, \"Purchase Request\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(270, \"mat-radio-group\", 61)(271, \"mat-radio-button\", 42);\n          i0.ɵɵtext(272, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(273, \"mat-radio-button\", 43);\n          i0.ɵɵtext(274, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(275, \"br\");\n          i0.ɵɵelementStart(276, \"div\", 18)(277, \"label\", 60);\n          i0.ɵɵtext(278, \"Post Grn Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(279, \"mat-radio-group\", 62)(280, \"mat-radio-button\", 42);\n          i0.ɵɵtext(281, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(282, \"mat-radio-button\", 43);\n          i0.ɵɵtext(283, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(284, \"br\");\n          i0.ɵɵelementStart(285, \"div\", 18)(286, \"label\", 60);\n          i0.ɵɵtext(287, \"Indent Approval\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(288, \"mat-radio-group\", 63)(289, \"mat-radio-button\", 42);\n          i0.ɵɵtext(290, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(291, \"mat-radio-button\", 43);\n          i0.ɵɵtext(292, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(293, \"br\");\n          i0.ɵɵelementStart(294, \"div\", 18)(295, \"label\", 60);\n          i0.ɵɵtext(296, \"Purchase Approval\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(297, \"mat-radio-group\", 64)(298, \"mat-radio-button\", 42);\n          i0.ɵɵtext(299, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(300, \"mat-radio-button\", 43);\n          i0.ɵɵtext(301, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(302, \"br\");\n          i0.ɵɵelementStart(303, \"div\", 18)(304, \"label\", 60);\n          i0.ɵɵtext(305, \"Purchase Order\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(306, \"mat-radio-group\", 65)(307, \"mat-radio-button\", 42);\n          i0.ɵɵtext(308, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(309, \"mat-radio-button\", 43);\n          i0.ɵɵtext(310, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(311, \"br\");\n          i0.ɵɵelementStart(312, \"div\", 18)(313, \"label\", 60);\n          i0.ɵɵtext(314, \"PI Approval\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(315, \"mat-radio-group\", 66)(316, \"mat-radio-button\", 42);\n          i0.ɵɵtext(317, \"Yes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(318, \"mat-radio-button\", 43);\n          i0.ɵɵtext(319, \"No\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(320, \"button\", 33);\n          i0.ɵɵtext(321, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(322, \"mat-tab\", 67)(323, \"mat-tab-group\", 68)(324, \"mat-tab\", 69)(325, \"mat-card-content\", 14)(326, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_326_listener() {\n            return ctx.updateReport();\n          });\n          i0.ɵɵelementStart(327, \"mat-form-field\", 30)(328, \"mat-label\");\n          i0.ɵɵtext(329, \"Select Report\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(330, \"mat-select\", 70);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_330_listener($event) {\n            return ctx.reportChange($event);\n          });\n          i0.ɵɵelementStart(331, \"mat-option\");\n          i0.ɵɵelement(332, \"ngx-mat-select-search\", 71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(333, DashboardProfileComponent_mat_option_333_Template, 3, 4, \"mat-option\", 32);\n          i0.ɵɵpipe(334, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(335, \"mat-accordion\", 72);\n          i0.ɵɵtemplate(336, DashboardProfileComponent_mat_expansion_panel_336_Template, 17, 7, \"mat-expansion-panel\", 73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(337, \"br\");\n          i0.ɵɵelementStart(338, \"button\", 33);\n          i0.ɵɵtext(339, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(340, \"mat-tab\", 74)(341, \"mat-card-content\", 14)(342, \"form\", 35);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_342_listener() {\n            return ctx.updateReport(false);\n          });\n          i0.ɵɵelementStart(343, \"mat-form-field\", 30)(344, \"mat-label\");\n          i0.ɵɵtext(345, \"Select Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(346, \"mat-select\", 75);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardProfileComponent_Template_mat_select_ngModelChange_346_listener($event) {\n            return ctx.roleChange($event);\n          });\n          i0.ɵɵelementStart(347, \"mat-option\");\n          i0.ɵɵelement(348, \"ngx-mat-select-search\", 71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(349, DashboardProfileComponent_mat_option_349_Template, 3, 4, \"mat-option\", 32);\n          i0.ɵɵpipe(350, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(351, DashboardProfileComponent_mat_accordion_351_Template, 18, 7, \"mat-accordion\", 76);\n          i0.ɵɵelement(352, \"br\");\n          i0.ɵɵelementStart(353, \"button\", 33);\n          i0.ɵɵtext(354, \"Submit\");\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵelementStart(355, \"mat-tab\", 77)(356, \"mat-card\", 13)(357, \"mat-card-content\", 14)(358, \"form\", 26);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_358_listener() {\n            return ctx.submit();\n          });\n          i0.ɵɵelementStart(359, \"div\")(360, \"div\", 78)(361, \"div\", 29)(362, \"mat-form-field\", 79)(363, \"mat-label\");\n          i0.ɵɵtext(364, \"Select a Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(365, \"mat-select\", 80);\n          i0.ɵɵlistener(\"selectionChange\", function DashboardProfileComponent_Template_mat_select_selectionChange_365_listener($event) {\n            return ctx.onLocationChange($event);\n          });\n          i0.ɵɵtemplate(366, DashboardProfileComponent_mat_label_366_Template, 2, 0, \"mat-label\", 81);\n          i0.ɵɵelementStart(367, \"input\", 82, 83);\n          i0.ɵɵlistener(\"keydown.space\", function DashboardProfileComponent_Template_input_keydown_space_367_listener($event) {\n            return $event.stopPropagation();\n          })(\"input\", function DashboardProfileComponent_Template_input_input_367_listener() {\n            i0.ɵɵrestoreView(_r205);\n            const _r24 = i0.ɵɵreference(368);\n            return i0.ɵɵresetView(ctx.togglePlaceholderLabel(_r24.value));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(369, DashboardProfileComponent_mat_option_369_Template, 3, 4, \"mat-option\", 32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(370, DashboardProfileComponent_div_370_Template, 9, 1, \"div\", 24);\n          i0.ɵɵtemplate(371, DashboardProfileComponent_div_371_Template, 11, 2, \"div\", 84);\n          i0.ɵɵtemplate(372, DashboardProfileComponent_div_372_Template, 3, 0, \"div\", 85);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(373, \"mat-tab\", 86)(374, \"mat-card\", 13)(375, \"mat-tab-group\", 87);\n          i0.ɵɵlistener(\"selectedTabChange\", function DashboardProfileComponent_Template_mat_tab_group_selectedTabChange_375_listener($event) {\n            return ctx.tabChange($event);\n          });\n          i0.ɵɵelementStart(376, \"mat-tab\", 25)(377, \"mat-card-content\", 88)(378, \"button\", 89);\n          i0.ɵɵlistener(\"click\", function DashboardProfileComponent_Template_button_click_378_listener() {\n            return ctx.refreshSales();\n          });\n          i0.ɵɵtext(379, \"Refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(380, \"form\", 90);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_380_listener() {\n            return ctx.salesRerun();\n          });\n          i0.ɵɵelementStart(381, \"div\")(382, \"mat-form-field\", 91)(383, \"mat-label\");\n          i0.ɵɵtext(384, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(385, \"input\", 92)(386, \"mat-datepicker-toggle\", 93)(387, \"mat-datepicker\", 94, 95);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(389, \"mat-form-field\", 91)(390, \"mat-label\");\n          i0.ɵɵtext(391, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(392, \"input\", 96)(393, \"mat-datepicker-toggle\", 93)(394, \"mat-datepicker\", 94, 97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(396, \"button\", 98);\n          i0.ɵɵtext(397, \"Submit\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(398, \"div\", 99, 100);\n          i0.ɵɵtemplate(401, DashboardProfileComponent_div_401_Template, 16, 3, \"div\", 101);\n          i0.ɵɵtemplate(402, DashboardProfileComponent_div_402_Template, 2, 2, \"div\", 24);\n          i0.ɵɵtemplate(403, DashboardProfileComponent_div_403_Template, 2, 0, \"div\", 102);\n          i0.ɵɵelement(404, \"mat-paginator\", 103, 104);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(406, \"mat-tab\", 105)(407, \"mat-card-content\", 88)(408, \"button\", 106);\n          i0.ɵɵlistener(\"click\", function DashboardProfileComponent_Template_button_click_408_listener() {\n            return ctx.refreshWac();\n          });\n          i0.ɵɵtext(409, \"Refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(410, \"form\", 90);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_410_listener() {\n            return ctx.weightedAvg();\n          });\n          i0.ɵɵelementStart(411, \"button\", 107);\n          i0.ɵɵtext(412, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(413, \"div\", 99, 100);\n          i0.ɵɵtemplate(416, DashboardProfileComponent_div_416_Template, 13, 3, \"div\", 101);\n          i0.ɵɵtemplate(417, DashboardProfileComponent_div_417_Template, 2, 2, \"div\", 24);\n          i0.ɵɵtemplate(418, DashboardProfileComponent_div_418_Template, 2, 0, \"div\", 102);\n          i0.ɵɵelement(419, \"mat-paginator\", 103, 108);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(421, \"mat-tab\", 109)(422, \"mat-card-content\", 88)(423, \"button\", 106);\n          i0.ɵɵlistener(\"click\", function DashboardProfileComponent_Template_button_click_423_listener() {\n            return ctx.refreshForecast();\n          });\n          i0.ɵɵtext(424, \"Refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(425, \"form\", 90);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardProfileComponent_Template_form_ngSubmit_425_listener() {\n            return ctx.forecast();\n          });\n          i0.ɵɵelementStart(426, \"button\", 107);\n          i0.ɵɵtext(427, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(428, \"div\", 99, 100);\n          i0.ɵɵtemplate(431, DashboardProfileComponent_div_431_Template, 13, 3, \"div\", 101);\n          i0.ɵɵtemplate(432, DashboardProfileComponent_div_432_Template, 2, 2, \"div\", 24);\n          i0.ɵɵtemplate(433, DashboardProfileComponent_div_433_Template, 2, 0, \"div\", 102);\n          i0.ɵɵelement(434, \"mat-paginator\", 103, 110);\n          i0.ɵɵelementEnd()()()()()()()()()()()()()();\n          i0.ɵɵtemplate(436, DashboardProfileComponent_ng_template_436_Template, 14, 2, \"ng-template\", null, 111, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const _r29 = i0.ɵɵreference(388);\n          const _r30 = i0.ɵɵreference(395);\n          i0.ɵɵadvance(27);\n          i0.ɵɵtextInterpolate(ctx.user.name);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.user.role);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.user.email);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.IpType);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.showIpProcess);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showIpProcess);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.salesLocation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.priceData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.priceTierList);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.accessForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedSettingRoles);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedExcelRoles);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"formGroup\", ctx.moduleForm);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.showInventoryStatusField);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.showUserStatusField);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.showRecipeStatusField);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPartyStatusField);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.grnForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteGRNField);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEditGRNField);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCloseGRNField);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.poForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeletePOField);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEditPOField);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showClosePOField);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.indentForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteIndentField);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCloseIndentField);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formGroup\", ctx.emailForm);\n          i0.ɵɵadvance(60);\n          i0.ɵɵproperty(\"formGroup\", ctx.reportForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.reportFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(334, 67, ctx.report));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredReports);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.roleForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.report_FilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(350, 69, ctx.reports));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.roleForm.get(\"report_Access\").value);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"formControl\", ctx.branchData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPlaceholderLabel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formControl\", ctx.vendorFilterCtrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocation);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.pickDateOption === \"yes\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFinalStep());\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"formGroup\", ctx.salesForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matDatepicker\", _r29);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r29);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", _r30)(\"disabled\", !ctx.salesForm.get(\"startDate\").value)(\"min\", ctx.salesForm.get(\"startDate\").value);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r30);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSalesDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSalesDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSourceSales.data.length == 0 && ctx.isSalesDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(71, _c7));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.wacForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWacDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isWacDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSourceWeightedAvg.data.length == 0 && ctx.isWacDataReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(72, _c7));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.forecastForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isForecastReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isForecastReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSourceForecast.data.length == 0 && ctx.isForecastReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(73, _c7));\n        }\n      },\n      dependencies: [CommonModule, i10.NgClass, i10.NgForOf, i10.NgIf, i10.AsyncPipe, i10.UpperCasePipe, i10.TitleCasePipe, i10.DatePipe, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, MatSliderModule, MatTabsModule, i12.MatTab, i12.MatTabGroup, MatButtonModule, i13.MatButton, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.RadioControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.NgForm, MatIconModule, i14.MatIcon, MatDividerModule, ReactiveFormsModule, i2.FormControlDirective, i2.FormGroupDirective, i2.FormControlName, MatFormFieldModule, i15.MatFormField, i15.MatLabel, i15.MatSuffix, MatListModule, i16.MatList, i16.MatListItem, MatRadioModule, i17.MatRadioGroup, i17.MatRadioButton, MatInputModule, i18.MatInput, MatCardModule, i19.MatCard, i19.MatCardContent, i19.MatCardTitle, MatDialogModule, MatSelectModule, i20.MatSelect, i21.MatOption, NgxMatSelectSearchModule, i22.MatSelectSearchComponent, MatExpansionModule, i23.MatAccordion, i23.MatExpansionPanel, i23.MatExpansionPanelHeader, i23.MatExpansionPanelTitle, MatTableModule, i24.MatTable, i24.MatHeaderCellDef, i24.MatHeaderRowDef, i24.MatColumnDef, i24.MatCellDef, i24.MatRowDef, i24.MatHeaderCell, i24.MatCell, i24.MatHeaderRow, i24.MatRow, MatPaginatorModule, i25.MatPaginator, NgxSkeletonLoaderModule, i26.NgxSkeletonLoaderComponent],\n      styles: [\".list-item-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 10px;\\n  align-items: center;\\n}\\n\\n.bold[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: red;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.form-check-input[type=radio][_ngcontent-%COMP%] {\\n  width: 22px;\\n  height: 22px;\\n}\\n\\n.form-check-input[type=radio][_ngcontent-%COMP%] {\\n  border: 2px solid #000;\\n  margin-right: 8px;\\n  margin-bottom: 5px;\\n}\\n\\n.ipText[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: medium;\\n  font-weight: bold;\\n}\\n\\n.emailAccessIpText[_ngcontent-%COMP%] {\\n  width: 200px;\\n  font-size: medium;\\n  font-weight: bold;\\n}\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\nmat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.custom-select[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%] {\\n  position: relative !important;\\n}\\n\\n.custom-dropdown[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%] {\\n  top: unset !important;\\n  bottom: 0 !important;\\n}\\n\\n.center-label[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  margin-top: 0px; \\n\\n  margin-bottom: 10px; \\n\\n}\\n\\n.mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 100%;\\n  align-items: center; \\n\\n  justify-content: center; \\n\\n}\\n\\n.placeholder-label[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.6); \\n\\n  pointer-events: none; \\n\\n}\\n\\n.cardContent[_ngcontent-%COMP%] {\\n  display: block;\\n  padding: 0px;\\n}\\n\\n.tabGroup[_ngcontent-%COMP%] {\\n  padding: 5px;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow-y: auto;\\n}\\n\\n.datePicker[_ngcontent-%COMP%] {\\n  width: 300px;\\n  padding: 30px 10px 5px 0px;\\n}\\n\\n.refreshButton[_ngcontent-%COMP%] {\\n  padding-top: 20px;\\n  padding-bottom: 10px;\\n  float: right;\\n}\\n\\n.refresh[_ngcontent-%COMP%] {\\n  height: 3rem !important;\\n}\\n\\n.gap[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  margin-bottom: 10px;\\n  margin-top: 20px;\\n}\\n\\n.end[_ngcontent-%COMP%] {\\n  float: right;\\n  margin-top: 38px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZGFzaGJvYXJkLXByb2ZpbGUvZGFzaGJvYXJkLXByb2ZpbGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxnQkFBQTtFQUNBLFVBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQUNGOztBQUVBO0VBQ0Usc0JBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsaUJBQUE7QUFDRjs7QUFFQTtFQUNFLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0FBQ0Y7O0FBQ0E7RUFDRSw2QkFBQTtBQUVGOztBQUFBO0VBQ0UscUJBQUE7RUFDQSxvQkFBQTtBQUdGOztBQURBO0VBQ0UsaUJBQUE7RUFDQSxlQUFBLEVBQUEscUJBQUE7RUFDQSxtQkFBQSxFQUFBLG9DQUFBO0FBSUY7O0FBREE7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBLEVBQUEsa0NBQUE7RUFDQSx1QkFBQSxFQUFBLG9DQUFBO0FBSUY7O0FBRkE7RUFDRSx5QkFBQSxFQUFBLHVDQUFBO0VBQ0Esb0JBQUEsRUFBQSw4Q0FBQTtBQUtGOztBQUZBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUFLRjs7QUFGQTtFQUNFLFlBQUE7QUFLRjs7QUFGQTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtBQUtGOztBQUZBO0VBQ0UsWUFBQTtFQUNBLDBCQUFBO0FBS0Y7O0FBRkE7RUFDRSxpQkFBQTtFQUNBLG9CQUFBO0VBQ0EsWUFBQTtBQUtGOztBQUZBO0VBQ0UsdUJBQUE7QUFLRjs7QUFGQTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQUtGOztBQUZBO0VBQ0UsWUFBQTtFQUNBLDJCQUFBO0FBS0YiLCJzb3VyY2VzQ29udGVudCI6WyIubGlzdC1pdGVtLWNvbnRlbnQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICBnYXA6IDEwcHg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbi5ib2xkIHtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG59XG5cbi5jdXJzb3ItcG9pbnRlciB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cblxuLmVycm9yLW1lc3NhZ2Uge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xuICBjb2xvcjogcmVkO1xufVxuXG4uZm9ybS1jaGVjayB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7XG59XG5cbi5mb3JtLWNoZWNrLWlucHV0W3R5cGU9XCJyYWRpb1wiXSB7XG4gIHdpZHRoOiAyMnB4OyBcbiAgaGVpZ2h0OiAyMnB4OyBcbn1cblxuLmZvcm0tY2hlY2staW5wdXRbdHlwZT1cInJhZGlvXCJdIHtcbiAgYm9yZGVyOiAycHggc29saWQgIzAwMDsgXG4gIG1hcmdpbi1yaWdodDogOHB4OyBcbiAgbWFyZ2luLWJvdHRvbTogNXB4O1xufVxuXG4uaXBUZXh0e1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogbWVkaXVtO1xuICBmb250LXdlaWdodDogYm9sZDtcbn1cblxuLmVtYWlsQWNjZXNzSXBUZXh0e1xuICB3aWR0aDogMjAwcHg7XG4gIGZvbnQtc2l6ZTogbWVkaXVtO1xuICBmb250LXdlaWdodDogYm9sZDtcbn1cblxuLmV4YW1wbGUtc3BhY2VyIHtcbiAgZmxleDogMSAxIGF1dG87XG59XG5cbm1hdC1mb3JtLWZpZWxkIHtcbiAgd2lkdGg6IDEwMCU7XG59XG4uY3VzdG9tLXNlbGVjdCAubWF0LXNlbGVjdC10cmlnZ2VyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XG59XG4uY3VzdG9tLWRyb3Bkb3duIC5tYXQtc2VsZWN0LXRyaWdnZXIge1xuICB0b3A6IHVuc2V0ICFpbXBvcnRhbnQ7XG4gIGJvdHRvbTogMCAhaW1wb3J0YW50O1xufVxuLmNlbnRlci1sYWJlbCB7XG4gIG1hcmdpbi1sZWZ0OiAxMHB4OyBcbiAgbWFyZ2luLXRvcDogMHB4OyAvKiBBZGp1c3QgYXMgbmVlZGVkICovXG4gIG1hcmdpbi1ib3R0b206IDEwcHg7IC8qIEhvcml6b250YWxseSBjZW50ZXIgdGhlIGNvbnRlbnQgKi9cbn1cblxuLm1hdC1mb3JtLWZpZWxkLXdyYXBwZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBtYXgtd2lkdGg6IDEwMCU7IFxuICBhbGlnbi1pdGVtczogY2VudGVyOyAvKiBWZXJ0aWNhbGx5IGNlbnRlciB0aGUgY29udGVudCAqL1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgLyogSG9yaXpvbnRhbGx5IGNlbnRlciB0aGUgY29udGVudCAqL1xufVxuLnBsYWNlaG9sZGVyLWxhYmVsIHtcbiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42KTsgLyogQWRqdXN0IHBsYWNlaG9sZGVyIGNvbG9yIGlmIG5lZWRlZCAqL1xuICBwb2ludGVyLWV2ZW50czogbm9uZTsgLyogRW5zdXJlIHRoZSBsYWJlbCBkb2Vzbid0IGludGVyY2VwdCBjbGlja3MgKi9cbn1cblxuLmNhcmRDb250ZW50e1xuICBkaXNwbGF5OiBibG9jaztcbiAgcGFkZGluZzogMHB4O1xufVxuXG4udGFiR3JvdXB7XG4gIHBhZGRpbmc6IDVweDtcbn1cblxuLnNlY3Rpb24ge1xuICB3aWR0aDogMTAwJTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbn1cblxuLmRhdGVQaWNrZXIge1xuICB3aWR0aDogMzAwcHg7XG4gIHBhZGRpbmc6IDMwcHggMTBweCA1cHggMHB4O1xufVxuXG4ucmVmcmVzaEJ1dHRvbntcbiAgcGFkZGluZy10b3A6IDIwcHg7XG4gIHBhZGRpbmctYm90dG9tOiAxMHB4O1xuICBmbG9hdDogcmlnaHQ7XG59XG5cbi5yZWZyZXNoe1xuICBoZWlnaHQ6IDNyZW0gIWltcG9ydGFudDtcbn1cblxuLmdhcHtcbiAgbWFyZ2luLWxlZnQ6IDEwcHg7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gIG1hcmdpbi10b3A6IDIwcHg7XG59XG5cbi5lbmR7XG4gIGZsb2F0OiByaWdodDtcbiAgbWFyZ2luLXRvcDogMzhweCAhaW1wb3J0YW50O1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardProfileComponent };", "map": {"version": 3, "names": ["inject", "CommonModule", "HttpHeaders", "environment", "MatButtonModule", "MatIconModule", "MatDividerModule", "MatListModule", "MatCardModule", "BreakpointObserver", "Breakpoints", "ReplaySubject", "Subject", "map", "takeUntil", "debounceTime", "distinctUntilChanged", "MatTabsModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatFormFieldModule", "MatNativeDateModule", "MatDatepickerModule", "MatSliderModule", "MatInputModule", "MatDialogModule", "MatRadioModule", "MatSelectModule", "GlobalsService", "NgxMatSelectSearchModule", "MatExpansionModule", "MatTableDataSource", "MatTableModule", "MatPaginatorModule", "NgxSkeletonLoaderModule", "DateAdapter", "MAT_DATE_FORMATS", "MAT_DATE_LOCALE", "MAT_MOMENT_DATE_ADAPTER_OPTIONS", "MomentDateAdapter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardProfileComponent_form_58_div_20_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r56", "ctx_r55", "ɵɵnextContext", "ɵɵresetView", "ipOrSubnet", "ctx_r57", "validateInput", "ɵɵtemplate", "DashboardProfileComponent_form_58_div_20_div_4_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r51", "isIPValid", "DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_4_listener", "_r61", "ctx_r60", "startIP", "ctx_r62", "DashboardProfileComponent_form_58_div_21_div_5_Template", "DashboardProfileComponent_form_58_div_21_Template_input_ngModelChange_9_listener", "ctx_r63", "endIP", "ctx_r64", "DashboardProfileComponent_form_58_div_21_div_10_Template", "ctx_r52", "ipPattern", "test", "ctx_r53", "DashboardProfileComponent_form_58_Template_form_ngSubmit_0_listener", "_r66", "ctx_r65", "onSubmit", "DashboardProfileComponent_form_58_Template_input_ngModelChange_12_listener", "ctx_r67", "selectedOption", "DashboardProfileComponent_form_58_Template_input_ngModelChange_17_listener", "ctx_r68", "DashboardProfileComponent_form_58_div_20_Template", "DashboardProfileComponent_form_58_div_21_Template", "DashboardProfileComponent_form_58_button_22_Template", "ɵɵtextInterpolate1", "ctx_r1", "currentIp", "DashboardProfileComponent_div_59_Template_button_click_1_listener", "_r70", "ctx_r69", "option_r72", "restaurantIdOld", "ɵɵpipeBind1", "branchName", "DashboardProfileComponent_div_65_Template_mat_select_selectionChange_5_listener", "_r74", "ctx_r73", "getPriceTires", "DashboardProfileComponent_div_65_mat_option_6_Template", "ctx_r3", "branchData", "filteredBranches", "option_r75", "option_r76", "option_r77", "option_r79", "DashboardProfileComponent_div_116_Template_mat_select_ngModelChange_4_listener", "_r81", "ctx_r80", "validateSales", "ɵɵelement", "DashboardProfileComponent_div_116_Template_mat_option_click_7_listener", "ctx_r82", "toggleSelectAllInventory", "DashboardProfileComponent_div_116_mat_option_11_Template", "ctx_r7", "inventoryFilterCtrl", "inventory", "option_r84", "DashboardProfileComponent_div_130_Template_mat_select_ngModelChange_4_listener", "_r86", "ctx_r85", "DashboardProfileComponent_div_130_Template_mat_option_click_7_listener", "ctx_r87", "toggleSelectAllUser", "DashboardProfileComponent_div_130_mat_option_11_Template", "ctx_r8", "userFilterCtrl", "users", "option_r89", "DashboardProfileComponent_div_144_Template_mat_select_ngModelChange_4_listener", "_r91", "ctx_r90", "DashboardProfileComponent_div_144_Template_mat_option_click_7_listener", "ctx_r92", "toggleSelectAllRecipe", "DashboardProfileComponent_div_144_mat_option_11_Template", "ctx_r9", "recipeFilterCtrl", "recipe", "option_r94", "DashboardProfileComponent_div_158_Template_mat_select_ngModelChange_4_listener", "_r96", "ctx_r95", "DashboardProfileComponent_div_158_Template_mat_option_click_7_listener", "ctx_r97", "toggleSelectAllParty", "DashboardProfileComponent_div_158_mat_option_11_Template", "ctx_r10", "partyFilterCtrl", "party", "option_r99", "DashboardProfileComponent_div_173_Template_mat_select_ngModelChange_4_listener", "_r101", "ctx_r100", "DashboardProfileComponent_div_173_Template_mat_option_click_7_listener", "ctx_r102", "toggleSelectAllDeleteGRN", "DashboardProfileComponent_div_173_mat_option_11_Template", "ctx_r11", "deleteGRNFilterCtrl", "deleteGRN", "option_r104", "DashboardProfileComponent_div_183_Template_mat_select_ngModelChange_4_listener", "_r106", "ctx_r105", "DashboardProfileComponent_div_183_Template_mat_option_click_7_listener", "ctx_r107", "toggleSelectAllEditGRN", "DashboardProfileComponent_div_183_mat_option_11_Template", "ctx_r12", "editGRNFilterCtrl", "editGRN", "option_r109", "DashboardProfileComponent_div_193_Template_mat_select_ngModelChange_4_listener", "_r111", "ctx_r110", "DashboardProfileComponent_div_193_Template_mat_option_click_7_listener", "ctx_r112", "toggleSelectAllCloseGRN", "DashboardProfileComponent_div_193_mat_option_11_Template", "ctx_r13", "closeGRNFilterCtrl", "closeGRN", "option_r114", "DashboardProfileComponent_div_208_Template_mat_select_ngModelChange_4_listener", "_r116", "ctx_r115", "DashboardProfileComponent_div_208_Template_mat_option_click_7_listener", "ctx_r117", "toggleSelectAllDeletePO", "DashboardProfileComponent_div_208_mat_option_11_Template", "ctx_r14", "deletePOFilterCtrl", "deletePO", "option_r119", "DashboardProfileComponent_div_218_Template_mat_select_ngModelChange_4_listener", "_r121", "ctx_r120", "DashboardProfileComponent_div_218_Template_mat_option_click_7_listener", "ctx_r122", "toggleSelectAllEditPO", "DashboardProfileComponent_div_218_mat_option_11_Template", "ctx_r15", "editPOFilterCtrl", "editPO", "option_r124", "DashboardProfileComponent_div_228_Template_mat_select_ngModelChange_4_listener", "_r126", "ctx_r125", "DashboardProfileComponent_div_228_Template_mat_option_click_7_listener", "ctx_r127", "toggleSelectAllClosePO", "DashboardProfileComponent_div_228_mat_option_11_Template", "ctx_r16", "closePOFilterCtrl", "closePO", "option_r129", "DashboardProfileComponent_div_243_Template_mat_select_ngModelChange_4_listener", "_r131", "ctx_r130", "DashboardProfileComponent_div_243_Template_mat_option_click_7_listener", "ctx_r132", "toggleSelectAllDeleteIndent", "DashboardProfileComponent_div_243_mat_option_11_Template", "ctx_r17", "deleteIndentFilterCtrl", "deleteIndent", "option_r134", "DashboardProfileComponent_div_253_Template_mat_select_ngModelChange_4_listener", "_r136", "ctx_r135", "DashboardProfileComponent_div_253_Template_mat_option_click_7_listener", "ctx_r137", "toggleSelectAllCloseIndent", "DashboardProfileComponent_div_253_mat_option_11_Template", "ctx_r18", "closeIndentFilterCtrl", "closeIndent", "option_r138", "option_r141", "DashboardProfileComponent_mat_expansion_panel_336_Template_mat_option_click_11_listener", "_r143", "ctx_r142", "selectAllRolesForReport", "DashboardProfileComponent_mat_expansion_panel_336_mat_option_15_Template", "report_r139", "displayName", "ctx_r20", "report_FilterCtrl", "reports", "option_r144", "option_r146", "DashboardProfileComponent_mat_accordion_351_Template_mat_option_click_12_listener", "_r148", "ctx_r147", "selectAllReportsForRoles", "DashboardProfileComponent_mat_accordion_351_mat_option_16_Template", "ctx_r22", "filteredRole", "reportFilterCtrl", "report", "option_r149", "DashboardProfileComponent_div_370_Template_mat_radio_group_ngModelChange_4_listener", "_r151", "ctx_r150", "pickDateOption", "ctx_r26", "DashboardProfileComponent_div_371_Template_mat_select_ngModelChange_6_listener", "_r153", "ctx_r152", "selectedDateOption", "DashboardProfileComponent_div_371_Template_mat_select_selectionChange_6_listener", "ctx_r154", "onDateSelectionChange", "value", "ctx_r27", "i_r166", "ɵɵpipeBind2", "element_r167", "createTs", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵtextInterpolate2", "element_r168", "triggeredTs", "modTs", "ctx_r169", "convertToIST", "DashboardProfileComponent_div_401_mat_cell_10_ng_container_1_Template", "DashboardProfileComponent_div_401_mat_cell_10_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r170", "ctx_r162", "getJobStatus", "element_r173", "ɵɵpureFunction1", "_c5", "row_r174", "Discontinued", "DashboardProfileComponent_div_401_mat_header_cell_3_Template", "DashboardProfileComponent_div_401_mat_cell_4_Template", "DashboardProfileComponent_div_401_mat_header_cell_6_Template", "DashboardProfileComponent_div_401_mat_cell_7_Template", "DashboardProfileComponent_div_401_mat_header_cell_9_Template", "DashboardProfileComponent_div_401_mat_cell_10_Template", "DashboardProfileComponent_div_401_mat_header_cell_12_Template", "DashboardProfileComponent_div_401_mat_cell_13_Template", "DashboardProfileComponent_div_401_mat_header_row_14_Template", "DashboardProfileComponent_div_401_mat_row_15_Template", "ctx_r33", "dataSourceSales", "salesColumns", "ɵɵpureFunction0", "_c6", "i_r184", "element_r185", "ctx_r178", "ctx_r180", "element_r186", "row_r187", "DashboardProfileComponent_div_416_mat_header_cell_3_Template", "DashboardProfileComponent_div_416_mat_cell_4_Template", "DashboardProfileComponent_div_416_mat_header_cell_6_Template", "DashboardProfileComponent_div_416_mat_cell_7_Template", "DashboardProfileComponent_div_416_mat_header_cell_9_Template", "DashboardProfileComponent_div_416_mat_cell_10_Template", "DashboardProfileComponent_div_416_mat_header_row_11_Template", "DashboardProfileComponent_div_416_mat_row_12_Template", "ctx_r39", "dataSourceWeightedAvg", "weightedAvgColumns", "i_r197", "element_r198", "ctx_r191", "ctx_r193", "element_r199", "row_r200", "DashboardProfileComponent_div_431_mat_header_cell_3_Template", "DashboardProfileComponent_div_431_mat_cell_4_Template", "DashboardProfileComponent_div_431_mat_header_cell_6_Template", "DashboardProfileComponent_div_431_mat_cell_7_Template", "DashboardProfileComponent_div_431_mat_header_cell_9_Template", "DashboardProfileComponent_div_431_mat_cell_10_Template", "DashboardProfileComponent_div_431_mat_header_row_11_Template", "DashboardProfileComponent_div_431_mat_row_12_Template", "ctx_r45", "dataSourceForecast", "forecastColumns", "DashboardProfileComponent_ng_template_436_Template_input_ngModelChange_4_listener", "_r202", "ctx_r201", "IpType", "DashboardProfileComponent_ng_template_436_Template_input_ngModelChange_8_listener", "ctx_r203", "DashboardProfileComponent_ng_template_436_Template_button_click_12_listener", "ctx_r204", "submitIp", "ctx_r50", "MY_DATE_FORMATS", "parse", "dateInput", "display", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "DashboardProfileComponent", "constructor", "shareDataService", "formBuilder", "ngZone", "http", "api", "notify", "cd", "sharedData", "auth", "dialog", "masterDataService", "router", "fb", "monthlyClosingDatesArray", "engineUrl", "startingDates", "showLocationSelection", "selectedLocation", "monthlyClosingDates", "selectedMonthDates", "selected<PERSON>ay", "selectedDays", "closingType", "selectedDate", "monthlyDate", "fifteenDaysDate", "isReadOnly", "salesLocation", "breakpointObserver", "RegExp", "profitTarget", "priceData", "showIpProcess", "showIpBtn", "isSalesDataReady", "isWacDataReady", "isForecastReady", "selectedSettingRoles", "selectedExcelRoles", "showPlaceholderLabel", "roles", "rolesIndent", "rolesGRN", "rolesPO", "inventory_Access", "user_Access", "delete_GRN", "edit_GRN", "close_GRN", "delete_PO", "edit_PO", "close_PO", "delete_Indent", "edit_Indent", "close_Indent", "report_Access", "reportAccess", "showInventoryStatusField", "showUserStatusField", "showRecipeStatusField", "showPartyStatusField", "showDeleteGRNField", "showEditGRNField", "showCloseGRNField", "showDeletePOField", "showEditPOField", "showClosePOField", "showDeleteIndentField", "showEditIndentField", "showCloseIndentField", "showPartialIndentField", "filteredReports", "updatedReportAccess", "inventoryBank", "userBank", "recipeBank", "partyBank", "grnBank", "poBank", "indentBank", "editIndentFilterCtrl", "editIndent", "reportBank", "report_Bank", "_onD<PERSON>roy", "accessForm", "group", "moduleForm", "inventoryStatusButton", "userStatusButton", "recipeStatusButton", "partyStatusButton", "recipe_Access", "party_Access", "salesForm", "startDate", "required", "endDate", "wacForm", "forecastForm", "grnForm", "formType", "deleteButtonGRN", "editButtonGRN", "closeButtonGRN", "poForm", "deleteButtonPO", "editButtonPO", "closeButtonPO", "indentForm", "deleteButtonIndent", "editButtonIndent", "closeButtonIndent", "partialButtonIndent", "emailForm", "purchaseRequestButton", "postGRNStatusButton", "indentApprovalButton", "purchaseApprovalButton", "purchaseOrderButton", "reportFailedButton", "error<PERSON><PERSON><PERSON><PERSON><PERSON>", "approvedButton", "rejected<PERSON><PERSON><PERSON>", "reportButton", "pi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportForm", "roleForm", "user", "getCurrentUser", "accessData", "sessionStorage", "getItem", "access", "JSON", "userRole", "getCurrRole", "readIPConfig", "getIPAddress", "getRoles", "getReportData", "settings", "push", "bulkExcel", "isSmallDevice$", "observe", "XSmall", "Small", "pipe", "result", "matches", "onTabChange", "event", "index", "togglePlaceholderLabel", "inputValue", "length", "startingDate", "Date", "ngOnInit", "getMonthlyClosingDates", "vendorFilterCtrl", "pattern", "valueChanges", "subscribe", "newValue", "vendorFilterBanks", "selectedBranchesSource", "data", "branches", "setValue", "ngAfterViewInit", "paginator", "salesPaginator", "wacPaginator", "forecastPaginator", "salesTab", "checkMenuNavigate", "tabGroup", "selectedIndex", "ngOnDestroy", "next", "complete", "searchTerm", "normalizedSearchTerm", "toLowerCase", "replace", "filter", "branch", "includes", "date", "offset", "istTime", "getTime", "hours", "getHours", "updatedTime", "setHours", "toLocaleTimeString", "hour", "minute", "second", "hour12", "tenantId", "res", "processRoles", "role", "slice", "Filter", "superAdminIndex", "indexOf", "unshift", "splice", "error", "err", "bank", "form", "search", "type", "bankName", "deleteCtrl", "editCtrl", "closeCtrl", "deleteFilterCtrl", "editFilterCtrl", "closeFilterCtrl", "area", "toggleSelectAll", "action", "control", "controls", "openDialog", "open", "dialogRef", "autoFocus", "disableClose", "min<PERSON><PERSON><PERSON>", "closeDialog", "closeAll", "start", "end", "undefined", "detectChanges", "<PERSON><PERSON><PERSON><PERSON>", "selectedR<PERSON>", "find", "requiredReports", "responseReport", "reportChange", "selectedReport", "patchValue", "getPOSPriceTires", "Array", "isArray", "priceTierList", "id", "name", "decorationPackage", "object", "item", "priceTierName", "defaultPriceTier", "console", "permissions", "responseGRN", "responsePO", "responseIndent", "responseEmail", "responseUIAccess", "keys", "Object", "last<PERSON>ey", "hasOwnProperty", "initializeFormFields", "log", "displayNames", "deleteAccess", "editAccess", "rtvAccess", "delete", "edit", "rtv", "closeAccess", "close", "partial", "purchaseRequest", "postGrnStatus", "indentApproval", "purchaseApproval", "purchaseOrder", "pi<PERSON><PERSON><PERSON><PERSON>", "status", "obj", "userEmail", "updateIPConfig", "snackBarShowSuccess", "snackBarShowError", "addConfig", "setNavigation", "navigate", "snackBarShowInfo", "radioChange", "getAccess", "getRolesList", "setAccess", "accessString", "stringify", "setItem", "submitPermission", "settingRole", "bulkExcelRole", "from", "Set", "updateAccess", "updateGRN", "updatePermissions", "updatePO", "updateIndent", "refreshForecast", "forecastTab", "refreshWac", "wacTab", "refreshSales", "tabChange", "tab", "textLabel", "salesRetrigger", "sales", "wacRetrigger", "wac", "forecastRetrigger", "dateCorrection", "changedDate", "getMinutes", "toISOString", "salesRerun", "invalid", "mark<PERSON>llAsTouched", "created<PERSON>y", "email", "get", "reset", "weightedAvg", "istDate", "forecast", "forecastData", "updateModule", "module", "accessPermission", "updateConfigAccess", "success", "updateEmail", "emailValues", "reportFailed", "systemErrorLog", "approved", "rejected", "getBackendName", "backendName", "updateReport", "reportBased", "reportName", "formGroup", "showDeleteField", "showEditField", "showCloseField", "deleteRole", "editRole", "closeRole", "contains", "inventoryStatusShown", "userStatusShown", "recipeStatusShown", "partyStatusShown", "isDeleteGrnShown", "isEditGrnShown", "isCloseGrnShown", "isDeletePoShown", "isEditPoShown", "isClosePoShown", "isDeleteIndentShown", "isCloseIndentShown", "onLocationSubmit", "isPartialIndentShown", "for<PERSON>ach", "month", "getStartOfMonthDate", "goBackToLocationSelection", "dates", "onLocationChange", "submit", "alert", "closingDetails", "selectedClosingDates", "startOfMonthDate", "split", "payload", "location", "headers", "post", "response", "closeMessage", "submissionMessage", "monthIndex", "getMonth", "isFinalStep", "element", "pssi", "ɵɵdirectiveInject", "i1", "ShareDataService", "i2", "FormBuilder", "NgZone", "i3", "HttpClient", "i4", "InventoryService", "i5", "NotificationService", "ChangeDetectorRef", "i6", "AuthService", "i7", "MatDialog", "i8", "MasterDataService", "i9", "Router", "selectors", "viewQuery", "DashboardProfileComponent_Query", "rf", "ctx", "provide", "useClass", "deps", "useValue", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardProfileComponent_Template", "DashboardProfileComponent_Template_mat_tab_group_selectedTabChange_12_listener", "DashboardProfileComponent_Template_mat_radio_group_ngModelChange_53_listener", "DashboardProfileComponent_Template_mat_radio_group_change_53_listener", "DashboardProfileComponent_form_58_Template", "DashboardProfileComponent_div_59_Template", "DashboardProfileComponent_Template_form_ngSubmit_64_listener", "DashboardProfileComponent_div_65_Template", "DashboardProfileComponent_mat_option_72_Template", "DashboardProfileComponent_Template_form_ngSubmit_79_listener", "DashboardProfileComponent_Template_mat_select_ngModelChange_86_listener", "DashboardProfileComponent_mat_option_87_Template", "DashboardProfileComponent_Template_mat_select_ngModelChange_94_listener", "DashboardProfileComponent_mat_option_95_Template", "DashboardProfileComponent_Template_form_ngSubmit_103_listener", "DashboardProfileComponent_Template_mat_radio_group_change_110_listener", "DashboardProfileComponent_div_116_Template", "DashboardProfileComponent_Template_mat_radio_group_change_124_listener", "DashboardProfileComponent_div_130_Template", "DashboardProfileComponent_Template_mat_radio_group_change_138_listener", "DashboardProfileComponent_div_144_Template", "DashboardProfileComponent_Template_mat_radio_group_change_152_listener", "DashboardProfileComponent_div_158_Template", "DashboardProfileComponent_Template_form_ngSubmit_163_listener", "DashboardProfileComponent_Template_mat_radio_group_change_167_listener", "DashboardProfileComponent_div_173_Template", "DashboardProfileComponent_Template_mat_radio_group_change_177_listener", "DashboardProfileComponent_div_183_Template", "DashboardProfileComponent_Template_mat_radio_group_change_187_listener", "DashboardProfileComponent_div_193_Template", "DashboardProfileComponent_Template_form_ngSubmit_198_listener", "DashboardProfileComponent_Template_mat_radio_group_change_202_listener", "DashboardProfileComponent_div_208_Template", "DashboardProfileComponent_Template_mat_radio_group_change_212_listener", "DashboardProfileComponent_div_218_Template", "DashboardProfileComponent_Template_mat_radio_group_change_222_listener", "DashboardProfileComponent_div_228_Template", "DashboardProfileComponent_Template_form_ngSubmit_233_listener", "DashboardProfileComponent_Template_mat_radio_group_change_237_listener", "DashboardProfileComponent_div_243_Template", "DashboardProfileComponent_Template_mat_radio_group_change_247_listener", "DashboardProfileComponent_div_253_Template", "DashboardProfileComponent_Template_mat_radio_group_change_257_listener", "DashboardProfileComponent_Template_form_ngSubmit_266_listener", "DashboardProfileComponent_Template_form_ngSubmit_326_listener", "DashboardProfileComponent_Template_mat_select_ngModelChange_330_listener", "DashboardProfileComponent_mat_option_333_Template", "DashboardProfileComponent_mat_expansion_panel_336_Template", "DashboardProfileComponent_Template_form_ngSubmit_342_listener", "DashboardProfileComponent_Template_mat_select_ngModelChange_346_listener", "DashboardProfileComponent_mat_option_349_Template", "DashboardProfileComponent_mat_accordion_351_Template", "DashboardProfileComponent_Template_form_ngSubmit_358_listener", "DashboardProfileComponent_Template_mat_select_selectionChange_365_listener", "DashboardProfileComponent_mat_label_366_Template", "DashboardProfileComponent_Template_input_keydown_space_367_listener", "stopPropagation", "DashboardProfileComponent_Template_input_input_367_listener", "_r205", "_r24", "ɵɵreference", "DashboardProfileComponent_mat_option_369_Template", "DashboardProfileComponent_div_370_Template", "DashboardProfileComponent_div_371_Template", "DashboardProfileComponent_div_372_Template", "DashboardProfileComponent_Template_mat_tab_group_selectedTabChange_375_listener", "DashboardProfileComponent_Template_button_click_378_listener", "DashboardProfileComponent_Template_form_ngSubmit_380_listener", "DashboardProfileComponent_div_401_Template", "DashboardProfileComponent_div_402_Template", "DashboardProfileComponent_div_403_Template", "DashboardProfileComponent_Template_button_click_408_listener", "DashboardProfileComponent_Template_form_ngSubmit_410_listener", "DashboardProfileComponent_div_416_Template", "DashboardProfileComponent_div_417_Template", "DashboardProfileComponent_div_418_Template", "DashboardProfileComponent_Template_button_click_423_listener", "DashboardProfileComponent_Template_form_ngSubmit_425_listener", "DashboardProfileComponent_div_431_Template", "DashboardProfileComponent_div_432_Template", "DashboardProfileComponent_div_433_Template", "DashboardProfileComponent_ng_template_436_Template", "ɵɵtextInterpolate", "_r29", "_r30", "_c7", "i10", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "UpperCasePipe", "TitleCasePipe", "DatePipe", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "Mat<PERSON><PERSON>", "MatTabGroup", "i13", "MatButton", "ɵNgNoValidate", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "i14", "MatIcon", "FormControlDirective", "FormGroupDirective", "FormControlName", "i15", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i16", "MatList", "MatListItem", "i17", "MatRadioGroup", "MatRadioButton", "i18", "MatInput", "i19", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardTitle", "i20", "MatSelect", "i21", "MatOption", "i22", "MatSelectSearchComponent", "i23", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "i24", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i25", "MatPaginator", "i26", "NgxSkeletonLoaderComponent", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard-profile/dashboard-profile.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard-profile/dashboard-profile.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, TemplateRef, ViewChild, inject , OnInit} from '@angular/core';\nimport { CommonModule , formatDate} from '@angular/common';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\n\nimport { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap  } from 'rxjs/operators';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';\nimport { AbstractControl, FormBuilder, FormControl,FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { NgZone } from '@angular/core';\t\nimport { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';\nimport { MatInputModule } from '@angular/material/input';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { Router } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';\nimport { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';\n\nexport const MY_DATE_FORMATS = {\n  parse: {\n    dateInput: 'DD/MM/YYYY',\n  },\n  display: {\n    dateInput: 'DD/MM/YYYY',\n    monthYearLabel: 'MMM YYYY',\n    dateA11yLabel: 'DD/MM/YYYY',\n    monthYearA11yLabel: 'MMMM YYYY',\n  },\n};\n\n@Component({\n  selector: 'app-dashboard-profile',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatSliderModule,\n    MatTabsModule,\n    BackgroundImageCardComponent,\n    MatButtonModule,\n    FormsModule ,\n    MatIconModule,\n    BackgroundImageCardHeaderComponent, \n    MatDividerModule,\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatListModule,\n    MatRadioModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatCardModule,\n    MatDialogModule,\n    MatSelectModule,\n    NgxMatSelectSearchModule,\n    MatExpansionModule,\n    MatTableModule,\n    MatPaginatorModule,\n    NgxSkeletonLoaderModule\n  ],\n  providers: [\n    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },\n    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },\n  ],\n  templateUrl: './dashboard-profile.component.html',\n  styleUrls: ['./dashboard-profile.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardProfileComponent {\n  monthlyClosingDatesArray: { date: string, month: string }[] = [];\n  submissionMessage: string;\n  startingDate: Date;\n  engineUrl: string = environment.engineUrl;\n\n  // selectedDateOption: string; // Tracks selected date option\n  startingDates: { [key: string]: Date } = {}; \n  showLocationSelection: boolean = true;\n  selectedLocation: string = ''; \n  selectedDateOption: string | null = null;\n  monthlyClosingDates: any[] = []; \n  selectedMonthDates: Date[] = [];\n  selectedDay: Date | null = null;\n  selectedDays: { [key: string]: Date | null } = {}; \n\n  pickDateOption: string = 'no';\n  closingType: string = '';\n  selectedDate: string = '';\n  monthlyDate: string = ''; \n  fifteenDaysDate: string = ''; \n  // selectedLocation: boolean = false; // Indicates if a location has been selected\n  @ViewChild('salesPaginator') salesPaginator: MatPaginator;\n  @ViewChild('wacPaginator') wacPaginator: MatPaginator;\n  @ViewChild('forecastPaginator') forecastPaginator: MatPaginator;\n  @ViewChild('showDialog') dialogRef: TemplateRef<any>;\n  public isReadOnly = false;\n  public user: any;\n  public userRole: any; \n  public currentIp :string;\n\tsalesLocation:boolean=false\t\t\n  breakpointObserver = inject(BreakpointObserver);\n  isSmallDevice$;\n  ipPattern: RegExp = new RegExp(\"(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\");\n  ipOrSubnet: string = '';\n  startIP: string = '';\n  endIP: string = '';\n  selectedOption: string = 'individual';\n  profitTarget: number = 1;\n  priceTier: any ;\n  isIPValid : boolean = false;\n  priceData = new FormControl();\n  branchData=new FormControl(null);\t\n  IpType: string = 'withoutIp'\n  showIpProcess : boolean = false;\n  showIpBtn : boolean = false;\n  isSalesDataReady : boolean = false;\n  isWacDataReady : boolean = false;\n  isForecastReady : boolean = false;\n  selectedSettingRoles = [] \n  selectedExcelRoles = []\n  accessForm: any; \n  access: any;\n  priceTierList: any[];\n  branches: any[];\t\n  form: FormGroup;\t\t\n  object: any;\n  vendorFilterCtrl: FormControl<string>;\n  filteredBranches: any[];\n  showPlaceholderLabel: boolean=true;\n  roles: string[] = [];\n  rolesIndent: string[] = [];\t\t\n\trolesGRN: string[] = [];\t\t\n\trolesPO: string[] = [];\n  grnForm: any;\n  poForm: any;\n  indentForm: any;\n  emailForm: any;\n  moduleForm: any;\n  reportForm: any;\n  roleForm: any;\n  salesForm: any;\n  wacForm: any;\n  forecastForm: any;\n  inventory_Access = []\n  user_Access = []\n  delete_GRN = []\n  edit_GRN = []\n  close_GRN = []\n  delete_PO = []\n  edit_PO = []\n  close_PO = []\n  delete_Indent = []\n  edit_Indent = []\n  close_Indent = []\n  report_Access = []\n  reportAccess = []\n  showInventoryStatusField = false;\n  showUserStatusField = false;\n  showRecipeStatusField = false;\n  showPartyStatusField = false;\n  showDeleteGRNField = false;\n  showEditGRNField = false;\n  showCloseGRNField = false;\n  showDeletePOField = false;\n  showEditPOField = false;\n  showClosePOField = false;\n  showDeleteIndentField = false;\n  showEditIndentField = false;\n  showCloseIndentField = false;\n  showPartialIndentField = false;\n  responseGRN: any;\n  responsePO: any;\n  responseIndent: any;\n  responseEmail: any;\n  responseUIAccess: any;\n  responseReport: any;\n  displayNames: any;\n  filteredReports: any[] = [];\n  filteredRole: any;\n  updatedReportAccess: { [key: string]: any } = {};\n  salesColumns = ['position', 'createdDate','status','createdTime']\n  dataSourceSales = new MatTableDataSource<any>([]);\n  weightedAvgColumns = ['position','status','createdTime']\n  dataSourceWeightedAvg = new MatTableDataSource<any>([]);\n  forecastColumns = ['position','status','createdTime']\n  dataSourceForecast = new MatTableDataSource<any>([]);\n\n  public inventoryBank: any[] = [];\n  public inventoryFilterCtrl: FormControl = new FormControl();\n  public inventory: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public userBank: any[] = [];\n  public userFilterCtrl: FormControl = new FormControl();\n  public users: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public recipeBank: any[] = [];\n  public recipeFilterCtrl: FormControl = new FormControl();\n  public recipe: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public partyBank: any[] = [];\n  public partyFilterCtrl: FormControl = new FormControl();\n  public party: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public grnBank: any[] = [];\n  public deleteGRNFilterCtrl: FormControl = new FormControl();\n  public deleteGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public editGRNFilterCtrl: FormControl = new FormControl();\n  public editGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public closeGRNFilterCtrl: FormControl = new FormControl();\n  public closeGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public poBank: any[] = [];\n  public deletePOFilterCtrl: FormControl = new FormControl();\n  public deletePO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public editPOFilterCtrl: FormControl = new FormControl();\n  public editPO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public closePOFilterCtrl: FormControl = new FormControl();\n  public closePO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  \n  public indentBank: any[] = [];\n  public deleteIndentFilterCtrl: FormControl = new FormControl();\n  public deleteIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public editIndentFilterCtrl: FormControl = new FormControl();\n  public editIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  public closeIndentFilterCtrl: FormControl = new FormControl();\n  public closeIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public reportBank: any[] = [];\n  public reportFilterCtrl: FormControl = new FormControl();\n  public report: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  public report_Bank: any[] = [];\n  public report_FilterCtrl: FormControl = new FormControl();\n  public reports: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n\n  protected _onDestroy = new Subject<void>();\n  InventoryService: any;\n  // http: any;\n  @ViewChild('tabGroup') tabGroup: MatTabGroup;\n    \t\t\n    constructor(private shareDataService: ShareDataService,\t\t\n    private formBuilder: FormBuilder,\t\t\n    private ngZone: NgZone,\n    private http: HttpClient,\n    private api: InventoryService,\n    private notify: NotificationService,\n    private cd: ChangeDetectorRef,\n    private sharedData: ShareDataService,\n    private auth: AuthService,\n    private dialog: MatDialog,\n    private masterDataService: MasterDataService,\n    private router: Router,\n    private fb: FormBuilder,\n\n    ) {\n      this.accessForm = this.fb.group({\n        selectedSettingRoles: [''],\n        selectedExcelRoles: ['']\n      });\n      this.moduleForm = this.fb.group({\n        inventoryStatusButton: [''],\n        userStatusButton: [''],\n        recipeStatusButton: [''],\n        partyStatusButton: [''],\n        inventory_Access: [''],\n        user_Access: [''],\n        recipe_Access: [''],\n        party_Access: ['']\n      });\n      this.salesForm = this.fb.group({\n        startDate: ['', Validators.required],\n        endDate: ['', Validators.required]    \n      });\n      this.wacForm = this.fb.group({\n        // startDate: [''],\n      });\n      this.forecastForm = this.fb.group({\n        startDate: [''],\n        endDate: ['']\n      });\n      this.grnForm = this.fb.group({\n        delete_GRN: [''],\n        edit_GRN: [''],\n        close_GRN: [''],\n        formType: ['grnAccess'],\n        deleteButtonGRN: [''],\n        editButtonGRN: [''],\n        closeButtonGRN: ['']\n      });\n      this.poForm = this.fb.group({\n        delete_PO: [''],\n        edit_PO: [''],\n        close_PO: [''],\n        formType: ['POAccess'],\n        deleteButtonPO: [''],\n        editButtonPO: [''],\n        closeButtonPO: ['']\n      });\n      this.indentForm = this.fb.group({\n        delete_Indent: [''],\n        edit_Indent: [''],\n        close_Indent: [''],\n        formType: ['indentAccess'],\n        deleteButtonIndent: [''],\n        editButtonIndent: [''],\n        closeButtonIndent: [''],\n        partialButtonIndent: ['']\n      });\n      this.emailForm = this.fb.group({\n        purchaseRequestButton: [''],\n        postGRNStatusButton: [''],\n        indentApprovalButton: [''],\n        purchaseApprovalButton: [''],\n        purchaseOrderButton: [''],\n        reportFailedButton: [''],\n        errorLogButton: [''],\n        approvedButton: [''],\n        rejectedButton: [''],\n        reportButton: [''],\n        piApprovalButton: ['']\n      });\n      this.reportForm = this.fb.group({\n        report_Access: [''],\n        reportAccess: ['']\n      });\n      this.roleForm = this.fb.group({\n        report_Access: [''],\n        reportAccess: ['']\n      });\n      this.user = this.auth.getCurrentUser();      \n      const accessData = sessionStorage.getItem('access');\n      this.access = accessData ? JSON.parse(accessData) : {};\n      this.userRole = this.auth.getCurrRole();\n      this.readIPConfig();\n      this.getIPAddress();\n      this.getPriceTires();\n      this.getRoles();\n      this.getReportData();\n      if(this.access.settings){\n        this.selectedSettingRoles.push(...this.access.settings)\n      }\n      if(this.access.bulkExcel){\n        this.selectedExcelRoles.push(...this.access.bulkExcel)\n      }\n      \n      this.isSmallDevice$ = this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).pipe(\n        map((result) => {\n          return result.matches;\n        })\n      );\n  }\n\n  onTabChange(event: any): void {\n    if (event.index === 2) {\n      this.getRoles();\n    }\n  }\n\n  togglePlaceholderLabel(inputValue: string): void {\n    this.showPlaceholderLabel = inputValue.length === 0;\n    this.startingDate = new Date(); // or set a default value if needed\n\n  }\n\n  ngOnInit(): void { \n    // this.initializeMonthlyClosingDates();\n    this.monthlyClosingDates = this.getMonthlyClosingDates();\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged())\n    .subscribe(newValue => {\n      this.vendorFilterBanks(newValue);\n    });\n    this.shareDataService.selectedBranchesSource.subscribe(data => {\n      this.branches=data\n      this.filteredBranches= this.branches\n\n      if (this.branches.length === 1) {\n        this.branchData.setValue(this.branches[0].restaurantIdOld);\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      } else {\n        this.branchData.setValue(null);\n        this.selectedLocation = null;  \n      }\n      \n    });\n  }\n\n  ngAfterViewInit() {\n     this.dataSourceSales.paginator = this.salesPaginator;\n     this.dataSourceWeightedAvg.paginator = this.wacPaginator;\n     this.dataSourceForecast.paginator = this.forecastPaginator;\n     this.salesTab();\n     if (this.sharedData.checkMenuNavigate() === true) {\n      this.tabGroup.selectedIndex = 2;\n      }\n    // this.dataSource.sort = this.sort;\n  }\n\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n\n  vendorFilterBanks(searchTerm: string) {\n    if (!searchTerm) {\n      this.filteredBranches = this.branches;\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }  \n\n  convertToIST(createTs: string): string {\n    const date = new Date(createTs);\n    const offset = 6.5 * 60 * 60 * 1000; \n    const istTime = new Date(date.getTime() + offset);\n    let hours = istTime.getHours();    \n    if (hours >= 12) {\n        hours = hours - 12; \n    } else {\n        hours = hours + 12; \n    }\n    const updatedTime = new Date(istTime);\n    updatedTime.setHours(hours);\n    return updatedTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true });\n  }\n\n  getRoles() {\n    this.api.getRoles(this.user.tenantId).subscribe({\n      next: (res) => {        \n        if (res['success'] == true) { \n          this.processRoles(res['roles'], 'GRN');\n          this.processRoles(res['roles'], 'PO');\n          this.processRoles(res['roles'], 'Indent');\n  \n          this.roles = res['roles'];\n          this.inventoryBank = this.roles.map(role => role);\n          this.inventory.next(this.inventoryBank.slice());\n          this.inventoryFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.inventoryBank, this.inventoryFilterCtrl, this.inventory);\n          });\n          this.roles = res['roles'];\n          this.userBank = this.roles.map(role => role);\n          this.users.next(this.userBank.slice());\n          this.userFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.userBank, this.userFilterCtrl, this.users);\n          });          \n          this.roles = res['roles'];\n          this.recipeBank = this.roles.map(role => role);\n          this.recipe.next(this.recipeBank.slice());\n          this.recipeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.recipeBank, this.recipeFilterCtrl, this.recipe);\n          });\n          this.roles = res['roles'];\n          this.partyBank = this.roles.map(role => role);\n          this.party.next(this.partyBank.slice());\n          this.partyFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.partyBank, this.partyFilterCtrl, this.party);\n          });\n          this.roles = res['roles'];\n          this.report_Bank = this.roles.map(role => role);\n          this.reports.next(this.report_Bank.slice());\n          this.report_FilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.report_Bank, this.report_FilterCtrl, this.reports);\n          });\n          let superAdminIndex = this.roles.indexOf(\"superAdmin\");\n          if (superAdminIndex !== -1) {\n            this.roles.unshift(this.roles.splice(superAdminIndex, 1)[0]);\n          }\n        }\n      },\n      error: (err) => {\n      },\n    });\n  }\n\n  protected Filter(bank:any, form:any, data:any) {\n    if (!bank) {\n      return;\n    }\n    let search = form.value;\n    if (!search) {\n      data.next(bank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    data.next(\n      bank.filter(data => data.toLowerCase().indexOf(search) > -1)\n    );\n  }\n  \n  processRoles(roles: any[], type: string) {\n    const bankName = `${type.toLowerCase()}Bank`;\n    const deleteCtrl = `delete${type}`;\n    const editCtrl = `edit${type}`;\n    const closeCtrl = `close${type}`;\n    const deleteFilterCtrl = `${deleteCtrl}FilterCtrl`;\n    const editFilterCtrl = `${editCtrl}FilterCtrl`;\n    const closeFilterCtrl = `${closeCtrl}FilterCtrl`;\n\n    this[bankName] = roles.map(area => area);\n  \n    this[deleteCtrl].next(this[bankName].slice());\n    this[deleteFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[deleteFilterCtrl], this[deleteCtrl]);\n    });\n  \n    this[editCtrl].next(this[bankName].slice());\n    this[editFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[editFilterCtrl], this[editCtrl]);\n    });\n\n    this[closeCtrl].next(this[bankName].slice());\n    this[closeFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this.Filter(this[bankName], this[closeFilterCtrl], this[closeCtrl]);\n    });\n  }\n  \n  toggleSelectAll(formType: string, action: string) {\n    const control = this[`${formType.toLowerCase()}Form`].controls[`${action}_${formType}`];\n    const bankName = `${formType.toLowerCase()}Bank`;\n  \n    if (control.value.length - 1 === this[bankName].length) {\n      control.setValue([]);\n    } else {\n      control.setValue(this[bankName]);\n    }\n  \n    this.validateSales(control.value);\n  }\n  \n  toggleSelectAllDeleteGRN() {\n    this.toggleSelectAll('GRN', 'delete');\n  }\n  \n  toggleSelectAllEditGRN() {\n    this.toggleSelectAll('GRN', 'edit');\n  }\n\n  toggleSelectAllCloseGRN() {\n    this.toggleSelectAll('GRN', 'close');\n  }\n  \n  toggleSelectAllDeletePO() {\n    this.toggleSelectAll('PO', 'delete');\n  }\n  \n  toggleSelectAllEditPO() {\n    this.toggleSelectAll('PO', 'edit');\n  }\n\n  toggleSelectAllClosePO() {\n    this.toggleSelectAll('PO', 'close');\n  }\n  \n  toggleSelectAllDeleteIndent() {\n    this.toggleSelectAll('Indent', 'delete');\n  }\n  \n  // toggleSelectAllEditIndent() {\n  //   this.toggleSelectAll('Indent', 'edit');\n  // }\n\n  toggleSelectAllCloseIndent() {\n    this.toggleSelectAll('Indent', 'close');\n  }\n\n  toggleSelectAllInventory() {\n    const control = this.moduleForm.controls['inventory_Access'];\n    if (control.value.length - 1 === this.inventoryBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.inventory_Access);\n    } else {\n      control.setValue(this.inventoryBank);\n      this.validateSales(this.moduleForm.value.inventory_Access);\n    }\n  }\n\n  toggleSelectAllUser() {\n    const control = this.moduleForm.controls['user_Access'];\n    if (control.value.length - 1 === this.userBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.user_Access);\n    } else {\n      control.setValue(this.userBank);\n      this.validateSales(this.moduleForm.value.user_Access);\n    }\n  }\n  \n  toggleSelectAllRecipe() {\n    const control = this.moduleForm.controls['recipe_Access'];\n    if (control.value.length - 1 === this.recipeBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.recipe_Access);\n    } else {\n      control.setValue(this.recipeBank);\n      this.validateSales(this.moduleForm.value.recipe_Access);\n    }\n  }\n\n  toggleSelectAllParty() {\n    const control = this.moduleForm.controls['party_Access'];\n    if (control.value.length - 1 === this.partyBank.length) {\n      control.setValue([]);\n      this.validateSales(this.moduleForm.value.party_Access);\n    } else {\n      control.setValue(this.partyBank);\n      this.validateSales(this.moduleForm.value.party_Access);\n    }\n  }\n\n  selectAllRolesForReport() {\n    const control = this.reportForm.controls['report_Access'];\n    if (control.value.length - 1 === this.report_Bank.length) {\n      control.setValue([]);\n      this.validateSales(this.reportForm.value.report_Access);\n    } else {\n      control.setValue(this.report_Bank);\n      this.validateSales(this.reportForm.value.report_Access);\n    }\n  }\n\n  selectAllReportsForRoles() {\n    const control = this.roleForm.controls['reportAccess'];\n    if (control.value.length - 1 === this.reportBank.length) {\n      control.setValue([]);\n      this.validateSales(this.roleForm.value.reportAccess);\n    } else {\n      control.setValue(this.reportBank);\n      this.validateSales(this.roleForm.value.reportAccess);\n    }\n  }\n\n  openDialog(): void {\n    this.dialog.open(this.dialogRef,{\n      autoFocus: false,\n      disableClose: true,\n      minWidth:'40vw'\n    });\n  }\n\n  closeDialog(): void {\n    this.dialog.closeAll();\n  }\n\n  validateInput() {\n    if(this.selectedOption == \"individual\"){\n      this.isIPValid = this.ipPattern.test(this.ipOrSubnet);\n    }else{\n      let start = this.ipPattern.test(this.startIP);\n      let end = this.ipPattern.test(this.endIP);\n      if(start && end){\n        this.isIPValid = true;\n      }else{\n        this.isIPValid = false;\n      }\n    } \n  }\n  \n  validateSales(delete_GRN?: any) {\n    this.profitTarget < 1 ? this.profitTarget = 1 : undefined;\n    this.cd.detectChanges();\n  }\n\n  roleChange(selectedRole: string) {\n    this.filteredRole = this.roles.find(role => role === selectedRole);\n    const requiredReports = this.responseReport\n    .filter(report => report.access.includes(selectedRole))\n    .map(report => report.displayName);\n    const control = this.roleForm.controls['reportAccess'];\n    control.setValue(requiredReports);\n    this.validateSales(requiredReports);\n    this.cd.detectChanges();\n  }\n\n  reportChange(selectedReport: string) {\n    this.filteredReports = this.responseReport.filter(report => report.displayName === selectedReport);    \n    if (this.filteredReports.length > 0) {\n      const reportAccess = this.updatedReportAccess[selectedReport] || this.filteredReports[0].access;\n      this.reportForm.patchValue({\n        report_Access: reportAccess\n      });\n    }\n    this.cd.detectChanges();\n  }\n\n  getPriceTires(){\n    this.api.getPOSPriceTires(this.user.tenantId, this.branchData.value).subscribe({\n      next: (res) => {\n        if (Array.isArray(res)) {\n          this.priceTierList = res.map(({ id, name }) => ({ id, name }));          \n        let decorationPackage\n        if(this.object && this.object[this.branchData.value]){\n          decorationPackage = this.priceTierList.find(item => item.name === this.object[this.branchData.value].priceTierName || item.id  === this.object[this.branchData.value].defaultPriceTier );\n        }       \n        this.priceData.setValue(decorationPackage || null);\n      }\n      },\n      error: (err) => {\n        console.error(err);\n      },\n    });\n  }\n\n  readIPConfig() {\n    this.api.readIPConfig(this.user.tenantId).subscribe({\n      next: (res) => {\n        if (res?.['success']) {\n          const data = res['data'];\n          const permissions = data?.['permission'] || {};          \n          this.responseGRN = permissions['grnAccess'] || [];\n          this.responsePO = permissions['POAccess'] || [];\n          this.responseIndent = permissions['indentAccess'] || [];\n          this.responseEmail = permissions['emailConfiguration'] || [];          \n          this.responseUIAccess = permissions['UIAccess'] || [];          \n          this.salesLocation = data['multiPosUser'] || [];\n  \n          const defaultPriceTier = data['defaultPriceTier'] || {};\n          if (defaultPriceTier && typeof defaultPriceTier === 'object') {\n            const keys = Object.keys(defaultPriceTier);\n            if (keys.length > 0) {\n              const lastKey = keys[keys.length - 1];\n              this.branchData.setValue(lastKey);\n              this.object = defaultPriceTier;\n              this.getPriceTires();\n            }\n          } else {\n            console.error('defaultPriceTier is either undefined or not an object');\n          }\n  \n          if (permissions.hasOwnProperty('sales') && permissions['sales'].hasOwnProperty('profitTarget')) {\n            this.profitTarget = permissions['sales']['profitTarget']; \n          } else {\n            this.profitTarget = 1;\n          }  \n          this.isIPValid = true;      \n          this.selectedOption = data['ip_type'];\n          \n          if (this.selectedOption == 'range') {\n            this.startIP = data['ips_range']?.['start'] || '0.0.0.0';\n            this.endIP = data['ips_range']?.['end'] || '0.0.0.0';\n            if (this.startIP == '0.0.0.0') {\n              this.IpType = 'withoutIp';\n            } else {\n              this.IpType = 'withIp';\n              this.showIpProcess = true;\n            }\n          } else {\n            this.ipOrSubnet = data['allowed_ips']?.[0] || '';\n            this.IpType = 'withIp';\n            this.showIpProcess = true;\n          }         \n          this.cd.detectChanges();\n          this.initializeFormFields();\n        }\n      },\n      error: (err) => { console.log(err) }\n    });\n  }\n\n  getReportData() {\n    this.api.getReportData(this.user.tenantId).subscribe({\n      next: (res) => {\n        if (res?.['success']) {\n          this.responseReport = res['data']['types'];                    \n          this.displayNames = this.responseReport.map(report => report.displayName);\n          this.reportBank = this.responseReport.map(report => report.displayName);\n          this.report.next(this.reportBank.slice());\n          this.reportFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n            this.Filter(this.reportBank, this.reportFilterCtrl, this.report);\n          });\n          this.cd.detectChanges();\n        }\n      },\n      error: (err) => { console.log(err) }\n    });\n  }\n  \n  initializeFormFields() {\n    if (this.responseGRN) {\n        this.grnForm.patchValue({\n            delete_GRN: this.responseGRN.deleteAccess,\n            edit_GRN: this.responseGRN.editAccess,\n            close_GRN: this.responseGRN.rtvAccess,\n            deleteButtonGRN: this.responseGRN.delete ? 'Yes' : 'No',\n            editButtonGRN: this.responseGRN.edit ? 'Yes' : 'No',\n            closeButtonGRN: this.responseGRN.rtv ? 'Yes' : 'No'\n        });\n        this.showDeleteGRNField = this.responseGRN.delete;\n        this.showEditGRNField = this.responseGRN.edit;\n        this.showCloseGRNField = this.responseGRN.rtv;\n    }\n\n    if (this.responsePO) {\n        this.poForm.patchValue({\n            delete_PO: this.responsePO.deleteAccess,\n            edit_PO: this.responsePO.editAccess,\n            close_PO: this.responsePO.closeAccess,\n            deleteButtonPO: this.responsePO.delete ? 'Yes' : 'No',\n            editButtonPO: this.responsePO.edit ? 'Yes' : 'No',\n            closeButtonPO: this.responsePO.close ? 'Yes' : 'No'\n        });\n        this.showDeletePOField = this.responsePO.delete;\n        this.showEditPOField = this.responsePO.edit;\n        this.showClosePOField = this.responsePO.close;\n    }\n\n    if (this.responseIndent) {\n        this.indentForm.patchValue({\n            delete_Indent: this.responseIndent.deleteAccess,\n            // edit_Indent: this.responseIndent.editAccess,\n            close_Indent: this.responseIndent.closeAccess,\n            deleteButtonIndent: this.responseIndent.delete ? 'Yes' : 'No',\n            // editButtonIndent: this.responseIndent.edit ? 'Yes' : 'No',\n            closeButtonIndent: this.responseIndent.close ? 'Yes' : 'No',\n            partialButtonIndent: this.responseIndent.partial ? 'Yes' : 'No'\n        });\n        this.showDeleteIndentField = this.responseIndent.delete;\n        // this.showEditIndentField = this.responseIndent.edit;\n        this.showCloseIndentField = this.responseIndent.close;\n    }\n\n    if (this.responseEmail) {\n        this.emailForm.patchValue({\n            purchaseRequestButton: this.responseEmail.purchaseRequest ? 'Yes' : 'No',\n            postGRNStatusButton: this.responseEmail.postGrnStatus ? 'Yes' : 'No',\n            indentApprovalButton: this.responseEmail.indentApproval ? 'Yes' : 'No',\n            purchaseApprovalButton: this.responseEmail.purchaseApproval ? 'Yes' : 'No',\n            purchaseOrderButton: this.responseEmail.purchaseOrder ? 'Yes' : 'No',\n            // reportFailedButton: this.responseEmail.reportFailed ? 'Yes' : 'No',\n            // errorLogButton: this.responseEmail.systemErrorLog ? 'Yes' : 'No',\n            // approvedButton: this.responseEmail.approved ? 'Yes' : 'No',\n            // rejectedButton: this.responseEmail.rejected ? 'Yes' : 'No',\n            // reportButton: this.responseEmail.report ? 'Yes' : 'No',\n            piApprovalButton: this.responseEmail.piApproval ? 'Yes' : 'No'\n        });\n    }\n\n    if (this.responseUIAccess) {\n      this.moduleForm.patchValue({\n        inventory_Access: this.responseUIAccess.inventory.access,\n        user_Access: this.responseUIAccess.user.access,\n        recipe_Access: this.responseUIAccess.recipe.access,\n        // party_Access: this.responseUIAccess.party.access,\n        inventoryStatusButton: this.responseUIAccess.inventory.status ? 'Yes' : 'No',\n        userStatusButton: this.responseUIAccess.user.status ? 'Yes' : 'No',\n        recipeStatusButton: this.responseUIAccess.recipe.status ? 'Yes' : 'No',\n        // partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No',\n      });\n      this.showInventoryStatusField = this.responseUIAccess.inventory.status;\n      this.showUserStatusField = this.responseUIAccess.user.status;\n      this.showRecipeStatusField = this.responseUIAccess.recipe.status;\n      // this.showPartyStatusField = this.responseUIAccess.party.status;\n    } \n    if (this.responseUIAccess.party) {\n      this.moduleForm.patchValue({\n        party_Access: this.responseUIAccess.party.access,\n        partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No'\n      });\n      this.showPartyStatusField = this.responseUIAccess.party.status;\n    } else {\n      this.moduleForm.patchValue({\n        partyStatusButton: 'No'\n      });\n    }\n    }\n\n  onSubmit() {\n    let obj;\n    if(this.IpType == \"withoutIp\"){\n      obj={\n        \"tenantId\" : this.user.tenantId,\n        \"email\" : this.user.userEmail,\n        \"name\" : this.user.name,\n        \"ip_type\" : 'range',\n        \"ips_range\":{ \"start\" : '0.0.0.0', \"end\" : '***************' },\n        \"allowed_ips\" :[this.ipOrSubnet]\n      }\n    }else{\n      obj={\n        \"tenantId\" : this.user.tenantId,\n        \"email\" : this.user.userEmail,\n        \"name\" : this.user.name,\n        \"ip_type\" : this.selectedOption,\n        \"ips_range\":{ \"start\" : this.startIP, \"end\" : this.endIP },\n        \"allowed_ips\" :[this.ipOrSubnet]\n      }\n    }\n    this.api.updateIPConfig(obj).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully!');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.cd.detectChanges();\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }\n\n  addConfig() {\n    let obj = {}\n    obj['branch']=this.branchData.value\n    obj['tenantId'] = this.user.tenantId;\n    obj['priceTierData'] = this.priceData.value.id;\n    obj['priceTierName'] = this.priceData.value.name;\n    obj['salesConfig'] = true;\n\n    this.api.updateIPConfig(obj).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully!');\n          const defaultPriceTier = res['data']['defaultPriceTier'] || {};\n          if (defaultPriceTier && typeof defaultPriceTier === 'object') {\n            this.object = defaultPriceTier;\n          }\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n        this.cd.detectChanges();\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }\n\n  getIPAddress(){\n    this.api.getIPAddress().subscribe((res: any) => {\n      this.currentIp = res['ipString'];\n      this.cd.detectChanges();\n    });\n  }\n\n  submitIp(){\n    if(this.IpType === 'withoutIp'){\n      this.startIP;\n      this.endIP;\n      this.onSubmit()\n      this.masterDataService.setNavigation('inventoryList');\n      this.router.navigate(['/dashboard/home']);\n      this.dialog.closeAll();\n    }else if(this.IpType === 'withIp'){\n      this.dialog.closeAll();\n    }else{\n      this.notify.snackBarShowInfo('select any of one IP type')\n    }\n  }\n\n  radioChange(){\n    if(this.IpType == \"withIp\"){\n      this.showIpProcess = true;\n    }else{\n      this.showIpProcess = false;\n      this.startIP = '0.0.0.0'\n      this.endIP = '***************'\n      this.selectedOption ='range'\n    }\n  }\n \n  getAccess() {\n    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {\n      if ('access' in data) {\n        this.sharedData.setAccess(data['access']);\n        const accessString = JSON.stringify(data['access']);\n        sessionStorage.setItem(GlobalsService.accessData, accessString);\n        sessionStorage.setItem('access', accessString);\n        this.access = data['access'];\n        if (this.access.settings) {\n          this.selectedSettingRoles.push(...this.access.settings);\n        }\n        if (this.access.bulkExcel) {\n          this.selectedExcelRoles.push(...this.access.bulkExcel);\n        }\n      }\n    });\n  }\n\n  submitPermission(){\n    let settingRole = []\n    let bulkExcelRole = []\n    settingRole = this.accessForm.value.selectedSettingRoles\n    bulkExcelRole = this.accessForm.value.selectedExcelRoles\n    settingRole.push('superAdmin')\n    bulkExcelRole.push('superAdmin')\n    let obj = {}\n    let access = {\n      settings :  Array.from(new Set(settingRole)) ,\n      bulkExcel : Array.from(new Set(bulkExcelRole))\n    }\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['access'] = access\n    this.api.updateAccess(obj).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('Updated successfully')\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }   \n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });   \n  }\n\n  updateGRN() {\n    this.updatePermissions('GRN', this.grnForm, this.showDeleteGRNField, this.showEditGRNField, this.showCloseGRNField);\n  }\n  \n  updatePO() {\n    this.updatePermissions('PO', this.poForm, this.showDeletePOField, this.showEditPOField, this.showClosePOField);\n  }\n  \n  updateIndent() {\n    this.updatePermissions('Indent', this.indentForm, this.showDeleteIndentField, this.showEditIndentField, this.showCloseIndentField, this.showPartialIndentField);\n  }\n\n  refreshForecast(){\n    this.forecastTab();\n  }\n\n  refreshWac(){\n    this.wacTab();\n  }\n\n  refreshSales(){\n    this.salesTab();\n  }\n\n  tabChange(event: MatTabChangeEvent) {\n    if (event.tab.textLabel === 'SALES') {\n      this.salesTab();\n    }\n    if (event.tab.textLabel === 'WAC') {\n      this.wacTab();\n    }\n    if (event.tab.textLabel === 'FORECAST') {\n      this.forecastTab();\n    }\n  }\n  \n  salesTab() {\n    let obj = {}\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['event'] = 'sales' ;\n    this.api.salesRetrigger(obj).subscribe({\n      next: (res) => {\n        const sales = res['data'];\n        this.dataSourceSales.data = res['data'];\n        this.dataSourceSales.paginator = this.salesPaginator;\n        this.isSalesDataReady = true;\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });      \n  }\n\n  wacTab(){\n    let obj = {}\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['event'] = 'weightedAverage' ;    \n    this.api.wacRetrigger(obj).subscribe({\n      next: (res) => {\n        const wac = res['data'];\n        this.dataSourceWeightedAvg.data = res['data'];\n        this.dataSourceWeightedAvg.paginator = this.wacPaginator;\n        this.isWacDataReady = true;\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  forecastTab(){\n    let obj = {}\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['event'] = 'forecast' ;    \n    this.api.forecastRetrigger(obj).subscribe({\n      next: (res) => {\n        const wac = res['data'];\n        this.dataSourceForecast.data = res['data'];\n        this.dataSourceForecast.paginator = this.forecastPaginator;\n        this.isForecastReady = true;\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  dateCorrection(date){\n    const changedDate = new Date(date);\n    changedDate.setHours(changedDate.getHours() + 5, changedDate.getMinutes() + 30);\n    return changedDate.toISOString()\n  }\n  \n  salesRerun() {   \n    if (this.salesForm.invalid) {\n      this.salesForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else { \n    let obj = {\n      tenantId: this.user.tenantId,\n      event: 'sales',\n      createdBy: this.user.email,\n      startDate: this.dateCorrection(this.salesForm.get('startDate').value),\n      endDate: this.dateCorrection(this.salesForm.get('endDate').value)\n    };\n    \n    this.api.salesRerun(obj).subscribe({\n      next: (res) => {\n        if (res.result === 'success') {\n          this.notify.snackBarShowSuccess('Submitted Successfully');\n          this.salesForm.reset(); \n          this.refreshSales()\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }      },\n      error: (err) => {\n        console.log(err);\n      },\n    });  \n  }\n  }\n\n  weightedAvg(){\n    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1)+ 'Z';        \n    let obj = {}\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['event'] = 'weightedAverage' ;  \n    obj['createdBy'] = this.user.email ; \n    obj['currentDate'] = istDate ; \n    this.api.weightedAvg(obj).subscribe({\n      next: (res) => {\n        if (res.result === 'success') {\n          this.notify.snackBarShowSuccess('Submitted Successfully');\n          this.refreshWac() ;\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  forecast(){\n    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1)+ 'Z';        \n    let obj = {}\n    obj['tenantId'] = this.user.tenantId ;  \n    obj['event'] = 'forecast' ; \n    obj['createdBy'] = this.user.email ; \n    obj['currentDate'] = istDate ; \n      // startDate: this.dateCorrection(this.forecastForm.get('startDate').value),\n      // endDate: this.dateCorrection(this.forecastForm.get('endDate').value)\n    this.api.forecastData(obj).subscribe({\n      next: (res) => {\n        if (res.result === 'success') {\n          this.refreshForecast() ;\n          this.notify.snackBarShowSuccess('Submitted Successfully');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      },\n    });\n  }\n\n  updateModule() {    \n    let module = this.moduleForm.value;        \n    let obj: any = {\n      accessPermission:{\n      inventory: {\n        status: module.inventoryStatusButton === 'Yes',\n        access: module.inventory_Access\n      },\n      user: {\n        status: module.userStatusButton === 'Yes',\n        access: module.user_Access\n      },\n      recipe: {\n        status: module.recipeStatusButton === 'Yes',\n        access: module.recipe_Access\n      },\n      party: {\n        status: module.partyStatusButton === 'Yes',\n        access: module.party_Access\n      }\n    },\n      tenantId: this.user.tenantId,\n    };\n    this.api.updateConfigAccess(obj).subscribe({\n      next: (res) => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }\n\n  updateEmail() {\n    let emailValues = this.emailForm.value;\n    let obj: any = {\n        purchaseRequest: emailValues.purchaseRequestButton === 'Yes',\n        postGrnStatus: emailValues.postGRNStatusButton === 'Yes',\n        indentApproval: emailValues.indentApprovalButton === 'Yes',\n        purchaseApproval: emailValues.purchaseApprovalButton === 'Yes',\n        purchaseOrder: emailValues.purchaseOrderButton === 'Yes',\n        piApproval: emailValues.piApprovalButton === 'Yes',\n        // reportFailed: emailValues.reportFailedButton === 'Yes',\n        // systemErrorLog: emailValues.errorLogButton === 'Yes',\n        // approved: emailValues.approvedButton === 'Yes',\n        // rejected: emailValues.rejectedButton === 'Yes',\n        // report: emailValues.reportButton === 'Yes',\n        reportFailed: false,\n        systemErrorLog: false,\n        approved: false,\n        rejected: false,\n        report: false,     \n        tenantId: this.user.tenantId,\n    };\n    this.api.updateConfigAccess(obj).subscribe({\n      next: (res) => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }  \n\n  getBackendName(reportAccess: string): string {\n    const report = this.responseReport.find(report => report.displayName === reportAccess);\n    return report ? report.backendName : '';\n  }\n\n  updateReport(reportBased = true) {\n    let report = this.reportForm.value;\n    let backendName = this.getBackendName(report.reportAccess);\n    let obj: any = {}\n    if (reportBased) {\n      obj = {\n        tenantId: this.user.tenantId,\n        reportName : backendName,\n        access: report.report_Access,\n        reportBased: true,\n      };\n    } else {\n      obj = {\n        tenantId: this.user.tenantId,\n        role: this.roleForm.get('report_Access').value,\n        reports: this.roleForm.get('reportAccess').value,\n        reportBased: false,\n      };\n    }\n    this.api.updateReport(obj).subscribe({\n      next: (res) => {\n        if (res.success) {\n          this.updatedReportAccess[report.reportAccess] = report.report_Access; \n          this.getReportData();\n          reportBased ? this.roleForm.patchValue({\n            report_Access: undefined\n          }) : undefined\n          this.notify.snackBarShowSuccess('Updated Successfully');\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }\n\n  updatePermissions(formType: string, formGroup: FormGroup, showDeleteField: boolean, showEditField: boolean, showCloseField: boolean, showPartialIndentField?: boolean) {\n    let deleteRole = formGroup.value[`delete_${formType}`] || [];\n    let editRole = formGroup.value[`edit_${formType}`] || []; \n    let closeRole = formGroup.value[`close_${formType}`] || []; \n    let obj: any = {\n      delete: showDeleteField,\n      edit: showEditField,\n      close: showCloseField,\n      formType: formGroup.value.formType,\n      tenantId: this.user.tenantId,\n      deleteAccess: Array.from(new Set(deleteRole)),\n      editAccess: Array.from(new Set(editRole)),\n      closeAccess: Array.from(new Set(closeRole))\n    };      \n    if (formGroup.contains('partialButtonIndent')) {\n      obj.partial = formGroup.value['partialButtonIndent'] === 'Yes';\n    }\n    this.api.updateConfigAccess(obj).subscribe({\n      next: (res) => {\n        if (res.success) {\n          this.notify.snackBarShowSuccess('Updated Successfully');\n          this.getRoles();\n          this.getAccess();\n        } else {\n          this.notify.snackBarShowError('Something went wrong!');\n        }\n      },\n      error: (err) => {\n        console.log(err);\n      }\n    });\n  }\n\n  inventoryStatusShown(event: any) {\n    this.showInventoryStatusField = event.value === 'Yes';\n  }\n\n  userStatusShown(event: any) {\n    this.showUserStatusField = event.value === 'Yes';\n  }\n\n  recipeStatusShown(event: any) {\n    this.showRecipeStatusField = event.value === 'Yes';\n  }\n\n  partyStatusShown(event: any) {\n    this.showPartyStatusField = event.value === 'Yes';\n  }\n\n  isDeleteGrnShown(event: any) {\n    this.showDeleteGRNField = event.value === 'Yes';\n  }\n\n  isEditGrnShown(event: any) {\n    this.showEditGRNField = event.value === 'Yes';\n  }\n\n  isCloseGrnShown(event: any) {\n    this.showCloseGRNField = event.value === 'Yes';\n  }\n\n  isDeletePoShown(event: any) {\n    this.showDeletePOField = event.value === 'Yes';\n  }\n\n  isEditPoShown(event: any) {\n    this.showEditPOField = event.value === 'Yes';\n  }\n\n  isClosePoShown(event: any) {\n    this.showClosePOField = event.value === 'Yes';\n  }\n\n  isDeleteIndentShown(event: any) {\n    this.showDeleteIndentField = event.value === 'Yes';\n  }\n\n  isCloseIndentShown(event: any) {\n    this.showCloseIndentField = event.value === 'Yes';\n  }\n\n  onLocationSubmit(): void {\n  }\n\n\n  isPartialIndentShown(event: any) {\n    this.showPartialIndentField = event.value === 'Yes';\n  }  \n \n   onDateSelectionChange(value: string | null): void {\n    if (value === 'startOfMonth') {\n      this.monthlyClosingDates.forEach(month => {\n        this.startingDates[month.month] = this.getStartOfMonthDate(month);\n      });\n    } else {\n      this.startingDates = {}; \n    }\n    this.selectedDateOption = value; \n  }\n\n  goBackToLocationSelection(): void {\n    this.selectedDateOption = null; \n    this.showLocationSelection = true; \n  }\n\n\n  getMonthlyClosingDates(): { month: string, dates: Date[] }[] {\n    return [\n      { month: 'January', dates: [new Date()] },\n      { month: 'February', dates: [new Date()] },\n      { month: 'March', dates: [new Date()] },\n      { month: 'April', dates: [new Date()] },\n      { month: 'May', dates: [new Date()] },\n      { month: 'June', dates: [new Date()] },\n      { month: 'July', dates: [new Date()] },\n      { month: 'August', dates: [new Date()] },\n      { month: 'September', dates: [new Date()] },\n      { month: 'October', dates: [new Date()] },\n      { month: 'November', dates: [new Date()] },\n      { month: 'December', dates: [new Date()] }\n    ];\n  }\n  onLocationChange(event: any): void {\n    this.selectedLocation = event.value;\n    this.showLocationSelection = false; \n  }\n\nsubmit(): void {\n  if (!this.selectedLocation) {\n    alert('Please select a location.');\n    return; \n  }\n\n  const closingDetails: any = {};\n\n  if (this.pickDateOption === 'no') {\n    closingDetails[this.selectedLocation] = {\n      status: false,\n      selectedClosingDates: {}\n    };\n  } else if (this.pickDateOption === 'yes' && this.selectedDateOption === 'startOfMonth') {\n    closingDetails[this.selectedLocation] = {\n      status: true,\n      selectedClosingDates: {}\n    };\n\n    this.monthlyClosingDates.forEach(month => {\n      const startOfMonthDate = this.getStartOfMonthDate(month).toISOString().split('T')[0];\n      closingDetails[this.selectedLocation].selectedClosingDates[month.month.toLowerCase()] = startOfMonthDate;\n    });\n  } else {\n    closingDetails[this.selectedLocation] = {\n      status: true,\n      selectedClosingDates: {}\n    };\n  }\n\n  const payload = {\n    location: this.selectedLocation,\n    closingDetails: closingDetails\n  };\n  const headers = new HttpHeaders({\n    'accept': 'application/json',\n    'Content-Type': 'application/json'\n  });\n\n  this.http.post(`${this.engineUrl}master_data/update-closing-dates`, payload, { headers })\n  .subscribe(\n    response => {\n      this.notify.snackBarShowSuccess('Closing dates have been successfully updated.');\n    },\n    error => {\n      console.error('Error:', error);\n      this.notify.snackBarShowError('Failed to update closing dates. Please try again.');\n    }\n  );\n    \n}\ncloseMessage(): void {\n  this.submissionMessage = null;\n}\n  getStartOfMonthDate(month: any): Date {\n   \n    const monthIndex = new Date(Date.parse(month.month +\" 2, 2024\")).getMonth(); // Get the month index\n    return new Date(2024, monthIndex, 2); \n  }\n  isFinalStep(): boolean {\n    if (!this.selectedLocation) {\n      return false;\n    }\n  \n    if (this.pickDateOption === 'no') {\n      return true;\n    }\n  \n    return this.pickDateOption === 'yes' && !!this.selectedDateOption;\n  }\n\n  getJobStatus(element) {\n    return element.error \n      ? 'Something Went Wrong' \n      : element.pssi \n        ? 'Completed' \n        : 'In-Progress';\n  }\n  \n}\n", "<div class=\"main-content\">\n  <div class=\"container-fluid\">\n    <div class=\"row\">\n      <div class=\"col-md-12\">\n        <div class=\"card\">\n          <div class=\"card-header card-header-danger\">\n            <div class=\"header-content\">\n              <mat-icon class=\"icon\">settings</mat-icon>\n              <h3 class=\"mat-headline-6 title\">Profile Setting</h3>\n            </div>\n          </div>\n          <div class=\"card-body\">\n            <mat-tab-group mat-stretch-tabs=\"false\" #tabGroup mat-align-tabs=\"start\" (selectedTabChange)=\"onTabChange($event)\">\n              <mat-tab label=\"PERSONAL INFO\">\n                <mat-card appearance=\"outlined\">\n                  <mat-card-content class=\"mat-card-content\">\n                    <br>\n                      <mat-list>\n                        <mat-list-item>\n                          <div class=\"list-item-content\">\n                            <mat-icon>person</mat-icon>\n                            <div>\n                              <span class=\"bold\">Name: </span>\n                              <span>{{user.name}}</span>\n                            </div>\n                          </div>\n                        </mat-list-item>\n                        <mat-list-item>\n                          <div class=\"list-item-content\">\n                            <mat-icon>bookmark</mat-icon>\n                            <div>\n                              <span class=\"bold\">Role: </span>\n                              <span>{{user.role}}</span>\n                            </div>\n                          </div>\n                        </mat-list-item>\n                        <mat-list-item>\n                          <div class=\"list-item-content\">\n                            <mat-icon>alternate_email</mat-icon>\n                            <div>\n                              <span class=\"bold\">Email: </span>\n                              <span>{{user.email}}</span>\n                            </div>\n                          </div>\n                        </mat-list-item>\n                      </mat-list>\n                  </mat-card-content>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"IP CONFIG\">\n                <mat-card appearance=\"outlined\">\n                  <mat-card-content class=\"mat-card-content\">\n                    <br>\n                    <div class=\"col\">\n                      <label class=\"ipText\">Do you want to enable IP restriction ?</label>\n                      <mat-radio-group [(ngModel)]=\"IpType\" (change)=\"radioChange()\">\n                        <mat-radio-button value=\"withIp\">Yes</mat-radio-button>\n                        <mat-radio-button value=\"withoutIp\">No</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <form (ngSubmit)=\"onSubmit()\" class=\"my-4 mx-auto\" *ngIf=\"showIpProcess\">\n                      <div class=\"mb-3\">\n                        <mat-card-title> Your current IP -<strong> {{currentIp}}</strong> </mat-card-title>\n                        <mat-card-title class=\"mt-3\"> <strong>Choose an option </strong> </mat-card-title>\n                        <div class=\"row mt-3\">\n                          <div class=\"col-md-4\">\n                            <div class=\"form-check\">\n                              <input class=\"form-check-input\" type=\"radio\" name=\"option\" id=\"option1\" value=\"individual\"\n                                [(ngModel)]=\"selectedOption\">\n                              <label class=\"form-check-label fw-normal cursor-pointer\" for=\"option1\">This IP address or\n                                subnet</label>\n                            </div>\n                          </div>\n                          <div class=\"col-md-4\">\n                            <div class=\"form-check\">\n                              <input class=\"form-check-input\" type=\"radio\" name=\"option\" id=\"option2\" value=\"range\"\n                                [(ngModel)]=\"selectedOption\">\n                              <label class=\"form-check-label fw-normal cursor-pointer\" for=\"option2\">This IP address\n                                range</label>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div *ngIf=\"selectedOption === 'individual'\" class=\"mb-3\">\n                        <label for=\"individual\" class=\"form-label\">This IP address or subnet:</label>\n                        <input type=\"text\" id=\"individual\" name=\"individual\" [(ngModel)]=\"ipOrSubnet\"\n                          class=\"form-control\" (ngModelChange)=\"validateInput()\">\n                        <div *ngIf=\"!isIPValid\">\n                          <p class=\"error-message\">Invalid IP address</p>\n                        </div>\n                      </div>\n\n                      <div *ngIf=\"selectedOption === 'range'\" class=\"row mb-3 mt-3\">\n                        <div class=\"col-md-6\">\n                          <label for=\"startIP\" class=\"form-label\">From:</label>\n                          <input type=\"text\" id=\"startIP\" name=\"startIP\" [(ngModel)]=\"startIP\" class=\"form-control\"\n                            (ngModelChange)=\"validateInput()\">\n                          <div *ngIf=\"!ipPattern.test(this.startIP)\">\n                            <p class=\"error-message\">Invalid IP address</p>\n                          </div>\n                        </div>\n                        <div class=\"col-md-6\">\n                          <label for=\"endIP\" class=\"form-label\">To:</label>\n                          <input type=\"text\" id=\"endIP\" name=\"endIP\" [(ngModel)]=\"endIP\" class=\"form-control\"\n                            (ngModelChange)=\"validateInput()\">\n                          <div *ngIf=\"!ipPattern.test(this.endIP)\">\n                            <p class=\"error-message\">Invalid IP address</p>\n                          </div>\n                        </div>\n                      </div>\n                      <button type=\"submit\" class=\"btn btn-primary float-end mb-3\" [disabled]=\"!isIPValid\"\n                        matTooltip=\"Invalid IP Address\"\n                        *ngIf=\"selectedOption === 'individual' || selectedOption === 'range'\">Submit</button>\n                    </form>\n                    <div *ngIf=\"!showIpProcess\">\n                      <button type=\"submit\" class=\"btn btn-primary float-end mb-3\" matTooltip=\"Invalid IP Address\"\n                        (click)=\"onSubmit()\">Submit</button>\n                    </div>\n                  </mat-card-content>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"SALES\">\n                <mat-card appearance=\"outlined\">\n                  <mat-card-content class=\"mat-card-content\">\n                    <br>\n                    <form (ngSubmit)=\"addConfig()\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                      <!-- <div class=\"row mb-3 mt-3\">\n                        <div class=\"col-md-6\">\n                          <label class=\"form-label\">Profit Target(%)</label>\n                          <input type=\"number\" name=\"Profit Target\" [(ngModel)]=\"profitTarget\" class=\"form-control\"\n                            (ngModelChange)=\"validateSales()\" min=\"1\">\n                        </div>\n                      </div> -->\n                      <div *ngIf=\"salesLocation\" class=\"row mb-3 mt-3\">\n                        <div class=\"col-md-6\">\n                          <mat-form-field appearance=\"outline\" class=\"custom-form-field\">\n                            <mat-label>Location</mat-label>\n                            <mat-select [formControl]=\"branchData\" (selectionChange)=\"getPriceTires()\" panelClass=\"custom-dropdown\">\n                              <!-- <mat-label class=\"center-label\"*ngIf=\"showPlaceholderLabel\">Search ....</mat-label>\n                              <input matInput [formControl]=\"vendorFilterCtrl\" #inputField (keydown.space)=\"$event.stopPropagation()\" (input)=\"togglePlaceholderLabel(inputField.value)\"style=\"z-index: 1; padding-left: 10px;\"> -->\n                              <mat-option *ngFor=\"let option of filteredBranches\" [value]=\"option.restaurantIdOld\">\n                                {{ option.branchName | uppercase}}\n                              </mat-option>\n                            </mat-select>\n                          </mat-form-field>\n                        </div>\n                      </div>\n\n                      <div  class=\"row mb-3 mt-3\">\n                        <div class=\"col-md-6\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Price Tier</mat-label>\n                            <mat-select ng [formControl]=\"priceData\">\n                              <mat-option *ngFor=\"let option of priceTierList\" [value]=\"option\" >\n                                {{option['name'] | titlecase}}\n                              </mat-option>\n                            </mat-select>\n                          </mat-form-field>\n                        </div>\n                      </div>\n                      <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                    </form>\n                  </mat-card-content>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"ACCESS\" >\n                <mat-card appearance=\"outlined\">\n                  <mat-card-content class=\"mat-card-content\">\n                    <br>\n                    <form (ngSubmit)=\"submitPermission()\" [formGroup]=\"accessForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                        <div>\n                          <h4>\n                            Setting\n                          </h4>\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Roles for setting</mat-label>\n                            <mat-select formControlName=\"selectedSettingRoles\" [(ngModel)]=\"selectedSettingRoles\" multiple (ngModelChange)=\"validateSales()\">\n                              <mat-option *ngFor=\"let option of roles\" [value]=\"option\" [disabled]=\"option === 'superadmin'\">\n                                {{option | titlecase}}\n                              </mat-option>\n                            </mat-select>\n                          </mat-form-field>\n                        </div>\n\n                        <div>\n                          <h4>\n                            Excel Upload\n                          </h4>\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Roles for Excel Upload</mat-label>\n                            <mat-select formControlName=\"selectedExcelRoles\" [(ngModel)]=\"selectedExcelRoles\" multiple (ngModelChange)=\"validateSales()\">\n                              <mat-option *ngFor=\"let option of roles\" [value]=\"option\" [disabled]=\"option === 'superadmin'\">\n                                {{option | titlecase}}\n                              </mat-option>\n                            </mat-select>\n                          </mat-form-field>\n                        </div>\n                      <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                    </form>\n\n                  </mat-card-content>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"PERMISSION\">\n                <mat-card appearance=\"outlined\">\n                  <mat-tab-group>\n                    <mat-tab label=\"Module Access\">\n                      <mat-card-content class=\"mat-card-content\">\n                        <form (ngSubmit)=\"updateModule()\" [formGroup]=\"moduleForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                          <h1><b>Inventory Management</b></h1>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Inventory Status</label>\n                            <mat-radio-group formControlName=\"inventoryStatusButton\" class=\"col\" (change)=\"inventoryStatusShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showInventoryStatusField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Inventory Access</mat-label>\n                              <mat-select formControlName=\"inventory_Access\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"inventoryFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllInventory()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of inventory | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <br>\n                          <h1><b>User Management</b></h1>\n                          <div class=\"col\">\n                            <label class=\"ipText\">User Status</label>\n                            <mat-radio-group formControlName=\"userStatusButton\" class=\"col\" (change)=\"userStatusShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showUserStatusField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>User Access</mat-label>\n                              <mat-select formControlName=\"user_Access\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"userFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllUser()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of users | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <br>\n                          <h1><b>Recipe Management</b></h1>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Recipe Status</label>\n                            <mat-radio-group formControlName=\"recipeStatusButton\" class=\"col\" (change)=\"recipeStatusShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showRecipeStatusField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Recipe Access</mat-label>\n                              <mat-select formControlName=\"recipe_Access\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"recipeFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllRecipe()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of recipe | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <br>\n                          <h1><b>Party Management</b></h1>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Party Status</label>\n                            <mat-radio-group formControlName=\"partyStatusButton\" class=\"col\" (change)=\"partyStatusShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showPartyStatusField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Party Access</mat-label>\n                              <mat-select formControlName=\"party_Access\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"partyFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllParty()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of party | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                    <mat-tab label=\"GRN Access\">\n                      <mat-card-content class=\"mat-card-content\">\n                        <form (ngSubmit)=\"updateGRN()\" [formGroup]=\"grnForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                          <div class=\"col\">\n                            <label class=\"ipText\">Delete</label>\n                            <mat-radio-group formControlName=\"deleteButtonGRN\" class=\"col\" (change)=\"isDeleteGrnShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showDeleteGRNField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Delete Access</mat-label>\n                              <mat-select formControlName=\"delete_GRN\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"deleteGRNFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllDeleteGRN()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of deleteGRN | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Edit</label>\n                            <mat-radio-group formControlName=\"editButtonGRN\" class=\"col\" (change)=\"isEditGrnShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showEditGRNField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Edit Access</mat-label>\n                              <mat-select formControlName=\"edit_GRN\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"editGRNFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllEditGRN()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of editGRN | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n\n                          <div class=\"col\">\n                            <label class=\"ipText\">RTV</label>\n                            <mat-radio-group formControlName=\"closeButtonGRN\" class=\"col\" (change)=\"isCloseGrnShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showCloseGRNField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>RTV Access</mat-label>\n                              <mat-select formControlName=\"close_GRN\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"closeGRNFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllCloseGRN()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of closeGRN | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                    <mat-tab label=\"PO Access\">\n                      <mat-card-content class=\"mat-card-content\">\n                        <form (ngSubmit)=\"updatePO()\" [formGroup]=\"poForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                          <div class=\"col\">\n                            <label class=\"ipText\">Delete</label>\n                            <mat-radio-group formControlName=\"deleteButtonPO\" class=\"col\" (change)=\"isDeletePoShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showDeletePOField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Delete Access</mat-label>\n                              <mat-select formControlName=\"delete_PO\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"deletePOFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllDeletePO()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of deletePO | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Edit</label>\n                            <mat-radio-group formControlName=\"editButtonPO\" class=\"col\" (change)=\"isEditPoShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showEditPOField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Edit Access</mat-label>\n                              <mat-select formControlName=\"edit_PO\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"editPOFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllEditPO()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of editPO | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n\n                          <div class=\"col\">\n                            <label class=\"ipText\">Close</label>\n                            <mat-radio-group formControlName=\"closeButtonPO\" class=\"col\" (change)=\"isClosePoShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showClosePOField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Close Access</mat-label>\n                              <mat-select formControlName=\"close_PO\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"closePOFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllClosePO()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of closePO | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n\n                          <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                    <mat-tab label=\"Indent Access\">\n                      <mat-card-content class=\"mat-card-content\">\n                        <form (ngSubmit)=\"updateIndent()\" [formGroup]=\"indentForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                          <div class=\"col\">\n                            <label class=\"ipText\">Delete</label>\n                            <mat-radio-group formControlName=\"deleteButtonIndent\" class=\"col\" (change)=\"isDeleteIndentShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showDeleteIndentField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Delete Access</mat-label>\n                              <mat-select formControlName=\"delete_Indent\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"deleteIndentFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllDeleteIndent()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of deleteIndent | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                  <!-- do not remove -->\n                          <!-- <div class=\"col\">\n                            <label class=\"ipText\">Edit</label>\n                            <mat-radio-group formControlName=\"editButtonIndent\" class=\"col\" (change)=\"isEditIndentShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showEditIndentField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Edit Access</mat-label>\n                              <mat-select formControlName=\"edit_Indent\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"editIndentFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllEditIndent()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of editIndent | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>                  -->\n                          <div class=\"col\">\n                            <label class=\"ipText\">Close</label>\n                            <mat-radio-group formControlName=\"closeButtonIndent\" class=\"col\" (change)=\"isCloseIndentShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div *ngIf=\"showCloseIndentField\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Close Access</mat-label>\n                              <mat-select formControlName=\"close_Indent\" multiple (ngModelChange)=\"validateSales()\">\n                                <mat-option>\n                                  <ngx-mat-select-search [formControl]=\"closeIndentFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                </mat-option>\n                                <mat-option class=\"hide-checkbox\" (click)=\"toggleSelectAllCloseIndent()\">\n                                  <mat-icon matSuffix>check_circle</mat-icon>\n                                  Select All / Deselect All\n                                </mat-option>\n                                <mat-option *ngFor=\"let option of closeIndent | async\" [value]=\"option\">\n                                  {{option | titlecase}}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                          <div class=\"col\">\n                            <label class=\"ipText\">Partial Indent</label>\n                            <mat-radio-group formControlName=\"partialButtonIndent\" class=\"col\" (change)=\"isPartialIndentShown($event)\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                    <mat-tab label=\"Email Access\">\n                      <mat-card-content class=\"mat-card-content\">\n                        <form (ngSubmit)=\"updateEmail()\" [formGroup]=\"emailForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Purchase Request</label>\n                            <mat-radio-group formControlName=\"purchaseRequestButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Post Grn Status</label>\n                            <mat-radio-group formControlName=\"postGRNStatusButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Indent Approval</label>\n                            <mat-radio-group formControlName=\"indentApprovalButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Purchase Approval</label>\n                            <mat-radio-group formControlName=\"purchaseApprovalButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Purchase Order</label>\n                            <mat-radio-group formControlName=\"purchaseOrderButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <!-- <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Report Failed</label>\n                            <mat-radio-group formControlName=\"reportFailedButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">System Error Log</label>\n                            <mat-radio-group formControlName=\"errorLogButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Approved</label>\n                            <mat-radio-group formControlName=\"approvedButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Rejected</label>\n                            <mat-radio-group formControlName=\"rejectedButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br>\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">Report</label>\n                            <mat-radio-group formControlName=\"reportButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <br> -->\n                          <div class=\"col\">\n                            <label class=\"emailAccessIpText\">PI Approval</label>\n                            <mat-radio-group formControlName=\"piApprovalButton\" class=\"col\">\n                              <mat-radio-button value=\"Yes\">Yes</mat-radio-button>\n                              <mat-radio-button value=\"No\">No</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                          <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                    <mat-tab label=\"Report Access\">\n                      <mat-tab-group class=\"tabGroup\" >\n                        <mat-tab label=\"Report Based\">\n                          <mat-card-content class=\"mat-card-content\">\n                            <form (ngSubmit)=\"updateReport()\" [formGroup]=\"reportForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                              <mat-form-field appearance=\"outline\">\n                                <mat-label>Select Report</mat-label>\n                                <mat-select formControlName=\"reportAccess\" (ngModelChange)=\"reportChange($event)\">\n                                  <mat-option>\n                                    <ngx-mat-select-search [formControl]=\"reportFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                  </mat-option>\n                                  <mat-option *ngFor=\"let option of report | async\" [value]=\"option\">\n                                    {{option | titlecase}}\n                                  </mat-option>\n                                </mat-select>\n                              </mat-form-field>\n\n                              <mat-accordion multi=\"true\">\n                                <mat-expansion-panel *ngFor=\"let report of filteredReports\">\n                                  <mat-expansion-panel-header>\n                                    <mat-panel-title>\n                                      {{ report.displayName | uppercase }} REPORT\n                                    </mat-panel-title>\n                                  </mat-expansion-panel-header>\n                                  <mat-form-field appearance=\"outline\">\n                                    <mat-label>Select Roles</mat-label>\n                                    <mat-select formControlName=\"report_Access\" multiple >\n                                      <mat-option>\n                                        <ngx-mat-select-search [formControl]=\"report_FilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                      </mat-option>\n                                      <mat-option class=\"hide-checkbox\" (click)=\"selectAllRolesForReport()\">\n                                        <mat-icon matSuffix>check_circle</mat-icon>\n                                        Select All / Deselect All\n                                      </mat-option>\n                                      <mat-option *ngFor=\"let option of reports | async\" [value]=\"option\">\n                                        {{option | titlecase}}\n                                      </mat-option>\n                                    </mat-select>\n                                  </mat-form-field>\n                                </mat-expansion-panel>\n                              </mat-accordion>\n                              <br>\n                              <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                            </form>\n                          </mat-card-content>\n                        </mat-tab>\n\n                        <mat-tab label=\"Role Based\">\n                          <mat-card-content class=\"mat-card-content\">\n                            <form (ngSubmit)=\"updateReport(false)\" [formGroup]=\"roleForm\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                              <mat-form-field appearance=\"outline\">\n                                <mat-label>Select Role</mat-label>\n                                <mat-select formControlName=\"report_Access\" (ngModelChange)=\"roleChange($event)\">\n                                  <mat-option>\n                                    <ngx-mat-select-search [formControl]=\"report_FilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                  </mat-option>\n                                  <mat-option *ngFor=\"let option of reports | async\" [value]=\"option\">\n                                    {{option | titlecase}}\n                                  </mat-option>\n                                </mat-select>\n                              </mat-form-field>\n\n                              <mat-accordion multi=\"true\" *ngIf=\"roleForm.get('report_Access').value\">\n                                <mat-expansion-panel>\n                                  <mat-expansion-panel-header>\n                                    <mat-panel-title>\n                                      {{ filteredRole | uppercase }}\n                                    </mat-panel-title>\n                                  </mat-expansion-panel-header>\n                                  <mat-form-field appearance=\"outline\">\n                                    <mat-label>Select Report</mat-label>\n                                    <mat-select formControlName=\"reportAccess\" multiple >\n                                      <mat-option>\n                                        <ngx-mat-select-search [formControl]=\"reportFilterCtrl\" placeholderLabel=\"Search...\"></ngx-mat-select-search>\n                                      </mat-option>\n                                      <mat-option class=\"hide-checkbox\" (click)=\"selectAllReportsForRoles()\">\n                                        <mat-icon matSuffix>check_circle</mat-icon>\n                                        Select All / Deselect All\n                                      </mat-option>\n                                      <mat-option *ngFor=\"let option of report | async\" [value]=\"option\">\n                                        {{option | titlecase}}\n                                      </mat-option>\n                                    </mat-select>\n                                  </mat-form-field>\n                                </mat-expansion-panel>\n                              </mat-accordion>\n                              <br>\n                              <button type=\"submit\" class=\"btn btn-primary float-end mb-3\">Submit</button>\n                            </form>\n                          </mat-card-content>\n                        </mat-tab>\n                      </mat-tab-group>\n                    </mat-tab>\n\n                  </mat-tab-group>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"FREEZE CLOSING OPERATIONS\">\n                <mat-card appearance=\"outlined\">\n                  <mat-card-content class=\"mat-card-content\">\n                    <form (ngSubmit)=\"submit()\" class=\"my-4 mx-auto\" style=\"max-width: 500px;\">\n                      <!-- Location selection -->\n                      <div>\n                        <div class=\"row mb-3\">\n                          <div class=\"col-md-6\">\n                            <mat-form-field appearance=\"outline\" class=\"custom-form-field\">\n                              <mat-label>Select a Location</mat-label>\n\n                              <mat-select [formControl]=\"branchData\" (selectionChange)=\"onLocationChange($event)\" panelClass=\"custom-dropdown\">\n                                <mat-label class=\"center-label\" *ngIf=\"showPlaceholderLabel\">Search ....</mat-label>\n                                <input matInput [formControl]=\"vendorFilterCtrl\" #inputField (keydown.space)=\"$event.stopPropagation()\" (input)=\"togglePlaceholderLabel(inputField.value)\" style=\"z-index: 1; padding-left: 10px;\">\n                                <mat-option *ngFor=\"let option of filteredBranches\" [value]=\"option.restaurantIdOld\">\n                                  {{ option.branchName | uppercase }}\n                                </mat-option>\n                              </mat-select>\n                            </mat-form-field>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Date option selection -->\n                      <div *ngIf=\"selectedLocation\">\n                        <div class=\"col mb-3\">\n                          <label class=\"ipText\">Do you want to pick a date?</label>\n                          <mat-radio-group [(ngModel)]=\"pickDateOption\" name=\"pickDateOption\">\n                            <mat-radio-button value=\"yes\">Yes</mat-radio-button>\n                            <mat-radio-button value=\"no\">No</mat-radio-button>\n                          </mat-radio-group>\n                        </div>\n                      </div>\n\n                      <!-- Monthly closing date dropdown -->\n                      <div *ngIf=\"pickDateOption === 'yes'\" class=\"mb-3\">\n                        <label for=\"monthlyDate\" class=\"form-label\">Select a monthly closing date:</label>\n                        <mat-form-field appearance=\"outline\" class=\"custom-form-field\">\n                          <mat-label>Monthly Date</mat-label>\n                          <mat-select [(ngModel)]=\"selectedDateOption\" name=\"monthlyDate\" (selectionChange)=\"onDateSelectionChange($event.value)\">\n                            <mat-option [value]=\"null\">--Select--</mat-option>\n                            <mat-option value=\"startOfMonth\">Starting of the Month</mat-option>\n                          </mat-select>\n                        </mat-form-field>\n                      </div>\n\n                      <!-- Submit button only visible at final step -->\n                      <div *ngIf=\"isFinalStep()\" class=\"btn btn-primary float-end mb-3\">\n                        <button type=\"submit\" class=\"btn btn-primary float-end\">Submit</button>\n                            </div>\n                          </form>\n                        </mat-card-content>\n                </mat-card>\n              </mat-tab>\n              <mat-tab label=\"RETRIGGER\">\n                <mat-card appearance=\"outlined\">\n                  <mat-tab-group class=\"tabGroup\" (selectedTabChange)=\"tabChange($event)\">\n\n                    <mat-tab label=\"SALES\">\n                      <mat-card-content class=\"cardContent\">\n                        <button (click)=\"refreshSales()\" mat-raised-button color=\"warn\" class=\"refresh end\">Refresh</button>\n                        <form (ngSubmit)=\"salesRerun()\" [formGroup]=\"salesForm\">\n                          <div>\n                            <mat-form-field appearance=\"outline\" class=\"datePicker\">\n                              <mat-label>Start Date</mat-label>\n                              <input matInput [matDatepicker]=\"startPicker\" formControlName=\"startDate\">\n                              <mat-datepicker-toggle matIconSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n                              <mat-datepicker touchUi #startPicker></mat-datepicker>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\" class=\"datePicker\">\n                              <mat-label>End Date</mat-label>\n                              <input matInput [matDatepicker]=\"endPicker\" formControlName=\"endDate\"\n                              [disabled]=\"!salesForm.get('startDate').value\" [min]=\"salesForm.get('startDate').value\">\n                              <mat-datepicker-toggle matIconSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n                              <mat-datepicker touchUi #endPicker></mat-datepicker>\n                            </mat-form-field>\n\n                            <button type=\"submit\" mat-raised-button color=\"primary\" class=\"refresh\">Submit</button>\n                          </div>\n\n                          <div class=\"section\" #section #widgetsContent>\n                            <div class=\"tableDiv\" *ngIf=\"isSalesDataReady\">\n                                <mat-table [dataSource]=\"dataSourceSales\" matSort>\n\n                                  <ng-container matColumnDef=\"position\">\n                                    <mat-header-cell *matHeaderCellDef class=\"tableSnoCol\"> S.No </mat-header-cell>\n                                    <mat-cell *matCellDef=\"let element; let i = index;\" class=\"tableSnoCol\"> {{i+1}} </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"createdDate\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Sales Date </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\"> {{element.createTs | date: 'dd-MM-yyyy'}} </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"createdTime\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Created At </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      <ng-container *ngIf=\"element.triggeredTs || element.modTs; else noDate\">\n                                        {{ element.triggeredTs ? (element.triggeredTs | date: 'dd-MM-yyyy') : (element.modTs | date: 'dd-MM-yyyy') }}\n                                        {{ element.triggeredTs ? convertToIST(element.triggeredTs) : convertToIST(element.modTs) }}\n                                      </ng-container>\n                                      <ng-template #noDate> - </ng-template>\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"status\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Status </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      {{ getJobStatus(element) }}\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <mat-header-row *matHeaderRowDef=\"salesColumns\"></mat-header-row>\n                                  <mat-row *matRowDef=\"let row; columns: salesColumns;\"\n                                    [ngClass]=\"{'highlighted-row': row.Discontinued === 'yes'}\"></mat-row>\n                                </mat-table>\n                            </div>\n\n                            <div *ngIf=\"!isSalesDataReady\">\n                              <ngx-skeleton-loader count=\"5\" animation=\"progress-dark\"\n                                [theme]=\"{ 'border-radius': '5px', height: '30px' }\"></ngx-skeleton-loader>\n                            </div>\n\n                            <div class=\"text-center m-3\" *ngIf=\"dataSourceSales.data.length == 0 && isSalesDataReady\">\n                              Data Not Found\n                            </div>\n                            <mat-paginator class=\"mat-paginator-sticky\" [pageSizeOptions]=\"[5, 10, 25, 50, 100]\" #salesPaginator></mat-paginator>\n                          </div>\n\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n                    <mat-tab label=\"WAC\">\n                      <mat-card-content class=\"cardContent\">\n                        <button (click)=\"refreshWac()\" mat-raised-button color=\"warn\" class=\"refresh float-end gap\">Refresh</button>\n                        <form (ngSubmit)=\"weightedAvg()\" [formGroup]=\"wacForm\">\n                          <button type=\"submit\" mat-raised-button color=\"primary\" class=\"refresh float-end gap\">Submit</button>\n                          <div class=\"section\" #section #widgetsContent>\n                            <div class=\"tableDiv\" *ngIf=\"isWacDataReady\">\n                                <mat-table [dataSource]=\"dataSourceWeightedAvg\" matSort >\n                                  <ng-container matColumnDef=\"position\">\n                                    <mat-header-cell *matHeaderCellDef class=\"tableSnoCol\"> S.No </mat-header-cell>\n                                    <mat-cell *matCellDef=\"let element; let i = index;\" class=\"tableSnoCol\"> {{i+1}} </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"createdTime\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Created At </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      {{ element.createTs  | date: 'dd-MM-yyyy' }}\n                                      {{ convertToIST(element.createTs) }}\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"status\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Status </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      {{ getJobStatus(element) }}\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <mat-header-row *matHeaderRowDef=\"weightedAvgColumns\"></mat-header-row>\n                                  <mat-row *matRowDef=\"let row; columns: weightedAvgColumns;\"\n                                    [ngClass]=\"{'highlighted-row': row.Discontinued === 'yes'}\"></mat-row>\n                                </mat-table>\n                            </div>\n\n                            <div *ngIf=\"!isWacDataReady\">\n                              <ngx-skeleton-loader count=\"5\" animation=\"progress-dark\"\n                                [theme]=\"{ 'border-radius': '5px', height: '30px' }\"></ngx-skeleton-loader>\n                            </div>\n\n                            <div class=\"text-center m-3\" *ngIf=\"dataSourceWeightedAvg.data.length == 0 && isWacDataReady\">\n                              Data Not Found\n                            </div>\n                            <mat-paginator class=\"mat-paginator-sticky\" [pageSizeOptions]=\"[5, 10, 25, 50, 100]\" #wacPaginator></mat-paginator>\n                          </div>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n                    <mat-tab label=\"FORECAST\">\n                      <mat-card-content class=\"cardContent\">\n                        <button (click)=\"refreshForecast()\" mat-raised-button color=\"warn\" class=\"refresh float-end gap\">Refresh</button>\n                        <form (ngSubmit)=\"forecast()\" [formGroup]=\"forecastForm\">\n                          <!-- <div>\n                            <mat-form-field appearance=\"outline\" class=\"datePicker\">\n                              <mat-label>Start Date</mat-label>\n                              <input matInput [matDatepicker]=\"startPickerForecast\" formControlName=\"startDate\">\n                              <mat-datepicker-toggle matIconSuffix [for]=\"startPickerForecast\"></mat-datepicker-toggle>\n                              <mat-datepicker touchUi #startPickerForecast></mat-datepicker>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\" class=\"datePicker\">\n                              <mat-label>End Date</mat-label>\n                              <input matInput [matDatepicker]=\"endPickerForecast\" formControlName=\"endDate\"\n                              [disabled]=\"!forecastForm.get('startDate').value\" [min]=\"forecastForm.get('startDate').value\">\n                              <mat-datepicker-toggle matIconSuffix [for]=\"endPickerForecast\"></mat-datepicker-toggle>\n                              <mat-datepicker touchUi #endPickerForecast></mat-datepicker>\n                            </mat-form-field>\n\n                            <button type=\"submit\" mat-raised-button disabled color=\"primary\" class=\"refresh\">Submit</button>\n                            <button (click)=\"refreshForecast()\" mat-raised-button color=\"warn\" class=\"refresh end\">Refresh</button>\n                          </div> -->\n\n                          <button type=\"submit\" mat-raised-button color=\"primary\" class=\"refresh float-end gap\">Submit</button>\n\n                          <div class=\"section\" #section #widgetsContent>\n                            <div class=\"tableDiv\" *ngIf=\"isForecastReady\">\n                                <mat-table [dataSource]=\"dataSourceForecast\" matSort >\n                                  <ng-container matColumnDef=\"position\">\n                                    <mat-header-cell *matHeaderCellDef class=\"tableSnoCol\"> S.No </mat-header-cell>\n                                    <mat-cell *matCellDef=\"let element; let i = index;\" class=\"tableSnoCol\"> {{i+1}} </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"createdTime\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Created At </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      {{ element.createTs  | date: 'dd-MM-yyyy' }}\n                                      {{ convertToIST(element.createTs) }}\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <ng-container matColumnDef=\"status\">\n                                    <mat-header-cell class=\"custom-header\" *matHeaderCellDef> Status </mat-header-cell>\n                                    <mat-cell class=\"custom-cell\" *matCellDef=\"let element\">\n                                      {{ getJobStatus(element) }}\n                                    </mat-cell>\n                                  </ng-container>\n\n                                  <mat-header-row *matHeaderRowDef=\"forecastColumns\"></mat-header-row>\n                                  <mat-row *matRowDef=\"let row; columns: forecastColumns;\"\n                                    [ngClass]=\"{'highlighted-row': row.Discontinued === 'yes'}\"></mat-row>\n                                </mat-table>\n                            </div>\n\n                            <div *ngIf=\"!isForecastReady\">\n                              <ngx-skeleton-loader count=\"5\" animation=\"progress-dark\"\n                                [theme]=\"{ 'border-radius': '5px', height: '30px' }\"></ngx-skeleton-loader>\n                            </div>\n\n                            <div class=\"text-center m-3\" *ngIf=\"dataSourceForecast.data.length == 0 && isForecastReady\">\n                              Data Not Found\n                            </div>\n                            <mat-paginator class=\"mat-paginator-sticky\" [pageSizeOptions]=\"[5, 10, 25, 50, 100]\" #forecastPaginator></mat-paginator>\n                          </div>\n                        </form>\n                      </mat-card-content>\n                    </mat-tab>\n\n                  </mat-tab-group>\n                </mat-card>\n              </mat-tab>\n            </mat-tab-group>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<ng-template #showDialog>\n  <div class=\"registration-form m-3 py-2 px-3\">\n    <div>\n      <div class=\"row my-3\">\n        <div class=\"form-check col-6 justify-content-center\">\n          <input class=\"form-check-input\" type=\"radio\" name=\"option\" id=\"option1\" value=\"withoutIp\"\n            [(ngModel)]=\"IpType\">\n          <label class=\"form-check-label fw-normal cursor-pointer\" for=\"option1\">use Without IP</label>\n        </div>\n        <div class=\"form-check col-6 justify-content-center\">\n          <input class=\"form-check-input\" type=\"radio\" name=\"option\" id=\"option2\" value=\"withIp\" [(ngModel)]=\"IpType\">\n          <label class=\"form-check-label fw-normal cursor-pointer\" for=\"option2\">Use With IP</label>\n        </div>\n      </div>\n    </div>\n    <div class=\"d-flex justify-content-end\">\n      <button mat-raised-button color=\"accent\" matTooltip=\"update\" (click)=\"submitIp()\"> Ok </button>\n    </div>\n  </div>\n</ng-template>"], "mappings": "AAAA,SAAwFA,MAAM,QAAgB,eAAe;AAC7H,SAASC,YAAY,QAAoB,iBAAiB;AAC1D,SAAqBC,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAAqBC,aAAa,EAAEC,OAAO,EAASC,GAAG,EAAaC,SAAS,QAAQ,MAAM;AAC3F,SAASC,YAAY,EAAEC,oBAAoB,QAAoB,gBAAgB;AAE/E,SAAyCC,aAAa,QAAQ,wBAAwB;AACtF,SAAuCC,WAAW,EAAYC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClI,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,eAAe,QAAQ,0BAA0B;AAG1D,SAASC,cAAc,QAAQ,yBAAyB;AAGxD,SAAoBC,eAAe,QAAsB,0BAA0B;AAGnF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AAEjE,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAAuBC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,wBAAwB;AACvF,SAASC,+BAA+B,EAAEC,iBAAiB,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICkD7EC,EAAA,CAAAC,cAAA,UAAwB;IACGD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IALnDH,EAAA,CAAAC,cAAA,eAA0D;IACbD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7EH,EAAA,CAAAC,cAAA,iBACyD;IADJD,EAAA,CAAAI,UAAA,2BAAAC,iFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,OAAA,CAAAG,UAAA,GAAAN,MAAA;IAAA,EAAwB,2BAAAD,iFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAK,OAAA,GAAAb,EAAA,CAAAU,aAAA;MAAA,OACrCV,EAAA,CAAAW,WAAA,CAAAE,OAAA,CAAAC,aAAA,EAAe;IAAA,EADsB;IAA7Ed,EAAA,CAAAG,YAAA,EACyD;IACzDH,EAAA,CAAAe,UAAA,IAAAC,uDAAA,kBAEM;IACRhB,EAAA,CAAAG,YAAA,EAAM;;;;IALiDH,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAkB,UAAA,YAAAC,OAAA,CAAAP,UAAA,CAAwB;IAEvEZ,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,UAAAC,OAAA,CAAAC,SAAA,CAAgB;;;;;IAUpBpB,EAAA,CAAAC,cAAA,UAA2C;IAChBD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOjDH,EAAA,CAAAC,cAAA,UAAyC;IACdD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAdrDH,EAAA,CAAAC,cAAA,cAA8D;IAElBD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAC,cAAA,iBACoC;IADWD,EAAA,CAAAI,UAAA,2BAAAiB,iFAAAf,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAY,OAAA,CAAAC,OAAA,GAAAlB,MAAA;IAAA,EAAqB,2BAAAe,iFAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAG,OAAA,GAAAzB,EAAA,CAAAU,aAAA;MAAA,OACjDV,EAAA,CAAAW,WAAA,CAAAc,OAAA,CAAAX,aAAA,EAAe;IAAA,EADkC;IAApEd,EAAA,CAAAG,YAAA,EACoC;IACpCH,EAAA,CAAAe,UAAA,IAAAW,uDAAA,kBAEM;IACR1B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACkBD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjDH,EAAA,CAAAC,cAAA,iBACoC;IADOD,EAAA,CAAAI,UAAA,2BAAAuB,iFAAArB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAM,OAAA,GAAA5B,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiB,OAAA,CAAAC,KAAA,GAAAvB,MAAA;IAAA,EAAmB,2BAAAqB,iFAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAe,IAAA;MAAA,MAAAQ,OAAA,GAAA9B,EAAA,CAAAU,aAAA;MAAA,OAC3CV,EAAA,CAAAW,WAAA,CAAAmB,OAAA,CAAAhB,aAAA,EAAe;IAAA,EAD4B;IAA9Dd,EAAA,CAAAG,YAAA,EACoC;IACpCH,EAAA,CAAAe,UAAA,KAAAgB,wDAAA,kBAEM;IACR/B,EAAA,CAAAG,YAAA,EAAM;;;;IAb2CH,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAAkB,UAAA,YAAAc,OAAA,CAAAR,OAAA,CAAqB;IAE9DxB,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,UAAAc,OAAA,CAAAC,SAAA,CAAAC,IAAA,CAAAF,OAAA,CAAAR,OAAA,EAAmC;IAMExB,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,YAAAc,OAAA,CAAAH,KAAA,CAAmB;IAExD7B,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,UAAAc,OAAA,CAAAC,SAAA,CAAAC,IAAA,CAAAF,OAAA,CAAAH,KAAA,EAAiC;;;;;IAK3C7B,EAAA,CAAAC,cAAA,kBAEwE;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF1BH,EAAA,CAAAkB,UAAA,cAAAiB,OAAA,CAAAf,SAAA,CAAuB;;;;;;IAnDtFpB,EAAA,CAAAC,cAAA,gBAAyE;IAAnED,EAAA,CAAAI,UAAA,sBAAAgC,oEAAA;MAAApC,EAAA,CAAAO,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAU,aAAA;MAAA,OAAYV,EAAA,CAAAW,WAAA,CAAA2B,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC3BvC,EAAA,CAAAC,cAAA,eAAkB;IACCD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAACD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjEH,EAAA,CAAAC,cAAA,0BAA6B;IAASD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChEH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAI,UAAA,2BAAAoC,2EAAAlC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA8B,IAAA;MAAA,MAAAI,OAAA,GAAAzC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA8B,OAAA,CAAAC,cAAA,GAAApC,MAAA;IAAA,EAA4B;IAD9BN,EAAA,CAAAG,YAAA,EAC+B;IAC/BH,EAAA,CAAAC,cAAA,kBAAuE;IAAAD,EAAA,CAAAE,MAAA,iCAC/D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGpBH,EAAA,CAAAC,cAAA,gBAAsB;IAGhBD,EAAA,CAAAI,UAAA,2BAAAuC,2EAAArC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA8B,IAAA;MAAA,MAAAO,OAAA,GAAA5C,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiC,OAAA,CAAAF,cAAA,GAAApC,MAAA;IAAA,EAA4B;IAD9BN,EAAA,CAAAG,YAAA,EAC+B;IAC/BH,EAAA,CAAAC,cAAA,kBAAuE;IAAAD,EAAA,CAAAE,MAAA,6BAChE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAMvBH,EAAA,CAAAe,UAAA,KAAA8B,iDAAA,kBAOM;IAEN7C,EAAA,CAAAe,UAAA,KAAA+B,iDAAA,mBAiBM;IACN9C,EAAA,CAAAe,UAAA,KAAAgC,oDAAA,sBAEuF;IACzF/C,EAAA,CAAAG,YAAA,EAAO;;;;IApDwCH,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAgD,kBAAA,MAAAC,MAAA,CAAAC,SAAA,KAAa;IAMhDlD,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,UAAA,YAAA+B,MAAA,CAAAP,cAAA,CAA4B;IAQ5B1C,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,UAAA,YAAA+B,MAAA,CAAAP,cAAA,CAA4B;IAQhC1C,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAkB,UAAA,SAAA+B,MAAA,CAAAP,cAAA,kBAAqC;IASrC1C,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,SAAA+B,MAAA,CAAAP,cAAA,aAAgC;IAoBnC1C,EAAA,CAAAiB,SAAA,GAAmE;IAAnEjB,EAAA,CAAAkB,UAAA,SAAA+B,MAAA,CAAAP,cAAA,qBAAAO,MAAA,CAAAP,cAAA,aAAmE;;;;;;IAExE1C,EAAA,CAAAC,cAAA,UAA4B;IAExBD,EAAA,CAAAI,UAAA,mBAAA+C,kEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA6C,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA0C,OAAA,CAAAd,QAAA,EAAU;IAAA,EAAC;IAACvC,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAwB9BH,EAAA,CAAAC,cAAA,sBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAkB,UAAA,UAAAoC,UAAA,CAAAC,eAAA,CAAgC;IAClFvD,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAF,UAAA,CAAAG,UAAA,OACF;;;;;;IATRzD,EAAA,CAAAC,cAAA,cAAiD;IAGhCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAC,cAAA,qBAAwG;IAAjED,EAAA,CAAAI,UAAA,6BAAAsD,gFAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAoD,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAU,aAAA;MAAA,OAAmBV,EAAA,CAAAW,WAAA,CAAAiD,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGxE7D,EAAA,CAAAe,UAAA,IAAA+C,sDAAA,yBAEa;IACf9D,EAAA,CAAAG,YAAA,EAAa;;;;IANDH,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAkB,UAAA,gBAAA6C,MAAA,CAAAC,UAAA,CAA0B;IAGLhE,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,YAAA6C,MAAA,CAAAE,gBAAA,CAAmB;;;;;IAalDjE,EAAA,CAAAC,cAAA,sBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAAkB,UAAA,UAAAgD,UAAA,CAAgB;IAC/DlE,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAU,UAAA,eACF;;;;;IAsBAlE,EAAA,CAAAC,cAAA,sBAA+F;IAC7FD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAAkB,UAAA,UAAAiD,UAAA,CAAgB,aAAAA,UAAA;IACvDnE,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAW,UAAA,OACF;;;;;IAYAnE,EAAA,CAAAC,cAAA,sBAA+F;IAC7FD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAAkB,UAAA,UAAAkD,UAAA,CAAgB,aAAAA,UAAA;IACvDpE,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAY,UAAA,OACF;;;;;IAoCEpE,EAAA,CAAAC,cAAA,sBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAkB,UAAA,UAAAmD,UAAA,CAAgB;IACnErE,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAa,UAAA,OACF;;;;;;IAbNrE,EAAA,CAAAC,cAAA,UAAsC;IAEvBD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAC,cAAA,sBAA0F;IAAlCD,EAAA,CAAAI,UAAA,2BAAAkE,+EAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAA6D,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IACvFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAAgH;IAClH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAuE;IAArCD,EAAA,CAAAI,UAAA,mBAAAuE,uEAAA;MAAA3E,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAAK,OAAA,GAAA5E,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAiE,OAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IACpE7E,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA+D,wDAAA,yBAEa;;IACf9E,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,gBAAA6D,MAAA,CAAAC,mBAAA,CAAmC;IAM7BhF,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAuB,MAAA,CAAAE,SAAA,EAAoB;;;;;IA2BnDjF,EAAA,CAAAC,cAAA,sBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAAkB,UAAA,UAAAgE,UAAA,CAAgB;IAC/DlF,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA0B,UAAA,OACF;;;;;;IAbNlF,EAAA,CAAAC,cAAA,UAAiC;IAElBD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAC,cAAA,sBAAqF;IAAlCD,EAAA,CAAAI,UAAA,2BAAA+E,+EAAA;MAAAnF,EAAA,CAAAO,aAAA,CAAA6E,IAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAA0E,OAAA,CAAAZ,aAAA,EAAe;IAAA,EAAC;IAClFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA2G;IAC7G1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAkE;IAAhCD,EAAA,CAAAI,UAAA,mBAAAkF,uEAAA;MAAAtF,EAAA,CAAAO,aAAA,CAAA6E,IAAA;MAAA,MAAAG,OAAA,GAAAvF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA4E,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAC/DxF,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA0E,wDAAA,yBAEa;;IACfzF,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,gBAAAwE,MAAA,CAAAC,cAAA,CAA8B;IAMxB3F,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAkC,MAAA,CAAAE,KAAA,EAAgB;;;;;IA2B/C5F,EAAA,CAAAC,cAAA,sBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFqCH,EAAA,CAAAkB,UAAA,UAAA2E,UAAA,CAAgB;IAChE7F,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAqC,UAAA,OACF;;;;;;IAbN7F,EAAA,CAAAC,cAAA,UAAmC;IAEpBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,sBAAuF;IAAlCD,EAAA,CAAAI,UAAA,2BAAA0F,+EAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,IAAA;MAAA,MAAAC,OAAA,GAAAhG,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAqF,OAAA,CAAAvB,aAAA,EAAe;IAAA,EAAC;IACpFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA6G;IAC/G1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAoE;IAAlCD,EAAA,CAAAI,UAAA,mBAAA6F,uEAAA;MAAAjG,EAAA,CAAAO,aAAA,CAAAwF,IAAA;MAAA,MAAAG,OAAA,GAAAlG,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuF,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IACjEnG,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAqF,wDAAA,yBAEa;;IACfpG,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,gBAAAmF,MAAA,CAAAC,gBAAA,CAAgC;IAM1BtG,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAA6C,MAAA,CAAAE,MAAA,EAAiB;;;;;IA2BhDvG,EAAA,CAAAC,cAAA,sBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAAkB,UAAA,UAAAsF,UAAA,CAAgB;IAC/DxG,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAgD,UAAA,OACF;;;;;;IAbNxG,EAAA,CAAAC,cAAA,UAAkC;IAEnBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,sBAAsF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAqG,+EAAA;MAAAzG,EAAA,CAAAO,aAAA,CAAAmG,IAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAgG,OAAA,CAAAlC,aAAA,EAAe;IAAA,EAAC;IACnFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA4G;IAC9G1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAmE;IAAjCD,EAAA,CAAAI,UAAA,mBAAAwG,uEAAA;MAAA5G,EAAA,CAAAO,aAAA,CAAAmG,IAAA;MAAA,MAAAG,OAAA,GAAA7G,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAkG,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAChE9G,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAgG,wDAAA,yBAEa;;IACf/G,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAkB,UAAA,gBAAA8F,OAAA,CAAAC,eAAA,CAA+B;IAMzBjH,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAwD,OAAA,CAAAE,KAAA,EAAgB;;;;;IAiC/ClH,EAAA,CAAAC,cAAA,sBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAkB,UAAA,UAAAiG,UAAA,CAAgB;IACnEnH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA2D,UAAA,OACF;;;;;;IAbNnH,EAAA,CAAAC,cAAA,UAAgC;IAEjBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,sBAAoF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAgH,+EAAA;MAAApH,EAAA,CAAAO,aAAA,CAAA8G,KAAA;MAAA,MAAAC,QAAA,GAAAtH,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAA2G,QAAA,CAAA7C,aAAA,EAAe;IAAA,EAAC;IACjFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAAgH;IAClH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAuE;IAArCD,EAAA,CAAAI,UAAA,mBAAAmH,uEAAA;MAAAvH,EAAA,CAAAO,aAAA,CAAA8G,KAAA;MAAA,MAAAG,QAAA,GAAAxH,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA6G,QAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IACpEzH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA2G,wDAAA,yBAEa;;IACf1H,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,gBAAAyG,OAAA,CAAAC,mBAAA,CAAmC;IAM7B5H,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAmE,OAAA,CAAAE,SAAA,EAAoB;;;;;IAyBnD7H,EAAA,CAAAC,cAAA,sBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAkB,UAAA,UAAA4G,WAAA,CAAgB;IACjE9H,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAsE,WAAA,OACF;;;;;;IAbN9H,EAAA,CAAAC,cAAA,UAA8B;IAEfD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAC,cAAA,sBAAkF;IAAlCD,EAAA,CAAAI,UAAA,2BAAA2H,+EAAA;MAAA/H,EAAA,CAAAO,aAAA,CAAAyH,KAAA;MAAA,MAAAC,QAAA,GAAAjI,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAsH,QAAA,CAAAxD,aAAA,EAAe;IAAA,EAAC;IAC/EzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA8G;IAChH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAqE;IAAnCD,EAAA,CAAAI,UAAA,mBAAA8H,uEAAA;MAAAlI,EAAA,CAAAO,aAAA,CAAAyH,KAAA;MAAA,MAAAG,QAAA,GAAAnI,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAwH,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAClEpI,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAsH,wDAAA,yBAEa;;IACfrI,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,gBAAAoH,OAAA,CAAAC,iBAAA,CAAiC;IAM3BvI,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAA8E,OAAA,CAAAE,OAAA,EAAkB;;;;;IA0BjDxI,EAAA,CAAAC,cAAA,sBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAkB,UAAA,UAAAuH,WAAA,CAAgB;IAClEzI,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAiF,WAAA,OACF;;;;;;IAbNzI,EAAA,CAAAC,cAAA,UAA+B;IAEhBD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAC,cAAA,sBAAmF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAsI,+EAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAoI,KAAA;MAAA,MAAAC,QAAA,GAAA5I,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAiI,QAAA,CAAAnE,aAAA,EAAe;IAAA,EAAC;IAChFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA+G;IACjH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAsE;IAApCD,EAAA,CAAAI,UAAA,mBAAAyI,uEAAA;MAAA7I,EAAA,CAAAO,aAAA,CAAAoI,KAAA;MAAA,MAAAG,QAAA,GAAA9I,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAmI,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACnE/I,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAiI,wDAAA,yBAEa;;IACfhJ,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAkC;IAAlCjB,EAAA,CAAAkB,UAAA,gBAAA+H,OAAA,CAAAC,kBAAA,CAAkC;IAM5BlJ,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAyF,OAAA,CAAAE,QAAA,EAAmB;;;;;IAiClDnJ,EAAA,CAAAC,cAAA,sBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAkB,UAAA,UAAAkI,WAAA,CAAgB;IAClEpJ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA4F,WAAA,OACF;;;;;;IAbNpJ,EAAA,CAAAC,cAAA,UAA+B;IAEhBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,sBAAmF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAiJ,+EAAA;MAAArJ,EAAA,CAAAO,aAAA,CAAA+I,KAAA;MAAA,MAAAC,QAAA,GAAAvJ,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAA4I,QAAA,CAAA9E,aAAA,EAAe;IAAA,EAAC;IAChFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA+G;IACjH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAsE;IAApCD,EAAA,CAAAI,UAAA,mBAAAoJ,uEAAA;MAAAxJ,EAAA,CAAAO,aAAA,CAAA+I,KAAA;MAAA,MAAAG,QAAA,GAAAzJ,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA8I,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACnE1J,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA4I,wDAAA,yBAEa;;IACf3J,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAkC;IAAlCjB,EAAA,CAAAkB,UAAA,gBAAA0I,OAAA,CAAAC,kBAAA,CAAkC;IAM5B7J,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAoG,OAAA,CAAAE,QAAA,EAAmB;;;;;IAyBlD9J,EAAA,CAAAC,cAAA,sBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFqCH,EAAA,CAAAkB,UAAA,UAAA6I,WAAA,CAAgB;IAChE/J,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAuG,WAAA,OACF;;;;;;IAbN/J,EAAA,CAAAC,cAAA,UAA6B;IAEdD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAC,cAAA,sBAAiF;IAAlCD,EAAA,CAAAI,UAAA,2BAAA4J,+EAAA;MAAAhK,EAAA,CAAAO,aAAA,CAAA0J,KAAA;MAAA,MAAAC,QAAA,GAAAlK,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAuJ,QAAA,CAAAzF,aAAA,EAAe;IAAA,EAAC;IAC9EzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA6G;IAC/G1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAoE;IAAlCD,EAAA,CAAAI,UAAA,mBAAA+J,uEAAA;MAAAnK,EAAA,CAAAO,aAAA,CAAA0J,KAAA;MAAA,MAAAG,QAAA,GAAApK,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAyJ,QAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IACjErK,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAuJ,wDAAA,yBAEa;;IACftK,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,gBAAAqJ,OAAA,CAAAC,gBAAA,CAAgC;IAM1BxK,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAA+G,OAAA,CAAAE,MAAA,EAAiB;;;;;IA0BhDzK,EAAA,CAAAC,cAAA,sBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAkB,UAAA,UAAAwJ,WAAA,CAAgB;IACjE1K,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAkH,WAAA,OACF;;;;;;IAbN1K,EAAA,CAAAC,cAAA,UAA8B;IAEfD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,sBAAkF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAuK,+EAAA;MAAA3K,EAAA,CAAAO,aAAA,CAAAqK,KAAA;MAAA,MAAAC,QAAA,GAAA7K,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAkK,QAAA,CAAApG,aAAA,EAAe;IAAA,EAAC;IAC/EzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAA8G;IAChH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAqE;IAAnCD,EAAA,CAAAI,UAAA,mBAAA0K,uEAAA;MAAA9K,EAAA,CAAAO,aAAA,CAAAqK,KAAA;MAAA,MAAAG,QAAA,GAAA/K,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAoK,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAClEhL,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAkK,wDAAA,yBAEa;;IACfjL,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,gBAAAgK,OAAA,CAAAC,iBAAA,CAAiC;IAM3BnL,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAA0H,OAAA,CAAAE,OAAA,EAAkB;;;;;IAkCjDpL,EAAA,CAAAC,cAAA,sBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF2CH,EAAA,CAAAkB,UAAA,UAAAmK,WAAA,CAAgB;IACtErL,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA6H,WAAA,OACF;;;;;;IAbNrL,EAAA,CAAAC,cAAA,UAAmC;IAEpBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,sBAAuF;IAAlCD,EAAA,CAAAI,UAAA,2BAAAkL,+EAAA;MAAAtL,EAAA,CAAAO,aAAA,CAAAgL,KAAA;MAAA,MAAAC,QAAA,GAAAxL,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAA6K,QAAA,CAAA/G,aAAA,EAAe;IAAA,EAAC;IACpFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAAmH;IACrH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAA0E;IAAxCD,EAAA,CAAAI,UAAA,mBAAAqL,uEAAA;MAAAzL,EAAA,CAAAO,aAAA,CAAAgL,KAAA;MAAA,MAAAG,QAAA,GAAA1L,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+K,QAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IACvE3L,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA6K,wDAAA,yBAEa;;IACf5L,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,gBAAA2K,OAAA,CAAAC,sBAAA,CAAsC;IAMhC9L,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAqI,OAAA,CAAAE,YAAA,EAAuB;;;;;IAmDtD/L,EAAA,CAAAC,cAAA,sBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0CH,EAAA,CAAAkB,UAAA,UAAA8K,WAAA,CAAgB;IACrEhM,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAwI,WAAA,OACF;;;;;;IAbNhM,EAAA,CAAAC,cAAA,UAAkC;IAEnBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,sBAAsF;IAAlCD,EAAA,CAAAI,UAAA,2BAAA6L,+EAAA;MAAAjM,EAAA,CAAAO,aAAA,CAAA2L,KAAA;MAAA,MAAAC,QAAA,GAAAnM,EAAA,CAAAU,aAAA;MAAA,OAAiBV,EAAA,CAAAW,WAAA,CAAAwL,QAAA,CAAA1H,aAAA,EAAe;IAAA,EAAC;IACnFzE,EAAA,CAAAC,cAAA,iBAAY;IACVD,EAAA,CAAA0E,SAAA,gCAAkH;IACpH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAyE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAgM,uEAAA;MAAApM,EAAA,CAAAO,aAAA,CAAA2L,KAAA;MAAA,MAAAG,QAAA,GAAArM,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA0L,QAAA,CAAAC,0BAAA,EAA4B;IAAA,EAAC;IACtEtM,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAwL,wDAAA,yBAEa;;IACfvM,EAAA,CAAAG,YAAA,EAAa;;;;IATcH,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAkB,UAAA,gBAAAsL,OAAA,CAAAC,qBAAA,CAAqC;IAM/BzM,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAgJ,OAAA,CAAAE,WAAA,EAAsB;;;;;IA4HnD1M,EAAA,CAAAC,cAAA,sBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFqCH,EAAA,CAAAkB,UAAA,UAAAyL,WAAA,CAAgB;IAChE3M,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAmJ,WAAA,OACF;;;;;IAqBI3M,EAAA,CAAAC,cAAA,sBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAkB,UAAA,UAAA0L,WAAA,CAAgB;IACjE5M,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAoJ,WAAA,OACF;;;;;;IAlBN5M,EAAA,CAAAC,cAAA,0BAA4D;IAGtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAEpBH,EAAA,CAAAC,cAAA,yBAAqC;IACxBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,sBAAsD;IAElDD,EAAA,CAAA0E,SAAA,iCAA8G;IAChH1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,uBAAsE;IAApCD,EAAA,CAAAI,UAAA,mBAAAyM,wFAAA;MAAA7M,EAAA,CAAAO,aAAA,CAAAuM,KAAA;MAAA,MAAAC,QAAA,GAAA/M,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAoM,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACnEhN,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAAkM,wEAAA,yBAEa;;IACfjN,EAAA,CAAAG,YAAA,EAAa;;;;;IAhBXH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA0J,WAAA,CAAAC,WAAA,cACF;IAM2BnN,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,gBAAAkM,OAAA,CAAAC,iBAAA,CAAiC;IAM3BrN,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAA4J,OAAA,CAAAE,OAAA,EAAkB;;;;;IAsBrDtN,EAAA,CAAAC,cAAA,sBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAkB,UAAA,UAAAqM,WAAA,CAAgB;IACjEvN,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA+J,WAAA,OACF;;;;;IAqBIvN,EAAA,CAAAC,cAAA,sBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFqCH,EAAA,CAAAkB,UAAA,UAAAsM,WAAA,CAAgB;IAChExN,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAgK,WAAA,OACF;;;;;;IAnBRxN,EAAA,CAAAC,cAAA,wBAAwE;IAIhED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAEpBH,EAAA,CAAAC,cAAA,yBAAqC;IACxBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,sBAAqD;IAEjDD,EAAA,CAAA0E,SAAA,iCAA6G;IAC/G1E,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,uBAAuE;IAArCD,EAAA,CAAAI,UAAA,mBAAAqN,kFAAA;MAAAzN,EAAA,CAAAO,aAAA,CAAAmN,KAAA;MAAA,MAAAC,QAAA,GAAA3N,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgN,QAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IACpE5N,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAe,UAAA,KAAA8M,kEAAA,yBAEa;;IACf7N,EAAA,CAAAG,YAAA,EAAa;;;;IAhBXH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAAsK,OAAA,CAAAC,YAAA,OACF;IAM2B/N,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,gBAAA4M,OAAA,CAAAE,gBAAA,CAAgC;IAM1BhO,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,QAAAsK,OAAA,CAAAG,MAAA,EAAiB;;;;;IA8BtDjO,EAAA,CAAAC,cAAA,qBAA6D;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAEpFH,EAAA,CAAAC,cAAA,sBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAkB,UAAA,UAAAgN,WAAA,CAAA3K,eAAA,CAAgC;IAClFvD,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAwD,WAAA,OAAA0K,WAAA,CAAAzK,UAAA,OACF;;;;;;IAQVzD,EAAA,CAAAC,cAAA,UAA8B;IAEJD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAC,cAAA,2BAAoE;IAAnDD,EAAA,CAAAI,UAAA,2BAAA+N,oFAAA7N,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA6N,KAAA;MAAA,MAAAC,QAAA,GAAArO,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA0N,QAAA,CAAAC,cAAA,GAAAhO,MAAA;IAAA,EAA4B;IAC3CN,EAAA,CAAAC,cAAA,4BAA8B;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IACpDH,EAAA,CAAAC,cAAA,4BAA6B;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAmB;;;;IAFnCH,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,UAAA,YAAAqN,OAAA,CAAAD,cAAA,CAA4B;;;;;;IAQjDtO,EAAA,CAAAC,cAAA,eAAmD;IACLD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClFH,EAAA,CAAAC,cAAA,yBAA+D;IAClDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,sBAAwH;IAA5GD,EAAA,CAAAI,UAAA,2BAAAoO,+EAAAlO,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAkO,KAAA;MAAA,MAAAC,QAAA,GAAA1O,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA+N,QAAA,CAAAC,kBAAA,GAAArO,MAAA;IAAA,EAAgC,6BAAAsO,iFAAAtO,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAkO,KAAA;MAAA,MAAAI,QAAA,GAAA7O,EAAA,CAAAU,aAAA;MAAA,OAAuCV,EAAA,CAAAW,WAAA,CAAAkO,QAAA,CAAAC,qBAAA,CAAAxO,MAAA,CAAAyO,KAAA,CAAmC;IAAA,EAA1E;IAC1C/O,EAAA,CAAAC,cAAA,sBAA2B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAClDH,EAAA,CAAAC,cAAA,sBAAiC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFzDH,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,YAAA8N,OAAA,CAAAL,kBAAA,CAAgC;IAC9B3O,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,eAAc;;;;;IAOhClB,EAAA,CAAAC,cAAA,eAAkE;IACRD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAsC3DH,EAAA,CAAAC,cAAA,2BAAuD;IAACD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAC/EH,EAAA,CAAAC,cAAA,oBAAwE;IAACD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAnBH,EAAA,CAAAiB,SAAA,GAAQ;IAARjB,EAAA,CAAAgD,kBAAA,MAAAiM,MAAA,UAAQ;;;;;IAIjFjP,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACvFH,EAAA,CAAAC,cAAA,oBAAwD;IAACD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAArDH,EAAA,CAAAiB,SAAA,GAA0C;IAA1CjB,EAAA,CAAAgD,kBAAA,MAAAhD,EAAA,CAAAkP,WAAA,OAAAC,YAAA,CAAAC,QAAA,qBAA0C;;;;;IAInGpP,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAErFH,EAAA,CAAAqP,uBAAA,GAAwE;IACtErP,EAAA,CAAAE,MAAA,GAEF;;;IAAAF,EAAA,CAAAsP,qBAAA,EAAe;;;;;IAFbtP,EAAA,CAAAiB,SAAA,GAEF;IAFEjB,EAAA,CAAAuP,kBAAA,MAAAC,YAAA,CAAAC,WAAA,GAAAzP,EAAA,CAAAkP,WAAA,OAAAM,YAAA,CAAAC,WAAA,kBAAAzP,EAAA,CAAAkP,WAAA,OAAAM,YAAA,CAAAE,KAAA,sBAAAF,YAAA,CAAAC,WAAA,GAAAE,QAAA,CAAAC,YAAA,CAAAJ,YAAA,CAAAC,WAAA,IAAAE,QAAA,CAAAC,YAAA,CAAAJ,YAAA,CAAAE,KAAA,OAEF;;;;;IACsB1P,EAAA,CAAAE,MAAA,UAAE;;;;;IAL1BF,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAe,UAAA,IAAA8O,qEAAA,4BAGe;IACf7P,EAAA,CAAAe,UAAA,IAAA+O,oEAAA,kCAAA9P,EAAA,CAAA+P,sBAAA,CAAsC;IACxC/P,EAAA,CAAAG,YAAA,EAAW;;;;;IALMH,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAkB,UAAA,SAAAsO,YAAA,CAAAC,WAAA,IAAAD,YAAA,CAAAE,KAAA,CAA4C,aAAAM,KAAA;;;;;IAS7DhQ,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACnFH,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADTH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAiN,QAAA,CAAAC,YAAA,CAAAC,YAAA,OACF;;;;;IAGFnQ,EAAA,CAAA0E,SAAA,qBAAiE;;;;;;;;;;IACjE1E,EAAA,CAAA0E,SAAA,mBACwE;;;;IAAtE1E,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoQ,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,YAAA,YAA2D;;;;;IAjCnEvQ,EAAA,CAAAC,cAAA,eAA+C;IAGzCD,EAAA,CAAAqP,uBAAA,QAAsC;IACpCrP,EAAA,CAAAe,UAAA,IAAAyP,4DAAA,+BAA+E;IAC/ExQ,EAAA,CAAAe,UAAA,IAAA0P,qDAAA,wBAA4F;IAC9FzQ,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAyC;IACvCrP,EAAA,CAAAe,UAAA,IAAA2P,4DAAA,+BAAuF;IACvF1Q,EAAA,CAAAe,UAAA,IAAA4P,qDAAA,wBAA8G;IAChH3Q,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAyC;IACvCrP,EAAA,CAAAe,UAAA,IAAA6P,4DAAA,+BAAuF;IACvF5Q,EAAA,CAAAe,UAAA,KAAA8P,sDAAA,wBAMW;IACb7Q,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,SAAoC;IAClCrP,EAAA,CAAAe,UAAA,KAAA+P,6DAAA,+BAAmF;IACnF9Q,EAAA,CAAAe,UAAA,KAAAgQ,sDAAA,wBAEW;IACb/Q,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAe,UAAA,KAAAiQ,4DAAA,8BAAiE;IACjEhR,EAAA,CAAAe,UAAA,KAAAkQ,qDAAA,uBACwE;IAC1EjR,EAAA,CAAAG,YAAA,EAAY;;;;IAjCDH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,eAAAgQ,OAAA,CAAAC,eAAA,CAA8B;IA8BtBnR,EAAA,CAAAiB,SAAA,IAA6B;IAA7BjB,EAAA,CAAAkB,UAAA,oBAAAgQ,OAAA,CAAAE,YAAA,CAA6B;IAChBpR,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,UAAA,qBAAAgQ,OAAA,CAAAE,YAAA,CAAsB;;;;;;;;;;;IAK1DpR,EAAA,CAAAC,cAAA,UAA+B;IAC7BD,EAAA,CAAA0E,SAAA,+BAC6E;IAC/E1E,EAAA,CAAAG,YAAA,EAAM;;;IADFH,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAkB,UAAA,UAAAlB,EAAA,CAAAqR,eAAA,IAAAC,GAAA,EAAoD;;;;;IAGxDtR,EAAA,CAAAC,cAAA,eAA0F;IACxFD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBEH,EAAA,CAAAC,cAAA,2BAAuD;IAACD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAC/EH,EAAA,CAAAC,cAAA,oBAAwE;IAACD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAnBH,EAAA,CAAAiB,SAAA,GAAQ;IAARjB,EAAA,CAAAgD,kBAAA,MAAAuO,MAAA,UAAQ;;;;;IAIjFvR,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACvFH,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GAEF;;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAFTH,EAAA,CAAAiB,SAAA,GAEF;IAFEjB,EAAA,CAAAuP,kBAAA,MAAAvP,EAAA,CAAAkP,WAAA,OAAAsC,YAAA,CAAApC,QAAA,sBAAAqC,QAAA,CAAA7B,YAAA,CAAA4B,YAAA,CAAApC,QAAA,OAEF;;;;;IAIApP,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACnFH,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADTH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAA0O,QAAA,CAAAxB,YAAA,CAAAyB,YAAA,OACF;;;;;IAGF3R,EAAA,CAAA0E,SAAA,qBAAuE;;;;;IACvE1E,EAAA,CAAA0E,SAAA,mBACwE;;;;IAAtE1E,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoQ,eAAA,IAAAC,GAAA,EAAAuB,QAAA,CAAArB,YAAA,YAA2D;;;;;IAxBnEvQ,EAAA,CAAAC,cAAA,eAA6C;IAEvCD,EAAA,CAAAqP,uBAAA,QAAsC;IACpCrP,EAAA,CAAAe,UAAA,IAAA8Q,4DAAA,+BAA+E;IAC/E7R,EAAA,CAAAe,UAAA,IAAA+Q,qDAAA,wBAA4F;IAC9F9R,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAyC;IACvCrP,EAAA,CAAAe,UAAA,IAAAgR,4DAAA,+BAAuF;IACvF/R,EAAA,CAAAe,UAAA,IAAAiR,qDAAA,wBAGW;IACbhS,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAoC;IAClCrP,EAAA,CAAAe,UAAA,IAAAkR,4DAAA,+BAAmF;IACnFjS,EAAA,CAAAe,UAAA,KAAAmR,sDAAA,wBAEW;IACblS,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAe,UAAA,KAAAoR,4DAAA,8BAAuE;IACvEnS,EAAA,CAAAe,UAAA,KAAAqR,qDAAA,uBACwE;IAC1EpS,EAAA,CAAAG,YAAA,EAAY;;;;IAxBDH,EAAA,CAAAiB,SAAA,GAAoC;IAApCjB,EAAA,CAAAkB,UAAA,eAAAmR,OAAA,CAAAC,qBAAA,CAAoC;IAqB5BtS,EAAA,CAAAiB,SAAA,IAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,oBAAAmR,OAAA,CAAAE,kBAAA,CAAmC;IACtBvS,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,UAAA,qBAAAmR,OAAA,CAAAE,kBAAA,CAA4B;;;;;IAKhEvS,EAAA,CAAAC,cAAA,UAA6B;IAC3BD,EAAA,CAAA0E,SAAA,+BAC6E;IAC/E1E,EAAA,CAAAG,YAAA,EAAM;;;IADFH,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAkB,UAAA,UAAAlB,EAAA,CAAAqR,eAAA,IAAAC,GAAA,EAAoD;;;;;IAGxDtR,EAAA,CAAAC,cAAA,eAA8F;IAC5FD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoCEH,EAAA,CAAAC,cAAA,2BAAuD;IAACD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAC/EH,EAAA,CAAAC,cAAA,oBAAwE;IAACD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAnBH,EAAA,CAAAiB,SAAA,GAAQ;IAARjB,EAAA,CAAAgD,kBAAA,MAAAwP,MAAA,UAAQ;;;;;IAIjFxS,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACvFH,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GAEF;;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAFTH,EAAA,CAAAiB,SAAA,GAEF;IAFEjB,EAAA,CAAAuP,kBAAA,MAAAvP,EAAA,CAAAkP,WAAA,OAAAuD,YAAA,CAAArD,QAAA,sBAAAsD,QAAA,CAAA9C,YAAA,CAAA6C,YAAA,CAAArD,QAAA,OAEF;;;;;IAIApP,EAAA,CAAAC,cAAA,2BAAyD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACnFH,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADTH,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAA2P,QAAA,CAAAzC,YAAA,CAAA0C,YAAA,OACF;;;;;IAGF5S,EAAA,CAAA0E,SAAA,qBAAoE;;;;;IACpE1E,EAAA,CAAA0E,SAAA,mBACwE;;;;IAAtE1E,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAoQ,eAAA,IAAAC,GAAA,EAAAwC,QAAA,CAAAtC,YAAA,YAA2D;;;;;IAxBnEvQ,EAAA,CAAAC,cAAA,eAA8C;IAExCD,EAAA,CAAAqP,uBAAA,QAAsC;IACpCrP,EAAA,CAAAe,UAAA,IAAA+R,4DAAA,+BAA+E;IAC/E9S,EAAA,CAAAe,UAAA,IAAAgS,qDAAA,wBAA4F;IAC9F/S,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAyC;IACvCrP,EAAA,CAAAe,UAAA,IAAAiS,4DAAA,+BAAuF;IACvFhT,EAAA,CAAAe,UAAA,IAAAkS,qDAAA,wBAGW;IACbjT,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAqP,uBAAA,QAAoC;IAClCrP,EAAA,CAAAe,UAAA,IAAAmS,4DAAA,+BAAmF;IACnFlT,EAAA,CAAAe,UAAA,KAAAoS,sDAAA,wBAEW;IACbnT,EAAA,CAAAsP,qBAAA,EAAe;IAEftP,EAAA,CAAAe,UAAA,KAAAqS,4DAAA,8BAAoE;IACpEpT,EAAA,CAAAe,UAAA,KAAAsS,qDAAA,uBACwE;IAC1ErT,EAAA,CAAAG,YAAA,EAAY;;;;IAxBDH,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,eAAAoS,OAAA,CAAAC,kBAAA,CAAiC;IAqBzBvT,EAAA,CAAAiB,SAAA,IAAgC;IAAhCjB,EAAA,CAAAkB,UAAA,oBAAAoS,OAAA,CAAAE,eAAA,CAAgC;IACnBxT,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAkB,UAAA,qBAAAoS,OAAA,CAAAE,eAAA,CAAyB;;;;;IAK7DxT,EAAA,CAAAC,cAAA,UAA8B;IAC5BD,EAAA,CAAA0E,SAAA,+BAC6E;IAC/E1E,EAAA,CAAAG,YAAA,EAAM;;;IADFH,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAkB,UAAA,UAAAlB,EAAA,CAAAqR,eAAA,IAAAC,GAAA,EAAoD;;;;;IAGxDtR,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAmBhCH,EAAA,CAAAC,cAAA,eAA6C;IAKnCD,EAAA,CAAAI,UAAA,2BAAAqT,kFAAAnT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAmT,KAAA;MAAA,MAAAC,QAAA,GAAA3T,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAgT,QAAA,CAAAC,MAAA,GAAAtT,MAAA;IAAA,EAAoB;IADtBN,EAAA,CAAAG,YAAA,EACuB;IACvBH,EAAA,CAAAC,cAAA,iBAAuE;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE/FH,EAAA,CAAAC,cAAA,eAAqD;IACoCD,EAAA,CAAAI,UAAA,2BAAAyT,kFAAAvT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAmT,KAAA;MAAA,MAAAI,QAAA,GAAA9T,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAmT,QAAA,CAAAF,MAAA,GAAAtT,MAAA;IAAA,EAAoB;IAA3GN,EAAA,CAAAG,YAAA,EAA4G;IAC5GH,EAAA,CAAAC,cAAA,iBAAuE;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIhGH,EAAA,CAAAC,cAAA,gBAAwC;IACuBD,EAAA,CAAAI,UAAA,mBAAA2T,4EAAA;MAAA/T,EAAA,CAAAO,aAAA,CAAAmT,KAAA;MAAA,MAAAM,QAAA,GAAAhU,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAqT,QAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAEjU,EAAA,CAAAE,MAAA,YAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAVzFH,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,YAAAgT,OAAA,CAAAN,MAAA,CAAoB;IAIiE5T,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,YAAAgT,OAAA,CAAAN,MAAA,CAAoB;;;;;;AD5+BrH,OAAO,MAAMO,eAAe,GAAG;EAC7BC,KAAK,EAAE;IACLC,SAAS,EAAE;GACZ;EACDC,OAAO,EAAE;IACPD,SAAS,EAAE,YAAY;IACvBE,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE,YAAY;IAC3BC,kBAAkB,EAAE;;CAEvB;AAED,MAsCaC,yBAAyB;EA2KlCC,YAAoBC,gBAAkC,EAC9CC,WAAwB,EACxBC,MAAc,EACdC,IAAgB,EAChBC,GAAqB,EACrBC,MAA2B,EAC3BC,EAAqB,EACrBC,UAA4B,EAC5BC,IAAiB,EACjBC,MAAiB,EACjBC,iBAAoC,EACpCC,MAAc,EACdC,EAAe;IAZH,KAAAZ,gBAAgB,GAAhBA,gBAAgB;IAC5B,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAtLZ,KAAAC,wBAAwB,GAAsC,EAAE;IAGhE,KAAAC,SAAS,GAAWjY,WAAW,CAACiY,SAAS;IAEzC;IACA,KAAAC,aAAa,GAA4B,EAAE;IAC3C,KAAAC,qBAAqB,GAAY,IAAI;IACrC,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAlH,kBAAkB,GAAkB,IAAI;IACxC,KAAAmH,mBAAmB,GAAU,EAAE;IAC/B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,YAAY,GAAmC,EAAE;IAEjD,KAAA3H,cAAc,GAAW,IAAI;IAC7B,KAAA4H,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,eAAe,GAAW,EAAE;IAMrB,KAAAC,UAAU,GAAG,KAAK;IAI1B,KAAAC,aAAa,GAAS,KAAK;IAC1B,KAAAC,kBAAkB,GAAGlZ,MAAM,CAACS,kBAAkB,CAAC;IAE/C,KAAAkE,SAAS,GAAW,IAAIwU,MAAM,CAAC,4FAA4F,CAAC;IAC5H,KAAA7V,UAAU,GAAW,EAAE;IACvB,KAAAY,OAAO,GAAW,EAAE;IACpB,KAAAK,KAAK,GAAW,EAAE;IAClB,KAAAa,cAAc,GAAW,YAAY;IACrC,KAAAgU,YAAY,GAAW,CAAC;IAExB,KAAAtV,SAAS,GAAa,KAAK;IAC3B,KAAAuV,SAAS,GAAG,IAAInY,WAAW,EAAE;IAC7B,KAAAwF,UAAU,GAAC,IAAIxF,WAAW,CAAC,IAAI,CAAC;IAChC,KAAAoV,MAAM,GAAW,WAAW;IAC5B,KAAAgD,aAAa,GAAa,KAAK;IAC/B,KAAAC,SAAS,GAAa,KAAK;IAC3B,KAAAC,gBAAgB,GAAa,KAAK;IAClC,KAAAC,cAAc,GAAa,KAAK;IAChC,KAAAC,eAAe,GAAa,KAAK;IACjC,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,EAAE;IASvB,KAAAC,oBAAoB,GAAU,IAAI;IAClC,KAAAC,KAAK,GAAa,EAAE;IACpB,KAAAC,WAAW,GAAa,EAAE;IAC3B,KAAAC,QAAQ,GAAa,EAAE;IACvB,KAAAC,OAAO,GAAa,EAAE;IAWrB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,sBAAsB,GAAG,KAAK;IAQ9B,KAAAC,eAAe,GAAU,EAAE;IAE3B,KAAAC,mBAAmB,GAA2B,EAAE;IAChD,KAAAhI,YAAY,GAAG,CAAC,UAAU,EAAE,aAAa,EAAC,QAAQ,EAAC,aAAa,CAAC;IACjE,KAAAD,eAAe,GAAG,IAAI5R,kBAAkB,CAAM,EAAE,CAAC;IACjD,KAAAgT,kBAAkB,GAAG,CAAC,UAAU,EAAC,QAAQ,EAAC,aAAa,CAAC;IACxD,KAAAD,qBAAqB,GAAG,IAAI/S,kBAAkB,CAAM,EAAE,CAAC;IACvD,KAAAiU,eAAe,GAAG,CAAC,UAAU,EAAC,QAAQ,EAAC,aAAa,CAAC;IACrD,KAAAD,kBAAkB,GAAG,IAAIhU,kBAAkB,CAAM,EAAE,CAAC;IAE7C,KAAA8Z,aAAa,GAAU,EAAE;IACzB,KAAArU,mBAAmB,GAAgB,IAAIxG,WAAW,EAAE;IACpD,KAAAyG,SAAS,GAAyB,IAAIhH,aAAa,CAAQ,CAAC,CAAC;IAE7D,KAAAqb,QAAQ,GAAU,EAAE;IACpB,KAAA3T,cAAc,GAAgB,IAAInH,WAAW,EAAE;IAC/C,KAAAoH,KAAK,GAAyB,IAAI3H,aAAa,CAAQ,CAAC,CAAC;IAEzD,KAAAsb,UAAU,GAAU,EAAE;IACtB,KAAAjT,gBAAgB,GAAgB,IAAI9H,WAAW,EAAE;IACjD,KAAA+H,MAAM,GAAyB,IAAItI,aAAa,CAAQ,CAAC,CAAC;IAE1D,KAAAub,SAAS,GAAU,EAAE;IACrB,KAAAvS,eAAe,GAAgB,IAAIzI,WAAW,EAAE;IAChD,KAAA0I,KAAK,GAAyB,IAAIjJ,aAAa,CAAQ,CAAC,CAAC;IAEzD,KAAAwb,OAAO,GAAU,EAAE;IACnB,KAAA7R,mBAAmB,GAAgB,IAAIpJ,WAAW,EAAE;IACpD,KAAAqJ,SAAS,GAAyB,IAAI5J,aAAa,CAAQ,CAAC,CAAC;IAC7D,KAAAsK,iBAAiB,GAAgB,IAAI/J,WAAW,EAAE;IAClD,KAAAgK,OAAO,GAAyB,IAAIvK,aAAa,CAAQ,CAAC,CAAC;IAC3D,KAAAiL,kBAAkB,GAAgB,IAAI1K,WAAW,EAAE;IACnD,KAAA2K,QAAQ,GAAyB,IAAIlL,aAAa,CAAQ,CAAC,CAAC;IAE5D,KAAAyb,MAAM,GAAU,EAAE;IAClB,KAAA7P,kBAAkB,GAAgB,IAAIrL,WAAW,EAAE;IACnD,KAAAsL,QAAQ,GAAyB,IAAI7L,aAAa,CAAQ,CAAC,CAAC;IAC5D,KAAAuM,gBAAgB,GAAgB,IAAIhM,WAAW,EAAE;IACjD,KAAAiM,MAAM,GAAyB,IAAIxM,aAAa,CAAQ,CAAC,CAAC;IAC1D,KAAAkN,iBAAiB,GAAgB,IAAI3M,WAAW,EAAE;IAClD,KAAA4M,OAAO,GAAyB,IAAInN,aAAa,CAAQ,CAAC,CAAC;IAE3D,KAAA0b,UAAU,GAAU,EAAE;IACtB,KAAA7N,sBAAsB,GAAgB,IAAItN,WAAW,EAAE;IACvD,KAAAuN,YAAY,GAAyB,IAAI9N,aAAa,CAAQ,CAAC,CAAC;IAChE,KAAA2b,oBAAoB,GAAgB,IAAIpb,WAAW,EAAE;IACrD,KAAAqb,UAAU,GAAyB,IAAI5b,aAAa,CAAQ,CAAC,CAAC;IAC9D,KAAAwO,qBAAqB,GAAgB,IAAIjO,WAAW,EAAE;IACtD,KAAAkO,WAAW,GAAyB,IAAIzO,aAAa,CAAQ,CAAC,CAAC;IAE/D,KAAA6b,UAAU,GAAU,EAAE;IACtB,KAAA9L,gBAAgB,GAAgB,IAAIxP,WAAW,EAAE;IACjD,KAAAyP,MAAM,GAAyB,IAAIhQ,aAAa,CAAQ,CAAC,CAAC;IAE1D,KAAA8b,WAAW,GAAU,EAAE;IACvB,KAAA1M,iBAAiB,GAAgB,IAAI7O,WAAW,EAAE;IAClD,KAAA8O,OAAO,GAAyB,IAAIrP,aAAa,CAAQ,CAAC,CAAC;IAExD,KAAA+b,UAAU,GAAG,IAAI9b,OAAO,EAAQ;IAoBtC,IAAI,CAAC+b,UAAU,GAAG,IAAI,CAACzE,EAAE,CAAC0E,KAAK,CAAC;MAC9BjD,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,kBAAkB,EAAE,CAAC,EAAE;KACxB,CAAC;IACF,IAAI,CAACiD,UAAU,GAAG,IAAI,CAAC3E,EAAE,CAAC0E,KAAK,CAAC;MAC9BE,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvB/C,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjB+C,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IACF,IAAI,CAACC,SAAS,GAAG,IAAI,CAAClF,EAAE,CAAC0E,KAAK,CAAC;MAC7BS,SAAS,EAAE,CAAC,EAAE,EAAEhc,UAAU,CAACic,QAAQ,CAAC;MACpCC,OAAO,EAAE,CAAC,EAAE,EAAElc,UAAU,CAACic,QAAQ;KAClC,CAAC;IACF,IAAI,CAACE,OAAO,GAAG,IAAI,CAACtF,EAAE,CAAC0E,KAAK,CAAC;MAC3B;IAAA,CACD,CAAC;IACF,IAAI,CAACa,YAAY,GAAG,IAAI,CAACvF,EAAE,CAAC0E,KAAK,CAAC;MAChCS,SAAS,EAAE,CAAC,EAAE,CAAC;MACfE,OAAO,EAAE,CAAC,EAAE;KACb,CAAC;IACF,IAAI,CAACG,OAAO,GAAG,IAAI,CAACxF,EAAE,CAAC0E,KAAK,CAAC;MAC3BxC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfqD,QAAQ,EAAE,CAAC,WAAW,CAAC;MACvBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC,EAAE;KACpB,CAAC;IACF,IAAI,CAACC,MAAM,GAAG,IAAI,CAAC7F,EAAE,CAAC0E,KAAK,CAAC;MAC1BrC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdkD,QAAQ,EAAE,CAAC,UAAU,CAAC;MACtBK,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,aAAa,EAAE,CAAC,EAAE;KACnB,CAAC;IACF,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjG,EAAE,CAAC0E,KAAK,CAAC;MAC9BlC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClB+C,QAAQ,EAAE,CAAC,cAAc,CAAC;MAC1BS,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,mBAAmB,EAAE,CAAC,EAAE;KACzB,CAAC;IACF,IAAI,CAACC,SAAS,GAAG,IAAI,CAACtG,EAAE,CAAC0E,KAAK,CAAC;MAC7B6B,qBAAqB,EAAE,CAAC,EAAE,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBC,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,sBAAsB,EAAE,CAAC,EAAE,CAAC;MAC5BC,mBAAmB,EAAE,CAAC,EAAE,CAAC;MACzBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC,EAAE;KACtB,CAAC;IACF,IAAI,CAACC,UAAU,GAAG,IAAI,CAAClH,EAAE,CAAC0E,KAAK,CAAC;MAC9B/B,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IACF,IAAI,CAACuE,QAAQ,GAAG,IAAI,CAACnH,EAAE,CAAC0E,KAAK,CAAC;MAC5B/B,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IACF,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACxH,IAAI,CAACyH,cAAc,EAAE;IACtC,MAAMC,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC;IACnD,IAAI,CAACC,MAAM,GAAGH,UAAU,GAAGI,IAAI,CAAC9I,KAAK,CAAC0I,UAAU,CAAC,GAAG,EAAE;IACtD,IAAI,CAACK,QAAQ,GAAG,IAAI,CAAC/H,IAAI,CAACgI,WAAW,EAAE;IACvC,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACzZ,aAAa,EAAE;IACpB,IAAI,CAAC0Z,QAAQ,EAAE;IACf,IAAI,CAACC,aAAa,EAAE;IACpB,IAAG,IAAI,CAACP,MAAM,CAACQ,QAAQ,EAAC;MACtB,IAAI,CAACxG,oBAAoB,CAACyG,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,CAACQ,QAAQ,CAAC;;IAEzD,IAAG,IAAI,CAACR,MAAM,CAACU,SAAS,EAAC;MACvB,IAAI,CAACzG,kBAAkB,CAACwG,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,CAACU,SAAS,CAAC;;IAGxD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACpH,kBAAkB,CAACqH,OAAO,CAAC,CAAC7f,WAAW,CAAC8f,MAAM,EAAE9f,WAAW,CAAC+f,KAAK,CAAC,CAAC,CAACC,IAAI,CACjG7f,GAAG,CAAE8f,MAAM,IAAI;MACb,OAAOA,MAAM,CAACC,OAAO;IACvB,CAAC,CAAC,CACH;EACL;EAEAC,WAAWA,CAACC,KAAU;IACpB,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;MACrB,IAAI,CAACd,QAAQ,EAAE;;EAEnB;EAEAe,sBAAsBA,CAACC,UAAkB;IACvC,IAAI,CAACpH,oBAAoB,GAAGoH,UAAU,CAACC,MAAM,KAAK,CAAC;IACnD,IAAI,CAACC,YAAY,GAAG,IAAIC,IAAI,EAAE,CAAC,CAAC;EAElC;;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC7I,mBAAmB,GAAG,IAAI,CAAC8I,sBAAsB,EAAE;IACxD,IAAI,CAACC,gBAAgB,GAAG,IAAIrgB,WAAW,CAAC,EAAE,EAAEG,UAAU,CAACmgB,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAClF,IAAI,CAACD,gBAAgB,CAACE,YAAY,CAACf,IAAI,CAAC3f,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CACjF0gB,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC;IACF,IAAI,CAACrK,gBAAgB,CAACuK,sBAAsB,CAACH,SAAS,CAACI,IAAI,IAAG;MAC5D,IAAI,CAACC,QAAQ,GAACD,IAAI;MAClB,IAAI,CAACnb,gBAAgB,GAAE,IAAI,CAACob,QAAQ;MAEpC,IAAI,IAAI,CAACA,QAAQ,CAACb,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACxa,UAAU,CAACsb,QAAQ,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC9b,eAAe,CAAC;QAC1D,IAAI,CAACsS,gBAAgB,GAAG,IAAI,CAACwJ,QAAQ,CAAC,CAAC,CAAC,CAAC9b,eAAe;OACzD,MAAM;QACL,IAAI,CAACS,UAAU,CAACsb,QAAQ,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACzJ,gBAAgB,GAAG,IAAI;;IAGhC,CAAC,CAAC;EACJ;EAEA0J,eAAeA,CAAA;IACZ,IAAI,CAACpO,eAAe,CAACqO,SAAS,GAAG,IAAI,CAACC,cAAc;IACpD,IAAI,CAACnN,qBAAqB,CAACkN,SAAS,GAAG,IAAI,CAACE,YAAY;IACxD,IAAI,CAACnM,kBAAkB,CAACiM,SAAS,GAAG,IAAI,CAACG,iBAAiB;IAC1D,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,IAAI,CAACzK,UAAU,CAAC0K,iBAAiB,EAAE,KAAK,IAAI,EAAE;MACjD,IAAI,CAACC,QAAQ,CAACC,aAAa,GAAG,CAAC;;IAEjC;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChG,UAAU,CAACiG,IAAI,EAAE;IACtB,IAAI,CAACjG,UAAU,CAACkG,QAAQ,EAAE;EAC5B;EAEAhB,iBAAiBA,CAACiB,UAAkB;IAClC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAAClc,gBAAgB,GAAG,IAAI,CAACob,QAAQ;KACtC,MAAM;MACL,MAAMe,oBAAoB,GAAGD,UAAU,CAACE,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAACrc,gBAAgB,GAAG,IAAI,CAACob,QAAQ,CAACkB,MAAM,CAACC,MAAM,IACjDA,MAAM,CAAC/c,UAAU,CAAC4c,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACG,QAAQ,CAACL,oBAAoB,CAAC,CAClF;;EAEL;EAEAxQ,YAAYA,CAACR,QAAgB;IAC3B,MAAMsR,IAAI,GAAG,IAAIhC,IAAI,CAACtP,QAAQ,CAAC;IAC/B,MAAMuR,MAAM,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACnC,MAAMC,OAAO,GAAG,IAAIlC,IAAI,CAACgC,IAAI,CAACG,OAAO,EAAE,GAAGF,MAAM,CAAC;IACjD,IAAIG,KAAK,GAAGF,OAAO,CAACG,QAAQ,EAAE;IAC9B,IAAID,KAAK,IAAI,EAAE,EAAE;MACbA,KAAK,GAAGA,KAAK,GAAG,EAAE;KACrB,MAAM;MACHA,KAAK,GAAGA,KAAK,GAAG,EAAE;;IAEtB,MAAME,WAAW,GAAG,IAAItC,IAAI,CAACkC,OAAO,CAAC;IACrCI,WAAW,CAACC,QAAQ,CAACH,KAAK,CAAC;IAC3B,OAAOE,WAAW,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;EACzH;EAEA/D,QAAQA,CAAA;IACN,IAAI,CAACvI,GAAG,CAACuI,QAAQ,CAAC,IAAI,CAACX,IAAI,CAAC2E,QAAQ,CAAC,CAACvC,SAAS,CAAC;MAC9CiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE;UAC1B,IAAI,CAACC,YAAY,CAACD,GAAG,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC;UACtC,IAAI,CAACC,YAAY,CAACD,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC;UACrC,IAAI,CAACC,YAAY,CAACD,GAAG,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC;UAEzC,IAAI,CAACpK,KAAK,GAAGoK,GAAG,CAAC,OAAO,CAAC;UACzB,IAAI,CAACnI,aAAa,GAAG,IAAI,CAACjC,KAAK,CAACjZ,GAAG,CAACujB,IAAI,IAAIA,IAAI,CAAC;UACjD,IAAI,CAACzc,SAAS,CAACgb,IAAI,CAAC,IAAI,CAAC5G,aAAa,CAACsI,KAAK,EAAE,CAAC;UAC/C,IAAI,CAAC3c,mBAAmB,CAAC+Z,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YACpF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACvI,aAAa,EAAE,IAAI,CAACrU,mBAAmB,EAAE,IAAI,CAACC,SAAS,CAAC;UAC3E,CAAC,CAAC;UACF,IAAI,CAACmS,KAAK,GAAGoK,GAAG,CAAC,OAAO,CAAC;UACzB,IAAI,CAAClI,QAAQ,GAAG,IAAI,CAAClC,KAAK,CAACjZ,GAAG,CAACujB,IAAI,IAAIA,IAAI,CAAC;UAC5C,IAAI,CAAC9b,KAAK,CAACqa,IAAI,CAAC,IAAI,CAAC3G,QAAQ,CAACqI,KAAK,EAAE,CAAC;UACtC,IAAI,CAAChc,cAAc,CAACoZ,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YAC/E,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACtI,QAAQ,EAAE,IAAI,CAAC3T,cAAc,EAAE,IAAI,CAACC,KAAK,CAAC;UAC7D,CAAC,CAAC;UACF,IAAI,CAACwR,KAAK,GAAGoK,GAAG,CAAC,OAAO,CAAC;UACzB,IAAI,CAACjI,UAAU,GAAG,IAAI,CAACnC,KAAK,CAACjZ,GAAG,CAACujB,IAAI,IAAIA,IAAI,CAAC;UAC9C,IAAI,CAACnb,MAAM,CAAC0Z,IAAI,CAAC,IAAI,CAAC1G,UAAU,CAACoI,KAAK,EAAE,CAAC;UACzC,IAAI,CAACrb,gBAAgB,CAACyY,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YACjF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACrI,UAAU,EAAE,IAAI,CAACjT,gBAAgB,EAAE,IAAI,CAACC,MAAM,CAAC;UAClE,CAAC,CAAC;UACF,IAAI,CAAC6Q,KAAK,GAAGoK,GAAG,CAAC,OAAO,CAAC;UACzB,IAAI,CAAChI,SAAS,GAAG,IAAI,CAACpC,KAAK,CAACjZ,GAAG,CAACujB,IAAI,IAAIA,IAAI,CAAC;UAC7C,IAAI,CAACxa,KAAK,CAAC+Y,IAAI,CAAC,IAAI,CAACzG,SAAS,CAACmI,KAAK,EAAE,CAAC;UACvC,IAAI,CAAC1a,eAAe,CAAC8X,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YAChF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACpI,SAAS,EAAE,IAAI,CAACvS,eAAe,EAAE,IAAI,CAACC,KAAK,CAAC;UAC/D,CAAC,CAAC;UACF,IAAI,CAACkQ,KAAK,GAAGoK,GAAG,CAAC,OAAO,CAAC;UACzB,IAAI,CAACzH,WAAW,GAAG,IAAI,CAAC3C,KAAK,CAACjZ,GAAG,CAACujB,IAAI,IAAIA,IAAI,CAAC;UAC/C,IAAI,CAACpU,OAAO,CAAC2S,IAAI,CAAC,IAAI,CAAClG,WAAW,CAAC4H,KAAK,EAAE,CAAC;UAC3C,IAAI,CAACtU,iBAAiB,CAAC0R,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YAClF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAAC7H,WAAW,EAAE,IAAI,CAAC1M,iBAAiB,EAAE,IAAI,CAACC,OAAO,CAAC;UACrE,CAAC,CAAC;UACF,IAAIuU,eAAe,GAAG,IAAI,CAACzK,KAAK,CAAC0K,OAAO,CAAC,YAAY,CAAC;UACtD,IAAID,eAAe,KAAK,CAAC,CAAC,EAAE;YAC1B,IAAI,CAACzK,KAAK,CAAC2K,OAAO,CAAC,IAAI,CAAC3K,KAAK,CAAC4K,MAAM,CAACH,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;MAGlE,CAAC;MACDI,KAAK,EAAGC,GAAG,IAAI,CACf;KACD,CAAC;EACJ;EAEUN,MAAMA,CAACO,IAAQ,EAAEC,IAAQ,EAAEhD,IAAQ;IAC3C,IAAI,CAAC+C,IAAI,EAAE;MACT;;IAEF,IAAIE,MAAM,GAAGD,IAAI,CAACrT,KAAK;IACvB,IAAI,CAACsT,MAAM,EAAE;MACXjD,IAAI,CAACa,IAAI,CAACkC,IAAI,CAACR,KAAK,EAAE,CAAC;MACvB;KACD,MAAM;MACLU,MAAM,GAAGA,MAAM,CAAChC,WAAW,EAAE;;IAE/BjB,IAAI,CAACa,IAAI,CACPkC,IAAI,CAAC5B,MAAM,CAACnB,IAAI,IAAIA,IAAI,CAACiB,WAAW,EAAE,CAACyB,OAAO,CAACO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7D;EACH;EAEAZ,YAAYA,CAACrK,KAAY,EAAEkL,IAAY;IACrC,MAAMC,QAAQ,GAAG,GAAGD,IAAI,CAACjC,WAAW,EAAE,MAAM;IAC5C,MAAMmC,UAAU,GAAG,SAASF,IAAI,EAAE;IAClC,MAAMG,QAAQ,GAAG,OAAOH,IAAI,EAAE;IAC9B,MAAMI,SAAS,GAAG,QAAQJ,IAAI,EAAE;IAChC,MAAMK,gBAAgB,GAAG,GAAGH,UAAU,YAAY;IAClD,MAAMI,cAAc,GAAG,GAAGH,QAAQ,YAAY;IAC9C,MAAMI,eAAe,GAAG,GAAGH,SAAS,YAAY;IAEhD,IAAI,CAACH,QAAQ,CAAC,GAAGnL,KAAK,CAACjZ,GAAG,CAAC2kB,IAAI,IAAIA,IAAI,CAAC;IAExC,IAAI,CAACN,UAAU,CAAC,CAACvC,IAAI,CAAC,IAAI,CAACsC,QAAQ,CAAC,CAACZ,KAAK,EAAE,CAAC;IAC7C,IAAI,CAACgB,gBAAgB,CAAC,CAAC5D,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;MAClF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACW,QAAQ,CAAC,EAAE,IAAI,CAACI,gBAAgB,CAAC,EAAE,IAAI,CAACH,UAAU,CAAC,CAAC;IACvE,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAAC,CAACxC,IAAI,CAAC,IAAI,CAACsC,QAAQ,CAAC,CAACZ,KAAK,EAAE,CAAC;IAC3C,IAAI,CAACiB,cAAc,CAAC,CAAC7D,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;MAChF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACW,QAAQ,CAAC,EAAE,IAAI,CAACK,cAAc,CAAC,EAAE,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnE,CAAC,CAAC;IAEF,IAAI,CAACC,SAAS,CAAC,CAACzC,IAAI,CAAC,IAAI,CAACsC,QAAQ,CAAC,CAACZ,KAAK,EAAE,CAAC;IAC5C,IAAI,CAACkB,eAAe,CAAC,CAAC9D,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;MACjF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACW,QAAQ,CAAC,EAAE,IAAI,CAACM,eAAe,CAAC,EAAE,IAAI,CAACH,SAAS,CAAC,CAAC;IACrE,CAAC,CAAC;EACJ;EAEAK,eAAeA,CAAC9H,QAAgB,EAAE+H,MAAc;IAC9C,MAAMC,OAAO,GAAG,IAAI,CAAC,GAAGhI,QAAQ,CAACoF,WAAW,EAAE,MAAM,CAAC,CAAC6C,QAAQ,CAAC,GAAGF,MAAM,IAAI/H,QAAQ,EAAE,CAAC;IACvF,MAAMsH,QAAQ,GAAG,GAAGtH,QAAQ,CAACoF,WAAW,EAAE,MAAM;IAEhD,IAAI4C,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC+D,QAAQ,CAAC,CAAC/D,MAAM,EAAE;MACtDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;KACrB,MAAM;MACL2D,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC;;IAGlC,IAAI,CAAC9d,aAAa,CAACwe,OAAO,CAAClU,KAAK,CAAC;EACnC;EAEAtH,wBAAwBA,CAAA;IACtB,IAAI,CAACsb,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;EACvC;EAEA3a,sBAAsBA,CAAA;IACpB,IAAI,CAAC2a,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC;EACrC;EAEAha,uBAAuBA,CAAA;IACrB,IAAI,CAACga,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC;EACtC;EAEArZ,uBAAuBA,CAAA;IACrB,IAAI,CAACqZ,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC;EACtC;EAEA1Y,qBAAqBA,CAAA;IACnB,IAAI,CAAC0Y,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC;EACpC;EAEA/X,sBAAsBA,CAAA;IACpB,IAAI,CAAC+X,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;EACrC;EAEApX,2BAA2BA,CAAA;IACzB,IAAI,CAACoX,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC1C;EAEA;EACA;EACA;EAEAzW,0BAA0BA,CAAA;IACxB,IAAI,CAACyW,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC;EACzC;EAEAle,wBAAwBA,CAAA;IACtB,MAAMoe,OAAO,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,QAAQ,CAAC,kBAAkB,CAAC;IAC5D,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAACnF,aAAa,CAACmF,MAAM,EAAE;MAC1DyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAACyI,gBAAgB,CAAC;KAC3D,MAAM;MACLyL,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAACjG,aAAa,CAAC;MACpC,IAAI,CAAC5U,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAACyI,gBAAgB,CAAC;;EAE9D;EAEAhS,mBAAmBA,CAAA;IACjB,MAAMyd,OAAO,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,QAAQ,CAAC,aAAa,CAAC;IACvD,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAAClF,QAAQ,CAACkF,MAAM,EAAE;MACrDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAAC0I,WAAW,CAAC;KACtD,MAAM;MACLwL,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAAChG,QAAQ,CAAC;MAC/B,IAAI,CAAC7U,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAAC0I,WAAW,CAAC;;EAEzD;EAEAtR,qBAAqBA,CAAA;IACnB,MAAM8c,OAAO,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,QAAQ,CAAC,eAAe,CAAC;IACzD,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAACjF,UAAU,CAACiF,MAAM,EAAE;MACvDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAACyL,aAAa,CAAC;KACxD,MAAM;MACLyI,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAAC/F,UAAU,CAAC;MACjC,IAAI,CAAC9U,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAACyL,aAAa,CAAC;;EAE3D;EAEA1T,oBAAoBA,CAAA;IAClB,MAAMmc,OAAO,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,QAAQ,CAAC,cAAc,CAAC;IACxD,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAAChF,SAAS,CAACgF,MAAM,EAAE;MACtDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAAC0L,YAAY,CAAC;KACvD,MAAM;MACLwI,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAAC9F,SAAS,CAAC;MAChC,IAAI,CAAC/U,aAAa,CAAC,IAAI,CAAC0V,UAAU,CAACpL,KAAK,CAAC0L,YAAY,CAAC;;EAE1D;EAEAzN,uBAAuBA,CAAA;IACrB,MAAMiW,OAAO,GAAG,IAAI,CAACvG,UAAU,CAACwG,QAAQ,CAAC,eAAe,CAAC;IACzD,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAACzE,WAAW,CAACyE,MAAM,EAAE;MACxDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAACiY,UAAU,CAAC3N,KAAK,CAACoJ,aAAa,CAAC;KACxD,MAAM;MACL8K,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAACvF,WAAW,CAAC;MAClC,IAAI,CAACtV,aAAa,CAAC,IAAI,CAACiY,UAAU,CAAC3N,KAAK,CAACoJ,aAAa,CAAC;;EAE3D;EAEAvK,wBAAwBA,CAAA;IACtB,MAAMqV,OAAO,GAAG,IAAI,CAACtG,QAAQ,CAACuG,QAAQ,CAAC,cAAc,CAAC;IACtD,IAAID,OAAO,CAAClU,KAAK,CAACyP,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC1E,UAAU,CAAC0E,MAAM,EAAE;MACvDyE,OAAO,CAAC3D,QAAQ,CAAC,EAAE,CAAC;MACpB,IAAI,CAAC7a,aAAa,CAAC,IAAI,CAACkY,QAAQ,CAAC5N,KAAK,CAACqJ,YAAY,CAAC;KACrD,MAAM;MACL6K,OAAO,CAAC3D,QAAQ,CAAC,IAAI,CAACxF,UAAU,CAAC;MACjC,IAAI,CAACrV,aAAa,CAAC,IAAI,CAACkY,QAAQ,CAAC5N,KAAK,CAACqJ,YAAY,CAAC;;EAExD;EAEA+K,UAAUA,CAAA;IACR,IAAI,CAAC9N,MAAM,CAAC+N,IAAI,CAAC,IAAI,CAACC,SAAS,EAAC;MAC9BC,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAC;KACV,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpO,MAAM,CAACqO,QAAQ,EAAE;EACxB;EAEA5iB,aAAaA,CAAA;IACX,IAAG,IAAI,CAAC4B,cAAc,IAAI,YAAY,EAAC;MACrC,IAAI,CAACtB,SAAS,GAAG,IAAI,CAACa,SAAS,CAACC,IAAI,CAAC,IAAI,CAACtB,UAAU,CAAC;KACtD,MAAI;MACH,IAAI+iB,KAAK,GAAG,IAAI,CAAC1hB,SAAS,CAACC,IAAI,CAAC,IAAI,CAACV,OAAO,CAAC;MAC7C,IAAIoiB,GAAG,GAAG,IAAI,CAAC3hB,SAAS,CAACC,IAAI,CAAC,IAAI,CAACL,KAAK,CAAC;MACzC,IAAG8hB,KAAK,IAAIC,GAAG,EAAC;QACd,IAAI,CAACxiB,SAAS,GAAG,IAAI;OACtB,MAAI;QACH,IAAI,CAACA,SAAS,GAAG,KAAK;;;EAG5B;EAEAqD,aAAaA,CAACiT,UAAgB;IAC5B,IAAI,CAAChB,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,GAAG,CAAC,GAAGmN,SAAS;IACzD,IAAI,CAAC3O,EAAE,CAAC4O,aAAa,EAAE;EACzB;EAEAC,UAAUA,CAACC,YAAoB;IAC7B,IAAI,CAACjW,YAAY,GAAG,IAAI,CAACqJ,KAAK,CAAC6M,IAAI,CAACvC,IAAI,IAAIA,IAAI,KAAKsC,YAAY,CAAC;IAClE,MAAME,eAAe,GAAG,IAAI,CAACC,cAAc,CAC1C5D,MAAM,CAACtS,MAAM,IAAIA,MAAM,CAACgP,MAAM,CAACwD,QAAQ,CAACuD,YAAY,CAAC,CAAC,CACtD7lB,GAAG,CAAC8P,MAAM,IAAIA,MAAM,CAACd,WAAW,CAAC;IAClC,MAAM8V,OAAO,GAAG,IAAI,CAACtG,QAAQ,CAACuG,QAAQ,CAAC,cAAc,CAAC;IACtDD,OAAO,CAAC3D,QAAQ,CAAC4E,eAAe,CAAC;IACjC,IAAI,CAACzf,aAAa,CAACyf,eAAe,CAAC;IACnC,IAAI,CAAChP,EAAE,CAAC4O,aAAa,EAAE;EACzB;EAEAM,YAAYA,CAACC,cAAsB;IACjC,IAAI,CAAClL,eAAe,GAAG,IAAI,CAACgL,cAAc,CAAC5D,MAAM,CAACtS,MAAM,IAAIA,MAAM,CAACd,WAAW,KAAKkX,cAAc,CAAC;IAClG,IAAI,IAAI,CAAClL,eAAe,CAACqF,MAAM,GAAG,CAAC,EAAE;MACnC,MAAMpG,YAAY,GAAG,IAAI,CAACgB,mBAAmB,CAACiL,cAAc,CAAC,IAAI,IAAI,CAAClL,eAAe,CAAC,CAAC,CAAC,CAAC8D,MAAM;MAC/F,IAAI,CAACP,UAAU,CAAC4H,UAAU,CAAC;QACzBnM,aAAa,EAAEC;OAChB,CAAC;;IAEJ,IAAI,CAAClD,EAAE,CAAC4O,aAAa,EAAE;EACzB;EAEAjgB,aAAaA,CAAA;IACX,IAAI,CAACmR,GAAG,CAACuP,gBAAgB,CAAC,IAAI,CAAC3H,IAAI,CAAC2E,QAAQ,EAAE,IAAI,CAACvd,UAAU,CAAC+K,KAAK,CAAC,CAACiQ,SAAS,CAAC;MAC7EiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIgD,KAAK,CAACC,OAAO,CAACjD,GAAG,CAAC,EAAE;UACtB,IAAI,CAACkD,aAAa,GAAGlD,GAAG,CAACrjB,GAAG,CAAC,CAAC;YAAEwmB,EAAE;YAAEC;UAAI,CAAE,MAAM;YAAED,EAAE;YAAEC;UAAI,CAAE,CAAC,CAAC;UAChE,IAAIC,iBAAiB;UACrB,IAAG,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,IAAI,CAAC9gB,UAAU,CAAC+K,KAAK,CAAC,EAAC;YACnD8V,iBAAiB,GAAG,IAAI,CAACH,aAAa,CAACT,IAAI,CAACc,IAAI,IAAIA,IAAI,CAACH,IAAI,KAAK,IAAI,CAACE,MAAM,CAAC,IAAI,CAAC9gB,UAAU,CAAC+K,KAAK,CAAC,CAACiW,aAAa,IAAID,IAAI,CAACJ,EAAE,KAAM,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC9gB,UAAU,CAAC+K,KAAK,CAAC,CAACkW,gBAAgB,CAAE;;UAE1L,IAAI,CAACtO,SAAS,CAAC2I,QAAQ,CAACuF,iBAAiB,IAAI,IAAI,CAAC;;MAEpD,CAAC;MACD5C,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACjD,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEA7E,YAAYA,CAAA;IACV,IAAI,CAACrI,GAAG,CAACqI,YAAY,CAAC,IAAI,CAACT,IAAI,CAAC2E,QAAQ,CAAC,CAACvC,SAAS,CAAC;MAClDiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,GAAG,SAAS,CAAC,EAAE;UACpB,MAAMpC,IAAI,GAAGoC,GAAG,CAAC,MAAM,CAAC;UACxB,MAAM2D,WAAW,GAAG/F,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE;UAC9C,IAAI,CAACgG,WAAW,GAAGD,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE;UACjD,IAAI,CAACE,UAAU,GAAGF,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE;UAC/C,IAAI,CAACG,cAAc,GAAGH,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE;UACvD,IAAI,CAACI,aAAa,GAAGJ,WAAW,CAAC,oBAAoB,CAAC,IAAI,EAAE;UAC5D,IAAI,CAACK,gBAAgB,GAAGL,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE;UACrD,IAAI,CAAC5O,aAAa,GAAG6I,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;UAE/C,MAAM6F,gBAAgB,GAAG7F,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;UACvD,IAAI6F,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;YAC5D,MAAMQ,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACR,gBAAgB,CAAC;YAC1C,IAAIQ,IAAI,CAACjH,MAAM,GAAG,CAAC,EAAE;cACnB,MAAMmH,OAAO,GAAGF,IAAI,CAACA,IAAI,CAACjH,MAAM,GAAG,CAAC,CAAC;cACrC,IAAI,CAACxa,UAAU,CAACsb,QAAQ,CAACqG,OAAO,CAAC;cACjC,IAAI,CAACb,MAAM,GAAGG,gBAAgB;cAC9B,IAAI,CAACphB,aAAa,EAAE;;WAEvB,MAAM;YACLqhB,OAAO,CAACjD,KAAK,CAAC,uDAAuD,CAAC;;UAGxE,IAAIkD,WAAW,CAACS,cAAc,CAAC,OAAO,CAAC,IAAIT,WAAW,CAAC,OAAO,CAAC,CAACS,cAAc,CAAC,cAAc,CAAC,EAAE;YAC9F,IAAI,CAAClP,YAAY,GAAGyO,WAAW,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;WACzD,MAAM;YACL,IAAI,CAACzO,YAAY,GAAG,CAAC;;UAEvB,IAAI,CAACtV,SAAS,GAAG,IAAI;UACrB,IAAI,CAACsB,cAAc,GAAG0c,IAAI,CAAC,SAAS,CAAC;UAErC,IAAI,IAAI,CAAC1c,cAAc,IAAI,OAAO,EAAE;YAClC,IAAI,CAAClB,OAAO,GAAG4d,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,IAAI,SAAS;YACxD,IAAI,CAACvd,KAAK,GAAGud,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS;YACpD,IAAI,IAAI,CAAC5d,OAAO,IAAI,SAAS,EAAE;cAC7B,IAAI,CAACoS,MAAM,GAAG,WAAW;aAC1B,MAAM;cACL,IAAI,CAACA,MAAM,GAAG,QAAQ;cACtB,IAAI,CAACgD,aAAa,GAAG,IAAI;;WAE5B,MAAM;YACL,IAAI,CAAChW,UAAU,GAAGwe,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;YAChD,IAAI,CAACxL,MAAM,GAAG,QAAQ;YACtB,IAAI,CAACgD,aAAa,GAAG,IAAI;;UAE3B,IAAI,CAAC1B,EAAE,CAAC4O,aAAa,EAAE;UACvB,IAAI,CAAC+B,oBAAoB,EAAE;;MAE/B,CAAC;MACD5D,KAAK,EAAGC,GAAG,IAAI;QAAGgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAAC;KACpC,CAAC;EACJ;EAEA1E,aAAaA,CAAA;IACX,IAAI,CAACxI,GAAG,CAACwI,aAAa,CAAC,IAAI,CAACZ,IAAI,CAAC2E,QAAQ,CAAC,CAACvC,SAAS,CAAC;MACnDiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,GAAG,SAAS,CAAC,EAAE;UACpB,IAAI,CAAC2C,cAAc,GAAG3C,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;UAC1C,IAAI,CAACuE,YAAY,GAAG,IAAI,CAAC5B,cAAc,CAAChmB,GAAG,CAAC8P,MAAM,IAAIA,MAAM,CAACd,WAAW,CAAC;UACzE,IAAI,CAAC2M,UAAU,GAAG,IAAI,CAACqK,cAAc,CAAChmB,GAAG,CAAC8P,MAAM,IAAIA,MAAM,CAACd,WAAW,CAAC;UACvE,IAAI,CAACc,MAAM,CAACgS,IAAI,CAAC,IAAI,CAACnG,UAAU,CAAC6H,KAAK,EAAE,CAAC;UACzC,IAAI,CAAC3T,gBAAgB,CAAC+Q,YAAY,CAACf,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAAC4b,UAAU,CAAC,CAAC,CAACgF,SAAS,CAAC,MAAK;YACjF,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAAC9H,UAAU,EAAE,IAAI,CAAC9L,gBAAgB,EAAE,IAAI,CAACC,MAAM,CAAC;UAClE,CAAC,CAAC;UACF,IAAI,CAACiH,EAAE,CAAC4O,aAAa,EAAE;;MAE3B,CAAC;MACD7B,KAAK,EAAGC,GAAG,IAAI;QAAGgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAAC;KACpC,CAAC;EACJ;EAEA2D,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACT,WAAW,EAAE;MAClB,IAAI,CAACpK,OAAO,CAACsJ,UAAU,CAAC;QACpB5M,UAAU,EAAE,IAAI,CAAC0N,WAAW,CAACY,YAAY;QACzCrO,QAAQ,EAAE,IAAI,CAACyN,WAAW,CAACa,UAAU;QACrCrO,SAAS,EAAE,IAAI,CAACwN,WAAW,CAACc,SAAS;QACrChL,eAAe,EAAE,IAAI,CAACkK,WAAW,CAACe,MAAM,GAAG,KAAK,GAAG,IAAI;QACvDhL,aAAa,EAAE,IAAI,CAACiK,WAAW,CAACgB,IAAI,GAAG,KAAK,GAAG,IAAI;QACnDhL,cAAc,EAAE,IAAI,CAACgK,WAAW,CAACiB,GAAG,GAAG,KAAK,GAAG;OAClD,CAAC;MACF,IAAI,CAAC5N,kBAAkB,GAAG,IAAI,CAAC2M,WAAW,CAACe,MAAM;MACjD,IAAI,CAACzN,gBAAgB,GAAG,IAAI,CAAC0M,WAAW,CAACgB,IAAI;MAC7C,IAAI,CAACzN,iBAAiB,GAAG,IAAI,CAACyM,WAAW,CAACiB,GAAG;;IAGjD,IAAI,IAAI,CAAChB,UAAU,EAAE;MACjB,IAAI,CAAChK,MAAM,CAACiJ,UAAU,CAAC;QACnBzM,SAAS,EAAE,IAAI,CAACwN,UAAU,CAACW,YAAY;QACvClO,OAAO,EAAE,IAAI,CAACuN,UAAU,CAACY,UAAU;QACnClO,QAAQ,EAAE,IAAI,CAACsN,UAAU,CAACiB,WAAW;QACrChL,cAAc,EAAE,IAAI,CAAC+J,UAAU,CAACc,MAAM,GAAG,KAAK,GAAG,IAAI;QACrD5K,YAAY,EAAE,IAAI,CAAC8J,UAAU,CAACe,IAAI,GAAG,KAAK,GAAG,IAAI;QACjD5K,aAAa,EAAE,IAAI,CAAC6J,UAAU,CAACkB,KAAK,GAAG,KAAK,GAAG;OAClD,CAAC;MACF,IAAI,CAAC3N,iBAAiB,GAAG,IAAI,CAACyM,UAAU,CAACc,MAAM;MAC/C,IAAI,CAACtN,eAAe,GAAG,IAAI,CAACwM,UAAU,CAACe,IAAI;MAC3C,IAAI,CAACtN,gBAAgB,GAAG,IAAI,CAACuM,UAAU,CAACkB,KAAK;;IAGjD,IAAI,IAAI,CAACjB,cAAc,EAAE;MACrB,IAAI,CAAC7J,UAAU,CAAC6I,UAAU,CAAC;QACvBtM,aAAa,EAAE,IAAI,CAACsN,cAAc,CAACU,YAAY;QAC/C;QACA9N,YAAY,EAAE,IAAI,CAACoN,cAAc,CAACgB,WAAW;QAC7C5K,kBAAkB,EAAE,IAAI,CAAC4J,cAAc,CAACa,MAAM,GAAG,KAAK,GAAG,IAAI;QAC7D;QACAvK,iBAAiB,EAAE,IAAI,CAAC0J,cAAc,CAACiB,KAAK,GAAG,KAAK,GAAG,IAAI;QAC3D1K,mBAAmB,EAAE,IAAI,CAACyJ,cAAc,CAACkB,OAAO,GAAG,KAAK,GAAG;OAC9D,CAAC;MACF,IAAI,CAACzN,qBAAqB,GAAG,IAAI,CAACuM,cAAc,CAACa,MAAM;MACvD;MACA,IAAI,CAAClN,oBAAoB,GAAG,IAAI,CAACqM,cAAc,CAACiB,KAAK;;IAGzD,IAAI,IAAI,CAAChB,aAAa,EAAE;MACpB,IAAI,CAACzJ,SAAS,CAACwI,UAAU,CAAC;QACtBvI,qBAAqB,EAAE,IAAI,CAACwJ,aAAa,CAACkB,eAAe,GAAG,KAAK,GAAG,IAAI;QACxEzK,mBAAmB,EAAE,IAAI,CAACuJ,aAAa,CAACmB,aAAa,GAAG,KAAK,GAAG,IAAI;QACpEzK,oBAAoB,EAAE,IAAI,CAACsJ,aAAa,CAACoB,cAAc,GAAG,KAAK,GAAG,IAAI;QACtEzK,sBAAsB,EAAE,IAAI,CAACqJ,aAAa,CAACqB,gBAAgB,GAAG,KAAK,GAAG,IAAI;QAC1EzK,mBAAmB,EAAE,IAAI,CAACoJ,aAAa,CAACsB,aAAa,GAAG,KAAK,GAAG,IAAI;QACpE;QACA;QACA;QACA;QACA;QACApK,gBAAgB,EAAE,IAAI,CAAC8I,aAAa,CAACuB,UAAU,GAAG,KAAK,GAAG;OAC7D,CAAC;;IAGN,IAAI,IAAI,CAACtB,gBAAgB,EAAE;MACzB,IAAI,CAACrL,UAAU,CAACmK,UAAU,CAAC;QACzB9M,gBAAgB,EAAE,IAAI,CAACgO,gBAAgB,CAACvgB,SAAS,CAACgY,MAAM;QACxDxF,WAAW,EAAE,IAAI,CAAC+N,gBAAgB,CAAC5I,IAAI,CAACK,MAAM;QAC9CzC,aAAa,EAAE,IAAI,CAACgL,gBAAgB,CAACjf,MAAM,CAAC0W,MAAM;QAClD;QACA7C,qBAAqB,EAAE,IAAI,CAACoL,gBAAgB,CAACvgB,SAAS,CAAC8hB,MAAM,GAAG,KAAK,GAAG,IAAI;QAC5E1M,gBAAgB,EAAE,IAAI,CAACmL,gBAAgB,CAAC5I,IAAI,CAACmK,MAAM,GAAG,KAAK,GAAG,IAAI;QAClEzM,kBAAkB,EAAE,IAAI,CAACkL,gBAAgB,CAACjf,MAAM,CAACwgB,MAAM,GAAG,KAAK,GAAG;QAClE;OACD,CAAC;;MACF,IAAI,CAAC1O,wBAAwB,GAAG,IAAI,CAACmN,gBAAgB,CAACvgB,SAAS,CAAC8hB,MAAM;MACtE,IAAI,CAACzO,mBAAmB,GAAG,IAAI,CAACkN,gBAAgB,CAAC5I,IAAI,CAACmK,MAAM;MAC5D,IAAI,CAACxO,qBAAqB,GAAG,IAAI,CAACiN,gBAAgB,CAACjf,MAAM,CAACwgB,MAAM;MAChE;;;IAEF,IAAI,IAAI,CAACvB,gBAAgB,CAACte,KAAK,EAAE;MAC/B,IAAI,CAACiT,UAAU,CAACmK,UAAU,CAAC;QACzB7J,YAAY,EAAE,IAAI,CAAC+K,gBAAgB,CAACte,KAAK,CAAC+V,MAAM;QAChD1C,iBAAiB,EAAE,IAAI,CAACiL,gBAAgB,CAACte,KAAK,CAAC6f,MAAM,GAAG,KAAK,GAAG;OACjE,CAAC;MACF,IAAI,CAACvO,oBAAoB,GAAG,IAAI,CAACgN,gBAAgB,CAACte,KAAK,CAAC6f,MAAM;KAC/D,MAAM;MACL,IAAI,CAAC5M,UAAU,CAACmK,UAAU,CAAC;QACzB/J,iBAAiB,EAAE;OACpB,CAAC;;EAEJ;EAEFhY,QAAQA,CAAA;IACN,IAAIykB,GAAG;IACP,IAAG,IAAI,CAACpT,MAAM,IAAI,WAAW,EAAC;MAC5BoT,GAAG,GAAC;QACF,UAAU,EAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;QAC/B,OAAO,EAAG,IAAI,CAAC3E,IAAI,CAACqK,SAAS;QAC7B,MAAM,EAAG,IAAI,CAACrK,IAAI,CAACgI,IAAI;QACvB,SAAS,EAAG,OAAO;QACnB,WAAW,EAAC;UAAE,OAAO,EAAG,SAAS;UAAE,KAAK,EAAG;QAAiB,CAAE;QAC9D,aAAa,EAAE,CAAC,IAAI,CAAChkB,UAAU;OAChC;KACF,MAAI;MACHomB,GAAG,GAAC;QACF,UAAU,EAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;QAC/B,OAAO,EAAG,IAAI,CAAC3E,IAAI,CAACqK,SAAS;QAC7B,MAAM,EAAG,IAAI,CAACrK,IAAI,CAACgI,IAAI;QACvB,SAAS,EAAG,IAAI,CAACliB,cAAc;QAC/B,WAAW,EAAC;UAAE,OAAO,EAAG,IAAI,CAAClB,OAAO;UAAE,KAAK,EAAG,IAAI,CAACK;QAAK,CAAE;QAC1D,aAAa,EAAE,CAAC,IAAI,CAACjB,UAAU;OAChC;;IAEH,IAAI,CAACoU,GAAG,CAACkS,cAAc,CAACF,GAAG,CAAC,CAAChI,SAAS,CAAC;MACrCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAACvM,MAAM,CAACkS,mBAAmB,CAAC,uBAAuB,CAAC;SACzD,MAAM;UACL,IAAI,CAAClS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;QAExD,IAAI,CAAClS,EAAE,CAAC4O,aAAa,EAAE;MACzB,CAAC;MACD7B,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAmF,SAASA,CAAA;IACP,IAAIL,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,QAAQ,CAAC,GAAC,IAAI,CAAChjB,UAAU,CAAC+K,KAAK;IACnCiY,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAACrQ,SAAS,CAAC5H,KAAK,CAAC4V,EAAE;IAC9CqC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAACrQ,SAAS,CAAC5H,KAAK,CAAC6V,IAAI;IAChDoC,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI;IAEzB,IAAI,CAAChS,GAAG,CAACkS,cAAc,CAACF,GAAG,CAAC,CAAChI,SAAS,CAAC;MACrCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAACvM,MAAM,CAACkS,mBAAmB,CAAC,uBAAuB,CAAC;UACxD,MAAMlC,gBAAgB,GAAGzD,GAAG,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,IAAI,EAAE;UAC9D,IAAIyD,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;YAC5D,IAAI,CAACH,MAAM,GAAGG,gBAAgB;;SAEjC,MAAM;UACL,IAAI,CAAChQ,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;QAExD,IAAI,CAAClS,EAAE,CAAC4O,aAAa,EAAE;MACzB,CAAC;MACD7B,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA5E,YAAYA,CAAA;IACV,IAAI,CAACtI,GAAG,CAACsI,YAAY,EAAE,CAAC0B,SAAS,CAAEwC,GAAQ,IAAI;MAC7C,IAAI,CAACte,SAAS,GAAGse,GAAG,CAAC,UAAU,CAAC;MAChC,IAAI,CAACtM,EAAE,CAAC4O,aAAa,EAAE;IACzB,CAAC,CAAC;EACJ;EAEA7P,QAAQA,CAAA;IACN,IAAG,IAAI,CAACL,MAAM,KAAK,WAAW,EAAC;MAC7B,IAAI,CAACpS,OAAO;MACZ,IAAI,CAACK,KAAK;MACV,IAAI,CAACU,QAAQ,EAAE;MACf,IAAI,CAAC+S,iBAAiB,CAACgS,aAAa,CAAC,eAAe,CAAC;MACrD,IAAI,CAAC/R,MAAM,CAACgS,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,IAAI,CAAClS,MAAM,CAACqO,QAAQ,EAAE;KACvB,MAAK,IAAG,IAAI,CAAC9P,MAAM,KAAK,QAAQ,EAAC;MAChC,IAAI,CAACyB,MAAM,CAACqO,QAAQ,EAAE;KACvB,MAAI;MACH,IAAI,CAACzO,MAAM,CAACuS,gBAAgB,CAAC,2BAA2B,CAAC;;EAE7D;EAEAC,WAAWA,CAAA;IACT,IAAG,IAAI,CAAC7T,MAAM,IAAI,QAAQ,EAAC;MACzB,IAAI,CAACgD,aAAa,GAAG,IAAI;KAC1B,MAAI;MACH,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACpV,OAAO,GAAG,SAAS;MACxB,IAAI,CAACK,KAAK,GAAG,iBAAiB;MAC9B,IAAI,CAACa,cAAc,GAAE,OAAO;;EAEhC;EAEAglB,SAASA,CAAA;IACP,IAAI,CAACtS,IAAI,CAACuS,YAAY,CAAC;MAAEpG,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E;IAAQ,CAAE,CAAC,CAACvC,SAAS,CAAEI,IAAI,IAAI;MAC1E,IAAI,QAAQ,IAAIA,IAAI,EAAE;QACpB,IAAI,CAACjK,UAAU,CAACyS,SAAS,CAACxI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAMyI,YAAY,GAAG3K,IAAI,CAAC4K,SAAS,CAAC1I,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnDrC,cAAc,CAACgL,OAAO,CAAC3oB,cAAc,CAAC0d,UAAU,EAAE+K,YAAY,CAAC;QAC/D9K,cAAc,CAACgL,OAAO,CAAC,QAAQ,EAAEF,YAAY,CAAC;QAC9C,IAAI,CAAC5K,MAAM,GAAGmC,IAAI,CAAC,QAAQ,CAAC;QAC5B,IAAI,IAAI,CAACnC,MAAM,CAACQ,QAAQ,EAAE;UACxB,IAAI,CAACxG,oBAAoB,CAACyG,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,CAACQ,QAAQ,CAAC;;QAEzD,IAAI,IAAI,CAACR,MAAM,CAACU,SAAS,EAAE;UACzB,IAAI,CAACzG,kBAAkB,CAACwG,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,CAACU,SAAS,CAAC;;;IAG5D,CAAC,CAAC;EACJ;EAEAqK,gBAAgBA,CAAA;IACd,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,aAAa,GAAG,EAAE;IACtBD,WAAW,GAAG,IAAI,CAAChO,UAAU,CAAClL,KAAK,CAACkI,oBAAoB;IACxDiR,aAAa,GAAG,IAAI,CAACjO,UAAU,CAAClL,KAAK,CAACmI,kBAAkB;IACxD+Q,WAAW,CAACvK,IAAI,CAAC,YAAY,CAAC;IAC9BwK,aAAa,CAACxK,IAAI,CAAC,YAAY,CAAC;IAChC,IAAIsJ,GAAG,GAAG,EAAE;IACZ,IAAI/J,MAAM,GAAG;MACXQ,QAAQ,EAAI+G,KAAK,CAAC2D,IAAI,CAAC,IAAIC,GAAG,CAACH,WAAW,CAAC,CAAC;MAC5CtK,SAAS,EAAG6G,KAAK,CAAC2D,IAAI,CAAC,IAAIC,GAAG,CAACF,aAAa,CAAC;KAC9C;IACDlB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,QAAQ,CAAC,GAAG/J,MAAM;IACtB,IAAI,CAACjI,GAAG,CAACqT,YAAY,CAACrB,GAAG,CAAC,CAAChI,SAAS,CAAC;MACnCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAACvM,MAAM,CAACkS,mBAAmB,CAAC,sBAAsB,CAAC;UACvD,IAAI,CAAC5J,QAAQ,EAAE;UACf,IAAI,CAACmK,SAAS,EAAE;SACjB,MAAM;UACL,IAAI,CAACzS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAoG,SAASA,CAAA;IACP,IAAI,CAACC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAACvN,OAAO,EAAE,IAAI,CAACvC,kBAAkB,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,iBAAiB,CAAC;EACrH;EAEA6P,QAAQA,CAAA;IACN,IAAI,CAACD,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAClN,MAAM,EAAE,IAAI,CAACzC,iBAAiB,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,gBAAgB,CAAC;EAChH;EAEA2P,YAAYA,CAAA;IACV,IAAI,CAACF,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC9M,UAAU,EAAE,IAAI,CAAC1C,qBAAqB,EAAE,IAAI,CAACC,mBAAmB,EAAE,IAAI,CAACC,oBAAoB,EAAE,IAAI,CAACC,sBAAsB,CAAC;EACjK;EAEAwP,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACC,MAAM,EAAE;EACf;EAEAC,YAAYA,CAAA;IACV,IAAI,CAAClJ,QAAQ,EAAE;EACjB;EAEAmJ,SAASA,CAAC3K,KAAwB;IAChC,IAAIA,KAAK,CAAC4K,GAAG,CAACC,SAAS,KAAK,OAAO,EAAE;MACnC,IAAI,CAACrJ,QAAQ,EAAE;;IAEjB,IAAIxB,KAAK,CAAC4K,GAAG,CAACC,SAAS,KAAK,KAAK,EAAE;MACjC,IAAI,CAACJ,MAAM,EAAE;;IAEf,IAAIzK,KAAK,CAAC4K,GAAG,CAACC,SAAS,KAAK,UAAU,EAAE;MACtC,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEA/I,QAAQA,CAAA;IACN,IAAIoH,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,OAAO,CAAC,GAAG,OAAO;IACtB,IAAI,CAAChS,GAAG,CAACkU,cAAc,CAAClC,GAAG,CAAC,CAAChI,SAAS,CAAC;MACrCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,MAAM2H,KAAK,GAAG3H,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAACrQ,eAAe,CAACiO,IAAI,GAAGoC,GAAG,CAAC,MAAM,CAAC;QACvC,IAAI,CAACrQ,eAAe,CAACqO,SAAS,GAAG,IAAI,CAACC,cAAc;QACpD,IAAI,CAAC3I,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACDmL,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA2G,MAAMA,CAAA;IACJ,IAAI7B,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,OAAO,CAAC,GAAG,iBAAiB;IAChC,IAAI,CAAChS,GAAG,CAACoU,YAAY,CAACpC,GAAG,CAAC,CAAChI,SAAS,CAAC;MACnCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,MAAM6H,GAAG,GAAG7H,GAAG,CAAC,MAAM,CAAC;QACvB,IAAI,CAAClP,qBAAqB,CAAC8M,IAAI,GAAGoC,GAAG,CAAC,MAAM,CAAC;QAC7C,IAAI,CAAClP,qBAAqB,CAACkN,SAAS,GAAG,IAAI,CAACE,YAAY;QACxD,IAAI,CAAC3I,cAAc,GAAG,IAAI;MAC5B,CAAC;MACDkL,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAyG,WAAWA,CAAA;IACT,IAAI3B,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,OAAO,CAAC,GAAG,UAAU;IACzB,IAAI,CAAChS,GAAG,CAACsU,iBAAiB,CAACtC,GAAG,CAAC,CAAChI,SAAS,CAAC;MACxCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,MAAM6H,GAAG,GAAG7H,GAAG,CAAC,MAAM,CAAC;QACvB,IAAI,CAACjO,kBAAkB,CAAC6L,IAAI,GAAGoC,GAAG,CAAC,MAAM,CAAC;QAC1C,IAAI,CAACjO,kBAAkB,CAACiM,SAAS,GAAG,IAAI,CAACG,iBAAiB;QAC1D,IAAI,CAAC3I,eAAe,GAAG,IAAI;MAC7B,CAAC;MACDiL,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAqH,cAAcA,CAAC7I,IAAI;IACjB,MAAM8I,WAAW,GAAG,IAAI9K,IAAI,CAACgC,IAAI,CAAC;IAClC8I,WAAW,CAACvI,QAAQ,CAACuI,WAAW,CAACzI,QAAQ,EAAE,GAAG,CAAC,EAAEyI,WAAW,CAACC,UAAU,EAAE,GAAG,EAAE,CAAC;IAC/E,OAAOD,WAAW,CAACE,WAAW,EAAE;EAClC;EAEAC,UAAUA,CAAA;IACR,IAAI,IAAI,CAACjP,SAAS,CAACkP,OAAO,EAAE;MAC1B,IAAI,CAAClP,SAAS,CAACmP,gBAAgB,EAAE;MACjC,IAAI,CAAC5U,MAAM,CAACmS,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACP,IAAIJ,GAAG,GAAG;QACRzF,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E,QAAQ;QAC5BnD,KAAK,EAAE,OAAO;QACd0L,SAAS,EAAE,IAAI,CAAClN,IAAI,CAACmN,KAAK;QAC1BpP,SAAS,EAAE,IAAI,CAAC4O,cAAc,CAAC,IAAI,CAAC7O,SAAS,CAACsP,GAAG,CAAC,WAAW,CAAC,CAACjb,KAAK,CAAC;QACrE8L,OAAO,EAAE,IAAI,CAAC0O,cAAc,CAAC,IAAI,CAAC7O,SAAS,CAACsP,GAAG,CAAC,SAAS,CAAC,CAACjb,KAAK;OACjE;MAED,IAAI,CAACiG,GAAG,CAAC2U,UAAU,CAAC3C,GAAG,CAAC,CAAChI,SAAS,CAAC;QACjCiB,IAAI,EAAGuB,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACvD,MAAM,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAChJ,MAAM,CAACkS,mBAAmB,CAAC,wBAAwB,CAAC;YACzD,IAAI,CAACzM,SAAS,CAACuP,KAAK,EAAE;YACtB,IAAI,CAACnB,YAAY,EAAE;WACpB,MAAM;YACL,IAAI,CAAC7T,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;QACjD,CAAC;QACVnF,KAAK,EAAGC,GAAG,IAAI;UACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;QAClB;OACD,CAAC;;EAEJ;EAEAgI,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAIzL,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACmC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC6I,WAAW,EAAE,CAAC/H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAE,GAAG;IACrG,IAAIqF,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,OAAO,CAAC,GAAG,iBAAiB;IAChCA,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACpK,IAAI,CAACmN,KAAK;IAClC/C,GAAG,CAAC,aAAa,CAAC,GAAGmD,OAAO;IAC5B,IAAI,CAACnV,GAAG,CAACkV,WAAW,CAAClD,GAAG,CAAC,CAAChI,SAAS,CAAC;MAClCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACvD,MAAM,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAChJ,MAAM,CAACkS,mBAAmB,CAAC,wBAAwB,CAAC;UACzD,IAAI,CAACyB,UAAU,EAAE;SAClB,MAAM;UACL,IAAI,CAAC3T,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAkI,QAAQA,CAAA;IACN,MAAMD,OAAO,GAAG,IAAIzL,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACmC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC6I,WAAW,EAAE,CAAC/H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAE,GAAG;IACrG,IAAIqF,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACpK,IAAI,CAAC2E,QAAQ;IACpCyF,GAAG,CAAC,OAAO,CAAC,GAAG,UAAU;IACzBA,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACpK,IAAI,CAACmN,KAAK;IAClC/C,GAAG,CAAC,aAAa,CAAC,GAAGmD,OAAO;IAC1B;IACA;IACF,IAAI,CAACnV,GAAG,CAACqV,YAAY,CAACrD,GAAG,CAAC,CAAChI,SAAS,CAAC;MACnCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACvD,MAAM,KAAK,SAAS,EAAE;UAC5B,IAAI,CAACyK,eAAe,EAAE;UACtB,IAAI,CAACzT,MAAM,CAACkS,mBAAmB,CAAC,wBAAwB,CAAC;SAC1D,MAAM;UACL,IAAI,CAAClS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAoI,YAAYA,CAAA;IACV,IAAIC,MAAM,GAAG,IAAI,CAACpQ,UAAU,CAACpL,KAAK;IAClC,IAAIiY,GAAG,GAAQ;MACbwD,gBAAgB,EAAC;QACjBvlB,SAAS,EAAE;UACT8hB,MAAM,EAAEwD,MAAM,CAACnQ,qBAAqB,KAAK,KAAK;UAC9C6C,MAAM,EAAEsN,MAAM,CAAC/S;SAChB;QACDoF,IAAI,EAAE;UACJmK,MAAM,EAAEwD,MAAM,CAAClQ,gBAAgB,KAAK,KAAK;UACzC4C,MAAM,EAAEsN,MAAM,CAAC9S;SAChB;QACDlR,MAAM,EAAE;UACNwgB,MAAM,EAAEwD,MAAM,CAACjQ,kBAAkB,KAAK,KAAK;UAC3C2C,MAAM,EAAEsN,MAAM,CAAC/P;SAChB;QACDtT,KAAK,EAAE;UACL6f,MAAM,EAAEwD,MAAM,CAAChQ,iBAAiB,KAAK,KAAK;UAC1C0C,MAAM,EAAEsN,MAAM,CAAC9P;;OAElB;MACC8G,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E;KACrB;IACD,IAAI,CAACvM,GAAG,CAACyV,kBAAkB,CAACzD,GAAG,CAAC,CAAChI,SAAS,CAAC;MACzCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACkJ,OAAO,EAAE;UACf,IAAI,CAACzV,MAAM,CAACkS,mBAAmB,CAAC,sBAAsB,CAAC;UACvD,IAAI,CAAC5J,QAAQ,EAAE;UACf,IAAI,CAACmK,SAAS,EAAE;SACjB,MAAM;UACL,IAAI,CAACzS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAyI,WAAWA,CAAA;IACT,IAAIC,WAAW,GAAG,IAAI,CAAC9O,SAAS,CAAC/M,KAAK;IACtC,IAAIiY,GAAG,GAAQ;MACXP,eAAe,EAAEmE,WAAW,CAAC7O,qBAAqB,KAAK,KAAK;MAC5D2K,aAAa,EAAEkE,WAAW,CAAC5O,mBAAmB,KAAK,KAAK;MACxD2K,cAAc,EAAEiE,WAAW,CAAC3O,oBAAoB,KAAK,KAAK;MAC1D2K,gBAAgB,EAAEgE,WAAW,CAAC1O,sBAAsB,KAAK,KAAK;MAC9D2K,aAAa,EAAE+D,WAAW,CAACzO,mBAAmB,KAAK,KAAK;MACxD2K,UAAU,EAAE8D,WAAW,CAACnO,gBAAgB,KAAK,KAAK;MAClD;MACA;MACA;MACA;MACA;MACAoO,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACf/c,MAAM,EAAE,KAAK;MACbsT,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E;KACvB;IACD,IAAI,CAACvM,GAAG,CAACyV,kBAAkB,CAACzD,GAAG,CAAC,CAAChI,SAAS,CAAC;MACzCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACkJ,OAAO,EAAE;UACf,IAAI,CAACzV,MAAM,CAACkS,mBAAmB,CAAC,sBAAsB,CAAC;UACvD,IAAI,CAAC5J,QAAQ,EAAE;UACf,IAAI,CAACmK,SAAS,EAAE;SACjB,MAAM;UACL,IAAI,CAACzS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA+I,cAAcA,CAAC7S,YAAoB;IACjC,MAAMnK,MAAM,GAAG,IAAI,CAACkW,cAAc,CAACF,IAAI,CAAChW,MAAM,IAAIA,MAAM,CAACd,WAAW,KAAKiL,YAAY,CAAC;IACtF,OAAOnK,MAAM,GAAGA,MAAM,CAACid,WAAW,GAAG,EAAE;EACzC;EAEAC,YAAYA,CAACC,WAAW,GAAG,IAAI;IAC7B,IAAInd,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC3N,KAAK;IAClC,IAAImc,WAAW,GAAG,IAAI,CAACD,cAAc,CAAChd,MAAM,CAACmK,YAAY,CAAC;IAC1D,IAAI4O,GAAG,GAAQ,EAAE;IACjB,IAAIoE,WAAW,EAAE;MACfpE,GAAG,GAAG;QACJzF,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E,QAAQ;QAC5B8J,UAAU,EAAGH,WAAW;QACxBjO,MAAM,EAAEhP,MAAM,CAACkK,aAAa;QAC5BiT,WAAW,EAAE;OACd;KACF,MAAM;MACLpE,GAAG,GAAG;QACJzF,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E,QAAQ;QAC5BG,IAAI,EAAE,IAAI,CAAC/E,QAAQ,CAACqN,GAAG,CAAC,eAAe,CAAC,CAACjb,KAAK;QAC9CzB,OAAO,EAAE,IAAI,CAACqP,QAAQ,CAACqN,GAAG,CAAC,cAAc,CAAC,CAACjb,KAAK;QAChDqc,WAAW,EAAE;OACd;;IAEH,IAAI,CAACpW,GAAG,CAACmW,YAAY,CAACnE,GAAG,CAAC,CAAChI,SAAS,CAAC;MACnCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACkJ,OAAO,EAAE;UACf,IAAI,CAACtR,mBAAmB,CAACnL,MAAM,CAACmK,YAAY,CAAC,GAAGnK,MAAM,CAACkK,aAAa;UACpE,IAAI,CAACqF,aAAa,EAAE;UACpB4N,WAAW,GAAG,IAAI,CAACzO,QAAQ,CAAC2H,UAAU,CAAC;YACrCnM,aAAa,EAAE0L;WAChB,CAAC,GAAGA,SAAS;UACd,IAAI,CAAC5O,MAAM,CAACkS,mBAAmB,CAAC,sBAAsB,CAAC;SACxD,MAAM;UACL,IAAI,CAAClS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEAqG,iBAAiBA,CAACtN,QAAgB,EAAEqQ,SAAoB,EAAEC,eAAwB,EAAEC,aAAsB,EAAEC,cAAuB,EAAEvS,sBAAgC;IACnK,IAAIwS,UAAU,GAAGJ,SAAS,CAACvc,KAAK,CAAC,UAAUkM,QAAQ,EAAE,CAAC,IAAI,EAAE;IAC5D,IAAI0Q,QAAQ,GAAGL,SAAS,CAACvc,KAAK,CAAC,QAAQkM,QAAQ,EAAE,CAAC,IAAI,EAAE;IACxD,IAAI2Q,SAAS,GAAGN,SAAS,CAACvc,KAAK,CAAC,SAASkM,QAAQ,EAAE,CAAC,IAAI,EAAE;IAC1D,IAAI+L,GAAG,GAAQ;MACbb,MAAM,EAAEoF,eAAe;MACvBnF,IAAI,EAAEoF,aAAa;MACnBjF,KAAK,EAAEkF,cAAc;MACrBxQ,QAAQ,EAAEqQ,SAAS,CAACvc,KAAK,CAACkM,QAAQ;MAClCsG,QAAQ,EAAE,IAAI,CAAC3E,IAAI,CAAC2E,QAAQ;MAC5ByE,YAAY,EAAExB,KAAK,CAAC2D,IAAI,CAAC,IAAIC,GAAG,CAACsD,UAAU,CAAC,CAAC;MAC7CzF,UAAU,EAAEzB,KAAK,CAAC2D,IAAI,CAAC,IAAIC,GAAG,CAACuD,QAAQ,CAAC,CAAC;MACzCrF,WAAW,EAAE9B,KAAK,CAAC2D,IAAI,CAAC,IAAIC,GAAG,CAACwD,SAAS,CAAC;KAC3C;IACD,IAAIN,SAAS,CAACO,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC7C7E,GAAG,CAACR,OAAO,GAAG8E,SAAS,CAACvc,KAAK,CAAC,qBAAqB,CAAC,KAAK,KAAK;;IAEhE,IAAI,CAACiG,GAAG,CAACyV,kBAAkB,CAACzD,GAAG,CAAC,CAAChI,SAAS,CAAC;MACzCiB,IAAI,EAAGuB,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACkJ,OAAO,EAAE;UACf,IAAI,CAACzV,MAAM,CAACkS,mBAAmB,CAAC,sBAAsB,CAAC;UACvD,IAAI,CAAC5J,QAAQ,EAAE;UACf,IAAI,CAACmK,SAAS,EAAE;SACjB,MAAM;UACL,IAAI,CAACzS,MAAM,CAACmS,iBAAiB,CAAC,uBAAuB,CAAC;;MAE1D,CAAC;MACDnF,KAAK,EAAGC,GAAG,IAAI;QACbgD,OAAO,CAACY,GAAG,CAAC5D,GAAG,CAAC;MAClB;KACD,CAAC;EACJ;EAEA4J,oBAAoBA,CAAC1N,KAAU;IAC7B,IAAI,CAAC/F,wBAAwB,GAAG+F,KAAK,CAACrP,KAAK,KAAK,KAAK;EACvD;EAEAgd,eAAeA,CAAC3N,KAAU;IACxB,IAAI,CAAC9F,mBAAmB,GAAG8F,KAAK,CAACrP,KAAK,KAAK,KAAK;EAClD;EAEAid,iBAAiBA,CAAC5N,KAAU;IAC1B,IAAI,CAAC7F,qBAAqB,GAAG6F,KAAK,CAACrP,KAAK,KAAK,KAAK;EACpD;EAEAkd,gBAAgBA,CAAC7N,KAAU;IACzB,IAAI,CAAC5F,oBAAoB,GAAG4F,KAAK,CAACrP,KAAK,KAAK,KAAK;EACnD;EAEAmd,gBAAgBA,CAAC9N,KAAU;IACzB,IAAI,CAAC3F,kBAAkB,GAAG2F,KAAK,CAACrP,KAAK,KAAK,KAAK;EACjD;EAEAod,cAAcA,CAAC/N,KAAU;IACvB,IAAI,CAAC1F,gBAAgB,GAAG0F,KAAK,CAACrP,KAAK,KAAK,KAAK;EAC/C;EAEAqd,eAAeA,CAAChO,KAAU;IACxB,IAAI,CAACzF,iBAAiB,GAAGyF,KAAK,CAACrP,KAAK,KAAK,KAAK;EAChD;EAEAsd,eAAeA,CAACjO,KAAU;IACxB,IAAI,CAACxF,iBAAiB,GAAGwF,KAAK,CAACrP,KAAK,KAAK,KAAK;EAChD;EAEAud,aAAaA,CAAClO,KAAU;IACtB,IAAI,CAACvF,eAAe,GAAGuF,KAAK,CAACrP,KAAK,KAAK,KAAK;EAC9C;EAEAwd,cAAcA,CAACnO,KAAU;IACvB,IAAI,CAACtF,gBAAgB,GAAGsF,KAAK,CAACrP,KAAK,KAAK,KAAK;EAC/C;EAEAyd,mBAAmBA,CAACpO,KAAU;IAC5B,IAAI,CAACrF,qBAAqB,GAAGqF,KAAK,CAACrP,KAAK,KAAK,KAAK;EACpD;EAEA0d,kBAAkBA,CAACrO,KAAU;IAC3B,IAAI,CAACnF,oBAAoB,GAAGmF,KAAK,CAACrP,KAAK,KAAK,KAAK;EACnD;EAEA2d,gBAAgBA,CAAA,GAChB;EAGAC,oBAAoBA,CAACvO,KAAU;IAC7B,IAAI,CAAClF,sBAAsB,GAAGkF,KAAK,CAACrP,KAAK,KAAK,KAAK;EACrD;EAECD,qBAAqBA,CAACC,KAAoB;IACzC,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC5B,IAAI,CAAC+G,mBAAmB,CAAC8W,OAAO,CAACC,KAAK,IAAG;QACvC,IAAI,CAAClX,aAAa,CAACkX,KAAK,CAACA,KAAK,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAAC;MACnE,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAAClX,aAAa,GAAG,EAAE;;IAEzB,IAAI,CAAChH,kBAAkB,GAAGI,KAAK;EACjC;EAEAge,yBAAyBA,CAAA;IACvB,IAAI,CAACpe,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACiH,qBAAqB,GAAG,IAAI;EACnC;EAGAgJ,sBAAsBA,CAAA;IACpB,OAAO,CACL;MAAEiO,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACzC;MAAEmO,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EAC1C;MAAEmO,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACvC;MAAEmO,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACvC;MAAEmO,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACrC;MAAEmO,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACtC;MAAEmO,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACtC;MAAEmO,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACxC;MAAEmO,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EAC3C;MAAEmO,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EACzC;MAAEmO,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,EAC1C;MAAEmO,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE,CAAC,IAAItO,IAAI,EAAE;IAAC,CAAE,CAC3C;EACH;EACAuO,gBAAgBA,CAAC7O,KAAU;IACzB,IAAI,CAACvI,gBAAgB,GAAGuI,KAAK,CAACrP,KAAK;IACnC,IAAI,CAAC6G,qBAAqB,GAAG,KAAK;EACpC;EAEFsX,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAACrX,gBAAgB,EAAE;MAC1BsX,KAAK,CAAC,2BAA2B,CAAC;MAClC;;IAGF,MAAMC,cAAc,GAAQ,EAAE;IAE9B,IAAI,IAAI,CAAC9e,cAAc,KAAK,IAAI,EAAE;MAChC8e,cAAc,CAAC,IAAI,CAACvX,gBAAgB,CAAC,GAAG;QACtCkR,MAAM,EAAE,KAAK;QACbsG,oBAAoB,EAAE;OACvB;KACF,MAAM,IAAI,IAAI,CAAC/e,cAAc,KAAK,KAAK,IAAI,IAAI,CAACK,kBAAkB,KAAK,cAAc,EAAE;MACtFye,cAAc,CAAC,IAAI,CAACvX,gBAAgB,CAAC,GAAG;QACtCkR,MAAM,EAAE,IAAI;QACZsG,oBAAoB,EAAE;OACvB;MAED,IAAI,CAACvX,mBAAmB,CAAC8W,OAAO,CAACC,KAAK,IAAG;QACvC,MAAMS,gBAAgB,GAAG,IAAI,CAACR,mBAAmB,CAACD,KAAK,CAAC,CAACnD,WAAW,EAAE,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpFH,cAAc,CAAC,IAAI,CAACvX,gBAAgB,CAAC,CAACwX,oBAAoB,CAACR,KAAK,CAACA,KAAK,CAACxM,WAAW,EAAE,CAAC,GAAGiN,gBAAgB;MAC1G,CAAC,CAAC;KACH,MAAM;MACLF,cAAc,CAAC,IAAI,CAACvX,gBAAgB,CAAC,GAAG;QACtCkR,MAAM,EAAE,IAAI;QACZsG,oBAAoB,EAAE;OACvB;;IAGH,MAAMG,OAAO,GAAG;MACdC,QAAQ,EAAE,IAAI,CAAC5X,gBAAgB;MAC/BuX,cAAc,EAAEA;KACjB;IACD,MAAMM,OAAO,GAAG,IAAIlwB,WAAW,CAAC;MAC9B,QAAQ,EAAE,kBAAkB;MAC5B,cAAc,EAAE;KACjB,CAAC;IAEF,IAAI,CAACuX,IAAI,CAAC4Y,IAAI,CAAC,GAAG,IAAI,CAACjY,SAAS,kCAAkC,EAAE8X,OAAO,EAAE;MAAEE;IAAO,CAAE,CAAC,CACxF1O,SAAS,CACR4O,QAAQ,IAAG;MACT,IAAI,CAAC3Y,MAAM,CAACkS,mBAAmB,CAAC,+CAA+C,CAAC;IAClF,CAAC,EACDlF,KAAK,IAAG;MACNiD,OAAO,CAACjD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9B,IAAI,CAAChN,MAAM,CAACmS,iBAAiB,CAAC,mDAAmD,CAAC;IACpF,CAAC,CACF;EAEH;EACAyG,YAAYA,CAAA;IACV,IAAI,CAACC,iBAAiB,GAAG,IAAI;EAC/B;EACEhB,mBAAmBA,CAACD,KAAU;IAE5B,MAAMkB,UAAU,GAAG,IAAIrP,IAAI,CAACA,IAAI,CAACtK,KAAK,CAACyY,KAAK,CAACA,KAAK,GAAE,UAAU,CAAC,CAAC,CAACmB,QAAQ,EAAE,CAAC,CAAC;IAC7E,OAAO,IAAItP,IAAI,CAAC,IAAI,EAAEqP,UAAU,EAAE,CAAC,CAAC;EACtC;EACAE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACpY,gBAAgB,EAAE;MAC1B,OAAO,KAAK;;IAGd,IAAI,IAAI,CAACvH,cAAc,KAAK,IAAI,EAAE;MAChC,OAAO,IAAI;;IAGb,OAAO,IAAI,CAACA,cAAc,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,CAACK,kBAAkB;EACnE;EAEAuB,YAAYA,CAACge,OAAO;IAClB,OAAOA,OAAO,CAACjM,KAAK,GAChB,sBAAsB,GACtBiM,OAAO,CAACC,IAAI,GACV,WAAW,GACX,aAAa;EACrB;;;uBA55CWzZ,yBAAyB,EAAA1U,EAAA,CAAAouB,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtuB,EAAA,CAAAouB,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxuB,EAAA,CAAAouB,iBAAA,CAAApuB,EAAA,CAAAyuB,MAAA,GAAAzuB,EAAA,CAAAouB,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAA3uB,EAAA,CAAAouB,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA7uB,EAAA,CAAAouB,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAA/uB,EAAA,CAAAouB,iBAAA,CAAApuB,EAAA,CAAAgvB,iBAAA,GAAAhvB,EAAA,CAAAouB,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtuB,EAAA,CAAAouB,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAlvB,EAAA,CAAAouB,iBAAA,CAAAe,EAAA,CAAAC,SAAA,GAAApvB,EAAA,CAAAouB,iBAAA,CAAAiB,EAAA,CAAAC,iBAAA,GAAAtvB,EAAA,CAAAouB,iBAAA,CAAAmB,EAAA,CAAAC,MAAA,GAAAxvB,EAAA,CAAAouB,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAzB9Z,yBAAyB;MAAA+a,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;uCARzB,CACT;QAAEE,OAAO,EAAEnwB,WAAW;QAAEowB,QAAQ,EAAEhwB,iBAAiB;QAAEiwB,IAAI,EAAE,CAACnwB,eAAe,EAAEC,+BAA+B;MAAC,CAAE,EAC/G;QAAEgwB,OAAO,EAAElwB,gBAAgB;QAAEqwB,QAAQ,EAAE9b;MAAe,CAAE,CACzD,GAAAnU,EAAA,CAAAkwB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtFH5vB,EAAA,CAAAC,cAAA,aAA0B;UAOWD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAC,cAAA,YAAiC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGzDH,EAAA,CAAAC,cAAA,cAAuB;UACoDD,EAAA,CAAAI,UAAA,+BAAAowB,+EAAAlwB,MAAA;YAAA,OAAqBuvB,GAAA,CAAA1R,WAAA,CAAA7d,MAAA,CAAmB;UAAA,EAAC;UAChHN,EAAA,CAAAC,cAAA,mBAA+B;UAGzBD,EAAA,CAAA0E,SAAA,UAAI;UACF1E,EAAA,CAAAC,cAAA,gBAAU;UAGMD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,WAAK;UACgBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhCH,EAAA,CAAAC,cAAA,qBAAe;UAEDD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,WAAK;UACgBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhCH,EAAA,CAAAC,cAAA,qBAAe;UAEDD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,WAAK;UACgBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAQ3CH,EAAA,CAAAC,cAAA,mBAA2B;UAGrBD,EAAA,CAAA0E,SAAA,UAAI;UACJ1E,EAAA,CAAAC,cAAA,eAAiB;UACOD,EAAA,CAAAE,MAAA,8CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,2BAA+D;UAA9CD,EAAA,CAAAI,UAAA,2BAAAqwB,6EAAAnwB,MAAA;YAAA,OAAAuvB,GAAA,CAAAjc,MAAA,GAAAtT,MAAA;UAAA,EAAoB,oBAAAowB,sEAAA;YAAA,OAAWb,GAAA,CAAApI,WAAA,EAAa;UAAA,EAAxB;UACnCznB,EAAA,CAAAC,cAAA,4BAAiC;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACvDH,EAAA,CAAAC,cAAA,4BAAoC;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI7DH,EAAA,CAAAe,UAAA,KAAA4vB,0CAAA,oBAsDO;UACP3wB,EAAA,CAAAe,UAAA,KAAA6vB,yCAAA,kBAGM;UACR5wB,EAAA,CAAAG,YAAA,EAAmB;UAGvBH,EAAA,CAAAC,cAAA,mBAAuB;UAGjBD,EAAA,CAAA0E,SAAA,UAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAA8E;UAAxED,EAAA,CAAAI,UAAA,sBAAAywB,6DAAA;YAAA,OAAYhB,GAAA,CAAAxI,SAAA,EAAW;UAAA,EAAC;UAQ5BrnB,EAAA,CAAAe,UAAA,KAAA+vB,yCAAA,kBAaM;UAEN9wB,EAAA,CAAAC,cAAA,eAA4B;UAGXD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,sBAAyC;UACvCD,EAAA,CAAAe,UAAA,KAAAgwB,gDAAA,yBAEa;UACf/wB,EAAA,CAAAG,YAAA,EAAa;UAInBH,EAAA,CAAAC,cAAA,kBAA6D;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKpFH,EAAA,CAAAC,cAAA,mBAAyB;UAGnBD,EAAA,CAAA0E,SAAA,UAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAA8G;UAAxGD,EAAA,CAAAI,UAAA,sBAAA4wB,6DAAA;YAAA,OAAYnB,GAAA,CAAA7H,gBAAA,EAAkB;UAAA,EAAC;UACjChoB,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,sBAAiI;UAA9ED,EAAA,CAAAI,UAAA,2BAAA6wB,wEAAA3wB,MAAA;YAAA,OAAAuvB,GAAA,CAAA5Y,oBAAA,GAAA3W,MAAA;UAAA,EAAkC,2BAAA2wB,wEAAA;YAAA,OAA2BpB,GAAA,CAAAprB,aAAA,EAAe;UAAA,EAA1C;UACnFzE,EAAA,CAAAe,UAAA,KAAAmwB,gDAAA,yBAEa;UACflxB,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7CH,EAAA,CAAAC,cAAA,sBAA6H;UAA5ED,EAAA,CAAAI,UAAA,2BAAA+wB,wEAAA7wB,MAAA;YAAA,OAAAuvB,GAAA,CAAA3Y,kBAAA,GAAA5W,MAAA;UAAA,EAAgC,2BAAA6wB,wEAAA;YAAA,OAA2BtB,GAAA,CAAAprB,aAAA,EAAe;UAAA,EAA1C;UAC/EzE,EAAA,CAAAe,UAAA,KAAAqwB,gDAAA,yBAEa;UACfpxB,EAAA,CAAAG,YAAA,EAAa;UAGnBH,EAAA,CAAAC,cAAA,kBAA6D;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMpFH,EAAA,CAAAC,cAAA,mBAA4B;UAKZD,EAAA,CAAAI,UAAA,sBAAAixB,8DAAA;YAAA,OAAYxB,GAAA,CAAAvF,YAAA,EAAc;UAAA,EAAC;UAC/BtqB,EAAA,CAAAC,cAAA,WAAI;UAAGD,EAAA,CAAAE,MAAA,6BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/BH,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAC,cAAA,4BAA6G;UAAxCD,EAAA,CAAAI,UAAA,oBAAAkxB,uEAAAhxB,MAAA;YAAA,OAAUuvB,GAAA,CAAA/D,oBAAA,CAAAxrB,MAAA,CAA4B;UAAA,EAAC;UAC1GN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAwwB,0CAAA,mBAgBM;UACNvxB,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,WAAI;UAAGD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1BH,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAmG;UAAnCD,EAAA,CAAAI,UAAA,oBAAAoxB,uEAAAlxB,MAAA;YAAA,OAAUuvB,GAAA,CAAA9D,eAAA,CAAAzrB,MAAA,CAAuB;UAAA,EAAC;UAChGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA0wB,0CAAA,mBAgBM;UACNzxB,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,WAAI;UAAGD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,4BAAuG;UAArCD,EAAA,CAAAI,UAAA,oBAAAsxB,uEAAApxB,MAAA;YAAA,OAAUuvB,GAAA,CAAA7D,iBAAA,CAAA1rB,MAAA,CAAyB;UAAA,EAAC;UACpGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA4wB,0CAAA,mBAgBM;UACN3xB,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,WAAI;UAAGD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3BH,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,4BAAqG;UAApCD,EAAA,CAAAI,UAAA,oBAAAwxB,uEAAAtxB,MAAA;YAAA,OAAUuvB,GAAA,CAAA5D,gBAAA,CAAA3rB,MAAA,CAAwB;UAAA,EAAC;UAClGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA8wB,0CAAA,mBAgBM;UACN7xB,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA4B;UAElBD,EAAA,CAAAI,UAAA,sBAAA0xB,8DAAA;YAAA,OAAYjC,GAAA,CAAAvH,SAAA,EAAW;UAAA,EAAC;UAC5BtoB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,4BAAmG;UAApCD,EAAA,CAAAI,UAAA,oBAAA2xB,uEAAAzxB,MAAA;YAAA,OAAUuvB,GAAA,CAAA3D,gBAAA,CAAA5rB,MAAA,CAAwB;UAAA,EAAC;UAChGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAixB,0CAAA,mBAgBM;UACNhyB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,4BAA+F;UAAlCD,EAAA,CAAAI,UAAA,oBAAA6xB,uEAAA3xB,MAAA;YAAA,OAAUuvB,GAAA,CAAA1D,cAAA,CAAA7rB,MAAA,CAAsB;UAAA,EAAC;UAC5FN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAmxB,0CAAA,mBAgBM;UAENlyB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjCH,EAAA,CAAAC,cAAA,4BAAiG;UAAnCD,EAAA,CAAAI,UAAA,oBAAA+xB,uEAAA7xB,MAAA;YAAA,OAAUuvB,GAAA,CAAAzD,eAAA,CAAA9rB,MAAA,CAAuB;UAAA,EAAC;UAC9FN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAqxB,0CAAA,mBAgBM;UACNpyB,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA2B;UAEjBD,EAAA,CAAAI,UAAA,sBAAAiyB,8DAAA;YAAA,OAAYxC,GAAA,CAAArH,QAAA,EAAU;UAAA,EAAC;UAC3BxoB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,4BAAiG;UAAnCD,EAAA,CAAAI,UAAA,oBAAAkyB,uEAAAhyB,MAAA;YAAA,OAAUuvB,GAAA,CAAAxD,eAAA,CAAA/rB,MAAA,CAAuB;UAAA,EAAC;UAC9FN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAwxB,0CAAA,mBAgBM;UACNvyB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,4BAA6F;UAAjCD,EAAA,CAAAI,UAAA,oBAAAoyB,uEAAAlyB,MAAA;YAAA,OAAUuvB,GAAA,CAAAvD,aAAA,CAAAhsB,MAAA,CAAqB;UAAA,EAAC;UAC1FN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA0xB,0CAAA,mBAgBM;UAENzyB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnCH,EAAA,CAAAC,cAAA,4BAA+F;UAAlCD,EAAA,CAAAI,UAAA,oBAAAsyB,uEAAApyB,MAAA;YAAA,OAAUuvB,GAAA,CAAAtD,cAAA,CAAAjsB,MAAA,CAAsB;UAAA,EAAC;UAC5FN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA4xB,0CAAA,mBAgBM;UAEN3yB,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA+B;UAErBD,EAAA,CAAAI,UAAA,sBAAAwyB,8DAAA;YAAA,OAAY/C,GAAA,CAAApH,YAAA,EAAc;UAAA,EAAC;UAC/BzoB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,4BAAyG;UAAvCD,EAAA,CAAAI,UAAA,oBAAAyyB,uEAAAvyB,MAAA;YAAA,OAAUuvB,GAAA,CAAArD,mBAAA,CAAAlsB,MAAA,CAA2B;UAAA,EAAC;UACtGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAA+xB,0CAAA,mBAgBM;UA2BN9yB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnCH,EAAA,CAAAC,cAAA,4BAAuG;UAAtCD,EAAA,CAAAI,UAAA,oBAAA2yB,uEAAAzyB,MAAA;YAAA,OAAUuvB,GAAA,CAAApD,kBAAA,CAAAnsB,MAAA,CAA0B;UAAA,EAAC;UACpGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAe,UAAA,MAAAiyB,0CAAA,mBAgBM;UACNhzB,EAAA,CAAAC,cAAA,gBAAiB;UACOD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,4BAA2G;UAAxCD,EAAA,CAAAI,UAAA,oBAAA6yB,uEAAA3yB,MAAA;YAAA,OAAUuvB,GAAA,CAAAlD,oBAAA,CAAArsB,MAAA,CAA4B;UAAA,EAAC;UACxGN,EAAA,CAAAC,cAAA,6BAA8B;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA8B;UAEpBD,EAAA,CAAAI,UAAA,sBAAA8yB,8DAAA;YAAA,OAAYrD,GAAA,CAAAlF,WAAA,EAAa;UAAA,EAAC;UAC9B3qB,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzDH,EAAA,CAAAC,cAAA,4BAAqE;UACrCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,4BAAmE;UACnCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,4BAAoE;UACpCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,4BAAsE;UACtCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,4BAAmE;UACnCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAA0E,SAAA,WAAI;UAyCJ1E,EAAA,CAAAC,cAAA,gBAAiB;UACkBD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,4BAAgE;UAChCD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACpDH,EAAA,CAAAC,cAAA,6BAA6B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAGtDH,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA+B;UAIjBD,EAAA,CAAAI,UAAA,sBAAA+yB,8DAAA;YAAA,OAAYtD,GAAA,CAAA1E,YAAA,EAAc;UAAA,EAAC;UAC/BnrB,EAAA,CAAAC,cAAA,2BAAqC;UACxBD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAC,cAAA,uBAAkF;UAAvCD,EAAA,CAAAI,UAAA,2BAAAgzB,yEAAA9yB,MAAA;YAAA,OAAiBuvB,GAAA,CAAAzL,YAAA,CAAA9jB,MAAA,CAAoB;UAAA,EAAC;UAC/EN,EAAA,CAAAC,cAAA,mBAAY;UACVD,EAAA,CAAA0E,SAAA,kCAA6G;UAC/G1E,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAe,UAAA,MAAAsyB,iDAAA,yBAEa;;UACfrzB,EAAA,CAAAG,YAAA,EAAa;UAGfH,EAAA,CAAAC,cAAA,0BAA4B;UAC1BD,EAAA,CAAAe,UAAA,MAAAuyB,0DAAA,mCAqBsB;UACxBtzB,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKlFH,EAAA,CAAAC,cAAA,oBAA4B;UAElBD,EAAA,CAAAI,UAAA,sBAAAmzB,8DAAA;YAAA,OAAY1D,GAAA,CAAA1E,YAAA,CAAa,KAAK,CAAC;UAAA,EAAC;UACpCnrB,EAAA,CAAAC,cAAA,2BAAqC;UACxBD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAC,cAAA,uBAAiF;UAArCD,EAAA,CAAAI,UAAA,2BAAAozB,yEAAAlzB,MAAA;YAAA,OAAiBuvB,GAAA,CAAA9L,UAAA,CAAAzjB,MAAA,CAAkB;UAAA,EAAC;UAC9EN,EAAA,CAAAC,cAAA,mBAAY;UACVD,EAAA,CAAA0E,SAAA,kCAA8G;UAChH1E,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAe,UAAA,MAAA0yB,iDAAA,yBAEa;;UACfzzB,EAAA,CAAAG,YAAA,EAAa;UAGfH,EAAA,CAAAe,UAAA,MAAA2yB,oDAAA,6BAuBgB;UAChB1zB,EAAA,CAAA0E,SAAA,WAAI;UACJ1E,EAAA,CAAAC,cAAA,mBAA6D;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAU5FH,EAAA,CAAAC,cAAA,oBAA2C;UAG/BD,EAAA,CAAAI,UAAA,sBAAAuzB,8DAAA;YAAA,OAAY9D,GAAA,CAAA3C,MAAA,EAAQ;UAAA,EAAC;UAEzBltB,EAAA,CAAAC,cAAA,YAAK;UAIcD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAExCH,EAAA,CAAAC,cAAA,uBAAiH;UAA1ED,EAAA,CAAAI,UAAA,6BAAAwzB,2EAAAtzB,MAAA;YAAA,OAAmBuvB,GAAA,CAAA5C,gBAAA,CAAA3sB,MAAA,CAAwB;UAAA,EAAC;UACjFN,EAAA,CAAAe,UAAA,MAAA8yB,gDAAA,wBAAoF;UACpF7zB,EAAA,CAAAC,cAAA,sBAAmM;UAAtID,EAAA,CAAAI,UAAA,2BAAA0zB,oEAAAxzB,MAAA;YAAA,OAAiBA,MAAA,CAAAyzB,eAAA,EAAwB;UAAA,EAAC,mBAAAC,4DAAA;YAAAh0B,EAAA,CAAAO,aAAA,CAAA0zB,KAAA;YAAA,MAAAC,IAAA,GAAAl0B,EAAA,CAAAm0B,WAAA;YAAA,OAAUn0B,EAAA,CAAAW,WAAA,CAAAkvB,GAAA,CAAAvR,sBAAA,CAAA4V,IAAA,CAAAnlB,KAAA,CAAwC;UAAA,EAAlD;UAAvG/O,EAAA,CAAAG,YAAA,EAAmM;UACnMH,EAAA,CAAAe,UAAA,MAAAqzB,iDAAA,yBAEa;UACfp0B,EAAA,CAAAG,YAAA,EAAa;UAOrBH,EAAA,CAAAe,UAAA,MAAAszB,0CAAA,kBAQM;UAGNr0B,EAAA,CAAAe,UAAA,MAAAuzB,0CAAA,mBASM;UAGNt0B,EAAA,CAAAe,UAAA,MAAAwzB,0CAAA,kBAEY;UACRv0B,EAAA,CAAAG,YAAA,EAAO;UAInBH,EAAA,CAAAC,cAAA,oBAA2B;UAESD,EAAA,CAAAI,UAAA,+BAAAo0B,gFAAAl0B,MAAA;YAAA,OAAqBuvB,GAAA,CAAA9G,SAAA,CAAAzoB,MAAA,CAAiB;UAAA,EAAC;UAErEN,EAAA,CAAAC,cAAA,oBAAuB;UAEXD,EAAA,CAAAI,UAAA,mBAAAq0B,6DAAA;YAAA,OAAS5E,GAAA,CAAA/G,YAAA,EAAc;UAAA,EAAC;UAAoD9oB,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpGH,EAAA,CAAAC,cAAA,iBAAwD;UAAlDD,EAAA,CAAAI,UAAA,sBAAAs0B,8DAAA;YAAA,OAAY7E,GAAA,CAAAlG,UAAA,EAAY;UAAA,EAAC;UAC7B3pB,EAAA,CAAAC,cAAA,YAAK;UAEUD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAA0E,SAAA,kBAA0E;UAG5E1E,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,2BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAA0E,SAAA,kBACwF;UAG1F1E,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,mBAAwE;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGzFH,EAAA,CAAAC,cAAA,qBAA8C;UAC5CD,EAAA,CAAAe,UAAA,MAAA4zB,0CAAA,oBAmCM;UAEN30B,EAAA,CAAAe,UAAA,MAAA6zB,0CAAA,kBAGM;UAEN50B,EAAA,CAAAe,UAAA,MAAA8zB,0CAAA,mBAEM;UACN70B,EAAA,CAAA0E,SAAA,gCAAqH;UACvH1E,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,qBAAqB;UAETD,EAAA,CAAAI,UAAA,mBAAA00B,6DAAA;YAAA,OAASjF,GAAA,CAAAjH,UAAA,EAAY;UAAA,EAAC;UAA8D5oB,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5GH,EAAA,CAAAC,cAAA,iBAAuD;UAAjDD,EAAA,CAAAI,UAAA,sBAAA20B,8DAAA;YAAA,OAAYlF,GAAA,CAAA3F,WAAA,EAAa;UAAA,EAAC;UAC9BlqB,EAAA,CAAAC,cAAA,oBAAsF;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrGH,EAAA,CAAAC,cAAA,qBAA8C;UAC5CD,EAAA,CAAAe,UAAA,MAAAi0B,0CAAA,oBA0BM;UAENh1B,EAAA,CAAAe,UAAA,MAAAk0B,0CAAA,kBAGM;UAENj1B,EAAA,CAAAe,UAAA,MAAAm0B,0CAAA,mBAEM;UACNl1B,EAAA,CAAA0E,SAAA,gCAAmH;UACrH1E,EAAA,CAAAG,YAAA,EAAM;UAIZH,EAAA,CAAAC,cAAA,qBAA0B;UAEdD,EAAA,CAAAI,UAAA,mBAAA+0B,6DAAA;YAAA,OAAStF,GAAA,CAAAnH,eAAA,EAAiB;UAAA,EAAC;UAA8D1oB,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACjHH,EAAA,CAAAC,cAAA,iBAAyD;UAAnDD,EAAA,CAAAI,UAAA,sBAAAg1B,8DAAA;YAAA,OAAYvF,GAAA,CAAAzF,QAAA,EAAU;UAAA,EAAC;UAqB3BpqB,EAAA,CAAAC,cAAA,oBAAsF;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAErGH,EAAA,CAAAC,cAAA,qBAA8C;UAC5CD,EAAA,CAAAe,UAAA,MAAAs0B,0CAAA,oBA0BM;UAENr1B,EAAA,CAAAe,UAAA,MAAAu0B,0CAAA,kBAGM;UAENt1B,EAAA,CAAAe,UAAA,MAAAw0B,0CAAA,mBAEM;UACNv1B,EAAA,CAAA0E,SAAA,gCAAwH;UAC1H1E,EAAA,CAAAG,YAAA,EAAM;UAgBhCH,EAAA,CAAAe,UAAA,MAAAy0B,kDAAA,mCAAAx1B,EAAA,CAAA+P,sBAAA,CAmBc;;;;;UAvgCsB/P,EAAA,CAAAiB,SAAA,IAAa;UAAbjB,EAAA,CAAAy1B,iBAAA,CAAA5F,GAAA,CAAAjT,IAAA,CAAAgI,IAAA,CAAa;UASb5kB,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAy1B,iBAAA,CAAA5F,GAAA,CAAAjT,IAAA,CAAA8E,IAAA,CAAa;UASb1hB,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAy1B,iBAAA,CAAA5F,GAAA,CAAAjT,IAAA,CAAAmN,KAAA,CAAc;UAcX/pB,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAAjc,MAAA,CAAoB;UAMa5T,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAjZ,aAAA,CAAmB;UAuDjE5W,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,UAAA,UAAA2uB,GAAA,CAAAjZ,aAAA,CAAoB;UAmBlB5W,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAtZ,aAAA,CAAmB;UAmBJvW,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAkB,UAAA,gBAAA2uB,GAAA,CAAAlZ,SAAA,CAAyB;UACP3W,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAAnL,aAAA,CAAgB;UAgBnB1kB,EAAA,CAAAiB,SAAA,GAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA5V,UAAA,CAAwB;UAOHja,EAAA,CAAAiB,SAAA,GAAkC;UAAlCjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAA5Y,oBAAA,CAAkC;UACpDjX,EAAA,CAAAiB,SAAA,GAAQ;UAARjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAAzY,KAAA,CAAQ;UAaQpX,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAA3Y,kBAAA,CAAgC;UAChDlX,EAAA,CAAAiB,SAAA,GAAQ;UAARjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAAzY,KAAA,CAAQ;UAiBXpX,EAAA,CAAAiB,SAAA,GAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA1V,UAAA,CAAwB;UAUlDna,EAAA,CAAAiB,SAAA,IAA8B;UAA9BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAxX,wBAAA,CAA8B;UA2B9BrY,EAAA,CAAAiB,SAAA,IAAyB;UAAzBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAvX,mBAAA,CAAyB;UA2BzBtY,EAAA,CAAAiB,SAAA,IAA2B;UAA3BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAtX,qBAAA,CAA2B;UA2B3BvY,EAAA,CAAAiB,SAAA,IAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAArX,oBAAA,CAA0B;UAwBHxY,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA7U,OAAA,CAAqB;UAS5Chb,EAAA,CAAAiB,SAAA,IAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAApX,kBAAA,CAAwB;UAyBxBzY,EAAA,CAAAiB,SAAA,IAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAnX,gBAAA,CAAsB;UA0BtB1Y,EAAA,CAAAiB,SAAA,IAAuB;UAAvBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAlX,iBAAA,CAAuB;UAwBD3Y,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAAxU,MAAA,CAAoB;UAS1Crb,EAAA,CAAAiB,SAAA,IAAuB;UAAvBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAjX,iBAAA,CAAuB;UAyBvB5Y,EAAA,CAAAiB,SAAA,IAAqB;UAArBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAhX,eAAA,CAAqB;UA0BrB7Y,EAAA,CAAAiB,SAAA,IAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA/W,gBAAA,CAAsB;UAyBI9Y,EAAA,CAAAiB,SAAA,GAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAApU,UAAA,CAAwB;UASlDzb,EAAA,CAAAiB,SAAA,IAA2B;UAA3BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA9W,qBAAA,CAA2B;UAmD3B/Y,EAAA,CAAAiB,SAAA,IAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA5W,oBAAA,CAA0B;UA+BDjZ,EAAA,CAAAiB,SAAA,IAAuB;UAAvBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA/T,SAAA,CAAuB;UAiGlB9b,EAAA,CAAAiB,SAAA,IAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAAnT,UAAA,CAAwB;UAK3B1c,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,gBAAA2uB,GAAA,CAAA7hB,gBAAA,CAAgC;UAE1BhO,EAAA,CAAAiB,SAAA,GAAiB;UAAjBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,UAAAqsB,GAAA,CAAA5hB,MAAA,EAAiB;UAOVjO,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAA1W,eAAA,CAAkB;UA+BvBnZ,EAAA,CAAAiB,SAAA,GAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAAlT,QAAA,CAAsB;UAK9B3c,EAAA,CAAAiB,SAAA,GAAiC;UAAjCjB,EAAA,CAAAkB,UAAA,gBAAA2uB,GAAA,CAAAxiB,iBAAA,CAAiC;UAE3BrN,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,WAAA,UAAAqsB,GAAA,CAAAviB,OAAA,EAAkB;UAMxBtN,EAAA,CAAAiB,SAAA,GAAyC;UAAzCjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAlT,QAAA,CAAAqN,GAAA,kBAAAjb,KAAA,CAAyC;UA8C1D/O,EAAA,CAAAiB,SAAA,IAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,gBAAA2uB,GAAA,CAAA7rB,UAAA,CAA0B;UACHhE,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA1Y,oBAAA,CAA0B;UAC3CnX,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,gBAAA2uB,GAAA,CAAAhR,gBAAA,CAAgC;UACjB7e,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,YAAA2uB,GAAA,CAAA5rB,gBAAA,CAAmB;UAUtDjE,EAAA,CAAAiB,SAAA,GAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAha,gBAAA,CAAsB;UAWtB7V,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAvhB,cAAA,WAA8B;UAY9BtO,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA5B,WAAA,GAAmB;UAcSjuB,EAAA,CAAAiB,SAAA,GAAuB;UAAvBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAAnV,SAAA,CAAuB;UAIjC1a,EAAA,CAAAiB,SAAA,GAA6B;UAA7BjB,EAAA,CAAAkB,UAAA,kBAAAw0B,IAAA,CAA6B;UACR11B,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,QAAAw0B,IAAA,CAAmB;UAMxC11B,EAAA,CAAAiB,SAAA,GAA2B;UAA3BjB,EAAA,CAAAkB,UAAA,kBAAAy0B,IAAA,CAA2B,cAAA9F,GAAA,CAAAnV,SAAA,CAAAsP,GAAA,cAAAjb,KAAA,SAAA8gB,GAAA,CAAAnV,SAAA,CAAAsP,GAAA,cAAAjb,KAAA;UAEN/O,EAAA,CAAAiB,SAAA,GAAiB;UAAjBjB,EAAA,CAAAkB,UAAA,QAAAy0B,IAAA,CAAiB;UAQjC31B,EAAA,CAAAiB,SAAA,GAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA/Y,gBAAA,CAAsB;UAqCvC9W,EAAA,CAAAiB,SAAA,GAAuB;UAAvBjB,EAAA,CAAAkB,UAAA,UAAA2uB,GAAA,CAAA/Y,gBAAA,CAAuB;UAKC9W,EAAA,CAAAiB,SAAA,GAA0D;UAA1DjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA1e,eAAA,CAAAiO,IAAA,CAAAZ,MAAA,SAAAqR,GAAA,CAAA/Y,gBAAA,CAA0D;UAG5C9W,EAAA,CAAAiB,SAAA,GAAwC;UAAxCjB,EAAA,CAAAkB,UAAA,oBAAAlB,EAAA,CAAAqR,eAAA,KAAAukB,GAAA,EAAwC;UASvD51B,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA/U,OAAA,CAAqB;UAG3B9a,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA9Y,cAAA,CAAoB;UA4BrC/W,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAAkB,UAAA,UAAA2uB,GAAA,CAAA9Y,cAAA,CAAqB;UAKG/W,EAAA,CAAAiB,SAAA,GAA8D;UAA9DjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAvd,qBAAA,CAAA8M,IAAA,CAAAZ,MAAA,SAAAqR,GAAA,CAAA9Y,cAAA,CAA8D;UAGhD/W,EAAA,CAAAiB,SAAA,GAAwC;UAAxCjB,EAAA,CAAAkB,UAAA,oBAAAlB,EAAA,CAAAqR,eAAA,KAAAukB,GAAA,EAAwC;UAQ1D51B,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,cAAA2uB,GAAA,CAAA9U,YAAA,CAA0B;UAwB7B/a,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAA7Y,eAAA,CAAqB;UA4BtChX,EAAA,CAAAiB,SAAA,GAAsB;UAAtBjB,EAAA,CAAAkB,UAAA,UAAA2uB,GAAA,CAAA7Y,eAAA,CAAsB;UAKEhX,EAAA,CAAAiB,SAAA,GAA4D;UAA5DjB,EAAA,CAAAkB,UAAA,SAAA2uB,GAAA,CAAAtc,kBAAA,CAAA6L,IAAA,CAAAZ,MAAA,SAAAqR,GAAA,CAAA7Y,eAAA,CAA4D;UAG9ChX,EAAA,CAAAiB,SAAA,GAAwC;UAAxCjB,EAAA,CAAAkB,UAAA,oBAAAlB,EAAA,CAAAqR,eAAA,KAAAukB,GAAA,EAAwC;;;qBDj8B5Gr4B,YAAY,EAAAs4B,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,SAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,aAAA,EAAAN,GAAA,CAAAO,QAAA,EACZt3B,mBAAmB,EAAAu3B,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB33B,mBAAmB,EACnBE,eAAe,EACfR,aAAa,EAAAk4B,GAAA,CAAAC,MAAA,EAAAD,GAAA,CAAAE,WAAA,EAEbj5B,eAAe,EAAAk5B,GAAA,CAAAC,SAAA,EACfp4B,WAAW,EAAA8vB,EAAA,CAAAuI,aAAA,EAAAvI,EAAA,CAAAwI,oBAAA,EAAAxI,EAAA,CAAAyI,yBAAA,EAAAzI,EAAA,CAAA0I,eAAA,EAAA1I,EAAA,CAAA2I,oBAAA,EAAA3I,EAAA,CAAA4I,OAAA,EAAA5I,EAAA,CAAA6I,MAAA,EACXz5B,aAAa,EAAA05B,GAAA,CAAAC,OAAA,EAEb15B,gBAAgB,EAChBc,mBAAmB,EAAA6vB,EAAA,CAAAgJ,oBAAA,EAAAhJ,EAAA,CAAAiJ,kBAAA,EAAAjJ,EAAA,CAAAkJ,eAAA,EACnB74B,kBAAkB,EAAA84B,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EAClBh6B,aAAa,EAAAi6B,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EACb94B,cAAc,EAAA+4B,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EAEdn5B,cAAc,EAAAo5B,GAAA,CAAAC,QAAA,EACdv6B,aAAa,EAAAw6B,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,YAAA,EACbx5B,eAAe,EACfE,eAAe,EAAAu5B,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfx5B,wBAAwB,EAAAy5B,GAAA,CAAAC,wBAAA,EACxBz5B,kBAAkB,EAAA05B,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,iBAAA,EAAAF,GAAA,CAAAG,uBAAA,EAAAH,GAAA,CAAAI,sBAAA,EAClB55B,cAAc,EAAA65B,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdt6B,kBAAkB,EAAAu6B,GAAA,CAAAC,YAAA,EAClBv6B,uBAAuB,EAAAw6B,GAAA,CAAAC,0BAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAUd3lB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}