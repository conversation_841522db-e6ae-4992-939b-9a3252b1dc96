from pydantic import BaseModel
from typing import Optional, List

class ConsolidatedPurchaseIndent(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    PackageName: str
    WACUnitCost: float
    OrderedQty: float
    ReceivedQty: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float

class InventoryStatusReport(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    EntryType: str
    PackageName: str
    UOM: str
    StoreStock: float
    TaxPercentage: float
    UnitPrice: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float
    GrandTotalExclTax: float
    GrandTaxAmount: float
    GrandTotalInclTax: float
    TotalStockInHand: float

class StoreVariance(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    EntryType: str
    PackageName: str
    UOM: str
    TaxPercentage: float
    WACwithTax: float
    OpeningQty: int
    PurchaseQty: float
    IndentQty: float
    IbtInQty: int
    IbtOutQty: int
    SpoilageQty: float
    ClosingQty: int
    PhysicalClosingQty: int
    Variance: int
    VarianceAmount: float
    OpeningAmount: float
    PurchaseAmount: float
    IndentAmount: float
    IbtInAmount: float
    IbtOutAmount: float
    SpoilageAmount: float
    ClosingAmount: float
    PhysicalClosingAmount: float

class ProfitMarginReport(BaseModel):
    Location: str
    WorkArea: str
    Category: str
    SubCategory: str
    MenuItemCode: str
    MenuItemName: str
    ModifierName: str
    ServingSize: str
    UnitCost: float
    ModifierCost: float
    SellingPrice: float
    SellingModifierPrice: float
    TotalSalesQty: int
    TotalSalesAmount: float
    TotalProductionCost: float
    MarginAmount: float
    MarginPercentage: float
    CostPercentage: float

class RateVarianceReport(BaseModel):
    RestaurantId: str
    Month: int
    Year: int
    ItemCode: str
    PackageName: str
    WeightedAverage: float

class POStatusReport(BaseModel):
    Location: str
    VendorId: str
    VendorName: str
    PoId: str
    PoCreatedDate: str
    DeliveryDate: str
    GrnId: str
    GrnDate: str
    Status: str

class PRStatusReport(BaseModel):
    Location: str
    Creator: str
    WorkArea: str
    Id: str
    CreatedDate: str
    CreatedTime: str
    DeliveryDate: str
    Approver: str
    ApprovalStatus: str
    ReqStatus: str
    Amount: float

class ManualClosingReport(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    EntryType: str
    PackageName: str
    UOM: str
    StoreStock: int
    UnitPrice: float
    TaxPercentage: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float
    BarEC: Optional[float] = None
    BarECTotalInclTax: Optional[float] = None
    GrandTotalExclTax: float
    GrandTaxAmount: float
    GrandTotalInclTax: float
    TotalQuantity: float
    FuelGasEC: Optional[float] = None
    FuelGasECTotalInclTax: Optional[float] = None
    ContiEC: Optional[float] = None
    ContiECTotalInclTax: Optional[float] = None
    BakeryEC: Optional[float] = None
    BakeryECTotalInclTax: Optional[float] = None
    ServiceEC: Optional[float] = None
    ServiceECTotalInclTax: Optional[float] = None
    TandoorEC: Optional[float] = None
    TandoorECTotalInclTax: Optional[float] = None
    OrientalEC: Optional[float] = None
    OrientalECTotalInclTax: Optional[float] = None
    IndianEC: Optional[float] = None
    IndianECTotalInclTax: Optional[float] = None
    HousekeepingEC: Optional[float] = None
    HousekeepingECTotalInclTax: Optional[float] = None
    StaffFoodEC: Optional[float] = None
    StaffFoodECTotalInclTax: Optional[float] = None
    BarJPN: Optional[float] = None
    BarJPNTotalInclTax: Optional[float] = None
    FuelGasJPN: Optional[float] = None
    FuelGasJPNTotalInclTax: Optional[float] = None
    ContiJPN: Optional[float] = None
    ContiJPNTotalInclTax: Optional[float] = None
    BakeryJPN: Optional[float] = None
    BakeryJPNTotalInclTax: Optional[float] = None
    ServiceJPN: Optional[float] = None
    ServiceJPNTotalInclTax: Optional[float] = None
    TandoorJPN: Optional[float] = None
    TandoorJPNTotalInclTax: Optional[float] = None
    StaffFoodJPN: Optional[float] = None
    StaffFoodJPNTotalInclTax: Optional[float] = None
    OrientalJPN: Optional[float] = None
    OrientalJPNTotalInclTax: Optional[float] = None
    IndianJPN: Optional[float] = None
    IndianJPNTotalInclTax: Optional[float] = None
    HousekeepingJPN: Optional[float] = None
    HousekeepingJPNTotalInclTax: Optional[float] = None
    BarKP: Optional[float] = None
    BarKPTotalInclTax: Optional[float] = None
    FuelGasKP: Optional[float] = None
    FuelGasKPTotalInclTax: Optional[float] = None
    BakeryKP: Optional[float] = None
    BakeryKPTotalInclTax: Optional[float] = None
    ContiKP: Optional[float] = None
    ContiKPTotalInclTax: Optional[float] = None
    ServiceKP: Optional[float] = None
    ServiceKPTotalInclTax: Optional[float] = None
    TandoorKP: Optional[float] = None
    TandoorKPTotalInclTax: Optional[float] = None
    OrientalKP: Optional[float] = None
    OrientalKPTotalInclTax: Optional[float] = None
    IndianKP: Optional[float] = None
    IndianKPTotalInclTax: Optional[float] = None
    HousekeepingKP: Optional[float] = None
    HousekeepingKPTotalInclTax: Optional[float] = None
    StaffFoodKP: Optional[float] = None
    StaffFoodKPTotalInclTax: Optional[float] = None
    ServiceWS: Optional[float] = None
    ServiceWSTotalInclTax: Optional[float] = None
    HousekeepingWS: Optional[float] = None
    HousekeepingWSTotalInclTax: Optional[float] = None
    BarWS: Optional[float] = None
    BarWSTotalInclTax: Optional[float] = None

class SystemClosingReport(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    EntryType: Optional[str] = "N/A"
    PackageName: str
    UOM: str
    StoreStock: float
    TaxPercentage: float
    UnitPrice: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float

class InventoryConsumptionNew(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemName: str
    ItemCode: str
    UOM: str
    WorkArea: str
    WorkAreaOpening: Optional[float] = "N/A"
    WorkAreaIndent: float
    WorkAreaTransferIn: float
    WorkAreaTransferOut: float
    SpoilageAdjustments: float
    TotalWorkAreaStock: float
    WorkAreaClosing: Optional[float] = "N/A"
    Actual: float
    Theoretical: float
    VarianceQty: float
    UnitPrice: float
    TaxPercentage: float
    WorkAreaOpeningInclTax: float
    WorkAreaClosingInclTax: float
    ActualInclTax: float
    TheoreticalInclTax: float
    VarianceInclTax: float

class IntraBranchTransferReport(BaseModel):
    Location: str
    Source: str
    Destination: str
    Date: int
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    PackageName: str
    TransferedQty: float
    UnitPrice: float
    TaxPercentage: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float

class GRNStatusReport(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    VendorId: str
    VendorName: str
    InvoiceId: str
    POId: str
    GRNId: str
    PIId: Optional[str] = "N/A"
    GRNDateBase: str
    ItemCode: str
    ItemName: str
    PackageName: str
    OrderQty: float
    ReceivedQty: float
    PendingQty: float
    UnitCost: float
    TaxPercentage: float
    TotalExclTax: float
    TaxAmount: float
    Cess: int
    Discount: int
    ExtraCharges: int
    TotalInclTax: float
    InvoiceDate: int
    DocumentDate: int
    Transportation: float
    ApprovalStatus: Optional[str] = None
    ApprovalStatusDetailed: Optional[str] = None
    Remarks: str
    GrandTotal: float

class IbtReport(BaseModel):
    Category: str
    SubCategory: str
    RequestedDateBase: int
    DispatchedDate: int
    ReceivedDate: int
    ID: str
    Source: str
    Destination: str
    Workarea: str
    DisptachStatus: str
    ReceiveStatus: str
    ItemName: str
    ItemCode: str
    PackageName: str
    RequestedQty: int
    DispatchedQty: int
    ReceivedQty: int
    PendingQty: int
    UnitPrice: float
    TaxPercentage: float
    UOM: str
    DispatchedTotalExclTax: float
    DispatchedTaxAmount: float
    DispatchedTotalInclTax: float
    ReceivedTotalExclTax: float
    ReceivedTaxAmount: float
    ReceivedTotalInclTax: float

class IndentIssueReport(BaseModel):
    IndentType: str
    Location: str
    WorkArea: str
    CreatedBy: str
    IndentID: str
    IndentNo: int
    RequestedDate: str
    SystemIssuedDate: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    PackageName: str
    RequestedQty: float
    IssuedQty: float
    PendingQty: float
    EntryType: str
    ActualIssuedDate: str
    IndentStatus: str
    TaxPercentage: float
    UnitPrice: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTax: float
    IssuedDate: Optional[str] = None

class FLRReport(BaseModel):
    ID: str
    Location: str
    WorkArea: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    PkgName: str
    PkgQty: float
    ManualClosingFullBottles: float
    ManualClosingML: float

class BillWiseReport(BaseModel):
    Location: str
    VendorId: str
    VendorName: str
    InvoiceId: str
    InvoiceDate: str
    GRNId: str
    TotalExclTax: float
    TaxAmount: float
    TotalInclTaxEtc: float
    Transportation: float
    Labour: float
    GrandTotal: float
    Status: str

class AdjustInventoryReport(BaseModel):
    Location: str
    ID: str
    WorkAreaStore: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    EntryType: str
    PackageName: str
    UOM: str
    Type: str
    Qty: float
    UnitPrice: float
    TaxPercentage: float
    TotalExclTax: float
    TaxAmount: float
    TotalInclTaxEtc: float
    RemarkType: str
    Remarks: str

class MenuEngineering(BaseModel):
    Location: str
    Category: str
    SubCategory: str
    ItemCode: str
    ItemName: str
    ServingSize: str
    CostOfProduction: float

class ReportModel(BaseModel):
    consolidated_purchase_indent: List[ConsolidatedPurchaseIndent] = []
    inventoryStatusReport: List[InventoryStatusReport] = []
    store_variance: List[StoreVariance] = []
    profitMarginReport: List[ProfitMarginReport] = []
    rateVarianceReport: List[RateVarianceReport] = []
    poStatusReport: List[POStatusReport] = []
    prStatusReport: List[PRStatusReport] = []
    manualClosingReport: List[ManualClosingReport] = []
    systemClosingReport: List[SystemClosingReport] = []
    inventoryConsumptionNew: List[InventoryConsumptionNew] = []
    intraBranchTransferReport: List[IntraBranchTransferReport] = []
    grnStatusReport: List[GRNStatusReport] = []
    ibtReport: List[IbtReport] = []
    indentIssueReport: List[IndentIssueReport] = []
    flrReport: List[FLRReport] = []
    billWiseReport: List[BillWiseReport] = []
    adjustInventoryReport: List[AdjustInventoryReport] = []
    menuEngineering: List[MenuEngineering] = []

# Instantiate the ReportModel


# Iterate through the list of entries and append them to the respective reports
# Access the data using the structured model

