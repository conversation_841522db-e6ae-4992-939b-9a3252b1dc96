{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 56)(1, \"mat-icon\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getReportIcon(i_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r11.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r14.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_94_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_94_button_7_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const suggestion_r16 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.applySuggestion(suggestion_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r16, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Try asking:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 62);\n    i0.ɵɵtemplate(7, SmartDashboardComponent_div_94_button_7_Template, 2, 1, \"button\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getSuggestions());\n  }\n}\nfunction SmartDashboardComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"button\", 66)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 67)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"download\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 68)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"fullscreen\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SmartDashboardComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 72);\n    i0.ɵɵtext(6, \"Your Dashboard Awaits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 73);\n    i0.ɵɵtext(8, \" Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 74)(10, \"div\", 75)(11, \"div\", 76)(12, \"mat-icon\", 77);\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 78)(15, \"h5\");\n    i0.ɵɵtext(16, \"Interactive Charts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Dynamic visualizations that respond to your queries\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 75)(20, \"div\", 76)(21, \"mat-icon\", 77);\n    i0.ɵɵtext(22, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 78)(24, \"h5\");\n    i0.ɵɵtext(25, \"Smart Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\");\n    i0.ɵɵtext(27, \"AI-powered analysis and recommendations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 75)(29, \"div\", 76)(30, \"mat-icon\", 77);\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 78)(33, \"h5\");\n    i0.ɵɵtext(34, \"Real-time Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\");\n    i0.ɵɵtext(36, \"Live updates and instant visualization\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 79);\n  }\n}\nclass SmartDashboardComponent {\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {\n    // Auto-detect header height from DOM, fallback to default\n    setTimeout(() => {\n      this.detectHeaderHeight();\n    }, 0);\n  }\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  getReportIcon(index) {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n  getSuggestions() {\n    const reportSuggestions = ['Show me top 10 items by quantity', 'Compare costs by location', 'Display monthly trends', 'Analyze vendor performance', 'Show category breakdown'];\n    return reportSuggestions;\n  }\n  applySuggestion(suggestion) {\n    this.chatMessage = suggestion;\n  }\n  /**\n   * Set the header height for proper dashboard height calculation\n   * @param height - Header height in pixels (default: 64)\n   */\n  setHeaderHeight(height = 64) {\n    this.renderer.setStyle(this.elementRef.nativeElement, '--header-height', `${height}px`);\n  }\n  /**\n   * Auto-detect header height from DOM\n   */\n  detectHeaderHeight() {\n    // Try to find common header selectors\n    const headerSelectors = ['mat-toolbar', '.mat-toolbar', 'header', '.header', '.app-header', '.navbar'];\n    for (const selector of headerSelectors) {\n      const headerElement = document.querySelector(selector);\n      if (headerElement) {\n        const height = headerElement.getBoundingClientRect().height;\n        this.setHeaderHeight(height);\n        break;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 101,\n      vars: 25,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"reports-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"appearance\", \"outline\", 1, \"report-dropdown\"], [\"placeholder\", \"Select Report Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [1, \"filter-group\", \"date-range-group\"], [1, \"date-range-container\"], [\"appearance\", \"outline\", 1, \"filter-field\", \"date-field\"], [\"matInput\", \"\", \"placeholder\", \"Start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [1, \"date-separator\"], [\"matInput\", \"\", \"placeholder\", \"End date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-header\"], [1, \"ai-title-section\"], [1, \"ai-icon\"], [1, \"ai-text\"], [1, \"ai-title\"], [1, \"ai-subtitle\"], [1, \"ai-status\"], [1, \"ai-input-wrapper\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matPrefix\", \"\", 1, \"input-prefix-icon\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me top performing restaurants this month, or Compare sales trends by location\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-fab\", \"\", \"color\", \"primary\", \"matTooltip\", \"Send message\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [\"class\", \"ai-suggestions\", 4, \"ngIf\"], [1, \"dashboard-section\"], [1, \"dashboard-header\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [1, \"dashboard-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"option-icon\"], [1, \"filter-badge\"], [1, \"selection-count\"], [1, \"ai-suggestions\"], [1, \"suggestions-header\"], [1, \"suggestion-chips\"], [\"mat-stroked-button\", \"\", \"class\", \"suggestion-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"suggestion-chip\", 3, \"click\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh charts\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export data\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Full screen\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"feature-showcase\"], [1, \"feature-item\"], [1, \"feature-icon-wrapper\"], [1, \"feature-icon\"], [1, \"feature-content\"], [1, \"charts-container\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"Reports\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-form-field\", 7)(10, \"mat-select\", 8);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_10_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_10_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(11, SmartDashboardComponent_mat_option_11_Template, 4, 3, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 3)(14, \"div\", 4)(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"h3\", 6);\n          i0.ɵɵtext(18, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, SmartDashboardComponent_span_19_Template, 2, 1, \"span\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 13)(22, \"label\", 14)(23, \"mat-icon\", 15);\n          i0.ɵɵtext(24, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 16);\n          i0.ɵɵtext(26, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, SmartDashboardComponent_span_27_Template, 2, 1, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-form-field\", 18)(29, \"mat-select\", 19);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_29_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(30, SmartDashboardComponent_mat_option_30_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 14)(33, \"mat-icon\", 15);\n          i0.ɵɵtext(34, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 16);\n          i0.ɵɵtext(36, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 18)(38, \"mat-select\", 20);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_38_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 21)(41, \"label\", 14)(42, \"mat-icon\", 15);\n          i0.ɵɵtext(43, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 16);\n          i0.ɵɵtext(45, \"Date Range\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 22)(47, \"mat-form-field\", 23)(48, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_48_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"mat-datepicker-toggle\", 25)(50, \"mat-datepicker\", null, 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"span\", 27);\n          i0.ɵɵtext(53, \"to\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-form-field\", 23)(55, \"input\", 28);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_55_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"mat-datepicker-toggle\", 25)(57, \"mat-datepicker\", null, 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 30)(60, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_60_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_64_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(65, \"mat-icon\");\n          i0.ɵɵtext(66, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(68, \"div\", 33)(69, \"div\", 34)(70, \"div\", 35)(71, \"div\", 36)(72, \"div\", 37)(73, \"mat-icon\", 38);\n          i0.ɵɵtext(74, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"h3\", 40);\n          i0.ɵɵtext(77, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"p\", 41);\n          i0.ɵɵtext(79, \"Ask questions about your data in natural language\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"div\", 42)(81, \"mat-icon\");\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\");\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(85, \"div\", 43)(86, \"div\", 44)(87, \"mat-form-field\", 45)(88, \"mat-icon\", 46);\n          i0.ɵɵtext(89, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"input\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_90_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_90_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_91_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(92, \"mat-icon\");\n          i0.ɵɵtext(93, \"send\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(94, SmartDashboardComponent_div_94_Template, 8, 1, \"div\", 49);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(95, \"div\", 50)(96, \"div\", 51);\n          i0.ɵɵtemplate(97, SmartDashboardComponent_div_97_Template, 10, 0, \"div\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"div\", 53);\n          i0.ɵɵtemplate(99, SmartDashboardComponent_div_99_Template, 37, 0, \"div\", 54);\n          i0.ɵɵtemplate(100, SmartDashboardComponent_div_100_Template, 1, 0, \"div\", 55);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(51);\n          const _r6 = i0.ɵɵreference(58);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(24);\n          i0.ɵɵclassProp(\"active\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount() > 0 ? \"check_circle\" : \"info\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0 && !ctx.chatMessage.trim());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, i2.MatFabButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatPrefix, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, MatTooltipModule, i9.MatTooltip, FormsModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel],\n      styles: [\"[_nghost-%COMP%] {\\n  --header-height: 86px;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - var(--header-height));\\n  min-height: calc(100vh - var(--header-height));\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(37, 99, 235, 0.06) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n.smart-dashboard-container.header-56[_ngcontent-%COMP%] {\\n  --header-height: 56px;\\n}\\n.smart-dashboard-container.header-64[_ngcontent-%COMP%] {\\n  --header-height: 64px;\\n}\\n.smart-dashboard-container.header-72[_ngcontent-%COMP%] {\\n  --header-height: 72px;\\n}\\n.smart-dashboard-container.header-80[_ngcontent-%COMP%] {\\n  --header-height: 80px;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 320px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reports-section[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n  overflow-y: auto;\\n  padding-right: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.875rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: #6b7280;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group.date-range-group[_ngcontent-%COMP%]   .date-range-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group.date-range-group[_ngcontent-%COMP%]   .date-range-container[_ngcontent-%COMP%]   .date-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group.date-range-group[_ngcontent-%COMP%]   .date-range-container[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #f3f4f6;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #4b5563;\\n  border-color: #d1d5db;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n  transform: translateY(-1px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e55a2b, #ff6b35);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 1rem;\\n  gap: 1rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  padding: 1.25rem;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 3px;\\n  background: linear-gradient(90deg, #ff6b35, #ff8c5a, #2563eb);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title-section[_ngcontent-%COMP%]   .ai-text[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 0.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title-section[_ngcontent-%COMP%]   .ai-text[_ngcontent-%COMP%]   .ai-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 0.75rem;\\n  background: #f3f4f6;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status.active[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n  border: 1px solid rgba(16, 185, 129, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  color: #ffffff;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.75rem;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestions-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: #f59e0b;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestion-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestion-chips[_ngcontent-%COMP%]   .suggestion-chip[_ngcontent-%COMP%] {\\n  height: 32px;\\n  border-radius: 0.75rem;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  border-color: rgba(255, 107, 53, 0.3);\\n  color: #ff6b35;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestion-chips[_ngcontent-%COMP%]   .suggestion-chip[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 53, 0.05);\\n  border-color: #ff6b35;\\n  transform: translateY(-1px);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1.25rem 1.25rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .dashboard-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 0.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  color: #4b5563;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.75rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0 0 2rem 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #f9fafb;\\n  border-radius: 0.75rem;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 53, 0.05);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  border-radius: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-icon-wrapper[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ffffff;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1.25rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 300px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - var(--header-height));\\n    min-height: calc(100vh - var(--header-height));\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 50vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.75rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group.date-range-group[_ngcontent-%COMP%]   .date-range-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group.date-range-group[_ngcontent-%COMP%]   .date-range-container[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n    text-align: center;\\n    padding: 0.25rem 0;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n    align-self: stretch;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-wrapper[_ngcontent-%COMP%]   .ai-suggestions[_ngcontent-%COMP%]   .suggestion-chips[_ngcontent-%COMP%]   .suggestion-chip[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 0;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    align-self: stretch;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 1.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-radius: 0.5rem !important;\\n  border: 1px solid #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 40px !important;\\n  min-height: 40px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  border-color: #9ca3af !important;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 10px 14px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.875rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #6b7280 !important;\\n  font-style: italic !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: #f9fafb !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.875rem !important;\\n  line-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n  font-size: 18px !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #e55a2b !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.5rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.875rem !important;\\n  min-height: 36px !important;\\n  line-height: 36px !important;\\n  padding: 0 12px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.5rem !important;\\n  font-size: 1rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button,   .mat-mdc-fab {\\n  border-radius: 0.5rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.875rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-mdc-fab {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-fab:hover {\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .mat-mdc-tooltip {\\n  background: #1f2937 !important;\\n  color: #ffffff !important;\\n  font-size: 0.75rem !important;\\n  border-radius: 0.375rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "MatTooltipModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r12", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getReportIcon", "ɵɵtextInterpolate1", "tab_r11", "label", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r13", "value", "baseDate_r14", "ɵɵlistener", "SmartDashboardComponent_div_94_button_7_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r18", "suggestion_r16", "$implicit", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "applySuggestion", "ɵɵtemplate", "SmartDashboardComponent_div_94_button_7_Template", "ctx_r7", "getSuggestions", "ɵɵelement", "SmartDashboardComponent", "constructor", "elementRef", "renderer", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "checked", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "setTimeout", "detectHeaderHeight", "toggleFilters", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "console", "log", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "icons", "reportSuggestions", "suggestion", "setHeaderHeight", "height", "setStyle", "nativeElement", "headerSelectors", "selector", "headerElement", "document", "querySelector", "getBoundingClientRect", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_10_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_10_listener", "SmartDashboardComponent_mat_option_11_Template", "SmartDashboardComponent_span_19_Template", "SmartDashboardComponent_span_27_Template", "SmartDashboardComponent_Template_mat_select_valueChange_29_listener", "SmartDashboardComponent_mat_option_30_Template", "SmartDashboardComponent_Template_mat_select_valueChange_38_listener", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_input_ngModelChange_48_listener", "SmartDashboardComponent_Template_input_ngModelChange_55_listener", "SmartDashboardComponent_Template_button_click_60_listener", "SmartDashboardComponent_Template_button_click_64_listener", "SmartDashboardComponent_Template_input_ngModelChange_90_listener", "SmartDashboardComponent_Template_input_keydown_90_listener", "SmartDashboardComponent_Template_button_click_91_listener", "SmartDashboardComponent_div_94_Template", "SmartDashboardComponent_div_97_Template", "SmartDashboardComponent_div_99_Template", "SmartDashboardComponent_div_100_Template", "_r5", "_r6", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "Mat<PERSON>ab<PERSON><PERSON><PERSON>", "i3", "MatIcon", "i4", "MatFormField", "MatPrefix", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "MatTooltip", "i10", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, Renderer2 } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatTooltipModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor(\n    private elementRef: ElementRef,\n    private renderer: Renderer2\n  ) { }\n\n  ngOnInit(): void {\n    // Auto-detect header height from DOM, fallback to default\n    setTimeout(() => {\n      this.detectHeaderHeight();\n    }, 0);\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n\n  getReportIcon(index: number): string {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n\n  getSuggestions(): string[] {\n    const reportSuggestions = [\n      'Show me top 10 items by quantity',\n      'Compare costs by location',\n      'Display monthly trends',\n      'Analyze vendor performance',\n      'Show category breakdown'\n    ];\n    return reportSuggestions;\n  }\n\n  applySuggestion(suggestion: string): void {\n    this.chatMessage = suggestion;\n  }\n\n  /**\n   * Set the header height for proper dashboard height calculation\n   * @param height - Header height in pixels (default: 64)\n   */\n  setHeaderHeight(height: number = 64): void {\n    this.renderer.setStyle(\n      this.elementRef.nativeElement,\n      '--header-height',\n      `${height}px`\n    );\n  }\n\n  /**\n   * Auto-detect header height from DOM\n   */\n  detectHeaderHeight(): void {\n    // Try to find common header selectors\n    const headerSelectors = [\n      'mat-toolbar',\n      '.mat-toolbar',\n      'header',\n      '.header',\n      '.app-header',\n      '.navbar'\n    ];\n\n    for (const selector of headerSelectors) {\n      const headerElement = document.querySelector(selector);\n      if (headerElement) {\n        const height = headerElement.getBoundingClientRect().height;\n        this.setHeaderHeight(height);\n        break;\n      }\n    }\n  }\n}\n", "<!--\n  Smart Dashboard Container\n  Header height is auto-detected. To manually set header height, use:\n  - CSS class: header-56, header-64, header-72, header-80\n  - Or call: setHeaderHeight(heightInPixels) from component\n-->\n<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Selection -->\n    <div class=\"sidebar-section reports-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">assessment</mat-icon>\n          <h3 class=\"section-title\">Reports</h3>\n        </div>\n      </div>\n      <mat-form-field appearance=\"outline\" class=\"report-dropdown\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Select Report Type\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <mat-icon class=\"option-icon\">{{ getReportIcon(i) }}</mat-icon>\n            {{ tab.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Date Range -->\n        <div class=\"filter-group date-range-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">date_range</mat-icon>\n            <span class=\"label-text\">Date Range</span>\n          </label>\n          <div class=\"date-range-container\">\n            <mat-form-field appearance=\"outline\" class=\"filter-field date-field\">\n              <input\n                matInput\n                [matDatepicker]=\"startPicker\"\n                [(ngModel)]=\"startDate\"\n                placeholder=\"Start date\"\n                readonly\n              >\n              <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n              <mat-datepicker #startPicker></mat-datepicker>\n            </mat-form-field>\n            <span class=\"date-separator\">to</span>\n            <mat-form-field appearance=\"outline\" class=\"filter-field date-field\">\n              <input\n                matInput\n                [matDatepicker]=\"endPicker\"\n                [(ngModel)]=\"endDate\"\n                placeholder=\"End date\"\n                readonly\n              >\n              <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n              <mat-datepicker #endPicker></mat-datepicker>\n            </mat-form-field>\n          </div>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Assistant Section -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-header\">\n          <div class=\"ai-title-section\">\n            <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n            <div class=\"ai-text\">\n              <h3 class=\"ai-title\">Smart Dashboard Assistant</h3>\n              <p class=\"ai-subtitle\">Ask questions about your data in natural language</p>\n            </div>\n          </div>\n          <div class=\"ai-status\" [class.active]=\"getActiveFiltersCount() > 0\">\n            <mat-icon>{{ getActiveFiltersCount() > 0 ? 'check_circle' : 'info' }}</mat-icon>\n            <span>{{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}</span>\n          </div>\n        </div>\n\n        <div class=\"ai-input-wrapper\">\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <mat-icon matPrefix class=\"input-prefix-icon\">chat</mat-icon>\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"e.g., Show me top performing restaurants this month, or Compare sales trends by location\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-fab\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n              matTooltip=\"Send message\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n\n          <div class=\"ai-suggestions\" *ngIf=\"getActiveFiltersCount() > 0 && !chatMessage.trim()\">\n            <div class=\"suggestions-header\">\n              <mat-icon>lightbulb</mat-icon>\n              <span>Try asking:</span>\n            </div>\n            <div class=\"suggestion-chips\">\n              <button\n                mat-stroked-button\n                class=\"suggestion-chip\"\n                *ngFor=\"let suggestion of getSuggestions()\"\n                (click)=\"applySuggestion(suggestion)\"\n              >\n                {{ suggestion }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-header\">\n        <!-- <div class=\"header-left\">\n          <mat-icon class=\"dashboard-icon\">dashboard</mat-icon>\n          <div class=\"header-text\">\n            <h3 class=\"dashboard-title\">Smart Dashboard</h3>\n            <p class=\"dashboard-subtitle\">AI-powered data visualization</p>\n          </div>\n        </div> -->\n        <div class=\"header-actions\" *ngIf=\"hasGeneratedCharts\">\n          <button mat-icon-button matTooltip=\"Refresh charts\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Export data\">\n            <mat-icon>download</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Full screen\">\n            <mat-icon>fullscreen</mat-icon>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!hasGeneratedCharts\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Your Dashboard Awaits</h4>\n            <p class=\"empty-state-description\">\n              Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data.\n            </p>\n\n            <div class=\"feature-showcase\">\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Interactive Charts</h5>\n                  <p>Dynamic visualizations that respond to your queries</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Smart Insights</h5>\n                  <p>AI-powered analysis and recommendations</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">speed</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Real-time Data</h5>\n                  <p>Live updates and instant visualization</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"hasGeneratedCharts\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICKlCC,EAAA,CAAAC,cAAA,qBAAgE;IAChCD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IAHuCH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAC/BL,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,KAAA,EAAsB;IACpDL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,OAAA,CAAAC,KAAA,MACF;;;;;IAWAZ,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAG,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,OAAAK,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEjB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAC,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAQ,YAAA,CAAAN,KAAA,MACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAgB,YAAA,CAAAD,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAU,YAAA,CAAAR,KAAA,MACF;;;;;;IAuGAZ,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAqB,UAAA,mBAAAC,yEAAA;MAAA,MAAAC,WAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,IAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAL,cAAA,CAA2B;IAAA,EAAC;IAErC1B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAgB,cAAA,MACF;;;;;IAbJ1B,EAAA,CAAAC,cAAA,cAAuF;IAEzED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1BH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAgC,UAAA,IAAAC,gDAAA,qBAOS;IACXjC,EAAA,CAAAG,YAAA,EAAM;;;;IALqBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAA8B,MAAA,CAAAC,cAAA,GAAmB;;;;;IAqBlDnC,EAAA,CAAAC,cAAA,cAAuD;IAEzCD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE9BH,EAAA,CAAAC,cAAA,iBAAiD;IACrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,iBAAiD;IACrCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAOnCH,EAAA,CAAAC,cAAA,cAAqD;IAGrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,qHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,cAA8B;IAGOD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAErDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,2DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9DH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEvDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIlDH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQvDH,EAAA,CAAAoC,SAAA,cAEM;;;ADjPd,MAsBaC,uBAAuB;EAqClCC,YACUC,UAAsB,EACtBC,QAAmB;IADnB,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,QAAQ,GAARA,QAAQ;IAtClB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAEzB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,wBAAwB;MAAEiC,OAAO,EAAE;IAAK,CAAE,EACzE;MAAE1B,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,iBAAiB;MAAEiC,OAAO,EAAE;IAAK,CAAE,EAClE;MAAE1B,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,aAAa;MAAEiC,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAE1B,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,gBAAgB;MAAEiC,OAAO,EAAE;IAAK,CAAE,EACjE;MAAE1B,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,oBAAoB;MAAEiC,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAC,SAAS,GAAG,CACV;MAAE3B,KAAK,EAAE,OAAO;MAAEP,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,YAAY;MAAEP,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEO,KAAK,EAAE,QAAQ;MAAEP,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAI,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAEvC,KAAK,EAAE,YAAY;MAAEwC,MAAM,EAAE;IAAI,CAAE,EACrC;MAAExC,KAAK,EAAE,iBAAiB;MAAEwC,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAExC,KAAK,EAAE,cAAc;MAAEwC,MAAM,EAAE;IAAK,CAAE,EACxC;MAAExC,KAAK,EAAE,kBAAkB;MAAEwC,MAAM,EAAE;IAAK,CAAE,CAC7C;EAKG;EAEJC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,CAAC,CAAC;EACP;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACf,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAgB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAChB,WAAW,GAAGgB,KAAK;IACxB,IAAI,CAACP,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACR,MAAM,GAAGS,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF;IACAI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACZ,IAAI,CAACO,KAAK,CAAC,CAAC9C,KAAK,CAAC;EACzD;EAEAoD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACd,WAAW,CAACe,IAAI,EAAE,EAAE;MAC3B;MACAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,WAAW,CAAC;MAE9C;MACA,IAAI,CAACP,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACO,WAAW,GAAG,EAAE;;EAEzB;EAEAgB,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;IACAT,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACtB,aAAa,GAAG,KAAK;EAC5B;EAEA+B,YAAYA,CAAA;IACV;IACA,IAAI,CAACxD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC+B,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACL,SAAS,CAACe,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAAC5B,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEA/B,qBAAqBA,CAAA;IACnB,IAAI4D,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1D,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAEyD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC3B,gBAAgB,KAAK,OAAO,EAAE2B,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC1B,SAAS,EAAE0B,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACzB,OAAO,EAAEyB,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;EAEAjE,aAAaA,CAACiD,KAAa;IACzB,MAAMiB,KAAK,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;IAC7E,OAAOA,KAAK,CAACjB,KAAK,CAAC,IAAI,YAAY;EACrC;EAEAvB,cAAcA,CAAA;IACZ,MAAMyC,iBAAiB,GAAG,CACxB,kCAAkC,EAClC,2BAA2B,EAC3B,wBAAwB,EACxB,4BAA4B,EAC5B,yBAAyB,CAC1B;IACD,OAAOA,iBAAiB;EAC1B;EAEA7C,eAAeA,CAAC8C,UAAkB;IAChC,IAAI,CAAC3B,WAAW,GAAG2B,UAAU;EAC/B;EAEA;;;;EAIAC,eAAeA,CAACC,MAAA,GAAiB,EAAE;IACjC,IAAI,CAACvC,QAAQ,CAACwC,QAAQ,CACpB,IAAI,CAACzC,UAAU,CAAC0C,aAAa,EAC7B,iBAAiB,EACjB,GAAGF,MAAM,IAAI,CACd;EACH;EAEA;;;EAGAxB,kBAAkBA,CAAA;IAChB;IACA,MAAM2B,eAAe,GAAG,CACtB,aAAa,EACb,cAAc,EACd,QAAQ,EACR,SAAS,EACT,aAAa,EACb,SAAS,CACV;IAED,KAAK,MAAMC,QAAQ,IAAID,eAAe,EAAE;MACtC,MAAME,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAACH,QAAQ,CAAC;MACtD,IAAIC,aAAa,EAAE;QACjB,MAAML,MAAM,GAAGK,aAAa,CAACG,qBAAqB,EAAE,CAACR,MAAM;QAC3D,IAAI,CAACD,eAAe,CAACC,MAAM,CAAC;QAC5B;;;EAGN;;;uBA/JW1C,uBAAuB,EAAArC,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAAyF,UAAA,GAAAzF,EAAA,CAAAwF,iBAAA,CAAAxF,EAAA,CAAA0F,SAAA;IAAA;EAAA;;;YAAvBrD,uBAAuB;MAAAsD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7F,EAAA,CAAA8F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCpCpG,EAAA,CAAAC,cAAA,aAAuC;UAOED,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAAA,CAAAC,cAAA,wBAA6D;UAC/CD,EAAA,CAAAqB,UAAA,yBAAAiF,oEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA3D,WAAA,GAAA6D,MAAA;UAAA,EAAuB,6BAAAC,wEAAAD,MAAA;YAAA,OAAoBF,GAAA,CAAA5C,WAAA,CAAA8C,MAAA,CAAApF,KAAA,CAAyB;UAAA,EAA7C;UACjCnB,EAAA,CAAAgC,UAAA,KAAAyE,8CAAA,wBAGa;UACfzG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAgC,UAAA,KAAA0E,wCAAA,mBAEO;UACT1G,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAgC,UAAA,KAAA2E,wCAAA,mBAEO;UACT3G,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAqB,UAAA,yBAAAuF,oEAAAL,MAAA;YAAA,OAAAF,GAAA,CAAArF,iBAAA,GAAAuF,MAAA;UAAA,EAA6B;UACvCvG,EAAA,CAAAgC,UAAA,KAAA6E,8CAAA,wBAEa;UACf7G,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAqB,UAAA,yBAAAyF,oEAAAP,MAAA;YAAA,OAAAF,GAAA,CAAAtD,gBAAA,GAAAwD,MAAA;UAAA,EAA4B;UACtCvG,EAAA,CAAAgC,UAAA,KAAA+E,8CAAA,wBAEa;UACf/G,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA2C;UAEVD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,eAAkC;UAK5BD,EAAA,CAAAqB,UAAA,2BAAA2F,iEAAAT,MAAA;YAAA,OAAAF,GAAA,CAAArD,SAAA,GAAAuD,MAAA;UAAA,EAAuB;UAHzBvG,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAoC,SAAA,iCAA6E;UAE/EpC,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,0BAAqE;UAIjED,EAAA,CAAAqB,UAAA,2BAAA4F,iEAAAV,MAAA;YAAA,OAAAF,GAAA,CAAApD,OAAA,GAAAsD,MAAA;UAAA,EAAqB;UAHvBvG,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAoC,SAAA,iCAA2E;UAE7EpC,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAAqB,UAAA,mBAAA6F,0DAAA;YAAA,OAASb,GAAA,CAAA7B,YAAA,EAAc;UAAA,EAAC;UACnExE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAqB,UAAA,mBAAA8F,0DAAA;YAAA,OAASd,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAClFvE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,eAAqB;UACED,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAE,MAAA,yDAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGhFH,EAAA,CAAAC,cAAA,eAAoE;UACxDD,EAAA,CAAAE,MAAA,IAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAkF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAInGH,EAAA,CAAAC,cAAA,eAA8B;UAGsBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7DH,EAAA,CAAAC,cAAA,iBAOC;UAHCD,EAAA,CAAAqB,UAAA,2BAAA+F,iEAAAb,MAAA;YAAA,OAAAF,GAAA,CAAAnD,WAAA,GAAAqD,MAAA;UAAA,EAAyB,qBAAAc,2DAAAd,MAAA;YAAA,OACdF,GAAA,CAAAnC,UAAA,CAAAqC,MAAA,CAAkB;UAAA,EADJ;UAJ3BvG,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAqB,UAAA,mBAAAiG,0DAAA;YAAA,OAASjB,GAAA,CAAArC,WAAA,EAAa;UAAA,EAAC;UAIvBhE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI7BH,EAAA,CAAAgC,UAAA,KAAAuF,uCAAA,kBAeM;UACRvH,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA+B;UAS3BD,EAAA,CAAAgC,UAAA,KAAAwF,uCAAA,mBAUM;UACRxH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA+B;UAE7BD,EAAA,CAAAgC,UAAA,KAAAyF,uCAAA,mBA0CM;UAGNzH,EAAA,CAAAgC,UAAA,MAAA0F,wCAAA,kBAEM;UACR1H,EAAA,CAAAG,YAAA,EAAM;;;;;UAhPQH,EAAA,CAAAM,SAAA,IAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAiG,GAAA,CAAA3D,WAAA,CAAuB;UACL1C,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAAlD,IAAA,CAAS;UAcTnD,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAAvF,qBAAA,OAAiC;UAY5Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAArF,iBAAA,CAAAC,MAAA,KAAkC;UAKrDjB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAiG,GAAA,CAAArF,iBAAA,CAA6B;UACNhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAAzD,SAAA,CAAY;UAcnC5C,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAiG,GAAA,CAAAtD,gBAAA,CAA4B;UACL/C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAAvD,SAAA,CAAY;UAiB3C9C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAuH,GAAA,CAA6B,YAAAtB,GAAA,CAAArD,SAAA;UAKEhD,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAuH,GAAA,CAAmB;UAOlD3H,EAAA,CAAAM,SAAA,GAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAwH,GAAA,CAA2B,YAAAvB,GAAA,CAAApD,OAAA;UAKIjD,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAwH,GAAA,CAAiB;UAkC/B5H,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA6H,WAAA,WAAAxB,GAAA,CAAAvF,qBAAA,OAA4C;UACvDd,EAAA,CAAAM,SAAA,GAA2D;UAA3DN,EAAA,CAAAO,iBAAA,CAAA8F,GAAA,CAAAvF,qBAAA,iCAA2D;UAC/Dd,EAAA,CAAAM,SAAA,GAAkF;UAAlFN,EAAA,CAAAO,iBAAA,CAAA8F,GAAA,CAAAvF,qBAAA,wDAAkF;UAYpFd,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAAnD,WAAA,CAAyB,aAAAmD,GAAA,CAAAvF,qBAAA;UAU3Bd,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAAiG,GAAA,CAAAnD,WAAA,CAAAe,IAAA,MAAAoC,GAAA,CAAAvF,qBAAA,SAAiE;UAOxCd,EAAA,CAAAM,SAAA,GAAwD;UAAxDN,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAAvF,qBAAA,WAAAuF,GAAA,CAAAnD,WAAA,CAAAe,IAAA,GAAwD;UA8B1DjE,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAA1D,kBAAA,CAAwB;UAe3B3C,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,UAAAiG,GAAA,CAAA1D,kBAAA,CAAyB;UA6CpB3C,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAA1D,kBAAA,CAAwB;;;qBD3O3DzD,YAAY,EAAA4I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7I,aAAa,EACbC,eAAe,EAAA6I,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,YAAA,EACf/I,aAAa,EAAAgJ,EAAA,CAAAC,OAAA,EACbhJ,kBAAkB,EAAAiJ,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAAAF,EAAA,CAAAG,SAAA,EAClBnJ,eAAe,EAAAoJ,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACftJ,cAAc,EAAAuJ,EAAA,CAAAC,QAAA,EACdvJ,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAAqJ,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBvJ,mBAAmB,EACnBC,gBAAgB,EAAAuJ,EAAA,CAAAC,UAAA,EAChBvJ,WAAW,EAAAwJ,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFtH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}