{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { mixinInitialized, mixinDisabled, AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\nimport { Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nfunction MatSortHeader_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"@arrowPosition.start\", function MatSortHeader_div_3_Template_div_animation_arrowPosition_start_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = true);\n    })(\"@arrowPosition.done\", function MatSortHeader_div_3_Template_div_animation_arrowPosition_done_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3._disableViewStateAnimation = false);\n    });\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵelement(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@arrowOpacity\", ctx_r0._getArrowViewState())(\"@arrowPosition\", ctx_r0._getArrowViewState())(\"@allowChildren\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicator\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@leftPointer\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@rightPointer\", ctx_r0._getArrowDirectionState());\n  }\n}\nconst _c1 = [\"*\"];\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to MatSort.\n/** @docs-private */\nconst _MatSortBase = mixinInitialized(mixinDisabled(class {}));\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort extends _MatSortBase {\n  /** The sort direction of the currently active MatSortable. */\n  get direction() {\n    return this._direction;\n  }\n  set direction(direction) {\n    if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  /**\n   * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n   * May be overridden by the MatSortable's disable clear input.\n   */\n  get disableClear() {\n    return this._disableClear;\n  }\n  set disableClear(v) {\n    this._disableClear = coerceBooleanProperty(v);\n  }\n  constructor(_defaultOptions) {\n    super();\n    this._defaultOptions = _defaultOptions;\n    /** Collection of all registered sortables that this directive manages. */\n    this.sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    this.start = 'asc';\n    this._direction = '';\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    this.sortChange = new EventEmitter();\n  }\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n    this.sortables.set(sortable.id, sortable);\n  }\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable) {\n    this.sortables.delete(sortable.id);\n  }\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable) {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n    this.sortChange.emit({\n      active: this.active,\n      direction: this.direction\n    });\n  }\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable) {\n    if (!sortable) {\n      return '';\n    }\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n  ngOnInit() {\n    this._markInitialized();\n  }\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  static {\n    this.ɵfac = function MatSort_Factory(t) {\n      return new (t || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSort,\n      selectors: [[\"\", \"matSort\", \"\"]],\n      hostAttrs: [1, \"mat-sort\"],\n      inputs: {\n        disabled: [\"matSortDisabled\", \"disabled\"],\n        active: [\"matSortActive\", \"active\"],\n        start: [\"matSortStart\", \"start\"],\n        direction: [\"matSortDirection\", \"direction\"],\n        disableClear: [\"matSortDisableClear\", \"disableClear\"]\n      },\n      outputs: {\n        sortChange: \"matSortChange\"\n      },\n      exportAs: [\"matSort\"],\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSort, [{\n    type: Directive,\n    args: [{\n      selector: '[matSort]',\n      exportAs: 'matSort',\n      host: {\n        'class': 'mat-sort'\n      },\n      inputs: ['disabled: matSortDisabled']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SORT_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    active: [{\n      type: Input,\n      args: ['matSortActive']\n    }],\n    start: [{\n      type: Input,\n      args: ['matSortStart']\n    }],\n    direction: [{\n      type: Input,\n      args: ['matSortDirection']\n    }],\n    disableClear: [{\n      type: Input,\n      args: ['matSortDisableClear']\n    }],\n    sortChange: [{\n      type: Output,\n      args: ['matSortChange']\n    }]\n  });\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n  /** Animation that moves the sort indicator. */\n  indicator: trigger('indicator', [state('active-asc, asc', style({\n    transform: 'translateY(0px)'\n  })),\n  // 10px is the height of the sort indicator, minus the width of the pointers\n  state('active-desc, desc', style({\n    transform: 'translateY(10px)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: trigger('leftPointer', [state('active-asc, asc', style({\n    transform: 'rotate(-45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: trigger('rightPointer', [state('active-asc, asc', style({\n    transform: 'rotate(45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(-45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: trigger('arrowOpacity', [state('desc-to-active, asc-to-active, active', style({\n    opacity: 1\n  })), state('desc-to-hint, asc-to-hint, hint', style({\n    opacity: 0.54\n  })), state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({\n    opacity: 0\n  })),\n  // Transition between all states except for immediate transitions\n  transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')), transition('* <=> *', animate(SORT_ANIMATION_TRANSITION))]),\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: trigger('arrowPosition', [\n  // Hidden Above => Hint Center\n  transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(-25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Below\n  transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(25%)'\n  })]))),\n  // Hidden Below => Hint Center\n  transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Above\n  transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(-25%)'\n  })]))), state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({\n    transform: 'translateY(0)'\n  })), state('hint-to-desc, active-to-desc, desc', style({\n    transform: 'translateY(-25%)'\n  })), state('hint-to-asc, active-to-asc, asc', style({\n    transform: 'translateY(25%)'\n  }))]),\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: trigger('allowChildren', [transition('* <=> *', [query('@*', animateChild(), {\n    optional: true\n  })])])\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    this.changes = new Subject();\n  }\n  static {\n    this.ɵfac = function MatSortHeaderIntl_Factory(t) {\n      return new (t || MatSortHeaderIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSortHeaderIntl,\n      factory: MatSortHeaderIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeaderIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n// Boilerplate for applying mixins to the sort header.\n/** @docs-private */\nconst _MatSortHeaderBase = mixinDisabled(class {});\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader extends _MatSortHeaderBase {\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  get sortActionDescription() {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value) {\n    this._updateSortActionDescription(value);\n  }\n  /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n  get disableClear() {\n    return this._disableClear;\n  }\n  set disableClear(v) {\n    this._disableClear = coerceBooleanProperty(v);\n  }\n  constructor(\n  /**\n   * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n   * @breaking-change 13.0.0\n   */\n  _intl, _changeDetectorRef,\n  // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n  // tslint:disable-next-line: lightweight-tokens\n  _sort, _columnDef, _focusMonitor, _elementRef, /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n  _ariaDescriber, defaultOptions) {\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    super();\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._sort = _sort;\n    this._columnDef = _columnDef;\n    this._focusMonitor = _focusMonitor;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    /**\n     * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n     * provide an affordance that the header is sortable by showing on focus and hover.\n     */\n    this._showIndicatorHint = false;\n    /**\n     * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n     * position through the animation. If animations are currently disabled, the fromState is removed\n     * so that there is no animation displayed.\n     */\n    this._viewState = {};\n    /** The direction the arrow should be facing according to the current state. */\n    this._arrowDirection = '';\n    /**\n     * Whether the view state animation should show the transition between the `from` and `to` states.\n     */\n    this._disableViewStateAnimation = false;\n    /** Sets the position of the arrow that displays when sorted. */\n    this.arrowPosition = 'after';\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    this._sortActionDescription = 'Sort';\n    if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n    this._handleStateChanges();\n  }\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n    // Initialize the direction of the arrow and set the view state to be immediately that state.\n    this._updateArrowDirection();\n    this._setAnimationTransitionState({\n      toState: this._isSorted() ? 'active' : this._arrowDirection\n    });\n    this._sort.register(this);\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const newState = !!origin;\n      if (newState !== this._showIndicatorHint) {\n        this._setIndicatorHintVisible(newState);\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._rerenderSubscription.unsubscribe();\n  }\n  /**\n   * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n   * user showing what the active sort will become. If set to false, the arrow will fade away.\n   */\n  _setIndicatorHintVisible(visible) {\n    // No-op if the sort header is disabled - should not make the hint visible.\n    if (this._isDisabled() && visible) {\n      return;\n    }\n    this._showIndicatorHint = visible;\n    if (!this._isSorted()) {\n      this._updateArrowDirection();\n      if (this._showIndicatorHint) {\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'hint'\n        });\n      } else {\n        this._setAnimationTransitionState({\n          fromState: 'hint',\n          toState: this._arrowDirection\n        });\n      }\n    }\n  }\n  /**\n   * Sets the animation transition view state for the arrow's position and opacity. If the\n   * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n   * no animation appears.\n   */\n  _setAnimationTransitionState(viewState) {\n    this._viewState = viewState || {};\n    // If the animation for arrow position state (opacity/translation) should be disabled,\n    // remove the fromState so that it jumps right to the toState.\n    if (this._disableViewStateAnimation) {\n      this._viewState = {\n        toState: viewState.toState\n      };\n    }\n  }\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    this._sort.sort(this);\n    // Do not show the animation if the header was already shown in the right position.\n    if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n      this._disableViewStateAnimation = true;\n    }\n  }\n  _handleClick() {\n    if (!this._isDisabled()) {\n      this._sort.sort(this);\n    }\n  }\n  _handleKeydown(event) {\n    if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n  }\n  /** Returns the animation state for the arrow direction (indicator and pointers). */\n  _getArrowDirectionState() {\n    return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n  }\n  /** Returns the arrow position state (opacity, translation). */\n  _getArrowViewState() {\n    const fromState = this._viewState.fromState;\n    return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n  }\n  /**\n   * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n   * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n   * active sorted direction. The reason this is updated through a function is because the direction\n   * should only be changed at specific times - when deactivated but the hint is displayed and when\n   * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n   * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n   * preserving its direction, even though the next sort direction is actually different and should\n   * only be changed once the arrow displays again (hint or activation).\n   */\n  _updateArrowDirection() {\n    this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n  }\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n  _updateSortActionDescription(newDescription) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n    this._sortActionDescription = newDescription;\n  }\n  /** Handles changes in the sorting state. */\n  _handleStateChanges() {\n    this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n      if (this._isSorted()) {\n        this._updateArrowDirection();\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = true;\n        }\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'active'\n        });\n        this._showIndicatorHint = false;\n      }\n      // If this header was recently active and now no longer sorted, animate away the arrow.\n      if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = false;\n        this._setAnimationTransitionState({\n          fromState: 'active',\n          toState: this._arrowDirection\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function MatSortHeader_Factory(t) {\n      return new (t || MatSortHeader)(i0.ɵɵdirectiveInject(MatSortHeaderIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MatSort, 8), i0.ɵɵdirectiveInject('MAT_SORT_HEADER_COLUMN_DEF', 8), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.AriaDescriber, 8), i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSortHeader,\n      selectors: [[\"\", \"mat-sort-header\", \"\"]],\n      hostAttrs: [1, \"mat-sort-header\"],\n      hostVars: 3,\n      hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n            return ctx._handleClick();\n          })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"mouseenter\", function MatSortHeader_mouseenter_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(true);\n          })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n          i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        id: [\"mat-sort-header\", \"id\"],\n        arrowPosition: \"arrowPosition\",\n        start: \"start\",\n        sortActionDescription: \"sortActionDescription\",\n        disableClear: \"disableClear\"\n      },\n      exportAs: [\"matSortHeader\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 7,\n      consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [\"class\", \"mat-sort-header-arrow\", 4, \"ngIf\"], [1, \"mat-sort-header-arrow\"], [1, \"mat-sort-header-stem\"], [1, \"mat-sort-header-indicator\"], [1, \"mat-sort-header-pointer-left\"], [1, \"mat-sort-header-pointer-right\"], [1, \"mat-sort-header-pointer-middle\"]],\n      template: function MatSortHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, MatSortHeader_div_3_Template, 6, 6, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\");\n          i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx._renderArrow());\n        }\n      },\n      dependencies: [i4.NgIf],\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeader, [{\n    type: Component,\n    args: [{\n      selector: '[mat-sort-header]',\n      exportAs: 'matSortHeader',\n      host: {\n        'class': 'mat-sort-header',\n        '(click)': '_handleClick()',\n        '(keydown)': '_handleKeydown($event)',\n        '(mouseenter)': '_setIndicatorHintVisible(true)',\n        '(mouseleave)': '_setIndicatorHintVisible(false)',\n        '[attr.aria-sort]': '_getAriaSortAttribute()',\n        '[class.mat-sort-header-disabled]': '_isDisabled()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled'],\n      animations: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren],\n      template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatSortHeaderIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSort,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: ['MAT_SORT_HEADER_COLUMN_DEF']\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: i3.FocusMonitor\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.AriaDescriber,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SORT_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    id: [{\n      type: Input,\n      args: ['mat-sort-header']\n    }],\n    arrowPosition: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    sortActionDescription: [{\n      type: Input\n    }],\n    disableClear: [{\n      type: Input\n    }]\n  });\n})();\nclass MatSortModule {\n  static {\n    this.ɵfac = function MatSortModule_Factory(t) {\n      return new (t || MatSortModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSortModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n      imports: [CommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule],\n      exports: [MatSort, MatSortHeader],\n      declarations: [MatSort, MatSortHeader],\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "Directive", "Optional", "Inject", "Input", "Output", "Injectable", "SkipSelf", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "i3", "coerceBooleanProperty", "SPACE", "ENTER", "mixinInitialized", "mixinDisabled", "AnimationDurations", "AnimationCurves", "MatCommonModule", "Subject", "merge", "trigger", "state", "style", "transition", "animate", "keyframes", "query", "animate<PERSON><PERSON><PERSON>", "i4", "CommonModule", "_c0", "MatSortHeader_div_3_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatSortHeader_div_3_Template_div_animation_arrowPosition_start_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_disableViewStateAnimation", "MatSortHeader_div_3_Template_div_animation_arrowPosition_done_0_listener", "ctx_r3", "ɵɵelement", "ɵɵelementEnd", "ctx_r0", "ɵɵproperty", "_getArrowViewState", "_getArrowDirectionState", "ɵɵadvance", "_c1", "getSortDuplicateSortableIdError", "id", "Error", "getSortHeaderNotContainedWithinSortError", "getSortHeaderMissingIdError", "getSortInvalidDirectionError", "direction", "MAT_SORT_DEFAULT_OPTIONS", "_MatSortBase", "MatSort", "_direction", "ngDevMode", "disableClear", "_disableClear", "v", "constructor", "_defaultOptions", "sortables", "Map", "_stateChanges", "start", "sortChange", "register", "sortable", "has", "set", "deregister", "delete", "sort", "active", "getNextSortDirection", "emit", "sortDirectionCycle", "getSortDirectionCycle", "nextDirectionIndex", "indexOf", "length", "ngOnInit", "_markInitialized", "ngOnChanges", "next", "ngOnDestroy", "complete", "ɵfac", "MatSort_Factory", "t", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "disabled", "outputs", "exportAs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "host", "undefined", "decorators", "sortOrder", "reverse", "push", "SORT_ANIMATION_TRANSITION", "ENTERING", "STANDARD_CURVE", "matSortAnimations", "indicator", "transform", "leftPointer", "rightPointer", "arrowOpacity", "opacity", "arrowPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional", "MatSortHeaderIntl", "changes", "MatSortHeaderIntl_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "MAT_SORT_HEADER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_SORT_HEADER_INTL_PROVIDER", "provide", "deps", "useFactory", "_MatSortHeaderBase", "Mat<PERSON>ort<PERSON><PERSON>er", "sortActionDescription", "_sortActionDescription", "value", "_updateSortActionDescription", "_intl", "_changeDetectorRef", "_sort", "_columnDef", "_focusMonitor", "_elementRef", "_ariaDescriber", "defaultOptions", "_showIndicatorHint", "_viewState", "_arrowDirection", "_handleStateChanges", "name", "_updateArrowDirection", "_setAnimationTransitionState", "toState", "_isSorted", "_sortButton", "nativeElement", "querySelector", "ngAfterViewInit", "monitor", "subscribe", "origin", "newState", "_setIndicatorHintVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stopMonitoring", "_rerenderSubscription", "unsubscribe", "visible", "_isDisabled", "fromState", "viewState", "_toggleOnInteraction", "_handleClick", "_handleKeydown", "event", "keyCode", "preventDefault", "_getAriaSortAttribute", "_renderArrow", "newDescription", "removeDescription", "describe", "MatSortHeader_Factory", "ChangeDetectorRef", "FocusMonitor", "ElementRef", "AriaDescriber", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "MatSortHeader_HostBindings", "MatSortHeader_click_HostBindingHandler", "MatSortHeader_keydown_HostBindingHandler", "$event", "MatSortHeader_mouseenter_HostBindingHandler", "MatSortHeader_mouseleave_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSortHeader_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵtemplate", "dependencies", "NgIf", "styles", "encapsulation", "data", "animation", "changeDetection", "None", "OnPush", "animations", "MatSortModule", "MatSortModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports", "declarations"], "sources": ["/home/<USER>/other/digi/digitorywebv4/node_modules/@angular/material/fesm2022/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { mixinInitialized, mixinDisabled, AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\nimport { Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n    return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n    return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to MatSort.\n/** @docs-private */\nconst _MatSortBase = mixinInitialized(mixinDisabled(class {\n}));\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort extends _MatSortBase {\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n        return this._direction;\n    }\n    set direction(direction) {\n        if (direction &&\n            direction !== 'asc' &&\n            direction !== 'desc' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortInvalidDirectionError(direction);\n        }\n        this._direction = direction;\n    }\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    get disableClear() {\n        return this._disableClear;\n    }\n    set disableClear(v) {\n        this._disableClear = coerceBooleanProperty(v);\n    }\n    constructor(_defaultOptions) {\n        super();\n        this._defaultOptions = _defaultOptions;\n        /** Collection of all registered sortables that this directive manages. */\n        this.sortables = new Map();\n        /** Used to notify any child components listening to state changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The direction to set when an MatSortable is initially sorted.\n         * May be overridden by the MatSortable's sort start.\n         */\n        this.start = 'asc';\n        this._direction = '';\n        /** Event emitted when the user changes either the active sort or sort direction. */\n        this.sortChange = new EventEmitter();\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!sortable.id) {\n                throw getSortHeaderMissingIdError();\n            }\n            if (this.sortables.has(sortable.id)) {\n                throw getSortDuplicateSortableIdError(sortable.id);\n            }\n        }\n        this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n        this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n        if (this.active != sortable.id) {\n            this.active = sortable.id;\n            this.direction = sortable.start ? sortable.start : this.start;\n        }\n        else {\n            this.direction = this.getNextSortDirection(sortable);\n        }\n        this.sortChange.emit({ active: this.active, direction: this.direction });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n        if (!sortable) {\n            return '';\n        }\n        // Get the sort direction cycle with the potential sortable overrides.\n        const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n        let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n        // Get and return the next direction in the cycle\n        let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n        if (nextDirectionIndex >= sortDirectionCycle.length) {\n            nextDirectionIndex = 0;\n        }\n        return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n        this._markInitialized();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSort, deps: [{ token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSort, selector: \"[matSort]\", inputs: { disabled: [\"matSortDisabled\", \"disabled\"], active: [\"matSortActive\", \"active\"], start: [\"matSortStart\", \"start\"], direction: [\"matSortDirection\", \"direction\"], disableClear: [\"matSortDisableClear\", \"disableClear\"] }, outputs: { sortChange: \"matSortChange\" }, host: { classAttribute: \"mat-sort\" }, exportAs: [\"matSort\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSort, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSort]',\n                    exportAs: 'matSort',\n                    host: {\n                        'class': 'mat-sort',\n                    },\n                    inputs: ['disabled: matSortDisabled'],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { active: [{\n                type: Input,\n                args: ['matSortActive']\n            }], start: [{\n                type: Input,\n                args: ['matSortStart']\n            }], direction: [{\n                type: Input,\n                args: ['matSortDirection']\n            }], disableClear: [{\n                type: Input,\n                args: ['matSortDisableClear']\n            }], sortChange: [{\n                type: Output,\n                args: ['matSortChange']\n            }] } });\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n    let sortOrder = ['asc', 'desc'];\n    if (start == 'desc') {\n        sortOrder.reverse();\n    }\n    if (!disableClear) {\n        sortOrder.push('');\n    }\n    return sortOrder;\n}\n\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n    /** Animation that moves the sort indicator. */\n    indicator: trigger('indicator', [\n        state('active-asc, asc', style({ transform: 'translateY(0px)' })),\n        // 10px is the height of the sort indicator, minus the width of the pointers\n        state('active-desc, desc', style({ transform: 'translateY(10px)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n    leftPointer: trigger('leftPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(-45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n    rightPointer: trigger('rightPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(-45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that controls the arrow opacity. */\n    arrowOpacity: trigger('arrowOpacity', [\n        state('desc-to-active, asc-to-active, active', style({ opacity: 1 })),\n        state('desc-to-hint, asc-to-hint, hint', style({ opacity: 0.54 })),\n        state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({ opacity: 0 })),\n        // Transition between all states except for immediate transitions\n        transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n        transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /**\n     * Animation for the translation of the arrow as a whole. States are separated into two\n     * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n     * peek, and active. The other states define a specific animation (source-to-destination)\n     * and are determined as a function of their prev user-perceived state and what the next state\n     * should be.\n     */\n    arrowPosition: trigger('arrowPosition', [\n        // Hidden Above => Hint Center\n        transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(-25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Below\n        transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(25%)' })]))),\n        // Hidden Below => Hint Center\n        transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Above\n        transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(-25%)' })]))),\n        state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({ transform: 'translateY(0)' })),\n        state('hint-to-desc, active-to-desc, desc', style({ transform: 'translateY(-25%)' })),\n        state('hint-to-asc, active-to-asc, asc', style({ transform: 'translateY(25%)' })),\n    ]),\n    /** Necessary trigger that calls animate on children animations. */\n    allowChildren: trigger('allowChildren', [\n        transition('* <=> *', [query('@*', animateChild(), { optional: true })]),\n    ]),\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n    constructor() {\n        /**\n         * Stream that emits whenever the labels here are changed. Use this to notify\n         * components if the labels have changed after initialization.\n         */\n        this.changes = new Subject();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortHeaderIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortHeaderIntl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortHeaderIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n    // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n    provide: MatSortHeaderIntl,\n    deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n    useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n\n// Boilerplate for applying mixins to the sort header.\n/** @docs-private */\nconst _MatSortHeaderBase = mixinDisabled(class {\n});\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader extends _MatSortHeaderBase {\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n        return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n        this._updateSortActionDescription(value);\n    }\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    get disableClear() {\n        return this._disableClear;\n    }\n    set disableClear(v) {\n        this._disableClear = coerceBooleanProperty(v);\n    }\n    constructor(\n    /**\n     * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n     * @breaking-change 13.0.0\n     */\n    _intl, _changeDetectorRef, \n    // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n    // tslint:disable-next-line: lightweight-tokens\n    _sort, _columnDef, _focusMonitor, _elementRef, \n    /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n    _ariaDescriber, defaultOptions) {\n        // Note that we use a string token for the `_columnDef`, because the value is provided both by\n        // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n        // and we want to avoid having the sort header depending on the CDK table because\n        // of this single reference.\n        super();\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._sort = _sort;\n        this._columnDef = _columnDef;\n        this._focusMonitor = _focusMonitor;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        /**\n         * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n         * provide an affordance that the header is sortable by showing on focus and hover.\n         */\n        this._showIndicatorHint = false;\n        /**\n         * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n         * position through the animation. If animations are currently disabled, the fromState is removed\n         * so that there is no animation displayed.\n         */\n        this._viewState = {};\n        /** The direction the arrow should be facing according to the current state. */\n        this._arrowDirection = '';\n        /**\n         * Whether the view state animation should show the transition between the `from` and `to` states.\n         */\n        this._disableViewStateAnimation = false;\n        /** Sets the position of the arrow that displays when sorted. */\n        this.arrowPosition = 'after';\n        // Default the action description to \"Sort\" because it's better than nothing.\n        // Without a description, the button's label comes from the sort header text content,\n        // which doesn't give any indication that it performs a sorting operation.\n        this._sortActionDescription = 'Sort';\n        if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortHeaderNotContainedWithinSortError();\n        }\n        if (defaultOptions?.arrowPosition) {\n            this.arrowPosition = defaultOptions?.arrowPosition;\n        }\n        this._handleStateChanges();\n    }\n    ngOnInit() {\n        if (!this.id && this._columnDef) {\n            this.id = this._columnDef.name;\n        }\n        // Initialize the direction of the arrow and set the view state to be immediately that state.\n        this._updateArrowDirection();\n        this._setAnimationTransitionState({\n            toState: this._isSorted() ? 'active' : this._arrowDirection,\n        });\n        this._sort.register(this);\n        this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n        this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n        // We use the focus monitor because we also want to style\n        // things differently based on the focus origin.\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n            const newState = !!origin;\n            if (newState !== this._showIndicatorHint) {\n                this._setIndicatorHintVisible(newState);\n                this._changeDetectorRef.markForCheck();\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._sort.deregister(this);\n        this._rerenderSubscription.unsubscribe();\n    }\n    /**\n     * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n     * user showing what the active sort will become. If set to false, the arrow will fade away.\n     */\n    _setIndicatorHintVisible(visible) {\n        // No-op if the sort header is disabled - should not make the hint visible.\n        if (this._isDisabled() && visible) {\n            return;\n        }\n        this._showIndicatorHint = visible;\n        if (!this._isSorted()) {\n            this._updateArrowDirection();\n            if (this._showIndicatorHint) {\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'hint' });\n            }\n            else {\n                this._setAnimationTransitionState({ fromState: 'hint', toState: this._arrowDirection });\n            }\n        }\n    }\n    /**\n     * Sets the animation transition view state for the arrow's position and opacity. If the\n     * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n     * no animation appears.\n     */\n    _setAnimationTransitionState(viewState) {\n        this._viewState = viewState || {};\n        // If the animation for arrow position state (opacity/translation) should be disabled,\n        // remove the fromState so that it jumps right to the toState.\n        if (this._disableViewStateAnimation) {\n            this._viewState = { toState: viewState.toState };\n        }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n        this._sort.sort(this);\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n            this._disableViewStateAnimation = true;\n        }\n    }\n    _handleClick() {\n        if (!this._isDisabled()) {\n            this._sort.sort(this);\n        }\n    }\n    _handleKeydown(event) {\n        if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n            event.preventDefault();\n            this._toggleOnInteraction();\n        }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n        return (this._sort.active == this.id &&\n            (this._sort.direction === 'asc' || this._sort.direction === 'desc'));\n    }\n    /** Returns the animation state for the arrow direction (indicator and pointers). */\n    _getArrowDirectionState() {\n        return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n    }\n    /** Returns the arrow position state (opacity, translation). */\n    _getArrowViewState() {\n        const fromState = this._viewState.fromState;\n        return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n    }\n    /**\n     * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n     * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n     * active sorted direction. The reason this is updated through a function is because the direction\n     * should only be changed at specific times - when deactivated but the hint is displayed and when\n     * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n     * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n     * preserving its direction, even though the next sort direction is actually different and should\n     * only be changed once the arrow displays again (hint or activation).\n     */\n    _updateArrowDirection() {\n        this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n    }\n    _isDisabled() {\n        return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n        if (!this._isSorted()) {\n            return 'none';\n        }\n        return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n        return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n        // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n        // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n        // for every *cell* in the table, creating a lot of unnecessary noise.\n        // If _sortButton is undefined, the component hasn't been initialized yet so there's\n        // nothing to update in the DOM.\n        if (this._sortButton) {\n            // removeDescription will no-op if there is no existing message.\n            // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n            this._ariaDescriber?.describe(this._sortButton, newDescription);\n        }\n        this._sortActionDescription = newDescription;\n    }\n    /** Handles changes in the sorting state. */\n    _handleStateChanges() {\n        this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n            if (this._isSorted()) {\n                this._updateArrowDirection();\n                // Do not show the animation if the header was already shown in the right position.\n                if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n                    this._disableViewStateAnimation = true;\n                }\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'active' });\n                this._showIndicatorHint = false;\n            }\n            // If this header was recently active and now no longer sorted, animate away the arrow.\n            if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n                this._disableViewStateAnimation = false;\n                this._setAnimationTransitionState({ fromState: 'active', toState: this._arrowDirection });\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortHeader, deps: [{ token: MatSortHeaderIntl }, { token: i0.ChangeDetectorRef }, { token: MatSort, optional: true }, { token: 'MAT_SORT_HEADER_COLUMN_DEF', optional: true }, { token: i3.FocusMonitor }, { token: i0.ElementRef }, { token: i3.AriaDescriber, optional: true }, { token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSortHeader, selector: \"[mat-sort-header]\", inputs: { disabled: \"disabled\", id: [\"mat-sort-header\", \"id\"], arrowPosition: \"arrowPosition\", start: \"start\", sortActionDescription: \"sortActionDescription\", disableClear: \"disableClear\" }, host: { listeners: { \"click\": \"_handleClick()\", \"keydown\": \"_handleKeydown($event)\", \"mouseenter\": \"_setIndicatorHintVisible(true)\", \"mouseleave\": \"_setIndicatorHintVisible(false)\" }, properties: { \"attr.aria-sort\": \"_getAriaSortAttribute()\", \"class.mat-sort-header-disabled\": \"_isDisabled()\" }, classAttribute: \"mat-sort-header\" }, exportAs: [\"matSortHeader\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"], dependencies: [{ kind: \"directive\", type: i4.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [\n            matSortAnimations.indicator,\n            matSortAnimations.leftPointer,\n            matSortAnimations.rightPointer,\n            matSortAnimations.arrowOpacity,\n            matSortAnimations.arrowPosition,\n            matSortAnimations.allowChildren,\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortHeader, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-sort-header]', exportAs: 'matSortHeader', host: {\n                        'class': 'mat-sort-header',\n                        '(click)': '_handleClick()',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(mouseenter)': '_setIndicatorHintVisible(true)',\n                        '(mouseleave)': '_setIndicatorHintVisible(false)',\n                        '[attr.aria-sort]': '_getAriaSortAttribute()',\n                        '[class.mat-sort-header-disabled]': '_isDisabled()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled'], animations: [\n                        matSortAnimations.indicator,\n                        matSortAnimations.leftPointer,\n                        matSortAnimations.rightPointer,\n                        matSortAnimations.arrowOpacity,\n                        matSortAnimations.arrowPosition,\n                        matSortAnimations.allowChildren,\n                    ], template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"] }]\n        }], ctorParameters: function () { return [{ type: MatSortHeaderIntl }, { type: i0.ChangeDetectorRef }, { type: MatSort, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: ['MAT_SORT_HEADER_COLUMN_DEF']\n                }, {\n                    type: Optional\n                }] }, { type: i3.FocusMonitor }, { type: i0.ElementRef }, { type: i3.AriaDescriber, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { id: [{\n                type: Input,\n                args: ['mat-sort-header']\n            }], arrowPosition: [{\n                type: Input\n            }], start: [{\n                type: Input\n            }], sortActionDescription: [{\n                type: Input\n            }], disableClear: [{\n                type: Input\n            }] } });\n\nclass MatSortModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortModule, declarations: [MatSort, MatSortHeader], imports: [CommonModule, MatCommonModule], exports: [MatSort, MatSortHeader] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortModule, providers: [MAT_SORT_HEADER_INTL_PROVIDER], imports: [CommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSortModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule],\n                    exports: [MatSort, MatSortHeader],\n                    declarations: [MatSort, MatSortHeader],\n                    providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,eAAe;AAC/L,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC9H,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAQ,qBAAqB;AAChH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AAAA,MAAAC,GAAA;AAAA,SAAAC,6BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAyHoGvC,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,YAgY+9E,CAAC;IAhYl+EzC,EAAE,CAAA0C,UAAA,kCAAAC,0EAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAAAF,MAAA,CAAAG,0BAAA,GAgYo5E,IAAI;IAAA,CAAC,CAAC,iCAAAC,yEAAA;MAhY55EjD,EAAE,CAAA4C,aAAA,CAAAL,GAAA;MAAA,MAAAW,MAAA,GAAFlD,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAAAG,MAAA,CAAAF,0BAAA,GAgYw9E,KAAK;IAAA,CAApE,CAAC;IAhY55EhD,EAAE,CAAAmD,SAAA,YAgY+gF,CAAC;IAhYlhFnD,EAAE,CAAAyC,cAAA,YAgYymF,CAAC;IAhY5mFzC,EAAE,CAAAmD,SAAA,YAgYgtF,CAAC,YAAD,CAAC,YAAD,CAAC;IAhYntFnD,EAAE,CAAAoD,YAAA,CAgYi4F,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GAhYp4FrD,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAsD,UAAA,kBAAAD,MAAA,CAAAE,kBAAA,EAgY2uE,CAAC,mBAAAF,MAAA,CAAAE,kBAAA,EAAD,CAAC,mBAAAF,MAAA,CAAAG,uBAAA,EAAD,CAAC;IAhY9uExD,EAAE,CAAAyD,SAAA,EAgYwmF,CAAC;IAhY3mFzD,EAAE,CAAAsD,UAAA,eAAAD,MAAA,CAAAG,uBAAA,EAgYwmF,CAAC;IAhY3mFxD,EAAE,CAAAyD,SAAA,EAgYysF,CAAC;IAhY5sFzD,EAAE,CAAAsD,UAAA,iBAAAD,MAAA,CAAAG,uBAAA,EAgYysF,CAAC;IAhY5sFxD,EAAE,CAAAyD,SAAA,EAgYkzF,CAAC;IAhYrzFzD,EAAE,CAAAsD,UAAA,kBAAAD,MAAA,CAAAG,uBAAA,EAgYkzF,CAAC;EAAA;AAAA;AAAA,MAAAE,GAAA;AAxfz5F,SAASC,+BAA+BA,CAACC,EAAE,EAAE;EACzC,OAAOC,KAAK,CAAE,kDAAiDD,EAAG,IAAG,CAAC;AAC1E;AACA;AACA,SAASE,wCAAwCA,CAAA,EAAG;EAChD,OAAOD,KAAK,CAAE,kFAAiF,CAAC;AACpG;AACA;AACA,SAASE,2BAA2BA,CAAA,EAAG;EACnC,OAAOF,KAAK,CAAE,kDAAiD,CAAC;AACpE;AACA;AACA,SAASG,4BAA4BA,CAACC,SAAS,EAAE;EAC7C,OAAOJ,KAAK,CAAE,GAAEI,SAAU,mDAAkD,CAAC;AACjF;;AAEA;AACA,MAAMC,wBAAwB,GAAG,IAAIjE,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA;AACA,MAAMkE,YAAY,GAAGjD,gBAAgB,CAACC,aAAa,CAAC,MAAM,EACzD,CAAC,CAAC;AACH;AACA,MAAMiD,OAAO,SAASD,YAAY,CAAC;EAC/B;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;EACA,IAAIJ,SAASA,CAACA,SAAS,EAAE;IACrB,IAAIA,SAAS,IACTA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,MAAM,KACnB,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMN,4BAA4B,CAACC,SAAS,CAAC;IACjD;IACA,IAAI,CAACI,UAAU,GAAGJ,SAAS;EAC/B;EACA;AACJ;AACA;AACA;EACI,IAAIM,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACE,CAAC,EAAE;IAChB,IAAI,CAACD,aAAa,GAAGzD,qBAAqB,CAAC0D,CAAC,CAAC;EACjD;EACAC,WAAWA,CAACC,eAAe,EAAE;IACzB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,IAAIvD,OAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACwD,KAAK,GAAG,KAAK;IAClB,IAAI,CAACV,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACW,UAAU,GAAG,IAAI9E,YAAY,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACI+E,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,OAAOZ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACY,QAAQ,CAACtB,EAAE,EAAE;QACd,MAAMG,2BAA2B,CAAC,CAAC;MACvC;MACA,IAAI,IAAI,CAACa,SAAS,CAACO,GAAG,CAACD,QAAQ,CAACtB,EAAE,CAAC,EAAE;QACjC,MAAMD,+BAA+B,CAACuB,QAAQ,CAACtB,EAAE,CAAC;MACtD;IACJ;IACA,IAAI,CAACgB,SAAS,CAACQ,GAAG,CAACF,QAAQ,CAACtB,EAAE,EAAEsB,QAAQ,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIG,UAAUA,CAACH,QAAQ,EAAE;IACjB,IAAI,CAACN,SAAS,CAACU,MAAM,CAACJ,QAAQ,CAACtB,EAAE,CAAC;EACtC;EACA;EACA2B,IAAIA,CAACL,QAAQ,EAAE;IACX,IAAI,IAAI,CAACM,MAAM,IAAIN,QAAQ,CAACtB,EAAE,EAAE;MAC5B,IAAI,CAAC4B,MAAM,GAAGN,QAAQ,CAACtB,EAAE;MACzB,IAAI,CAACK,SAAS,GAAGiB,QAAQ,CAACH,KAAK,GAAGG,QAAQ,CAACH,KAAK,GAAG,IAAI,CAACA,KAAK;IACjE,CAAC,MACI;MACD,IAAI,CAACd,SAAS,GAAG,IAAI,CAACwB,oBAAoB,CAACP,QAAQ,CAAC;IACxD;IACA,IAAI,CAACF,UAAU,CAACU,IAAI,CAAC;MAAEF,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEvB,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,CAAC;EAC5E;EACA;EACAwB,oBAAoBA,CAACP,QAAQ,EAAE;IAC3B,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,EAAE;IACb;IACA;IACA,MAAMX,YAAY,GAAGW,QAAQ,EAAEX,YAAY,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,CAAC,IAAI,CAACI,eAAe,EAAEJ,YAAY;IACxG,IAAIoB,kBAAkB,GAAGC,qBAAqB,CAACV,QAAQ,CAACH,KAAK,IAAI,IAAI,CAACA,KAAK,EAAER,YAAY,CAAC;IAC1F;IACA,IAAIsB,kBAAkB,GAAGF,kBAAkB,CAACG,OAAO,CAAC,IAAI,CAAC7B,SAAS,CAAC,GAAG,CAAC;IACvE,IAAI4B,kBAAkB,IAAIF,kBAAkB,CAACI,MAAM,EAAE;MACjDF,kBAAkB,GAAG,CAAC;IAC1B;IACA,OAAOF,kBAAkB,CAACE,kBAAkB,CAAC;EACjD;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,aAAa,CAACqB,IAAI,CAAC,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtB,aAAa,CAACuB,QAAQ,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFpC,OAAO,EAAjBpE,EAAE,CAAAyG,iBAAA,CAAiCvC,wBAAwB;IAAA,CAA4D;EAAE;EACzN;IAAS,IAAI,CAACwC,IAAI,kBAD8E1G,EAAE,CAAA2G,iBAAA;MAAAC,IAAA,EACJxC,OAAO;MAAAyC,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAAC,QAAA;QAAAxB,MAAA;QAAAT,KAAA;QAAAd,SAAA;QAAAM,YAAA;MAAA;MAAA0C,OAAA;QAAAjC,UAAA;MAAA;MAAAkC,QAAA;MAAAC,QAAA,GADLnH,EAAE,CAAAoH,0BAAA,EAAFpH,EAAE,CAAAqH,oBAAA;IAAA,EACia;EAAE;AACzgB;AACA;EAAA,QAAA/C,SAAA,oBAAAA,SAAA,KAHoGtE,EAAE,CAAAsH,iBAAA,CAGXlD,OAAO,EAAc,CAAC;IACrGwC,IAAI,EAAEzG,SAAS;IACfoH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBN,QAAQ,EAAE,SAAS;MACnBO,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDV,MAAM,EAAE,CAAC,2BAA2B;IACxC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEc,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9Df,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEvG,MAAM;QACZkH,IAAI,EAAE,CAACrD,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsB,MAAM,EAAE,CAAC;MACrCoB,IAAI,EAAEtG,KAAK;MACXiH,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAExC,KAAK,EAAE,CAAC;MACR6B,IAAI,EAAEtG,KAAK;MACXiH,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEtD,SAAS,EAAE,CAAC;MACZ2C,IAAI,EAAEtG,KAAK;MACXiH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEhD,YAAY,EAAE,CAAC;MACfqC,IAAI,EAAEtG,KAAK;MACXiH,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEvC,UAAU,EAAE,CAAC;MACb4B,IAAI,EAAErG,MAAM;MACZgH,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS3B,qBAAqBA,CAACb,KAAK,EAAER,YAAY,EAAE;EAChD,IAAIqD,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EAC/B,IAAI7C,KAAK,IAAI,MAAM,EAAE;IACjB6C,SAAS,CAACC,OAAO,CAAC,CAAC;EACvB;EACA,IAAI,CAACtD,YAAY,EAAE;IACfqD,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,OAAOF,SAAS;AACpB;AAEA,MAAMG,yBAAyB,GAAG3G,kBAAkB,CAAC4G,QAAQ,GAAG,GAAG,GAAG3G,eAAe,CAAC4G,cAAc;AACpG;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG;EACtB;EACAC,SAAS,EAAE1G,OAAO,CAAC,WAAW,EAAE,CAC5BC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC;EACjE;EACA1G,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACpExG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACkG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAM,WAAW,EAAE5G,OAAO,CAAC,aAAa,EAAE,CAChCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAChE1G,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACjExG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACkG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAO,YAAY,EAAE7G,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EAC/D1G,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAClExG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACkG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAQ,YAAY,EAAE9G,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,uCAAuC,EAAEC,KAAK,CAAC;IAAE6G,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACrE9G,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAE6G,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC,EAClE9G,KAAK,CAAC,2EAA2E,EAAEC,KAAK,CAAC;IAAE6G,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC;EACzG;EACA5G,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,EACpFD,UAAU,CAAC,SAAS,EAAEC,OAAO,CAACkG,yBAAyB,CAAC,CAAC,CAC5D,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,aAAa,EAAEhH,OAAO,CAAC,eAAe,EAAE;EACpC;EACAG,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAACkG,yBAAyB,EAAEjG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAmB,CAAC,CAAC,EAAEzG,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtL;EACAxG,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAACkG,yBAAyB,EAAEjG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAEzG,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrL;EACAxG,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAACkG,yBAAyB,EAAEjG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAkB,CAAC,CAAC,EAAEzG,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnL;EACAxG,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAACkG,yBAAyB,EAAEjG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAEzG,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpL1G,KAAK,CAAC,wEAAwE,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACtH1G,KAAK,CAAC,oCAAoC,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACrF1G,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CACpF,CAAC;EACF;EACAM,aAAa,EAAEjH,OAAO,CAAC,eAAe,EAAE,CACpCG,UAAU,CAAC,SAAS,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAE2G,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAAC,CAC3E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBlE,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACmE,OAAO,GAAG,IAAItH,OAAO,CAAC,CAAC;EAChC;EACA;IAAS,IAAI,CAAC+E,IAAI,YAAAwC,0BAAAtC,CAAA;MAAA,YAAAA,CAAA,IAAwFoC,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACG,KAAK,kBAvH6E/I,EAAE,CAAAgJ,kBAAA;MAAAC,KAAA,EAuHYL,iBAAiB;MAAAM,OAAA,EAAjBN,iBAAiB,CAAAtC,IAAA;MAAA6C,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAA7E,SAAA,oBAAAA,SAAA,KAzHoGtE,EAAE,CAAAsH,iBAAA,CAyHXsB,iBAAiB,EAAc,CAAC;IAC/GhC,IAAI,EAAEpG,UAAU;IAChB+G,IAAI,EAAE,CAAC;MAAE4B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASC,qCAAqCA,CAACC,UAAU,EAAE;EACvD,OAAOA,UAAU,IAAI,IAAIT,iBAAiB,CAAC,CAAC;AAChD;AACA;AACA,MAAMU,6BAA6B,GAAG;EAClC;EACAC,OAAO,EAAEX,iBAAiB;EAC1BY,IAAI,EAAE,CAAC,CAAC,IAAIpJ,QAAQ,CAAC,CAAC,EAAE,IAAIK,QAAQ,CAAC,CAAC,EAAEmI,iBAAiB,CAAC,CAAC;EAC3Da,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA,MAAMM,kBAAkB,GAAGvI,aAAa,CAAC,MAAM,EAC9C,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwI,aAAa,SAASD,kBAAkB,CAAC;EAC3C;AACJ;AACA;AACA;EACI,IAAIE,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACE,KAAK,EAAE;IAC7B,IAAI,CAACC,4BAA4B,CAACD,KAAK,CAAC;EAC5C;EACA;EACA,IAAIvF,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACE,CAAC,EAAE;IAChB,IAAI,CAACD,aAAa,GAAGzD,qBAAqB,CAAC0D,CAAC,CAAC;EACjD;EACAC,WAAWA;EACX;AACJ;AACA;AACA;EACIsF,KAAK,EAAEC,kBAAkB;EACzB;EACA;EACAC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAC7C;EACAC,cAAc,EAAEC,cAAc,EAAE;IAC5B;IACA;IACA;IACA;IACA,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;IACQ,IAAI,CAAC1H,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAI,CAACyF,aAAa,GAAG,OAAO;IAC5B;IACA;IACA;IACA,IAAI,CAACoB,sBAAsB,GAAG,MAAM;IACpC,IAAI,CAACK,KAAK,KAAK,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3D,MAAMR,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAIyG,cAAc,EAAE9B,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAG8B,cAAc,EAAE9B,aAAa;IACtD;IACA,IAAI,CAACkC,mBAAmB,CAAC,CAAC;EAC9B;EACA3E,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACpC,EAAE,IAAI,IAAI,CAACuG,UAAU,EAAE;MAC7B,IAAI,CAACvG,EAAE,GAAG,IAAI,CAACuG,UAAU,CAACS,IAAI;IAClC;IACA;IACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,4BAA4B,CAAC;MAC9BC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI,CAACN;IAChD,CAAC,CAAC;IACF,IAAI,CAACR,KAAK,CAACjF,QAAQ,CAAC,IAAI,CAAC;IACzB,IAAI,CAACgG,WAAW,GAAG,IAAI,CAACZ,WAAW,CAACa,aAAa,CAACC,aAAa,CAAC,4BAA4B,CAAC;IAC7F,IAAI,CAACpB,4BAA4B,CAAC,IAAI,CAACF,sBAAsB,CAAC;EAClE;EACAuB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAChB,aAAa,CAACiB,OAAO,CAAC,IAAI,CAAChB,WAAW,EAAE,IAAI,CAAC,CAACiB,SAAS,CAACC,MAAM,IAAI;MACnE,MAAMC,QAAQ,GAAG,CAAC,CAACD,MAAM;MACzB,IAAIC,QAAQ,KAAK,IAAI,CAAChB,kBAAkB,EAAE;QACtC,IAAI,CAACiB,wBAAwB,CAACD,QAAQ,CAAC;QACvC,IAAI,CAACvB,kBAAkB,CAACyB,YAAY,CAAC,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAtF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgE,aAAa,CAACuB,cAAc,CAAC,IAAI,CAACtB,WAAW,CAAC;IACnD,IAAI,CAACH,KAAK,CAAC7E,UAAU,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACuG,qBAAqB,CAACC,WAAW,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIJ,wBAAwBA,CAACK,OAAO,EAAE;IAC9B;IACA,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,IAAID,OAAO,EAAE;MAC/B;IACJ;IACA,IAAI,CAACtB,kBAAkB,GAAGsB,OAAO;IACjC,IAAI,CAAC,IAAI,CAACd,SAAS,CAAC,CAAC,EAAE;MACnB,IAAI,CAACH,qBAAqB,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACL,kBAAkB,EAAE;QACzB,IAAI,CAACM,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,IAAI,CAACtB,eAAe;UAAEK,OAAO,EAAE;QAAO,CAAC,CAAC;MAC3F,CAAC,MACI;QACD,IAAI,CAACD,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,MAAM;UAAEjB,OAAO,EAAE,IAAI,CAACL;QAAgB,CAAC,CAAC;MAC3F;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACII,4BAA4BA,CAACmB,SAAS,EAAE;IACpC,IAAI,CAACxB,UAAU,GAAGwB,SAAS,IAAI,CAAC,CAAC;IACjC;IACA;IACA,IAAI,IAAI,CAACjJ,0BAA0B,EAAE;MACjC,IAAI,CAACyH,UAAU,GAAG;QAAEM,OAAO,EAAEkB,SAAS,CAAClB;MAAQ,CAAC;IACpD;EACJ;EACA;EACAmB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAChC,KAAK,CAAC3E,IAAI,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,IAAI,CAACkF,UAAU,CAACM,OAAO,KAAK,MAAM,IAAI,IAAI,CAACN,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;MAC5E,IAAI,CAAC/H,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACAmJ,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC7B,KAAK,CAAC3E,IAAI,CAAC,IAAI,CAAC;IACzB;EACJ;EACA6G,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,KAAKM,KAAK,CAACC,OAAO,KAAKtL,KAAK,IAAIqL,KAAK,CAACC,OAAO,KAAKrL,KAAK,CAAC,EAAE;MAC7EoL,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB,IAAI,CAACL,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACAlB,SAASA,CAAA,EAAG;IACR,OAAQ,IAAI,CAACd,KAAK,CAAC1E,MAAM,IAAI,IAAI,CAAC5B,EAAE,KAC/B,IAAI,CAACsG,KAAK,CAACjG,SAAS,KAAK,KAAK,IAAI,IAAI,CAACiG,KAAK,CAACjG,SAAS,KAAK,MAAM,CAAC;EAC3E;EACA;EACAT,uBAAuBA,CAAA,EAAG;IACtB,OAAQ,GAAE,IAAI,CAACwH,SAAS,CAAC,CAAC,GAAG,SAAS,GAAG,EAAG,GAAE,IAAI,CAACN,eAAgB,EAAC;EACxE;EACA;EACAnH,kBAAkBA,CAAA,EAAG;IACjB,MAAMyI,SAAS,GAAG,IAAI,CAACvB,UAAU,CAACuB,SAAS;IAC3C,OAAO,CAACA,SAAS,GAAI,GAAEA,SAAU,MAAK,GAAG,EAAE,IAAI,IAAI,CAACvB,UAAU,CAACM,OAAO;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACH,eAAe,GAAG,IAAI,CAACM,SAAS,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,CAACjG,SAAS,GAAG,IAAI,CAACc,KAAK,IAAI,IAAI,CAACmF,KAAK,CAACnF,KAAK;EACnG;EACAgH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7B,KAAK,CAAClD,QAAQ,IAAI,IAAI,CAACA,QAAQ;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACxB,SAAS,CAAC,CAAC,EAAE;MACnB,OAAO,MAAM;IACjB;IACA,OAAO,IAAI,CAACd,KAAK,CAACjG,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;EACrE;EACA;EACAwI,YAAYA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAACV,WAAW,CAAC,CAAC,IAAI,IAAI,CAACf,SAAS,CAAC,CAAC;EAClD;EACAjB,4BAA4BA,CAAC2C,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzB,WAAW,EAAE;MAClB;MACA;MACA,IAAI,CAACX,cAAc,EAAEqC,iBAAiB,CAAC,IAAI,CAAC1B,WAAW,EAAE,IAAI,CAACpB,sBAAsB,CAAC;MACrF,IAAI,CAACS,cAAc,EAAEsC,QAAQ,CAAC,IAAI,CAAC3B,WAAW,EAAEyB,cAAc,CAAC;IACnE;IACA,IAAI,CAAC7C,sBAAsB,GAAG6C,cAAc;EAChD;EACA;EACA/B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACiB,qBAAqB,GAAGpK,KAAK,CAAC,IAAI,CAAC0I,KAAK,CAAClF,UAAU,EAAE,IAAI,CAACkF,KAAK,CAACpF,aAAa,EAAE,IAAI,CAACkF,KAAK,CAACnB,OAAO,CAAC,CAACyC,SAAS,CAAC,MAAM;MACpH,IAAI,IAAI,CAACN,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACH,qBAAqB,CAAC,CAAC;QAC5B;QACA,IAAI,IAAI,CAACJ,UAAU,CAACM,OAAO,KAAK,MAAM,IAAI,IAAI,CAACN,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;UAC5E,IAAI,CAAC/H,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAAC8H,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,IAAI,CAACtB,eAAe;UAAEK,OAAO,EAAE;QAAS,CAAC,CAAC;QACzF,IAAI,CAACP,kBAAkB,GAAG,KAAK;MACnC;MACA;MACA,IAAI,CAAC,IAAI,CAACQ,SAAS,CAAC,CAAC,IAAI,IAAI,CAACP,UAAU,IAAI,IAAI,CAACA,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;QAC9E,IAAI,CAAC/H,0BAA0B,GAAG,KAAK;QACvC,IAAI,CAAC8H,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,QAAQ;UAAEjB,OAAO,EAAE,IAAI,CAACL;QAAgB,CAAC,CAAC;MAC7F;MACA,IAAI,CAACT,kBAAkB,CAACyB,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACpF,IAAI,YAAAuG,sBAAArG,CAAA;MAAA,YAAAA,CAAA,IAAwFmD,aAAa,EA/XvB3J,EAAE,CAAAyG,iBAAA,CA+XuCmC,iBAAiB,GA/X1D5I,EAAE,CAAAyG,iBAAA,CA+XqEzG,EAAE,CAAC8M,iBAAiB,GA/X3F9M,EAAE,CAAAyG,iBAAA,CA+XsGrC,OAAO,MA/X/GpE,EAAE,CAAAyG,iBAAA,CA+X0I,4BAA4B,MA/XxKzG,EAAE,CAAAyG,iBAAA,CA+XmM3F,EAAE,CAACiM,YAAY,GA/XpN/M,EAAE,CAAAyG,iBAAA,CA+X+NzG,EAAE,CAACgN,UAAU,GA/X9OhN,EAAE,CAAAyG,iBAAA,CA+XyP3F,EAAE,CAACmM,aAAa,MA/X3QjN,EAAE,CAAAyG,iBAAA,CA+XsSvC,wBAAwB;IAAA,CAA4D;EAAE;EAC9d;IAAS,IAAI,CAACgJ,IAAI,kBAhY8ElN,EAAE,CAAAmN,iBAAA;MAAAvG,IAAA,EAgYJ+C,aAAa;MAAA9C,SAAA;MAAAC,SAAA;MAAAsG,QAAA;MAAAC,YAAA,WAAAC,2BAAAjL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhYXrC,EAAE,CAAA0C,UAAA,mBAAA6K,uCAAA;YAAA,OAgYJjL,GAAA,CAAA6J,YAAA,CAAa,CAAC;UAAA,uBAAAqB,yCAAAC,MAAA;YAAA,OAAdnL,GAAA,CAAA8J,cAAA,CAAAqB,MAAqB,CAAC;UAAA,0BAAAC,4CAAA;YAAA,OAAtBpL,GAAA,CAAAmJ,wBAAA,CAAyB,IAAI,CAAC;UAAA,0BAAAkC,4CAAA;YAAA,OAA9BrL,GAAA,CAAAmJ,wBAAA,CAAyB,KAAK,CAAC;UAAA;QAAA;QAAA,IAAApJ,EAAA;UAhY7BrC,EAAE,CAAA4N,WAAA,cAAAtL,GAAA,CAAAkK,qBAAA;UAAFxM,EAAE,CAAA6N,WAAA,6BAAAvL,GAAA,CAAAyJ,WAAA;QAAA;MAAA;MAAAhF,MAAA;QAAAC,QAAA;QAAApD,EAAA;QAAA6E,aAAA;QAAA1D,KAAA;QAAA6E,qBAAA;QAAArF,YAAA;MAAA;MAAA2C,QAAA;MAAAC,QAAA,GAAFnH,EAAE,CAAAoH,0BAAA;MAAA0G,KAAA,EAAA3L,GAAA;MAAA4L,kBAAA,EAAArK,GAAA;MAAAsK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAA/L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrC,EAAE,CAAAqO,eAAA;UAAFrO,EAAE,CAAAyC,cAAA,YAgY0kD,CAAC,YAAD,CAAC;UAhY7kDzC,EAAE,CAAAsO,YAAA,EAgYiiE,CAAC;UAhYpiEtO,EAAE,CAAAoD,YAAA,CAgY2iE,CAAC;UAhY9iEpD,EAAE,CAAAuO,UAAA,IAAAnM,4BAAA,gBAgY24F,CAAC;UAhY94FpC,EAAE,CAAAoD,YAAA,CAgYm5F,CAAC;QAAA;QAAA,IAAAf,EAAA;UAhYt5FrC,EAAE,CAAA6N,WAAA,2BAAAvL,GAAA,CAAA0I,SAAA,EAgYm5C,CAAC,oCAAA1I,GAAA,CAAAmG,aAAA,aAAD,CAAC;UAhYt5CzI,EAAE,CAAA4N,WAAA,aAAAtL,GAAA,CAAAyJ,WAAA,aAgYmhD,CAAC,SAAAzJ,GAAA,CAAAyJ,WAAA,oBAAD,CAAC;UAhYthD/L,EAAE,CAAAyD,SAAA,EAgYwrE,CAAC;UAhY3rEzD,EAAE,CAAAsD,UAAA,SAAAhB,GAAA,CAAAmK,YAAA,EAgYwrE,CAAC;QAAA;MAAA;MAAA+B,YAAA,GAA0lFvM,EAAE,CAACwM,IAAI;MAAAC,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAA+E,CACn8J3G,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACG,WAAW,EAC7BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACK,YAAY,EAC9BL,iBAAiB,CAACO,aAAa,EAC/BP,iBAAiB,CAACQ,aAAa;MAClC;MAAAoG,eAAA;IAAA,EAAiG;EAAE;AAC5G;AACA;EAAA,QAAAxK,SAAA,oBAAAA,SAAA,KAzYoGtE,EAAE,CAAAsH,iBAAA,CAyYXqC,aAAa,EAAc,CAAC;IAC3G/C,IAAI,EAAElG,SAAS;IACf6G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEN,QAAQ,EAAE,eAAe;MAAEO,IAAI,EAAE;QAC7D,OAAO,EAAE,iBAAiB;QAC1B,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE,wBAAwB;QACrC,cAAc,EAAE,gCAAgC;QAChD,cAAc,EAAE,iCAAiC;QACjD,kBAAkB,EAAE,yBAAyB;QAC7C,kCAAkC,EAAE;MACxC,CAAC;MAAEkH,aAAa,EAAEhO,iBAAiB,CAACoO,IAAI;MAAED,eAAe,EAAElO,uBAAuB,CAACoO,MAAM;MAAEjI,MAAM,EAAE,CAAC,UAAU,CAAC;MAAEkI,UAAU,EAAE,CACzH/G,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACG,WAAW,EAC7BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACK,YAAY,EAC9BL,iBAAiB,CAACO,aAAa,EAC/BP,iBAAiB,CAACQ,aAAa,CAClC;MAAEyF,QAAQ,EAAE,qxEAAqxE;MAAEO,MAAM,EAAE,CAAC,o0DAAo0D;IAAE,CAAC;EAChoI,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9H,IAAI,EAAEgC;IAAkB,CAAC,EAAE;MAAEhC,IAAI,EAAE5G,EAAE,CAAC8M;IAAkB,CAAC,EAAE;MAAElG,IAAI,EAAExC,OAAO;MAAEuD,UAAU,EAAE,CAAC;QACzHf,IAAI,EAAExG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwG,IAAI,EAAEc,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCf,IAAI,EAAEvG,MAAM;QACZkH,IAAI,EAAE,CAAC,4BAA4B;MACvC,CAAC,EAAE;QACCX,IAAI,EAAExG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwG,IAAI,EAAE9F,EAAE,CAACiM;IAAa,CAAC,EAAE;MAAEnG,IAAI,EAAE5G,EAAE,CAACgN;IAAW,CAAC,EAAE;MAAEpG,IAAI,EAAE9F,EAAE,CAACmM,aAAa;MAAEtF,UAAU,EAAE,CAAC;QAC7Ff,IAAI,EAAExG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwG,IAAI,EAAEc,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCf,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEvG,MAAM;QACZkH,IAAI,EAAE,CAACrD,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEN,EAAE,EAAE,CAAC;MACjCgD,IAAI,EAAEtG,KAAK;MACXiH,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEkB,aAAa,EAAE,CAAC;MAChB7B,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEyE,KAAK,EAAE,CAAC;MACR6B,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEsJ,qBAAqB,EAAE,CAAC;MACxBhD,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEiE,YAAY,EAAE,CAAC;MACfqC,IAAI,EAAEtG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4O,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC5I,IAAI,YAAA6I,sBAAA3I,CAAA;MAAA,YAAAA,CAAA,IAAwF0I,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAxb8EpP,EAAE,CAAAqP,gBAAA;MAAAzI,IAAA,EAwbSsI;IAAa,EAAwH;EAAE;EAClP;IAAS,IAAI,CAACI,IAAI,kBAzb8EtP,EAAE,CAAAuP,gBAAA;MAAAC,SAAA,EAybmC,CAAClG,6BAA6B,CAAC;MAAAmG,OAAA,GAAYvN,YAAY,EAAEZ,eAAe;IAAA,EAAI;EAAE;AACvN;AACA;EAAA,QAAAgD,SAAA,oBAAAA,SAAA,KA3boGtE,EAAE,CAAAsH,iBAAA,CA2bX4H,aAAa,EAAc,CAAC;IAC3GtI,IAAI,EAAE/F,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCkI,OAAO,EAAE,CAACvN,YAAY,EAAEZ,eAAe,CAAC;MACxCoO,OAAO,EAAE,CAACtL,OAAO,EAAEuF,aAAa,CAAC;MACjCgG,YAAY,EAAE,CAACvL,OAAO,EAAEuF,aAAa,CAAC;MACtC6F,SAAS,EAAE,CAAClG,6BAA6B;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASpF,wBAAwB,EAAEoF,6BAA6B,EAAEF,qCAAqC,EAAEhF,OAAO,EAAEuF,aAAa,EAAEf,iBAAiB,EAAEsG,aAAa,EAAEhH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}