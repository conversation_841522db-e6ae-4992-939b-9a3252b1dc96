{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SseService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n    this.eventSource = null;\n    this.messageSubject = new Subject();\n    this.connectionStatusSubject = new Subject();\n    this.dataUpdateSubject = new Subject();\n    // Cache for conversation history to prevent duplicate API calls\n    this.conversationHistoryCache = {};\n    // Flag to track if a request is in progress\n    this.requestInProgress = false;\n    // Timeout reference for auto-closing connections\n    this.connectionTimeout = null;\n  }\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect() {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId, query) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n    const responseSubject = new Subject();\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      const sseUrl = `${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`;\n      console.log('Connecting to SSE endpoint:', sseUrl);\n      this.eventSource = new EventSource(sseUrl);\n      // Force the connection to be established immediately\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n      // Handle specific event types like in the HTML example\n      // Handle regular messages\n      this.eventSource.addEventListener('message', event => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                console.log('Token received:', data.content);\n                // Emit the token immediately to ensure streaming\n                responseSubject.next(data.content);\n                // Force the browser to render immediately\n                setTimeout(() => {}, 0);\n              }\n              break;\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n            case 'data_update':\n              console.log('Data update received:', data);\n              this.dataUpdateSubject.next(data);\n              break;\n            case 'restaurant_data':\n              console.log('Restaurant data received:', data);\n              this.dataUpdateSubject.next(data);\n              break;\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n      // Handle connection close\n      this.eventSource.addEventListener('error', () => {\n        // Check if the connection is closed\n        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {\n          console.log('Connection closed');\n          this.eventSource = null;\n          this.requestInProgress = false;\n          // Clean up\n          if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n          }\n          // Complete the subject if not already completed\n          if (!responseSubject.closed) {\n            responseSubject.complete();\n          }\n        }\n      });\n      // Handle general errors\n      this.eventSource.addEventListener('error', event => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n        // Don't immediately close on all errors - some might be recoverable\n      });\n      // Handle connection close\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n      // Handle errors\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect() {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId, message) {\n    // Create a new message from the user with the current timestamp\n    const now = new Date();\n    const userMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: now\n    };\n    // Only add the user message to the stream if it's not a special command\n    if (!message.startsWith('__') || !message.endsWith('__')) {\n      this.messageSubject.next(userMessage);\n    }\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n    // Create an initial bot message with a loading indicator\n    // Use a timestamp slightly after the user message to ensure correct ordering\n    const botTimestamp = new Date(now.getTime() + 100); // 100ms after user message\n    const initialBotMessage = {\n      id: botMessageId,\n      text: 'AI is thinking...',\n      sender: 'bot',\n      timestamp: botTimestamp\n    };\n    // Send the initial message only if we have a valid user message\n    if (message && message.trim()) {\n      this.messageSubject.next(initialBotMessage);\n    }\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: chunk => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n        console.log('Received chunk:', chunk.substring(0, 20) + '...');\n        // Add to the full response\n        fullResponse += chunk;\n        // Process the text to ensure proper markdown formatting\n        // This helps with consistent rendering between streaming and history\n        const processedText = this.preprocessMarkdown(fullResponse);\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = processedText + '<span class=\"blinking-cursor\">|</span>';\n        // Update the bot message with the new content\n        const updatedMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: botTimestamp // Use consistent timestamp\n        };\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n          // Force a small delay to ensure the UI updates between chunks\n          setTimeout(() => {}, 0);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: botTimestamp // Use the same timestamp as the initial message\n          };\n\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n\n            this.messageSubject.next(completionMessage);\n          });\n        } else {\n          // Process the final text to ensure proper markdown formatting\n          const processedFinalText = this.preprocessMarkdown(fullResponse);\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage = {\n            id: botMessageId,\n            text: processedFinalText,\n            sender: 'bot',\n            timestamp: botTimestamp // Use the same timestamp as the initial message\n          };\n\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n\n            this.messageSubject.next(completionMessage);\n          });\n        }\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: error => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: botTimestamp // Use the same timestamp as the initial message\n        };\n\n        this.messageSubject.next(errorMessage);\n        // Emit a special message to indicate the response is complete\n        // This will be used to hide the loading indicator\n        const completionMessage = {\n          id: 'completion-' + botMessageId,\n          text: '',\n          sender: 'system',\n          timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n        };\n\n        this.messageSubject.next(completionMessage);\n      }\n    });\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId, restaurantInfo) {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` + `Location: ${restaurantInfo.location || 'Not specified'}\\n` + `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` + `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` + `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` + `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` + `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n  /**\n   * Get the message stream\n   */\n  get messages$() {\n    return this.messageSubject.asObservable();\n  }\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$() {\n    return this.connectionStatusSubject.asObservable();\n  }\n  /**\n   * Get the data update stream\n   */\n  get dataUpdates$() {\n    return this.dataUpdateSubject.asObservable();\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Preprocess markdown text to ensure consistent rendering\n   * @param text The markdown text to preprocess\n   * @returns Preprocessed markdown text\n   */\n  preprocessMarkdown(text) {\n    if (!text) return '';\n    // Ensure proper line breaks for markdown\n    let processed = text\n    // Ensure proper spacing for headers\n    .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Ensure proper spacing for list items\n    .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Ensure proper spacing for code blocks\n    .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n    // Fix incomplete code blocks\n    .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```');\n    // Check if the text ends with an incomplete code block and fix it\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n    return processed;\n  }\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId) {\n    if (!tenantId) {\n      console.error('getConversationHistory called with empty tenantId');\n      return of([]);\n    }\n    console.log('Making HTTP request to get conversation history for tenant:', tenantId);\n    const url = `${this.baseUrl}llm/conversation_history?tenant_id=${encodeURIComponent(tenantId)}`;\n    console.log('Request URL:', url);\n    return this.http.get(url).pipe(map(response => {\n      console.log('Received conversation history response:', response);\n      if (response && response.messages) {\n        console.log('Number of messages in response:', response.messages.length);\n        // Convert backend messages to frontend ChatMessage format\n        const messages = response.messages.map(msg => {\n          console.log('Processing message:', msg.id, msg.type, msg.content.substring(0, 30) + '...');\n          return {\n            id: msg.id ? msg.id.toString() : this.generateId(),\n            text: msg.content,\n            sender: msg.type === 'human' ? 'user' : 'bot',\n            timestamp: new Date(msg.created_at)\n          };\n        });\n        console.log('Converted messages count:', messages.length);\n        return messages;\n      }\n      console.log('No messages found in response');\n      return [];\n    }), catchError(error => {\n      console.error('Error fetching conversation history:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   * @param clearCache Whether to clear the cache (default: true)\n   * @param clearServer Whether to clear the server data (default: false)\n   */\n  clearConversationHistory(tenantId, clearCache = true, clearServer = false) {\n    if (!tenantId) {\n      console.error('clearConversationHistory called with empty tenantId');\n      return throwError(() => new Error('Invalid tenant ID'));\n    }\n    console.log('Clearing conversation history for tenant:', tenantId, 'clearCache:', clearCache, 'clearServer:', clearServer);\n    // Clear the cache if requested\n    if (clearCache) {\n      delete this.conversationHistoryCache[tenantId];\n      console.log('Cleared conversation history cache for tenant:', tenantId);\n    }\n    // Clear the server data if requested\n    if (clearServer) {\n      const url = `${this.baseUrl}llm/clear_history?tenant_id=${encodeURIComponent(tenantId)}`;\n      console.log('Making request to clear server history:', url);\n      return this.http.post(url, {}).pipe(map(response => {\n        console.log('Server history cleared successfully:', response);\n        return response;\n      }), catchError(error => {\n        console.error('Error clearing conversation history on server:', error);\n        return throwError(() => new Error('Failed to clear conversation history on server'));\n      }));\n    }\n    // If not clearing server data, just return success\n    return of({\n      status: 'ok',\n      message: 'Conversation history cleared in UI'\n    });\n  }\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   * @param addToStream Whether to add messages to the message stream\n   */\n  loadConversationHistory(tenantId, addToStream = true) {\n    if (!tenantId) {\n      console.error('loadConversationHistory called with empty tenantId');\n      return of([]);\n    }\n    console.log('SSE service loadConversationHistory called with tenantId:', tenantId);\n    // Always clear the cache to ensure we get fresh data\n    delete this.conversationHistoryCache[tenantId];\n    console.log('Cleared conversation history cache for tenant:', tenantId);\n    // If not in cache, fetch from server\n    console.log('Fetching conversation history from server for tenant:', tenantId);\n    return this.getConversationHistory(tenantId).pipe(map(messages => {\n      // Cache the conversation history\n      this.conversationHistoryCache[tenantId] = messages;\n      // Add messages to the stream if addToStream is true\n      if (addToStream) {\n        // Add each message to the stream\n        messages.forEach(message => {\n          this.messageSubject.next(message);\n        });\n      }\n      return messages;\n    }), catchError(error => {\n      console.error('Error loading conversation history:', error);\n      return of([]);\n    }));\n  }\n  static {\n    this.ɵfac = function SseService_Factory(t) {\n      return new (t || SseService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SseService,\n      factory: SseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SseService };", "map": {"version": 3, "names": ["Subject", "throwError", "of", "catchError", "map", "environment", "SseService", "constructor", "http", "baseUrl", "engineUrl", "eventSource", "messageSubject", "connectionStatusSubject", "dataUpdateSubject", "conversation<PERSON><PERSON><PERSON><PERSON>ache", "requestInProgress", "connectionTimeout", "connect", "next", "connectionStatus$", "streamResponse", "tenantId", "query", "console", "log", "clearTimeout", "close", "setTimeout", "responseSubject", "complete", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "timestamp", "Date", "getTime", "sseUrl", "EventSource", "onopen", "addEventListener", "event", "data", "JSON", "parse", "type", "content", "error", "Error", "warn", "readyState", "CLOSED", "closed", "customEvent", "onerror", "disconnect", "asObservable", "sendMessage", "message", "now", "userMessage", "id", "generateId", "text", "sender", "startsWith", "endsWith", "botMessageId", "fullResponse", "botTimestamp", "initialBotMessage", "trim", "subscribe", "chunk", "substring", "processedText", "preprocessMarkdown", "textWithCursor", "updatedMessage", "Promise", "resolve", "then", "fallbackMessage", "completionMessage", "processedFinalText", "finalMessage", "errorMessage", "submitRestaurantInfo", "restaurantInfo", "infoSummary", "location", "businessType", "cuisineType", "operatingHours", "specialties", "join", "contactInfo", "messages$", "dataUpdates$", "Math", "random", "toString", "processed", "replace", "codeBlockMatches", "match", "length", "getConversationHistory", "url", "get", "pipe", "response", "messages", "msg", "created_at", "clearConversationHistory", "clearCache", "clearServer", "post", "status", "loadConversationHistory", "addToStream", "for<PERSON>ach", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/sse.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, Subject, throwError, of } from 'rxjs';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { ChatMessage } from '../models/chat-message.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SseService {\n  private baseUrl: string = environment.engineUrl;\n  private eventSource: EventSource | null = null;\n  private messageSubject = new Subject<ChatMessage>();\n  private connectionStatusSubject = new Subject<boolean>();\n  private dataUpdateSubject = new Subject<any>();\n\n  // Cache for conversation history to prevent duplicate API calls\n  private conversationHistoryCache: { [tenantId: string]: ChatMessage[] } = {};\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Connect to the SSE endpoint\n   */\n  connect(): Observable<boolean> {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n\n  // Flag to track if a request is in progress\n  private requestInProgress = false;\n\n  // Timeout reference for auto-closing connections\n  private connectionTimeout: any = null;\n\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId: string, query: string): Observable<string> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring new request');\n      return of(''); // Return empty string\n    }\n\n    // Set the flag to indicate a request is in progress\n    this.requestInProgress = true;\n\n    // Clear any existing timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Close any existing connection\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n\n    // Set a timeout to force-close the connection after 30 seconds\n    this.connectionTimeout = setTimeout(() => {\n      console.log('Connection timeout reached, force closing');\n      if (this.eventSource) {\n        this.eventSource.close();\n        this.eventSource = null;\n      }\n      this.requestInProgress = false;\n      responseSubject.complete();\n    }, 30000); // 30 seconds timeout\n\n    const responseSubject = new Subject<string>();\n\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      // Use the correct endpoint URL from your FastAPI implementation\n      // Add a cache-busting parameter to prevent browser caching\n      const timestamp = new Date().getTime();\n      const sseUrl = `${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`;\n      console.log('Connecting to SSE endpoint:', sseUrl);\n      this.eventSource = new EventSource(sseUrl);\n\n      // Force the connection to be established immediately\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n\n      // Handle specific event types like in the HTML example\n\n      // Handle regular messages\n      this.eventSource.addEventListener('message', (event) => {\n        try {\n          // Parse the JSON data\n          const data = JSON.parse(event.data);\n          console.log('Received message:', data);\n\n          // Handle different message types\n          switch (data.type) {\n            case 'start':\n              console.log('Stream started');\n              break;\n\n            case 'token':\n              // Process token immediately\n              if (data.content) {\n                console.log('Token received:', data.content);\n                // Emit the token immediately to ensure streaming\n                responseSubject.next(data.content);\n                // Force the browser to render immediately\n                setTimeout(() => {}, 0);\n              }\n              break;\n\n            case 'end':\n              console.log('Stream ended');\n              responseSubject.complete();\n              break;\n\n            case 'error':\n              console.error('Stream error:', data.content);\n              responseSubject.error(new Error(data.content));\n              break;\n\n            case 'ping':\n              // Just a keep-alive, ignore\n              break;\n\n            case 'data_update':\n              console.log('Data update received:', data);\n              this.dataUpdateSubject.next(data);\n              break;\n\n            case 'restaurant_data':\n              console.log('Restaurant data received:', data);\n              this.dataUpdateSubject.next(data);\n              break;\n\n            default:\n              console.warn('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n        }\n      });\n\n      // Handle connection close\n      this.eventSource.addEventListener('error', () => {\n        // Check if the connection is closed\n        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {\n          console.log('Connection closed');\n          this.eventSource = null;\n          this.requestInProgress = false;\n\n          // Clean up\n          if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n          }\n\n          // Complete the subject if not already completed\n          if (!responseSubject.closed) {\n            responseSubject.complete();\n          }\n        }\n      });\n\n      // Handle general errors\n      this.eventSource.addEventListener('error', (event) => {\n        console.error('SSE error event:', event);\n        // Cast to any to access potential custom properties\n        const customEvent = event as any;\n        if (customEvent.data) {\n          console.error('Error data:', customEvent.data);\n        }\n\n        // Don't immediately close on all errors - some might be recoverable\n      });\n\n      // Handle connection close\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          console.log('Connection closed, cleaning up');\n          this.connectionStatusSubject.next(false);\n          // Complete the subject\n          responseSubject.complete();\n          this.disconnect();\n        }\n      };\n\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n\n      // Handle errors\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n\n        // Check if the EventSource is closed\n        if (this.eventSource && this.eventSource.readyState === 2) {\n          this.connectionStatusSubject.next(false);\n          // Complete the subject instead of erroring it out\n          responseSubject.complete();\n          this.disconnect();\n          // Reset the flag when there's an error\n          this.requestInProgress = false;\n        }\n      };\n\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      // Reset the flag when there's an error\n      this.requestInProgress = false;\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect(): void {\n    if (this.eventSource) {\n      console.log('Manually disconnecting EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n\n    // Clear any timeout\n    if (this.connectionTimeout) {\n      clearTimeout(this.connectionTimeout);\n      this.connectionTimeout = null;\n    }\n\n    // Always reset the request in progress flag when disconnecting\n    this.requestInProgress = false;\n  }\n\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId: string, message: string): Observable<ChatMessage> {\n    // Create a new message from the user with the current timestamp\n    const now = new Date();\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: now\n    };\n\n    // Only add the user message to the stream if it's not a special command\n    if (!message.startsWith('__') || !message.endsWith('__')) {\n      this.messageSubject.next(userMessage);\n    }\n\n    // Generate a unique ID for this bot response\n    const botMessageId = this.generateId();\n    let fullResponse = '';\n\n    // Create an initial bot message with a loading indicator\n    // Use a timestamp slightly after the user message to ensure correct ordering\n    const botTimestamp = new Date(now.getTime() + 100); // 100ms after user message\n    const initialBotMessage: ChatMessage = {\n      id: botMessageId,\n      text: 'AI is thinking...',\n      sender: 'bot',\n      timestamp: botTimestamp\n    };\n\n    // Send the initial message only if we have a valid user message\n    if (message && message.trim()) {\n      this.messageSubject.next(initialBotMessage);\n    }\n\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: (chunk: string) => {\n        // Skip empty chunks\n        if (!chunk.trim()) return;\n\n        console.log('Received chunk:', chunk.substring(0, 20) + '...');\n\n        // Add to the full response\n        fullResponse += chunk;\n\n        // Process the text to ensure proper markdown formatting\n        // This helps with consistent rendering between streaming and history\n        const processedText = this.preprocessMarkdown(fullResponse);\n\n        // Create a typing effect by adding a blinking cursor\n        const textWithCursor = processedText + '<span class=\"blinking-cursor\">|</span>';\n\n        // Update the bot message with the new content\n        const updatedMessage: ChatMessage = {\n          id: botMessageId,\n          text: textWithCursor,\n          sender: 'bot',\n          timestamp: botTimestamp // Use consistent timestamp\n        };\n\n        // Use zone.js microtask to ensure immediate UI update\n        Promise.resolve().then(() => {\n          // Send the updated message\n          this.messageSubject.next(updatedMessage);\n\n          // Force a small delay to ensure the UI updates between chunks\n          setTimeout(() => {}, 0);\n        });\n      },\n      complete: () => {\n        console.log('Stream completed, finalizing response');\n        // If we didn't receive any response, send a fallback message\n        if (!fullResponse.trim()) {\n          const fallbackMessage: ChatMessage = {\n            id: botMessageId,\n            text: \"I'm sorry, I couldn't generate a response. Please try again.\",\n            sender: 'bot',\n            timestamp: botTimestamp // Use the same timestamp as the initial message\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(fallbackMessage);\n\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage: ChatMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n            this.messageSubject.next(completionMessage);\n          });\n        } else {\n          // Process the final text to ensure proper markdown formatting\n          const processedFinalText = this.preprocessMarkdown(fullResponse);\n\n          // Send a final message to ensure the latest content is displayed\n          const finalMessage: ChatMessage = {\n            id: botMessageId,\n            text: processedFinalText,\n            sender: 'bot',\n            timestamp: botTimestamp // Use the same timestamp as the initial message\n          };\n          Promise.resolve().then(() => {\n            this.messageSubject.next(finalMessage);\n\n            // Emit a special message to indicate the response is complete\n            // This will be used to hide the loading indicator\n            const completionMessage: ChatMessage = {\n              id: 'completion-' + botMessageId,\n              text: '',\n              sender: 'system',\n              timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n            };\n            this.messageSubject.next(completionMessage);\n          });\n        }\n\n        // Ensure the connection is closed and request is marked as complete\n        if (this.eventSource) {\n          this.eventSource.close();\n          this.eventSource = null;\n        }\n        this.requestInProgress = false;\n\n        // Clear the timeout\n        if (this.connectionTimeout) {\n          clearTimeout(this.connectionTimeout);\n          this.connectionTimeout = null;\n        }\n      },\n      error: (error) => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage: ChatMessage = {\n          id: botMessageId,\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: botTimestamp // Use the same timestamp as the initial message\n        };\n        this.messageSubject.next(errorMessage);\n\n        // Emit a special message to indicate the response is complete\n        // This will be used to hide the loading indicator\n        const completionMessage: ChatMessage = {\n          id: 'completion-' + botMessageId,\n          text: '',\n          sender: 'system',\n          timestamp: new Date(botTimestamp.getTime() + 200) // 200ms after bot message\n        };\n        this.messageSubject.next(completionMessage);\n      }\n    });\n\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId: string, restaurantInfo: any): Observable<any> {\n    // If a request is already in progress, return an empty observable\n    if (this.requestInProgress) {\n      console.log('Request already in progress, ignoring submission');\n      return of(null);\n    }\n\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` +\n      `Location: ${restaurantInfo.location || 'Not specified'}\\n` +\n      `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` +\n      `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` +\n      `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` +\n      `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` +\n      `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n\n  /**\n   * Get the message stream\n   */\n  get messages$(): Observable<ChatMessage> {\n    return this.messageSubject.asObservable();\n  }\n\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$(): Observable<boolean> {\n    return this.connectionStatusSubject.asObservable();\n  }\n\n  /**\n   * Get the data update stream\n   */\n  get dataUpdates$(): Observable<any> {\n    return this.dataUpdateSubject.asObservable();\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Preprocess markdown text to ensure consistent rendering\n   * @param text The markdown text to preprocess\n   * @returns Preprocessed markdown text\n   */\n  private preprocessMarkdown(text: string): string {\n    if (!text) return '';\n\n    // Ensure proper line breaks for markdown\n    let processed = text\n      // Ensure proper spacing for headers\n      .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n      // Ensure proper spacing for list items\n      .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n      // Ensure proper spacing for code blocks\n      .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n      // Fix incomplete code blocks\n      .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```');\n\n    // Check if the text ends with an incomplete code block and fix it\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n\n    return processed;\n  }\n\n  /**\n   * Get conversation history for a tenant\n   * @param tenantId The tenant ID\n   */\n  getConversationHistory(tenantId: string): Observable<ChatMessage[]> {\n    if (!tenantId) {\n      console.error('getConversationHistory called with empty tenantId');\n      return of([]);\n    }\n\n    console.log('Making HTTP request to get conversation history for tenant:', tenantId);\n    const url = `${this.baseUrl}llm/conversation_history?tenant_id=${encodeURIComponent(tenantId)}`;\n    console.log('Request URL:', url);\n\n    return this.http.get<any>(url)\n      .pipe(\n        map(response => {\n          console.log('Received conversation history response:', response);\n          if (response && response.messages) {\n            console.log('Number of messages in response:', response.messages.length);\n\n            // Convert backend messages to frontend ChatMessage format\n            const messages = response.messages.map((msg: any) => {\n              console.log('Processing message:', msg.id, msg.type, msg.content.substring(0, 30) + '...');\n              return {\n                id: msg.id ? msg.id.toString() : this.generateId(),\n                text: msg.content,\n                sender: msg.type === 'human' ? 'user' : 'bot',\n                timestamp: new Date(msg.created_at)\n              };\n            });\n\n            console.log('Converted messages count:', messages.length);\n            return messages;\n          }\n          console.log('No messages found in response');\n          return [];\n        }),\n        catchError(error => {\n          console.error('Error fetching conversation history:', error);\n          return of([]);\n        })\n      );\n  }\n\n  /**\n   * Clear conversation history for a tenant\n   * @param tenantId The tenant ID\n   * @param clearCache Whether to clear the cache (default: true)\n   * @param clearServer Whether to clear the server data (default: false)\n   */\n  clearConversationHistory(tenantId: string, clearCache: boolean = true, clearServer: boolean = false): Observable<any> {\n    if (!tenantId) {\n      console.error('clearConversationHistory called with empty tenantId');\n      return throwError(() => new Error('Invalid tenant ID'));\n    }\n\n    console.log('Clearing conversation history for tenant:', tenantId, 'clearCache:', clearCache, 'clearServer:', clearServer);\n\n    // Clear the cache if requested\n    if (clearCache) {\n      delete this.conversationHistoryCache[tenantId];\n      console.log('Cleared conversation history cache for tenant:', tenantId);\n    }\n\n    // Clear the server data if requested\n    if (clearServer) {\n      const url = `${this.baseUrl}llm/clear_history?tenant_id=${encodeURIComponent(tenantId)}`;\n      console.log('Making request to clear server history:', url);\n\n      return this.http.post<any>(url, {})\n        .pipe(\n          map(response => {\n            console.log('Server history cleared successfully:', response);\n            return response;\n          }),\n          catchError(error => {\n            console.error('Error clearing conversation history on server:', error);\n            return throwError(() => new Error('Failed to clear conversation history on server'));\n          })\n        );\n    }\n\n    // If not clearing server data, just return success\n    return of({ status: 'ok', message: 'Conversation history cleared in UI' });\n  }\n\n\n\n  /**\n   * Load conversation history and update the message stream\n   * @param tenantId The tenant ID\n   * @param addToStream Whether to add messages to the message stream\n   */\n  loadConversationHistory(tenantId: string, addToStream: boolean = true): Observable<ChatMessage[]> {\n    if (!tenantId) {\n      console.error('loadConversationHistory called with empty tenantId');\n      return of([]);\n    }\n\n    console.log('SSE service loadConversationHistory called with tenantId:', tenantId);\n\n    // Always clear the cache to ensure we get fresh data\n    delete this.conversationHistoryCache[tenantId];\n    console.log('Cleared conversation history cache for tenant:', tenantId);\n\n    // If not in cache, fetch from server\n    console.log('Fetching conversation history from server for tenant:', tenantId);\n    return this.getConversationHistory(tenantId).pipe(\n      map(messages => {\n        // Cache the conversation history\n        this.conversationHistoryCache[tenantId] = messages;\n\n        // Add messages to the stream if addToStream is true\n        if (addToStream) {\n          // Add each message to the stream\n          messages.forEach(message => {\n            this.messageSubject.next(message);\n          });\n        }\n        return messages;\n      }),\n      catchError(error => {\n        console.error('Error loading conversation history:', error);\n        return of([]);\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,OAAO,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAE1D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,UAAU;EAUrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAThB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;IACvC,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAG,IAAIZ,OAAO,EAAe;IAC3C,KAAAa,uBAAuB,GAAG,IAAIb,OAAO,EAAW;IAChD,KAAAc,iBAAiB,GAAG,IAAId,OAAO,EAAO;IAE9C;IACQ,KAAAe,wBAAwB,GAA0C,EAAE;IAc5E;IACQ,KAAAC,iBAAiB,GAAG,KAAK;IAEjC;IACQ,KAAAC,iBAAiB,GAAQ,IAAI;EAhBG;EAExC;;;EAGAC,OAAOA,CAAA;IACL;IACA;IACA,IAAI,CAACL,uBAAuB,CAACM,IAAI,CAAC,IAAI,CAAC;IACvC,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAQA;;;;;EAKAC,cAAcA,CAACC,QAAgB,EAAEC,KAAa;IAC5C;IACA,IAAI,IAAI,CAACP,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,OAAOvB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAGjB;IACA,IAAI,CAACc,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,IAAI,CAACN,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACgB,KAAK,EAAE;MACxB,IAAI,CAAChB,WAAW,GAAG,IAAI;;IAGzB;IACA,IAAI,CAACM,iBAAiB,GAAGW,UAAU,CAAC,MAAK;MACvCJ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI,IAAI,CAACd,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACgB,KAAK,EAAE;QACxB,IAAI,CAAChB,WAAW,GAAG,IAAI;;MAEzB,IAAI,CAACK,iBAAiB,GAAG,KAAK;MAC9Ba,eAAe,CAACC,QAAQ,EAAE;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX,MAAMD,eAAe,GAAG,IAAI7B,OAAO,EAAU;IAE7C,IAAI;MACF;MACA,MAAM+B,YAAY,GAAGC,kBAAkB,CAACT,KAAK,CAAC;MAC9C;MACA;MACA,MAAMU,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;MACtC,MAAMC,MAAM,GAAG,GAAG,IAAI,CAAC3B,OAAO,iBAAiBsB,YAAY,cAAcT,QAAQ,MAAMW,SAAS,EAAE;MAClGT,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEW,MAAM,CAAC;MAClD,IAAI,CAACzB,WAAW,GAAG,IAAI0B,WAAW,CAACD,MAAM,CAAC;MAE1C;MACA,IAAI,CAACzB,WAAW,CAAC2B,MAAM,GAAG,MAAK;QAC7Bd,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACZ,uBAAuB,CAACM,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC;MAED;MAEA;MACA,IAAI,CAACR,WAAW,CAAC4B,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAI;QACrD,IAAI;UACF;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCjB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgB,IAAI,CAAC;UAEtC;UACA,QAAQA,IAAI,CAACG,IAAI;YACf,KAAK,OAAO;cACVpB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC7B;YAEF,KAAK,OAAO;cACV;cACA,IAAIgB,IAAI,CAACI,OAAO,EAAE;gBAChBrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgB,IAAI,CAACI,OAAO,CAAC;gBAC5C;gBACAhB,eAAe,CAACV,IAAI,CAACsB,IAAI,CAACI,OAAO,CAAC;gBAClC;gBACAjB,UAAU,CAAC,MAAK,CAAE,CAAC,EAAE,CAAC,CAAC;;cAEzB;YAEF,KAAK,KAAK;cACRJ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BI,eAAe,CAACC,QAAQ,EAAE;cAC1B;YAEF,KAAK,OAAO;cACVN,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEL,IAAI,CAACI,OAAO,CAAC;cAC5ChB,eAAe,CAACiB,KAAK,CAAC,IAAIC,KAAK,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC;cAC9C;YAEF,KAAK,MAAM;cACT;cACA;YAEF,KAAK,aAAa;cAChBrB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,IAAI,CAAC;cAC1C,IAAI,CAAC3B,iBAAiB,CAACK,IAAI,CAACsB,IAAI,CAAC;cACjC;YAEF,KAAK,iBAAiB;cACpBjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgB,IAAI,CAAC;cAC9C,IAAI,CAAC3B,iBAAiB,CAACK,IAAI,CAACsB,IAAI,CAAC;cACjC;YAEF;cACEjB,OAAO,CAACwB,IAAI,CAAC,uBAAuB,EAAEP,IAAI,CAACG,IAAI,CAAC;;SAErD,CAAC,OAAOE,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAEtD,CAAC,CAAC;MAEF;MACA,IAAI,CAACnC,WAAW,CAAC4B,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC9C;QACA,IAAI,IAAI,CAAC5B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACsC,UAAU,KAAKZ,WAAW,CAACa,MAAM,EAAE;UAC1E1B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;UAChC,IAAI,CAACd,WAAW,GAAG,IAAI;UACvB,IAAI,CAACK,iBAAiB,GAAG,KAAK;UAE9B;UACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;YAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;YACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;UAG/B;UACA,IAAI,CAACY,eAAe,CAACsB,MAAM,EAAE;YAC3BtB,eAAe,CAACC,QAAQ,EAAE;;;MAGhC,CAAC,CAAC;MAEF;MACA,IAAI,CAACnB,WAAW,CAAC4B,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;QACnDhB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEN,KAAK,CAAC;QACxC;QACA,MAAMY,WAAW,GAAGZ,KAAY;QAChC,IAAIY,WAAW,CAACX,IAAI,EAAE;UACpBjB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEM,WAAW,CAACX,IAAI,CAAC;;QAGhD;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC9B,WAAW,CAAC0C,OAAO,GAAIP,KAAK,IAAI;QACnCtB,OAAO,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACsC,UAAU,KAAK,CAAC,EAAE;UACzDzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C,IAAI,CAACZ,uBAAuB,CAACM,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACwB,UAAU,EAAE;;MAErB,CAAC;MAED;MACA,IAAI,CAAC3C,WAAW,CAAC2B,MAAM,GAAG,MAAK;QAC7Bd,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACZ,uBAAuB,CAACM,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC;MAED;MACA,IAAI,CAACR,WAAW,CAAC0C,OAAO,GAAIP,KAAK,IAAI;QACnCtB,OAAO,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACsC,UAAU,KAAK,CAAC,EAAE;UACzD,IAAI,CAACpC,uBAAuB,CAACM,IAAI,CAAC,KAAK,CAAC;UACxC;UACAU,eAAe,CAACC,QAAQ,EAAE;UAC1B,IAAI,CAACwB,UAAU,EAAE;UACjB;UACA,IAAI,CAACtC,iBAAiB,GAAG,KAAK;;MAElC,CAAC;MAED,OAAOa,eAAe,CAAC0B,YAAY,EAAE;KACtC,CAAC,OAAOT,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACjC,uBAAuB,CAACM,IAAI,CAAC,KAAK,CAAC;MACxC;MACA,IAAI,CAACH,iBAAiB,GAAG,KAAK;MAC9B,OAAOf,UAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,oCAAoC,CAAC,CAAC;;EAE5E;EAEA;;;EAGAO,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC3C,WAAW,EAAE;MACpBa,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,IAAI,CAACd,WAAW,CAACgB,KAAK,EAAE;MACxB,IAAI,CAAChB,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,uBAAuB,CAACM,IAAI,CAAC,KAAK,CAAC;;IAG1C;IACA,IAAI,IAAI,CAACF,iBAAiB,EAAE;MAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;IAG/B;IACA,IAAI,CAACD,iBAAiB,GAAG,KAAK;EAChC;EAEA;;;;;EAKAwC,WAAWA,CAAClC,QAAgB,EAAEmC,OAAe;IAC3C;IACA,MAAMC,GAAG,GAAG,IAAIxB,IAAI,EAAE;IACtB,MAAMyB,WAAW,GAAgB;MAC/BC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,IAAI,EAAEL,OAAO;MACbM,MAAM,EAAE,MAAM;MACd9B,SAAS,EAAEyB;KACZ;IAED;IACA,IAAI,CAACD,OAAO,CAACO,UAAU,CAAC,IAAI,CAAC,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxD,IAAI,CAACrD,cAAc,CAACO,IAAI,CAACwC,WAAW,CAAC;;IAGvC;IACA,MAAMO,YAAY,GAAG,IAAI,CAACL,UAAU,EAAE;IACtC,IAAIM,YAAY,GAAG,EAAE;IAErB;IACA;IACA,MAAMC,YAAY,GAAG,IAAIlC,IAAI,CAACwB,GAAG,CAACvB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;IACpD,MAAMkC,iBAAiB,GAAgB;MACrCT,EAAE,EAAEM,YAAY;MAChBJ,IAAI,EAAE,mBAAmB;MACzBC,MAAM,EAAE,KAAK;MACb9B,SAAS,EAAEmC;KACZ;IAED;IACA,IAAIX,OAAO,IAAIA,OAAO,CAACa,IAAI,EAAE,EAAE;MAC7B,IAAI,CAAC1D,cAAc,CAACO,IAAI,CAACkD,iBAAiB,CAAC;;IAG7C;IACA,IAAI,CAAChD,cAAc,CAACC,QAAQ,EAAEmC,OAAO,CAAC,CAACc,SAAS,CAAC;MAC/CpD,IAAI,EAAGqD,KAAa,IAAI;QACtB;QACA,IAAI,CAACA,KAAK,CAACF,IAAI,EAAE,EAAE;QAEnB9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+C,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAE9D;QACAN,YAAY,IAAIK,KAAK;QAErB;QACA;QACA,MAAME,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACR,YAAY,CAAC;QAE3D;QACA,MAAMS,cAAc,GAAGF,aAAa,GAAG,wCAAwC;QAE/E;QACA,MAAMG,cAAc,GAAgB;UAClCjB,EAAE,EAAEM,YAAY;UAChBJ,IAAI,EAAEc,cAAc;UACpBb,MAAM,EAAE,KAAK;UACb9B,SAAS,EAAEmC,YAAY,CAAC;SACzB;QAED;QACAU,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;UAC1B;UACA,IAAI,CAACpE,cAAc,CAACO,IAAI,CAAC0D,cAAc,CAAC;UAExC;UACAjD,UAAU,CAAC,MAAK,CAAE,CAAC,EAAE,CAAC,CAAC;QACzB,CAAC,CAAC;MACJ,CAAC;MACDE,QAAQ,EAAEA,CAAA,KAAK;QACbN,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA,IAAI,CAAC0C,YAAY,CAACG,IAAI,EAAE,EAAE;UACxB,MAAMW,eAAe,GAAgB;YACnCrB,EAAE,EAAEM,YAAY;YAChBJ,IAAI,EAAE,8DAA8D;YACpEC,MAAM,EAAE,KAAK;YACb9B,SAAS,EAAEmC,YAAY,CAAC;WACzB;;UACDU,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAACpE,cAAc,CAACO,IAAI,CAAC8D,eAAe,CAAC;YAEzC;YACA;YACA,MAAMC,iBAAiB,GAAgB;cACrCtB,EAAE,EAAE,aAAa,GAAGM,YAAY;cAChCJ,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE,QAAQ;cAChB9B,SAAS,EAAE,IAAIC,IAAI,CAACkC,YAAY,CAACjC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;aACnD;;YACD,IAAI,CAACvB,cAAc,CAACO,IAAI,CAAC+D,iBAAiB,CAAC;UAC7C,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMC,kBAAkB,GAAG,IAAI,CAACR,kBAAkB,CAACR,YAAY,CAAC;UAEhE;UACA,MAAMiB,YAAY,GAAgB;YAChCxB,EAAE,EAAEM,YAAY;YAChBJ,IAAI,EAAEqB,kBAAkB;YACxBpB,MAAM,EAAE,KAAK;YACb9B,SAAS,EAAEmC,YAAY,CAAC;WACzB;;UACDU,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAACpE,cAAc,CAACO,IAAI,CAACiE,YAAY,CAAC;YAEtC;YACA;YACA,MAAMF,iBAAiB,GAAgB;cACrCtB,EAAE,EAAE,aAAa,GAAGM,YAAY;cAChCJ,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE,QAAQ;cAChB9B,SAAS,EAAE,IAAIC,IAAI,CAACkC,YAAY,CAACjC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;aACnD;;YACD,IAAI,CAACvB,cAAc,CAACO,IAAI,CAAC+D,iBAAiB,CAAC;UAC7C,CAAC,CAAC;;QAGJ;QACA,IAAI,IAAI,CAACvE,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,CAACgB,KAAK,EAAE;UACxB,IAAI,CAAChB,WAAW,GAAG,IAAI;;QAEzB,IAAI,CAACK,iBAAiB,GAAG,KAAK;QAE9B;QACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAC1BS,YAAY,CAAC,IAAI,CAACT,iBAAiB,CAAC;UACpC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;MAEjC,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACftB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,MAAMuC,YAAY,GAAgB;UAChCzB,EAAE,EAAEM,YAAY;UAChBJ,IAAI,EAAE,sEAAsE;UAC5EC,MAAM,EAAE,KAAK;UACb9B,SAAS,EAAEmC,YAAY,CAAC;SACzB;;QACD,IAAI,CAACxD,cAAc,CAACO,IAAI,CAACkE,YAAY,CAAC;QAEtC;QACA;QACA,MAAMH,iBAAiB,GAAgB;UACrCtB,EAAE,EAAE,aAAa,GAAGM,YAAY;UAChCJ,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE,QAAQ;UAChB9B,SAAS,EAAE,IAAIC,IAAI,CAACkC,YAAY,CAACjC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;SACnD;;QACD,IAAI,CAACvB,cAAc,CAACO,IAAI,CAAC+D,iBAAiB,CAAC;MAC7C;KACD,CAAC;IAEF;IACA,OAAOhF,EAAE,CAACyD,WAAW,CAAC;EACxB;EAEA;;;;;EAKA2B,oBAAoBA,CAAChE,QAAgB,EAAEiE,cAAmB;IACxD;IACA,IAAI,IAAI,CAACvE,iBAAiB,EAAE;MAC1BQ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,OAAOvB,EAAE,CAAC,IAAI,CAAC;;IAGjB;IACA,MAAMsF,WAAW,GAAG,wDAAwD,GAC1E,aAAaD,cAAc,CAACE,QAAQ,IAAI,eAAe,IAAI,GAC3D,kBAAkBF,cAAc,CAACG,YAAY,IAAI,eAAe,IAAI,GACpE,iBAAiBH,cAAc,CAACI,WAAW,IAAI,eAAe,IAAI,GAClE,oBAAoBJ,cAAc,CAACK,cAAc,IAAI,eAAe,IAAI,GACxE,gBAAgBL,cAAc,CAACM,WAAW,GAAGN,cAAc,CAACM,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,IAAI,GACxG,iBAAiBP,cAAc,CAACQ,WAAW,IAAI,eAAe,EAAE;IAElE;IACA,OAAO,IAAI,CAACvC,WAAW,CAAClC,QAAQ,EAAEkE,WAAW,CAAC;EAChD;EAEA;;;EAGA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAACpF,cAAc,CAAC2C,YAAY,EAAE;EAC3C;EAEA;;;EAGA,IAAInC,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACP,uBAAuB,CAAC0C,YAAY,EAAE;EACpD;EAEA;;;EAGA,IAAI0C,YAAYA,CAAA;IACd,OAAO,IAAI,CAACnF,iBAAiB,CAACyC,YAAY,EAAE;EAC9C;EAEA;;;EAGQM,UAAUA,CAAA;IAChB,OAAOqC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC3B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGyB,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC3B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;;;EAKQE,kBAAkBA,CAACb,IAAY;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB;IACA,IAAIuC,SAAS,GAAGvC;IACd;IAAA,CACCwC,OAAO,CAAC,yBAAyB,EAAE,OAAO;IAC3C;IAAA,CACCA,OAAO,CAAC,2BAA2B,EAAE,OAAO;IAC7C;IAAA,CACCA,OAAO,CAAC,gBAAgB,EAAE,SAAS;IACpC;IAAA,CACCA,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;IAEpD;IACA,MAAMC,gBAAgB,GAAGF,SAAS,CAACG,KAAK,CAAC,YAAY,CAAC;IACtD,IAAID,gBAAgB,IAAIA,gBAAgB,CAACE,MAAM,GAAG,CAAC,EAAE;MACnDJ,SAAS,IAAI,OAAO;;IAGtB,OAAOA,SAAS;EAClB;EAEA;;;;EAIAK,sBAAsBA,CAACpF,QAAgB;IACrC,IAAI,CAACA,QAAQ,EAAE;MACbE,OAAO,CAACsB,KAAK,CAAC,mDAAmD,CAAC;MAClE,OAAO5C,EAAE,CAAC,EAAE,CAAC;;IAGfsB,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAEH,QAAQ,CAAC;IACpF,MAAMqF,GAAG,GAAG,GAAG,IAAI,CAAClG,OAAO,sCAAsCuB,kBAAkB,CAACV,QAAQ,CAAC,EAAE;IAC/FE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkF,GAAG,CAAC;IAEhC,OAAO,IAAI,CAACnG,IAAI,CAACoG,GAAG,CAAMD,GAAG,CAAC,CAC3BE,IAAI,CACHzG,GAAG,CAAC0G,QAAQ,IAAG;MACbtF,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEqF,QAAQ,CAAC;MAChE,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;QACjCvF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEqF,QAAQ,CAACC,QAAQ,CAACN,MAAM,CAAC;QAExE;QACA,MAAMM,QAAQ,GAAGD,QAAQ,CAACC,QAAQ,CAAC3G,GAAG,CAAE4G,GAAQ,IAAI;UAClDxF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuF,GAAG,CAACpD,EAAE,EAAEoD,GAAG,CAACpE,IAAI,EAAEoE,GAAG,CAACnE,OAAO,CAAC4B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAC1F,OAAO;YACLb,EAAE,EAAEoD,GAAG,CAACpD,EAAE,GAAGoD,GAAG,CAACpD,EAAE,CAACwC,QAAQ,EAAE,GAAG,IAAI,CAACvC,UAAU,EAAE;YAClDC,IAAI,EAAEkD,GAAG,CAACnE,OAAO;YACjBkB,MAAM,EAAEiD,GAAG,CAACpE,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK;YAC7CX,SAAS,EAAE,IAAIC,IAAI,CAAC8E,GAAG,CAACC,UAAU;WACnC;QACH,CAAC,CAAC;QAEFzF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsF,QAAQ,CAACN,MAAM,CAAC;QACzD,OAAOM,QAAQ;;MAEjBvF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,OAAO,EAAE;IACX,CAAC,CAAC,EACFtB,UAAU,CAAC2C,KAAK,IAAG;MACjBtB,OAAO,CAACsB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO5C,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAgH,wBAAwBA,CAAC5F,QAAgB,EAAE6F,UAAA,GAAsB,IAAI,EAAEC,WAAA,GAAuB,KAAK;IACjG,IAAI,CAAC9F,QAAQ,EAAE;MACbE,OAAO,CAACsB,KAAK,CAAC,qDAAqD,CAAC;MACpE,OAAO7C,UAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzDvB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEH,QAAQ,EAAE,aAAa,EAAE6F,UAAU,EAAE,cAAc,EAAEC,WAAW,CAAC;IAE1H;IACA,IAAID,UAAU,EAAE;MACd,OAAO,IAAI,CAACpG,wBAAwB,CAACO,QAAQ,CAAC;MAC9CE,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEH,QAAQ,CAAC;;IAGzE;IACA,IAAI8F,WAAW,EAAE;MACf,MAAMT,GAAG,GAAG,GAAG,IAAI,CAAClG,OAAO,+BAA+BuB,kBAAkB,CAACV,QAAQ,CAAC,EAAE;MACxFE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkF,GAAG,CAAC;MAE3D,OAAO,IAAI,CAACnG,IAAI,CAAC6G,IAAI,CAAMV,GAAG,EAAE,EAAE,CAAC,CAChCE,IAAI,CACHzG,GAAG,CAAC0G,QAAQ,IAAG;QACbtF,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEqF,QAAQ,CAAC;QAC7D,OAAOA,QAAQ;MACjB,CAAC,CAAC,EACF3G,UAAU,CAAC2C,KAAK,IAAG;QACjBtB,OAAO,CAACsB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,OAAO7C,UAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,gDAAgD,CAAC,CAAC;MACtF,CAAC,CAAC,CACH;;IAGL;IACA,OAAO7C,EAAE,CAAC;MAAEoH,MAAM,EAAE,IAAI;MAAE7D,OAAO,EAAE;IAAoC,CAAE,CAAC;EAC5E;EAIA;;;;;EAKA8D,uBAAuBA,CAACjG,QAAgB,EAAEkG,WAAA,GAAuB,IAAI;IACnE,IAAI,CAAClG,QAAQ,EAAE;MACbE,OAAO,CAACsB,KAAK,CAAC,oDAAoD,CAAC;MACnE,OAAO5C,EAAE,CAAC,EAAE,CAAC;;IAGfsB,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAEH,QAAQ,CAAC;IAElF;IACA,OAAO,IAAI,CAACP,wBAAwB,CAACO,QAAQ,CAAC;IAC9CE,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEH,QAAQ,CAAC;IAEvE;IACAE,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEH,QAAQ,CAAC;IAC9E,OAAO,IAAI,CAACoF,sBAAsB,CAACpF,QAAQ,CAAC,CAACuF,IAAI,CAC/CzG,GAAG,CAAC2G,QAAQ,IAAG;MACb;MACA,IAAI,CAAChG,wBAAwB,CAACO,QAAQ,CAAC,GAAGyF,QAAQ;MAElD;MACA,IAAIS,WAAW,EAAE;QACf;QACAT,QAAQ,CAACU,OAAO,CAAChE,OAAO,IAAG;UACzB,IAAI,CAAC7C,cAAc,CAACO,IAAI,CAACsC,OAAO,CAAC;QACnC,CAAC,CAAC;;MAEJ,OAAOsD,QAAQ;IACjB,CAAC,CAAC,EACF5G,UAAU,CAAC2C,KAAK,IAAG;MACjBtB,OAAO,CAACsB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO5C,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;;;uBApmBWI,UAAU,EAAAoH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVvH,UAAU;MAAAwH,OAAA,EAAVxH,UAAU,CAAAyH,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA;;SAEP1H,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}