{"ast": null, "code": "// For each opening emphasis-like marker find a matching closing one\n//\n'use strict';\n\nfunction processDelimiters(state, delimiters) {\n  var closerIdx,\n    openerIdx,\n    closer,\n    opener,\n    minOpenerIdx,\n    newMinOpenerIdx,\n    isOddMatch,\n    lastJump,\n    openersBottom = {},\n    max = delimiters.length;\n  if (!max) return;\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  var headerIdx = 0;\n  var lastTokenIdx = -2; // needs any value lower than -1\n  var jumps = [];\n  for (closerIdx = 0; closerIdx < max; closerIdx++) {\n    closer = delimiters[closerIdx];\n    jumps.push(0);\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx;\n    }\n    lastTokenIdx = closer.token;\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0;\n    if (!closer.close) continue;\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [-1, -1, -1, -1, -1, -1];\n    }\n    minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + closer.length % 3];\n    openerIdx = headerIdx - jumps[headerIdx] - 1;\n    newMinOpenerIdx = openerIdx;\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      opener = delimiters[openerIdx];\n      if (opener.marker !== closer.marker) continue;\n      if (opener.open && opener.end < 0) {\n        isOddMatch = false;\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true;\n            }\n          }\n        }\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open ? jumps[openerIdx - 1] + 1 : 0;\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump;\n          jumps[openerIdx] = lastJump;\n          closer.open = false;\n          opener.end = closerIdx;\n          opener.close = false;\n          newMinOpenerIdx = -1;\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2;\n          break;\n        }\n      }\n    }\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length || 0) % 3] = newMinOpenerIdx;\n    }\n  }\n}\nmodule.exports = function link_pairs(state) {\n  var curr,\n    tokens_meta = state.tokens_meta,\n    max = state.tokens_meta.length;\n  processDelimiters(state, state.delimiters);\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(state, tokens_meta[curr].delimiters);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}