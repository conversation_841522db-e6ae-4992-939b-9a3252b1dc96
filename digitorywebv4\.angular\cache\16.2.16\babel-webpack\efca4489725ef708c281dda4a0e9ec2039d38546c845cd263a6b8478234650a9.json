{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nlet DialogComponent = /*#__PURE__*/(() => {\n  class DialogComponent {\n    constructor(dialogRef, data) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.title = data.title;\n      this.message = data.message;\n    }\n    ngOnInit() {}\n    onConfirm() {\n      this.dialogRef.close(true);\n    }\n    onDismiss() {\n      this.dialogRef.close(false);\n    }\n    static {\n      this.ɵfac = function DialogComponent_Factory(t) {\n        return new (t || DialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DialogComponent,\n        selectors: [[\"app-dialog\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 10,\n        vars: 2,\n        consts: [[\"mat-dialog-title\", \"\"], [\"mat-dialog-content\", \"\"], [\"mat-dialog-actions\", \"\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n        template: function DialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"h1\", 0);\n            i0.ɵɵtext(1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"div\", 1)(3, \"p\");\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 2)(6, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function DialogComponent_Template_button_click_6_listener() {\n              return ctx.onDismiss();\n            });\n            i0.ɵɵtext(7, \"No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DialogComponent_Template_button_click_8_listener() {\n              return ctx.onConfirm();\n            });\n            i0.ɵɵtext(9, \"Yes\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.title, \"\\n\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.message);\n          }\n        },\n        dependencies: [CommonModule, MatIconModule, MatButtonModule, i2.MatButton, MatDialogModule, i1.MatDialogTitle, i1.MatDialogContent, i1.MatDialogActions],\n        changeDetection: 0\n      });\n    }\n  }\n  return DialogComponent;\n})();\nexport { DialogComponent };\nexport class DialogComponentModel {\n  constructor(title, message) {\n    this.title = title;\n    this.message = message;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}