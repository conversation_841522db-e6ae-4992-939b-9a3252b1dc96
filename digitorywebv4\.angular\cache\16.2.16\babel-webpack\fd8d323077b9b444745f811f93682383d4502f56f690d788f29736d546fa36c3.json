{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/radio\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/card\";\nimport * as i18 from \"@angular/material/progress-bar\";\nimport * as i19 from \"@angular/material/tabs\";\nfunction AccountSetupComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Account Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"img\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"apps\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_error_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 77);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"AI Data Generation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Agent Chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_150_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Dataset Download\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"div\", 80)(3, \"mat-icon\", 81);\n    i0.ɵɵtext(4, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 82)(10, \"mat-icon\", 83);\n    i0.ɵɵtext(11, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 84)(14, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_152_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Start Generation \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_div_153_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r19.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_div_153_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 105);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_div_153_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101);\n    i0.ɵɵtemplate(2, AccountSetupComponent_div_153_div_17_mat_icon_2_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵtemplate(3, AccountSetupComponent_div_153_div_17_div_3_Template, 3, 0, \"div\", 49);\n    i0.ɵɵtemplate(4, AccountSetupComponent_div_153_div_17_mat_icon_4_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102)(6, \"div\", 103);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 104);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, step_r23.completed, ctx_r22.activeStep === i_r24, !step_r23.completed && ctx_r22.activeStep !== i_r24));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r23.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep === i_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep !== i_r24);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r23.description);\n  }\n}\nfunction AccountSetupComponent_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"mat-icon\", 88);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Your Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 89);\n    i0.ɵɵelement(7, \"mat-progress-bar\", 90);\n    i0.ɵɵelementStart(8, \"div\", 91);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 92)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AccountSetupComponent_div_153_span_13_Template, 3, 4, \"span\", 22);\n    i0.ɵɵtemplate(14, AccountSetupComponent_div_153_span_14_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(15, AccountSetupComponent_div_153_span_15_Template, 2, 0, \"span\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 94);\n    i0.ɵɵtemplate(17, AccountSetupComponent_div_153_div_17_Template, 10, 10, \"div\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 96)(19, \"div\", 97)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 98);\n    i0.ɵɵtext(25, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r14.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining > 0 && ctx_r14.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_div_154_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 107)(2, \"mat-icon\", 108);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 109);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 110)(9, \"mat-card\", 111)(10, \"mat-card-header\")(11, \"div\", 112)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-card-title\");\n    i0.ɵɵtext(15, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-card-subtitle\");\n    i0.ɵɵtext(17, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"ul\", 113)(20, \"li\");\n    i0.ɵɵtext(21, \"Optimized stock levels based on demand forecasting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵtext(23, \"Intelligent categorization and tagging\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"li\");\n    i0.ɵɵtext(25, \"Ready for import into your inventory system\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"mat-card-actions\")(27, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_154_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.downloadInventory());\n    });\n    i0.ɵɵelementStart(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Download \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"mat-card\", 111)(32, \"mat-card-header\")(33, \"div\", 115)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-card-title\");\n    i0.ɵɵtext(37, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"mat-card-subtitle\");\n    i0.ɵɵtext(39, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"mat-card-content\")(41, \"ul\", 113)(42, \"li\");\n    i0.ɵɵtext(43, \"Cost-effective packaging recommendations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"li\");\n    i0.ɵɵtext(45, \"Sustainable options highlighted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"li\");\n    i0.ɵɵtext(47, \"Compatibility with your inventory items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"mat-card-actions\")(49, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_154_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.downloadPackaging());\n    });\n    i0.ɵɵelementStart(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52, \" Download \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction AccountSetupComponent_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117)(2, \"mat-icon\", 118);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 119);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 120)(9, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_155_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c2 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.selectedTabIndex = 0;\n    this.tenantCreated = false;\n    this.aiDataAvailable = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // Chat bot related properties\n    this.showChatBot = false;\n    this.chatBotMinimized = false;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  onTabChange(index) {\n    this.selectedTabIndex = index;\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  toggleChatBot() {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 156,\n      vars: 26,\n      consts: [[1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"main-card\"], [\"animationDuration\", \"200ms\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"mat-tab-label\", \"\"], [1, \"tab-content\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"status-header\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\", 1, \"compact-radio\"], [\"value\", \"no\", \"color\", \"primary\", 1, \"compact-radio\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-header\"], [1, \"logo-container\"], [1, \"logo-preview-container\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [1, \"tab-content\", \"ai-tab-content\"], [\"animationDuration\", \"200ms\", 1, \"nested-tabs\"], [1, \"chat-interface\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"chat-description\"], [1, \"chat-content\"], [3, \"tenantId\", \"tenantName\"], [1, \"chat-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"next-step-button\", 3, \"click\"], [3, \"disabled\"], [1, \"dataset-download-section\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"tab-icon\"], [1, \"tab-label\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-placeholder\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-intro-panel\"], [1, \"intro-content\"], [1, \"intro-header\"], [1, \"intro-icon\"], [1, \"info-note\"], [1, \"info-icon\"], [1, \"intro-action\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-button\", 3, \"click\"], [1, \"ai-processing-panel\"], [1, \"processing-header\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"download-options\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [1, \"dataset-features\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"download-button\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"retry-button\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r33 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 6)(16, \"mat-icon\", 4);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_21_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"mat-card\", 10)(27, \"mat-card-content\")(28, \"mat-tab-group\", 11);\n          i0.ɵɵlistener(\"selectedIndexChange\", function AccountSetupComponent_Template_mat_tab_group_selectedIndexChange_28_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementStart(29, \"mat-tab\");\n          i0.ɵɵtemplate(30, AccountSetupComponent_ng_template_30_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"form\", 14)(33, \"h3\", 15);\n          i0.ɵɵtext(34, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 16)(36, \"div\", 17)(37, \"mat-form-field\", 18)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 19);\n          i0.ɵɵelementStart(41, \"mat-icon\", 20);\n          i0.ɵɵtext(42, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 18)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_46_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-icon\", 20);\n          i0.ɵɵtext(48, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, AccountSetupComponent_mat_error_49_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-form-field\", 18)(51, \"mat-label\");\n          i0.ɵɵtext(52, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"input\", 23);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_53_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-icon\", 20);\n          i0.ɵɵtext(55, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, AccountSetupComponent_mat_error_56_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 17)(58, \"mat-form-field\", 18)(59, \"mat-label\");\n          i0.ɵɵtext(60, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"input\", 24);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_61_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-icon\", 20);\n          i0.ɵɵtext(63, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(64, AccountSetupComponent_mat_error_64_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"mat-form-field\", 18)(66, \"mat-label\");\n          i0.ɵɵtext(67, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"input\", 25);\n          i0.ɵɵelementStart(69, \"mat-icon\", 20);\n          i0.ɵɵtext(70, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"mat-form-field\", 18)(72, \"mat-label\");\n          i0.ɵɵtext(73, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"input\", 26);\n          i0.ɵɵelementStart(75, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_75_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(76, \"mat-icon\");\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(78, \"div\", 28)(79, \"div\", 29)(80, \"div\", 30)(81, \"div\", 31)(82, \"h4\", 32);\n          i0.ɵɵtext(83, \"Account Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 33)(85, \"div\", 34)(86, \"label\", 35);\n          i0.ɵɵtext(87, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-radio-group\", 36)(89, \"mat-radio-button\", 37);\n          i0.ɵɵtext(90, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"mat-radio-button\", 38);\n          i0.ɵɵtext(92, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(93, \"div\", 34)(94, \"label\", 35);\n          i0.ɵɵtext(95, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"mat-radio-group\", 39)(97, \"mat-radio-button\", 37);\n          i0.ɵɵtext(98, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"mat-radio-button\", 38);\n          i0.ɵɵtext(100, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 34)(102, \"label\", 35);\n          i0.ɵɵtext(103, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"mat-radio-group\", 40)(105, \"mat-radio-button\", 37);\n          i0.ɵɵtext(106, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"mat-radio-button\", 38);\n          i0.ɵɵtext(108, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(109, \"div\", 41)(110, \"div\", 42)(111, \"h4\", 32);\n          i0.ɵɵtext(112, \"Company Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 43)(114, \"div\", 44);\n          i0.ɵɵtemplate(115, AccountSetupComponent_div_115_Template, 2, 1, \"div\", 45);\n          i0.ɵɵtemplate(116, AccountSetupComponent_div_116_Template, 3, 0, \"div\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 47)(118, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_118_listener() {\n            i0.ɵɵrestoreView(_r33);\n            const _r8 = i0.ɵɵreference(124);\n            return i0.ɵɵresetView(_r8.click());\n          });\n          i0.ɵɵtemplate(119, AccountSetupComponent_mat_icon_119_Template, 2, 0, \"mat-icon\", 22);\n          i0.ɵɵtemplate(120, AccountSetupComponent_div_120_Template, 3, 0, \"div\", 49);\n          i0.ɵɵelementStart(121, \"span\");\n          i0.ɵɵtext(122, \"Upload Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(123, \"input\", 50, 51);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_123_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(125, AccountSetupComponent_mat_error_125_Template, 2, 0, \"mat-error\", 52);\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵelementStart(126, \"mat-tab\");\n          i0.ɵɵtemplate(127, AccountSetupComponent_ng_template_127_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(128, \"div\", 53)(129, \"mat-tab-group\", 54)(130, \"mat-tab\");\n          i0.ɵɵtemplate(131, AccountSetupComponent_ng_template_131_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(132, \"div\", 55)(133, \"div\", 56)(134, \"div\", 57)(135, \"mat-icon\", 58);\n          i0.ɵɵtext(136, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"h3\");\n          i0.ɵɵtext(138, \"Restaurant Information Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(139, \"p\", 59);\n          i0.ɵɵtext(140, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 60);\n          i0.ɵɵelement(142, \"app-chat-bot\", 61);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 62)(144, \"button\", 63);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_144_listener() {\n            return ctx.startAIProcessing();\n          });\n          i0.ɵɵelementStart(145, \"span\");\n          i0.ɵɵtext(146, \"Continue to Data Processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"mat-icon\");\n          i0.ɵɵtext(148, \"arrow_forward\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(149, \"mat-tab\", 64);\n          i0.ɵɵtemplate(150, AccountSetupComponent_ng_template_150_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(151, \"div\", 65);\n          i0.ɵɵtemplate(152, AccountSetupComponent_div_152_Template, 18, 0, \"div\", 66);\n          i0.ɵɵtemplate(153, AccountSetupComponent_div_153_Template, 26, 6, \"div\", 67);\n          i0.ɵɵtemplate(154, AccountSetupComponent_div_154_Template, 53, 0, \"div\", 68);\n          i0.ɵɵtemplate(155, AccountSetupComponent_div_155_Template, 13, 0, \"div\", 69);\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(24, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(25, _c2));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTabIndex);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(38);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"tenantId\", ctx.registrationForm.value.tenantId)(\"tenantName\", ctx.registrationForm.value.tenantName);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", !ctx.aiDataAvailable);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDownloading && !ctx.downloadComplete && !ctx.downloadFailed);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDownloading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.downloadComplete);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.downloadFailed);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i15.MatRadioGroup, i15.MatRadioButton, MatButtonModule, i16.MatButton, i16.MatIconButton, MatCardModule, i17.MatCard, i17.MatCardActions, i17.MatCardAvatar, i17.MatCardContent, i17.MatCardHeader, i17.MatCardSubtitle, i17.MatCardTitle, MatSelectModule, MatProgressBarModule, i18.MatProgressBar, MatTabsModule, i19.MatTabLabel, i19.MatTab, i19.MatTabGroup, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "MatTabsModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_div_152_Template_button_click_14_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r19", "estimatedTimeRemaining", "ɵɵtemplate", "AccountSetupComponent_div_153_div_17_mat_icon_2_Template", "AccountSetupComponent_div_153_div_17_div_3_Template", "AccountSetupComponent_div_153_div_17_mat_icon_4_Template", "ɵɵpureFunction3", "_c0", "step_r23", "completed", "ctx_r22", "activeStep", "i_r24", "ɵɵtextInterpolate", "name", "description", "AccountSetupComponent_div_153_span_13_Template", "AccountSetupComponent_div_153_span_14_Template", "AccountSetupComponent_div_153_span_15_Template", "AccountSetupComponent_div_153_div_17_Template", "ctx_r14", "downloadProgress", "downloadSteps", "AccountSetupComponent_div_154_Template_button_click_27_listener", "_r29", "ctx_r28", "downloadInventory", "AccountSetupComponent_div_154_Template_button_click_49_listener", "ctx_r30", "downloadPackaging", "AccountSetupComponent_div_155_Template_button_click_9_listener", "_r32", "ctx_r31", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "selectedTabIndex", "tenantCreated", "aiDataAvailable", "showDataDownload", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "chatBotMinimized", "user", "getCurrentUser", "baseData", "getBaseData", "value", "isDuplicate", "key", "registrationForm", "group", "tenantId", "required", "tenantName", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "onTabChange", "index", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startAIProcessing", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "toggleChatBot", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_21_listener", "AccountSetupComponent_Template_mat_tab_group_selectedIndexChange_28_listener", "$event", "AccountSetupComponent_ng_template_30_Template", "AccountSetupComponent_Template_input_keyup_46_listener", "AccountSetupComponent_mat_error_49_Template", "AccountSetupComponent_Template_input_keyup_53_listener", "AccountSetupComponent_mat_error_56_Template", "AccountSetupComponent_Template_input_keyup_61_listener", "AccountSetupComponent_mat_error_64_Template", "AccountSetupComponent_Template_button_click_75_listener", "AccountSetupComponent_div_115_Template", "AccountSetupComponent_div_116_Template", "AccountSetupComponent_Template_button_click_118_listener", "_r33", "_r8", "ɵɵreference", "click", "AccountSetupComponent_mat_icon_119_Template", "AccountSetupComponent_div_120_Template", "AccountSetupComponent_Template_input_change_123_listener", "AccountSetupComponent_mat_error_125_Template", "AccountSetupComponent_ng_template_127_Template", "AccountSetupComponent_ng_template_131_Template", "AccountSetupComponent_Template_button_click_144_listener", "AccountSetupComponent_ng_template_150_Template", "AccountSetupComponent_div_152_Template", "AccountSetupComponent_div_153_Template", "AccountSetupComponent_div_154_Template", "AccountSetupComponent_div_155_Template", "ɵɵpureFunction0", "_c1", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i15", "MatRadioGroup", "MatRadioButton", "i16", "MatButton", "MatIconButton", "i17", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i18", "MatProgressBar", "i19", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport {ProgressBarMode, MatProgressBarModule} from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    MatTabsModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n  selectedTabIndex: number = 0;\n  tenantCreated: boolean = false;\n  aiDataAvailable: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // Chat bot related properties\n  showChatBot: boolean = false;\n  chatBotMinimized: boolean = false;\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true; // Enable the second tab after successful creation\n            this.aiDataAvailable = true; // Enable the dataset download tab\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  onTabChange(index: number) {\n    this.selectedTabIndex = index;\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  toggleChatBot(): void {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"main-card\">\n      <mat-card-content>\n        <!-- Tabbed interface -->\n        <mat-tab-group animationDuration=\"200ms\" [selectedIndex]=\"selectedTabIndex\" (selectedIndexChange)=\"onTabChange($event)\">\n          <!-- Account Information Tab -->\n          <mat-tab>\n            <ng-template mat-tab-label>\n              <mat-icon class=\"tab-icon\">business</mat-icon>\n              <span class=\"tab-label\">Account Information</span>\n            </ng-template>\n            \n            <div class=\"tab-content\">\n              <form class=\"account-form\" [formGroup]=\"registrationForm\">\n                <h3 class=\"form-section-title\">Account Information</h3>\n                <div class=\"compact-form-grid\">\n                  <!-- First row -->\n                  <div class=\"form-row\">\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Tenant Name</mat-label>\n                      <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                      <mat-icon matSuffix>business</mat-icon>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Tenant ID</mat-label>\n                      <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                        oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                      <mat-icon matSuffix>fingerprint</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                        Tenant ID already exists\n                      </mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Account Number</mat-label>\n                      <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                        oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                      <mat-icon matSuffix>account_balance</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                        Account number already exists\n                      </mat-error>\n                    </mat-form-field>\n                  </div>\n\n                  <!-- Second row -->\n                  <div class=\"form-row\">\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>G-Sheet</mat-label>\n                      <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                        (keyup)=\"checkGSheet($event)\">\n                      <mat-icon matSuffix>table_chart</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                        G-Sheet number already exists\n                      </mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Email</mat-label>\n                      <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                      <mat-icon matSuffix>email</mat-icon>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Password</mat-label>\n                      <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                        [type]=\"hidePassword ? 'password' : 'text'\" />\n                      <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                      </button>\n                    </mat-form-field>\n                  </div>\n\n                  <!-- Settings section with two-column layout -->\n                  <div class=\"settings-section\">\n                    <div class=\"two-column-grid\">\n                      <!-- Left column: Status options -->\n                      <div class=\"left-column\">\n                        <div class=\"status-header\">\n                          <h4 class=\"section-label\">Account Status</h4>\n                        </div>\n                        <div class=\"status-options\">\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Account</label>\n                            <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Active</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Inactive</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Forecast</label>\n                            <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Sales</label>\n                            <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Right column: Logo upload -->\n                      <div class=\"right-column\">\n                        <div class=\"logo-header\">\n                          <h4 class=\"section-label\">Company Logo</h4>\n                        </div>\n                        <div class=\"logo-container\">\n                          <div class=\"logo-preview-container\">\n                            <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                              <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                            </div>\n                            <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                              <mat-icon>apps</mat-icon>\n                            </div>\n                          </div>\n                          <div class=\"logo-actions\">\n                            <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                              <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                              <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                                <span class=\"sr-only\">Loading...</span>\n                              </div>\n                              <span>Upload Logo</span>\n                            </button>\n                            <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n                            <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                              Please upload a logo\n                            </mat-error>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </mat-tab>\n\n          <!-- AI Data Generation Tab -->\n          <mat-tab >\n            <ng-template mat-tab-label>\n              <mat-icon class=\"tab-icon\">auto_awesome</mat-icon>\n              <span class=\"tab-label\">AI Data Generation</span>\n            </ng-template>\n\n            <div class=\"tab-content ai-tab-content\">\n              <!-- Nested tabs for AI section -->\n              <mat-tab-group animationDuration=\"200ms\" class=\"nested-tabs\">\n                <!-- Agent Chat Tab -->\n                <mat-tab>\n                  <ng-template mat-tab-label>\n                    <mat-icon class=\"tab-icon\">chat</mat-icon>\n                    <span class=\"tab-label\">Agent Chat</span>\n                  </ng-template>\n\n                  <div class=\"chat-interface\">\n                    <div class=\"chat-header\">\n                      <div class=\"chat-title\">\n                        <mat-icon class=\"chat-icon\">smart_toy</mat-icon>\n                        <h3>Restaurant Information Assistant</h3>\n                      </div>\n                      <p class=\"chat-description\">\n                        Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n                      </p>\n                    </div>\n\n                    <div class=\"chat-content\">\n                      <app-chat-bot [tenantId]=\"registrationForm.value.tenantId\"\n                        [tenantName]=\"registrationForm.value.tenantName\">\n                      </app-chat-bot>\n                    </div>\n\n                    <div class=\"chat-actions\">\n                      <button mat-raised-button color=\"primary\" (click)=\"startAIProcessing()\" class=\"next-step-button\">\n                        <span>Continue to Data Processing</span>\n                        <mat-icon>arrow_forward</mat-icon>\n                      </button>\n                    </div>\n                  </div>\n                </mat-tab>\n\n                <!-- Dataset Download Tab -->\n                <mat-tab [disabled]=\"!aiDataAvailable\">\n                  <ng-template mat-tab-label>\n                    <mat-icon class=\"tab-icon\">download</mat-icon>\n                    <span class=\"tab-label\">Dataset Download</span>\n                  </ng-template>\n\n                  <div class=\"dataset-download-section\">\n                    <!-- Initial state - before starting process -->\n                    <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed\" class=\"ai-intro-panel\">\n                      <div class=\"intro-content\">\n                        <div class=\"intro-header\">\n                          <mat-icon class=\"intro-icon\">auto_awesome</mat-icon>\n                          <h3>Generate AI-Powered Datasets</h3>\n                        </div>\n                        <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will\n                          analyze your business needs and create personalized recommendations.</p>\n                        <p class=\"info-note\"><mat-icon class=\"info-icon\">info</mat-icon> This process takes approximately 15 minutes to complete. You can continue using\n                          the system while processing runs in the background.</p>\n                      </div>\n                      <div class=\"intro-action\">\n                        <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"generate-button\">\n                          <mat-icon>play_arrow</mat-icon>\n                          Start Generation\n                        </button>\n                      </div>\n                    </div>\n\n                    <!-- Processing state -->\n                    <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n                      <div class=\"processing-header\">\n                        <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n                        <h3>Processing Your Data</h3>\n                      </div>\n\n                      <!-- Progress indicator -->\n                      <div class=\"progress-container\">\n                        <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n                        <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n                      </div>\n\n                      <!-- Estimated time -->\n                      <div class=\"estimated-time\">\n                        <mat-icon>access_time</mat-icon>\n                        <span *ngIf=\"estimatedTimeRemaining > 60\">\n                          Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n                        </span>\n                        <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n                          Estimated time remaining: less than a minute\n                        </span>\n                        <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n                          Calculating...\n                        </span>\n                      </div>\n\n                      <!-- Processing steps -->\n                      <div class=\"processing-steps\">\n                        <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n                          [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n                          <div class=\"step-status\">\n                            <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n                            <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                              <span class=\"visually-hidden\">Loading...</span>\n                            </div>\n                            <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n                          </div>\n\n                          <div class=\"step-details\">\n                            <div class=\"step-name\">{{step.name}}</div>\n                            <div class=\"step-description\">{{step.description}}</div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Helpful tips section -->\n                      <div class=\"tips-section\">\n                        <div class=\"tip-header\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>Did You Know?</span>\n                        </div>\n                        <div class=\"tip-content\">\n                          AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n                          insights\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Download complete state -->\n                    <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n                      <div class=\"success-header\">\n                        <mat-icon class=\"success-icon\">task_alt</mat-icon>\n                        <h3>Processing Complete!</h3>\n                      </div>\n\n                      <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.</p>\n\n                      <div class=\"download-options\">\n                        <!-- Inventory Dataset Card -->\n                        <mat-card class=\"download-card\">\n                          <mat-card-header>\n                            <div mat-card-avatar class=\"inventory-icon\">\n                              <mat-icon>inventory_2</mat-icon>\n                            </div>\n                            <mat-card-title>Inventory Dataset</mat-card-title>\n                            <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n                          </mat-card-header>\n                          <mat-card-content>\n                            <ul class=\"dataset-features\">\n                              <li>Optimized stock levels based on demand forecasting</li>\n                              <li>Intelligent categorization and tagging</li>\n                              <li>Ready for import into your inventory system</li>\n                            </ul>\n                          </mat-card-content>\n                          <mat-card-actions>\n                            <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\" class=\"download-button\">\n                              <mat-icon>download</mat-icon> Download\n                            </button>\n                          </mat-card-actions>\n                        </mat-card>\n\n                        <!-- Packaging Dataset Card -->\n                        <mat-card class=\"download-card\">\n                          <mat-card-header>\n                            <div mat-card-avatar class=\"packaging-icon\">\n                              <mat-icon>category</mat-icon>\n                            </div>\n                            <mat-card-title>Packaging Dataset</mat-card-title>\n                            <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n                          </mat-card-header>\n                          <mat-card-content>\n                            <ul class=\"dataset-features\">\n                              <li>Cost-effective packaging recommendations</li>\n                              <li>Sustainable options highlighted</li>\n                              <li>Compatibility with your inventory items</li>\n                            </ul>\n                          </mat-card-content>\n                          <mat-card-actions>\n                            <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\" class=\"download-button\">\n                              <mat-icon>download</mat-icon> Download\n                            </button>\n                          </mat-card-actions>\n                        </mat-card>\n                      </div>\n                    </div>\n\n                    <!-- Error state -->\n                    <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n                      <div class=\"error-header\">\n                        <mat-icon class=\"error-icon\">error_outline</mat-icon>\n                        <h3>Processing Failed</h3>\n                      </div>\n\n                      <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n                        load or connection issues.</p>\n\n                      <div class=\"error-actions\">\n                        <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\" class=\"retry-button\">\n                          <mat-icon>refresh</mat-icon> Try Again\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </mat-tab>\n              </mat-tab-group>\n            </div>\n          </mat-tab>\n        </mat-tab-group>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAAyBC,oBAAoB,QAAO,gCAAgC;AACpF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICWdC,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAoB1CH,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA6DNH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAEtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKzBH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7DH,EAAA,CAAAC,cAAA,cAAuF;IAC/DD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAc1BH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS3CH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAgCzCH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAK/CH,EAAA,CAAAC,cAAA,cAA2F;IAGxDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLACmE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,YAAqB;IAA4BD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,4IACZ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EAAA,CAAAC,cAAA,eAA0B;IACkBD,EAAA,CAAAU,UAAA,mBAAAC,gEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAoBTH,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkB,kBAAA,gCAAAlB,EAAA,CAAAmB,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACArB,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,cAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,eACuI;IAGnID,EAAA,CAAAsB,UAAA,IAAAC,wDAAA,uBAAwD;IACxDvB,EAAA,CAAAsB,UAAA,IAAAE,mDAAA,kBAEM;IACNxB,EAAA,CAAAsB,UAAA,IAAAG,wDAAA,uBAAuF;IACzFzB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvHhC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAsB,QAAA,CAAAC,SAAA,CAAoB;IACzB7B,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAsB,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpChC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAsB,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7BhC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAiC,iBAAA,CAAAL,QAAA,CAAAM,IAAA,CAAa;IACNlC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAiC,iBAAA,CAAAL,QAAA,CAAAO,WAAA,CAAoB;;;;;IAzC1DnC,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI/BH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,eAA4B;IAChBD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAsB,UAAA,KAAAc,8CAAA,mBAEO;IACPpC,EAAA,CAAAsB,UAAA,KAAAe,8CAAA,mBAEO;IACPrC,EAAA,CAAAsB,UAAA,KAAAgB,8CAAA,mBAEO;IACTtC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAsB,UAAA,KAAAiB,6CAAA,oBAeM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAkC,OAAA,CAAAC,gBAAA,CAA0B;IACnCzC,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAkB,kBAAA,KAAAsB,OAAA,CAAAC,gBAAA,eAA8B;IAMnDzC,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,MAAiC;IAGjCrB,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,QAAAmB,OAAA,CAAAnB,sBAAA,OAAgE;IAGhErB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,OAAkC;IAOnBrB,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAkC,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5C1C,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,4FAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEpHH,EAAA,CAAAC,cAAA,eAA8B;IAKZD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAEVD,EAAA,CAAAE,MAAA,0DAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGxDH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAiC,gEAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA6B,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrE9C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAC,cAAA,qBAAgC;IAGhBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAEVD,EAAA,CAAAE,MAAA,gDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGpDH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAqC,gEAAA;MAAA/C,EAAA,CAAAY,aAAA,CAAAgC,IAAA;MAAA,MAAAI,OAAA,GAAAhD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgC,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEjD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAOjBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAAwC,+DAAA;MAAAlD,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAoC,OAAA,CAAAnC,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;ADzVjC,MAwBakD,qBAAqB;EAwChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IAlDpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAhE,OAAO,GAAkB,IAAI;IAC7B,KAAAiE,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAGhC,KAAAnC,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAAoC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAtC,gBAAgB,GAAW,CAAC;IAC5B,KAAAuC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAlD,UAAU,GAAW,CAAC;IACtB,KAAAV,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAA6D,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAY,KAAK;IAkB/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,IAAI,CAACsB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC3B,UAAU,CAAC4B,WAAW,EAAE,CAACC,KAAK;IAEnD;IACA,IAAI,IAAI,CAACvB,UAAU,EAAE;MACnB,IAAI,CAACwB,WAAW,GAAG,IAAI,CAACxB,UAAU,CAACyB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACjC,EAAE,CAACkC,KAAK,CAAC;MACpCC,QAAQ,EAAE,IAAInH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAE;MAC3DC,UAAU,EAAE,IAAIrH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MAC5DE,OAAO,EAAE,IAAItH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MACzDG,MAAM,EAAE,IAAIvH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MACxDI,SAAS,EAAE,IAAIxH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MAC3DK,QAAQ,EAAE,IAAIzH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MAC1DM,OAAO,EAAE,IAAI1H,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MAC3DO,QAAQ,EAAE,IAAI3H,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MAC5DQ,KAAK,EAAE,IAAI5H,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACiH,QAAQ,CAAC;MACzDS,IAAI,EAAE,IAAI7H,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACiH,QAAQ;KACtD,CAAc;EAEjB;EAEAU,QAAQA,CAAA;IACN;IACA,IAAI,CAAC9B,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACyB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAAChB,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAAC+B,WAAW,CAAC,IAAI,CAACxC,UAAU,CAACyC,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACnC,UAAU,GAAG,IAAI,CAAC,CAAC;UAExB;UACA,IAAI,CAACZ,GAAG,CAACgD,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAACxC,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAACuD,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAAC3D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAAC5C,UAAU,GAAG,KAAK,CAAC,CAAC;cACzB6C,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAAC1D,MAAM,CAACuD,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAAC3D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAACvB,gBAAgB,CAAC6B,UAAU,CAAC;MAC/BzB,UAAU,EAAEmB,IAAI,CAACnB,UAAU;MAC3BF,QAAQ,EAAEqB,IAAI,CAACrB,QAAQ;MACvBG,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAC/F,OAAO,GAAG0G,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAAC/B,aAAa,GAAG,CAAC;QACpBmD,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAACvC,EAAE,CAAC4D,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACrE,MAAM,CAAC4D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAACnC,gBAAgB,CAACoC,OAAO,EAAE;MACjC,IAAI,CAACpC,gBAAgB,CAACqC,gBAAgB,EAAE;MACxC,IAAI,CAACpE,MAAM,CAACuD,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAACtC,gBAAgB,CAACH,KAAK;MACxC,IAAI0C,GAAG,GAAQ;QACTrC,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ;QACzBE,UAAU,EAAEkC,MAAM,CAAClC,UAAU;QAC7BG,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAAC/B,aAAa,CAAC2D,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC3D,aAAa,CAAC,CAAC,CAAC,CAACmD,GAAG,GAAG;;OAEvE;MACD,IAAI,CAAC7D,GAAG,CAACsE,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAACrD,MAAM,CAACyE,mBAAmB,CAAC,IAAI,CAAC3D,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAACE,aAAa,GAAG,IAAI,CAAC,CAAC;YAC3B,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;YAC7B,IAAI,CAACrB,MAAM,CAAC4D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAACvD,iBAAiB,CAACyE,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAAC1E,MAAM,CAACuD,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC9D,gBAAgB,GAAG8D,KAAK;EAC/B;EAEAC,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACpD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACvD,UAAU,CAAC4B,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMqD,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClD,QAAQ,KAAM8C,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAClD,gBAAgB,CAACqD,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAACtD,gBAAgB,CAACqD,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACpD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACvD,UAAU,CAAC4B,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMqD,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7C,SAAS,KAAMyC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAClD,gBAAgB,CAACqD,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAACtD,gBAAgB,CAACqD,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACpD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACvD,UAAU,CAAC4B,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMqD,eAAe,GAAG3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9C,MAAM,KAAM0C,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAClD,gBAAgB,CAACqD,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAACtD,gBAAgB,CAACqD,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAAC/E,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9BkF,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACrK,OAAO,GAAGoK,MAAM;YACrB,IAAI,CAACjF,gBAAgB,CAAC6B,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAC/F;YAAO,CAAE,CAAC;YACxD,IAAI,CAACgE,aAAa,CAACsG,IAAI,CAAC;cAAEnD,GAAG,EAAEiD;YAAM,CAAE,CAAC;YACxC,IAAI,CAACtG,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAAC4D,aAAa,EAAE;UACzB,CAAC;UACDsC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACxI,aAAa,CAACgH,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACtJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAECd,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAAC0E,gBAAgB,CAACoC,OAAO,EAAE;MACjC,IAAI,CAACnE,MAAM,CAACuD,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACjC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnB,EAAE,CAAC4D,aAAa,EAAE;IAEvB;IACAwD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC1G,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACpB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAAC2D,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACiG,UAAU,EAAE;IACjB,MAAMrF,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACH,KAAK,CAACK,QAAQ;IAErD,IAAI,CAAC/B,GAAG,CAAC4H,eAAe,CAAC7F,QAAQ,CAAC,CAAC8F,IAAI,CACrCjM,UAAU,CAACkM,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAOhM,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAAC+G,SAAS,CAAEkF,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC7E,OAAO,EAAE;QAChC,IAAI,CAAC8E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACnG,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACgG,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAI,aAAaA,CAAA;IACX,IAAI,CAAC9G,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAACnB,EAAE,CAAC4D,aAAa,EAAE;EACzB;EAEAoE,kBAAkBA,CAACnG,QAAgB;IACjC,IAAI,CAACqG,aAAa,GAAGpM,QAAQ,CAAC,KAAK,CAAC,CAAC6L,IAAI,CACvChM,SAAS,CAAC,MAAM,IAAI,CAACmE,GAAG,CAACqI,SAAS,CAACtG,QAAQ,CAAC,CAAC,EAC7CjG,SAAS,CAAEkM,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAACrE,MAAM,KAAK,UAAU,IAAIqE,QAAQ,CAACrE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAEkF,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAACrE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACoE,mBAAmB,CAACC,QAAQ,CAACM,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAAC3J,gBAAgB,GAAGqJ,QAAQ,CAACO,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAAChL,sBAAsB,GAAGyK,QAAQ,CAACQ,wBAAwB,IAAI,CAAC;MAEpE,IAAIR,QAAQ,CAACS,WAAW,KAAKC,SAAS,IAAIV,QAAQ,CAACS,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACxK,UAAU,GAAG+J,QAAQ,CAACS,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIX,QAAQ,CAACS,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAAC/J,aAAa,CAACyF,MAAM,EAAE;YACjC,IAAI,CAACzF,aAAa,CAAC+J,CAAC,CAAC,CAAC5K,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAACmC,EAAE,CAAC4D,aAAa,EAAE;MAEvB,IAAIkE,QAAQ,CAACrE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAACiF,gBAAgB,EAAE;;IAE3B,CAAC,EAAEd,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAa,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC5H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAChB,EAAE,CAAC4D,aAAa,EAAE;IACvB,IAAI,CAAC1D,QAAQ,CAAC0I,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAlB,mBAAmBA,CAACO,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC5H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACf,QAAQ,CAAC0I,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAACpG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC3F,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGA6B,iBAAiBA,CAAA;IACf,IAAI,CAACgB,GAAG,CAACmJ,YAAY,CAAC,WAAW,EAAE,IAAI,CAACtH,gBAAgB,CAACH,KAAK,CAACK,QAAQ,CAAC,CAACe,SAAS,CAC/EsG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGxN,IAAI,CAACyN,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC9H,gBAAgB,CAACH,KAAK,CAACK,QAAQ,sBAAsB;QAC9E9F,IAAI,CAAC2N,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOpG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACnD,QAAQ,CAAC0I,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAxF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACnD,QAAQ,CAAC0I,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGA5J,iBAAiBA,CAAA;IACf,IAAI,CAACa,GAAG,CAACmJ,YAAY,CAAC,SAAS,EAAE,IAAI,CAACtH,gBAAgB,CAACH,KAAK,CAACK,QAAQ,CAAC,CAACe,SAAS,CAC7EsG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGxN,IAAI,CAACyN,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC9H,gBAAgB,CAACH,KAAK,CAACK,QAAQ,sBAAsB;QAC9E9F,IAAI,CAAC2N,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOpG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACnD,QAAQ,CAAC0I,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAxF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACnD,QAAQ,CAAC0I,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAzbWtJ,qBAAqB,EAAArD,EAAA,CAAA4N,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA9N,EAAA,CAAA4N,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhO,EAAA,CAAA4N,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAjO,EAAA,CAAA4N,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAnO,EAAA,CAAA4N,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAArO,EAAA,CAAA4N,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAvO,EAAA,CAAA4N,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAzO,EAAA,CAAA4N,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAA3O,EAAA,CAAA4N,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA7O,EAAA,CAAA4N,iBAAA,CAAA5N,EAAA,CAAA8O,iBAAA,GAAA9O,EAAA,CAAA4N,iBAAA,CAmDV3O,eAAe,MAAAe,EAAA,CAAA4N,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAAhP,EAAA,CAAA4N,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAnD1B7L,qBAAqB;MAAA8L,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArP,EAAA,CAAAsP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAArF,GAAA;QAAA,IAAAqF,EAAA;;UCpDlC5P,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAmP,wDAAA;YAAA,OAAStF,GAAA,CAAAzC,IAAA,EAAM;UAAA,EAAC;UACtB9H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA6B;UAIqDD,EAAA,CAAAU,UAAA,iCAAAoP,6EAAAC,MAAA;YAAA,OAAuBxF,GAAA,CAAA/B,WAAA,CAAAuH,MAAA,CAAmB;UAAA,EAAC;UAErH/P,EAAA,CAAAC,cAAA,eAAS;UACPD,EAAA,CAAAsB,UAAA,KAAA0O,6CAAA,0BAGc;UAEdhQ,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAuP,uDAAAF,MAAA;YAAA,OAASxF,GAAA,CAAA7B,aAAA,CAAAqH,MAAA,CAAqB;UAAA,EAAC;UADjF/P,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAsB,UAAA,KAAA4O,2CAAA,wBAEY;UACdlQ,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAyP,uDAAAJ,MAAA;YAAA,OAASxF,GAAA,CAAArB,cAAA,CAAA6G,MAAA,CAAsB;UAAA,EAAC;UADlF/P,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAsB,UAAA,KAAA8O,2CAAA,wBAEY;UACdpQ,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAA2P,uDAAAN,MAAA;YAAA,OAASxF,GAAA,CAAApB,WAAA,CAAA4G,MAAA,CAAmB;UAAA,EAAC;UAD/B/P,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAsB,UAAA,KAAAgP,2CAAA,wBAEY;UACdtQ,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAA6P,wDAAA;YAAA,OAAAhG,GAAA,CAAA9F,YAAA,IAAA8F,GAAA,CAAA9F,YAAA;UAAA,EAAsC;UACtEzE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UAKID,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UACvCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC7FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UACzCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACnCD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,6BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOtGH,EAAA,CAAAC,cAAA,gBAA0B;UAEID,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,gBAA4B;UAExBD,EAAA,CAAAsB,UAAA,MAAAkP,sCAAA,kBAEM;UACNxQ,EAAA,CAAAsB,UAAA,MAAAmP,sCAAA,kBAEM;UACRzQ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAAgQ,yDAAA;YAAA1Q,EAAA,CAAAY,aAAA,CAAA+P,IAAA;YAAA,MAAAC,GAAA,GAAA5Q,EAAA,CAAA6Q,WAAA;YAAA,OAAS7Q,EAAA,CAAAgB,WAAA,CAAA4P,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnE9Q,EAAA,CAAAsB,UAAA,MAAAyP,2CAAA,uBAA6D;UAC7D/Q,EAAA,CAAAsB,UAAA,MAAA0P,sCAAA,kBAEM;UACNhR,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAAuQ,yDAAAlB,MAAA;YAAA,OAAUxF,GAAA,CAAAnB,cAAA,CAAA2G,MAAA,CAAsB;UAAA,EAAC;UAA/D/P,EAAA,CAAAG,YAAA,EAAwG;UACxGH,EAAA,CAAAsB,UAAA,MAAA4P,4CAAA,wBAEY;UACdlR,EAAA,CAAAG,YAAA,EAAM;UAWtBH,EAAA,CAAAC,cAAA,gBAAU;UACRD,EAAA,CAAAsB,UAAA,MAAA6P,8CAAA,0BAGc;UAEdnR,EAAA,CAAAC,cAAA,gBAAwC;UAKlCD,EAAA,CAAAsB,UAAA,MAAA8P,8CAAA,0BAGc;UAEdpR,EAAA,CAAAC,cAAA,gBAA4B;UAGMD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChDH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,yCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE3CH,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAE,MAAA,kHACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,gBAA0B;UACxBD,EAAA,CAAAI,SAAA,yBAEe;UACjBJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA2Q,yDAAA;YAAA,OAAS9G,GAAA,CAAAkB,iBAAA,EAAmB;UAAA,EAAC;UACrEzL,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxCH,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAO1CH,EAAA,CAAAC,cAAA,oBAAuC;UACrCD,EAAA,CAAAsB,UAAA,MAAAgQ,8CAAA,0BAGc;UAEdtR,EAAA,CAAAC,cAAA,gBAAsC;UAEpCD,EAAA,CAAAsB,UAAA,MAAAiQ,sCAAA,mBAiBM;UAGNvR,EAAA,CAAAsB,UAAA,MAAAkQ,sCAAA,mBAyDM;UAGNxR,EAAA,CAAAsB,UAAA,MAAAmQ,sCAAA,mBAuDM;UAGNzR,EAAA,CAAAsB,UAAA,MAAAoQ,sCAAA,mBAcM;UACR1R,EAAA,CAAAG,YAAA,EAAM;;;UApXfH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA2R,eAAA,KAAAC,GAAA,EAAkC;UAIlC5R,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA2R,eAAA,KAAAE,GAAA,EAAqC;UAMJ7R,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAiC,iBAAA,CAAAsI,GAAA,CAAA7F,UAAA,yBAAsC;UAClE1E,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAiC,iBAAA,CAAAsI,GAAA,CAAA7F,UAAA,kCAA+C;UAOrD1E,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAkB,kBAAA,MAAAqJ,GAAA,CAAA7F,UAAA,4BACF;UAQ2C1E,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,kBAAAiK,GAAA,CAAA5F,gBAAA,CAAkC;UAS1C3E,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAiK,GAAA,CAAA5E,gBAAA,CAA8B;UAgBrC3F,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA5E,gBAAA,CAAAqD,GAAA,aAAA8I,QAAA,mBAAiE;UAUjE9R,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA5E,gBAAA,CAAAqD,GAAA,cAAA8I,QAAA,oBAAmE;UAanE9R,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA5E,gBAAA,CAAAqD,GAAA,WAAA8I,QAAA,iBAA6D;UAcvE9R,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA9F,YAAA,uBAA2C;UAEjCzE,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAiC,iBAAA,CAAAsI,GAAA,CAAA9F,YAAA,mCAAkD;UA+C7BzE,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA/J,OAAA,CAAa;UAGTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAAiK,GAAA,CAAA/J,OAAA,CAAc;UAMhCR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAAiK,GAAA,CAAAjG,kBAAA,CAAyB;UAC9BtE,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAAjG,kBAAA,CAAwB;UAMpBtE,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAA5E,gBAAA,CAAAqD,GAAA,SAAAjB,OAAA,IAAAwC,GAAA,CAAA5E,gBAAA,CAAAqD,GAAA,SAAA+I,OAAA,CAAkF;UA0CtF/R,EAAA,CAAAK,SAAA,IAA4C;UAA5CL,EAAA,CAAAM,UAAA,aAAAiK,GAAA,CAAA5E,gBAAA,CAAAH,KAAA,CAAAK,QAAA,CAA4C,eAAA0E,GAAA,CAAA5E,gBAAA,CAAAH,KAAA,CAAAO,UAAA;UAevD/F,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAM,UAAA,cAAAiK,GAAA,CAAA1F,eAAA,CAA6B;UAQ5B7E,EAAA,CAAAK,SAAA,GAA4D;UAA5DL,EAAA,CAAAM,UAAA,UAAAiK,GAAA,CAAAxF,aAAA,KAAAwF,GAAA,CAAAvF,gBAAA,KAAAuF,GAAA,CAAAtF,cAAA,CAA4D;UAoB5DjF,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAAxF,aAAA,CAAmB;UA4DnB/E,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAAvF,gBAAA,CAAsB;UA0DtBhF,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAM,UAAA,SAAAiK,GAAA,CAAAtF,cAAA,CAAoB;;;qBDtU1CxG,YAAY,EAAAuT,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZtT,aAAa,EAAAuT,GAAA,CAAAC,OAAA,EACbvT,cAAc,EAAAwT,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACd7T,gBAAgB,EAChBL,WAAW,EAAAuP,EAAA,CAAA4E,aAAA,EAAA5E,EAAA,CAAA6E,oBAAA,EAAA7E,EAAA,CAAA8E,eAAA,EAAA9E,EAAA,CAAA+E,oBAAA,EACXrU,mBAAmB,EAAAsP,EAAA,CAAAgF,kBAAA,EAAAhF,EAAA,CAAAiF,eAAA,EACnB9T,cAAc,EAAA+T,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdhU,eAAe,EAAAiU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACflU,aAAa,EAAAmU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACbzU,eAAe,EACfN,oBAAoB,EAAAgV,GAAA,CAAAC,cAAA,EACpBhV,aAAa,EAAAiV,GAAA,CAAAC,WAAA,EAAAD,GAAA,CAAAE,MAAA,EAAAF,GAAA,CAAAG,WAAA,EACb9U,gBAAgB,EAChBL,YAAY,EAAA2O,EAAA,CAAAyG,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHrR,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}