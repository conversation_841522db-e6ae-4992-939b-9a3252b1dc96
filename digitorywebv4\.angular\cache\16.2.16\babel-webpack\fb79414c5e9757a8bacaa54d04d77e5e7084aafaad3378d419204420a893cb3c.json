{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/tooltip\";\nimport * as i16 from \"@angular/material/radio\";\nimport * as i17 from \"@angular/material/button\";\nimport * as i18 from \"@angular/material/card\";\nimport * as i19 from \"@angular/material/progress-bar\";\nfunction AccountSetupComponent_mat_error_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"img\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 52);\n    i0.ɵɵtext(1, \" Please upload a company logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"h3\")(4, \"mat-icon\", 63);\n    i0.ɵɵtext(5, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 64)(14, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Get Started \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_4_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 75);\n    i0.ɵɵtext(1, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_4_app_chat_bot_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-bot\", 76);\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"tenantId\", ctx_r17.registrationForm.value.tenantId)(\"tenantName\", ctx_r17.registrationForm.value.tenantName);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"chat-bot-minimized\": a0\n  };\n};\nfunction AccountSetupComponent_mat_card_133_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"h3\")(3, \"mat-icon\", 68);\n    i0.ɵɵtext(4, \"smart_toy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Information Assistant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 69)(7, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.toggleChatBot());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.startAIProcessing());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"skip_next\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 72);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_133_div_4_p_14_Template, 2, 0, \"p\", 73);\n    i0.ɵɵtemplate(15, AccountSetupComponent_mat_card_133_div_4_app_chat_bot_15_Template, 1, 2, \"app-chat-bot\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r10.chatBotMinimized ? \"Expand\" : \"Minimize\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.chatBotMinimized ? \"expand_more\" : \"expand_less\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r10.chatBotMinimized));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.chatBotMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.chatBotMinimized);\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r21.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"span\", 99);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_133_div_5_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 23);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_133_div_5_div_16_div_3_Template, 3, 0, \"div\", 94);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 95)(6, \"div\", 96);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 97);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r25 = ctx.$implicit;\n    const i_r26 = ctx.index;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c1, step_r25.completed, ctx_r24.activeStep === i_r26, !step_r25.completed && ctx_r24.activeStep !== i_r26));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r25.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r25.completed && ctx_r24.activeStep === i_r26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r25.completed && ctx_r24.activeStep !== i_r26);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r25.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r25.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"h3\", 78)(2, \"mat-icon\", 79);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 80);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 81);\n    i0.ɵɵelementStart(7, \"div\", 82);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 83)(10, \"mat-icon\", 84);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_133_div_5_span_12_Template, 3, 4, \"span\", 23);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_133_div_5_span_13_Template, 2, 0, \"span\", 23);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_133_div_5_span_14_Template, 2, 0, \"span\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 86);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_133_div_5_div_16_Template, 10, 10, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 88)(18, \"div\", 89)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 90);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r11.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 0 && ctx_r11.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"mat-icon\", 102);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 103);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 104)(9, \"div\", 105)(10, \"mat-card\", 106)(11, \"mat-card-header\")(12, \"div\", 107)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-card-title\");\n    i0.ɵɵtext(16, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-subtitle\");\n    i0.ɵɵtext(18, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_6_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.downloadInventory());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Download \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 105)(25, \"mat-card\", 106)(26, \"mat-card-header\")(27, \"div\", 109)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-card-title\");\n    i0.ɵɵtext(31, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-card-subtitle\");\n    i0.ɵɵtext(33, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card-actions\")(35, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_6_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.downloadPackaging());\n    });\n    i0.ɵɵelementStart(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Download \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 111)(2, \"mat-icon\", 112);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 113);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 114)(9, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_133_div_7_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 53)(1, \"div\", 54);\n    i0.ɵɵtext(2, \"AI-Powered Data Generation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_133_div_3_Template, 18, 0, \"div\", 55);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_133_div_4_Template, 16, 7, \"div\", 56);\n    i0.ɵɵtemplate(5, AccountSetupComponent_mat_card_133_div_5_Template, 25, 6, \"div\", 57);\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_133_div_6_Template, 39, 0, \"div\", 58);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_133_div_7_Template, 13, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.isDownloading && !ctx_r8.downloadComplete && !ctx_r8.downloadFailed && !ctx_r8.showChatBot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.showChatBot && !ctx_r8.isDownloading && !ctx_r8.downloadComplete && !ctx_r8.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.downloadFailed);\n  }\n}\nconst _c2 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c3 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // Chat bot related properties\n    this.showChatBot = false;\n    this.chatBotMinimized = false;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.prefillData(this.dialogData.elements);\n      this.isEditMode = true;\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n                this.isEditMode = true;\n              } else {\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  toggleChatBot() {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 134,\n      vars: 20,\n      consts: [[1, \"account-setup-container\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-section\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"basic\", 1, \"cancel-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-grid\"], [1, \"form-column\"], [1, \"section-title\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"logo-upload-section\"], [1, \"logo-container\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [1, \"status-section\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\"], [\"value\", \"no\", \"color\", \"primary\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"text-center\", \"p-2\", \"my-2\", \"bottomTitles\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"ai-intro-panel\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-8\"], [1, \"ai-icon\"], [1, \"col-md-4\", \"text-center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-ai-btn\", 3, \"click\"], [1, \"chat-bot-section\"], [1, \"chat-bot-header\"], [1, \"chat-bot-icon\"], [1, \"chat-bot-actions\"], [\"mat-icon-button\", \"\", 3, \"matTooltip\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Skip to AI Processing\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"chat-bot-description\", 4, \"ngIf\"], [3, \"tenantId\", \"tenantName\", 4, \"ngIf\"], [1, \"chat-bot-description\"], [3, \"tenantId\", \"tenantName\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"row\", \"download-options\"], [1, \"col-md-6\", \"mb-3\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r35 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2)(3, \"mat-icon\", 3);\n          i0.ɵɵtext(4, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"span\", 4);\n          i0.ɵɵtext(8, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 2)(10, \"mat-icon\", 3);\n          i0.ɵɵtext(11, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"span\", 4);\n          i0.ɵɵtext(15, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\", 5)(17, \"mat-icon\", 3);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 6)(22, \"div\", 7)(23, \"h1\", 8);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 9);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 10)(28, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_28_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(29, \"mat-icon\");\n          i0.ɵɵtext(30, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_32_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelementStart(33, \"mat-icon\");\n          i0.ɵɵtext(34, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35, \" Back to Accounts \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 13)(37, \"mat-card\", 14)(38, \"mat-card-header\")(39, \"mat-card-title\");\n          i0.ɵɵtext(40, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"mat-card-subtitle\");\n          i0.ɵɵtext(42, \"Enter the tenant account details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-card-content\")(44, \"form\", 15)(45, \"div\", 16)(46, \"div\", 17)(47, \"h3\", 18);\n          i0.ɵɵtext(48, \"Basic Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"mat-form-field\", 19)(50, \"mat-label\");\n          i0.ɵɵtext(51, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"input\", 20);\n          i0.ɵɵelementStart(53, \"mat-icon\", 21);\n          i0.ɵɵtext(54, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"mat-form-field\", 19)(56, \"mat-label\");\n          i0.ɵɵtext(57, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 22);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_58_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"mat-icon\", 21);\n          i0.ɵɵtext(60, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(61, AccountSetupComponent_mat_error_61_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-form-field\", 19)(63, \"mat-label\");\n          i0.ɵɵtext(64, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"input\", 24);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_65_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"mat-icon\", 21);\n          i0.ɵɵtext(67, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(68, AccountSetupComponent_mat_error_68_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"mat-form-field\", 19)(70, \"mat-label\");\n          i0.ɵɵtext(71, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"input\", 25);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_72_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-icon\", 21);\n          i0.ɵɵtext(74, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(75, AccountSetupComponent_mat_error_75_Template, 2, 0, \"mat-error\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 17)(77, \"h3\", 18);\n          i0.ɵɵtext(78, \"Authentication\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"mat-form-field\", 19)(80, \"mat-label\");\n          i0.ɵɵtext(81, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"input\", 26);\n          i0.ɵɵelementStart(83, \"mat-icon\", 21);\n          i0.ɵɵtext(84, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 19)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(88, \"input\", 27);\n          i0.ɵɵelementStart(89, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_89_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(90, \"mat-icon\");\n          i0.ɵɵtext(91);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"div\", 29)(93, \"h3\", 18);\n          i0.ɵɵtext(94, \"Company Logo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 30);\n          i0.ɵɵtemplate(96, AccountSetupComponent_div_96_Template, 2, 1, \"div\", 31);\n          i0.ɵɵelementStart(97, \"div\", 32)(98, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_98_listener() {\n            i0.ɵɵrestoreView(_r35);\n            const _r6 = i0.ɵɵreference(103);\n            return i0.ɵɵresetView(_r6.click());\n          });\n          i0.ɵɵtemplate(99, AccountSetupComponent_div_99_Template, 3, 0, \"div\", 34);\n          i0.ɵɵtemplate(100, AccountSetupComponent_mat_icon_100_Template, 2, 0, \"mat-icon\", 23);\n          i0.ɵɵtext(101, \" Upload Logo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"input\", 35, 36);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_102_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(104, AccountSetupComponent_mat_error_104_Template, 2, 0, \"mat-error\", 37);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(105, \"div\", 38)(106, \"h3\", 18);\n          i0.ɵɵtext(107, \"Account Status Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 39)(109, \"div\", 40)(110, \"label\", 41);\n          i0.ɵɵtext(111, \"Account Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"mat-radio-group\", 42)(113, \"mat-radio-button\", 43);\n          i0.ɵɵtext(114, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"mat-radio-button\", 44);\n          i0.ɵɵtext(116, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(117, \"div\", 40)(118, \"label\", 41);\n          i0.ɵɵtext(119, \"Forecast Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"mat-radio-group\", 45)(121, \"mat-radio-button\", 43);\n          i0.ɵɵtext(122, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"mat-radio-button\", 44);\n          i0.ɵɵtext(124, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(125, \"div\", 40)(126, \"label\", 41);\n          i0.ɵɵtext(127, \"Sales Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"mat-radio-group\", 46)(129, \"mat-radio-button\", 43);\n          i0.ɵɵtext(130, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"mat-radio-button\", 44);\n          i0.ɵɵtext(132, \"Disabled\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(133, AccountSetupComponent_mat_card_133_Template, 8, 5, \"mat-card\", 47);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c2));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c3));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit \" + (((tmp_3_0 = ctx.registrationForm.get(\"tenantName\")) == null ? null : tmp_3_0.value) || \"Account\") : \"New Account\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"Create New Account\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Update tenant account details\" : \"Set up a new tenant account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update Account\" : \"Create Account\", \" \");\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, i15.MatTooltip, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i16.MatRadioGroup, i16.MatRadioButton, MatButtonModule, i17.MatButton, i17.MatIconButton, MatCardModule, i18.MatCard, i18.MatCardActions, i18.MatCardAvatar, i18.MatCardContent, i18.MatCardHeader, i18.MatCardSubtitle, i18.MatCardTitle, MatSelectModule, MatProgressBarModule, i19.MatProgressBar, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  font-size: 14px;\\n  background-color: #f5f7fa;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 500;\\n  margin: 0;\\n  color: #333;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 4px 0 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  padding: 16px 16px 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n  margin-bottom: 24px;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-column[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-column[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  margin-top: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 140px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: contain;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .logo-upload-section[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #555;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .status-section[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 20px;\\n  background-color: #e8eaf6;\\n  border-bottom: 1px solid #c5cae9;\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-bot-minimized[_ngcontent-%COMP%] {\\n  height: 0;\\n  overflow: hidden;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 500px;\\n  padding: 0 20px 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY3JtLW1hbmFnZW1lbnQvYWNjb3VudC1zZXR1cC9hY2NvdW50LXNldHVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsYUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQUFGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLHlCQUFBO0FBREo7QUFHSTtFQUNFLFdBQUE7RUFDQSxxQkFBQTtFQUNBLDJCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBRE47QUFHTTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGlCQUFBO0FBRFI7QUFJTTtFQUNFLGNBQUE7RUFDQSwwQkFBQTtBQUZSO0FBS007RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUFIUjtBQU9JO0VBQ0UsYUFBQTtFQUNBLFdBQUE7QUFMTjtBQVNJO0VBMUNGO0lBMkNJLGVBQUE7RUFOSjtFQVNNO0lBQ0UsZUFBQTtJQUNBLFlBQUE7SUFDQSxXQUFBO0VBUFI7RUFXSTtJQUNFLGFBQUE7RUFUTjtBQUNGO0FBY0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBWko7QUFlTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0FBYlI7QUFnQk07RUFDRSxXQUFBO0VBQ0EsZUFBQTtBQWRSO0FBa0JJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUFoQk47QUFrQk07RUFDRSxnQkFBQTtBQWhCUjtBQW1CTTtFQUNFLGdCQUFBO0FBakJSO0FBd0JJO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLHdDQUFBO0FBdEJOO0FBd0JNO0VBQ0Usb0JBQUE7QUF0QlI7QUF3QlE7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7QUF0QlY7QUF5QlE7RUFDRSxlQUFBO0FBdkJWO0FBMkJNO0VBQ0UsYUFBQTtBQXpCUjtBQStCTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQTdCUjtBQStCUTtFQU5GO0lBT0ksMEJBQUE7RUE1QlI7QUFDRjtBQWdDUTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBQUE7QUE5QlY7QUFpQ1E7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7QUEvQlY7QUFvQ007RUFDRSxnQkFBQTtBQWxDUjtBQW9DUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtBQWxDVjtBQW9DVTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFsQ1o7QUFvQ1k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0FBbENkO0FBc0NVO0VBQ0UsT0FBQTtBQXBDWjtBQXNDWTtFQUNFLGtCQUFBO0FBcENkO0FBdUNZO0VBQ0UsZUFBQTtFQUNBLGVBQUE7QUFyQ2Q7QUE0Q007RUFDRSxnQkFBQTtBQTFDUjtBQTRDUTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBQUE7QUExQ1Y7QUE2Q1E7RUFDRSxhQUFBO0VBQ0EscUNBQUE7RUFDQSxTQUFBO0FBM0NWO0FBNkNVO0VBTEY7SUFNSSwwQkFBQTtFQTFDVjtBQUNGO0FBNkNZO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0FBM0NkO0FBOENZO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtBQTVDZDs7QUFzREEsMkJBQUE7QUFDQTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx3Q0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBT0Esd0JBQUE7RUFnQkEseUJBQUE7RUFVQSw0QkFBQTtFQTZIQSwwQkFBQTtFQXFFQSx1QkFBQTtBQWpSRjtBQWdERTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7QUE5Q0o7QUFrREU7Ozs7RUFJRSxlQUFBO0VBQ0Esa0JBQUE7QUFoREo7QUFtREU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7QUFqREo7QUFxREU7RUFDRSxpQkFBQTtFQUNBLGVBQUE7QUFuREo7QUFxREk7RUFDRSxpQkFBQTtBQW5ETjtBQXdERTtFQTZDRSxrQkFBQTtFQW9EQSx5QkFBQTtBQXJKSjtBQXFESTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0VBT0EsMkNBQUE7QUF6RE47QUFvRE07RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFsRFI7QUFzRE07RUFDRSxvQ0FBQTtBQXBEUjtBQXVETTtFQUNFO0lBQU8sdUJBQUE7RUFwRGI7RUFxRE07SUFBSyx5QkFBQTtFQWxEWDtBQUNGO0FBcURJO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQW5ETjtBQXFETTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBbkRSO0FBdURJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLHFCQUFBO0FBckROO0FBdURNO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0FBckRSO0FBMERJO0VBQ0UsY0FBQTtBQXhETjtBQTBETTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBeERSO0FBMERRO0VBQ0Usd0NBQUE7QUF4RFY7QUEwRFU7RUFDRSxjQUFBO0FBeERaO0FBNERRO0VBQ0UseUNBQUE7RUFDQSw4QkFBQTtBQTFEVjtBQTZEUTtFQUNFLDZCQUFBO0VBQ0EsWUFBQTtBQTNEVjtBQThEUTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUE1RFY7QUErRFE7RUFDRSxZQUFBO0FBN0RWO0FBK0RVO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQTdEWjtBQWdFVTtFQUNFLFdBQUE7RUFDQSxlQUFBO0FBOURaO0FBcUVJO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsMENBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0FBbkVOO0FBcUVNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFuRVI7QUFxRVE7RUFDRSxpQkFBQTtBQW5FVjtBQXVFTTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtBQXJFUjtBQTRFSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBMUVOO0FBNEVNO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBMUVSO0FBNkVNO0VBQ0UsY0FBQTtFQUNBLFNBQUE7QUEzRVI7QUErRUk7RUFDRSxtQkFBQTtFQUNBLGVBQUE7QUE3RU47QUFnRkk7RUFDRSxrQkFBQTtBQTlFTjtBQWdGTTtFQUNFLFlBQUE7RUFDQSwwQkFBQTtBQTlFUjtBQWdGUTtFQUNFLDJCQUFBO0VBQ0EseUNBQUE7QUE5RVY7QUFpRlE7RUFDRSxtQkFBQTtBQS9FVjtBQWlGVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQS9FWjtBQWlGWTtFQUNFLGNBQUE7QUEvRWQ7QUFxRlU7RUFDRSxrQkFBQTtBQW5GWjtBQXFGWTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtBQW5GZDtBQXdGUTtFQUNFLHNCQUFBO0FBdEZWO0FBOEZJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUE1Rk47QUE4Rk07RUFDRSxjQUFBO0VBQ0Esa0JBQUE7QUE1RlI7QUErRk07RUFDRSxjQUFBO0VBQ0EsU0FBQTtBQTdGUjtBQWlHSTtFQUNFLG1CQUFBO0VBQ0EsZUFBQTtBQS9GTjtBQWtHSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUFoR047O0FBcUdBLDJCQUFBO0FBQ0E7RUFHTTtJQUNFLG1CQUFBO0VBcEdOO0VBeUdJO0lBQ0UsV0FBQTtJQUNBLGdCQUFBO0VBdkdOO0FBQ0Y7QUE0R0EsK0JBQUE7QUFDQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0FBMUdGOztBQTZHQTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBMUdGOztBQThHQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtBQTNHRjs7QUE4R0E7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxrQ0FBQTtBQTNHRjs7QUE4R0E7RUFDRTtJQUFXLFlBQUE7RUExR1g7RUEyR0E7SUFBTSxVQUFBO0VBeEdOO0FBQ0Y7QUEwR0Esb0JBQUE7QUFDQTtFQUNFLGNBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0Esd0NBQUE7QUF4R0Y7O0FBMkdBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0NBQUE7QUF4R0Y7O0FBMkdBO0VBQ0UsU0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBeEdGOztBQTJHQTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtBQXhHRjs7QUEyR0E7RUFDRSxhQUFBO0VBQ0EsUUFBQTtBQXhHRjs7QUEyR0E7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtFQUNBLGdDQUFBO0FBeEdGOztBQTJHQTtFQUNFLFNBQUE7RUFDQSxnQkFBQTtBQXhHRjs7QUEyR0EsOENBQUE7QUFDQTtFQUNFLGNBQUE7RUFDQSxhQUFBO0VBQ0Esb0JBQUE7QUF4R0YiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBNYWluIGNvbnRhaW5lciBzdHlsZXNcbi5hY2NvdW50LXNldHVwLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDI0cHg7XG4gIG1heC13aWR0aDogMTQwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcblxuICAvLyBCcmVhZGNydW1icyBzdHlsZXNcbiAgLmJyZWFkY3J1bWJzIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTtcbiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIGJveC1zaGFkb3c6IDAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGU0ZTg7XG5cbiAgICAuYnJlYWRjcnVtYi1pdGVtIHtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgICAgIC5icmVhZGNydW1iLWljb24ge1xuICAgICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICAgIGhlaWdodDogMThweDtcbiAgICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICAgIG1hcmdpbi1yaWdodDogNHB4O1xuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgY29sb3I6ICMzZjUxYjU7XG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgfVxuXG4gICAgICAmLmFjdGl2ZSB7XG4gICAgICAgIGNvbG9yOiAjM2Y1MWI1O1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5icmVhZGNydW1iLXNlcGFyYXRvciB7XG4gICAgICBtYXJnaW46IDAgOHB4O1xuICAgICAgY29sb3I6ICM5OTk7XG4gICAgfVxuXG4gICAgLy8gUmVzcG9uc2l2ZSBzdHlsZXMgZm9yIGJyZWFkY3J1bWJzXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG5cbiAgICAgIC5icmVhZGNydW1iLWl0ZW0ge1xuICAgICAgICAuYnJlYWRjcnVtYi1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5icmVhZGNydW1iLXNlcGFyYXRvciB7XG4gICAgICAgIG1hcmdpbjogMCA0cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gSGVhZGVyIHNlY3Rpb25cbiAgLmhlYWRlci1zZWN0aW9uIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG5cbiAgICAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgLnBhZ2UtdGl0bGUge1xuICAgICAgICBmb250LXNpemU6IDI4cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgY29sb3I6ICMzMzM7XG4gICAgICB9XG5cbiAgICAgIC5wYWdlLXN1YnRpdGxlIHtcbiAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgIG1hcmdpbjogNHB4IDAgMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMTJweDtcblxuICAgICAgLnNhdmUtYnV0dG9uIHtcbiAgICAgICAgbWluLXdpZHRoOiAxNDBweDtcbiAgICAgIH1cblxuICAgICAgLmNhbmNlbC1idXR0b24ge1xuICAgICAgICBtaW4td2lkdGg6IDE0MHB4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIENvbnRlbnQgc2VjdGlvblxuICAuY29udGVudC1zZWN0aW9uIHtcbiAgICAuZm9ybS1jYXJkIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuXG4gICAgICBtYXQtY2FyZC1oZWFkZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4IDE2cHggMDtcblxuICAgICAgICBtYXQtY2FyZC10aXRsZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIH1cblxuICAgICAgICBtYXQtY2FyZC1zdWJ0aXRsZSB7XG4gICAgICAgICAgbWFyZ2luLXRvcDogNHB4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIG1hdC1jYXJkLWNvbnRlbnQge1xuICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZvcm0gc3R5bGVzXG4gICAgLmFjY291bnQtZm9ybSB7XG4gICAgICAuZm9ybS1ncmlkIHtcbiAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyO1xuICAgICAgICBnYXA6IDI0cHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG5cbiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmZvcm0tY29sdW1uIHtcbiAgICAgICAgLnNlY3Rpb24tdGl0bGUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gICAgICAgICAgY29sb3I6ICM1NTU7XG4gICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7XG4gICAgICAgICAgcGFkZGluZy1ib3R0b206IDhweDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5mdWxsLXdpZHRoIHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIExvZ28gdXBsb2FkIHNlY3Rpb25cbiAgICAgIC5sb2dvLXVwbG9hZC1zZWN0aW9uIHtcbiAgICAgICAgbWFyZ2luLXRvcDogMTZweDtcblxuICAgICAgICAubG9nby1jb250YWluZXIge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6IDE2cHg7XG4gICAgICAgICAgbWFyZ2luLXRvcDogMTJweDtcblxuICAgICAgICAgIC5sb2dvLXByZXZpZXcge1xuICAgICAgICAgICAgd2lkdGg6IDE0MHB4O1xuICAgICAgICAgICAgaGVpZ2h0OiAxNDBweDtcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgICAgaW1nIHtcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICAgICAgb2JqZWN0LWZpdDogY29udGFpbjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAubG9nby1hY3Rpb25zIHtcbiAgICAgICAgICAgIGZsZXg6IDE7XG5cbiAgICAgICAgICAgIC51cGxvYWQtYnV0dG9uIHtcbiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAubG9nby1lcnJvciB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICAgICAgbWFyZ2luLXRvcDogNHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBTdGF0dXMgc2VjdGlvblxuICAgICAgLnN0YXR1cy1zZWN0aW9uIHtcbiAgICAgICAgbWFyZ2luLXRvcDogMTZweDtcblxuICAgICAgICAuc2VjdGlvbi10aXRsZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICAgICAgICBjb2xvcjogIzU1NTtcbiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTtcbiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogOHB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLnN0YXR1cy1vcHRpb25zIHtcbiAgICAgICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7XG4gICAgICAgICAgZ2FwOiAxNnB4O1xuXG4gICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuc3RhdHVzLW9wdGlvbiB7XG4gICAgICAgICAgICAuc3RhdHVzLWxhYmVsIHtcbiAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgICAgY29sb3I6ICM1NTU7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5zdGF0dXMtcmFkaW8tZ3JvdXAge1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgICBnYXA6IDhweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuXG4vKiBBSSBEYXRhIFNlY3Rpb24gU3R5bGVzICovXG4uYWktZGF0YS1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBtYXJnaW4tdG9wOiAycmVtO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgLmJvdHRvbVRpdGxlcyB7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgfVxuXG4gIC8qIENvbW1vbiBwYW5lbCBzdHlsZXMgKi9cbiAgLmFpLWludHJvLXBhbmVsLFxuICAuYWktcHJvY2Vzc2luZy1wYW5lbCxcbiAgLmFpLWNvbXBsZXRlLXBhbmVsLFxuICAuYWktZXJyb3ItcGFuZWwge1xuICAgIHBhZGRpbmc6IDEuNXJlbTtcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIH1cblxuICAuYWktaWNvbiB7XG4gICAgY29sb3I6ICM2NzNhYjc7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XG4gICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gIH1cblxuICAvKiBTdGFydCBidXR0b24gc3R5bGluZyAqL1xuICAuc3RhcnQtYWktYnRuIHtcbiAgICBwYWRkaW5nOiA4cHggMjRweDtcbiAgICBmb250LXNpemU6IDE2cHg7XG5cbiAgICBtYXQtaWNvbiB7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgICB9XG4gIH1cblxuICAvKiBQcm9jZXNzaW5nIHBhbmVsIHN0eWxlcyAqL1xuICAuYWktcHJvY2Vzc2luZy1wYW5lbCB7XG4gICAgLnByb2Nlc3NpbmctdGl0bGUge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAgIC5wcm9jZXNzaW5nLWljb24ge1xuICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gICAgICAgIGNvbG9yOiAjNjczYWI3O1xuICAgICAgfVxuXG4gICAgICAvKiBSb3RhdGlvbiBhbmltYXRpb24gZm9yIHByb2Nlc3NpbmcgaWNvbiAqL1xuICAgICAgLnJvdGF0aW5nIHtcbiAgICAgICAgYW5pbWF0aW9uOiByb3RhdGUgMnMgbGluZWFyIGluZmluaXRlO1xuICAgICAgfVxuXG4gICAgICBAa2V5ZnJhbWVzIHJvdGF0ZSB7XG4gICAgICAgIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxuICAgICAgICB0byB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAucHJvZ3Jlc3MtY29udGFpbmVyIHtcbiAgICAgIG1hcmdpbjogMS41cmVtIDA7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgICAgIC5wcm9ncmVzcy1sYWJlbCB7XG4gICAgICAgIG1hcmdpbi10b3A6IDhweDtcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5lc3RpbWF0ZWQtdGltZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuXG4gICAgICBtYXQtaWNvbiB7XG4gICAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICAgICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLyogU3RlcHMgc3R5bGluZyAqL1xuICAgIC5wcm9jZXNzaW5nLXN0ZXBzIHtcbiAgICAgIG1hcmdpbjogMnJlbSAwO1xuXG4gICAgICAuc3RlcC1yb3cge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICAgJi5jb21wbGV0ZWQtc3RlcCB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg3NiwgMTc1LCA4MCwgMC4xKTtcblxuICAgICAgICAgIC5zdGVwLXN0YXR1cyBtYXQtaWNvbiB7XG4gICAgICAgICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAmLmFjdGl2ZS1zdGVwIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEwMywgNTgsIDE4MywgMC4xKTtcbiAgICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM2NzNhYjc7XG4gICAgICAgIH1cblxuICAgICAgICAmLnBlbmRpbmctc3RlcCB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgb3BhY2l0eTogMC43O1xuICAgICAgICB9XG5cbiAgICAgICAgLnN0ZXAtc3RhdHVzIHtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICB9XG5cbiAgICAgICAgLnN0ZXAtZGV0YWlscyB7XG4gICAgICAgICAgZmxleC1ncm93OiAxO1xuXG4gICAgICAgICAgLnN0ZXAtbmFtZSB7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5zdGVwLWRlc2NyaXB0aW9uIHtcbiAgICAgICAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8qIFRpcHMgc2VjdGlvbiBzdHlsaW5nICovXG4gICAgLnRpcHMtc2VjdGlvbiB7XG4gICAgICBtYXJnaW4tdG9wOiAycmVtO1xuICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDE1MCwgMjQzLCAwLjA1KTtcbiAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzIxOTZmMztcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcblxuICAgICAgLnRpcC1oZWFkZXIge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBjb2xvcjogIzIxOTZmMztcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuXG4gICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAudGlwLWNvbnRlbnQge1xuICAgICAgICBjb2xvcjogIzU1NTtcbiAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qIENvbXBsZXRlIHBhbmVsIHN0eWxlcyAqL1xuICAuYWktY29tcGxldGUtcGFuZWwge1xuICAgIC5zdWNjZXNzLWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcblxuICAgICAgLnN1Y2Nlc3MtaWNvbiB7XG4gICAgICAgIGNvbG9yOiAjNGNhZjUwO1xuICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gICAgICB9XG5cbiAgICAgIGgzIHtcbiAgICAgICAgY29sb3I6ICM0Y2FmNTA7XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuc3VjY2Vzcy1tZXNzYWdlIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgfVxuXG4gICAgLmRvd25sb2FkLW9wdGlvbnMge1xuICAgICAgbWFyZ2luLXRvcDogMS41cmVtO1xuXG4gICAgICAuZG93bmxvYWQtY2FyZCB7XG4gICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnM7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgICAgICB9XG5cbiAgICAgICAgbWF0LWNhcmQtaGVhZGVyIHtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuXG4gICAgICAgICAgLmludmVudG9yeS1pY29uLCAucGFja2FnaW5nLWljb24ge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgY29sb3I6ICM2NzNhYjc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgbWF0LWNhcmQtY29udGVudCB7XG4gICAgICAgICAgdWwge1xuICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAyMHB4O1xuXG4gICAgICAgICAgICBsaSB7XG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1jYXJkLWFjdGlvbnMge1xuICAgICAgICAgIHBhZGRpbmc6IDhweCAxNnB4IDE2cHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKiBFcnJvciBwYW5lbCBzdHlsZXMgKi9cbiAgLmFpLWVycm9yLXBhbmVsIHtcbiAgICAuZXJyb3ItaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuXG4gICAgICAuZXJyb3ItaWNvbiB7XG4gICAgICAgIGNvbG9yOiAjZjQ0MzM2O1xuICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gICAgICB9XG5cbiAgICAgIGgzIHtcbiAgICAgICAgY29sb3I6ICNmNDQzMzY7XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZXJyb3ItbWVzc2FnZSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgIH1cblxuICAgIC5lcnJvci1hY3Rpb25zIHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIG1hcmdpbi10b3A6IDFyZW07XG4gICAgfVxuICB9XG59XG5cbi8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuYWktZGF0YS1zZWN0aW9uIHtcbiAgICAuZG93bmxvYWQtb3B0aW9ucyB7XG4gICAgICAuY29sLW1kLTYge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5haS1pbnRyby1wYW5lbCB7XG4gICAgICAuc3RhcnQtYWktYnRuIHtcbiAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgIG1hcmdpbi10b3A6IDFyZW07XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8qIEFkZGl0aW9uYWwgdXRpbGl0eSBjbGFzc2VzICovXG4uc3Bpbm5lci1ib3JkZXItc20ge1xuICB3aWR0aDogMjBweDtcbiAgaGVpZ2h0OiAyMHB4O1xufVxuXG4udmlzdWFsbHktaGlkZGVuIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB3aWR0aDogMXB4O1xuICBoZWlnaHQ6IDFweDtcbiAgbWFyZ2luOiAtMXB4O1xuICBwYWRkaW5nOiAwO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBjbGlwOiByZWN0KDAsIDAsIDAsIDApO1xuICBib3JkZXI6IDA7XG59XG5cblxuLmVzdGltYXRlZC10aW1lIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA4cHg7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgY29sb3I6ICNjY2M7XG59XG5cbi5jYWxjdWxhdGluZyB7XG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgY29sb3I6ICNmN2NlMmE7XG4gIGFuaW1hdGlvbjogZmFkZUluT3V0IDEuNXMgaW5maW5pdGU7XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluT3V0IHtcbiAgMCUsIDEwMCUgeyBvcGFjaXR5OiAwLjY7IH1cbiAgNTAlIHsgb3BhY2l0eTogMTsgfVxufVxuXG4vKiBDaGF0IEJvdCBTdHlsZXMgKi9cbi5jaGF0LWJvdC1zZWN0aW9uIHtcbiAgbWFyZ2luOiAyMHB4IDA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLmNoYXQtYm90LWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMTVweCAyMHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThlYWY2O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2M1Y2FlOTtcbn1cblxuLmNoYXQtYm90LWhlYWRlciBoMyB7XG4gIG1hcmdpbjogMDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZm9udC1zaXplOiAxOHB4O1xuICBjb2xvcjogIzNmNTFiNTtcbn1cblxuLmNoYXQtYm90LWljb24ge1xuICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gIGNvbG9yOiAjM2Y1MWI1O1xufVxuXG4uY2hhdC1ib3QtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogNXB4O1xufVxuXG4uY2hhdC1ib3QtZGVzY3JpcHRpb24ge1xuICBwYWRkaW5nOiAxNXB4IDIwcHg7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM1NTU7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XG59XG5cbi5jaGF0LWJvdC1taW5pbWl6ZWQge1xuICBoZWlnaHQ6IDA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIE1ha2Ugc3VyZSB0aGUgY2hhdCBib3QgaGFzIGEgZml4ZWQgaGVpZ2h0ICovXG5hcHAtY2hhdC1ib3Qge1xuICBkaXNwbGF5OiBibG9jaztcbiAgaGVpZ2h0OiA1MDBweDtcbiAgcGFkZGluZzogMCAyMHB4IDIwcHg7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_133_div_3_Template_button_click_14_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "ctx_r17", "registrationForm", "value", "tenantId", "tenantName", "AccountSetupComponent_mat_card_133_div_4_Template_button_click_7_listener", "_r19", "ctx_r18", "toggleChatBot", "AccountSetupComponent_mat_card_133_div_4_Template_button_click_10_listener", "ctx_r20", "startAIProcessing", "ɵɵtemplate", "AccountSetupComponent_mat_card_133_div_4_p_14_Template", "AccountSetupComponent_mat_card_133_div_4_app_chat_bot_15_Template", "ɵɵpropertyInterpolate", "ctx_r10", "chatBotMinimized", "ɵɵtextInterpolate", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r21", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_133_div_5_div_16_div_3_Template", "AccountSetupComponent_mat_card_133_div_5_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c1", "step_r25", "completed", "ctx_r24", "activeStep", "i_r26", "name", "description", "AccountSetupComponent_mat_card_133_div_5_span_12_Template", "AccountSetupComponent_mat_card_133_div_5_span_13_Template", "AccountSetupComponent_mat_card_133_div_5_span_14_Template", "AccountSetupComponent_mat_card_133_div_5_div_16_Template", "ctx_r11", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_133_div_6_Template_button_click_20_listener", "_r31", "ctx_r30", "downloadInventory", "AccountSetupComponent_mat_card_133_div_6_Template_button_click_35_listener", "ctx_r32", "downloadPackaging", "AccountSetupComponent_mat_card_133_div_7_Template_button_click_9_listener", "_r34", "ctx_r33", "AccountSetupComponent_mat_card_133_div_3_Template", "AccountSetupComponent_mat_card_133_div_4_Template", "AccountSetupComponent_mat_card_133_div_5_Template", "AccountSetupComponent_mat_card_133_div_6_Template", "AccountSetupComponent_mat_card_133_div_7_Template", "ctx_r8", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_28_listener", "AccountSetupComponent_Template_button_click_32_listener", "AccountSetupComponent_Template_input_keyup_58_listener", "$event", "AccountSetupComponent_mat_error_61_Template", "AccountSetupComponent_Template_input_keyup_65_listener", "AccountSetupComponent_mat_error_68_Template", "AccountSetupComponent_Template_input_keyup_72_listener", "AccountSetupComponent_mat_error_75_Template", "AccountSetupComponent_Template_button_click_89_listener", "AccountSetupComponent_div_96_Template", "AccountSetupComponent_Template_button_click_98_listener", "_r35", "_r6", "ɵɵreference", "click", "AccountSetupComponent_div_99_Template", "AccountSetupComponent_mat_icon_100_Template", "AccountSetupComponent_Template_input_change_102_listener", "AccountSetupComponent_mat_error_104_Template", "AccountSetupComponent_mat_card_133_Template", "ɵɵpureFunction0", "_c2", "_c3", "tmp_3_0", "<PERSON><PERSON><PERSON><PERSON>", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i15", "MatTooltip", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i16", "MatRadioGroup", "MatRadioButton", "i17", "MatButton", "MatIconButton", "i18", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i19", "MatProgressBar", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport {ProgressBarMode, MatProgressBarModule} from '@angular/material/progress-bar';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // Chat bot related properties\n  showChatBot: boolean = false;\n  chatBotMinimized: boolean = false;\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.prefillData(this.dialogData.elements);\n      this.isEditMode = true;\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n                this.isEditMode = true;\n              } else {\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  toggleChatBot(): void {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Breadcrumbs navigation -->\n  <div class=\"breadcrumbs\">\n    <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n      <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      <span>Dashboard</span>\n    </a>\n    <span class=\"breadcrumb-separator\">›</span>\n    <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n      <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n      <span>Accounts</span>\n    </a>\n    <span class=\"breadcrumb-separator\">›</span>\n    <span class=\"breadcrumb-item active\">\n      <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n      <span>{{isEditMode ? 'Edit ' + (registrationForm.get('tenantName')?.value || 'Account') : 'New Account'}}</span>\n    </span>\n  </div>\n\n  <div class=\"header-section\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">{{isEditMode ? 'Edit Account' : 'Create New Account'}}</h1>\n      <p class=\"page-subtitle\">{{isEditMode ? 'Update tenant account details' : 'Set up a new tenant account'}}</p>\n    </div>\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update Account' : 'Create Account'}}\n      </button>\n      <button (click)=\"close()\" mat-stroked-button color=\"basic\" class=\"cancel-button\">\n        <mat-icon>arrow_back</mat-icon>\n        Back to Accounts\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-header>\n        <mat-card-title>Account Information</mat-card-title>\n        <mat-card-subtitle>Enter the tenant account details</mat-card-subtitle>\n      </mat-card-header>\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n    <div class=\"form-grid\">\n      <div class=\"form-column\">\n        <h3 class=\"section-title\">Basic Information</h3>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Tenant Name</mat-label>\n          <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n          <mat-icon matSuffix>business</mat-icon>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Tenant ID</mat-label>\n          <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n            oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n          <mat-icon matSuffix>fingerprint</mat-icon>\n          <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n            Tenant ID already exists\n          </mat-error>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Account Number</mat-label>\n          <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n            oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n          <mat-icon matSuffix>account_balance</mat-icon>\n          <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n            Account number already exists\n          </mat-error>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>G-Sheet</mat-label>\n          <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n            (keyup)=\"checkGSheet($event)\">\n          <mat-icon matSuffix>table_chart</mat-icon>\n          <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n            G-Sheet number already exists\n          </mat-error>\n        </mat-form-field>\n      </div>\n\n      <div class=\"form-column\">\n        <h3 class=\"section-title\">Authentication</h3>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email</mat-label>\n          <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n          <mat-icon matSuffix>email</mat-icon>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Password</mat-label>\n          <input formControlName=\"password\" matInput placeholder=\"Enter password\" [type]=\"hidePassword ? 'password' : 'text'\" />\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n          </button>\n        </mat-form-field>\n\n        <div class=\"logo-upload-section\">\n          <h3 class=\"section-title\">Company Logo</h3>\n          <div class=\"logo-container\">\n            <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n              <img [src]=\"logoUrl\" alt=\"Company Logo\">\n            </div>\n            <div class=\"logo-actions\">\n              <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border\" role=\"status\">\n                  <span class=\"sr-only\">Loading...</span>\n                </div>\n                <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                Upload Logo\n              </button>\n              <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\" multiple>\n              <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                Please upload a company logo\n              </mat-error>\n            </div>\n          </div>\n        </div>\n      </div>\n\n    </div>\n\n    <div class=\"status-section\">\n      <h3 class=\"section-title\">Account Status Settings</h3>\n      <div class=\"status-options\">\n        <div class=\"status-option\">\n          <label class=\"status-label\">Account Status</label>\n          <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n            <mat-radio-button value=\"yes\" color=\"primary\">Active</mat-radio-button>\n            <mat-radio-button value=\"no\" color=\"primary\">Inactive</mat-radio-button>\n          </mat-radio-group>\n        </div>\n\n        <div class=\"status-option\">\n          <label class=\"status-label\">Forecast Status</label>\n          <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n            <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n            <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n          </mat-radio-group>\n        </div>\n\n        <div class=\"status-option\">\n          <label class=\"status-label\">Sales Status</label>\n          <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n            <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n            <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n          </mat-radio-group>\n        </div>\n      </div>\n    </div>\n  </form>\n</mat-card-content>\n</mat-card>\n\n<!-- AI Data Download Section - Shows after tenant creation -->\n<mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n  <div class=\"text-center p-2 my-2 bottomTitles\">AI-Powered Data Generation</div>\n\n  <!-- Initial state - before starting process -->\n  <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed && !showChatBot\" class=\"ai-intro-panel\">\n    <div class=\"row align-items-center\">\n      <div class=\"col-md-8\">\n        <h3><mat-icon class=\"ai-icon\">auto_awesome</mat-icon> Generate AI-Powered Datasets</h3>\n        <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.</p>\n        <p><strong>Note:</strong> This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.</p>\n      </div>\n      <div class=\"col-md-4 text-center\">\n        <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"start-ai-btn\">\n          <mat-icon>play_arrow</mat-icon>\n          Get Started\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Chat Bot Section -->\n  <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n    <div class=\"chat-bot-header\">\n      <h3>\n        <mat-icon class=\"chat-bot-icon\">smart_toy</mat-icon>\n        Restaurant Information Assistant\n      </h3>\n      <div class=\"chat-bot-actions\">\n        <button mat-icon-button (click)=\"toggleChatBot()\" matTooltip=\"{{ chatBotMinimized ? 'Expand' : 'Minimize' }}\">\n          <mat-icon>{{ chatBotMinimized ? 'expand_more' : 'expand_less' }}</mat-icon>\n        </button>\n        <button mat-icon-button (click)=\"startAIProcessing()\" matTooltip=\"Skip to AI Processing\">\n          <mat-icon>skip_next</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div [ngClass]=\"{'chat-bot-minimized': chatBotMinimized}\">\n      <p class=\"chat-bot-description\" *ngIf=\"!chatBotMinimized\">\n        Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n      </p>\n\n      <app-chat-bot\n        *ngIf=\"!chatBotMinimized\"\n        [tenantId]=\"registrationForm.value.tenantId\"\n        [tenantName]=\"registrationForm.value.tenantName\">\n      </app-chat-bot>\n    </div>\n  </div>\n\n  <!-- Processing state -->\n  <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n    <h3 class=\"processing-title\">\n      <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n      Processing Your Data\n    </h3>\n\n    <!-- Progress indicator -->\n    <div class=\"progress-container\">\n      <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n      <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n    </div>\n\n    <!-- Estimated time -->\n    <div class=\"estimated-time\">\n      <mat-icon class=\"icon\">access_time</mat-icon>\n      <span *ngIf=\"estimatedTimeRemaining > 60\">\n        Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n      </span>\n      <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n        Estimated time remaining: less than a minute\n      </span>\n      <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n        Calculating...\n      </span>\n    </div>\n\n    <!-- Processing steps -->\n    <div class=\"processing-steps\">\n      <div *ngFor=\"let step of downloadSteps; let i = index\"\n           class=\"step-row\"\n           [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n        <div class=\"step-status\">\n          <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n          <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n        </div>\n\n        <div class=\"step-details\">\n          <div class=\"step-name\">{{step.name}}</div>\n          <div class=\"step-description\">{{step.description}}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Helpful tips section -->\n    <div class=\"tips-section\">\n      <div class=\"tip-header\">\n        <mat-icon>lightbulb</mat-icon>\n        <span>Did You Know?</span>\n      </div>\n      <div class=\"tip-content\">\n        AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights\n      </div>\n    </div>\n  </div>\n\n  <!-- Download complete state -->\n  <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n    <div class=\"success-header\">\n      <mat-icon class=\"success-icon\">task_alt</mat-icon>\n      <h3>Processing Complete!</h3>\n    </div>\n\n    <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.</p>\n\n    <div class=\"row download-options\">\n      <!-- Inventory Dataset Card -->\n      <div class=\"col-md-6 mb-3\">\n        <mat-card class=\"download-card\">\n          <mat-card-header>\n            <div mat-card-avatar class=\"inventory-icon\">\n              <mat-icon>inventory_2</mat-icon>\n            </div>\n            <mat-card-title>Inventory Dataset</mat-card-title>\n            <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\">\n              <mat-icon>download</mat-icon> Download\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n\n      <!-- Packaging Dataset Card -->\n      <div class=\"col-md-6 mb-3\">\n        <mat-card class=\"download-card\">\n          <mat-card-header>\n            <div mat-card-avatar class=\"packaging-icon\">\n              <mat-icon>category</mat-icon>\n            </div>\n            <mat-card-title>Packaging Dataset</mat-card-title>\n            <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\">\n              <mat-icon>download</mat-icon> Download\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error state -->\n  <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n    <div class=\"error-header\">\n      <mat-icon class=\"error-icon\">error_outline</mat-icon>\n      <h3>Processing Failed</h3>\n    </div>\n\n    <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.</p>\n\n    <div class=\"error-actions\">\n      <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\">\n        <mat-icon>refresh</mat-icon> Try Again\n      </button>\n    </div>\n  </div>\n</mat-card>\n</div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAAyBC,oBAAoB,QAAO,gCAAgC;AACpF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICoClBC,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAwBVH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAIlBT,EAAA,CAAAC,cAAA,cAAqE;IAC7CD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEzCH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAI/DH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IA6CxBH,EAAA,CAAAC,cAAA,cAA2G;IAGvED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvFH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLAA0K;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjLH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,4IAAmI;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnKH,EAAA,CAAAC,cAAA,eAAkC;IACUD,EAAA,CAAAU,UAAA,mBAAAC,2EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAuBXH,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAEJH,EAAA,CAAAI,SAAA,uBAIe;;;;IAFbJ,EAAA,CAAAM,UAAA,aAAAY,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;;;;;;;IAvBlDtB,EAAA,CAAAC,cAAA,cAA4G;IAGtED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA8B;IACJD,EAAA,CAAAU,UAAA,mBAAAa,0EAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAS,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC/C1B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7EH,EAAA,CAAAC,cAAA,kBAAyF;IAAjED,EAAA,CAAAU,UAAA,mBAAAiB,2EAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAY,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnD7B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAKpCH,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAA8B,UAAA,KAAAC,sDAAA,gBAEI;IAEJ/B,EAAA,CAAA8B,UAAA,KAAAE,iEAAA,2BAIe;IACjBhC,EAAA,CAAAG,YAAA,EAAM;;;;IAnBgDH,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAiC,qBAAA,eAAAC,OAAA,CAAAC,gBAAA,yBAA2D;IACjGnC,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAoC,iBAAA,CAAAF,OAAA,CAAAC,gBAAA,iCAAsD;IAQjEnC,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAJ,OAAA,CAAAC,gBAAA,EAAoD;IACtBnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;IAKrDnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;;;;;IAuB1BnC,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuC,kBAAA,gCAAAvC,EAAA,CAAAwC,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACA1C,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAUHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,cAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAT3FH,EAAA,CAAAC,cAAA,cAE0I;IAGtID,EAAA,CAAA8B,UAAA,IAAAa,mEAAA,uBAAwD;IACxD3C,EAAA,CAAA8B,UAAA,IAAAc,8DAAA,kBAEM;IACN5C,EAAA,CAAA8B,UAAA,IAAAe,mEAAA,uBAAuF;IACzF7C,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZvDH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAG1HpD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAA0C,QAAA,CAAAC,SAAA,CAAoB;IACzBjD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpCpD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7BpD,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAK,IAAA,CAAa;IACNrD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAM,WAAA,CAAoB;;;;;IA1C1DtD,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAA8B,UAAA,KAAAyB,yDAAA,mBAEO;IACPvD,EAAA,CAAA8B,UAAA,KAAA0B,yDAAA,mBAEO;IACPxD,EAAA,CAAA8B,UAAA,KAAA2B,yDAAA,mBAEO;IACTzD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAA8B,UAAA,KAAA4B,wDAAA,oBAgBM;IACR1D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAqD,OAAA,CAAAC,gBAAA,CAA0B;IACnC5D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAuC,kBAAA,KAAAoB,OAAA,CAAAC,gBAAA,eAA8B;IAMnD5D,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,MAAiC;IAGjC1C,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,QAAAiB,OAAA,CAAAjB,sBAAA,OAAgE;IAGhE1C,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,OAAkC;IAOnB1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAqD,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5C7D,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,4FAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEpHH,EAAA,CAAAC,cAAA,eAAkC;IAMdD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAoD,2EAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEjE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,gBAA2B;IAITD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAwD,2EAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAI,OAAA,GAAAnE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEpE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQnBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHAAiH;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9IH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAA2D,0EAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuD,OAAA,CAAAtD,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA3KfH,EAAA,CAAAC,cAAA,mBAA2D;IACVD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/EH,EAAA,CAAA8B,UAAA,IAAA0C,iDAAA,mBAcM;IAGNxE,EAAA,CAAA8B,UAAA,IAAA2C,iDAAA,mBA2BM;IAGNzE,EAAA,CAAA8B,UAAA,IAAA4C,iDAAA,mBAyDM;IAGN1E,EAAA,CAAA8B,UAAA,IAAA6C,iDAAA,mBA8CM;IAGN3E,EAAA,CAAA8B,UAAA,IAAA8C,iDAAA,mBAaM;IACR5E,EAAA,CAAAG,YAAA,EAAW;;;;IA1KHH,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAM,UAAA,UAAAuE,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAI,WAAA,CAA4E;IAiB5EjF,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAI,WAAA,KAAAJ,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,CAA2E;IA8B3EhF,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAC,aAAA,CAAmB;IA4DnB9E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAE,gBAAA,CAAsB;IAiDtB/E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAG,cAAA,CAAoB;;;;;;;;;ADrS5B,MAuBaE,qBAAqB;EAqChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IA/CpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAA7F,OAAO,GAAkB,IAAI;IAC7B,KAAA8F,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAG3B,KAAA1C,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA2C,gBAAgB,GAAY,IAAI;IAChC,KAAA1B,aAAa,GAAY,KAAK;IAC9B,KAAAlB,gBAAgB,GAAW,CAAC;IAC5B,KAAAmB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA7B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAuC,WAAW,GAAY,KAAK;IAC5B,KAAA9C,gBAAgB,GAAY,KAAK;IAkB/B,IAAI,CAACsE,IAAI,GAAG,IAAI,CAACb,IAAI,CAACc,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACnB,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAEnD;IACA,IAAI,IAAI,CAAC0E,UAAU,EAAE;MACnB,IAAI,CAACe,WAAW,GAAG,IAAI,CAACf,UAAU,CAACgB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC1F,gBAAgB,GAAG,IAAI,CAACoE,EAAE,CAACwB,KAAK,CAAC;MACpC1F,QAAQ,EAAE,IAAI1C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAE;MAC3D1F,UAAU,EAAE,IAAI3C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAItI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAIvI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAIxI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIzI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAI1I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAI3I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAI5I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAI7I,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAAC3B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACY,WAAW,CAAC,IAAI,CAAC5B,UAAU,CAAC6B,QAAQ,CAAC;MAC1C,IAAI,CAACpB,UAAU,GAAG,IAAI;KACvB,MAAM;MACL;MACA,IAAI,CAACjB,KAAK,CAACsC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB;UACA,IAAI,CAACnC,GAAG,CAACoC,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;gBAC1B,IAAI,CAAC5B,UAAU,GAAG,IAAI;eACvB,MAAM;gBACL,IAAI,CAACd,MAAM,CAAC2C,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACbC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAAC9C,MAAM,CAAC2C,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAAChH,gBAAgB,CAACsH,UAAU,CAAC;MAC/BnH,UAAU,EAAE6G,IAAI,CAAC7G,UAAU;MAC3BD,QAAQ,EAAE8G,IAAI,CAAC9G,QAAQ;MACvB4F,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAChH,OAAO,GAAG2H,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAACnB,aAAa,GAAG,CAAC;QACpBuC,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAAC3B,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACzD,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAAC5H,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAAC7H,gBAAgB,CAAC8H,gBAAgB,EAAE;MACxC,IAAI,CAACxD,MAAM,CAAC2C,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAAC/H,gBAAgB,CAACC,KAAK;MACxC,IAAI+H,GAAG,GAAQ;QACT9H,QAAQ,EAAE6H,MAAM,CAAC7H,QAAQ;QACzBC,UAAU,EAAE4H,MAAM,CAAC5H,UAAU;QAC7B6F,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAACnB,aAAa,CAAC+C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC/C,aAAa,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACjD,GAAG,CAAC0D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAACzC,MAAM,CAAC6D,mBAAmB,CAAC,IAAI,CAAC/C,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAAClB,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAC3C,iBAAiB,CAAC6D,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAAC9D,MAAM,CAAC2C,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACzI,QAAQ,KAAMqI,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3C,SAAS,KAAMuC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAMwC,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACjE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9BoE,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACpL,OAAO,GAAGmL,MAAM;YACrB,IAAI,CAACxK,gBAAgB,CAACsH,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAChH;YAAO,CAAE,CAAC;YACxD,IAAI,CAAC6F,aAAa,CAACwF,IAAI,CAAC;cAAEjD,GAAG,EAAE+C;YAAM,CAAE,CAAC;YACxC,IAAI,CAACxF,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACgD,aAAa,EAAE;UACzB,CAAC;UACDoC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACpI,aAAa,CAAC4G,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACjJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEClC,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACE,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAACvD,MAAM,CAAC2C,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACnD,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC9C,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;IAEvB;IACAsD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA1K,iBAAiBA,CAAA;IACf,IAAI,CAACiD,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAAClB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACqC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACiH,UAAU,EAAE;IACjB,MAAM5K,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACsE,GAAG,CAAC6G,eAAe,CAACnL,QAAQ,CAAC,CAACoL,IAAI,CACrC/M,UAAU,CAACgN,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAO9M,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAACgI,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC1E,OAAO,EAAE;QAChC,IAAI,CAAC2E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACzL,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACsL,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAjL,aAAaA,CAAA;IACX,IAAI,CAACS,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAiE,kBAAkBA,CAACzL,QAAgB;IACjC,IAAI,CAAC0L,aAAa,GAAGjN,QAAQ,CAAC,KAAK,CAAC,CAAC2M,IAAI,CACvC9M,SAAS,CAAC,MAAM,IAAI,CAACgG,GAAG,CAACqH,SAAS,CAAC3L,QAAQ,CAAC,CAAC,EAC7CzB,SAAS,CAAEgN,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAAClE,MAAM,KAAK,UAAU,IAAIkE,QAAQ,CAAClE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAAClE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACiE,mBAAmB,CAACC,QAAQ,CAACK,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACrJ,gBAAgB,GAAGgJ,QAAQ,CAACM,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAACxK,sBAAsB,GAAGkK,QAAQ,CAACO,wBAAwB,IAAI,CAAC;MAEpE,IAAIP,QAAQ,CAACQ,WAAW,KAAKC,SAAS,IAAIT,QAAQ,CAACQ,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACjK,UAAU,GAAGyJ,QAAQ,CAACQ,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,QAAQ,CAACQ,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAACzJ,aAAa,CAACuF,MAAM,EAAE;YACjC,IAAI,CAACvF,aAAa,CAACyJ,CAAC,CAAC,CAACrK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAAC4C,EAAE,CAACgD,aAAa,EAAE;MAEvB,IAAI+D,QAAQ,CAAClE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC6E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEb,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAY,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACc,EAAE,CAACgD,aAAa,EAAE;IACvB,IAAI,CAAC9C,QAAQ,CAAC0H,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAjB,mBAAmBA,CAACM,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACe,QAAQ,CAAC0H,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAChG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC5G,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAgD,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,GAAG,CAACmI,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC/EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGAtJ,iBAAiBA,CAAA;IACf,IAAI,CAACuB,GAAG,CAACmI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC7EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBA1aWtI,qBAAqB,EAAAlF,EAAA,CAAAyO,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA3O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA9O,EAAA,CAAAyO,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAhP,EAAA,CAAAyO,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAAyO,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAApP,EAAA,CAAAyO,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAtP,EAAA,CAAAyO,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAxP,EAAA,CAAAyO,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA1P,EAAA,CAAAyO,iBAAA,CAAAzO,EAAA,CAAA2P,iBAAA,GAAA3P,EAAA,CAAAyO,iBAAA,CAgDVvP,eAAe,MAAAc,EAAA,CAAAyO,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAAyO,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAhD1B7K,qBAAqB;MAAA8K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlQ,EAAA,CAAAmQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAnF,GAAA;QAAA,IAAAmF,EAAA;;UClDlCzQ,EAAA,CAAAC,cAAA,aAAqC;UAIGD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExBH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIpHH,EAAA,CAAAC,cAAA,cAA4B;UAEDD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClFH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,IAAgF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE/GH,EAAA,CAAAC,cAAA,eAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAgQ,wDAAA;YAAA,OAASpF,GAAA,CAAAvC,IAAA,EAAM;UAAA,EAAC;UACtB/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiF;UAAzED,EAAA,CAAAU,UAAA,mBAAAiQ,wDAAA;YAAA,OAASrF,GAAA,CAAAxC,KAAA,EAAO;UAAA,EAAC;UACvB9I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6B;UAGPD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACpDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,wCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAoB;UAEzEH,EAAA,CAAAC,cAAA,wBAAkB;UAIUD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEhDH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAkQ,uDAAAC,MAAA;YAAA,OAASvF,GAAA,CAAA7B,aAAA,CAAAoH,MAAA,CAAqB;UAAA,EAAC;UADjF7Q,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAAgP,2CAAA,wBAEY;UACd9Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAqQ,uDAAAF,MAAA;YAAA,OAASvF,GAAA,CAAArB,cAAA,CAAA4G,MAAA,CAAsB;UAAA,EAAC;UADlF7Q,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAA8B,UAAA,KAAAkP,2CAAA,wBAEY;UACdhR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAuQ,uDAAAJ,MAAA;YAAA,OAASvF,GAAA,CAAApB,WAAA,CAAA2G,MAAA,CAAmB;UAAA,EAAC;UAD/B7Q,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAAoP,2CAAA,wBAEY;UACdlR,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,eAAyB;UACGD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAsH;UACtHJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAyQ,wDAAA;YAAA,OAAA7F,GAAA,CAAAhF,YAAA,IAAAgF,GAAA,CAAAhF,YAAA;UAAA,EAAsC;UACtEtG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI3EH,EAAA,CAAAC,cAAA,eAAiC;UACLD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAA8B,UAAA,KAAAsP,qCAAA,kBAEM;UACNpR,EAAA,CAAAC,cAAA,eAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA2Q,wDAAA;YAAArR,EAAA,CAAAY,aAAA,CAAA0Q,IAAA;YAAA,MAAAC,GAAA,GAAAvR,EAAA,CAAAwR,WAAA;YAAA,OAASxR,EAAA,CAAAgB,WAAA,CAAAuQ,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnEzR,EAAA,CAAA8B,UAAA,KAAA4P,qCAAA,kBAEM;UACN1R,EAAA,CAAA8B,UAAA,MAAA6P,2CAAA,uBAA6D;UAC7D3R,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,sBAAiH;UAAnFD,EAAA,CAAAU,UAAA,oBAAAkR,yDAAAf,MAAA;YAAA,OAAUvF,GAAA,CAAAnB,cAAA,CAAA0G,MAAA,CAAsB;UAAA,EAAC;UAA/D7Q,EAAA,CAAAG,YAAA,EAAiH;UACjHH,EAAA,CAAA8B,UAAA,MAAA+P,4CAAA,wBAEY;UACd7R,EAAA,CAAAG,YAAA,EAAM;UAOdH,EAAA,CAAAC,cAAA,gBAA4B;UACAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtDH,EAAA,CAAAC,cAAA,gBAA4B;UAEID,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClDH,EAAA,CAAAC,cAAA,4BAA6G;UAC7DD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACvEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,4BAA+G;UAC/DD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChDH,EAAA,CAAAC,cAAA,4BAAyG;UACzDD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAUpFH,EAAA,CAAA8B,UAAA,MAAAgQ,2CAAA,uBA8KW;UACX9R,EAAA,CAAAG,YAAA,EAAM;;;;UA5UCH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA+R,eAAA,KAAAC,GAAA,EAAkC;UAKlChS,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA+R,eAAA,KAAAE,GAAA,EAAqC;UAMJjS,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,yBAAsC;UAClEvG,EAAA,CAAAK,SAAA,GAAmG;UAAnGL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,gBAAA2L,OAAA,GAAA5G,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,iCAAAmI,OAAA,CAAA9Q,KAAA,gCAAmG;UAMlFpB,EAAA,CAAAK,SAAA,GAAsD;UAAtDL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,yCAAsD;UACpDvG,EAAA,CAAAK,SAAA,GAAgF;UAAhFL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,mEAAgF;UAKvGvG,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAuC,kBAAA,MAAA+I,GAAA,CAAA/E,UAAA,4CACF;UAe6BvG,EAAA,CAAAK,SAAA,IAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAgL,GAAA,CAAAnK,gBAAA,CAA8B;UAgB3CnB,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,aAAAoI,QAAA,mBAAiE;UAUjEnS,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,cAAAoI,QAAA,oBAAmE;UAUnEnS,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,WAAAoI,QAAA,iBAA6D;UAiBDnS,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAhF,YAAA,uBAA2C;UAEvGtG,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAAhF,YAAA,mCAAkD;UAOjCtG,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9K,OAAA,CAAa;UAK9BR,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnF,kBAAA,CAAwB;UAGnBnG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAAgL,GAAA,CAAAnF,kBAAA,CAAyB;UAI1BnG,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAf,OAAA,IAAAsC,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAqI,OAAA,CAAkF;UA2CjGpS,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9E,gBAAA,CAAsB;;;qBD9H7B9H,YAAY,EAAA2T,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZ1T,aAAa,EAAA2T,GAAA,CAAAC,OAAA,EACb3T,cAAc,EAAA4T,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACdjU,gBAAgB,EAAAkU,GAAA,CAAAC,UAAA,EAChBxU,WAAW,EAAAmQ,EAAA,CAAAsE,aAAA,EAAAtE,EAAA,CAAAuE,oBAAA,EAAAvE,EAAA,CAAAwE,eAAA,EAAAxE,EAAA,CAAAyE,oBAAA,EACX3U,mBAAmB,EAAAkQ,EAAA,CAAA0E,kBAAA,EAAA1E,EAAA,CAAA2E,eAAA,EACnBrU,cAAc,EAAAsU,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdvU,eAAe,EAAAwU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfzU,aAAa,EAAA0U,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACbhV,eAAe,EACfL,oBAAoB,EAAAsV,GAAA,CAAAC,cAAA,EACpBjV,gBAAgB,EAChBL,YAAY,EAAAwP,EAAA,CAAA+F,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIH3P,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}