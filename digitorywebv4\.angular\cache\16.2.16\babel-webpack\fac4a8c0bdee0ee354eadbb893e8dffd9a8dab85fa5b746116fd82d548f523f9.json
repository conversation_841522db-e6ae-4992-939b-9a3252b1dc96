{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FilterPanelComponent } from './filter-panel.component';\nimport { ChartWrapperComponent } from './chart-wrapper.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"ngx-markdown\";\nconst _c0 = [\"chatContainer\"];\nconst _c1 = [\"messageInput\"];\nfunction SmartDashboardComponent_div_71_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const report_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", report_r6.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", report_r6.display_name, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Report Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 23)(7, \"mat-select\", 24);\n    i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_div_71_Template_mat_select_selectionChange_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onReportTypeChange());\n    });\n    i0.ɵɵelementStart(8, \"mat-option\", 25);\n    i0.ɵɵtext(9, \"Choose report type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SmartDashboardComponent_div_71_mat_option_10_Template, 2, 2, \"mat-option\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.reportTypeControl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.availableReports);\n  }\n}\nfunction SmartDashboardComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"app-filter-panel\", 28);\n    i0.ɵɵlistener(\"filtersApplied\", function SmartDashboardComponent_div_72_Template_app_filter_panel_filtersApplied_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onFiltersApplied($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"reportType\", ctx_r1.reportTypeControl.value);\n  }\n}\nfunction SmartDashboardComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_73_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.generateDashboardFromFilters());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Generate Dashboard \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.currentFilters);\n  }\n}\nfunction SmartDashboardComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"psychology\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h1\");\n    i0.ɵɵtext(6, \"AI-Powered Data Analysis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Configure your report filters and generate a dashboard to start analyzing your data with AI.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 34)(10, \"div\", 35)(11, \"div\", 36)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"h3\");\n    i0.ɵɵtext(15, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 35)(17, \"div\", 36)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"h3\");\n    i0.ɵɵtext(21, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 35)(23, \"div\", 36)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"h3\");\n    i0.ɵɵtext(27, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 35)(29, \"div\", 36)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"h3\");\n    i0.ɵɵtext(33, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 37)(35, \"div\", 38)(36, \"div\", 39)(37, \"mat-icon\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"Select your filters (categories/regions)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 38)(42, \"div\", 39)(43, \"mat-icon\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"Ask the AI assistant what you'd like to visualize\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(35);\n    i0.ɵɵclassProp(\"completed\", ctx_r3.reportTypeControl.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.reportTypeControl.value ? \"check_circle\" : \"radio_button_unchecked\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"completed\", ctx_r3.currentSession);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.currentSession ? \"check_circle\" : \"radio_button_unchecked\");\n  }\n}\nfunction SmartDashboardComponent_div_76_div_21_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_76_div_21_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const query_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.sendExampleQuery(query_r18));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const query_r18 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", query_r18, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_76_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"h3\");\n    i0.ɵɵtext(2, \"Quick Analysis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52);\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_76_div_21_button_4_Template, 4, 1, \"button\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.exampleQueries.slice(0, 4));\n  }\n}\nfunction SmartDashboardComponent_div_76_div_23_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"app-chart-wrapper\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"chartData\", message_r21.chart_data)(\"chartType\", message_r21.chart_type || \"bar\");\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"ai-message\": a1\n  };\n};\nfunction SmartDashboardComponent_div_76_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"span\", 58)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 60);\n    i0.ɵɵelement(11, \"markdown\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SmartDashboardComponent_div_76_div_23_div_12_Template, 2, 2, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c2, message_r21.type === \"human\", message_r21.type === \"ai\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(message_r21.type === \"human\" ? \"person\" : \"psychology\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r21.type === \"human\" ? \"You\" : \"AI Assistant\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 6, message_r21.created_at, \"short\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"data\", message_r21.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r21.has_visualization && message_r21.chart_data);\n  }\n}\nfunction SmartDashboardComponent_div_76_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"mat-spinner\", 66);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"AI is analyzing your data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42)(3, \"span\", 43);\n    i0.ɵɵtext(4, \"Data Rows\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 44);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 42)(9, \"span\", 43);\n    i0.ɵɵtext(10, \"Columns\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 44);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 42)(15, \"span\", 43);\n    i0.ɵɵtext(16, \"Report Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 44);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 45, 46);\n    i0.ɵɵtemplate(21, SmartDashboardComponent_div_76_div_21_Template, 5, 1, \"div\", 47);\n    i0.ɵɵelementStart(22, \"div\", 48);\n    i0.ɵɵtemplate(23, SmartDashboardComponent_div_76_div_23_Template, 13, 12, \"div\", 49);\n    i0.ɵɵtemplate(24, SmartDashboardComponent_div_76_div_24_Template, 4, 0, \"div\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 7, ctx_r4.currentSession.dataframe_summary.rows));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 9, ctx_r4.currentSession.dataframe_summary.columns));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.reportTypeControl.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.chatMessages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.chatMessages)(\"ngForTrackBy\", ctx_r4.trackByIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoading);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, snackBar) {\n    this.smartDashboardService = smartDashboardService;\n    this.snackBar = snackBar;\n    // Form controls\n    this.reportTypeControl = new FormControl('');\n    this.messageControl = new FormControl('');\n    // Component state\n    this.availableReports = [];\n    this.chatMessages = [];\n    this.currentSession = null;\n    this.isLoading = false;\n    this.currentFilters = null;\n    // Chart data\n    this.chartData = null;\n    this.chartType = 'bar';\n    this.chartConfig = null;\n    // Example queries for data analysis\n    this.exampleQueries = ['Create a bar chart of total purchases by vendor', 'What is the average order quantity by category?', 'Show me a pie chart of inventory by location', 'Generate a line chart of purchase trends over time', 'What are the top 10 items by value?', 'Show me cost variance analysis'];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadAvailableReports();\n    this.subscribeToServiceState();\n  }\n  ngAfterViewInit() {\n    this.scrollToBottom();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  subscribeToServiceState() {\n    this.smartDashboardService.chatMessages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.chatMessages = messages;\n      setTimeout(() => this.scrollToBottom(), 100);\n    });\n    this.smartDashboardService.currentSession$.pipe(takeUntil(this.destroy$)).subscribe(session => {\n      this.currentSession = session;\n    });\n    this.smartDashboardService.isLoading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  loadAvailableReports() {\n    this.smartDashboardService.getAvailableReports().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.availableReports = response.reports;\n        }\n      },\n      error: error => {\n        this.showError('Failed to load available reports');\n        console.error('Error loading reports:', error);\n      }\n    });\n  }\n  onReportTypeChange() {\n    const reportType = this.reportTypeControl.value;\n    if (reportType) {\n      const selectedReport = this.availableReports.find(r => r.name === reportType);\n      if (selectedReport) {\n        this.addChatMessage({\n          content: `Great! I'll help you generate a ${selectedReport.display_name}. Please configure the filters and click \"Generate Dashboard\" to start.`,\n          type: 'ai',\n          created_at: new Date()\n        });\n      }\n    }\n  }\n  onFiltersApplied(filters) {\n    this.currentFilters = filters;\n    this.generateDashboardFromFilters();\n  }\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message) return;\n    if (!this.currentSession) {\n      this.showError('Please generate a dashboard first by selecting filters and clicking \"Generate Dashboard\"');\n      return;\n    }\n    // Add user message\n    this.addChatMessage({\n      content: message,\n      type: 'human',\n      created_at: new Date()\n    });\n    this.messageControl.setValue('');\n    this.smartDashboardService.setLoading(true);\n    // Chat with dashboard\n    this.chatWithDashboard(message);\n  }\n  generateDashboardFromFilters() {\n    if (!this.currentFilters || !this.reportTypeControl.value) {\n      this.showError('Please select filters and report type');\n      return;\n    }\n    this.smartDashboardService.setLoading(true);\n    // Convert filters to parameters format\n    const parameters = this.convertFiltersToParameters(this.currentFilters);\n    this.generateDashboard(this.reportTypeControl.value, parameters);\n  }\n  convertFiltersToParameters(filters) {\n    return {\n      selectedRestaurants: filters.selectedRestaurants,\n      selectedVendors: filters.selectedVendors || [],\n      selectedCategories: filters.selectedCategories || [],\n      selectedSubCategories: filters.selectedSubCategories || [],\n      selectedWorkAreas: filters.selectedWorkAreas || [],\n      startDate: filters.startDate?.toISOString(),\n      endDate: filters.endDate?.toISOString(),\n      selectedBaseDate: filters.selectedBaseDate || 'deliveryDate'\n    };\n  }\n  generateDashboard(reportType, parameters) {\n    const request = {\n      tenant_id: this.smartDashboardService.getTenantId(),\n      report_type: reportType,\n      parameters: parameters,\n      session_id: this.currentSession?.session_id\n    };\n    this.smartDashboardService.generateDashboard(request).subscribe({\n      next: response => {\n        this.handleDashboardResponse(response);\n      },\n      error: error => {\n        this.handleError('Error generating dashboard', error);\n      }\n    });\n  }\n  handleDashboardResponse(response) {\n    if (response.status === 'success') {\n      // Create session object\n      const session = {\n        session_id: response.session_id,\n        tenant_id: this.smartDashboardService.getTenantId(),\n        report_type: this.reportTypeControl.value,\n        parameters: this.convertFiltersToParameters(this.currentFilters),\n        dataframe_summary: response.dataframe_info,\n        created_at: new Date().toISOString(),\n        last_activity: new Date().toISOString()\n      };\n      this.smartDashboardService.setCurrentSession(session);\n      this.addChatMessage({\n        content: 'Dashboard generated successfully! You can now ask questions about your data.',\n        type: 'ai',\n        created_at: new Date()\n      });\n      this.showSuccess('Dashboard generated successfully!');\n    } else {\n      this.showError(`Error generating dashboard: ${response.message}`);\n    }\n    this.smartDashboardService.setLoading(false);\n  }\n  chatWithDashboard(message) {\n    if (!this.currentSession) return;\n    const request = {\n      tenant_id: this.smartDashboardService.getTenantId(),\n      session_id: this.currentSession.session_id,\n      message: message\n    };\n    this.smartDashboardService.chatWithDashboard(request).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          const chatResponse = response.data;\n          this.addChatMessage({\n            content: chatResponse.message,\n            type: 'ai',\n            created_at: new Date(),\n            chart_data: chatResponse.chart_data,\n            chart_type: chatResponse.chart_type,\n            has_visualization: chatResponse.has_visualization\n          });\n          // Handle visualization\n          if (chatResponse.has_visualization && chatResponse.chart_data) {\n            this.updateChart(chatResponse.chart_data, chatResponse.chart_type);\n          }\n        }\n        this.smartDashboardService.setLoading(false);\n      },\n      error: error => {\n        this.handleError('Error processing chat request', error);\n      }\n    });\n  }\n  updateChart(chartData, chartType) {\n    try {\n      this.chartType = chartType || 'bar';\n      // Convert PandasAI data to Chart.js format\n      this.chartData = ChartWrapperComponent.convertPandasAIToChartJS(chartData, this.chartType);\n      this.chartConfig = this.smartDashboardService.generateChartConfig(this.chartData, this.chartType);\n    } catch (error) {\n      console.error('Error updating chart:', error);\n      this.showError('Error displaying chart');\n    }\n  }\n  sendExampleQuery(query) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n  clearSession() {\n    if (this.currentSession) {\n      this.smartDashboardService.clearSession(this.currentSession.session_id).subscribe({\n        next: () => {\n          this.resetSession();\n          this.showSuccess('Session cleared successfully');\n        },\n        error: error => {\n          console.error('Error clearing session:', error);\n          this.resetSession(); // Reset anyway\n        }\n      });\n    } else {\n      this.resetSession();\n    }\n  }\n  resetSession() {\n    this.smartDashboardService.setCurrentSession(null);\n    this.smartDashboardService.clearChatMessages();\n    this.currentFilters = null;\n    this.chartData = null;\n    this.chartConfig = null;\n    this.reportTypeControl.setValue('');\n  }\n  addChatMessage(message) {\n    this.smartDashboardService.addChatMessage(message);\n  }\n  scrollToBottom() {\n    if (this.chatContainer) {\n      const element = this.chatContainer.nativeElement;\n      element.scrollTop = element.scrollHeight;\n    }\n  }\n  handleError(message, error) {\n    console.error(message, error);\n    this.showError(message);\n    this.smartDashboardService.setLoading(false);\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  trackByIndex(index) {\n    return index;\n  }\n  getStatusText() {\n    if (this.isLoading) return 'Processing...';\n    if (this.currentSession) return 'Dashboard ready';\n    return 'Ready';\n  }\n  getStatusClass() {\n    if (this.isLoading) return 'status-loading';\n    if (this.currentSession) return 'status-ready';\n    return 'status-ready';\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.reportTypeControl.value) count++;\n    if (this.currentFilters) {\n      if (this.currentFilters.selectedRestaurants?.length) count++;\n      if (this.currentFilters.selectedVendors?.length) count++;\n      if (this.currentFilters.selectedCategories?.length) count++;\n      if (this.currentFilters.selectedSubCategories?.length) count++;\n      if (this.currentFilters.startDate) count++;\n      if (this.currentFilters.endDate) count++;\n    }\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(ChartWrapperComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartWrapper = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 77,\n      vars: 7,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"main-layout\"], [1, \"sidebar\"], [1, \"filters-header\"], [1, \"filter-icon\"], [1, \"filter-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-group-header\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"value\", \"last30days\"], [\"value\", \"last7days\"], [\"value\", \"last90days\"], [\"value\", \"custom\"], [1, \"checkbox-group\"], [3, \"checked\"], [\"class\", \"filter-section\", 4, \"ngIf\"], [\"class\", \"generate-section\", 4, \"ngIf\"], [1, \"center-panel\"], [\"class\", \"welcome-state\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"filter-section\"], [1, \"section-header\"], [\"appearance\", \"outline\", 1, \"filter-select\"], [3, \"formControl\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [3, \"reportType\", \"filtersApplied\"], [1, \"generate-section\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-btn\", 3, \"disabled\", \"click\"], [1, \"welcome-state\"], [1, \"welcome-content\"], [1, \"ai-brain-icon\"], [1, \"features-grid\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"steps-section\"], [1, \"step-item\"], [1, \"step-icon\"], [1, \"dashboard-content\"], [1, \"data-summary\"], [1, \"summary-item\"], [1, \"summary-label\"], [1, \"summary-value\"], [1, \"chat-messages\"], [\"chatContainer\", \"\"], [\"class\", \"quick-actions\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"loading-message\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"action-grid\"], [\"mat-stroked-button\", \"\", \"class\", \"action-button\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"action-button\", 3, \"click\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-header\"], [1, \"message-sender\"], [1, \"message-time\"], [1, \"message-text\"], [3, \"data\"], [\"class\", \"message-chart\", 4, \"ngIf\"], [1, \"message-chart\"], [3, \"chartData\", \"chartType\"], [1, \"loading-message\"], [\"diameter\", \"24\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\");\n          i0.ɵɵtext(15, \"Time Period\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"mat-form-field\", 9)(17, \"mat-select\", 10)(18, \"mat-option\", 11);\n          i0.ɵɵtext(19, \"Last 7 days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-option\", 10);\n          i0.ɵɵtext(21, \"Last 30 days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-option\", 12);\n          i0.ɵɵtext(23, \"Last 90 days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-option\", 13);\n          i0.ɵɵtext(25, \"Custom Range\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 7)(27, \"div\", 8)(28, \"span\");\n          i0.ɵɵtext(29, \"Categories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"mat-checkbox\");\n          i0.ɵɵtext(32, \"E-commerce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-checkbox\");\n          i0.ɵɵtext(34, \"SaaS\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-checkbox\");\n          i0.ɵɵtext(36, \"Mobile Apps\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-checkbox\");\n          i0.ɵɵtext(38, \"Marketing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-checkbox\");\n          i0.ɵɵtext(40, \"Support\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 7)(42, \"div\", 8)(43, \"span\");\n          i0.ɵɵtext(44, \"Regions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 14)(46, \"mat-checkbox\");\n          i0.ɵɵtext(47, \"North America\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"mat-checkbox\", 15);\n          i0.ɵɵtext(49, \"Europe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-checkbox\");\n          i0.ɵɵtext(51, \"Asia Pacific\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"mat-checkbox\");\n          i0.ɵɵtext(53, \"Latin America\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-checkbox\");\n          i0.ɵɵtext(55, \"Africa\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 7)(57, \"div\", 8)(58, \"span\");\n          i0.ɵɵtext(59, \"Key Metrics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 14)(61, \"mat-checkbox\");\n          i0.ɵɵtext(62, \"Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"mat-checkbox\");\n          i0.ɵɵtext(64, \"Users\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"mat-checkbox\");\n          i0.ɵɵtext(66, \"Conversions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"mat-checkbox\");\n          i0.ɵɵtext(68, \"Engagement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"mat-checkbox\");\n          i0.ɵɵtext(70, \"Retention\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(71, SmartDashboardComponent_div_71_Template, 11, 2, \"div\", 16);\n          i0.ɵɵtemplate(72, SmartDashboardComponent_div_72_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(73, SmartDashboardComponent_div_73_Template, 5, 1, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 18);\n          i0.ɵɵtemplate(75, SmartDashboardComponent_div_75_Template, 47, 6, \"div\", 19);\n          i0.ɵɵtemplate(76, SmartDashboardComponent_div_76_Template, 25, 11, \"div\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount());\n          i0.ɵɵadvance(39);\n          i0.ɵɵproperty(\"checked\", true);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", false && ctx.reportTypeControl.value);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", false && ctx.reportTypeControl.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentSession);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentSession);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DecimalPipe, i3.DatePipe, FormsModule, i4.NgControlStatus, ReactiveFormsModule, i4.FormControlDirective, MatCardModule, MatButtonModule, i5.MatButton, MatInputModule, i6.MatFormField, MatSelectModule, i7.MatSelect, i8.MatOption, MatIconModule, i9.MatIcon, MatProgressSpinnerModule, i10.MatProgressSpinner, MatChipsModule, MatDividerModule, MatTooltipModule, MatSnackBarModule, MarkdownModule, i11.MarkdownComponent, FilterPanelComponent, ChartWrapperComponent],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: #f8f9fa;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 16px 24px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  flex: 1;\\n  max-width: 600px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #4285f4;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .ai-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #5f6368;\\n  font-weight: 500;\\n  white-space: nowrap;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .chat-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .chat-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .chat-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  padding: 8px 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n  color: white;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:disabled {\\n  background: #e8eaed;\\n  color: #9aa0a6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  color: #ea4335;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 280px 1fr;\\n  gap: 0;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  background: white;\\n  border-right: 1px solid #e9ecef;\\n  padding: 24px 20px;\\n  overflow-y: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%] {\\n  color: #4285f4;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filter-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #202124;\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n  color: white;\\n  border-radius: 12px;\\n  padding: 2px 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  min-width: 20px;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #5f6368;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .generate-section[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .generate-section[_ngcontent-%COMP%]   .generate-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 44px;\\n  background: #4285f4;\\n  color: white;\\n  font-weight: 500;\\n  border-radius: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .generate-section[_ngcontent-%COMP%]   .generate-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .generate-section[_ngcontent-%COMP%]   .generate-btn[_ngcontent-%COMP%]:disabled {\\n  background: #e8eaed;\\n  color: #9aa0a6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 32px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 500px;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 80px;\\n  width: 80px;\\n  height: 80px;\\n  color: #4285f4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 32px;\\n  font-weight: 400;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 40px 0;\\n  font-size: 16px;\\n  color: #5f6368;\\n  line-height: 1.5;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 24px;\\n  margin-bottom: 40px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #4285f4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%] {\\n  text-align: left;\\n  max-width: 400px;\\n  margin: 0 auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #34a853;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #34a853;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #9aa0a6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-state[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #5f6368;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  margin-bottom: 32px;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #5f6368;\\n  margin-bottom: 4px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  font-size: 20px;\\n  font-weight: 400;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  text-align: left;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  background: white;\\n  color: #202124;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  border-color: #4285f4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #4285f4;\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #e8f0fe;\\n  margin-left: 60px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  margin-right: 60px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #5f6368;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #9aa0a6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.5;\\n  color: #202124;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-chart[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-chart[_ngcontent-%COMP%]   app-chart-wrapper[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  min-height: 300px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .loading-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n  color: #5f6368;\\n  font-style: italic;\\n}\\n@media (max-width: 992px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    grid-template-rows: auto 1fr;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    order: 1;\\n    padding: 16px;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%] {\\n    order: 2;\\n    padding: 24px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%]   .chat-input-container[_ngcontent-%COMP%]   .chat-input-wrapper[_ngcontent-%COMP%]   .ai-label[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .steps-section[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 8px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .action-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    margin-left: 20px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .center-panel[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .messages-list[_ngcontent-%COMP%]   .message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    margin-right: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "FormControl", "MatCardModule", "MatButtonModule", "MatInputModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "MatChipsModule", "MatDividerModule", "MatTooltipModule", "MatSnackBarModule", "MarkdownModule", "Chart", "registerables", "Subject", "takeUntil", "FilterPanelComponent", "ChartWrapperComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "report_r6", "name", "ɵɵadvance", "ɵɵtextInterpolate1", "display_name", "ɵɵlistener", "SmartDashboardComponent_div_71_Template_mat_select_selectionChange_7_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "onReportTypeChange", "ɵɵtemplate", "SmartDashboardComponent_div_71_mat_option_10_Template", "ctx_r0", "reportTypeControl", "availableReports", "SmartDashboardComponent_div_72_Template_app_filter_panel_filtersApplied_1_listener", "$event", "_r10", "ctx_r9", "onFiltersApplied", "ctx_r1", "value", "SmartDashboardComponent_div_73_Template_button_click_1_listener", "_r12", "ctx_r11", "generateDashboardFromFilters", "ctx_r2", "isLoading", "currentFilters", "ɵɵclassProp", "ctx_r3", "ɵɵtextInterpolate", "currentSession", "SmartDashboardComponent_div_76_div_21_button_4_Template_button_click_0_listener", "restoredCtx", "_r20", "query_r18", "$implicit", "ctx_r19", "send<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SmartDashboardComponent_div_76_div_21_button_4_Template", "ctx_r14", "exampleQueries", "slice", "ɵɵelement", "message_r21", "chart_data", "chart_type", "SmartDashboardComponent_div_76_div_23_div_12_Template", "ɵɵpureFunction2", "_c2", "type", "ɵɵpipeBind2", "created_at", "content", "has_visualization", "SmartDashboardComponent_div_76_div_21_Template", "SmartDashboardComponent_div_76_div_23_Template", "SmartDashboardComponent_div_76_div_24_Template", "ɵɵpipeBind1", "ctx_r4", "dataframe_summary", "rows", "columns", "chatMessages", "length", "trackByIndex", "register", "SmartDashboardComponent", "constructor", "smartDashboardService", "snackBar", "messageControl", "chartData", "chartType", "chartConfig", "destroy$", "ngOnInit", "loadAvailableReports", "subscribeToServiceState", "ngAfterViewInit", "scrollToBottom", "ngOnDestroy", "next", "complete", "chatMessages$", "pipe", "subscribe", "messages", "setTimeout", "currentSession$", "session", "isLoading$", "loading", "getAvailableReports", "response", "status", "reports", "error", "showError", "console", "reportType", "selectedReport", "find", "r", "addChatMessage", "Date", "filters", "sendMessage", "message", "trim", "setValue", "setLoading", "chatWithDashboard", "parameters", "convertFiltersToParameters", "generateDashboard", "selectedRestaurants", "selectedVendors", "selectedCategories", "selectedSubCategories", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate", "toISOString", "endDate", "selectedBaseDate", "request", "tenant_id", "getTenantId", "report_type", "session_id", "handleDashboardResponse", "handleError", "dataframe_info", "last_activity", "setCurrentSession", "showSuccess", "chatResponse", "data", "updateChart", "convertPandasAIToChartJS", "generateChartConfig", "query", "clearSession", "resetSession", "clearChatMessages", "chatContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "open", "duration", "panelClass", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "index", "getStatusText", "getStatusClass", "getActiveFiltersCount", "count", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "MatSnackBar", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ctx", "SmartDashboardComponent_div_71_Template", "SmartDashboardComponent_div_72_Template", "SmartDashboardComponent_div_73_Template", "SmartDashboardComponent_div_75_Template", "SmartDashboardComponent_div_76_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i4", "NgControlStatus", "FormControlDirective", "i5", "MatButton", "i6", "MatFormField", "i7", "MatSelect", "i8", "MatOption", "i9", "MatIcon", "i10", "MatProgressSpinner", "i11", "MarkdownComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport {\n  SmartDashboardService,\n  ReportType,\n  ChatMessage,\n  DashboardSession,\n  DashboardResponse\n} from '../../services/smart-dashboard.service';\n\nimport { FilterPanelComponent, ReportFilters } from './filter-panel.component';\nimport { ChartWrapperComponent } from './chart-wrapper.component';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatInputModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatChipsModule,\n    MatDividerModule,\n    MatTooltipModule,\n    MatSnackBarModule,\n    MarkdownModule,\n    FilterPanelComponent,\n    ChartWrapperComponent\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('chatContainer') chatContainer!: ElementRef;\n  @ViewChild('messageInput') messageInput!: ElementRef;\n  @ViewChild(ChartWrapperComponent) chartWrapper!: ChartWrapperComponent;\n\n  // Form controls\n  reportTypeControl = new FormControl('');\n  messageControl = new FormControl('');\n\n  // Component state\n  availableReports: ReportType[] = [];\n  chatMessages: ChatMessage[] = [];\n  currentSession: DashboardSession | null = null;\n  isLoading = false;\n  currentFilters: ReportFilters | null = null;\n\n  // Chart data\n  chartData: any = null;\n  chartType: ChartType | string = 'bar';\n  chartConfig: ChartConfiguration | null = null;\n\n  // Example queries for data analysis\n  exampleQueries = [\n    'Create a bar chart of total purchases by vendor',\n    'What is the average order quantity by category?',\n    'Show me a pie chart of inventory by location',\n    'Generate a line chart of purchase trends over time',\n    'What are the top 10 items by value?',\n    'Show me cost variance analysis'\n  ];\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadAvailableReports();\n    this.subscribeToServiceState();\n  }\n\n  ngAfterViewInit(): void {\n    this.scrollToBottom();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private subscribeToServiceState(): void {\n    this.smartDashboardService.chatMessages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.chatMessages = messages;\n        setTimeout(() => this.scrollToBottom(), 100);\n      });\n\n    this.smartDashboardService.currentSession$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(session => {\n        this.currentSession = session;\n      });\n\n    this.smartDashboardService.isLoading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n      });\n  }\n\n  private loadAvailableReports(): void {\n    this.smartDashboardService.getAvailableReports().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.availableReports = response.reports;\n        }\n      },\n      error: (error) => {\n        this.showError('Failed to load available reports');\n        console.error('Error loading reports:', error);\n      }\n    });\n  }\n\n  onReportTypeChange(): void {\n    const reportType = this.reportTypeControl.value;\n    if (reportType) {\n      const selectedReport = this.availableReports.find(r => r.name === reportType);\n      if (selectedReport) {\n        this.addChatMessage({\n          content: `Great! I'll help you generate a ${selectedReport.display_name}. Please configure the filters and click \"Generate Dashboard\" to start.`,\n          type: 'ai',\n          created_at: new Date()\n        });\n      }\n    }\n  }\n\n  onFiltersApplied(filters: ReportFilters): void {\n    this.currentFilters = filters;\n    this.generateDashboardFromFilters();\n  }\n\n  sendMessage(): void {\n    const message = this.messageControl.value?.trim();\n    if (!message) return;\n\n    if (!this.currentSession) {\n      this.showError('Please generate a dashboard first by selecting filters and clicking \"Generate Dashboard\"');\n      return;\n    }\n\n    // Add user message\n    this.addChatMessage({\n      content: message,\n      type: 'human',\n      created_at: new Date()\n    });\n\n    this.messageControl.setValue('');\n    this.smartDashboardService.setLoading(true);\n\n    // Chat with dashboard\n    this.chatWithDashboard(message);\n  }\n\n  generateDashboardFromFilters(): void {\n    if (!this.currentFilters || !this.reportTypeControl.value) {\n      this.showError('Please select filters and report type');\n      return;\n    }\n\n    this.smartDashboardService.setLoading(true);\n\n    // Convert filters to parameters format\n    const parameters = this.convertFiltersToParameters(this.currentFilters);\n\n    this.generateDashboard(this.reportTypeControl.value, parameters);\n  }\n\n  private convertFiltersToParameters(filters: ReportFilters): any {\n    return {\n      selectedRestaurants: filters.selectedRestaurants,\n      selectedVendors: filters.selectedVendors || [],\n      selectedCategories: filters.selectedCategories || [],\n      selectedSubCategories: filters.selectedSubCategories || [],\n      selectedWorkAreas: filters.selectedWorkAreas || [],\n      startDate: filters.startDate?.toISOString(),\n      endDate: filters.endDate?.toISOString(),\n      selectedBaseDate: filters.selectedBaseDate || 'deliveryDate'\n    };\n  }\n\n  private generateDashboard(reportType: string, parameters: any): void {\n    const request = {\n      tenant_id: this.smartDashboardService.getTenantId(),\n      report_type: reportType,\n      parameters: parameters,\n      session_id: this.currentSession?.session_id\n    };\n\n    this.smartDashboardService.generateDashboard(request).subscribe({\n      next: (response) => {\n        this.handleDashboardResponse(response);\n      },\n      error: (error) => {\n        this.handleError('Error generating dashboard', error);\n      }\n    });\n  }\n\n  private handleDashboardResponse(response: DashboardResponse): void {\n    if (response.status === 'success') {\n      // Create session object\n      const session: DashboardSession = {\n        session_id: response.session_id,\n        tenant_id: this.smartDashboardService.getTenantId(),\n        report_type: this.reportTypeControl.value!,\n        parameters: this.convertFiltersToParameters(this.currentFilters!),\n        dataframe_summary: response.dataframe_info!,\n        created_at: new Date().toISOString(),\n        last_activity: new Date().toISOString()\n      };\n\n      this.smartDashboardService.setCurrentSession(session);\n\n      this.addChatMessage({\n        content: 'Dashboard generated successfully! You can now ask questions about your data.',\n        type: 'ai',\n        created_at: new Date()\n      });\n\n      this.showSuccess('Dashboard generated successfully!');\n    } else {\n      this.showError(`Error generating dashboard: ${response.message}`);\n    }\n\n    this.smartDashboardService.setLoading(false);\n  }\n\n  private chatWithDashboard(message: string): void {\n    if (!this.currentSession) return;\n\n    const request = {\n      tenant_id: this.smartDashboardService.getTenantId(),\n      session_id: this.currentSession.session_id,\n      message: message\n    };\n\n    this.smartDashboardService.chatWithDashboard(request).subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          const chatResponse = response.data;\n\n          this.addChatMessage({\n            content: chatResponse.message,\n            type: 'ai',\n            created_at: new Date(),\n            chart_data: chatResponse.chart_data,\n            chart_type: chatResponse.chart_type,\n            has_visualization: chatResponse.has_visualization\n          });\n\n          // Handle visualization\n          if (chatResponse.has_visualization && chatResponse.chart_data) {\n            this.updateChart(chatResponse.chart_data, chatResponse.chart_type);\n          }\n        }\n        this.smartDashboardService.setLoading(false);\n      },\n      error: (error) => {\n        this.handleError('Error processing chat request', error);\n      }\n    });\n  }\n\n  private updateChart(chartData: any, chartType?: string): void {\n    try {\n      this.chartType = chartType || 'bar';\n      // Convert PandasAI data to Chart.js format\n      this.chartData = ChartWrapperComponent.convertPandasAIToChartJS(chartData, this.chartType);\n      this.chartConfig = this.smartDashboardService.generateChartConfig(this.chartData, this.chartType);\n    } catch (error) {\n      console.error('Error updating chart:', error);\n      this.showError('Error displaying chart');\n    }\n  }\n\n  sendExampleQuery(query: string): void {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n\n  clearSession(): void {\n    if (this.currentSession) {\n      this.smartDashboardService.clearSession(this.currentSession.session_id).subscribe({\n        next: () => {\n          this.resetSession();\n          this.showSuccess('Session cleared successfully');\n        },\n        error: (error) => {\n          console.error('Error clearing session:', error);\n          this.resetSession(); // Reset anyway\n        }\n      });\n    } else {\n      this.resetSession();\n    }\n  }\n\n  private resetSession(): void {\n    this.smartDashboardService.setCurrentSession(null);\n    this.smartDashboardService.clearChatMessages();\n    this.currentFilters = null;\n    this.chartData = null;\n    this.chartConfig = null;\n    this.reportTypeControl.setValue('');\n  }\n\n  private addChatMessage(message: ChatMessage): void {\n    this.smartDashboardService.addChatMessage(message);\n  }\n\n  private scrollToBottom(): void {\n    if (this.chatContainer) {\n      const element = this.chatContainer.nativeElement;\n      element.scrollTop = element.scrollHeight;\n    }\n  }\n\n  private handleError(message: string, error: any): void {\n    console.error(message, error);\n    this.showError(message);\n    this.smartDashboardService.setLoading(false);\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  trackByIndex(index: number): number {\n    return index;\n  }\n\n  getStatusText(): string {\n    if (this.isLoading) return 'Processing...';\n    if (this.currentSession) return 'Dashboard ready';\n    return 'Ready';\n  }\n\n  getStatusClass(): string {\n    if (this.isLoading) return 'status-loading';\n    if (this.currentSession) return 'status-ready';\n    return 'status-ready';\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.reportTypeControl.value) count++;\n    if (this.currentFilters) {\n      if (this.currentFilters.selectedRestaurants?.length) count++;\n      if (this.currentFilters.selectedVendors?.length) count++;\n      if (this.currentFilters.selectedCategories?.length) count++;\n      if (this.currentFilters.selectedSubCategories?.length) count++;\n      if (this.currentFilters.startDate) count++;\n      if (this.currentFilters.endDate) count++;\n    }\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Main Layout -->\n  <div class=\"main-layout\">\n    <!-- Left Sidebar - Smart Filters -->\n    <div class=\"sidebar\">\n      <div class=\"filters-header\">\n        <mat-icon class=\"filter-icon\">tune</mat-icon>\n        <span class=\"filter-title\">Smart Filters</span>\n        <span class=\"filter-count\">{{ getActiveFiltersCount() }}</span>\n      </div>\n\n      <!-- Time Period -->\n      <div class=\"filter-group\">\n        <div class=\"filter-group-header\">\n          <mat-icon>schedule</mat-icon>\n          <span>Time Period</span>\n        </div>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select value=\"last30days\">\n            <mat-option value=\"last7days\">Last 7 days</mat-option>\n            <mat-option value=\"last30days\">Last 30 days</mat-option>\n            <mat-option value=\"last90days\">Last 90 days</mat-option>\n            <mat-option value=\"custom\">Custom Range</mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- Categories -->\n      <div class=\"filter-group\">\n        <div class=\"filter-group-header\">\n          <span>Categories</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox>E-commerce</mat-checkbox>\n          <mat-checkbox>SaaS</mat-checkbox>\n          <mat-checkbox>Mobile Apps</mat-checkbox>\n          <mat-checkbox>Marketing</mat-checkbox>\n          <mat-checkbox>Support</mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Regions -->\n      <div class=\"filter-group\">\n        <div class=\"filter-group-header\">\n          <span>Regions</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox>North America</mat-checkbox>\n          <mat-checkbox [checked]=\"true\">Europe</mat-checkbox>\n          <mat-checkbox>Asia Pacific</mat-checkbox>\n          <mat-checkbox>Latin America</mat-checkbox>\n          <mat-checkbox>Africa</mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"filter-group\">\n        <div class=\"filter-group-header\">\n          <span>Key Metrics</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox>Revenue</mat-checkbox>\n          <mat-checkbox>Users</mat-checkbox>\n          <mat-checkbox>Conversions</mat-checkbox>\n          <mat-checkbox>Engagement</mat-checkbox>\n          <mat-checkbox>Retention</mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Report Type Section (Hidden initially) -->\n      <div class=\"filter-section\" *ngIf=\"false\">\n        <div class=\"section-header\">\n          <mat-icon>description</mat-icon>\n          <span>Report Type</span>\n        </div>\n        <mat-form-field appearance=\"outline\" class=\"filter-select\">\n          <mat-select [formControl]=\"reportTypeControl\" (selectionChange)=\"onReportTypeChange()\">\n            <mat-option value=\"\">Choose report type</mat-option>\n            <mat-option *ngFor=\"let report of availableReports\" [value]=\"report.name\">\n              {{ report.display_name }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- Dynamic Filters (Hidden initially) -->\n      <div class=\"filter-section\" *ngIf=\"false && reportTypeControl.value\">\n        <app-filter-panel\n          [reportType]=\"reportTypeControl.value\"\n          (filtersApplied)=\"onFiltersApplied($event)\">\n        </app-filter-panel>\n      </div>\n\n      <!-- Generate Button (Hidden initially) -->\n      <div class=\"generate-section\" *ngIf=\"false && reportTypeControl.value\">\n        <button\n          mat-raised-button\n          color=\"primary\"\n          class=\"generate-btn\"\n          (click)=\"generateDashboardFromFilters()\"\n          [disabled]=\"isLoading || !currentFilters\">\n          <mat-icon>auto_awesome</mat-icon>\n          Generate Dashboard\n        </button>\n      </div>\n    </div>\n\n    <!-- Center Panel - Main Content -->\n    <div class=\"center-panel\">\n      <!-- Welcome State -->\n      <div class=\"welcome-state\" *ngIf=\"!currentSession\">\n        <div class=\"welcome-content\">\n          <div class=\"ai-brain-icon\">\n            <mat-icon>psychology</mat-icon>\n          </div>\n          <h1>AI-Powered Data Analysis</h1>\n          <p>Configure your report filters and generate a dashboard to start analyzing your data with AI.</p>\n\n          <div class=\"features-grid\">\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">\n                <mat-icon>bar_chart</mat-icon>\n              </div>\n              <h3>Interactive Charts</h3>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">\n                <mat-icon>insights</mat-icon>\n              </div>\n              <h3>Smart Insights</h3>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">\n                <mat-icon>chat</mat-icon>\n              </div>\n              <h3>Natural Language Queries</h3>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">\n                <mat-icon>speed</mat-icon>\n              </div>\n              <h3>Real-time Analysis</h3>\n            </div>\n          </div>\n\n          <!-- Steps -->\n          <div class=\"steps-section\">\n            <div class=\"step-item\" [class.completed]=\"reportTypeControl.value\">\n              <div class=\"step-icon\">\n                <mat-icon>{{ reportTypeControl.value ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n              </div>\n              <span>Select your filters (categories/regions)</span>\n            </div>\n            <div class=\"step-item\" [class.completed]=\"currentSession\">\n              <div class=\"step-icon\">\n                <mat-icon>{{ currentSession ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n              </div>\n              <span>Ask the AI assistant what you'd like to visualize</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Dashboard Content -->\n      <div class=\"dashboard-content\" *ngIf=\"currentSession\">\n        <!-- Data Summary Stats -->\n        <div class=\"data-summary\">\n          <div class=\"summary-item\">\n            <span class=\"summary-label\">Data Rows</span>\n            <span class=\"summary-value\">{{ currentSession.dataframe_summary.rows | number }}</span>\n          </div>\n          <div class=\"summary-item\">\n            <span class=\"summary-label\">Columns</span>\n            <span class=\"summary-value\">{{ currentSession.dataframe_summary.columns | number }}</span>\n          </div>\n          <div class=\"summary-item\">\n            <span class=\"summary-label\">Report Type</span>\n            <span class=\"summary-value\">{{ reportTypeControl.value }}</span>\n          </div>\n        </div>\n\n        <!-- Chat Messages -->\n        <div class=\"chat-messages\" #chatContainer>\n          <!-- Quick Actions -->\n          <div class=\"quick-actions\" *ngIf=\"chatMessages.length === 0\">\n            <h3>Quick Analysis</h3>\n            <div class=\"action-grid\">\n              <button\n                mat-stroked-button\n                *ngFor=\"let query of exampleQueries.slice(0, 4)\"\n                (click)=\"sendExampleQuery(query)\"\n                class=\"action-button\">\n                <mat-icon>auto_awesome</mat-icon>\n                {{ query }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Messages -->\n          <div class=\"messages-list\">\n            <div\n              *ngFor=\"let message of chatMessages; trackBy: trackByIndex\"\n              class=\"message\"\n              [ngClass]=\"{'user-message': message.type === 'human', 'ai-message': message.type === 'ai'}\">\n\n              <div class=\"message-content\">\n                <div class=\"message-header\">\n                  <span class=\"message-sender\">\n                    <mat-icon>{{ message.type === 'human' ? 'person' : 'psychology' }}</mat-icon>\n                    {{ message.type === 'human' ? 'You' : 'AI Assistant' }}\n                  </span>\n                  <span class=\"message-time\">\n                    {{ message.created_at | date:'short' }}\n                  </span>\n                </div>\n\n                <div class=\"message-text\">\n                  <markdown [data]=\"message.content\"></markdown>\n                </div>\n\n                <!-- Chart Visualization -->\n                <div *ngIf=\"message.has_visualization && message.chart_data\" class=\"message-chart\">\n                  <app-chart-wrapper\n                    [chartData]=\"message.chart_data\"\n                    [chartType]=\"message.chart_type || 'bar'\">\n                  </app-chart-wrapper>\n                </div>\n              </div>\n            </div>\n\n            <!-- Loading Indicator -->\n            <div *ngIf=\"isLoading\" class=\"loading-message\">\n              <mat-spinner diameter=\"24\"></mat-spinner>\n              <span>AI is analyzing your data...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,KAAK,EAAiCC,aAAa,QAAQ,UAAU;AAC9E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAUzC,SAASC,oBAAoB,QAAuB,0BAA0B;AAC9E,SAASC,qBAAqB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;ICoDrDC,EAAA,CAAAC,cAAA,qBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,IAAA,CAAqB;IACvEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,YAAA,MACF;;;;;;IAVNT,EAAA,CAAAC,cAAA,cAA0C;IAE5BD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1BH,EAAA,CAAAC,cAAA,yBAA2D;IACXD,EAAA,CAAAU,UAAA,6BAAAC,8EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAmBf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACpFjB,EAAA,CAAAC,cAAA,qBAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACpDH,EAAA,CAAAkB,UAAA,KAAAC,qDAAA,yBAEa;IACfnB,EAAA,CAAAG,YAAA,EAAa;;;;IALDH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,gBAAAgB,MAAA,CAAAC,iBAAA,CAAiC;IAEZrB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAE,gBAAA,CAAmB;;;;;;IAQxDtB,EAAA,CAAAC,cAAA,cAAqE;IAGjED,EAAA,CAAAU,UAAA,4BAAAa,mFAAAC,MAAA;MAAAxB,EAAA,CAAAY,aAAA,CAAAa,IAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAe,aAAA;MAAA,OAAkBf,EAAA,CAAAgB,WAAA,CAAAU,MAAA,CAAAC,gBAAA,CAAAH,MAAA,CAAwB;IAAA,EAAC;IAC7CxB,EAAA,CAAAG,YAAA,EAAmB;;;;IAFjBH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,eAAAwB,MAAA,CAAAP,iBAAA,CAAAQ,KAAA,CAAsC;;;;;;IAM1C7B,EAAA,CAAAC,cAAA,cAAuE;IAKnED,EAAA,CAAAU,UAAA,mBAAAoB,gEAAA;MAAA9B,EAAA,CAAAY,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgB,OAAA,CAAAC,4BAAA,EAA8B;IAAA,EAAC;IAExCjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,aAAA8B,MAAA,CAAAC,SAAA,KAAAD,MAAA,CAAAE,cAAA,CAAyC;;;;;IAU7CpC,EAAA,CAAAC,cAAA,cAAmD;IAGnCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mGAA4F;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnGH,EAAA,CAAAC,cAAA,cAA2B;IAGXD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAK/BH,EAAA,CAAAC,cAAA,eAA2B;IAGXD,EAAA,CAAAE,MAAA,IAAyE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,eAA0D;IAE5CD,EAAA,CAAAE,MAAA,IAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEvFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yDAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAVzCH,EAAA,CAAAO,SAAA,IAA2C;IAA3CP,EAAA,CAAAqC,WAAA,cAAAC,MAAA,CAAAjB,iBAAA,CAAAQ,KAAA,CAA2C;IAEpD7B,EAAA,CAAAO,SAAA,GAAyE;IAAzEP,EAAA,CAAAuC,iBAAA,CAAAD,MAAA,CAAAjB,iBAAA,CAAAQ,KAAA,6CAAyE;IAIhE7B,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAqC,WAAA,cAAAC,MAAA,CAAAE,cAAA,CAAkC;IAE3CxC,EAAA,CAAAO,SAAA,GAAgE;IAAhEP,EAAA,CAAAuC,iBAAA,CAAAD,MAAA,CAAAE,cAAA,6CAAgE;;;;;;IAgC5ExC,EAAA,CAAAC,cAAA,iBAIwB;IADtBD,EAAA,CAAAU,UAAA,mBAAA+B,gFAAA;MAAA,MAAAC,WAAA,GAAA1C,EAAA,CAAAY,aAAA,CAAA+B,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA8B,OAAA,CAAAC,gBAAA,CAAAH,SAAA,CAAuB;IAAA,EAAC;IAEjC5C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAoC,SAAA,MACF;;;;;IAVJ5C,EAAA,CAAAC,cAAA,cAA6D;IACvDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,UAAA,IAAA8B,uDAAA,qBAOS;IACXhD,EAAA,CAAAG,YAAA,EAAM;;;;IANgBH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,YAAA6C,OAAA,CAAAC,cAAA,CAAAC,KAAA,OAA6B;;;;;IAgC/CnD,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAoD,SAAA,4BAGoB;IACtBpD,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,cAAAiD,WAAA,CAAAC,UAAA,CAAgC,cAAAD,WAAA,CAAAE,UAAA;;;;;;;;;;;IAvBxCvD,EAAA,CAAAC,cAAA,cAG8F;IAK5ED,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7EH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAoD,SAAA,oBAA8C;IAChDpD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAkB,UAAA,KAAAsC,qDAAA,kBAKM;IACRxD,EAAA,CAAAG,YAAA,EAAM;;;;IAxBNH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAAL,WAAA,CAAAM,IAAA,cAAAN,WAAA,CAAAM,IAAA,WAA2F;IAK3E3D,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAuC,iBAAA,CAAAc,WAAA,CAAAM,IAAA,uCAAwD;IAClE3D,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA6C,WAAA,CAAAM,IAAA,2CACF;IAEE3D,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA4D,WAAA,OAAAP,WAAA,CAAAQ,UAAA,gBACF;IAIU7D,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAiD,WAAA,CAAAS,OAAA,CAAwB;IAI9B9D,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,SAAAiD,WAAA,CAAAU,iBAAA,IAAAV,WAAA,CAAAC,UAAA,CAAqD;;;;;IAU/DtD,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAoD,SAAA,sBAAyC;IACzCpD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IArEjDH,EAAA,CAAAC,cAAA,cAAsD;IAIpBD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzFH,EAAA,CAAAC,cAAA,cAA0B;IACID,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAuD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5FH,EAAA,CAAAC,cAAA,eAA0B;IACID,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpEH,EAAA,CAAAC,cAAA,mBAA0C;IAExCD,EAAA,CAAAkB,UAAA,KAAA8C,8CAAA,kBAYM;IAGNhE,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAkB,UAAA,KAAA+C,8CAAA,oBA4BM;IAGNjE,EAAA,CAAAkB,UAAA,KAAAgD,8CAAA,kBAGM;IACRlE,EAAA,CAAAG,YAAA,EAAM;;;;IAlEwBH,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAmE,WAAA,OAAAC,MAAA,CAAA5B,cAAA,CAAA6B,iBAAA,CAAAC,IAAA,EAAoD;IAIpDtE,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAmE,WAAA,QAAAC,MAAA,CAAA5B,cAAA,CAAA6B,iBAAA,CAAAE,OAAA,EAAuD;IAIvDvE,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAuC,iBAAA,CAAA6B,MAAA,CAAA/C,iBAAA,CAAAQ,KAAA,CAA6B;IAO/B7B,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAgE,MAAA,CAAAI,YAAA,CAAAC,MAAA,OAA+B;IAiBnCzE,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAgE,MAAA,CAAAI,YAAA,CAAiB,iBAAAJ,MAAA,CAAAM,YAAA;IA8BjC1E,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAgE,MAAA,CAAAjC,SAAA,CAAe;;;AD3MjCzC,KAAK,CAACiF,QAAQ,CAAC,GAAGhF,aAAa,CAAC;AAEhC,MAwBaiF,uBAAuB;EAiClCC,YACUC,qBAA4C,EAC5CC,QAAqB;IADrB,KAAAD,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,QAAQ,GAARA,QAAQ;IA9BlB;IACA,KAAA1D,iBAAiB,GAAG,IAAIvC,WAAW,CAAC,EAAE,CAAC;IACvC,KAAAkG,cAAc,GAAG,IAAIlG,WAAW,CAAC,EAAE,CAAC;IAEpC;IACA,KAAAwC,gBAAgB,GAAiB,EAAE;IACnC,KAAAkD,YAAY,GAAkB,EAAE;IAChC,KAAAhC,cAAc,GAA4B,IAAI;IAC9C,KAAAL,SAAS,GAAG,KAAK;IACjB,KAAAC,cAAc,GAAyB,IAAI;IAE3C;IACA,KAAA6C,SAAS,GAAQ,IAAI;IACrB,KAAAC,SAAS,GAAuB,KAAK;IACrC,KAAAC,WAAW,GAA8B,IAAI;IAE7C;IACA,KAAAjC,cAAc,GAAG,CACf,iDAAiD,EACjD,iDAAiD,EACjD,8CAA8C,EAC9C,oDAAoD,EACpD,qCAAqC,EACrC,gCAAgC,CACjC;IAEO,KAAAkC,QAAQ,GAAG,IAAIxF,OAAO,EAAQ;EAKnC;EAEHyF,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,QAAQ,CAACO,IAAI,EAAE;IACpB,IAAI,CAACP,QAAQ,CAACQ,QAAQ,EAAE;EAC1B;EAEQL,uBAAuBA,CAAA;IAC7B,IAAI,CAACT,qBAAqB,CAACe,aAAa,CACrCC,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAACxB,YAAY,GAAGwB,QAAQ;MAC5BC,UAAU,CAAC,MAAM,IAAI,CAACR,cAAc,EAAE,EAAE,GAAG,CAAC;IAC9C,CAAC,CAAC;IAEJ,IAAI,CAACX,qBAAqB,CAACoB,eAAe,CACvCJ,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC3D,cAAc,GAAG2D,OAAO;IAC/B,CAAC,CAAC;IAEJ,IAAI,CAACrB,qBAAqB,CAACsB,UAAU,CAClCN,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACM,OAAO,IAAG;MACnB,IAAI,CAAClE,SAAS,GAAGkE,OAAO;IAC1B,CAAC,CAAC;EACN;EAEQf,oBAAoBA,CAAA;IAC1B,IAAI,CAACR,qBAAqB,CAACwB,mBAAmB,EAAE,CAACP,SAAS,CAAC;MACzDJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAClF,gBAAgB,GAAGiF,QAAQ,CAACE,OAAO;;MAE5C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,kCAAkC,CAAC;QAClDC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAzF,kBAAkBA,CAAA;IAChB,MAAM4F,UAAU,GAAG,IAAI,CAACxF,iBAAiB,CAACQ,KAAK;IAC/C,IAAIgF,UAAU,EAAE;MACd,MAAMC,cAAc,GAAG,IAAI,CAACxF,gBAAgB,CAACyF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,IAAI,KAAKuG,UAAU,CAAC;MAC7E,IAAIC,cAAc,EAAE;QAClB,IAAI,CAACG,cAAc,CAAC;UAClBnD,OAAO,EAAE,mCAAmCgD,cAAc,CAACrG,YAAY,yEAAyE;UAChJkD,IAAI,EAAE,IAAI;UACVE,UAAU,EAAE,IAAIqD,IAAI;SACrB,CAAC;;;EAGR;EAEAvF,gBAAgBA,CAACwF,OAAsB;IACrC,IAAI,CAAC/E,cAAc,GAAG+E,OAAO;IAC7B,IAAI,CAAClF,4BAA4B,EAAE;EACrC;EAEAmF,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAI,CAACrC,cAAc,CAACnD,KAAK,EAAEyF,IAAI,EAAE;IACjD,IAAI,CAACD,OAAO,EAAE;IAEd,IAAI,CAAC,IAAI,CAAC7E,cAAc,EAAE;MACxB,IAAI,CAACmE,SAAS,CAAC,0FAA0F,CAAC;MAC1G;;IAGF;IACA,IAAI,CAACM,cAAc,CAAC;MAClBnD,OAAO,EAAEuD,OAAO;MAChB1D,IAAI,EAAE,OAAO;MACbE,UAAU,EAAE,IAAIqD,IAAI;KACrB,CAAC;IAEF,IAAI,CAAClC,cAAc,CAACuC,QAAQ,CAAC,EAAE,CAAC;IAChC,IAAI,CAACzC,qBAAqB,CAAC0C,UAAU,CAAC,IAAI,CAAC;IAE3C;IACA,IAAI,CAACC,iBAAiB,CAACJ,OAAO,CAAC;EACjC;EAEApF,4BAA4BA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACG,cAAc,IAAI,CAAC,IAAI,CAACf,iBAAiB,CAACQ,KAAK,EAAE;MACzD,IAAI,CAAC8E,SAAS,CAAC,uCAAuC,CAAC;MACvD;;IAGF,IAAI,CAAC7B,qBAAqB,CAAC0C,UAAU,CAAC,IAAI,CAAC;IAE3C;IACA,MAAME,UAAU,GAAG,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACvF,cAAc,CAAC;IAEvE,IAAI,CAACwF,iBAAiB,CAAC,IAAI,CAACvG,iBAAiB,CAACQ,KAAK,EAAE6F,UAAU,CAAC;EAClE;EAEQC,0BAA0BA,CAACR,OAAsB;IACvD,OAAO;MACLU,mBAAmB,EAAEV,OAAO,CAACU,mBAAmB;MAChDC,eAAe,EAAEX,OAAO,CAACW,eAAe,IAAI,EAAE;MAC9CC,kBAAkB,EAAEZ,OAAO,CAACY,kBAAkB,IAAI,EAAE;MACpDC,qBAAqB,EAAEb,OAAO,CAACa,qBAAqB,IAAI,EAAE;MAC1DC,iBAAiB,EAAEd,OAAO,CAACc,iBAAiB,IAAI,EAAE;MAClDC,SAAS,EAAEf,OAAO,CAACe,SAAS,EAAEC,WAAW,EAAE;MAC3CC,OAAO,EAAEjB,OAAO,CAACiB,OAAO,EAAED,WAAW,EAAE;MACvCE,gBAAgB,EAAElB,OAAO,CAACkB,gBAAgB,IAAI;KAC/C;EACH;EAEQT,iBAAiBA,CAACf,UAAkB,EAAEa,UAAe;IAC3D,MAAMY,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACzD,qBAAqB,CAAC0D,WAAW,EAAE;MACnDC,WAAW,EAAE5B,UAAU;MACvBa,UAAU,EAAEA,UAAU;MACtBgB,UAAU,EAAE,IAAI,CAAClG,cAAc,EAAEkG;KAClC;IAED,IAAI,CAAC5D,qBAAqB,CAAC8C,iBAAiB,CAACU,OAAO,CAAC,CAACvC,SAAS,CAAC;MAC9DJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAI,CAACoC,uBAAuB,CAACpC,QAAQ,CAAC;MACxC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACkC,WAAW,CAAC,4BAA4B,EAAElC,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEQiC,uBAAuBA,CAACpC,QAA2B;IACzD,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;MACjC;MACA,MAAML,OAAO,GAAqB;QAChCuC,UAAU,EAAEnC,QAAQ,CAACmC,UAAU;QAC/BH,SAAS,EAAE,IAAI,CAACzD,qBAAqB,CAAC0D,WAAW,EAAE;QACnDC,WAAW,EAAE,IAAI,CAACpH,iBAAiB,CAACQ,KAAM;QAC1C6F,UAAU,EAAE,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACvF,cAAe,CAAC;QACjEiC,iBAAiB,EAAEkC,QAAQ,CAACsC,cAAe;QAC3ChF,UAAU,EAAE,IAAIqD,IAAI,EAAE,CAACiB,WAAW,EAAE;QACpCW,aAAa,EAAE,IAAI5B,IAAI,EAAE,CAACiB,WAAW;OACtC;MAED,IAAI,CAACrD,qBAAqB,CAACiE,iBAAiB,CAAC5C,OAAO,CAAC;MAErD,IAAI,CAACc,cAAc,CAAC;QAClBnD,OAAO,EAAE,8EAA8E;QACvFH,IAAI,EAAE,IAAI;QACVE,UAAU,EAAE,IAAIqD,IAAI;OACrB,CAAC;MAEF,IAAI,CAAC8B,WAAW,CAAC,mCAAmC,CAAC;KACtD,MAAM;MACL,IAAI,CAACrC,SAAS,CAAC,+BAA+BJ,QAAQ,CAACc,OAAO,EAAE,CAAC;;IAGnE,IAAI,CAACvC,qBAAqB,CAAC0C,UAAU,CAAC,KAAK,CAAC;EAC9C;EAEQC,iBAAiBA,CAACJ,OAAe;IACvC,IAAI,CAAC,IAAI,CAAC7E,cAAc,EAAE;IAE1B,MAAM8F,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACzD,qBAAqB,CAAC0D,WAAW,EAAE;MACnDE,UAAU,EAAE,IAAI,CAAClG,cAAc,CAACkG,UAAU;MAC1CrB,OAAO,EAAEA;KACV;IAED,IAAI,CAACvC,qBAAqB,CAAC2C,iBAAiB,CAACa,OAAO,CAAC,CAACvC,SAAS,CAAC;MAC9DJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,MAAMyC,YAAY,GAAG1C,QAAQ,CAAC2C,IAAI;UAElC,IAAI,CAACjC,cAAc,CAAC;YAClBnD,OAAO,EAAEmF,YAAY,CAAC5B,OAAO;YAC7B1D,IAAI,EAAE,IAAI;YACVE,UAAU,EAAE,IAAIqD,IAAI,EAAE;YACtB5D,UAAU,EAAE2F,YAAY,CAAC3F,UAAU;YACnCC,UAAU,EAAE0F,YAAY,CAAC1F,UAAU;YACnCQ,iBAAiB,EAAEkF,YAAY,CAAClF;WACjC,CAAC;UAEF;UACA,IAAIkF,YAAY,CAAClF,iBAAiB,IAAIkF,YAAY,CAAC3F,UAAU,EAAE;YAC7D,IAAI,CAAC6F,WAAW,CAACF,YAAY,CAAC3F,UAAU,EAAE2F,YAAY,CAAC1F,UAAU,CAAC;;;QAGtE,IAAI,CAACuB,qBAAqB,CAAC0C,UAAU,CAAC,KAAK,CAAC;MAC9C,CAAC;MACDd,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACkC,WAAW,CAAC,+BAA+B,EAAElC,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEQyC,WAAWA,CAAClE,SAAc,EAAEC,SAAkB;IACpD,IAAI;MACF,IAAI,CAACA,SAAS,GAAGA,SAAS,IAAI,KAAK;MACnC;MACA,IAAI,CAACD,SAAS,GAAGlF,qBAAqB,CAACqJ,wBAAwB,CAACnE,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;MAC1F,IAAI,CAACC,WAAW,GAAG,IAAI,CAACL,qBAAqB,CAACuE,mBAAmB,CAAC,IAAI,CAACpE,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;KAClG,CAAC,OAAOwB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC;;EAE5C;EAEA5D,gBAAgBA,CAACuG,KAAa;IAC5B,IAAI,CAACtE,cAAc,CAACuC,QAAQ,CAAC+B,KAAK,CAAC;IACnC,IAAI,CAAClC,WAAW,EAAE;EACpB;EAEAmC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/G,cAAc,EAAE;MACvB,IAAI,CAACsC,qBAAqB,CAACyE,YAAY,CAAC,IAAI,CAAC/G,cAAc,CAACkG,UAAU,CAAC,CAAC3C,SAAS,CAAC;QAChFJ,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC6D,YAAY,EAAE;UACnB,IAAI,CAACR,WAAW,CAAC,8BAA8B,CAAC;QAClD,CAAC;QACDtC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAAC8C,YAAY,EAAE,CAAC,CAAC;QACvB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAAC1E,qBAAqB,CAACiE,iBAAiB,CAAC,IAAI,CAAC;IAClD,IAAI,CAACjE,qBAAqB,CAAC2E,iBAAiB,EAAE;IAC9C,IAAI,CAACrH,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC6C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACE,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC9D,iBAAiB,CAACkG,QAAQ,CAAC,EAAE,CAAC;EACrC;EAEQN,cAAcA,CAACI,OAAoB;IACzC,IAAI,CAACvC,qBAAqB,CAACmC,cAAc,CAACI,OAAO,CAAC;EACpD;EAEQ5B,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACiE,aAAa,EAAE;MACtB,MAAMC,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa;MAChDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;EAE5C;EAEQlB,WAAWA,CAACvB,OAAe,EAAEX,KAAU;IAC7CE,OAAO,CAACF,KAAK,CAACW,OAAO,EAAEX,KAAK,CAAC;IAC7B,IAAI,CAACC,SAAS,CAACU,OAAO,CAAC;IACvB,IAAI,CAACvC,qBAAqB,CAAC0C,UAAU,CAAC,KAAK,CAAC;EAC9C;EAEQb,SAASA,CAACU,OAAe;IAC/B,IAAI,CAACtC,QAAQ,CAACgF,IAAI,CAAC1C,OAAO,EAAE,OAAO,EAAE;MACnC2C,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEQjB,WAAWA,CAAC3B,OAAe;IACjC,IAAI,CAACtC,QAAQ,CAACgF,IAAI,CAAC1C,OAAO,EAAE,OAAO,EAAE;MACnC2C,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEAC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAClD,WAAW,EAAE;;EAEtB;EAEA1C,YAAYA,CAAC6F,KAAa;IACxB,OAAOA,KAAK;EACd;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACrI,SAAS,EAAE,OAAO,eAAe;IAC1C,IAAI,IAAI,CAACK,cAAc,EAAE,OAAO,iBAAiB;IACjD,OAAO,OAAO;EAChB;EAEAiI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtI,SAAS,EAAE,OAAO,gBAAgB;IAC3C,IAAI,IAAI,CAACK,cAAc,EAAE,OAAO,cAAc;IAC9C,OAAO,cAAc;EACvB;EAEAkI,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACtJ,iBAAiB,CAACQ,KAAK,EAAE8I,KAAK,EAAE;IACzC,IAAI,IAAI,CAACvI,cAAc,EAAE;MACvB,IAAI,IAAI,CAACA,cAAc,CAACyF,mBAAmB,EAAEpD,MAAM,EAAEkG,KAAK,EAAE;MAC5D,IAAI,IAAI,CAACvI,cAAc,CAAC0F,eAAe,EAAErD,MAAM,EAAEkG,KAAK,EAAE;MACxD,IAAI,IAAI,CAACvI,cAAc,CAAC2F,kBAAkB,EAAEtD,MAAM,EAAEkG,KAAK,EAAE;MAC3D,IAAI,IAAI,CAACvI,cAAc,CAAC4F,qBAAqB,EAAEvD,MAAM,EAAEkG,KAAK,EAAE;MAC9D,IAAI,IAAI,CAACvI,cAAc,CAAC8F,SAAS,EAAEyC,KAAK,EAAE;MAC1C,IAAI,IAAI,CAACvI,cAAc,CAACgG,OAAO,EAAEuC,KAAK,EAAE;;IAE1C,OAAOA,KAAK;EACd;;;uBA5VW/F,uBAAuB,EAAA5E,EAAA,CAAA4K,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAA9K,EAAA,CAAA4K,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBpG,uBAAuB;MAAAqG,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAGvBrL,qBAAqB;;;;;;;;;;;;;;;;UCzDlCC,EAAA,CAAAC,cAAA,aAAuC;UAMDD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/CH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,GAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIjEH,EAAA,CAAAC,cAAA,cAA0B;UAEZD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,yBAA0D;UAExBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAC,cAAA,sBAA+B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACxDH,EAAA,CAAAC,cAAA,sBAA+B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACxDH,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAM1DH,EAAA,CAAAC,cAAA,cAA0B;UAEhBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,eAA4B;UACZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACvCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACjCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACxCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACtCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAKxCH,EAAA,CAAAC,cAAA,cAA0B;UAEhBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtBH,EAAA,CAAAC,cAAA,eAA4B;UACZD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAC1CH,EAAA,CAAAC,cAAA,wBAA+B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACpDH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACzCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAC1CH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAKvCH,EAAA,CAAAC,cAAA,cAA0B;UAEhBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,eAA4B;UACZD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACpCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAClCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACxCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACvCH,EAAA,CAAAC,cAAA,oBAAc;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAK1CH,EAAA,CAAAkB,UAAA,KAAAoK,uCAAA,mBAaM;UAGNtL,EAAA,CAAAkB,UAAA,KAAAqK,uCAAA,kBAKM;UAGNvL,EAAA,CAAAkB,UAAA,KAAAsK,uCAAA,kBAUM;UACRxL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA0B;UAExBD,EAAA,CAAAkB,UAAA,KAAAuK,uCAAA,mBAmDM;UAGNzL,EAAA,CAAAkB,UAAA,KAAAwK,uCAAA,oBAyEM;UACV1L,EAAA,CAAAG,YAAA,EAAM;;;UAtO2BH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAuC,iBAAA,CAAA8I,GAAA,CAAAX,qBAAA,GAA6B;UAwCxC1K,EAAA,CAAAO,SAAA,IAAgB;UAAhBP,EAAA,CAAAI,UAAA,iBAAgB;UAsBLJ,EAAA,CAAAO,SAAA,IAAW;UAAXP,EAAA,CAAAI,UAAA,eAAW;UAgBXJ,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,kBAAAiL,GAAA,CAAAhK,iBAAA,CAAAQ,KAAA,CAAsC;UAQpC7B,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,kBAAAiL,GAAA,CAAAhK,iBAAA,CAAAQ,KAAA,CAAsC;UAgBzC7B,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAiL,GAAA,CAAA7I,cAAA,CAAqB;UAsDjBxC,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAI,UAAA,SAAAiL,GAAA,CAAA7I,cAAA,CAAoB;;;qBDlItD7D,YAAY,EAAAgN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EAAAJ,EAAA,CAAAK,QAAA,EACZpN,WAAW,EAAAqN,EAAA,CAAAC,eAAA,EACXrN,mBAAmB,EAAAoN,EAAA,CAAAE,oBAAA,EACnBpN,aAAa,EACbC,eAAe,EAAAoN,EAAA,CAAAC,SAAA,EACfpN,cAAc,EAAAqN,EAAA,CAAAC,YAAA,EACdrN,eAAe,EAAAsN,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfxN,aAAa,EAAAyN,EAAA,CAAAC,OAAA,EACbzN,wBAAwB,EAAA0N,GAAA,CAAAC,kBAAA,EACxB1N,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EAAAuN,GAAA,CAAAC,iBAAA,EACdnN,oBAAoB,EACpBC,qBAAqB;MAAAmN,MAAA;IAAA;EAAA;;SAKZtI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}