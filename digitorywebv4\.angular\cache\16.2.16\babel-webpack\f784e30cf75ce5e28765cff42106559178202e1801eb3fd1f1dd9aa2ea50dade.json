{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const i_r7 = restoredCtx.index;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onTabChange(i_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tab_r6.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r6.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.getActiveFiltersCount());\n  }\n}\nfunction SmartDashboardComponent_div_28_mat_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r14.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_28_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r15.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r15.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"label\", 29)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 30)(7, \"mat-select\", 31);\n    i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_div_28_Template_mat_select_valueChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.selectedLocations = $event);\n    });\n    i0.ɵɵtemplate(8, SmartDashboardComponent_div_28_mat_option_8_Template, 2, 2, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"label\", 29)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Base Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 30)(15, \"mat-select\", 33);\n    i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_div_28_Template_mat_select_valueChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.selectedBaseDate = $event);\n    });\n    i0.ɵɵtemplate(16, SmartDashboardComponent_div_28_mat_option_16_Template, 2, 2, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 28)(18, \"label\", 29)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"date_range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Start Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 30)(23, \"input\", 34);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_28_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.startDate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"mat-datepicker-toggle\", 35)(25, \"mat-datepicker\", null, 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"label\", 29)(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"date_range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" End Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 30)(33, \"input\", 37);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_28_Template_input_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.endDate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"mat-datepicker-toggle\", 35)(35, \"mat-datepicker\", null, 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(26);\n    const _r13 = i0.ɵɵreference(36);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedLocations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.locations);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedBaseDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.baseDates);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"matDatepicker\", _r12)(\"ngModel\", ctx_r2.startDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r12);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"matDatepicker\", _r13)(\"ngModel\", ctx_r2.endDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r13);\n  }\n}\nfunction SmartDashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"mat-form-field\", 42)(3, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_30_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.chatMessage = $event);\n    })(\"keydown\", function SmartDashboardComponent_div_30_Template_input_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onKeyPress($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_30_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.sendMessage());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"send\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Generate \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.chatMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.chatMessage.trim());\n  }\n}\nfunction SmartDashboardComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"mat-icon\", 47);\n    i0.ɵɵtext(3, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Ready to Generate Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Configure your filters and ask the AI assistant to generate visualizations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48)(9, \"div\", 49)(10, \"mat-icon\", 50);\n    i0.ɵɵtext(11, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"mat-icon\", 50);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 49)(20, \"mat-icon\", 50);\n    i0.ɵɵtext(21, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Natural Language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 49)(25, \"mat-icon\", 50);\n    i0.ɵɵtext(26, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵtext(4, \"Chart 1\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 53)(6, \"div\", 54);\n    i0.ɵɵtext(7, \"Chart 2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 53)(9, \"div\", 54);\n    i0.ɵɵtext(10, \"Chart 3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 53)(12, \"div\", 54);\n    i0.ɵɵtext(13, \"Chart 4\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.isFiltersExpanded = false;\n    this.isAIExpanded = false;\n    this.isFullscreen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersExpanded = !this.isFiltersExpanded;\n  }\n  toggleAI() {\n    this.isAIExpanded = !this.isAIExpanded;\n  }\n  toggleFullscreen() {\n    this.isFullscreen = !this.isFullscreen;\n  }\n  refreshCharts() {\n    // Refresh chart data\n    console.log('Refreshing charts...');\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 16,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"dashboard-header\"], [1, \"tabs-section\"], [1, \"tabs-container\"], [\"class\", \"tab-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"controls-section\"], [\"mat-button\", \"\", 1, \"toggle-btn\", 3, \"click\"], [\"class\", \"filter-count\", 4, \"ngIf\"], [1, \"expand-icon\"], [1, \"quick-actions\"], [\"mat-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"filters-section\"], [\"class\", \"filters-content\", 4, \"ngIf\"], [1, \"ai-section\"], [\"class\", \"ai-content\", 4, \"ngIf\"], [1, \"dashboard-charts\"], [1, \"charts-header\"], [1, \"charts-icon\"], [1, \"charts-actions\"], [\"mat-icon-button\", \"\", 1, \"fullscreen-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", 1, \"refresh-btn\", 3, \"click\"], [1, \"charts-content\"], [\"class\", \"charts-placeholder\", 4, \"ngIf\"], [\"class\", \"generated-charts\", 4, \"ngIf\"], [1, \"tab-button\", 3, \"click\"], [1, \"filter-count\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", 3, \"value\", \"valueChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [3, \"value\"], [1, \"ai-content\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me sales trends for the last quarter by region\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"charts-placeholder\"], [1, \"placeholder-content\"], [1, \"placeholder-icon\"], [1, \"feature-cards\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"generated-charts\"], [1, \"chart-grid\"], [1, \"chart-item\"], [1, \"chart-placeholder\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, SmartDashboardComponent_button_4_Template, 2, 3, \"button\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_6_listener() {\n            return ctx.toggleFilters();\n          });\n          i0.ɵɵelementStart(7, \"mat-icon\");\n          i0.ɵɵtext(8, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \" Filters \");\n          i0.ɵɵtemplate(10, SmartDashboardComponent_span_10_Template, 2, 1, \"span\", 7);\n          i0.ɵɵelementStart(11, \"mat-icon\", 8);\n          i0.ɵɵtext(12, \"expand_more\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_13_listener() {\n            return ctx.toggleAI();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" AI Assistant \");\n          i0.ɵɵelementStart(17, \"mat-icon\", 8);\n          i0.ɵɵtext(18, \"expand_more\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_20_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_23_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Apply \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 12);\n          i0.ɵɵtemplate(28, SmartDashboardComponent_div_28_Template, 37, 10, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 14);\n          i0.ɵɵtemplate(30, SmartDashboardComponent_div_30_Template, 8, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 16)(32, \"div\", 17)(33, \"mat-icon\", 18);\n          i0.ɵɵtext(34, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"h3\");\n          i0.ɵɵtext(36, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 19)(38, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_38_listener() {\n            return ctx.toggleFullscreen();\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"fullscreen\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_41_listener() {\n            return ctx.refreshCharts();\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 22);\n          i0.ɵɵtemplate(45, SmartDashboardComponent_div_45_Template, 29, 0, \"div\", 23);\n          i0.ɵɵtemplate(46, SmartDashboardComponent_div_46_Template, 14, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"expanded\", ctx.isFiltersExpanded);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"expanded\", ctx.isAIExpanded);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"collapsed\", !ctx.isFiltersExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFiltersExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"collapsed\", !ctx.isAIExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAIExpanded);\n          i0.ɵɵadvance(14);\n          i0.ɵɵclassProp(\"fullscreen\", ctx.isFullscreen);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  background-color: #f8f9fa;\\n  \\n\\n}\\n[_nghost-%COMP%]   *[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  height: 100vh;\\n  background-color: #f8f9fa;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 8px 16px;\\n  flex-shrink: 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.tabs-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  border-bottom: 2px solid transparent;\\n  transition: all 0.2s ease;\\n  height: 32px;\\n  border-radius: 4px 4px 0 0;\\n}\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: #f8f9fa;\\n}\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  border-bottom-color: #2196f3;\\n  background-color: #f8f9fa;\\n  font-weight: 600;\\n}\\n\\n.controls-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.toggle-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  height: 32px;\\n  padding: 0 8px;\\n  font-size: 12px;\\n  color: #666;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 4px;\\n}\\n.toggle-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #d0d0d0;\\n}\\n.toggle-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.toggle-btn[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n  border-radius: 8px;\\n  padding: 1px 4px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  min-width: 14px;\\n  text-align: center;\\n  margin-left: 2px;\\n}\\n.toggle-btn[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.toggle-btn[_ngcontent-%COMP%]   .expand-icon.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  min-width: 32px;\\n  padding: 0;\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n  border-radius: 4px;\\n}\\n.reset-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #d0d0d0;\\n}\\n.reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.apply-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  padding: 0 12px;\\n  background-color: #2196f3;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  border-radius: 4px;\\n}\\n.apply-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  margin-right: 4px;\\n}\\n\\n\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  flex-shrink: 0;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.filters-section.collapsed[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  border-bottom: none;\\n}\\n.filters-section[_ngcontent-%COMP%]:not(.collapsed) {\\n  max-height: 200px;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n  padding: 12px 16px;\\n  max-width: 1200px;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  color: #444;\\n  margin-bottom: 2px;\\n}\\n.filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  width: 12px;\\n  height: 12px;\\n  color: #666;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  max-height: 32px;\\n  overflow: hidden;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-datepicker-toggle {\\n  color: #666;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-icon-suffix {\\n  padding-left: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.checkbox-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 8px;\\n}\\n\\n.filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox {\\n  --mdc-checkbox-state-layer-size: 40px;\\n}\\n.filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  font-size: 14px;\\n  color: #333;\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px 20px;\\n}\\n\\n.ai-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 8px;\\n}\\n.ai-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.ai-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 13px;\\n  color: #666;\\n  line-height: 1.4;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  max-width: 700px;\\n  margin: 0 auto;\\n  align-items: flex-start;\\n}\\n\\n.ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  height: 40px;\\n  padding: 0 16px;\\n  background-color: #2196f3;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n}\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #e0e0e0;\\n  color: #999;\\n}\\n.send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1976d2;\\n}\\n.send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  padding: 16px 20px;\\n}\\n\\n.charts-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.charts-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n  min-height: 350px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 350px;\\n  padding: 32px 20px;\\n}\\n\\n.placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #ddd;\\n  margin-bottom: 16px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-top: 16px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e8e8e8;\\n  border-radius: 6px;\\n  padding: 12px 8px;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n  border-color: #ddd;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #ff9800;\\n  margin-bottom: 6px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.generated-charts[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  min-height: 350px;\\n  \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 12px;\\n  }\\n  .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 8px;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n  .tabs-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    -webkit-overflow-scrolling: touch;\\n    padding-bottom: 2px;\\n  }\\n  .tab-button[_ngcontent-%COMP%] {\\n    white-space: nowrap;\\n    min-width: 100px;\\n    font-size: 12px;\\n    padding: 6px 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .filters-section[_ngcontent-%COMP%], .ai-section[_ngcontent-%COMP%], .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .reset-btn[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 36px;\\n  }\\n  .charts-placeholder[_ngcontent-%COMP%] {\\n    min-height: 280px;\\n    padding: 20px 16px;\\n  }\\n}\\n\\n\\n.tab-button[_ngcontent-%COMP%]:focus, .send-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n.ai-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n\\n\\n.reset-btn[_ngcontent-%COMP%]:focus, .apply-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.loading[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.feature-card[_ngcontent-%COMP%], .charts-content[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SmartDashboardComponent_button_4_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "i_r7", "index", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onTabChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "tab_r6", "active", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtextInterpolate", "ctx_r1", "getActiveFiltersCount", "ɵɵproperty", "location_r14", "value", "baseDate_r15", "SmartDashboardComponent_div_28_Template_mat_select_valueChange_7_listener", "$event", "_r17", "ctx_r16", "selectedLocations", "ɵɵtemplate", "SmartDashboardComponent_div_28_mat_option_8_Template", "SmartDashboardComponent_div_28_Template_mat_select_valueChange_15_listener", "ctx_r18", "selectedBaseDate", "SmartDashboardComponent_div_28_mat_option_16_Template", "SmartDashboardComponent_div_28_Template_input_ngModelChange_23_listener", "ctx_r19", "startDate", "ɵɵelement", "SmartDashboardComponent_div_28_Template_input_ngModelChange_33_listener", "ctx_r20", "endDate", "ctx_r2", "locations", "baseDates", "_r12", "_r13", "SmartDashboardComponent_div_30_Template_input_ngModelChange_3_listener", "_r22", "ctx_r21", "chatMessage", "SmartDashboardComponent_div_30_Template_input_keydown_3_listener", "ctx_r23", "onKeyPress", "SmartDashboardComponent_div_30_Template_button_click_4_listener", "ctx_r24", "sendMessage", "ctx_r3", "trim", "SmartDashboardComponent", "constructor", "isFiltersOpen", "isFiltersExpanded", "isAIExpanded", "isFullscreen", "selectedTab", "hasGeneratedCharts", "checked", "tabs", "ngOnInit", "toggleFilters", "toggleAI", "toggleFullscreen", "refresh<PERSON><PERSON><PERSON>", "console", "log", "for<PERSON>ach", "tab", "i", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "length", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_button_4_Template", "SmartDashboardComponent_Template_button_click_6_listener", "SmartDashboardComponent_span_10_Template", "SmartDashboardComponent_Template_button_click_13_listener", "SmartDashboardComponent_Template_button_click_20_listener", "SmartDashboardComponent_Template_button_click_23_listener", "SmartDashboardComponent_div_28_Template", "SmartDashboardComponent_div_30_Template", "SmartDashboardComponent_Template_button_click_38_listener", "SmartDashboardComponent_Template_button_click_41_listener", "SmartDashboardComponent_div_45_Template", "SmartDashboardComponent_div_46_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatFormField", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  isFiltersExpanded = false;\n  isAIExpanded = false;\n  isFullscreen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersExpanded = !this.isFiltersExpanded;\n  }\n\n  toggleAI(): void {\n    this.isAIExpanded = !this.isAIExpanded;\n  }\n\n  toggleFullscreen(): void {\n    this.isFullscreen = !this.isFullscreen;\n  }\n\n  refreshCharts(): void {\n    // Refresh chart data\n    console.log('Refreshing charts...');\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Top Header with Tabs and Controls -->\n  <div class=\"dashboard-header\">\n    <!-- Report Type Tabs -->\n    <div class=\"tabs-section\">\n      <div class=\"tabs-container\">\n        <button\n          *ngFor=\"let tab of tabs; let i = index\"\n          class=\"tab-button\"\n          [class.active]=\"tab.active\"\n          (click)=\"onTabChange(i)\"\n        >\n          {{ tab.label }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Collapsible Controls -->\n    <div class=\"controls-section\">\n      <!-- Filters Toggle -->\n      <button mat-button class=\"toggle-btn\" (click)=\"toggleFilters()\">\n        <mat-icon>tune</mat-icon>\n        Filters\n        <span class=\"filter-count\" *ngIf=\"getActiveFiltersCount() > 0\">{{getActiveFiltersCount()}}</span>\n        <mat-icon class=\"expand-icon\" [class.expanded]=\"isFiltersExpanded\">expand_more</mat-icon>\n      </button>\n\n      <!-- AI Assistant Toggle -->\n      <button mat-button class=\"toggle-btn\" (click)=\"toggleAI()\">\n        <mat-icon>psychology</mat-icon>\n        AI Assistant\n        <mat-icon class=\"expand-icon\" [class.expanded]=\"isAIExpanded\">expand_more</mat-icon>\n      </button>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions\">\n        <button mat-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Collapsible Filters Section -->\n  <div class=\"filters-section\" [class.collapsed]=\"!isFiltersExpanded\">\n    <div class=\"filters-content\" *ngIf=\"isFiltersExpanded\">\n      <!-- 1. Location Multi-Select -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>location_on</mat-icon>\n          Restaurants\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select [(value)]=\"selectedLocations\" multiple>\n            <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n              {{ location.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- 2. Select Base Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>event</mat-icon>\n          Base Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select [(value)]=\"selectedBaseDate\">\n            <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n              {{ baseDate.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- 3. Start Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>date_range</mat-icon>\n          Start Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <input\n            matInput\n            [matDatepicker]=\"startPicker\"\n            [(ngModel)]=\"startDate\"\n            placeholder=\"Select start date\"\n            readonly\n          >\n          <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #startPicker></mat-datepicker>\n        </mat-form-field>\n      </div>\n\n      <!-- 4. End Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>date_range</mat-icon>\n          End Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <input\n            matInput\n            [matDatepicker]=\"endPicker\"\n            [(ngModel)]=\"endDate\"\n            placeholder=\"Select end date\"\n            readonly\n          >\n          <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #endPicker></mat-datepicker>\n        </mat-form-field>\n      </div>\n    </div>\n  </div>\n\n  <!-- Collapsible AI Assistant Section -->\n  <div class=\"ai-section\" [class.collapsed]=\"!isAIExpanded\">\n    <div class=\"ai-content\" *ngIf=\"isAIExpanded\">\n      <div class=\"ai-input-container\">\n        <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n          <input\n            matInput\n            type=\"text\"\n            placeholder=\"e.g., Show me sales trends for the last quarter by region\"\n            [(ngModel)]=\"chatMessage\"\n            (keydown)=\"onKeyPress($event)\"\n          >\n        </mat-form-field>\n        <button mat-raised-button color=\"primary\" class=\"send-btn\" (click)=\"sendMessage()\" [disabled]=\"!chatMessage.trim()\">\n          <mat-icon>send</mat-icon>\n          Generate\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Dashboard Charts Area (Primary Focus) -->\n  <div class=\"dashboard-charts\">\n    <div class=\"charts-header\">\n      <mat-icon class=\"charts-icon\">dashboard</mat-icon>\n      <h3>Dashboard</h3>\n      <div class=\"charts-actions\">\n        <button mat-icon-button class=\"fullscreen-btn\" (click)=\"toggleFullscreen()\">\n          <mat-icon>fullscreen</mat-icon>\n        </button>\n        <button mat-icon-button class=\"refresh-btn\" (click)=\"refreshCharts()\">\n          <mat-icon>refresh</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <!-- Charts Content - Main Focus Area -->\n    <div class=\"charts-content\" [class.fullscreen]=\"isFullscreen\">\n      <!-- Placeholder content when no charts are generated -->\n      <div class=\"charts-placeholder\" *ngIf=\"!hasGeneratedCharts\">\n        <div class=\"placeholder-content\">\n          <mat-icon class=\"placeholder-icon\">insights</mat-icon>\n          <h4>Ready to Generate Insights</h4>\n          <p>Configure your filters and ask the AI assistant to generate visualizations.</p>\n\n          <!-- Compact Feature Cards -->\n          <div class=\"feature-cards\">\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n              <span>Interactive Charts</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n              <span>Smart Insights</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">chat</mat-icon>\n              <span>Natural Language</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">schedule</mat-icon>\n              <span>Real-time Analysis</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Generated charts will appear here -->\n      <div class=\"generated-charts\" *ngIf=\"hasGeneratedCharts\">\n        <!-- Charts will be dynamically generated here -->\n        <div class=\"chart-grid\">\n          <!-- Sample chart placeholders -->\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 1</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 2</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 3</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 4</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICPpCC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAExBP,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAJPd,EAAA,CAAAe,WAAA,WAAAC,MAAA,CAAAC,MAAA,CAA2B;IAG3BjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACF;;;;;IAUApB,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAAlCd,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAqB,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA2B;;;;;IAmCtFvB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAC,YAAA,CAAAC,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAM,YAAA,CAAAL,KAAA,MACF;;;;;IAaApB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAG,YAAA,CAAAD,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAQ,YAAA,CAAAP,KAAA,MACF;;;;;;IA1BRpB,EAAA,CAAAC,cAAA,cAAuD;IAIvCD,EAAA,CAAAa,MAAA,kBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAChCd,EAAA,CAAAa,MAAA,oBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,yBAA0D;IAC5CD,EAAA,CAAAE,UAAA,yBAAA0B,0EAAAC,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAoB,OAAA,CAAAC,iBAAA,GAAAH,MAAA;IAAA,EAA6B;IACvC7B,EAAA,CAAAiC,UAAA,IAAAC,oDAAA,yBAEa;IACflC,EAAA,CAAAc,YAAA,EAAa;IAKjBd,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAa,MAAA,aAAK;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC1Bd,EAAA,CAAAa,MAAA,mBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAC5CD,EAAA,CAAAE,UAAA,yBAAAiC,2EAAAN,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAM,OAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAyB,OAAA,CAAAC,gBAAA,GAAAR,MAAA;IAAA,EAA4B;IACtC7B,EAAA,CAAAiC,UAAA,KAAAK,qDAAA,yBAEa;IACftC,EAAA,CAAAc,YAAA,EAAa;IAKjBd,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC/Bd,EAAA,CAAAa,MAAA,oBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAItDD,EAAA,CAAAE,UAAA,2BAAAqC,wEAAAV,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAU,OAAA,GAAAxC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA6B,OAAA,CAAAC,SAAA,GAAAZ,MAAA;IAAA,EAAuB;IAHzB7B,EAAA,CAAAc,YAAA,EAMC;IACDd,EAAA,CAAA0C,SAAA,iCAA6E;IAE/E1C,EAAA,CAAAc,YAAA,EAAiB;IAInBd,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC/Bd,EAAA,CAAAa,MAAA,kBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAItDD,EAAA,CAAAE,UAAA,2BAAAyC,wEAAAd,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAc,OAAA,GAAA5C,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiC,OAAA,CAAAC,OAAA,GAAAhB,MAAA;IAAA,EAAqB;IAHvB7B,EAAA,CAAAc,YAAA,EAMC;IACDd,EAAA,CAAA0C,SAAA,iCAA2E;IAE7E1C,EAAA,CAAAc,YAAA,EAAiB;;;;;;IA1DHd,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAwB,UAAA,UAAAsB,MAAA,CAAAd,iBAAA,CAA6B;IACNhC,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAwB,UAAA,YAAAsB,MAAA,CAAAC,SAAA,CAAY;IAcnC/C,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAwB,UAAA,UAAAsB,MAAA,CAAAT,gBAAA,CAA4B;IACLrC,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAwB,UAAA,YAAAsB,MAAA,CAAAE,SAAA,CAAY;IAgB7ChD,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAwB,UAAA,kBAAAyB,IAAA,CAA6B,YAAAH,MAAA,CAAAL,SAAA;IAKEzC,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAwB,UAAA,QAAAyB,IAAA,CAAmB;IAclDjD,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAwB,UAAA,kBAAA0B,IAAA,CAA2B,YAAAJ,MAAA,CAAAD,OAAA;IAKI7C,EAAA,CAAAkB,SAAA,GAAiB;IAAjBlB,EAAA,CAAAwB,UAAA,QAAA0B,IAAA,CAAiB;;;;;;IASxDlD,EAAA,CAAAC,cAAA,cAA6C;IAOrCD,EAAA,CAAAE,UAAA,2BAAAiD,uEAAAtB,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA0C,OAAA,CAAAC,WAAA,GAAAzB,MAAA;IAAA,EAAyB,qBAAA0B,iEAAA1B,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAI,OAAA,GAAAxD,EAAA,CAAAU,aAAA;MAAA,OACdV,EAAA,CAAAW,WAAA,CAAA6C,OAAA,CAAAC,UAAA,CAAA5B,MAAA,CAAkB;IAAA,EADJ;IAJ3B7B,EAAA,CAAAc,YAAA,EAMC;IAEHd,EAAA,CAAAC,cAAA,iBAAoH;IAAzDD,EAAA,CAAAE,UAAA,mBAAAwD,gEAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAO,OAAA,GAAA3D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAChF5D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACzBd,EAAA,CAAAa,MAAA,iBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAPLd,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAwB,UAAA,YAAAqC,MAAA,CAAAP,WAAA,CAAyB;IAIsDtD,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAwB,UAAA,cAAAqC,MAAA,CAAAP,WAAA,CAAAQ,IAAA,GAAgC;;;;;IA0BrH9D,EAAA,CAAAC,cAAA,cAA4D;IAErBD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACtDd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,iCAA0B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,kFAA2E;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAGlFd,EAAA,CAAAC,cAAA,cAA2B;IAEQD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACnDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAEjCd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACrDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAE7Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,YAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC9Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAE/Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IAOvCd,EAAA,CAAAC,cAAA,cAAyD;IAKpBD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,cAAwB;IACSD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,cAAwB;IACSD,EAAA,CAAAa,MAAA,eAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,eAAwB;IACSD,EAAA,CAAAa,MAAA,eAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;AD3LxD,MAqBaiD,uBAAuB;EAwClCC,YAAA;IAvCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAvB,SAAS,GAAG,CACV;MAAErB,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,wBAAwB;MAAEmD,OAAO,EAAE;IAAK,CAAE,EACzE;MAAE7C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,iBAAiB;MAAEmD,OAAO,EAAE;IAAK,CAAE,EAClE;MAAE7C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,aAAa;MAAEmD,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAE7C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,gBAAgB;MAAEmD,OAAO,EAAE;IAAK,CAAE,EACjE;MAAE7C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,oBAAoB;MAAEmD,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAvB,SAAS,GAAG,CACV;MAAEtB,KAAK,EAAE,OAAO;MAAEN,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,YAAY;MAAEN,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEM,KAAK,EAAE,QAAQ;MAAEN,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAY,iBAAiB,GAAa,EAAE;IAChC,KAAAK,gBAAgB,GAAG,OAAO;IAC1B,KAAAI,SAAS,GAAW,EAAE;IACtB,KAAAI,OAAO,GAAW,EAAE;IACpB,KAAAS,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAkB,IAAI,GAAG,CACL;MAAEpD,KAAK,EAAE,YAAY;MAAEH,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEG,KAAK,EAAE,iBAAiB;MAAEH,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEG,KAAK,EAAE,cAAc;MAAEH,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEG,KAAK,EAAE,kBAAkB;MAAEH,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBwD,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACR,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAS,QAAQA,CAAA;IACN,IAAI,CAACR,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAS,gBAAgBA,CAAA;IACd,IAAI,CAACR,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAS,aAAaA,CAAA;IACX;IACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC;EAEAnE,WAAWA,CAACJ,KAAa;IACvB,IAAI,CAAC6D,WAAW,GAAG7D,KAAK;IACxB,IAAI,CAACgE,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAAChE,MAAM,GAAGiE,CAAC,KAAK1E,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAoD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACN,WAAW,CAACQ,IAAI,EAAE,EAAE;MAC3B;MACAgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACzB,WAAW,CAAC;MAE9C;MACA,IAAI,CAACgB,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAAChB,WAAW,GAAG,EAAE;;EAEzB;EAEAG,UAAUA,CAAC0B,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAC1B,WAAW,EAAE;;EAEtB;EAEA2B,YAAYA,CAAA;IACV;IACAT,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACd,aAAa,GAAG,KAAK;EAC5B;EAEAuB,YAAYA,CAAA;IACV;IACA,IAAI,CAACxD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACK,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACI,SAAS,GAAG,EAAE;IACnB,IAAI,CAACI,OAAO,GAAG,EAAE;IACjB,IAAI,CAACE,SAAS,CAACiC,OAAO,CAACS,QAAQ,IAAIA,QAAQ,CAAClB,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEAhD,qBAAqBA,CAAA;IACnB,IAAImE,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,GAAG,CAAC,EAAED,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACrD,gBAAgB,KAAK,OAAO,EAAEqD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACjD,SAAS,EAAEiD,KAAK,EAAE;IAC3B,IAAI,IAAI,CAAC7C,OAAO,EAAE6C,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;;;uBA9GW3B,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAA6B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9F,EAAA,CAAA+F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCpCrG,EAAA,CAAAC,cAAA,aAAuC;UAM/BD,EAAA,CAAAiC,UAAA,IAAAsE,yCAAA,oBAOS;UACXvG,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,aAA8B;UAEUD,EAAA,CAAAE,UAAA,mBAAAsG,yDAAA;YAAA,OAASF,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAC7D1E,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,gBACA;UAAAb,EAAA,CAAAiC,UAAA,KAAAwE,wCAAA,kBAAiG;UACjGzG,EAAA,CAAAC,cAAA,mBAAmE;UAAAD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAI3Fd,EAAA,CAAAC,cAAA,iBAA2D;UAArBD,EAAA,CAAAE,UAAA,mBAAAwG,0DAAA;YAAA,OAASJ,GAAA,CAAA3B,QAAA,EAAU;UAAA,EAAC;UACxD3E,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAa,MAAA,sBACA;UAAAb,EAAA,CAAAC,cAAA,mBAA8D;UAAAD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAItFd,EAAA,CAAAC,cAAA,cAA2B;UACYD,EAAA,CAAAE,UAAA,mBAAAyG,0DAAA;YAAA,OAASL,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAC3DxF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAE9Bd,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAE,UAAA,mBAAA0G,0DAAA;YAAA,OAASN,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAClFvF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC1Bd,EAAA,CAAAa,MAAA,eACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAMfd,EAAA,CAAAC,cAAA,eAAoE;UAClED,EAAA,CAAAiC,UAAA,KAAA4E,uCAAA,oBAoEM;UACR7G,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,eAA0D;UACxDD,EAAA,CAAAiC,UAAA,KAAA6E,uCAAA,kBAgBM;UACR9G,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,eAA8B;UAEID,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAClDd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAClBd,EAAA,CAAAC,cAAA,eAA4B;UACqBD,EAAA,CAAAE,UAAA,mBAAA6G,0DAAA;YAAA,OAAST,GAAA,CAAA1B,gBAAA,EAAkB;UAAA,EAAC;UACzE5E,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAEjCd,EAAA,CAAAC,cAAA,kBAAsE;UAA1BD,EAAA,CAAAE,UAAA,mBAAA8G,0DAAA;YAAA,OAASV,GAAA,CAAAzB,aAAA,EAAe;UAAA,EAAC;UACnE7E,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAMlCd,EAAA,CAAAC,cAAA,eAA8D;UAE5DD,EAAA,CAAAiC,UAAA,KAAAgF,uCAAA,mBA0BM;UAGNjH,EAAA,CAAAiC,UAAA,KAAAiF,uCAAA,mBAiBM;UACRlH,EAAA,CAAAc,YAAA,EAAM;;;UAvMgBd,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAwB,UAAA,YAAA8E,GAAA,CAAA9B,IAAA,CAAS;UAgBCxE,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAwB,UAAA,SAAA8E,GAAA,CAAA/E,qBAAA,OAAiC;UAC/BvB,EAAA,CAAAkB,SAAA,GAAoC;UAApClB,EAAA,CAAAe,WAAA,aAAAuF,GAAA,CAAApC,iBAAA,CAAoC;UAOpClE,EAAA,CAAAkB,SAAA,GAA+B;UAA/BlB,EAAA,CAAAe,WAAA,aAAAuF,GAAA,CAAAnC,YAAA,CAA+B;UAiBtCnE,EAAA,CAAAkB,SAAA,IAAsC;UAAtClB,EAAA,CAAAe,WAAA,eAAAuF,GAAA,CAAApC,iBAAA,CAAsC;UACnClE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAwB,UAAA,SAAA8E,GAAA,CAAApC,iBAAA,CAAuB;UAwE/BlE,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAe,WAAA,eAAAuF,GAAA,CAAAnC,YAAA,CAAiC;UAC9BnE,EAAA,CAAAkB,SAAA,GAAkB;UAAlBlB,EAAA,CAAAwB,UAAA,SAAA8E,GAAA,CAAAnC,YAAA,CAAkB;UAmCfnE,EAAA,CAAAkB,SAAA,IAAiC;UAAjClB,EAAA,CAAAe,WAAA,eAAAuF,GAAA,CAAAlC,YAAA,CAAiC;UAE1BpE,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAAwB,UAAA,UAAA8E,GAAA,CAAAhC,kBAAA,CAAyB;UA6B3BtE,EAAA,CAAAkB,SAAA,GAAwB;UAAxBlB,EAAA,CAAAwB,UAAA,SAAA8E,GAAA,CAAAhC,kBAAA,CAAwB;;;qBDzKzDnF,YAAY,EAAAgI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjI,aAAa,EACbC,eAAe,EAAAiI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflI,aAAa,EAAAmI,EAAA,CAAAC,OAAA,EACbnI,kBAAkB,EAAAoI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBrI,eAAe,EAAAsI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfxI,cAAc,EAAAyI,EAAA,CAAAC,QAAA,EACdzI,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAAuI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBzI,mBAAmB,EACnBC,WAAW,EAAAyI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKF7E,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}