{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 61)(1, \"mat-icon\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getReportIcon(i_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r11.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r14.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_94_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_94_button_7_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const suggestion_r16 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.applySuggestion(suggestion_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r16, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Try asking:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 67);\n    i0.ɵɵtemplate(7, SmartDashboardComponent_div_94_button_7_Template, 2, 1, \"button\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getSuggestions());\n  }\n}\nfunction SmartDashboardComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"button\", 71)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 72)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"download\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 73)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"fullscreen\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SmartDashboardComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 76)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 77);\n    i0.ɵɵtext(6, \"Your Dashboard Awaits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 78);\n    i0.ɵɵtext(8, \" Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"div\", 80)(11, \"div\", 81)(12, \"mat-icon\", 82);\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 83)(15, \"h5\");\n    i0.ɵɵtext(16, \"Interactive Charts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Dynamic visualizations that respond to your queries\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 80)(20, \"div\", 81)(21, \"mat-icon\", 82);\n    i0.ɵɵtext(22, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 83)(24, \"h5\");\n    i0.ɵɵtext(25, \"Smart Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\");\n    i0.ɵɵtext(27, \"AI-powered analysis and recommendations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 80)(29, \"div\", 81)(30, \"mat-icon\", 82);\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 83)(33, \"h5\");\n    i0.ɵɵtext(34, \"Real-time Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\");\n    i0.ɵɵtext(36, \"Live updates and instant visualization\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 84);\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 109,\n      vars: 25,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"reports-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"appearance\", \"outline\", 1, \"report-dropdown\"], [\"placeholder\", \"Select Report Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [1, \"filter-group\", \"date-range-group\"], [1, \"date-range-container\"], [\"appearance\", \"outline\", 1, \"filter-field\", \"date-field\"], [\"matInput\", \"\", \"placeholder\", \"Start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [1, \"date-separator\"], [\"matInput\", \"\", \"placeholder\", \"End date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-header\"], [1, \"ai-title-section\"], [1, \"ai-icon\"], [1, \"ai-text\"], [1, \"ai-title\"], [1, \"ai-subtitle\"], [1, \"ai-status\"], [1, \"ai-input-wrapper\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matPrefix\", \"\", 1, \"input-prefix-icon\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me top performing restaurants this month, or Compare sales trends by location\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-fab\", \"\", \"color\", \"primary\", \"matTooltip\", \"Send message\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [\"class\", \"ai-suggestions\", 4, \"ngIf\"], [1, \"dashboard-section\"], [1, \"dashboard-header\"], [1, \"header-left\"], [1, \"dashboard-icon\"], [1, \"header-text\"], [1, \"dashboard-title\"], [1, \"dashboard-subtitle\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [1, \"dashboard-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"option-icon\"], [1, \"filter-badge\"], [1, \"selection-count\"], [1, \"ai-suggestions\"], [1, \"suggestions-header\"], [1, \"suggestion-chips\"], [\"mat-stroked-button\", \"\", \"class\", \"suggestion-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"suggestion-chip\", 3, \"click\"], [1, \"header-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh charts\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Export data\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Full screen\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"feature-showcase\"], [1, \"feature-item\"], [1, \"feature-icon-wrapper\"], [1, \"feature-icon\"], [1, \"feature-content\"], [1, \"charts-container\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"Reports\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-form-field\", 7)(10, \"mat-select\", 8);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_10_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_10_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(11, SmartDashboardComponent_mat_option_11_Template, 4, 3, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 3)(14, \"div\", 4)(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"h3\", 6);\n          i0.ɵɵtext(18, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, SmartDashboardComponent_span_19_Template, 2, 1, \"span\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 13)(22, \"label\", 14)(23, \"mat-icon\", 15);\n          i0.ɵɵtext(24, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 16);\n          i0.ɵɵtext(26, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, SmartDashboardComponent_span_27_Template, 2, 1, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-form-field\", 18)(29, \"mat-select\", 19);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_29_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(30, SmartDashboardComponent_mat_option_30_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 14)(33, \"mat-icon\", 15);\n          i0.ɵɵtext(34, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 16);\n          i0.ɵɵtext(36, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 18)(38, \"mat-select\", 20);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_38_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 21)(41, \"label\", 14)(42, \"mat-icon\", 15);\n          i0.ɵɵtext(43, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 16);\n          i0.ɵɵtext(45, \"Date Range\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 22)(47, \"mat-form-field\", 23)(48, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_48_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"mat-datepicker-toggle\", 25)(50, \"mat-datepicker\", null, 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"span\", 27);\n          i0.ɵɵtext(53, \"to\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-form-field\", 23)(55, \"input\", 28);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_55_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"mat-datepicker-toggle\", 25)(57, \"mat-datepicker\", null, 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 30)(60, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_60_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_64_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(65, \"mat-icon\");\n          i0.ɵɵtext(66, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(68, \"div\", 33)(69, \"div\", 34)(70, \"div\", 35)(71, \"div\", 36)(72, \"div\", 37)(73, \"mat-icon\", 38);\n          i0.ɵɵtext(74, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"h3\", 40);\n          i0.ɵɵtext(77, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"p\", 41);\n          i0.ɵɵtext(79, \"Ask questions about your data in natural language\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"div\", 42)(81, \"mat-icon\");\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\");\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(85, \"div\", 43)(86, \"div\", 44)(87, \"mat-form-field\", 45)(88, \"mat-icon\", 46);\n          i0.ɵɵtext(89, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"input\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_90_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_90_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_91_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(92, \"mat-icon\");\n          i0.ɵɵtext(93, \"send\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(94, SmartDashboardComponent_div_94_Template, 8, 1, \"div\", 49);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(95, \"div\", 50)(96, \"div\", 51)(97, \"div\", 52)(98, \"mat-icon\", 53);\n          i0.ɵɵtext(99, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"div\", 54)(101, \"h3\", 55);\n          i0.ɵɵtext(102, \"Smart Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"p\", 56);\n          i0.ɵɵtext(104, \"AI-powered data visualization\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(105, SmartDashboardComponent_div_105_Template, 10, 0, \"div\", 57);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"div\", 58);\n          i0.ɵɵtemplate(107, SmartDashboardComponent_div_107_Template, 37, 0, \"div\", 59);\n          i0.ɵɵtemplate(108, SmartDashboardComponent_div_108_Template, 1, 0, \"div\", 60);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(51);\n          const _r6 = i0.ɵɵreference(58);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(24);\n          i0.ɵɵclassProp(\"active\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount() > 0 ? \"check_circle\" : \"info\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0 && !ctx.chatMessage.trim());\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, i2.MatFabButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatPrefix, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 89vh;\\n  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);\\n  animation: _ngcontent-%COMP%_backgroundFloat 20s ease-in-out infinite;\\n  pointer-events: none;\\n}\\n.smart-dashboard-container.dark-mode[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #0f172a 0%, #111827 100%);\\n  color: #f1f5f9;\\n}\\n.smart-dashboard-container.dark-mode[_ngcontent-%COMP%]::before {\\n  background: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.2) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);\\n}\\n\\n@keyframes _ngcontent-%COMP%_backgroundFloat {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-20px);\\n  }\\n}\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-right: 1px solid rgba(209, 213, 219, 0.3);\\n  position: relative;\\n  z-index: 10;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(51, 65, 85, 0.8);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(71, 85, 105, 0.3);\\n  border-right-color: rgba(71, 85, 105, 0.5);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-reports[_ngcontent-%COMP%]   .reports-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-reports[_ngcontent-%COMP%]   .reports-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6366f1;\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-reports[_ngcontent-%COMP%]   .reports-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-reports[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-reports[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-select:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #10b981;\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #10b981, #06d6a0);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f59e0b, #fbbf24);\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(99, 102, 241, 0.3);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 4px;\\n  font-weight: 500;\\n  color: #4b5563;\\n  font-size: 0.75rem;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  color: #cbd5e1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  width: 0.8rem;\\n  height: 0.8rem;\\n  color: #9ca3af;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field {\\n  width: 100%;\\n  height: 36px !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #ffffff;\\n  border-radius: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  height: 36px !important;\\n  min-height: 36px !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transform: translateY(-1px);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #334155;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  display: flex;\\n  gap: 0.5rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem 0.5rem 0.75rem;\\n  border-top: 1px solid rgba(209, 213, 219, 0.5);\\n  z-index: 10;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%] {\\n  border-top-color: rgba(71, 85, 105, 0.5);\\n  background: rgba(51, 65, 85, 0.95);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 32px;\\n  border-radius: 0.5rem;\\n  color: #4b5563;\\n  border: 1px solid rgba(209, 213, 219, 0.5);\\n  transition: all 0.3s ease-out;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(243, 244, 246, 0.8);\\n  transform: translateY(-1px);\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  color: #cbd5e1;\\n  border-color: rgba(71, 85, 105, 0.5);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(51, 65, 85, 0.8);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 32px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, #6366f1, #4f46e5);\\n  font-weight: 600;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  font-size: 0.8rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-filters[_ngcontent-%COMP%]   .filters-actions-sticky[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #4f46e5, #6366f1);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0.75rem;\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: rgba(51, 65, 85, 0.9);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(71, 85, 105, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #6366f1, #10b981, #f59e0b);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: #6366f1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-subtitle[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.8rem;\\n  margin-left: auto;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-header[_ngcontent-%COMP%]   .ai-subtitle[_ngcontent-%COMP%] {\\n  color: #cbd5e1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #1e293b;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field input {\\n  font-size: 0.925rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field input::placeholder {\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #6366f1, #4f46e5);\\n  color: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%] {\\n  background: rgba(51, 65, 85, 0.9);\\n  -webkit-backdrop-filter: blur(16px);\\n          backdrop-filter: blur(16px);\\n  border: 1px solid rgba(71, 85, 105, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem 1rem 0.75rem;\\n  border-bottom: 1px solid rgba(209, 213, 219, 0.3);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(71, 85, 105, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: #f59e0b;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #1f2937, #4b5563);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f1f5f9, #cbd5e1);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  padding: 0.5rem 1rem 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(99, 102, 241, 0.3);\\n  border-radius: 2px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(99, 102, 241, 0.5);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.75rem 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: #d1d5db;\\n  margin-bottom: 0.5rem;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  color: #475569;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin: 0 0 0.25rem 0;\\n  color: #374151;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #f1f5f9;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  margin: 0 0 0.75rem 0;\\n  line-height: 1.4;\\n  font-size: 0.8rem;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #cbd5e1;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 0.5rem;\\n  margin-top: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  padding: 0.5rem;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n  border: 1px solid rgba(229, 231, 235, 0.5);\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: #334155;\\n  border-color: rgba(71, 85, 105, 0.5);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  color: #6366f1;\\n  margin-bottom: 2px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.dark-mode[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .charts-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #f1f5f9;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .generated-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0%, 100% {\\n    transform: rotate(0deg) scale(1);\\n  }\\n  25% {\\n    transform: rotate(5deg) scale(1.1);\\n  }\\n  75% {\\n    transform: rotate(-5deg) scale(1.1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-height: auto;\\n    padding: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n    gap: 0.75rem;\\n  }\\n  .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .dashboard-charts[_ngcontent-%COMP%]   .charts-content[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n  .left-sidebar .mat-mdc-form-field {\\n  height: 36px !important;\\n  width: 100% !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  border-radius: 0.5rem !important;\\n  height: 36px !important;\\n  min-height: 36px !important;\\n  border: 1px solid rgba(209, 213, 219, 0.6) !important;\\n  background: #ffffff !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6366f1 !important;\\n  font-size: 18px !important;\\n}\\n  .left-sidebar .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #4f46e5 !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .left-sidebar .mat-mdc-form-field .mat-mdc-select-value,   .left-sidebar .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.8rem !important;\\n  line-height: 20px !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.5rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 32px !important;\\n  line-height: 32px !important;\\n  padding: 0 12px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(99, 102, 241, 0.1) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(99, 102, 241, 0.15) !important;\\n}\\n  .mat-mdc-button {\\n  border-radius: 0.5rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.8rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r12", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getReportIcon", "ɵɵtextInterpolate1", "tab_r11", "label", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r13", "value", "baseDate_r14", "ɵɵlistener", "SmartDashboardComponent_div_94_button_7_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r18", "suggestion_r16", "$implicit", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "applySuggestion", "ɵɵtemplate", "SmartDashboardComponent_div_94_button_7_Template", "ctx_r7", "getSuggestions", "ɵɵelement", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "checked", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "toggleFilters", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "console", "log", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_10_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_10_listener", "SmartDashboardComponent_mat_option_11_Template", "SmartDashboardComponent_span_19_Template", "SmartDashboardComponent_span_27_Template", "SmartDashboardComponent_Template_mat_select_valueChange_29_listener", "SmartDashboardComponent_mat_option_30_Template", "SmartDashboardComponent_Template_mat_select_valueChange_38_listener", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_input_ngModelChange_48_listener", "SmartDashboardComponent_Template_input_ngModelChange_55_listener", "SmartDashboardComponent_Template_button_click_60_listener", "SmartDashboardComponent_Template_button_click_64_listener", "SmartDashboardComponent_Template_input_ngModelChange_90_listener", "SmartDashboardComponent_Template_input_keydown_90_listener", "SmartDashboardComponent_Template_button_click_91_listener", "SmartDashboardComponent_div_94_Template", "SmartDashboardComponent_div_105_Template", "SmartDashboardComponent_div_107_Template", "SmartDashboardComponent_div_108_Template", "_r5", "_r6", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "Mat<PERSON>ab<PERSON><PERSON><PERSON>", "i3", "MatIcon", "i4", "MatFormField", "MatPrefix", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Selection -->\n    <div class=\"sidebar-section reports-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">assessment</mat-icon>\n          <h3 class=\"section-title\">Reports</h3>\n        </div>\n      </div>\n      <mat-form-field appearance=\"outline\" class=\"report-dropdown\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Select Report Type\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <mat-icon class=\"option-icon\">{{ getReportIcon(i) }}</mat-icon>\n            {{ tab.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Date Range -->\n        <div class=\"filter-group date-range-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">date_range</mat-icon>\n            <span class=\"label-text\">Date Range</span>\n          </label>\n          <div class=\"date-range-container\">\n            <mat-form-field appearance=\"outline\" class=\"filter-field date-field\">\n              <input\n                matInput\n                [matDatepicker]=\"startPicker\"\n                [(ngModel)]=\"startDate\"\n                placeholder=\"Start date\"\n                readonly\n              >\n              <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n              <mat-datepicker #startPicker></mat-datepicker>\n            </mat-form-field>\n            <span class=\"date-separator\">to</span>\n            <mat-form-field appearance=\"outline\" class=\"filter-field date-field\">\n              <input\n                matInput\n                [matDatepicker]=\"endPicker\"\n                [(ngModel)]=\"endDate\"\n                placeholder=\"End date\"\n                readonly\n              >\n              <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n              <mat-datepicker #endPicker></mat-datepicker>\n            </mat-form-field>\n          </div>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Assistant Section -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-header\">\n          <div class=\"ai-title-section\">\n            <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n            <div class=\"ai-text\">\n              <h3 class=\"ai-title\">Smart Dashboard Assistant</h3>\n              <p class=\"ai-subtitle\">Ask questions about your data in natural language</p>\n            </div>\n          </div>\n          <div class=\"ai-status\" [class.active]=\"getActiveFiltersCount() > 0\">\n            <mat-icon>{{ getActiveFiltersCount() > 0 ? 'check_circle' : 'info' }}</mat-icon>\n            <span>{{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}</span>\n          </div>\n        </div>\n\n        <div class=\"ai-input-wrapper\">\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <mat-icon matPrefix class=\"input-prefix-icon\">chat</mat-icon>\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"e.g., Show me top performing restaurants this month, or Compare sales trends by location\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-fab\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n              matTooltip=\"Send message\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n\n          <div class=\"ai-suggestions\" *ngIf=\"getActiveFiltersCount() > 0 && !chatMessage.trim()\">\n            <div class=\"suggestions-header\">\n              <mat-icon>lightbulb</mat-icon>\n              <span>Try asking:</span>\n            </div>\n            <div class=\"suggestion-chips\">\n              <button\n                mat-stroked-button\n                class=\"suggestion-chip\"\n                *ngFor=\"let suggestion of getSuggestions()\"\n                (click)=\"applySuggestion(suggestion)\"\n              >\n                {{ suggestion }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-header\">\n        <div class=\"header-left\">\n          <mat-icon class=\"dashboard-icon\">dashboard</mat-icon>\n          <div class=\"header-text\">\n            <h3 class=\"dashboard-title\">Smart Dashboard</h3>\n            <p class=\"dashboard-subtitle\">AI-powered data visualization</p>\n          </div>\n        </div>\n        <div class=\"header-actions\" *ngIf=\"hasGeneratedCharts\">\n          <button mat-icon-button matTooltip=\"Refresh charts\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Export data\">\n            <mat-icon>download</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Full screen\">\n            <mat-icon>fullscreen</mat-icon>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!hasGeneratedCharts\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Your Dashboard Awaits</h4>\n            <p class=\"empty-state-description\">\n              Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data.\n            </p>\n\n            <div class=\"feature-showcase\">\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Interactive Charts</h5>\n                  <p>Dynamic visualizations that respond to your queries</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Smart Insights</h5>\n                  <p>AI-powered analysis and recommendations</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">speed</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Real-time Data</h5>\n                  <p>Live updates and instant visualization</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"hasGeneratedCharts\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAE5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;ICDlCC,EAAA,CAAAC,cAAA,qBAAgE;IAChCD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IAHuCH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAC/BL,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,KAAA,EAAsB;IACpDL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,OAAA,CAAAC,KAAA,MACF;;;;;IAWAZ,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAG,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,OAAAK,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEjB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAC,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAQ,YAAA,CAAAN,KAAA,MACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAgB,YAAA,CAAAD,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAU,YAAA,CAAAR,KAAA,MACF;;;;;;IAuGAZ,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAqB,UAAA,mBAAAC,yEAAA;MAAA,MAAAC,WAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,IAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAF,OAAA,CAAAG,eAAA,CAAAL,cAAA,CAA2B;IAAA,EAAC;IAErC1B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAgB,cAAA,MACF;;;;;IAbJ1B,EAAA,CAAAC,cAAA,cAAuF;IAEzED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1BH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAgC,UAAA,IAAAC,gDAAA,qBAOS;IACXjC,EAAA,CAAAG,YAAA,EAAM;;;;IALqBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAA8B,MAAA,CAAAC,cAAA,GAAmB;;;;;IAqBlDnC,EAAA,CAAAC,cAAA,cAAuD;IAEzCD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE9BH,EAAA,CAAAC,cAAA,iBAAiD;IACrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,iBAAiD;IACrCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAOnCH,EAAA,CAAAC,cAAA,cAAqD;IAGrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,qHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,cAA8B;IAGOD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAErDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,2DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9DH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEvDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIlDH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQvDH,EAAA,CAAAoC,SAAA,cAEM;;;AD3Od,MAqBaC,uBAAuB;EAqClCC,YAAA;IApCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAEvB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,wBAAwB;MAAE+B,OAAO,EAAE;IAAK,CAAE,EACzE;MAAExB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,iBAAiB;MAAE+B,OAAO,EAAE;IAAK,CAAE,EAClE;MAAExB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,aAAa;MAAE+B,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAExB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,gBAAgB;MAAE+B,OAAO,EAAE;IAAK,CAAE,EACjE;MAAExB,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,oBAAoB;MAAE+B,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAC,SAAS,GAAG,CACV;MAAEzB,KAAK,EAAE,OAAO;MAAEP,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,YAAY;MAAEP,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEO,KAAK,EAAE,QAAQ;MAAEP,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAI,iBAAiB,GAAa,EAAE;IAChC,KAAA6B,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAErC,KAAK,EAAE,YAAY;MAAEsC,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEtC,KAAK,EAAE,iBAAiB;MAAEsC,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEtC,KAAK,EAAE,cAAc;MAAEsC,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEtC,KAAK,EAAE,kBAAkB;MAAEsC,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBC,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACb,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAc,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACd,WAAW,GAAGc,KAAK;IACxB,IAAI,CAACL,IAAI,CAACM,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACN,MAAM,GAAGO,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF;IACAI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACV,IAAI,CAACK,KAAK,CAAC,CAAC1C,KAAK,CAAC;EACzD;EAEAgD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;MAC3B;MACAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACX,WAAW,CAAC;MAE9C;MACA,IAAI,CAACP,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACO,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;IACAT,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACpB,aAAa,GAAG,KAAK;EAC5B;EAEA6B,YAAYA,CAAA;IACV;IACA,IAAI,CAACpD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC6B,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACL,SAAS,CAACa,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAAC1B,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEA7B,qBAAqBA,CAAA;IACnB,IAAIwD,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACtD,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAEqD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACzB,gBAAgB,KAAK,OAAO,EAAEyB,KAAK,EAAE;IAC9C,IAAI,IAAI,CAACxB,SAAS,EAAEwB,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACvB,OAAO,EAAEuB,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;;;uBAhGWjC,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAkC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzE,EAAA,CAAA0E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCpChF,EAAA,CAAAC,cAAA,aAAuC;UAOED,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAAA,CAAAC,cAAA,wBAA6D;UAC/CD,EAAA,CAAAqB,UAAA,yBAAA6D,oEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAzC,WAAA,GAAA2C,MAAA;UAAA,EAAuB,6BAAAC,wEAAAD,MAAA;YAAA,OAAoBF,GAAA,CAAA5B,WAAA,CAAA8B,MAAA,CAAAhE,KAAA,CAAyB;UAAA,EAA7C;UACjCnB,EAAA,CAAAgC,UAAA,KAAAqD,8CAAA,wBAGa;UACfrF,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAgC,UAAA,KAAAsD,wCAAA,mBAEO;UACTtF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAgC,UAAA,KAAAuD,wCAAA,mBAEO;UACTvF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAqB,UAAA,yBAAAmE,oEAAAL,MAAA;YAAA,OAAAF,GAAA,CAAAjE,iBAAA,GAAAmE,MAAA;UAAA,EAA6B;UACvCnF,EAAA,CAAAgC,UAAA,KAAAyD,8CAAA,wBAEa;UACfzF,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAqB,UAAA,yBAAAqE,oEAAAP,MAAA;YAAA,OAAAF,GAAA,CAAApC,gBAAA,GAAAsC,MAAA;UAAA,EAA4B;UACtCnF,EAAA,CAAAgC,UAAA,KAAA2D,8CAAA,wBAEa;UACf3F,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA2C;UAEVD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,eAAkC;UAK5BD,EAAA,CAAAqB,UAAA,2BAAAuE,iEAAAT,MAAA;YAAA,OAAAF,GAAA,CAAAnC,SAAA,GAAAqC,MAAA;UAAA,EAAuB;UAHzBnF,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAoC,SAAA,iCAA6E;UAE/EpC,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,0BAAqE;UAIjED,EAAA,CAAAqB,UAAA,2BAAAwE,iEAAAV,MAAA;YAAA,OAAAF,GAAA,CAAAlC,OAAA,GAAAoC,MAAA;UAAA,EAAqB;UAHvBnF,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAoC,SAAA,iCAA2E;UAE7EpC,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAAqB,UAAA,mBAAAyE,0DAAA;YAAA,OAASb,GAAA,CAAAb,YAAA,EAAc;UAAA,EAAC;UACnEpE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAqB,UAAA,mBAAA0E,0DAAA;YAAA,OAASd,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAClFnE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,eAAqB;UACED,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAE,MAAA,yDAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGhFH,EAAA,CAAAC,cAAA,eAAoE;UACxDD,EAAA,CAAAE,MAAA,IAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAkF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAInGH,EAAA,CAAAC,cAAA,eAA8B;UAGsBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7DH,EAAA,CAAAC,cAAA,iBAOC;UAHCD,EAAA,CAAAqB,UAAA,2BAAA2E,iEAAAb,MAAA;YAAA,OAAAF,GAAA,CAAAjC,WAAA,GAAAmC,MAAA;UAAA,EAAyB,qBAAAc,2DAAAd,MAAA;YAAA,OACdF,GAAA,CAAAnB,UAAA,CAAAqB,MAAA,CAAkB;UAAA,EADJ;UAJ3BnF,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAqB,UAAA,mBAAA6E,0DAAA;YAAA,OAASjB,GAAA,CAAArB,WAAA,EAAa;UAAA,EAAC;UAIvB5D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI7BH,EAAA,CAAAgC,UAAA,KAAAmE,uCAAA,kBAeM;UACRnG,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA+B;UAGQD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,gBAAyB;UACKD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGnEH,EAAA,CAAAgC,UAAA,MAAAoE,wCAAA,mBAUM;UACRpG,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA+B;UAE7BD,EAAA,CAAAgC,UAAA,MAAAqE,wCAAA,mBA0CM;UAGNrG,EAAA,CAAAgC,UAAA,MAAAsE,wCAAA,kBAEM;UACRtG,EAAA,CAAAG,YAAA,EAAM;;;;;UAhPQH,EAAA,CAAAM,SAAA,IAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAA6E,GAAA,CAAAzC,WAAA,CAAuB;UACLxC,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAA6E,GAAA,CAAAhC,IAAA,CAAS;UAcTjD,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAnE,qBAAA,OAAiC;UAY5Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAjE,iBAAA,CAAAC,MAAA,KAAkC;UAKrDjB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAA6E,GAAA,CAAAjE,iBAAA,CAA6B;UACNhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA6E,GAAA,CAAAvC,SAAA,CAAY;UAcnC1C,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAA6E,GAAA,CAAApC,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA6E,GAAA,CAAArC,SAAA,CAAY;UAiB3C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAmG,GAAA,CAA6B,YAAAtB,GAAA,CAAAnC,SAAA;UAKE9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAmG,GAAA,CAAmB;UAOlDvG,EAAA,CAAAM,SAAA,GAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAoG,GAAA,CAA2B,YAAAvB,GAAA,CAAAlC,OAAA;UAKI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAoG,GAAA,CAAiB;UAkC/BxG,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAyG,WAAA,WAAAxB,GAAA,CAAAnE,qBAAA,OAA4C;UACvDd,EAAA,CAAAM,SAAA,GAA2D;UAA3DN,EAAA,CAAAO,iBAAA,CAAA0E,GAAA,CAAAnE,qBAAA,iCAA2D;UAC/Dd,EAAA,CAAAM,SAAA,GAAkF;UAAlFN,EAAA,CAAAO,iBAAA,CAAA0E,GAAA,CAAAnE,qBAAA,wDAAkF;UAYpFd,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA6E,GAAA,CAAAjC,WAAA,CAAyB,aAAAiC,GAAA,CAAAnE,qBAAA;UAU3Bd,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAA6E,GAAA,CAAAjC,WAAA,CAAAa,IAAA,MAAAoB,GAAA,CAAAnE,qBAAA,SAAiE;UAOxCd,EAAA,CAAAM,SAAA,GAAwD;UAAxDN,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAnE,qBAAA,WAAAmE,GAAA,CAAAjC,WAAA,CAAAa,IAAA,GAAwD;UA8B1D7D,EAAA,CAAAM,SAAA,IAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAxC,kBAAA,CAAwB;UAe3BzC,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,UAAA6E,GAAA,CAAAxC,kBAAA,CAAyB;UA6CpBzC,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAxC,kBAAA,CAAwB;;;qBDrO3DtD,YAAY,EAAAuH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxH,aAAa,EACbC,eAAe,EAAAwH,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,YAAA,EACf1H,aAAa,EAAA2H,EAAA,CAAAC,OAAA,EACb3H,kBAAkB,EAAA4H,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAAAF,EAAA,CAAAG,SAAA,EAClB9H,eAAe,EAAA+H,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfjI,cAAc,EAAAkI,EAAA,CAAAC,QAAA,EACdlI,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAAgI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBlI,mBAAmB,EACnBC,WAAW,EAAAkI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFhG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}