{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/checkbox\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const i_r8 = restoredCtx.index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onTabChange(i_r8));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r7 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tab_r7.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r7.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", period_r11.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_mat_checkbox_35_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const category_r12 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(category_r12.checked = $event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngModel\", category_r12.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_mat_checkbox_42_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const region_r15 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(region_r15.checked = $event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngModel\", region_r15.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r15.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_mat_checkbox_49_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const metric_r18 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(metric_r18.checked = $event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const metric_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngModel\", metric_r18.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", metric_r18.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"mat-icon\", 36);\n    i0.ɵɵtext(3, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Ready to Generate Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Configure your filters above and ask the AI assistant to generate visualizations for your data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38)(10, \"mat-icon\", 39);\n    i0.ɵɵtext(11, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h5\");\n    i0.ɵɵtext(13, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 38)(15, \"mat-icon\", 39);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h5\");\n    i0.ɵɵtext(18, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"mat-icon\", 39);\n    i0.ɵɵtext(21, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h5\");\n    i0.ɵɵtext(23, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 38)(25, \"mat-icon\", 39);\n    i0.ɵɵtext(26, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"h5\");\n    i0.ɵɵtext(28, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 40);\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 74,\n      vars: 10,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"report-tabs\"], [1, \"tabs-container\"], [\"class\", \"tab-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"filters-section\"], [1, \"filters-header\"], [1, \"filters-title\"], [1, \"filters-actions\"], [\"mat-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"checkbox-grid\"], [\"class\", \"filter-checkbox\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [1, \"ai-section\"], [1, \"ai-header\"], [1, \"ai-icon\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me sales trends for the last quarter by region\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-charts\"], [1, \"charts-header\"], [1, \"charts-icon\"], [1, \"charts-content\"], [\"class\", \"charts-placeholder\", 4, \"ngIf\"], [\"class\", \"generated-charts\", 4, \"ngIf\"], [1, \"tab-button\", 3, \"click\"], [3, \"value\"], [1, \"filter-checkbox\", 3, \"ngModel\", \"ngModelChange\"], [1, \"charts-placeholder\"], [1, \"placeholder-content\"], [1, \"placeholder-icon\"], [1, \"feature-cards\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"generated-charts\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, SmartDashboardComponent_button_3_Template, 2, 3, \"button\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"mat-icon\");\n          i0.ɵɵtext(8, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"h3\");\n          i0.ɵɵtext(10, \"Smart Filters\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_12_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_16_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" Apply Filters \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11)(22, \"label\", 12)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25, \" Time Period \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 13)(27, \"mat-select\", 14);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_27_listener($event) {\n            return ctx.selectedTimePeriod = $event;\n          });\n          i0.ɵɵtemplate(28, SmartDashboardComponent_mat_option_28_Template, 2, 2, \"mat-option\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 11)(30, \"label\", 12)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" Categories \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 16);\n          i0.ɵɵtemplate(35, SmartDashboardComponent_mat_checkbox_35_Template, 2, 2, \"mat-checkbox\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 11)(37, \"label\", 12)(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"public\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40, \" Regions \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 16);\n          i0.ɵɵtemplate(42, SmartDashboardComponent_mat_checkbox_42_Template, 2, 2, \"mat-checkbox\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 11)(44, \"label\", 12)(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" Key Metrics \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 16);\n          i0.ɵɵtemplate(49, SmartDashboardComponent_mat_checkbox_49_Template, 2, 2, \"mat-checkbox\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 18)(51, \"div\", 19)(52, \"mat-icon\", 20);\n          i0.ɵɵtext(53, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"h3\");\n          i0.ɵɵtext(55, \"Ask AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"Ask questions about your data or request specific visualizations\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 21)(59, \"mat-form-field\", 22)(60, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_60_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_60_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_61_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"send\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Generate \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 25)(66, \"div\", 26)(67, \"mat-icon\", 27);\n          i0.ɵɵtext(68, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"h3\");\n          i0.ɵɵtext(70, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 28);\n          i0.ɵɵtemplate(72, SmartDashboardComponent_div_72_Template, 29, 0, \"div\", 29);\n          i0.ɵɵtemplate(73, SmartDashboardComponent_div_73_Template, 1, 0, \"div\", 30);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"value\", ctx.selectedTimePeriod);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.timePeriods);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.regions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.keyMetrics);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim());\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, i8.MatCheckbox, MatTabsModule, MatSidenavModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  background-color: #f8f9fa;\\n  \\n\\n}\\n[_nghost-%COMP%]   *[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  min-height: 100vh;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.report-tabs[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 0 24px;\\n  flex-shrink: 0;\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  border-bottom: 3px solid transparent;\\n  transition: all 0.2s ease;\\n  height: 48px;\\n}\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: #f5f5f5;\\n}\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  border-bottom-color: #2196f3;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 20px 24px;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.filters-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  height: 36px;\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n}\\n.reset-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.apply-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  height: 36px;\\n  background-color: #2196f3;\\n  color: white;\\n}\\n.apply-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 24px;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #666;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n}\\n\\n.checkbox-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 8px;\\n}\\n\\n.filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox {\\n  --mdc-checkbox-state-layer-size: 40px;\\n}\\n.filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  font-size: 14px;\\n  color: #333;\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 20px 24px;\\n}\\n\\n.ai-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  margin-bottom: 12px;\\n}\\n.ai-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.ai-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n  align-items: flex-start;\\n}\\n\\n.ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 48px;\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  padding: 0 24px;\\n  background-color: #2196f3;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #e0e0e0;\\n  color: #999;\\n}\\n.send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1976d2;\\n}\\n\\n\\n\\n.dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  padding: 24px;\\n}\\n\\n.charts-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 24px;\\n}\\n.charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n.charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.charts-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid #e0e0e0;\\n  min-height: 400px;\\n  overflow: hidden;\\n}\\n\\n.charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 400px;\\n  padding: 40px 20px;\\n}\\n\\n.placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 600px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  font-size: 16px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 20px 12px;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  color: #ff9800;\\n  margin-bottom: 8px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.generated-charts[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  min-height: 400px;\\n  \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .checkbox-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 16px;\\n  }\\n  .tabs-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .tab-button[_ngcontent-%COMP%] {\\n    white-space: nowrap;\\n    min-width: 120px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .filters-section[_ngcontent-%COMP%], .ai-section[_ngcontent-%COMP%], .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .reset-btn[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n\\n.tab-button[_ngcontent-%COMP%]:focus, .send-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n.ai-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n\\n\\n.reset-btn[_ngcontent-%COMP%]:focus, .apply-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.loading[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.feature-card[_ngcontent-%COMP%], .charts-content[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SmartDashboardComponent_button_3_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "i_r8", "index", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onTabChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "tab_r7", "active", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵproperty", "period_r11", "value", "SmartDashboardComponent_mat_checkbox_35_Template_mat_checkbox_ngModelChange_0_listener", "$event", "_r14", "category_r12", "$implicit", "checked", "SmartDashboardComponent_mat_checkbox_42_Template_mat_checkbox_ngModelChange_0_listener", "_r17", "region_r15", "SmartDashboardComponent_mat_checkbox_49_Template_mat_checkbox_ngModelChange_0_listener", "_r20", "metric_r18", "ɵɵelement", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "baseDates", "selectedLocations", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "ngOnInit", "toggleFilters", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "console", "log", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "SmartDashboardComponent_button_3_Template", "SmartDashboardComponent_Template_button_click_12_listener", "SmartDashboardComponent_Template_button_click_16_listener", "SmartDashboardComponent_Template_mat_select_valueChange_27_listener", "selectedTimePeriod", "SmartDashboardComponent_mat_option_28_Template", "SmartDashboardComponent_mat_checkbox_35_Template", "SmartDashboardComponent_mat_checkbox_42_Template", "SmartDashboardComponent_mat_checkbox_49_Template", "SmartDashboardComponent_Template_input_ngModelChange_60_listener", "SmartDashboardComponent_Template_input_keydown_60_listener", "SmartDashboardComponent_Template_button_click_61_listener", "SmartDashboardComponent_div_72_Template", "SmartDashboardComponent_div_73_Template", "timePeriods", "categories", "regions", "keyMetrics", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "i3", "MatIcon", "i4", "MatFormField", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatCheckbox", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- 1. Report Type Tabs (First) -->\n  <div class=\"report-tabs\">\n    <div class=\"tabs-container\">\n      <button\n        *ngFor=\"let tab of tabs; let i = index\"\n        class=\"tab-button\"\n        [class.active]=\"tab.active\"\n        (click)=\"onTabChange(i)\"\n      >\n        {{ tab.label }}\n      </button>\n    </div>\n  </div>\n\n  <!-- 2. Smart Filters Section (Second) -->\n  <div class=\"filters-section\">\n    <div class=\"filters-header\">\n      <div class=\"filters-title\">\n        <mat-icon>tune</mat-icon>\n        <h3>Smart Filters</h3>\n      </div>\n      <div class=\"filters-actions\">\n        <button mat-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n\n    <div class=\"filters-content\">\n      <!-- Time Period -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>schedule</mat-icon>\n          Time Period\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select [(value)]=\"selectedTimePeriod\">\n            <mat-option *ngFor=\"let period of timePeriods\" [value]=\"period.value\">\n              {{ period.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- Categories -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>category</mat-icon>\n          Categories\n        </label>\n        <div class=\"checkbox-grid\">\n          <mat-checkbox\n            *ngFor=\"let category of categories\"\n            [(ngModel)]=\"category.checked\"\n            class=\"filter-checkbox\"\n          >\n            {{ category.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Regions -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>public</mat-icon>\n          Regions\n        </label>\n        <div class=\"checkbox-grid\">\n          <mat-checkbox\n            *ngFor=\"let region of regions\"\n            [(ngModel)]=\"region.checked\"\n            class=\"filter-checkbox\"\n          >\n            {{ region.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>analytics</mat-icon>\n          Key Metrics\n        </label>\n        <div class=\"checkbox-grid\">\n          <mat-checkbox\n            *ngFor=\"let metric of keyMetrics\"\n            [(ngModel)]=\"metric.checked\"\n            class=\"filter-checkbox\"\n          >\n            {{ metric.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 3. Ask AI Input Section (Third) -->\n  <div class=\"ai-section\">\n    <div class=\"ai-header\">\n      <mat-icon class=\"ai-icon\">psychology</mat-icon>\n      <h3>Ask AI Assistant</h3>\n      <p>Ask questions about your data or request specific visualizations</p>\n    </div>\n    <div class=\"ai-input-container\">\n      <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n        <input\n          matInput\n          type=\"text\"\n          placeholder=\"e.g., Show me sales trends for the last quarter by region\"\n          [(ngModel)]=\"chatMessage\"\n          (keydown)=\"onKeyPress($event)\"\n        >\n      </mat-form-field>\n      <button mat-raised-button color=\"primary\" class=\"send-btn\" (click)=\"sendMessage()\" [disabled]=\"!chatMessage.trim()\">\n        <mat-icon>send</mat-icon>\n        Generate\n      </button>\n    </div>\n  </div>\n\n  <!-- 4. Dashboard Charts Area (Fourth) -->\n  <div class=\"dashboard-charts\">\n    <div class=\"charts-header\">\n      <mat-icon class=\"charts-icon\">dashboard</mat-icon>\n      <h3>Dashboard</h3>\n    </div>\n\n    <!-- Charts will be generated here based on AI input -->\n    <div class=\"charts-content\">\n      <!-- Placeholder content when no charts are generated -->\n      <div class=\"charts-placeholder\" *ngIf=\"!hasGeneratedCharts\">\n        <div class=\"placeholder-content\">\n          <mat-icon class=\"placeholder-icon\">insights</mat-icon>\n          <h4>Ready to Generate Insights</h4>\n          <p>Configure your filters above and ask the AI assistant to generate visualizations for your data.</p>\n\n          <!-- Feature Cards -->\n          <div class=\"feature-cards\">\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n              <h5>Interactive Charts</h5>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n              <h5>Smart Insights</h5>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">chat</mat-icon>\n              <h5>Natural Language Queries</h5>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">schedule</mat-icon>\n              <h5>Real-time Analysis</h5>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Generated charts will appear here -->\n      <div class=\"generated-charts\" *ngIf=\"hasGeneratedCharts\">\n        <!-- Charts will be dynamically generated here -->\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICTtCC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAExBP,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAJPd,EAAA,CAAAe,WAAA,WAAAC,MAAA,CAAAC,MAAA,CAA2B;IAG3BjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACF;;;;;IAgCMpB,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAqB,UAAA,UAAAC,UAAA,CAAAC,KAAA,CAAsB;IACnEvB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAG,UAAA,CAAAF,KAAA,MACF;;;;;;IAYFpB,EAAA,CAAAC,cAAA,uBAIC;IAFCD,EAAA,CAAAE,UAAA,2BAAAsB,uFAAAC,MAAA;MAAA,MAAArB,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAqB,IAAA;MAAA,MAAAC,YAAA,GAAAvB,WAAA,CAAAwB,SAAA;MAAA,OAAa5B,EAAA,CAAAW,WAAA,CAAAgB,YAAA,CAAAE,OAAA,GAAAJ,MAAA,CACnB;IAAA,EADoC;IAG9BzB,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAe;;;;IAJbd,EAAA,CAAAqB,UAAA,YAAAM,YAAA,CAAAE,OAAA,CAA8B;IAG9B7B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAQ,YAAA,CAAAP,KAAA,MACF;;;;;;IAWApB,EAAA,CAAAC,cAAA,uBAIC;IAFCD,EAAA,CAAAE,UAAA,2BAAA4B,uFAAAL,MAAA;MAAA,MAAArB,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA0B,IAAA;MAAA,MAAAC,UAAA,GAAA5B,WAAA,CAAAwB,SAAA;MAAA,OAAa5B,EAAA,CAAAW,WAAA,CAAAqB,UAAA,CAAAH,OAAA,GAAAJ,MAAA,CACnB;IAAA,EADkC;IAG5BzB,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAe;;;;IAJbd,EAAA,CAAAqB,UAAA,YAAAW,UAAA,CAAAH,OAAA,CAA4B;IAG5B7B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAa,UAAA,CAAAZ,KAAA,MACF;;;;;;IAWApB,EAAA,CAAAC,cAAA,uBAIC;IAFCD,EAAA,CAAAE,UAAA,2BAAA+B,uFAAAR,MAAA;MAAA,MAAArB,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA6B,IAAA;MAAA,MAAAC,UAAA,GAAA/B,WAAA,CAAAwB,SAAA;MAAA,OAAa5B,EAAA,CAAAW,WAAA,CAAAwB,UAAA,CAAAN,OAAA,GAAAJ,MAAA,CACnB;IAAA,EADkC;IAG5BzB,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAe;;;;IAJbd,EAAA,CAAAqB,UAAA,YAAAc,UAAA,CAAAN,OAAA,CAA4B;IAG5B7B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAgB,UAAA,CAAAf,KAAA,MACF;;;;;IAwCJpB,EAAA,CAAAC,cAAA,cAA4D;IAErBD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACtDd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,iCAA0B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,sGAA+F;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAGtGd,EAAA,CAAAC,cAAA,cAA2B;IAEQD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACnDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAE7Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACrDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEzBd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,YAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC9Cd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,gCAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClDd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;;;;;IAOnCd,EAAA,CAAAoC,SAAA,cAEM;;;ADzJZ,MAmBaC,uBAAuB;EAqClCC,YAAA;IApCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAEnB,KAAK,EAAE,aAAa;MAAEH,KAAK,EAAE,wBAAwB;MAAES,OAAO,EAAE;IAAK,CAAE,EACzE;MAAEN,KAAK,EAAE,aAAa;MAAEH,KAAK,EAAE,iBAAiB;MAAES,OAAO,EAAE;IAAK,CAAE,EAClE;MAAEN,KAAK,EAAE,aAAa;MAAEH,KAAK,EAAE,aAAa;MAAES,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAEN,KAAK,EAAE,aAAa;MAAEH,KAAK,EAAE,gBAAgB;MAAES,OAAO,EAAE;IAAK,CAAE,EACjE;MAAEN,KAAK,EAAE,aAAa;MAAEH,KAAK,EAAE,oBAAoB;MAAES,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAc,SAAS,GAAG,CACV;MAAEpB,KAAK,EAAE,OAAO;MAAEH,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEG,KAAK,EAAE,WAAW;MAAEH,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEG,KAAK,EAAE,WAAW;MAAEH,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEG,KAAK,EAAE,YAAY;MAAEH,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEG,KAAK,EAAE,QAAQ;MAAEH,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAwB,iBAAiB,GAAa,EAAE;IAChC,KAAAC,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAE7B,KAAK,EAAE,YAAY;MAAEH,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEG,KAAK,EAAE,iBAAiB;MAAEH,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEG,KAAK,EAAE,cAAc;MAAEH,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEG,KAAK,EAAE,kBAAkB;MAAEH,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBiC,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACZ,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEA3B,WAAWA,CAACJ,KAAa;IACvB,IAAI,CAACgC,WAAW,GAAGhC,KAAK;IACxB,IAAI,CAACyC,IAAI,CAACG,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACpC,MAAM,GAAGqC,CAAC,KAAK9C,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEA+C,WAAWA,CAAA;IACT,IAAI,IAAI,CAACP,WAAW,CAACQ,IAAI,EAAE,EAAE;MAC3B;MACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACV,WAAW,CAAC;MAE9C;MACA,IAAI,CAACP,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACO,WAAW,GAAG,EAAE;;EAEzB;EAEAW,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAEAS,YAAYA,CAAA;IACV;IACAP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACnB,aAAa,GAAG,KAAK;EAC5B;EAEA0B,YAAYA,CAAA;IACV;IACA,IAAI,CAACrB,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACL,SAAS,CAACU,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAACrC,OAAO,GAAG,KAAK,CAAC;EAC9D;;;uBArFWQ,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAA8B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArE,EAAA,CAAAsE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCpC5E,EAAA,CAAAC,cAAA,aAAuC;UAIjCD,EAAA,CAAA8E,UAAA,IAAAC,yCAAA,oBAOS;UACX/E,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,aAA6B;UAGbD,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAExBd,EAAA,CAAAC,cAAA,cAA6B;UACUD,EAAA,CAAAE,UAAA,mBAAA8E,0DAAA;YAAA,OAASH,GAAA,CAAAZ,YAAA,EAAc;UAAA,EAAC;UAC3DjE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC5Bd,EAAA,CAAAa,MAAA,eACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,iBAAqF;UAAzBD,EAAA,CAAAE,UAAA,mBAAA+E,0DAAA;YAAA,OAASJ,GAAA,CAAAb,YAAA,EAAc;UAAA,EAAC;UAClFhE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC1Bd,EAAA,CAAAa,MAAA,uBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAIbd,EAAA,CAAAC,cAAA,eAA6B;UAIbD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC7Bd,EAAA,CAAAa,MAAA,qBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAE,UAAA,yBAAAgF,oEAAAzD,MAAA;YAAA,OAAAoD,GAAA,CAAAM,kBAAA,GAAA1D,MAAA;UAAA,EAA8B;UACxCzB,EAAA,CAAA8E,UAAA,KAAAM,8CAAA,yBAEa;UACfpF,EAAA,CAAAc,YAAA,EAAa;UAKjBd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC7Bd,EAAA,CAAAa,MAAA,oBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA8E,UAAA,KAAAO,gDAAA,2BAMe;UACjBrF,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC3Bd,EAAA,CAAAa,MAAA,iBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA8E,UAAA,KAAAQ,gDAAA,2BAMe;UACjBtF,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC9Bd,EAAA,CAAAa,MAAA,qBACF;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACRd,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA8E,UAAA,KAAAS,gDAAA,2BAMe;UACjBvF,EAAA,CAAAc,YAAA,EAAM;UAMZd,EAAA,CAAAC,cAAA,eAAwB;UAEMD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Cd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAa,MAAA,wEAAgE;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAEzEd,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAAE,UAAA,2BAAAsF,iEAAA/D,MAAA;YAAA,OAAAoD,GAAA,CAAA7B,WAAA,GAAAvB,MAAA;UAAA,EAAyB,qBAAAgE,2DAAAhE,MAAA;YAAA,OACdoD,GAAA,CAAAlB,UAAA,CAAAlC,MAAA,CAAkB;UAAA,EADJ;UAJ3BzB,EAAA,CAAAc,YAAA,EAMC;UAEHd,EAAA,CAAAC,cAAA,kBAAoH;UAAzDD,EAAA,CAAAE,UAAA,mBAAAwF,0DAAA;YAAA,OAASb,GAAA,CAAAtB,WAAA,EAAa;UAAA,EAAC;UAChFvD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,kBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAKbd,EAAA,CAAAC,cAAA,eAA8B;UAEID,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAClDd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAIpBd,EAAA,CAAAC,cAAA,eAA4B;UAE1BD,EAAA,CAAA8E,UAAA,KAAAa,uCAAA,mBA0BM;UAGN3F,EAAA,CAAA8E,UAAA,KAAAc,uCAAA,kBAEM;UACR5F,EAAA,CAAAc,YAAA,EAAM;;;UApKcd,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAA5B,IAAA,CAAS;UAqCXjD,EAAA,CAAAkB,SAAA,IAA8B;UAA9BlB,EAAA,CAAAqB,UAAA,UAAAwD,GAAA,CAAAM,kBAAA,CAA8B;UACTnF,EAAA,CAAAkB,SAAA,GAAc;UAAdlB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAAgB,WAAA,CAAc;UAexB7F,EAAA,CAAAkB,SAAA,GAAa;UAAblB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAAiB,UAAA,CAAa;UAiBf9F,EAAA,CAAAkB,SAAA,GAAU;UAAVlB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAAkB,OAAA,CAAU;UAiBV/F,EAAA,CAAAkB,SAAA,GAAa;UAAblB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAAmB,UAAA,CAAa;UAwBlChG,EAAA,CAAAkB,SAAA,IAAyB;UAAzBlB,EAAA,CAAAqB,UAAA,YAAAwD,GAAA,CAAA7B,WAAA,CAAyB;UAIsDhD,EAAA,CAAAkB,SAAA,GAAgC;UAAhClB,EAAA,CAAAqB,UAAA,cAAAwD,GAAA,CAAA7B,WAAA,CAAAQ,IAAA,GAAgC;UAiBlFxD,EAAA,CAAAkB,SAAA,IAAyB;UAAzBlB,EAAA,CAAAqB,UAAA,UAAAwD,GAAA,CAAApC,kBAAA,CAAyB;UA6B3BzC,EAAA,CAAAkB,SAAA,GAAwB;UAAxBlB,EAAA,CAAAqB,UAAA,SAAAwD,GAAA,CAAApC,kBAAA,CAAwB;;;qBDnJzDpD,YAAY,EAAA4G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7G,aAAa,EACbC,eAAe,EAAA6G,EAAA,CAAAC,SAAA,EACf7G,aAAa,EAAA8G,EAAA,CAAAC,OAAA,EACb9G,kBAAkB,EAAA+G,EAAA,CAAAC,YAAA,EAClB/G,eAAe,EAAAgH,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACflH,cAAc,EAAAmH,EAAA,CAAAC,QAAA,EACdnH,iBAAiB,EAAAoH,EAAA,CAAAC,WAAA,EACjBpH,aAAa,EACbC,gBAAgB,EAChBC,WAAW,EAAAmH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFjF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}