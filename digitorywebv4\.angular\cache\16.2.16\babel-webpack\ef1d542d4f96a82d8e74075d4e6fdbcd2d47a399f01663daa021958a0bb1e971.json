{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SseService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n    this.eventSource = null;\n    this.messageSubject = new Subject();\n    this.connectionStatusSubject = new Subject();\n  }\n  /**\n   * Connect to the SSE endpoint\n   * @param tenantId The tenant ID to connect with\n   */\n  connect(tenantId) {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId, query) {\n    if (this.eventSource) {\n      this.eventSource.close();\n    }\n    const responseSubject = new Subject();\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      this.eventSource = new EventSource(`${this.baseUrl}/llmask?query=${encodedQuery}&tenant_id=${tenantId}`);\n      // Handle incoming messages\n      this.eventSource.onmessage = event => {\n        try {\n          const chunk = event.data;\n          if (chunk === \"[DONE]\") {\n            // End of stream\n            responseSubject.complete();\n            this.eventSource?.close();\n            this.eventSource = null;\n          } else {\n            // Add chunk to response\n            responseSubject.next(chunk);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n          responseSubject.error(error);\n        }\n      };\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n      // Handle errors\n      this.eventSource.onerror = error => {\n        console.error('SSE connection error:', error);\n        this.connectionStatusSubject.next(false);\n        responseSubject.error(error);\n        this.disconnect();\n      };\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect() {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n  }\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId, message) {\n    // Create a new message from the user\n    const userMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: chunk => {\n        // Create a bot message for each chunk\n        const botMessage = {\n          id: this.generateId(),\n          text: chunk,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(botMessage);\n      },\n      error: error => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage = {\n          id: this.generateId(),\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n      }\n    });\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId, restaurantInfo) {\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` + `Location: ${restaurantInfo.location || 'Not specified'}\\n` + `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` + `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` + `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` + `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` + `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n  /**\n   * Get the message stream\n   */\n  get messages$() {\n    return this.messageSubject.asObservable();\n  }\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$() {\n    return this.connectionStatusSubject.asObservable();\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  static {\n    this.ɵfac = function SseService_Factory(t) {\n      return new (t || SseService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SseService,\n      factory: SseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SseService };", "map": {"version": 3, "names": ["Subject", "throwError", "of", "environment", "SseService", "constructor", "http", "baseUrl", "engineUrl", "eventSource", "messageSubject", "connectionStatusSubject", "connect", "tenantId", "next", "connectionStatus$", "streamResponse", "query", "close", "responseSubject", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "EventSource", "onmessage", "event", "chunk", "data", "complete", "error", "console", "onopen", "log", "onerror", "disconnect", "asObservable", "Error", "sendMessage", "message", "userMessage", "id", "generateId", "text", "sender", "timestamp", "Date", "subscribe", "botMessage", "errorMessage", "submitRestaurantInfo", "restaurantInfo", "infoSummary", "location", "businessType", "cuisineType", "operatingHours", "specialties", "join", "contactInfo", "messages$", "Math", "random", "toString", "substring", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/sse.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, Subject, throwError, of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { ChatMessage } from '../models/chat-message.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SseService {\n  private baseUrl: string = environment.engineUrl;\n  private eventSource: EventSource | null = null;\n  private messageSubject = new Subject<ChatMessage>();\n  private connectionStatusSubject = new Subject<boolean>();\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Connect to the SSE endpoint\n   * @param tenantId The tenant ID to connect with\n   */\n  connect(tenantId: string): Observable<boolean> {\n    // We don't need to establish a persistent connection initially\n    // since we'll create a new EventSource for each query\n    this.connectionStatusSubject.next(true);\n    return this.connectionStatus$;\n  }\n\n  /**\n   * Stream a response from the SSE endpoint\n   * @param tenantId The tenant ID\n   * @param query The user's query\n   */\n  streamResponse(tenantId: string, query: string): Observable<string> {\n    if (this.eventSource) {\n      this.eventSource.close();\n    }\n\n    const responseSubject = new Subject<string>();\n\n    try {\n      // Create a new EventSource connection to the SSE endpoint with the query\n      const encodedQuery = encodeURIComponent(query);\n      this.eventSource = new EventSource(`${this.baseUrl}/llmask?query=${encodedQuery}&tenant_id=${tenantId}`);\n\n      // Handle incoming messages\n      this.eventSource.onmessage = (event) => {\n        try {\n          const chunk = event.data;\n\n          if (chunk === \"[DONE]\") {\n            // End of stream\n            responseSubject.complete();\n            this.eventSource?.close();\n            this.eventSource = null;\n          } else {\n            // Add chunk to response\n            responseSubject.next(chunk);\n          }\n        } catch (error) {\n          console.error('Error parsing SSE message:', error);\n          responseSubject.error(error);\n        }\n      };\n\n      // Handle connection open\n      this.eventSource.onopen = () => {\n        console.log('SSE connection established');\n        this.connectionStatusSubject.next(true);\n      };\n\n      // Handle errors\n      this.eventSource.onerror = (error) => {\n        console.error('SSE connection error:', error);\n        this.connectionStatusSubject.next(false);\n        responseSubject.error(error);\n        this.disconnect();\n      };\n\n      return responseSubject.asObservable();\n    } catch (error) {\n      console.error('Failed to establish SSE connection:', error);\n      this.connectionStatusSubject.next(false);\n      return throwError(() => new Error('Failed to establish SSE connection'));\n    }\n  }\n\n  /**\n   * Disconnect from the SSE endpoint\n   */\n  disconnect(): void {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n      this.connectionStatusSubject.next(false);\n    }\n  }\n\n  /**\n   * Send a message to the chat endpoint\n   * @param tenantId The tenant ID\n   * @param message The message to send\n   */\n  sendMessage(tenantId: string, message: string): Observable<ChatMessage> {\n    // Create a new message from the user\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: message,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Add the user message to the message stream\n    this.messageSubject.next(userMessage);\n\n    // Stream the response from the SSE endpoint\n    this.streamResponse(tenantId, message).subscribe({\n      next: (chunk: string) => {\n        // Create a bot message for each chunk\n        const botMessage: ChatMessage = {\n          id: this.generateId(),\n          text: chunk,\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(botMessage);\n      },\n      error: (error) => {\n        console.error('Error streaming response:', error);\n        // Send an error message\n        const errorMessage: ChatMessage = {\n          id: this.generateId(),\n          text: 'Sorry, there was an error processing your request. Please try again.',\n          sender: 'bot',\n          timestamp: new Date()\n        };\n        this.messageSubject.next(errorMessage);\n      }\n    });\n\n    // Return the user message as an observable\n    return of(userMessage);\n  }\n\n  /**\n   * Submit the collected restaurant information\n   * @param tenantId The tenant ID\n   * @param restaurantInfo The restaurant information\n   */\n  submitRestaurantInfo(tenantId: string, restaurantInfo: any): Observable<any> {\n    // Create a message to send to the agent with the restaurant information\n    const infoSummary = `I want to save the following restaurant information:\\n` +\n      `Location: ${restaurantInfo.location || 'Not specified'}\\n` +\n      `Business Type: ${restaurantInfo.businessType || 'Not specified'}\\n` +\n      `Cuisine Type: ${restaurantInfo.cuisineType || 'Not specified'}\\n` +\n      `Operating Hours: ${restaurantInfo.operatingHours || 'Not specified'}\\n` +\n      `Specialties: ${restaurantInfo.specialties ? restaurantInfo.specialties.join(', ') : 'Not specified'}\\n` +\n      `Contact Info: ${restaurantInfo.contactInfo || 'Not specified'}`;\n\n    // Send the information summary as a message\n    return this.sendMessage(tenantId, infoSummary);\n  }\n\n  /**\n   * Get the message stream\n   */\n  get messages$(): Observable<ChatMessage> {\n    return this.messageSubject.asObservable();\n  }\n\n  /**\n   * Get the connection status stream\n   */\n  get connectionStatus$(): Observable<boolean> {\n    return this.connectionStatusSubject.asObservable();\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n}\n"], "mappings": "AAEA,SAAqBA,OAAO,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAE1D,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,UAAU;EAMrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IALhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;IACvC,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAG,IAAIV,OAAO,EAAe;IAC3C,KAAAW,uBAAuB,GAAG,IAAIX,OAAO,EAAW;EAEhB;EAExC;;;;EAIAY,OAAOA,CAACC,QAAgB;IACtB;IACA;IACA,IAAI,CAACF,uBAAuB,CAACG,IAAI,CAAC,IAAI,CAAC;IACvC,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAEA;;;;;EAKAC,cAAcA,CAACH,QAAgB,EAAEI,KAAa;IAC5C,IAAI,IAAI,CAACR,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACS,KAAK,EAAE;;IAG1B,MAAMC,eAAe,GAAG,IAAInB,OAAO,EAAU;IAE7C,IAAI;MACF;MACA,MAAMoB,YAAY,GAAGC,kBAAkB,CAACJ,KAAK,CAAC;MAC9C,IAAI,CAACR,WAAW,GAAG,IAAIa,WAAW,CAAC,GAAG,IAAI,CAACf,OAAO,iBAAiBa,YAAY,cAAcP,QAAQ,EAAE,CAAC;MAExG;MACA,IAAI,CAACJ,WAAW,CAACc,SAAS,GAAIC,KAAK,IAAI;QACrC,IAAI;UACF,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI;UAExB,IAAID,KAAK,KAAK,QAAQ,EAAE;YACtB;YACAN,eAAe,CAACQ,QAAQ,EAAE;YAC1B,IAAI,CAAClB,WAAW,EAAES,KAAK,EAAE;YACzB,IAAI,CAACT,WAAW,GAAG,IAAI;WACxB,MAAM;YACL;YACAU,eAAe,CAACL,IAAI,CAACW,KAAK,CAAC;;SAE9B,CAAC,OAAOG,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClDT,eAAe,CAACS,KAAK,CAACA,KAAK,CAAC;;MAEhC,CAAC;MAED;MACA,IAAI,CAACnB,WAAW,CAACqB,MAAM,GAAG,MAAK;QAC7BD,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACpB,uBAAuB,CAACG,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC;MAED;MACA,IAAI,CAACL,WAAW,CAACuB,OAAO,GAAIJ,KAAK,IAAI;QACnCC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACjB,uBAAuB,CAACG,IAAI,CAAC,KAAK,CAAC;QACxCK,eAAe,CAACS,KAAK,CAACA,KAAK,CAAC;QAC5B,IAAI,CAACK,UAAU,EAAE;MACnB,CAAC;MAED,OAAOd,eAAe,CAACe,YAAY,EAAE;KACtC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACjB,uBAAuB,CAACG,IAAI,CAAC,KAAK,CAAC;MACxC,OAAOb,UAAU,CAAC,MAAM,IAAIkC,KAAK,CAAC,oCAAoC,CAAC,CAAC;;EAE5E;EAEA;;;EAGAF,UAAUA,CAAA;IACR,IAAI,IAAI,CAACxB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACS,KAAK,EAAE;MACxB,IAAI,CAACT,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,uBAAuB,CAACG,IAAI,CAAC,KAAK,CAAC;;EAE5C;EAEA;;;;;EAKAsB,WAAWA,CAACvB,QAAgB,EAAEwB,OAAe;IAC3C;IACA,MAAMC,WAAW,GAAgB;MAC/BC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,IAAI,EAAEJ,OAAO;MACbK,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,IAAI,CAAClC,cAAc,CAACI,IAAI,CAACwB,WAAW,CAAC;IAErC;IACA,IAAI,CAACtB,cAAc,CAACH,QAAQ,EAAEwB,OAAO,CAAC,CAACQ,SAAS,CAAC;MAC/C/B,IAAI,EAAGW,KAAa,IAAI;QACtB;QACA,MAAMqB,UAAU,GAAgB;UAC9BP,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBC,IAAI,EAAEhB,KAAK;UACXiB,MAAM,EAAE,KAAK;UACbC,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAAClC,cAAc,CAACI,IAAI,CAACgC,UAAU,CAAC;MACtC,CAAC;MACDlB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,MAAMmB,YAAY,GAAgB;UAChCR,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrBC,IAAI,EAAE,sEAAsE;UAC5EC,MAAM,EAAE,KAAK;UACbC,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAAClC,cAAc,CAACI,IAAI,CAACiC,YAAY,CAAC;MACxC;KACD,CAAC;IAEF;IACA,OAAO7C,EAAE,CAACoC,WAAW,CAAC;EACxB;EAEA;;;;;EAKAU,oBAAoBA,CAACnC,QAAgB,EAAEoC,cAAmB;IACxD;IACA,MAAMC,WAAW,GAAG,wDAAwD,GAC1E,aAAaD,cAAc,CAACE,QAAQ,IAAI,eAAe,IAAI,GAC3D,kBAAkBF,cAAc,CAACG,YAAY,IAAI,eAAe,IAAI,GACpE,iBAAiBH,cAAc,CAACI,WAAW,IAAI,eAAe,IAAI,GAClE,oBAAoBJ,cAAc,CAACK,cAAc,IAAI,eAAe,IAAI,GACxE,gBAAgBL,cAAc,CAACM,WAAW,GAAGN,cAAc,CAACM,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,IAAI,GACxG,iBAAiBP,cAAc,CAACQ,WAAW,IAAI,eAAe,EAAE;IAElE;IACA,OAAO,IAAI,CAACrB,WAAW,CAACvB,QAAQ,EAAEqC,WAAW,CAAC;EAChD;EAEA;;;EAGA,IAAIQ,SAASA,CAAA;IACX,OAAO,IAAI,CAAChD,cAAc,CAACwB,YAAY,EAAE;EAC3C;EAEA;;;EAGA,IAAInB,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACJ,uBAAuB,CAACuB,YAAY,EAAE;EACpD;EAEA;;;EAGQM,UAAUA,CAAA;IAChB,OAAOmB,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;;;uBA7KW1D,UAAU,EAAA2D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAV9D,UAAU;MAAA+D,OAAA,EAAV/D,UAAU,CAAAgE,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA;;SAEPjE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}