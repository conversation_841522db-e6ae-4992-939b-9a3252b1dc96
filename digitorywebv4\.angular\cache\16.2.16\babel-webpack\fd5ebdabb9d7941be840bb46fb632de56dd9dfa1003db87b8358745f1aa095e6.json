{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nclass DashboardService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.dashboardUrl = environment.engineUrl;\n    this.inventoryUrl = environment.engineUrl; // Inventory API URL\n    this.filtersSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.filters$ = this.filtersSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n  }\n  getHeaders() {\n    const user = this.authService.getCurrentUser();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${user?.token || ''}`\n    });\n  }\n  // Get purchase dashboard data\n  getPurchaseDashboard(filters) {\n    this.loadingSubject.next(true);\n    const requestBody = {\n      tenant_id: filters.tenant_id,\n      start_date: filters.start_date,\n      end_date: filters.end_date,\n      selected_restaurants: filters.selected_restaurants,\n      selected_vendors: filters.selected_vendors,\n      selected_categories: filters.selected_categories,\n      selected_sub_categories: filters.selected_sub_categories,\n      selected_base_date: filters.selected_base_date\n    };\n    return this.http.post(`${this.dashboardUrl}/api/dashboard/purchase`, requestBody, {\n      headers: this.getHeaders()\n    }).pipe(map(response => {\n      this.loadingSubject.next(false);\n      return response;\n    }), catchError(error => {\n      this.loadingSubject.next(false);\n      console.error('Dashboard API Error:', error);\n      // Return a mock response instead of throwing error to prevent logout\n      const mockResponse = {\n        success: false,\n        message: 'Dashboard service is currently unavailable. Please try again later.',\n        data: {\n          overview: 'Dashboard service is currently unavailable.',\n          cost_analysis: 'Unable to load cost analysis at this time.',\n          supplier_performance: 'Unable to load supplier performance data.',\n          trend_analysis: 'Unable to load trend analysis.',\n          top_items: 'Unable to load top items data.',\n          predictive_insights: 'Unable to load predictive insights.',\n          data_summary: {\n            total_records: 0,\n            date_range: {\n              start: filters.start_date,\n              end: filters.end_date\n            },\n            unique_suppliers: 0,\n            unique_items: 0,\n            unique_locations: 0,\n            total_cost: 0,\n            avg_accuracy: 0\n          },\n          generated_at: new Date().toISOString()\n        }\n      };\n      return of(mockResponse);\n    }));\n  }\n  // Get filter options (restaurants, vendors, categories, etc.)\n  getFilterOptions(tenantId) {\n    // Using existing inventory service endpoints to get filter options\n    return this.http.get(`${this.inventoryUrl}/getBaseDataForMD?tenantId=${tenantId}`, {\n      headers: this.getHeaders()\n    }).pipe(map(response => {\n      // Transform the response to match our FilterOptions interface\n      return {\n        restaurants: response.restaurants || [],\n        vendors: response.vendors || [],\n        categories: response.categories || [],\n        subCategories: response.subCategories || []\n      };\n    }), catchError(error => {\n      console.error('Filter Options API Error:', error);\n      // Return empty options on error\n      return of({\n        restaurants: [],\n        vendors: [],\n        categories: [],\n        subCategories: []\n      });\n    }));\n  }\n  // Update filters\n  updateFilters(filters) {\n    this.filtersSubject.next(filters);\n  }\n  // Get current filters\n  getCurrentFilters() {\n    return this.filtersSubject.value;\n  }\n  // Initialize default filters\n  initializeDefaultFilters(tenantId) {\n    const today = new Date();\n    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n    const defaultFilters = {\n      tenant_id: tenantId,\n      start_date: startOfMonth.toISOString().split('T')[0],\n      end_date: today.toISOString().split('T')[0],\n      selected_restaurants: [],\n      selected_vendors: [],\n      selected_categories: [],\n      selected_sub_categories: [],\n      selected_base_date: today.toISOString().split('T')[0]\n    };\n    this.updateFilters(defaultFilters);\n    return defaultFilters;\n  }\n  // Query dashboard with natural language\n  queryDashboard(query, tenantId) {\n    const requestBody = {\n      tenant_id: tenantId,\n      query: query\n    };\n    return this.http.post(`${this.dashboardUrl}/api/dashboard/query`, requestBody, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error('Dashboard Query API Error:', error);\n      // Return a mock response instead of throwing error\n      return of({\n        success: false,\n        message: 'Dashboard query service is currently unavailable.',\n        answer: 'Unable to process query at this time. Please try again later.'\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function DashboardService_Factory(t) {\n      return new (t || DashboardService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardService,\n      factory: DashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { DashboardService };", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "of", "map", "catchError", "environment", "DashboardService", "constructor", "http", "authService", "dashboardUrl", "engineUrl", "inventoryUrl", "filtersSubject", "loadingSubject", "filters$", "asObservable", "loading$", "getHeaders", "user", "getCurrentUser", "token", "getPurchaseDashboard", "filters", "next", "requestBody", "tenant_id", "start_date", "end_date", "selected_restaurants", "selected_vendors", "selected_categories", "selected_sub_categories", "selected_base_date", "post", "headers", "pipe", "response", "error", "console", "mockResponse", "success", "message", "data", "overview", "cost_analysis", "supplier_performance", "trend_analysis", "top_items", "predictive_insights", "data_summary", "total_records", "date_range", "start", "end", "unique_suppliers", "unique_items", "unique_locations", "total_cost", "avg_accuracy", "generated_at", "Date", "toISOString", "getFilterOptions", "tenantId", "get", "restaurants", "vendors", "categories", "subCategories", "updateFilters", "getCurrentFilters", "value", "initializeDefaultFilters", "today", "startOfMonth", "getFullYear", "getMonth", "defaultFilters", "split", "queryDashboard", "query", "answer", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from './auth.service';\n\nexport interface DashboardFilters {\n  tenant_id: string;\n  start_date: string;\n  end_date: string;\n  selected_restaurants: string[];\n  selected_vendors: string[];\n  selected_categories: string[];\n  selected_sub_categories: string[];\n  selected_base_date: string;\n}\n\nexport interface DashboardResponse {\n  success: boolean;\n  data?: {\n    overview: string;\n    cost_analysis: string;\n    supplier_performance: string;\n    trend_analysis: string;\n    top_items: string;\n    predictive_insights: string;\n    data_summary: {\n      total_records: number;\n      date_range: { start: string; end: string };\n      unique_suppliers: number;\n      unique_items: number;\n      unique_locations: number;\n      total_cost: number;\n      avg_accuracy: number;\n    };\n    generated_at: string;\n  };\n  message?: string;\n}\n\nexport interface FilterOptions {\n  restaurants: { id: string; name: string }[];\n  vendors: { id: string; name: string }[];\n  categories: { id: string; name: string }[];\n  subCategories: { id: string; name: string }[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardService {\n  private dashboardUrl: string = environment.engineUrl;\n  private inventoryUrl: string = environment.engineUrl; // Inventory API URL\n  private filtersSubject = new BehaviorSubject<DashboardFilters | null>(null);\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n\n  public filters$ = this.filtersSubject.asObservable();\n  public loading$ = this.loadingSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  private getHeaders(): HttpHeaders {\n    const user = this.authService.getCurrentUser();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${user?.token || ''}`\n    });\n  }\n\n  // Get purchase dashboard data\n  getPurchaseDashboard(filters: DashboardFilters): Observable<DashboardResponse> {\n    this.loadingSubject.next(true);\n\n    const requestBody = {\n      tenant_id: filters.tenant_id,\n      start_date: filters.start_date,\n      end_date: filters.end_date,\n      selected_restaurants: filters.selected_restaurants,\n      selected_vendors: filters.selected_vendors,\n      selected_categories: filters.selected_categories,\n      selected_sub_categories: filters.selected_sub_categories,\n      selected_base_date: filters.selected_base_date\n    };\n\n    return this.http.post<DashboardResponse>(\n      `${this.dashboardUrl}/api/dashboard/purchase`,\n      requestBody,\n      { headers: this.getHeaders() }\n    ).pipe(\n      map(response => {\n        this.loadingSubject.next(false);\n        return response;\n      }),\n      catchError(error => {\n        this.loadingSubject.next(false);\n        console.error('Dashboard API Error:', error);\n\n        // Return a mock response instead of throwing error to prevent logout\n        const mockResponse: DashboardResponse = {\n          success: false,\n          message: 'Dashboard service is currently unavailable. Please try again later.',\n          data: {\n            overview: 'Dashboard service is currently unavailable.',\n            cost_analysis: 'Unable to load cost analysis at this time.',\n            supplier_performance: 'Unable to load supplier performance data.',\n            trend_analysis: 'Unable to load trend analysis.',\n            top_items: 'Unable to load top items data.',\n            predictive_insights: 'Unable to load predictive insights.',\n            data_summary: {\n              total_records: 0,\n              date_range: { start: filters.start_date, end: filters.end_date },\n              unique_suppliers: 0,\n              unique_items: 0,\n              unique_locations: 0,\n              total_cost: 0,\n              avg_accuracy: 0\n            },\n            generated_at: new Date().toISOString()\n          }\n        };\n\n        return of(mockResponse);\n      })\n    );\n  }\n\n  // Get filter options (restaurants, vendors, categories, etc.)\n  getFilterOptions(tenantId: string): Observable<FilterOptions> {\n    // Using existing inventory service endpoints to get filter options\n    return this.http.get<any>(`${this.inventoryUrl}/getBaseDataForMD?tenantId=${tenantId}`,\n      { headers: this.getHeaders() }\n    ).pipe(\n      map(response => {\n        // Transform the response to match our FilterOptions interface\n        return {\n          restaurants: response.restaurants || [],\n          vendors: response.vendors || [],\n          categories: response.categories || [],\n          subCategories: response.subCategories || []\n        };\n      }),\n      catchError(error => {\n        console.error('Filter Options API Error:', error);\n        // Return empty options on error\n        return of({\n          restaurants: [],\n          vendors: [],\n          categories: [],\n          subCategories: []\n        });\n      })\n    );\n  }\n\n  // Update filters\n  updateFilters(filters: DashboardFilters): void {\n    this.filtersSubject.next(filters);\n  }\n\n  // Get current filters\n  getCurrentFilters(): DashboardFilters | null {\n    return this.filtersSubject.value;\n  }\n\n  // Initialize default filters\n  initializeDefaultFilters(tenantId: string): DashboardFilters {\n    const today = new Date();\n    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n\n    const defaultFilters: DashboardFilters = {\n      tenant_id: tenantId,\n      start_date: startOfMonth.toISOString().split('T')[0],\n      end_date: today.toISOString().split('T')[0],\n      selected_restaurants: [],\n      selected_vendors: [],\n      selected_categories: [],\n      selected_sub_categories: [],\n      selected_base_date: today.toISOString().split('T')[0]\n    };\n\n    this.updateFilters(defaultFilters);\n    return defaultFilters;\n  }\n\n  // Query dashboard with natural language\n  queryDashboard(query: string, tenantId: string): Observable<any> {\n    const requestBody = {\n      tenant_id: tenantId,\n      query: query\n    };\n\n    return this.http.post<any>(\n      `${this.dashboardUrl}/api/dashboard/query`,\n      requestBody,\n      { headers: this.getHeaders() }\n    ).pipe(\n      catchError(error => {\n        console.error('Dashboard Query API Error:', error);\n        // Return a mock response instead of throwing error\n        return of({\n          success: false,\n          message: 'Dashboard query service is currently unavailable.',\n          answer: 'Unable to process query at this time. Please try again later.'\n        });\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,eAAe,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;;;AA4C1D,MAGaC,gBAAgB;EAS3BC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAVb,KAAAC,YAAY,GAAWL,WAAW,CAACM,SAAS;IAC5C,KAAAC,YAAY,GAAWP,WAAW,CAACM,SAAS,CAAC,CAAC;IAC9C,KAAAE,cAAc,GAAG,IAAIZ,eAAe,CAA0B,IAAI,CAAC;IACnE,KAAAa,cAAc,GAAG,IAAIb,eAAe,CAAU,KAAK,CAAC;IAErD,KAAAc,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACG,YAAY,EAAE;IAC7C,KAAAC,QAAQ,GAAG,IAAI,CAACH,cAAc,CAACE,YAAY,EAAE;EAKjD;EAEKE,UAAUA,CAAA;IAChB,MAAMC,IAAI,GAAG,IAAI,CAACV,WAAW,CAACW,cAAc,EAAE;IAC9C,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUmB,IAAI,EAAEE,KAAK,IAAI,EAAE;KAC7C,CAAC;EACJ;EAEA;EACAC,oBAAoBA,CAACC,OAAyB;IAC5C,IAAI,CAACT,cAAc,CAACU,IAAI,CAAC,IAAI,CAAC;IAE9B,MAAMC,WAAW,GAAG;MAClBC,SAAS,EAAEH,OAAO,CAACG,SAAS;MAC5BC,UAAU,EAAEJ,OAAO,CAACI,UAAU;MAC9BC,QAAQ,EAAEL,OAAO,CAACK,QAAQ;MAC1BC,oBAAoB,EAAEN,OAAO,CAACM,oBAAoB;MAClDC,gBAAgB,EAAEP,OAAO,CAACO,gBAAgB;MAC1CC,mBAAmB,EAAER,OAAO,CAACQ,mBAAmB;MAChDC,uBAAuB,EAAET,OAAO,CAACS,uBAAuB;MACxDC,kBAAkB,EAAEV,OAAO,CAACU;KAC7B;IAED,OAAO,IAAI,CAACzB,IAAI,CAAC0B,IAAI,CACnB,GAAG,IAAI,CAACxB,YAAY,yBAAyB,EAC7Ce,WAAW,EACX;MAAEU,OAAO,EAAE,IAAI,CAACjB,UAAU;IAAE,CAAE,CAC/B,CAACkB,IAAI,CACJjC,GAAG,CAACkC,QAAQ,IAAG;MACb,IAAI,CAACvB,cAAc,CAACU,IAAI,CAAC,KAAK,CAAC;MAC/B,OAAOa,QAAQ;IACjB,CAAC,CAAC,EACFjC,UAAU,CAACkC,KAAK,IAAG;MACjB,IAAI,CAACxB,cAAc,CAACU,IAAI,CAAC,KAAK,CAAC;MAC/Be,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA,MAAME,YAAY,GAAsB;QACtCC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,qEAAqE;QAC9EC,IAAI,EAAE;UACJC,QAAQ,EAAE,6CAA6C;UACvDC,aAAa,EAAE,4CAA4C;UAC3DC,oBAAoB,EAAE,2CAA2C;UACjEC,cAAc,EAAE,gCAAgC;UAChDC,SAAS,EAAE,gCAAgC;UAC3CC,mBAAmB,EAAE,qCAAqC;UAC1DC,YAAY,EAAE;YACZC,aAAa,EAAE,CAAC;YAChBC,UAAU,EAAE;cAAEC,KAAK,EAAE9B,OAAO,CAACI,UAAU;cAAE2B,GAAG,EAAE/B,OAAO,CAACK;YAAQ,CAAE;YAChE2B,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE,CAAC;YACfC,gBAAgB,EAAE,CAAC;YACnBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;WACf;UACDC,YAAY,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;OAEvC;MAED,OAAO5D,EAAE,CAACsC,YAAY,CAAC;IACzB,CAAC,CAAC,CACH;EACH;EAEA;EACAuB,gBAAgBA,CAACC,QAAgB;IAC/B;IACA,OAAO,IAAI,CAACxD,IAAI,CAACyD,GAAG,CAAM,GAAG,IAAI,CAACrD,YAAY,8BAA8BoD,QAAQ,EAAE,EACpF;MAAE7B,OAAO,EAAE,IAAI,CAACjB,UAAU;IAAE,CAAE,CAC/B,CAACkB,IAAI,CACJjC,GAAG,CAACkC,QAAQ,IAAG;MACb;MACA,OAAO;QACL6B,WAAW,EAAE7B,QAAQ,CAAC6B,WAAW,IAAI,EAAE;QACvCC,OAAO,EAAE9B,QAAQ,CAAC8B,OAAO,IAAI,EAAE;QAC/BC,UAAU,EAAE/B,QAAQ,CAAC+B,UAAU,IAAI,EAAE;QACrCC,aAAa,EAAEhC,QAAQ,CAACgC,aAAa,IAAI;OAC1C;IACH,CAAC,CAAC,EACFjE,UAAU,CAACkC,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA,OAAOpC,EAAE,CAAC;QACRgE,WAAW,EAAE,EAAE;QACfC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE;OAChB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAC,aAAaA,CAAC/C,OAAyB;IACrC,IAAI,CAACV,cAAc,CAACW,IAAI,CAACD,OAAO,CAAC;EACnC;EAEA;EACAgD,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC1D,cAAc,CAAC2D,KAAK;EAClC;EAEA;EACAC,wBAAwBA,CAACT,QAAgB;IACvC,MAAMU,KAAK,GAAG,IAAIb,IAAI,EAAE;IACxB,MAAMc,YAAY,GAAG,IAAId,IAAI,CAACa,KAAK,CAACE,WAAW,EAAE,EAAEF,KAAK,CAACG,QAAQ,EAAE,EAAE,CAAC,CAAC;IAEvE,MAAMC,cAAc,GAAqB;MACvCpD,SAAS,EAAEsC,QAAQ;MACnBrC,UAAU,EAAEgD,YAAY,CAACb,WAAW,EAAE,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDnD,QAAQ,EAAE8C,KAAK,CAACZ,WAAW,EAAE,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3ClD,oBAAoB,EAAE,EAAE;MACxBC,gBAAgB,EAAE,EAAE;MACpBC,mBAAmB,EAAE,EAAE;MACvBC,uBAAuB,EAAE,EAAE;MAC3BC,kBAAkB,EAAEyC,KAAK,CAACZ,WAAW,EAAE,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACrD;IAED,IAAI,CAACT,aAAa,CAACQ,cAAc,CAAC;IAClC,OAAOA,cAAc;EACvB;EAEA;EACAE,cAAcA,CAACC,KAAa,EAAEjB,QAAgB;IAC5C,MAAMvC,WAAW,GAAG;MAClBC,SAAS,EAAEsC,QAAQ;MACnBiB,KAAK,EAAEA;KACR;IAED,OAAO,IAAI,CAACzE,IAAI,CAAC0B,IAAI,CACnB,GAAG,IAAI,CAACxB,YAAY,sBAAsB,EAC1Ce,WAAW,EACX;MAAEU,OAAO,EAAE,IAAI,CAACjB,UAAU;IAAE,CAAE,CAC/B,CAACkB,IAAI,CACJhC,UAAU,CAACkC,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAOpC,EAAE,CAAC;QACRuC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,mDAAmD;QAC5DwC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC,CACH;EACH;;;uBA/JW5E,gBAAgB,EAAA6E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAhBlF,gBAAgB;MAAAmF,OAAA,EAAhBnF,gBAAgB,CAAAoF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA;;SAEPrF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}