{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { LayoutModule } from '@angular/cdk/layout';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { AuthInterceptor } from './_interceptors/auth.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideAnimations(), {\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }, provideHttpClient(withInterceptorsFromDi()), importProvidersFrom(LayoutModule, MatSnackBarModule)]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "routes", "provideAnimations", "HTTP_INTERCEPTORS", "provideHttpClient", "withInterceptorsFromDi", "LayoutModule", "MatSnackBarModule", "AuthInterceptor", "appConfig", "providers", "provide", "useClass", "multi"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\n\nimport { routes } from './app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { HTTP_INTERCEPTORS, provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';\nimport { LayoutModule } from '@angular/cdk/layout';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { AuthInterceptor } from './_interceptors/auth.interceptor';\nimport { LoadingInterceptor } from './_interceptors/loading.interceptor';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideAnimations(),\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\n    provideHttpClient(withInterceptorsFromDi()),\n    importProvidersFrom(LayoutModule, MatSnackBarModule),\n  ],\n};\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,EAAEC,iBAAiB,EAAoBC,sBAAsB,QAAQ,sBAAsB;AACrH,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,kCAAkC;AAGlE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTV,aAAa,CAACC,MAAM,CAAC,EACrBC,iBAAiB,EAAE,EACnB;IAAES,OAAO,EAAER,iBAAiB;IAAES,QAAQ,EAAEJ,eAAe;IAAEK,KAAK,EAAE;EAAI,CAAE,EACtET,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CN,mBAAmB,CAACO,YAAY,EAAEC,iBAAiB,CAAC;CAEvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}