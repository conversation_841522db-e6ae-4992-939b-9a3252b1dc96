{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"../../services/smart-dashboard.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"ngx-mat-select-search\";\nconst _c0 = [\"chartsContainer\"];\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r10.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r11));\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.displayName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"div\", 68)(3, \"mat-icon\", 69);\n    i0.ɵɵtext(4, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.getLoadingMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getLoadingMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r7.getEmptyStateMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getEmptyStateMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_110_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 82)(5, \"div\", 83);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.label);\n  }\n}\nfunction SmartDashboardComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_110_div_2_Template, 9, 3, \"div\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getSummaryItems());\n  }\n}\nfunction SmartDashboardComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85, 86);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService, smartDashboardService, cdr) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.smartDashboardService = smartDashboardService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.tabs = [];\n    this.selectedTab = 0;\n    this.locations = [];\n    this.baseDates = [];\n    this.selectedLocations = [];\n    this.selectedBaseDate = '';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    this.dashboardData = null;\n    this.charts = [];\n    this.isLoading = false;\n    this.dashboardConfig = null;\n    this.useDefaultCharts = false;\n    this.locationFilterCtrl = new FormControl();\n    this.allLocationsSelected = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n  initializeComponent() {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n    this.smartDashboardService.dashboardData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.dashboardData = data;\n      if (data) {\n        this.renderCharts();\n      }\n      this.cdr.detectChanges();\n    });\n    this.smartDashboardService.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.detectChanges();\n    });\n  }\n  loadDashboardConfig() {\n    this.smartDashboardService.getDashboardConfig().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.dashboardConfig = response.data;\n        }\n      },\n      error: () => {}\n    });\n  }\n  setDefaultTabs() {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n  setDefaultBaseDates() {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n  setDefaultDates() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n  onDefaultChartsToggle() {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n    }\n  }\n  sendMessage() {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: true\n            }));\n            this.selectedLocations = this.locations.map(location => location.value);\n            this.setupLocationFilter();\n          } else {\n            this.locations = [];\n            this.selectedLocations = [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n  getDashboardDescription(index) {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n  areAllFiltersValid() {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n  setupLocationFilter() {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(startWith(''), map(value => this.filterLocations(value || '')));\n  }\n  filterLocations(value) {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location => location.label.toLowerCase().includes(filterValue));\n  }\n  toggleAllLocations() {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  onLocationSelectionChange(selectedValues) {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  updateLocationStates() {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n  isAllSelected() {\n    return this.selectedLocations.length === this.locations.length;\n  }\n  getLoadingMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n  getEmptyStateMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n  generateDashboard() {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n    // Helper function to convert Date to string format\n    const formatDateValue = dateValue => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n    this.clearCharts();\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n    this.smartDashboardService.generateDashboard(request).pipe(first()).subscribe({\n      next: () => {},\n      error: () => {}\n    });\n  }\n  clearCharts() {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n  renderCharts() {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n    this.clearCharts();\n    setTimeout(() => {\n      this.dashboardData.charts.forEach(chartConfig => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n  getSummaryItems() {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n    return this.dashboardData.summary_items;\n  }\n  createChart(chartConfig) {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n      const chart = new Chart(ctx, {\n        type: chartConfig.type,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n      this.charts.push(chart);\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.SmartDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartsContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 112,\n      vars: 38,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search restaurants...\", \"noEntriesFoundLabel\", \"No restaurants found\", 3, \"formControl\"], [1, \"select-all-controls\"], [\"mat-button\", \"\", 1, \"select-toggle-btn\", 3, \"click\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"mode-selection-card\"], [1, \"mode-header\"], [1, \"mode-icon\"], [1, \"mode-title\"], [1, \"toggle-wrapper\"], [1, \"toggle-option\"], [1, \"option-icon\"], [1, \"option-label\"], [\"color\", \"primary\", 1, \"mode-toggle\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", 3, \"placeholder\", \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"matTooltip\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"dashboard-summary\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"selection-count\"], [1, \"loading-state\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"dashboard-summary\"], [1, \"summary-cards\"], [\"class\", \"summary-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-value\"], [1, \"summary-label\"], [1, \"charts-container\"], [\"chartsContainer\", \"\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_27_listener($event) {\n            return ctx.onLocationSelectionChange($event.value);\n          });\n          i0.ɵɵelementStart(28, \"mat-option\");\n          i0.ɵɵelement(29, \"ngx-mat-select-search\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_31_listener() {\n            return ctx.toggleAllLocations();\n          });\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_option_33_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵpipe(34, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"label\", 16)(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 18);\n          i0.ɵɵtext(40, \"Base Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 20)(42, \"mat-select\", 25);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_42_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(43, SmartDashboardComponent_mat_option_43_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"label\", 16)(46, \"mat-icon\", 17);\n          i0.ɵɵtext(47, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\", 18);\n          i0.ɵɵtext(49, \"Start Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"mat-form-field\", 20)(51, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_51_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"mat-datepicker-toggle\", 27)(53, \"mat-datepicker\", null, 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 15)(56, \"label\", 16)(57, \"mat-icon\", 17);\n          i0.ɵɵtext(58, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 18);\n          i0.ɵɵtext(60, \"End Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 20)(62, \"input\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_62_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"mat-datepicker-toggle\", 27)(64, \"mat-datepicker\", null, 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 31)(67, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_67_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Reset \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"div\", 33)(72, \"div\", 34)(73, \"div\", 35)(74, \"div\", 36)(75, \"mat-icon\", 37);\n          i0.ɵɵtext(76, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"span\", 38);\n          i0.ɵɵtext(78, \"Digi AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\", 39);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"div\", 41)(83, \"div\", 42)(84, \"mat-icon\", 43);\n          i0.ɵɵtext(85, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"span\", 44);\n          i0.ɵɵtext(87, \"Dashboard Mode\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 45)(89, \"div\", 46)(90, \"mat-icon\", 47);\n          i0.ɵɵtext(91, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"span\", 48);\n          i0.ɵɵtext(93, \"Custom Query\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"mat-slide-toggle\", 49);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_mat_slide_toggle_ngModelChange_94_listener($event) {\n            return ctx.useDefaultCharts = $event;\n          })(\"change\", function SmartDashboardComponent_Template_mat_slide_toggle_change_94_listener() {\n            return ctx.onDefaultChartsToggle();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 46)(96, \"mat-icon\", 47);\n          i0.ɵɵtext(97, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"span\", 48);\n          i0.ɵɵtext(99, \"Default Charts\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(100, \"div\", 50)(101, \"mat-form-field\", 51)(102, \"input\", 52);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_102_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_102_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_103_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(104, \"mat-icon\");\n          i0.ɵɵtext(105);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(106, \"div\", 54)(107, \"div\", 55);\n          i0.ɵɵtemplate(108, SmartDashboardComponent_div_108_Template, 9, 2, \"div\", 56);\n          i0.ɵɵtemplate(109, SmartDashboardComponent_div_109_Template, 9, 2, \"div\", 57);\n          i0.ɵɵtemplate(110, SmartDashboardComponent_div_110_Template, 3, 1, \"div\", 58);\n          i0.ɵɵtemplate(111, SmartDashboardComponent_div_111_Template, 2, 0, \"div\", 59);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(54);\n          const _r5 = i0.ɵɵreference(65);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isAllSelected() ? \"Deselect All\" : \"Select All\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(34, 36, ctx.filteredLocations));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(16);\n          i0.ɵɵclassProp(\"ready\", ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.areAllFiltersValid() ? \"Ready to analyze\" : \"Please fill all required filters\", \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"active\", !ctx.useDefaultCharts);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"disabled-mode\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.useDefaultCharts ? \"Default charts mode - input disabled\" : \"Ask me about your business data...\")(\"ngModel\", ctx.chatMessage)(\"disabled\", !ctx.areAllFiltersValid() || ctx.useDefaultCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.useDefaultCharts ? !ctx.areAllFiltersValid() : !ctx.chatMessage.trim() || !ctx.areAllFiltersValid())(\"matTooltip\", ctx.useDefaultCharts ? \"Generate Default Dashboard\" : \"Send Custom Query\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.useDefaultCharts ? \"auto_awesome\" : \"send\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.dashboardData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.dashboardData == null ? null : ctx.dashboardData.summary_items));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatSuffix, MatSelectModule, i8.MatSelect, i8.MatSelectTrigger, i9.MatOption, MatInputModule, i10.MatInput, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, MatSlideToggleModule, i12.MatSlideToggle, FormsModule, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel, ReactiveFormsModule, i13.FormControlDirective, NgxMatSelectSearchModule, i14.MatSelectSearchComponent],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 270px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem !important;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.5rem;\\n  max-height: calc(100vh - 280px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #374151;\\n  border-color: #d1d5db;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n  background: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #ff6b35;\\n  color: #ff6b35;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0;\\n  gap: 0;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4b5563;\\n  padding: 3px 8px;\\n  border-radius: 0.375rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  gap: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .default-charts-toggle[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%] {\\n  --mdc-switch-selected-track-color: #ff6b35;\\n  --mdc-switch-selected-handle-color: #ffffff;\\n  --mdc-switch-selected-hover-track-color: #e55a2b;\\n  --mdc-switch-selected-focus-track-color: #e55a2b;\\n  --mdc-switch-selected-pressed-track-color: #e55a2b;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .default-charts-toggle[_ngcontent-%COMP%]   .mdc-form-field[_ngcontent-%COMP%] {\\n  color: #374151;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  width: 100%;\\n  max-width: none;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #f9fafb !important;\\n  border-color: #d1d5db !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #d1d5db !important;\\n  box-shadow: none !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n  border: 2px solid #ff6b35;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  border-color: #e55a2b;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  background: #d1d5db;\\n  border-color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #ff6b35;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%] {\\n  padding: 1rem 1rem 0.75rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #4b5563;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  align-content: start;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header {\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header h3 {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container canvas {\\n  max-width: 100%;\\n  height: 300px !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    align-self: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 6px 8px !important;\\n  min-height: 18px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 2px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 36px !important;\\n  line-height: 1.2 !important;\\n  padding: 6px 8px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:first-child {\\n  margin-top: 0 !important;\\n  padding-top: 4px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-select-panel {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .select-all-controls {\\n  padding: 4px 8px 6px 8px !important;\\n  border-bottom: 1px solid #e0e0e0 !important;\\n  background: #f8f9fa !important;\\n  margin: 0 !important;\\n  border-radius: 0 !important;\\n}\\n  .select-all-controls .select-toggle-btn {\\n  width: 100% !important;\\n  height: 26px !important;\\n  font-size: 0.75rem !important;\\n  padding: 0 !important;\\n  border-radius: 3px !important;\\n  font-weight: 600 !important;\\n  background: rgba(255, 107, 53, 0.1) !important;\\n  color: #ff6b35 !important;\\n  border: 1px solid rgba(255, 107, 53, 0.2) !important;\\n  transition: all 0.2s ease !important;\\n  min-height: 26px !important;\\n  line-height: 26px !important;\\n}\\n  .select-all-controls .select-toggle-btn:hover {\\n  background: rgba(255, 107, 53, 0.15) !important;\\n  border-color: rgba(255, 107, 53, 0.3) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatSlideToggleModule", "FormsModule", "ReactiveFormsModule", "FormControl", "first", "takeUntil", "startWith", "map", "Subject", "Chart", "registerables", "NgxMatSelectSearchModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r11", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r10", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "selectedLocations", "length", "location_r12", "value", "baseDate_r13", "displayName", "ctx_r6", "getLoadingMessage", "title", "description", "ctx_r7", "getEmptyStateMessage", "item_r15", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_110_div_2_Template", "ctx_r8", "getSummaryItems", "ɵɵelement", "register", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "smartDashboardService", "cdr", "destroy$", "tabs", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "dashboardData", "charts", "isLoading", "dashboardConfig", "useDefaultCharts", "locationFilterCtrl", "allLocationsSelected", "ngOnInit", "user", "getCurrentUser", "setDefaultDates", "initializeComponent", "setupLocationFilter", "detectChanges", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "setDefaultTabs", "setDefaultBaseDates", "loadDashboardConfig", "loadLocations", "dashboardData$", "pipe", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON>", "loading$", "loading", "getDashboardConfig", "response", "status", "error", "getDefaultDashboardTabs", "active", "getDefaultBaseDateOptions", "today", "Date", "year", "getFullYear", "month", "getMonth", "firstDayOfMonth", "formatDateForInput", "date", "String", "padStart", "day", "getDate", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "clearDashboardData", "onDefaultChartsToggle", "sendMessage", "areAllFiltersValid", "generateDashboard", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "tenantId", "getLocations", "res", "result", "branches", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "location", "resetFilters", "updateLocationStates", "setValue", "filters", "baseDate", "validateFilters", "filteredLocations", "valueChanges", "filterLocations", "filterValue", "toLowerCase", "filter", "includes", "toggleAllLocations", "isAllSelected", "onLocationSelectionChange", "<PERSON><PERSON><PERSON><PERSON>", "currentTab", "dashboardType", "formatDateValue", "dateValue", "request", "user_query", "dashboard_type", "tenant_id", "use_default_charts", "chart", "destroy", "chartsContainer", "nativeElement", "innerHTML", "setTimeout", "chartConfig", "createChart", "summary_items", "canvas", "document", "createElement", "width", "height", "chartContainer", "className", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "chartOptions", "getChartConfig", "type", "mergedOptions", "options", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "i3", "SmartDashboardService", "ChangeDetectorRef", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_27_listener", "SmartDashboardComponent_Template_button_click_31_listener", "SmartDashboardComponent_mat_option_33_Template", "SmartDashboardComponent_Template_mat_select_valueChange_42_listener", "SmartDashboardComponent_mat_option_43_Template", "SmartDashboardComponent_Template_input_ngModelChange_51_listener", "SmartDashboardComponent_Template_input_ngModelChange_62_listener", "SmartDashboardComponent_Template_button_click_67_listener", "SmartDashboardComponent_Template_mat_slide_toggle_ngModelChange_94_listener", "SmartDashboardComponent_Template_mat_slide_toggle_change_94_listener", "SmartDashboardComponent_Template_input_ngModelChange_102_listener", "SmartDashboardComponent_Template_input_keydown_102_listener", "SmartDashboardComponent_Template_button_click_103_listener", "SmartDashboardComponent_div_108_Template", "SmartDashboardComponent_div_109_Template", "SmartDashboardComponent_div_110_Template", "SmartDashboardComponent_div_111_Template", "ɵɵpipeBind1", "_r4", "_r5", "ɵɵclassProp", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "MatSuffix", "i8", "MatSelect", "MatSelectTrigger", "i9", "MatOption", "i10", "MatInput", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "MatSlideToggle", "i13", "DefaultValueAccessor", "NgControlStatus", "NgModel", "FormControlDirective", "i14", "MatSelectSearchComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, OnDestroy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { SmartDashboardService, DashboardTab, BaseDate, DashboardFilters, DashboardData, SummaryItem } from '../../services/smart-dashboard.service';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, ChartType, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatSlideToggleModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NgxMatSelectSearchModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('chartsContainer', { static: false }) chartsContainer!: ElementRef;\n\n  private destroy$ = new Subject<void>();\n\n  tabs: DashboardTab[] = [];\n  selectedTab = 0;\n  user: any;\n  locations: any[] = [];\n  baseDates: BaseDate[] = [];\n  selectedLocations: string[] = [];\n  selectedBaseDate = '';\n  startDate: string | Date = '';\n  endDate: string | Date = '';\n  chatMessage = '';\n  dashboardData: DashboardData | null = null;\n  charts: Chart[] = [];\n  isLoading = false;\n  dashboardConfig: any = null;\n  useDefaultCharts = false;\n\n  locationFilterCtrl = new FormControl();\n  filteredLocations: Observable<any[]>;\n  allLocationsSelected = true;\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService,\n    private smartDashboardService: SmartDashboardService,\n    private cdr: ChangeDetectorRef\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n\n  ngAfterViewInit(): void {}\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n\n  private initializeComponent(): void {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n\n    this.smartDashboardService.dashboardData$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.dashboardData = data;\n        if (data) {\n          this.renderCharts();\n        }\n        this.cdr.detectChanges();\n      });\n\n    this.smartDashboardService.loading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n        this.cdr.detectChanges();\n      });\n  }\n\n\n\n  private loadDashboardConfig(): void {\n    this.smartDashboardService.getDashboardConfig()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.dashboardConfig = response.data;\n          }\n        },\n        error: () => {}\n      });\n  }\n\n  private setDefaultTabs(): void {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n\n  private setDefaultBaseDates(): void {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n\n  private setDefaultDates(): void {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n\n  onDefaultChartsToggle(): void {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n    }\n  }\n\n  sendMessage(): void {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: true\n              }));\n              this.selectedLocations = this.locations.map(location => location.value);\n              this.setupLocationFilter();\n            } else {\n              this.locations = [];\n              this.selectedLocations = [];\n            }\n            this.cdr.detectChanges();\n          },\n          error: () => {\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n\n\n\n  getDashboardDescription(index: number): string {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n\n  areAllFiltersValid(): boolean {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n\n  setupLocationFilter(): void {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(\n      startWith(''),\n      map(value => this.filterLocations(value || ''))\n    );\n  }\n\n  private filterLocations(value: string): any[] {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location =>\n      location.label.toLowerCase().includes(filterValue)\n    );\n  }\n\n  toggleAllLocations(): void {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  onLocationSelectionChange(selectedValues: any[]): void {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  private updateLocationStates(): void {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n\n  isAllSelected(): boolean {\n    return this.selectedLocations.length === this.locations.length;\n  }\n\n  getLoadingMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n\n  getEmptyStateMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n\n  generateDashboard(): void {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n\n    // Helper function to convert Date to string format\n    const formatDateValue = (dateValue: any): string => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n\n    this.clearCharts();\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n\n    this.smartDashboardService.generateDashboard(request)\n      .pipe(first())\n      .subscribe({\n        next: () => {},\n        error: () => {}\n      });\n  }\n\n  clearCharts(): void {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n\n  renderCharts(): void {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n\n    this.clearCharts();\n\n    setTimeout(() => {\n      this.dashboardData.charts.forEach((chartConfig: any) => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n\n  getSummaryItems(): SummaryItem[] {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n\n    return this.dashboardData.summary_items;\n  }\n\n  createChart(chartConfig: any): void {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n\n      const chart = new Chart(ctx, {\n        type: chartConfig.type as ChartType,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n\n      this.charts.push(chart);\n    }\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants *</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select\n              [(ngModel)]=\"selectedLocations\"\n              multiple\n              placeholder=\"Select restaurants\"\n              (selectionChange)=\"onLocationSelectionChange($event.value)\">\n\n              <!-- Search -->\n              <mat-option>\n                <ngx-mat-select-search\n                  placeholderLabel=\"Search restaurants...\"\n                  noEntriesFoundLabel=\"No restaurants found\"\n                  [formControl]=\"locationFilterCtrl\">\n                </ngx-mat-select-search>\n              </mat-option>\n\n              <!-- Select All Control -->\n              <div class=\"select-all-controls\">\n                <button\n                  mat-button\n                  class=\"select-toggle-btn\"\n                  (click)=\"toggleAllLocations()\">\n                  {{ isAllSelected() ? 'Deselect All' : 'Select All' }}\n                </button>\n              </div>\n\n              <!-- Location Options -->\n              <mat-option\n                *ngFor=\"let location of filteredLocations | async\"\n                [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Digi AI Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"areAllFiltersValid()\">\n            {{ areAllFiltersValid() ? 'Ready to analyze' : 'Please fill all required filters' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <!-- Mode Selection Card -->\n          <div class=\"mode-selection-card\">\n            <div class=\"mode-header\">\n              <mat-icon class=\"mode-icon\">tune</mat-icon>\n              <span class=\"mode-title\">Dashboard Mode</span>\n            </div>\n            <div class=\"toggle-wrapper\">\n              <div class=\"toggle-option\" [class.active]=\"!useDefaultCharts\">\n                <mat-icon class=\"option-icon\">chat</mat-icon>\n                <span class=\"option-label\">Custom Query</span>\n              </div>\n              <mat-slide-toggle\n                [(ngModel)]=\"useDefaultCharts\"\n                (change)=\"onDefaultChartsToggle()\"\n                color=\"primary\"\n                class=\"mode-toggle\">\n              </mat-slide-toggle>\n              <div class=\"toggle-option\" [class.active]=\"useDefaultCharts\">\n                <mat-icon class=\"option-icon\">dashboard</mat-icon>\n                <span class=\"option-label\">Default Charts</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- AI Input Container -->\n          <div class=\"ai-input-container\" [class.disabled-mode]=\"useDefaultCharts\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                [placeholder]=\"useDefaultCharts ? 'Default charts mode - input disabled' : 'Ask me about your business data...'\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"!areAllFiltersValid() || useDefaultCharts\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"useDefaultCharts ? !areAllFiltersValid() : (!chatMessage.trim() || !areAllFiltersValid())\"\n              [matTooltip]=\"useDefaultCharts ? 'Generate Default Dashboard' : 'Send Custom Query'\"\n            >\n              <mat-icon>{{ useDefaultCharts ? 'auto_awesome' : 'send' }}</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-content\">\n        <!-- Loading State -->\n        <div class=\"loading-state\" *ngIf=\"isLoading\">\n          <div class=\"loading-content\">\n            <div class=\"loading-spinner\">\n              <mat-icon class=\"spin\">refresh</mat-icon>\n            </div>\n            <h4 class=\"loading-title\">{{ getLoadingMessage().title }}</h4>\n            <p class=\"loading-description\">\n              {{ getLoadingMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!isLoading && !dashboardData\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">{{ getEmptyStateMessage().title }}</h4>\n            <p class=\"empty-state-description\">\n              {{ getEmptyStateMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Dashboard Summary -->\n        <div class=\"dashboard-summary\" *ngIf=\"!isLoading && dashboardData?.summary_items\">\n          <div class=\"summary-cards\">\n            <div class=\"summary-card\" *ngFor=\"let item of getSummaryItems()\">\n              <div class=\"summary-icon\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n              </div>\n              <div class=\"summary-content\">\n                <div class=\"summary-value\">{{ item.value }}</div>\n                <div class=\"summary-label\">{{ item.label }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"!isLoading && dashboardData\" #chartsContainer>\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AAErE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAI9E,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjE,SAASC,OAAO,QAAoB,MAAM;AAC1C,SAASC,KAAK,EAAaC,aAAa,QAAQ,UAAU;AAC1D,SAASC,wBAAwB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;;ICLtDC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,KAAA,EAAgC;;;;;IAwBjEL,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IA6BEf,EAAA,CAAAC,cAAA,qBAE2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFXH,EAAA,CAAAI,UAAA,UAAAY,YAAA,CAAAC,KAAA,CAAwB;IACxBjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAI,YAAA,CAAAP,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAD,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,YAAA,CAAAC,WAAA,MACF;;;;;IA0HNnB,EAAA,CAAAC,cAAA,cAA6C;IAGhBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHsBH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,iBAAA,CAAAa,MAAA,CAAAC,iBAAA,GAAAC,KAAA,CAA+B;IAEvDtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,MAAA,CAAAC,iBAAA,GAAAE,WAAA,MACF;;;;;IAKJvB,EAAA,CAAAC,cAAA,cAA8D;IAG9CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAH0BH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,iBAAA,CAAAiB,MAAA,CAAAC,oBAAA,GAAAH,KAAA,CAAkC;IAE9DtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAY,MAAA,CAAAC,oBAAA,GAAAF,WAAA,MACF;;;;;IAOAvB,EAAA,CAAAC,cAAA,cAAiE;IAEnDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJvCH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAC,IAAA,CAAe;IAGE3B,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAT,KAAA,CAAgB;IAChBjB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAjB,KAAA,CAAgB;;;;;IARnDT,EAAA,CAAAC,cAAA,cAAkF;IAE9ED,EAAA,CAAA4B,UAAA,IAAAC,8CAAA,kBAQM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IATuCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAC,eAAA,GAAoB;;;;;IAanE/B,EAAA,CAAAgC,SAAA,kBAEM;;;AD5OdnC,KAAK,CAACoC,QAAQ,CAAC,GAAGnC,aAAa,CAAC;AAEhC,MAqBaoC,uBAAuB;EAyBlCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,qBAA4C,EAC5CC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IA1BL,KAAAC,QAAQ,GAAG,IAAI5C,OAAO,EAAQ;IAEtC,KAAA6C,IAAI,GAAmB,EAAE;IACzB,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA9B,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,EAAE;IACrB,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,OAAO,GAAkB,EAAE;IAC3B,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IAExB,KAAAC,kBAAkB,GAAG,IAAI/D,WAAW,EAAE;IAEtC,KAAAgE,oBAAoB,GAAG,IAAI;EAOvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IAC7C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACtB,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA,GAAU;EAEzBC,WAAWA,CAAA;IACT,IAAI,CAACxB,QAAQ,CAACyB,IAAI,EAAE;IACpB,IAAI,CAACzB,QAAQ,CAAC0B,QAAQ,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,cAAc,EAAE;IACrB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAACjC,qBAAqB,CAACkC,cAAc,CACtCC,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAC1B,aAAa,GAAG0B,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,YAAY,EAAE;;MAErB,IAAI,CAACrC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACxB,qBAAqB,CAACuC,QAAQ,CAChCJ,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC3B,SAAS,GAAG2B,OAAO;MACxB,IAAI,CAACvC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACN;EAIQQ,mBAAmBA,CAAA;IACzB,IAAI,CAAChC,qBAAqB,CAACyC,kBAAkB,EAAE,CAC5CN,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC7B,eAAe,GAAG4B,QAAQ,CAACL,IAAI;;MAExC,CAAC;MACDO,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEQd,cAAcA,CAAA;IACpB,IAAI,CAAC3B,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAAC6C,uBAAuB,EAAE;IAChE,IAAI,IAAI,CAAC1C,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC2C,MAAM,GAAG,IAAI;;EAE9B;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAACzB,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAAC+C,yBAAyB,EAAE;IACvE,IAAI,IAAI,CAACzC,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;EAEnD;EAEQ0C,eAAeA,CAAA;IACrB,MAAM2B,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,EAAE;IAChC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,QAAQ,EAAE;IAC9B,MAAMC,eAAe,GAAG,IAAIL,IAAI,CAACC,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC5C,SAAS,GAAG,IAAI,CAAC+C,kBAAkB,CAACD,eAAe,CAAC;IACzD,IAAI,CAAC7C,OAAO,GAAG,IAAI,CAAC8C,kBAAkB,CAACP,KAAK,CAAC;EAC/C;EAEQO,kBAAkBA,CAACC,IAAU;IACnC,MAAMN,IAAI,GAAGM,IAAI,CAACL,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGK,MAAM,CAACD,IAAI,CAACH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGR,IAAI,IAAIE,KAAK,IAAIO,GAAG,EAAE;EAClC;EAEAE,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC1D,WAAW,GAAG0D,KAAK;IACxB,IAAI,CAAC3D,IAAI,CAAC4D,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAAClB,MAAM,GAAGmB,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC9D,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;EACpB;EAEAsC,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACnE,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAElB;IACA,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACzB,IAAI,CAACL,WAAW,GAAG,EAAE;;EAEzB;EAEA0D,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrD,gBAAgB,EAAE;MACzB;MACA,IAAI,IAAI,CAACsD,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,MAAM;MACL;MACA,IAAI,IAAI,CAAC5D,WAAW,CAAC6D,IAAI,EAAE,IAAI,IAAI,CAACF,kBAAkB,EAAE,EAAE;QACxD,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAAC5D,WAAW,GAAG,EAAE;;;EAG3B;EAEA8D,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAIAnC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACd,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC0D,QAAQ,EAAE;MACnC,IAAI,CAAC9E,gBAAgB,CAAC+E,YAAY,CAAC,IAAI,CAAC3D,IAAI,CAAC0D,QAAQ,CAAC,CACnD1C,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;QACTT,IAAI,EAAGoD,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAC5E,SAAS,GAAG0E,GAAG,CAACE,QAAQ,CAAC5H,GAAG,CAAE6H,MAAW,KAAM;cAClDvG,KAAK,EAAEuG,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjElH,KAAK,EAAE+G,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;YACH,IAAI,CAAChH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACoI,QAAQ,IAAIA,QAAQ,CAAC9G,KAAK,CAAC;YACvE,IAAI,CAAC4C,mBAAmB,EAAE;WAC3B,MAAM;YACL,IAAI,CAAClB,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC7B,iBAAiB,GAAG,EAAE;;UAE7B,IAAI,CAACyB,GAAG,CAACuB,aAAa,EAAE;QAC1B,CAAC;QACDoB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvC,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEAqF,YAAYA,CAAA;IACV,IAAI,CAAClH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACoI,QAAQ,IAAIA,QAAQ,CAAC9G,KAAK,CAAC;IACvE,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC7B,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK,GAAG,EAAE;IAChF,IAAI,CAAC0C,eAAe,EAAE;IACtB,IAAI,CAACsE,oBAAoB,EAAE;IAC3B,IAAI,CAAC3E,kBAAkB,CAAC4E,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAAC5F,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAClB,IAAI,CAAC5B,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAIAnD,uBAAuBA,CAACyF,KAAa;IACnC,IAAI,IAAI,CAAC3D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC2D,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI,CAAC3D,IAAI,CAAC2D,KAAK,CAAC,CAAC7E,WAAW;;IAErC,OAAO,8BAA8B;EACvC;EAEAoF,kBAAkBA,CAAA;IAChB,MAAMwB,OAAO,GAAG;MACdxF,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCsH,QAAQ,EAAE,IAAI,CAACvF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IACD,OAAO,IAAI,CAACT,qBAAqB,CAAC+F,eAAe,CAACF,OAAO,CAAC;EAC5D;EAEAtE,mBAAmBA,CAAA;IACjB,IAAI,CAACyE,iBAAiB,GAAG,IAAI,CAAChF,kBAAkB,CAACiF,YAAY,CAAC9D,IAAI,CAChE/E,SAAS,CAAC,EAAE,CAAC,EACbC,GAAG,CAACsB,KAAK,IAAI,IAAI,CAACuH,eAAe,CAACvH,KAAK,IAAI,EAAE,CAAC,CAAC,CAChD;EACH;EAEQuH,eAAeA,CAACvH,KAAa;IACnC,IAAI,CAAC,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClD,OAAO,EAAE;;IAEX,MAAM0H,WAAW,GAAGxH,KAAK,CAACyH,WAAW,EAAE;IACvC,OAAO,IAAI,CAAC/F,SAAS,CAACgG,MAAM,CAACZ,QAAQ,IACnCA,QAAQ,CAACtH,KAAK,CAACiI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACnD;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,aAAa,EAAE,EAAE;MACxB,IAAI,CAAChI,iBAAiB,GAAG,EAAE;KAC5B,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAACoI,QAAQ,IAAIA,QAAQ,CAAC9G,KAAK,CAAC,CAAC;;IAE9E,IAAI,CAACgH,oBAAoB,EAAE;IAC3B,IAAI,CAAC1F,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAiF,yBAAyBA,CAACC,cAAqB;IAC7C,IAAI,CAAClI,iBAAiB,GAAGkI,cAAc;IACvC,IAAI,CAACf,oBAAoB,EAAE;IAC3B,IAAI,CAAC1F,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEQmE,oBAAoBA,CAAA;IAC1B,IAAI,CAACtF,SAAS,CAAC0D,OAAO,CAAC0B,QAAQ,IAAG;MAChCA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAAChH,iBAAiB,CAAC8H,QAAQ,CAACb,QAAQ,CAAC9G,KAAK,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACsC,oBAAoB,GAAG,IAAI,CAACzC,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EACrF;EAEA+H,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChI,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EAChE;EAEAM,iBAAiBA,CAAA;IACf,MAAM4H,UAAU,GAAG,IAAI,CAACxG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMwG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAChI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACjB,iBAAiB,CAAC6H,aAAa,CAAC;EACpE;EAEAzH,oBAAoBA,CAAA;IAClB,MAAMwH,UAAU,GAAG,IAAI,CAACxG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMwG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAChI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACb,oBAAoB,CAACyH,aAAa,CAAC;EACvE;EAEAtC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE,EAAE;MAC9B;;IAGF;IACA,MAAMwC,eAAe,GAAIC,SAAc,IAAY;MACjD,IAAIA,SAAS,YAAY7D,IAAI,EAAE;QAC7B,OAAO,IAAI,CAACM,kBAAkB,CAACuD,SAAS,CAAC;;MAE3C,OAAOA,SAAS,IAAI,EAAE;IACxB,CAAC;IAED,MAAMjB,OAAO,GAAqB;MAChCxF,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCsH,QAAQ,EAAE,IAAI,CAACvF,gBAAgB;MAC/BC,SAAS,EAAEqG,eAAe,CAAC,IAAI,CAACrG,SAAS,CAAC;MAC1CC,OAAO,EAAEoG,eAAe,CAAC,IAAI,CAACpG,OAAO;KACtC;IAED,IAAI,CAACoB,WAAW,EAAE;IAElB,MAAM8E,UAAU,GAAG,IAAI,CAACxG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMwG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAChI,KAAK,GAAG,UAAU;IAEhE,MAAMoI,OAAO,GAAG;MACdlB,OAAO,EAAEA,OAAO;MAChBmB,UAAU,EAAE,IAAI,CAACtG,WAAW;MAC5BuG,cAAc,EAAEL,aAAa;MAC7BM,SAAS,EAAE,IAAI,CAAC/F,IAAI,EAAE0D,QAAQ,IAAI,EAAE;MACpCsC,kBAAkB,EAAE,IAAI,CAACpG;KAC1B;IAED,IAAI,CAACf,qBAAqB,CAACsE,iBAAiB,CAACyC,OAAO,CAAC,CAClD5E,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdiB,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEAf,WAAWA,CAAA;IACT,IAAI,CAACjB,MAAM,CAACmD,OAAO,CAACqD,KAAK,IAAG;MAC1B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;IACF,IAAI,CAACzG,MAAM,GAAG,EAAE;IAEhB,IAAI,IAAI,CAAC0G,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;;EAErD;EAEAlF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC3B,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,EAAE;MACrD;;IAGF,IAAI,CAACiB,WAAW,EAAE;IAElB4F,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9G,aAAa,CAACC,MAAM,CAACmD,OAAO,CAAE2D,WAAgB,IAAI;QACrD,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAjI,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAEiH,aAAa,EAAE;MACtC,OAAO,EAAE;;IAGX,OAAO,IAAI,CAACjH,aAAa,CAACiH,aAAa;EACzC;EAEAD,WAAWA,CAACD,WAAgB;IAC1B,MAAMG,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACxC,EAAE,GAAG,SAASqC,WAAW,CAACrC,EAAE,EAAE;IACrCwC,MAAM,CAACG,KAAK,GAAG,GAAG;IAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;IAEnB,MAAMC,cAAc,GAAGJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACpDG,cAAc,CAACC,SAAS,GAAG,iBAAiB;IAC5CD,cAAc,CAACV,SAAS,GAAG;;cAEjBE,WAAW,CAAC1I,KAAK;;KAE1B;IACDkJ,cAAc,CAACE,WAAW,CAACP,MAAM,CAAC;IAElC,IAAI,IAAI,CAACP,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACa,WAAW,CAACF,cAAc,CAAC;;IAGhE,MAAMG,GAAG,GAAGR,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;IACnC,IAAID,GAAG,EAAE;MACP,MAAME,YAAY,GAAG,IAAI,CAACvI,qBAAqB,CAACwI,cAAc,CAACd,WAAW,CAACe,IAAI,CAAC;MAEhF,MAAMC,aAAa,GAAG;QACpB,GAAGH,YAAY;QACf,IAAIb,WAAW,CAACiB,OAAO,IAAI,EAAE;OAC9B;MAED,MAAMvB,KAAK,GAAG,IAAI7J,KAAK,CAAC8K,GAAG,EAAE;QAC3BI,IAAI,EAAEf,WAAW,CAACe,IAAiB;QACnCpG,IAAI,EAAEqF,WAAW,CAACrF,IAAI;QACtBsG,OAAO,EAAED;OACV,CAAC;MAEF,IAAI,CAAC9H,MAAM,CAACgI,IAAI,CAACxB,KAAK,CAAC;;EAE3B;;;uBAlYWxH,uBAAuB,EAAAlC,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAvL,EAAA,CAAAmL,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAAzL,EAAA,CAAAmL,iBAAA,CAAAnL,EAAA,CAAA0L,iBAAA;IAAA;EAAA;;;YAAvBxJ,uBAAuB;MAAAyJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAnB,GAAA;QAAA,IAAAmB,EAAA;;;;;;;;;;;;;;;UC5CpC9L,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAA+L,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAtB,GAAA,CAAAjI,WAAA,GAAAuJ,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBtB,GAAA,CAAAxE,WAAA,CAAA8F,MAAA,CAAAhL,KAAA,CAAyB;UAAA,EAA7C;UACjCjB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAA4B,UAAA,KAAAuK,8CAAA,wBAOa;UACfnM,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIhDH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAA4B,UAAA,KAAAwK,wCAAA,mBAEO;UACTpM,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAEtDD,EAAA,CAAA+L,UAAA,2BAAAM,sEAAAJ,MAAA;YAAA,OAAAtB,GAAA,CAAA7J,iBAAA,GAAAmL,MAAA;UAAA,EAA+B,6BAAAK,wEAAAL,MAAA;YAAA,OAGZtB,GAAA,CAAA5B,yBAAA,CAAAkD,MAAA,CAAAhL,KAAA,CAAuC;UAAA,EAH3B;UAM/BjB,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAgC,SAAA,iCAIwB;UAC1BhC,EAAA,CAAAG,YAAA,EAAa;UAGbH,EAAA,CAAAC,cAAA,eAAiC;UAI7BD,EAAA,CAAA+L,UAAA,mBAAAQ,0DAAA;YAAA,OAAS5B,GAAA,CAAA9B,kBAAA,EAAoB;UAAA,EAAC;UAC9B7I,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAA4B,UAAA,KAAA4K,8CAAA,wBAIa;;UACfxM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA+L,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAtB,GAAA,CAAA9H,gBAAA,GAAAoJ,MAAA;UAAA,EAA4B;UACtCjM,EAAA,CAAA4B,UAAA,KAAA8K,8CAAA,wBAEa;UACf1M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA+L,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAtB,GAAA,CAAA7H,SAAA,GAAAmJ,MAAA;UAAA,EAAuB;UAHzBjM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA6E;UAE/EhC,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA+L,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAtB,GAAA,CAAA5H,OAAA,GAAAkJ,MAAA;UAAA,EAAqB;UAHvBjM,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA2E;UAE7EhC,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA+L,UAAA,mBAAAc,0DAAA;YAAA,OAASlC,GAAA,CAAA3C,YAAA,EAAc;UAAA,EAAC;UACnEhI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtDH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,eAA6B;UAIKD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhDH,EAAA,CAAAC,cAAA,eAA4B;UAEMD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhDH,EAAA,CAAAC,cAAA,4BAIsB;UAHpBD,EAAA,CAAA+L,UAAA,2BAAAe,4EAAAb,MAAA;YAAA,OAAAtB,GAAA,CAAAtH,gBAAA,GAAA4I,MAAA;UAAA,EAA8B,oBAAAc,qEAAA;YAAA,OACpBpC,GAAA,CAAAlE,qBAAA,EAAuB;UAAA,EADH;UAIhCzG,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAC,cAAA,eAA6D;UAC7BD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMtDH,EAAA,CAAAC,cAAA,gBAAyE;UAMnED,EAAA,CAAA+L,UAAA,2BAAAiB,kEAAAf,MAAA;YAAA,OAAAtB,GAAA,CAAA3H,WAAA,GAAAiJ,MAAA;UAAA,EAAyB,qBAAAgB,4DAAAhB,MAAA;YAAA,OACdtB,GAAA,CAAA7D,UAAA,CAAAmF,MAAA,CAAkB;UAAA,EADJ;UAJ3BjM,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,mBAOC;UAHCD,EAAA,CAAA+L,UAAA,mBAAAmB,2DAAA;YAAA,OAASvC,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAIvB1G,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,KAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQ/EH,EAAA,CAAAC,cAAA,gBAA+B;UAG3BD,EAAA,CAAA4B,UAAA,MAAAuL,wCAAA,kBAUM;UAGNnN,EAAA,CAAA4B,UAAA,MAAAwL,wCAAA,kBAUM;UAGNpN,EAAA,CAAA4B,UAAA,MAAAyL,wCAAA,kBAYM;UAGNrN,EAAA,CAAA4B,UAAA,MAAA0L,wCAAA,kBAEM;UACRtN,EAAA,CAAAG,YAAA,EAAM;;;;;UA5PQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAjI,WAAA,CAAuB;UAIC1C,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAAoK,GAAA,CAAAlI,IAAA,CAAAkI,GAAA,CAAAjI,WAAA,mBAAAiI,GAAA,CAAAlI,IAAA,CAAAkI,GAAA,CAAAjI,WAAA,EAAAjC,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAAlI,IAAA,CAAS;UA4BJzC,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAA7J,iBAAA,CAAAC,MAAA,KAAkC;UAM/Df,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAA7J,iBAAA,CAA+B;UAU3Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAuK,GAAA,CAAArH,kBAAA,CAAkC;UAUlCtD,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAA+J,GAAA,CAAA7B,aAAA,wCACF;UAKqB9I,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuN,WAAA,SAAA5C,GAAA,CAAArC,iBAAA,EAA4B;UAezCtI,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAA9H,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAA/H,SAAA,CAAY;UAgB7C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAAoN,GAAA,CAA6B,YAAA7C,GAAA,CAAA7H,SAAA;UAKE9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAAoN,GAAA,CAAmB;UAclDxN,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAAqN,GAAA,CAA2B,YAAA9C,GAAA,CAAA5H,OAAA;UAKI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAAqN,GAAA,CAAiB;UAwBrBzN,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAA0N,WAAA,UAAA/C,GAAA,CAAAhE,kBAAA,GAAoC;UACjE3G,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAA+J,GAAA,CAAAhE,kBAAA,kEACF;UAU+B3G,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAA0N,WAAA,YAAA/C,GAAA,CAAAtH,gBAAA,CAAkC;UAK3DrD,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAAtH,gBAAA,CAA8B;UAKLrD,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAA0N,WAAA,WAAA/C,GAAA,CAAAtH,gBAAA,CAAiC;UAQhCrD,EAAA,CAAAM,SAAA,GAAwC;UAAxCN,EAAA,CAAA0N,WAAA,kBAAA/C,GAAA,CAAAtH,gBAAA,CAAwC;UAKlErD,EAAA,CAAAM,SAAA,GAAgH;UAAhHN,EAAA,CAAAI,UAAA,gBAAAuK,GAAA,CAAAtH,gBAAA,iFAAgH,YAAAsH,GAAA,CAAA3H,WAAA,eAAA2H,GAAA,CAAAhE,kBAAA,MAAAgE,GAAA,CAAAtH,gBAAA;UAWlHrD,EAAA,CAAAM,SAAA,GAAsG;UAAtGN,EAAA,CAAAI,UAAA,aAAAuK,GAAA,CAAAtH,gBAAA,IAAAsH,GAAA,CAAAhE,kBAAA,MAAAgE,GAAA,CAAA3H,WAAA,CAAA6D,IAAA,OAAA8D,GAAA,CAAAhE,kBAAA,GAAsG,eAAAgE,GAAA,CAAAtH,gBAAA;UAG5FrD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAO,iBAAA,CAAAoK,GAAA,CAAAtH,gBAAA,2BAAgD;UAWpCrD,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAAxH,SAAA,CAAe;UAajBnD,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,KAAAwH,GAAA,CAAA1H,aAAA,CAAkC;UAa5BjD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,KAAAwH,GAAA,CAAA1H,aAAA,kBAAA0H,GAAA,CAAA1H,aAAA,CAAAiH,aAAA,EAAgD;UAejDlK,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAAuK,GAAA,CAAAxH,SAAA,IAAAwH,GAAA,CAAA1H,aAAA,CAAiC;;;qBDpOpEtE,YAAY,EAAAgP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,SAAA,EACZlP,aAAa,EACbC,eAAe,EAAAkP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfnP,aAAa,EAAAoP,EAAA,CAAAC,OAAA,EACbpP,kBAAkB,EAAAqP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBtP,eAAe,EAAAuP,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACf1P,cAAc,EAAA2P,GAAA,CAAAC,QAAA,EACd3P,mBAAmB,EAAA4P,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB9P,mBAAmB,EACnBC,oBAAoB,EAAA8P,GAAA,CAAAC,cAAA,EACpB9P,WAAW,EAAA+P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACXjQ,mBAAmB,EAAA8P,GAAA,CAAAI,oBAAA,EACnBzP,wBAAwB,EAAA0P,GAAA,CAAAC,wBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKfzN,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}