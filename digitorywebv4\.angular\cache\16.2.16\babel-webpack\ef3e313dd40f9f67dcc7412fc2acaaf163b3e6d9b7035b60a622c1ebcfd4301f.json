{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction ChatBotComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.connectToSse());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_mat_spinner_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 19);\n  }\n}\nfunction ChatBotComponent_div_14_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"smart_toy\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_14_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, ChatBotComponent_div_14_mat_icon_2_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵtemplate(3, ChatBotComponent_div_14_mat_icon_3_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"div\", 24);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c0, message_r6.sender === \"user\", message_r6.sender === \"bot\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r6.sender === \"bot\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r6.sender === \"user\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r6.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, message_r6.timestamp, \"shortTime\"));\n  }\n}\nfunction ChatBotComponent_mat_spinner_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"connected\": a0,\n    \"disconnected\": a1\n  };\n};\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnected = false;\n    this.isConnecting = false;\n    this.isSubmitting = false;\n    this.restaurantInfo = {};\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n  }\n  ngOnInit() {\n    // Initialize with a welcome message\n    this.messages = [{\n      id: this.generateId(),\n      text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n      sender: 'bot',\n      timestamp: new Date()\n    }];\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Check if we already have a message with the same ID to avoid duplicates\n      if (!this.messages.some(m => m.id === message.id)) {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      }\n    });\n    // Subscribe to connection status\n    this.connectionSubscription = this.sseService.connectionStatus$.subscribe(connected => {\n      this.isConnected = connected;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      if (connected) {\n        this.snackBar.open('Connected to chat service', 'Close', {\n          duration: 3000\n        });\n      } else {\n        this.snackBar.open('Disconnected from chat service', 'Close', {\n          duration: 3000\n        });\n      }\n    });\n    // Connect to the SSE endpoint\n    this.connectToSse();\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  /**\n   * Connect to the SSE endpoint\n   */\n  connectToSse() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to connect', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    this.sseService.connect(this.tenantId).subscribe({\n      error: error => {\n        console.error('Failed to connect to SSE:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to connect to chat service', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.connectToSse();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.isConnected) {\n      this.snackBar.open('Not connected to chat service', 'Retry', {\n        duration: 3000\n      }).onAction().subscribe(() => {\n        this.connectToSse();\n      });\n      return;\n    }\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      error: error => {\n        console.error('Error sending message:', error);\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.currentMessage = messageToSend;\n          this.cd.detectChanges();\n        });\n      }\n    });\n  }\n  /**\n   * Submit the collected restaurant information\n   */\n  submitInfo() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to submit information', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Extract restaurant information from chat history\n    this.extractRestaurantInfo();\n    this.isSubmitting = true;\n    this.cd.detectChanges();\n    // Prepare restaurant info with tenant details\n    const restaurantData = {\n      ...this.restaurantInfo,\n      tenantId: this.tenantId,\n      tenantName: this.tenantName\n    };\n    this.sseService.submitRestaurantInfo(this.tenantId, restaurantData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.snackBar.open('Restaurant information submitted successfully', 'Close', {\n          duration: 3000\n        });\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error submitting restaurant info:', error);\n        this.isSubmitting = false;\n        this.snackBar.open('Failed to submit restaurant information', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.submitInfo();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Extract restaurant information from chat history\n   * This is a simple implementation - in a real app, you might want to use\n   * more sophisticated NLP techniques or structured data collection\n   */\n  extractRestaurantInfo() {\n    // Initialize restaurant info with default values\n    this.restaurantInfo = {\n      tenantId: this.tenantId,\n      location: '',\n      businessType: '',\n      cuisineType: '',\n      operatingHours: '',\n      specialties: [],\n      contactInfo: ''\n    };\n    // Extract information from user messages\n    const userMessages = this.messages.filter(m => m.sender === 'user').map(m => m.text.toLowerCase());\n    // Simple keyword-based extraction\n    for (const message of userMessages) {\n      // Location extraction\n      if (message.includes('location') || message.includes('address') || message.includes('located')) {\n        this.restaurantInfo.location = message;\n      }\n      // Business type extraction\n      if (message.includes('business') || message.includes('restaurant type') || message.includes('establishment')) {\n        this.restaurantInfo.businessType = message;\n      }\n      // Cuisine type extraction\n      if (message.includes('cuisine') || message.includes('food') || message.includes('serve')) {\n        this.restaurantInfo.cuisineType = message;\n      }\n      // Hours extraction\n      if (message.includes('hours') || message.includes('open') || message.includes('close') || message.includes('timing')) {\n        this.restaurantInfo.operatingHours = message;\n      }\n      // Contact info extraction\n      if (message.includes('contact') || message.includes('phone') || message.includes('email') || message.includes('website')) {\n        this.restaurantInfo.contactInfo = message;\n      }\n      // Specialties extraction\n      if (message.includes('special') || message.includes('signature') || message.includes('famous for')) {\n        this.restaurantInfo.specialties = [message];\n      }\n    }\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    setTimeout(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    }, 100);\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 28,\n      vars: 13,\n      consts: [[1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"connection-status\", 3, \"ngClass\"], [1, \"status-dot\"], [1, \"status-text\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Reconnect\", 3, \"click\", 4, \"ngIf\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"chat-actions\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", \"class\", \"submit-spinner\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Reconnect\", 3, \"click\"], [\"diameter\", \"20\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-avatar\"], [4, \"ngIf\"], [1, \"message-content\"], [1, \"message-text\"], [1, \"message-time\"], [\"diameter\", \"20\", 1, \"submit-spinner\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-icon\", 3);\n          i0.ɵɵtext(4, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6, \"Restaurant Information Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵelement(8, \"span\", 5);\n          i0.ɵɵelementStart(9, \"span\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, ChatBotComponent_button_11_Template, 3, 0, \"button\", 7);\n          i0.ɵɵtemplate(12, ChatBotComponent_mat_spinner_12_Template, 1, 0, \"mat-spinner\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 10, 11, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_22_listener() {\n            return ctx.submitInfo();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"Submit Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, ChatBotComponent_mat_spinner_27_Template, 1, 0, \"mat-spinner\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c1, ctx.isConnected, !ctx.isConnected));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.isConnected ? \"Connected\" : \"Disconnected\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isConnected && !ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConnecting);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", !ctx.isConnected || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || !ctx.isConnected || ctx.isSubmitting);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.isConnected || ctx.isSubmitting);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, i9.MatProgressSpinner],\n      styles: [\".chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  max-height: 500px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background-color: #3f51b5;\\n  color: white;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.connection-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 12px;\\n}\\n\\n.status-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 6px;\\n}\\n\\n.connected[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.disconnected[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: #f5f5f5;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  flex-direction: row-reverse;\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-avatar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  margin: 0 8px;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe;\\n  color: #0288d1;\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #388e3c;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 10px 14px;\\n  border-radius: 18px;\\n  position: relative;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-top-right-radius: 4px;\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #757575;\\n  margin-top: 4px;\\n  text-align: right;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 8px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ChatBotComponent_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "connectToSse", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "ChatBotComponent_div_14_mat_icon_2_Template", "ChatBotComponent_div_14_mat_icon_3_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r6", "sender", "ɵɵadvance", "ɵɵtextInterpolate", "text", "ɵɵpipeBind2", "timestamp", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnected", "isConnecting", "isSubmitting", "restaurantInfo", "messageSubscription", "connectionSubscription", "ngOnInit", "id", "generateId", "Date", "messages$", "subscribe", "message", "some", "m", "push", "scrollToBottom", "detectChanges", "connectionStatus$", "connected", "open", "duration", "ngOnDestroy", "unsubscribe", "disconnect", "connect", "error", "console", "onAction", "sendMessage", "trim", "messageToSend", "submitInfo", "extractRestaurantInfo", "restaurantData", "submitRestaurantInfo", "next", "location", "businessType", "cuisineType", "operatingHours", "specialties", "contactInfo", "userMessages", "filter", "map", "toLowerCase", "includes", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "setTimeout", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "Math", "random", "toString", "substring", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_button_11_Template", "ChatBotComponent_mat_spinner_12_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_Template_button_click_22_listener", "ChatBotComponent_mat_spinner_27_Template", "_c1", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatProgressSpinner", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage, RestaurantInfo } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnected: boolean = false;\n  isConnecting: boolean = false;\n  isSubmitting: boolean = false;\n  restaurantInfo: Partial<RestaurantInfo> = {};\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Initialize with a welcome message\n    this.messages = [\n      {\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ];\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Check if we already have a message with the same ID to avoid duplicates\n        if (!this.messages.some(m => m.id === message.id)) {\n          this.messages.push(message);\n          this.scrollToBottom();\n          this.cd.detectChanges();\n        }\n      }\n    );\n\n    // Subscribe to connection status\n    this.connectionSubscription = this.sseService.connectionStatus$.subscribe(\n      (connected: boolean) => {\n        this.isConnected = connected;\n        this.isConnecting = false;\n        this.cd.detectChanges();\n\n        if (connected) {\n          this.snackBar.open('Connected to chat service', 'Close', {\n            duration: 3000\n          });\n        } else {\n          this.snackBar.open('Disconnected from chat service', 'Close', {\n            duration: 3000\n          });\n        }\n      }\n    );\n\n    // Connect to the SSE endpoint\n    this.connectToSse();\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  /**\n   * Connect to the SSE endpoint\n   */\n  connectToSse(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to connect', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    this.sseService.connect(this.tenantId).subscribe({\n      error: (error) => {\n        console.error('Failed to connect to SSE:', error);\n        this.isConnecting = false;\n        this.snackBar.open('Failed to connect to chat service', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.connectToSse();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.isConnected) {\n      this.snackBar.open('Not connected to chat service', 'Retry', {\n        duration: 3000\n      }).onAction().subscribe(() => {\n        this.connectToSse();\n      });\n      return;\n    }\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        }).onAction().subscribe(() => {\n          this.currentMessage = messageToSend;\n          this.cd.detectChanges();\n        });\n      }\n    });\n  }\n\n  /**\n   * Submit the collected restaurant information\n   */\n  submitInfo(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to submit information', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Extract restaurant information from chat history\n    this.extractRestaurantInfo();\n\n    this.isSubmitting = true;\n    this.cd.detectChanges();\n\n    // Prepare restaurant info with tenant details\n    const restaurantData = {\n      ...this.restaurantInfo,\n      tenantId: this.tenantId,\n      tenantName: this.tenantName\n    };\n\n    this.sseService.submitRestaurantInfo(this.tenantId, restaurantData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.snackBar.open('Restaurant information submitted successfully', 'Close', {\n          duration: 3000\n        });\n\n        this.scrollToBottom();\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error submitting restaurant info:', error);\n        this.isSubmitting = false;\n        this.snackBar.open('Failed to submit restaurant information', 'Retry', {\n          duration: 5000\n        }).onAction().subscribe(() => {\n          this.submitInfo();\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Extract restaurant information from chat history\n   * This is a simple implementation - in a real app, you might want to use\n   * more sophisticated NLP techniques or structured data collection\n   */\n  private extractRestaurantInfo(): void {\n    // Initialize restaurant info with default values\n    this.restaurantInfo = {\n      tenantId: this.tenantId,\n      location: '',\n      businessType: '',\n      cuisineType: '',\n      operatingHours: '',\n      specialties: [],\n      contactInfo: ''\n    };\n\n    // Extract information from user messages\n    const userMessages = this.messages.filter(m => m.sender === 'user').map(m => m.text.toLowerCase());\n\n    // Simple keyword-based extraction\n    for (const message of userMessages) {\n      // Location extraction\n      if (message.includes('location') || message.includes('address') || message.includes('located')) {\n        this.restaurantInfo.location = message;\n      }\n\n      // Business type extraction\n      if (message.includes('business') || message.includes('restaurant type') || message.includes('establishment')) {\n        this.restaurantInfo.businessType = message;\n      }\n\n      // Cuisine type extraction\n      if (message.includes('cuisine') || message.includes('food') || message.includes('serve')) {\n        this.restaurantInfo.cuisineType = message;\n      }\n\n      // Hours extraction\n      if (message.includes('hours') || message.includes('open') || message.includes('close') || message.includes('timing')) {\n        this.restaurantInfo.operatingHours = message;\n      }\n\n      // Contact info extraction\n      if (message.includes('contact') || message.includes('phone') || message.includes('email') || message.includes('website')) {\n        this.restaurantInfo.contactInfo = message;\n      }\n\n      // Specialties extraction\n      if (message.includes('special') || message.includes('signature') || message.includes('famous for')) {\n        this.restaurantInfo.specialties = [message];\n      }\n    }\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    setTimeout(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    }, 100);\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n}\n", "<div class=\"chat-container\">\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">chat</mat-icon>\n      <span>Restaurant Information Assistant</span>\n    </div>\n    <div class=\"connection-status\" [ngClass]=\"{'connected': isConnected, 'disconnected': !isConnected}\">\n      <span class=\"status-dot\"></span>\n      <span class=\"status-text\">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>\n      <button mat-icon-button *ngIf=\"!isConnected && !isConnecting\" (click)=\"connectToSse()\" matTooltip=\"Reconnect\">\n        <mat-icon>refresh</mat-icon>\n      </button>\n      <mat-spinner *ngIf=\"isConnecting\" diameter=\"20\"></mat-spinner>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n      <div class=\"message-avatar\">\n        <mat-icon *ngIf=\"message.sender === 'bot'\">smart_toy</mat-icon>\n        <mat-icon *ngIf=\"message.sender === 'user'\">person</mat-icon>\n      </div>\n      <div class=\"message-content\">\n        <div class=\"message-text\">{{ message.text }}</div>\n        <div class=\"message-time\">{{ message.timestamp | date:'shortTime' }}</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput \n             [(ngModel)]=\"currentMessage\" \n             placeholder=\"Type your message...\" \n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"!isConnected || isSubmitting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || !isConnected || isSubmitting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n\n  <div class=\"chat-actions\">\n    <button mat-raised-button color=\"accent\" (click)=\"submitInfo()\" [disabled]=\"!isConnected || isSubmitting\">\n      <mat-icon>save</mat-icon>\n      <span>Submit Information</span>\n      <mat-spinner *ngIf=\"isSubmitting\" diameter=\"20\" class=\"submit-spinner\"></mat-spinner>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;;;;;;;;;;;;;;ICCvEC,EAAA,CAAAC,cAAA,iBAA8G;IAAhDD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACpFT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAE9BX,EAAA,CAAAY,SAAA,sBAA8D;;;;;IAO5DZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAU,MAAA,gBAAS;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAC/DX,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;;;;;;;IAHjEX,EAAA,CAAAC,cAAA,cAAiK;IAE7JD,EAAA,CAAAa,UAAA,IAAAC,2CAAA,uBAA+D;IAC/Dd,EAAA,CAAAa,UAAA,IAAAE,2CAAA,uBAA6D;IAC/Df,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA6B;IACDD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAClDX,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAU,MAAA,GAA0C;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IAPdX,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,aAAAD,UAAA,CAAAC,MAAA,YAAgG;IAEjJpB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAgB,UAAA,SAAAG,UAAA,CAAAC,MAAA,WAA8B;IAC9BpB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAgB,UAAA,SAAAG,UAAA,CAAAC,MAAA,YAA+B;IAGhBpB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAsB,iBAAA,CAAAH,UAAA,CAAAI,IAAA,CAAkB;IAClBvB,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAwB,WAAA,OAAAL,UAAA,CAAAM,SAAA,eAA0C;;;;;IAsBtEzB,EAAA,CAAAY,SAAA,sBAAqF;;;;;;;;;ADhC3F,MAkBac,gBAAgB;EAc3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAhBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,cAAc,GAA4B,EAAE;IAEpC,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;EAMvD;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,QAAQ,GAAG,CACd;MACES,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBpB,IAAI,EAAE,2KAA2K;MACjLH,MAAM,EAAE,KAAK;MACbK,SAAS,EAAE,IAAImB,IAAI;KACpB,CACF;IAED;IACA,IAAI,CAACL,mBAAmB,GAAG,IAAI,CAACX,UAAU,CAACiB,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAI,CAAC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACP,EAAE,KAAKK,OAAO,CAACL,EAAE,CAAC,EAAE;QACjD,IAAI,CAACT,QAAQ,CAACiB,IAAI,CAACH,OAAO,CAAC;QAC3B,IAAI,CAACI,cAAc,EAAE;QACrB,IAAI,CAACtB,EAAE,CAACuB,aAAa,EAAE;;IAE3B,CAAC,CACF;IAED;IACA,IAAI,CAACZ,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAACyB,iBAAiB,CAACP,SAAS,CACtEQ,SAAkB,IAAI;MACrB,IAAI,CAACnB,WAAW,GAAGmB,SAAS;MAC5B,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACP,EAAE,CAACuB,aAAa,EAAE;MAEvB,IAAIE,SAAS,EAAE;QACb,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE;UACvDC,QAAQ,EAAE;SACX,CAAC;OACH,MAAM;QACL,IAAI,CAAC1B,QAAQ,CAACyB,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAC5DC,QAAQ,EAAE;SACX,CAAC;;IAEN,CAAC,CACF;IAED;IACA,IAAI,CAAC/C,YAAY,EAAE;EACrB;EAEAgD,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAAClB,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACkB,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC9B,UAAU,CAAC+B,UAAU,EAAE;EAC9B;EAEA;;;EAGAlD,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACsB,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACyB,IAAI,CAAC,kCAAkC,EAAE,OAAO,EAAE;QAC9DC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,CAACpB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACP,EAAE,CAACuB,aAAa,EAAE;IAEvB,IAAI,CAACxB,UAAU,CAACgC,OAAO,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAACe,SAAS,CAAC;MAC/Ce,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACzB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,QAAQ,CAACyB,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAC/DC,QAAQ,EAAE;SACX,CAAC,CAACO,QAAQ,EAAE,CAACjB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACrC,YAAY,EAAE;QACrB,CAAC,CAAC;QACF,IAAI,CAACoB,EAAE,CAACuB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAY,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC9B,cAAc,CAAC+B,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAC9B,WAAW,EAAE;MACrB,IAAI,CAACL,QAAQ,CAACyB,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;QAC3DC,QAAQ,EAAE;OACX,CAAC,CAACO,QAAQ,EAAE,CAACjB,SAAS,CAAC,MAAK;QAC3B,IAAI,CAACrC,YAAY,EAAE;MACrB,CAAC,CAAC;MACF;;IAGF;IACA,MAAMyD,aAAa,GAAG,IAAI,CAAChC,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAACuB,aAAa,EAAE;IAEvB;IACA,IAAI,CAACxB,UAAU,CAACoC,WAAW,CAAC,IAAI,CAACjC,QAAQ,EAAEmC,aAAa,CAAC,CAACpB,SAAS,CAAC;MAClEe,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAC/B,QAAQ,CAACyB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC,CAACO,QAAQ,EAAE,CAACjB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACZ,cAAc,GAAGgC,aAAa;UACnC,IAAI,CAACrC,EAAE,CAACuB,aAAa,EAAE;QACzB,CAAC,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;;;EAGAe,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACpC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACyB,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;QACzEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAACY,qBAAqB,EAAE;IAE5B,IAAI,CAAC/B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,EAAE,CAACuB,aAAa,EAAE;IAEvB;IACA,MAAMiB,cAAc,GAAG;MACrB,GAAG,IAAI,CAAC/B,cAAc;MACtBP,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA;KAClB;IAED,IAAI,CAACJ,UAAU,CAAC0C,oBAAoB,CAAC,IAAI,CAACvC,QAAQ,EAAEsC,cAAc,CAAC,CAACvB,SAAS,CAAC;MAC5EyB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACP,QAAQ,CAACyB,IAAI,CAAC,+CAA+C,EAAE,OAAO,EAAE;UAC3EC,QAAQ,EAAE;SACX,CAAC;QAEF,IAAI,CAACL,cAAc,EAAE;QACrB,IAAI,CAACtB,EAAE,CAACuB,aAAa,EAAE;MACzB,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACxB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACP,QAAQ,CAACyB,IAAI,CAAC,yCAAyC,EAAE,OAAO,EAAE;UACrEC,QAAQ,EAAE;SACX,CAAC,CAACO,QAAQ,EAAE,CAACjB,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACqB,UAAU,EAAE;QACnB,CAAC,CAAC;QACF,IAAI,CAACtC,EAAE,CAACuB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;;EAKQgB,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC9B,cAAc,GAAG;MACpBP,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvByC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE;KACd;IAED;IACA,MAAMC,YAAY,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAAC7B,MAAM,KAAK,MAAM,CAAC,CAAC4D,GAAG,CAAC/B,CAAC,IAAIA,CAAC,CAAC1B,IAAI,CAAC0D,WAAW,EAAE,CAAC;IAElG;IACA,KAAK,MAAMlC,OAAO,IAAI+B,YAAY,EAAE;MAClC;MACA,IAAI/B,OAAO,CAACmC,QAAQ,CAAC,UAAU,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC9F,IAAI,CAAC5C,cAAc,CAACkC,QAAQ,GAAGzB,OAAO;;MAGxC;MACA,IAAIA,OAAO,CAACmC,QAAQ,CAAC,UAAU,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,iBAAiB,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC5G,IAAI,CAAC5C,cAAc,CAACmC,YAAY,GAAG1B,OAAO;;MAG5C;MACA,IAAIA,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,MAAM,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxF,IAAI,CAAC5C,cAAc,CAACoC,WAAW,GAAG3B,OAAO;;MAG3C;MACA,IAAIA,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,MAAM,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACpH,IAAI,CAAC5C,cAAc,CAACqC,cAAc,GAAG5B,OAAO;;MAG9C;MACA,IAAIA,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,OAAO,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACxH,IAAI,CAAC5C,cAAc,CAACuC,WAAW,GAAG9B,OAAO;;MAG3C;MACA,IAAIA,OAAO,CAACmC,QAAQ,CAAC,SAAS,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,WAAW,CAAC,IAAInC,OAAO,CAACmC,QAAQ,CAAC,YAAY,CAAC,EAAE;QAClG,IAAI,CAAC5C,cAAc,CAACsC,WAAW,GAAG,CAAC7B,OAAO,CAAC;;;EAGjD;EAEA;;;;EAIAoC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACvB,WAAW,EAAE;;EAEtB;EAEA;;;EAGQb,cAAcA,CAAA;IACpBqC,UAAU,CAAC,MAAK;MACd,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQlD,UAAUA,CAAA;IAChB,OAAOmD,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;;;uBAjRWvE,gBAAgB,EAAA1B,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAlG,EAAA,CAAAqG,iBAAA,GAAArG,EAAA,CAAAkG,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB7E,gBAAgB;MAAA8E,SAAA;MAAAC,MAAA;QAAA1E,QAAA;QAAAC,UAAA;MAAA;MAAA0E,UAAA;MAAAC,QAAA,GAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChC7BlH,EAAA,CAAAC,cAAA,aAA4B;UAGMD,EAAA,CAAAU,MAAA,WAAI;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAC3CX,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAU,MAAA,uCAAgC;UAAAV,EAAA,CAAAW,YAAA,EAAO;UAE/CX,EAAA,CAAAC,cAAA,aAAoG;UAClGD,EAAA,CAAAY,SAAA,cAAgC;UAChCZ,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAU,MAAA,IAAgD;UAAAV,EAAA,CAAAW,YAAA,EAAO;UACjFX,EAAA,CAAAa,UAAA,KAAAuG,mCAAA,oBAES;UACTpH,EAAA,CAAAa,UAAA,KAAAwG,wCAAA,yBAA8D;UAChErH,EAAA,CAAAW,YAAA,EAAM;UAGRX,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAa,UAAA,KAAAyG,gCAAA,oBASM;UACRtH,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAE,UAAA,2BAAAqH,0DAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAjF,cAAA,GAAAsF,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBL,GAAA,CAAAhC,UAAA,CAAAqC,MAAA,CAAkB;UAAA,EAFD;UADnCxH,EAAA,CAAAW,YAAA,EAIiD;UAEnDX,EAAA,CAAAC,cAAA,kBAAiI;UAA5FD,EAAA,CAAAE,UAAA,mBAAAwH,mDAAA;YAAA,OAASP,GAAA,CAAAnD,WAAA,EAAa;UAAA,EAAC;UAC1DhE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAU,MAAA,YAAI;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAI7BX,EAAA,CAAAC,cAAA,eAA0B;UACiBD,EAAA,CAAAE,UAAA,mBAAAyH,mDAAA;YAAA,OAASR,GAAA,CAAAhD,UAAA,EAAY;UAAA,EAAC;UAC7DnE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAU,MAAA,YAAI;UAAAV,EAAA,CAAAW,YAAA,EAAW;UACzBX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,0BAAkB;UAAAV,EAAA,CAAAW,YAAA,EAAO;UAC/BX,EAAA,CAAAa,UAAA,KAAA+G,wCAAA,0BAAqF;UACvF5H,EAAA,CAAAW,YAAA,EAAS;;;UAzCsBX,EAAA,CAAAqB,SAAA,GAAoE;UAApErB,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAA4G,GAAA,EAAAV,GAAA,CAAAhF,WAAA,GAAAgF,GAAA,CAAAhF,WAAA,EAAoE;UAEvEnC,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAA6F,GAAA,CAAAhF,WAAA,gCAAgD;UACjDnC,EAAA,CAAAqB,SAAA,GAAmC;UAAnCrB,EAAA,CAAAgB,UAAA,UAAAmG,GAAA,CAAAhF,WAAA,KAAAgF,GAAA,CAAA/E,YAAA,CAAmC;UAG9CpC,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAgB,UAAA,SAAAmG,GAAA,CAAA/E,YAAA,CAAkB;UAKTpC,EAAA,CAAAqB,SAAA,GAAW;UAAXrB,EAAA,CAAAgB,UAAA,YAAAmG,GAAA,CAAAlF,QAAA,CAAW;UAe3BjC,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAAgB,UAAA,YAAAmG,GAAA,CAAAjF,cAAA,CAA4B,cAAAiF,GAAA,CAAAhF,WAAA,IAAAgF,GAAA,CAAA9E,YAAA;UAKwBrC,EAAA,CAAAqB,SAAA,GAAmE;UAAnErB,EAAA,CAAAgB,UAAA,cAAAmG,GAAA,CAAAjF,cAAA,CAAA+B,IAAA,OAAAkD,GAAA,CAAAhF,WAAA,IAAAgF,GAAA,CAAA9E,YAAA,CAAmE;UAMhErC,EAAA,CAAAqB,SAAA,GAAyC;UAAzCrB,EAAA,CAAAgB,UAAA,cAAAmG,GAAA,CAAAhF,WAAA,IAAAgF,GAAA,CAAA9E,YAAA,CAAyC;UAGzFrC,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAgB,UAAA,SAAAmG,GAAA,CAAA9E,YAAA,CAAkB;;;qBD5BlC9C,YAAY,EAAAuI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ1I,WAAW,EAAA2I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX7I,mBAAmB,EACnBC,eAAe,EAAA6I,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACf/I,aAAa,EACbC,kBAAkB,EAAA+I,EAAA,CAAAC,YAAA,EAClB/I,aAAa,EAAAgJ,EAAA,CAAAC,OAAA,EACbhJ,cAAc,EAAAiJ,EAAA,CAAAC,QAAA,EACdjJ,wBAAwB,EAAAkJ,EAAA,CAAAC,kBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMf1H,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}