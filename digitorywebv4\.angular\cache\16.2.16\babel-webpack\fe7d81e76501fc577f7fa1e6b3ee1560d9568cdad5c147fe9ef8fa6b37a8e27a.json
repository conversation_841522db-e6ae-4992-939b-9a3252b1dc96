{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSliderModule } from '@angular/material/slider';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { interval } from 'rxjs';\nimport { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/inventory.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/share-data.service\";\nimport * as i7 from \"ngx-skeleton-loader\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@angular/material/tooltip\";\nimport * as i13 from \"@angular/material/paginator\";\nimport * as i14 from \"@angular/material/sort\";\nfunction SyncDataComponent_div_0_div_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.retrieveData[0] == null ? null : ctx_r5.retrieveData[0].sessionId, \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_8_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Bulk Update \");\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r11.statusData.provision.datetime, \"dd/MM/yyyy hh:mm a\"));\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r12.statusData.provision.datetime, \"MMM d, y\"));\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 25);\n    i0.ɵɵtext(1, \"timer\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r17.statusData.quality.datetime, \"dd/MM/yyyy hh:mm a\"));\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r18.statusData.quality.datetime, \"MMM d, y\"));\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"span\", 23);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_mat_icon_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 25);\n    i0.ɵɵtext(1, \"timer\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r23.statusData.deployment.datetime, \"dd/MM/yyyy hh:mm a\"));\n  }\n}\nfunction SyncDataComponent_div_0_div_8_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r24.statusData.deployment.datetime, \"MMM d, y\"));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"completed\": a0\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"completed\": a0,\n    \"error\": a1\n  };\n};\nfunction SyncDataComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\");\n    i0.ɵɵtext(3, \" Session Id - \");\n    i0.ɵɵtemplate(4, SyncDataComponent_div_0_div_8_ng_container_4_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵtemplate(5, SyncDataComponent_div_0_div_8_ng_template_5_Template, 1, 0, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"div\", 12)(9, \"div\", 13)(10, \"div\", 14);\n    i0.ɵɵtemplate(11, SyncDataComponent_div_0_div_8_mat_icon_11_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(12, SyncDataComponent_div_0_div_8_mat_icon_12_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(13, SyncDataComponent_div_0_div_8_div_13_Template, 3, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtext(15, \"Provision\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 17)(17, \"div\");\n    i0.ɵɵtemplate(18, SyncDataComponent_div_0_div_8_span_18_Template, 3, 4, \"span\", 18);\n    i0.ɵɵtemplate(19, SyncDataComponent_div_0_div_8_span_19_Template, 3, 4, \"span\", 18);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 19)(21, \"div\", 14);\n    i0.ɵɵtemplate(22, SyncDataComponent_div_0_div_8_mat_icon_22_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(23, SyncDataComponent_div_0_div_8_mat_icon_23_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(24, SyncDataComponent_div_0_div_8_div_24_Template, 3, 0, \"div\", 15);\n    i0.ɵɵtemplate(25, SyncDataComponent_div_0_div_8_mat_icon_25_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 16);\n    i0.ɵɵtext(27, \"Quality\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 17)(29, \"div\");\n    i0.ɵɵtemplate(30, SyncDataComponent_div_0_div_8_span_30_Template, 3, 4, \"span\", 18);\n    i0.ɵɵtemplate(31, SyncDataComponent_div_0_div_8_span_31_Template, 3, 4, \"span\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 14);\n    i0.ɵɵtemplate(34, SyncDataComponent_div_0_div_8_mat_icon_34_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(35, SyncDataComponent_div_0_div_8_mat_icon_35_Template, 2, 0, \"mat-icon\", 0);\n    i0.ɵɵtemplate(36, SyncDataComponent_div_0_div_8_div_36_Template, 3, 0, \"div\", 15);\n    i0.ɵɵtemplate(37, SyncDataComponent_div_0_div_8_mat_icon_37_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 16);\n    i0.ɵɵtext(39, \"Deployment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 17)(41, \"div\");\n    i0.ɵɵtemplate(42, SyncDataComponent_div_0_div_8_span_42_Template, 3, 4, \"span\", 18);\n    i0.ɵɵtemplate(43, SyncDataComponent_div_0_div_8_span_43_Template, 3, 4, \"span\", 18);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(6);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.retrieveData[0] == null ? null : ctx_r2.retrieveData[0].sessionId) !== \"NA\" && ctx_r2.retrieveData[0] && (ctx_r2.retrieveData[0] == null ? null : ctx_r2.retrieveData[0].sessionId) !== undefined)(\"ngIfElse\", _r6);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c0, ctx_r2.statusData.provision.status === true));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.provision.status === true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.provision.error === true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.provision.status === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSmallScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSmallScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(24, _c1, ctx_r2.statusData.quality.status === true && ctx_r2.statusData.quality.error === false, ctx_r2.statusData.quality.status === true && ctx_r2.statusData.quality.error === true));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.quality.status === true && ctx_r2.statusData.quality.error === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.quality.status === true && ctx_r2.statusData.quality.error === true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.provision.status === true && ctx_r2.statusData.provision.error === false && ctx_r2.statusData.quality.status === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.provision.status === false || ctx_r2.statusData.provision.error === true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSmallScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSmallScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c1, ctx_r2.statusData.deployment.status === true && ctx_r2.statusData.deployment.error === false, ctx_r2.statusData.deployment.status === true && ctx_r2.statusData.deployment.error === true));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.deployment.status === true && ctx_r2.statusData.deployment.error === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.deployment.status === true && ctx_r2.statusData.deployment.error === true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.quality.status === true && ctx_r2.statusData.quality.error === false && ctx_r2.statusData.deployment.status === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statusData.quality.status === false || ctx_r2.statusData.quality.error === true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSmallScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSmallScreen);\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 47);\n    i0.ɵɵtext(1, \" Preview \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48)(1, \"button\", 49)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 50);\n    i0.ɵɵtext(1, \" Session Id \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const element_r46 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r46.sessionId, \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Bulk Update UI \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48);\n    i0.ɵɵtemplate(1, SyncDataComponent_div_0_div_9_mat_cell_7_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵtemplate(2, SyncDataComponent_div_0_div_9_mat_cell_7_ng_template_2_Template, 1, 0, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r46 = ctx.$implicit;\n    const _r48 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r46.sessionId !== \"NA\" && element_r46.sessionId)(\"ngIfElse\", _r48);\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 47);\n    i0.ɵɵtext(1, \" Client \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r51 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r51.client, \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 50);\n    i0.ɵɵtext(1, \" Created By \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r52 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r52.email, \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 50);\n    i0.ɵɵtext(1, \" Synced By \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r53 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r53.syncEmail ? element_r53.syncEmail : \"-\", \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 50);\n    i0.ɵɵtext(1, \" Created Date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r54 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, element_r54.createTs, \"dd-MM-yyyy hh:mm:ss a\"), \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 47);\n    i0.ɵɵtext(1, \" Category \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r55 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", element_r55.category, \" \");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 47);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"mat-icon\", 55);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Completed \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"mat-icon\", 56);\n    i0.ɵɵtext(2, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Failed \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4, \" Progress \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_cell_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 52);\n    i0.ɵɵtemplate(1, SyncDataComponent_div_0_div_9_mat_cell_25_div_1_Template, 4, 0, \"div\", 53);\n    i0.ɵɵtemplate(2, SyncDataComponent_div_0_div_9_mat_cell_25_div_2_Template, 4, 0, \"div\", 53);\n    i0.ɵɵtemplate(3, SyncDataComponent_div_0_div_9_mat_cell_25_div_3_Template, 5, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r56 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r56.status === \"Completed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r56.status === \"Failed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", element_r56.status === \"pending\");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_cell_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 57);\n    i0.ɵɵtext(1, \" Action \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"active-row\": a0\n  };\n};\nfunction SyncDataComponent_div_0_div_9_mat_cell_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 57)(1, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function SyncDataComponent_div_0_div_9_mat_cell_28_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const element_r60 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r61.viewStatus(element_r60));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 59);\n    i0.ɵɵtext(3, \"remove_red_eye\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SyncDataComponent_div_0_div_9_mat_cell_28_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const element_r60 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r63.getErrorlog(element_r60));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\", 59);\n    i0.ɵɵtext(6, \"error\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const element_r60 = ctx.$implicit;\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c2, ctx_r42.activeRowIndex === element_r60));\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_header_row_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction SyncDataComponent_div_0_div_9_mat_row_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\");\n  }\n}\nconst _c3 = function () {\n  return [5, 10, 25, 50, 100];\n};\nfunction SyncDataComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"mat-table\", 27);\n    i0.ɵɵelementContainerStart(2, 28);\n    i0.ɵɵtemplate(3, SyncDataComponent_div_0_div_9_mat_header_cell_3_Template, 2, 0, \"mat-header-cell\", 29);\n    i0.ɵɵtemplate(4, SyncDataComponent_div_0_div_9_mat_cell_4_Template, 4, 0, \"mat-cell\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 31);\n    i0.ɵɵtemplate(6, SyncDataComponent_div_0_div_9_mat_header_cell_6_Template, 2, 0, \"mat-header-cell\", 32);\n    i0.ɵɵtemplate(7, SyncDataComponent_div_0_div_9_mat_cell_7_Template, 4, 2, \"mat-cell\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 33);\n    i0.ɵɵtemplate(9, SyncDataComponent_div_0_div_9_mat_header_cell_9_Template, 2, 0, \"mat-header-cell\", 29);\n    i0.ɵɵtemplate(10, SyncDataComponent_div_0_div_9_mat_cell_10_Template, 2, 1, \"mat-cell\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 34);\n    i0.ɵɵtemplate(12, SyncDataComponent_div_0_div_9_mat_header_cell_12_Template, 2, 0, \"mat-header-cell\", 32);\n    i0.ɵɵtemplate(13, SyncDataComponent_div_0_div_9_mat_cell_13_Template, 2, 1, \"mat-cell\", 35);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 36);\n    i0.ɵɵtemplate(15, SyncDataComponent_div_0_div_9_mat_header_cell_15_Template, 2, 0, \"mat-header-cell\", 32);\n    i0.ɵɵtemplate(16, SyncDataComponent_div_0_div_9_mat_cell_16_Template, 2, 1, \"mat-cell\", 35);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 37);\n    i0.ɵɵtemplate(18, SyncDataComponent_div_0_div_9_mat_header_cell_18_Template, 2, 0, \"mat-header-cell\", 32);\n    i0.ɵɵtemplate(19, SyncDataComponent_div_0_div_9_mat_cell_19_Template, 3, 4, \"mat-cell\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 38);\n    i0.ɵɵtemplate(21, SyncDataComponent_div_0_div_9_mat_header_cell_21_Template, 2, 0, \"mat-header-cell\", 29);\n    i0.ɵɵtemplate(22, SyncDataComponent_div_0_div_9_mat_cell_22_Template, 2, 1, \"mat-cell\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(23, 39);\n    i0.ɵɵtemplate(24, SyncDataComponent_div_0_div_9_mat_header_cell_24_Template, 2, 0, \"mat-header-cell\", 29);\n    i0.ɵɵtemplate(25, SyncDataComponent_div_0_div_9_mat_cell_25_Template, 4, 3, \"mat-cell\", 40);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(26, 41);\n    i0.ɵɵtemplate(27, SyncDataComponent_div_0_div_9_mat_header_cell_27_Template, 2, 0, \"mat-header-cell\", 42);\n    i0.ɵɵtemplate(28, SyncDataComponent_div_0_div_9_mat_cell_28_Template, 7, 3, \"mat-cell\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(29, SyncDataComponent_div_0_div_9_mat_header_row_29_Template, 1, 0, \"mat-header-row\", 44);\n    i0.ɵɵtemplate(30, SyncDataComponent_div_0_div_9_mat_row_30_Template, 1, 0, \"mat-row\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"mat-paginator\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r3.dataSource);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r3.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r3.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(5, _c3));\n  }\n}\nfunction SyncDataComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-empty-state\", 61);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SyncDataComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 2)(2, \"mat-icon\", 3);\n    i0.ɵɵlistener(\"click\", function SyncDataComponent_div_0_Template_mat_icon_click_2_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.return());\n    });\n    i0.ɵɵtext(3, \"close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\")(6, \"div\")(7, \"mat-card\");\n    i0.ɵɵtemplate(8, SyncDataComponent_div_0_div_8_Template, 44, 30, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, SyncDataComponent_div_0_div_9_Template, 32, 6, \"div\", 6);\n    i0.ɵɵtemplate(10, SyncDataComponent_div_0_div_10_Template, 2, 0, \"div\", 0);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.retrieveData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dataSource.data.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dataSource.data.length === 0);\n  }\n}\nconst _c4 = function () {\n  return {\n    \"border-radius\": \"4px\",\n    \"height\": \"30px\",\n    \"margin-bottom\": \"8px\",\n    \"width\": \"19%\",\n    \"margin-right\": \"1%\",\n    \"display\": \"inline-block\",\n    \"opacity\": \"0.85\"\n  };\n};\nfunction SyncDataComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"span\");\n    i0.ɵɵtext(2, \"Loading History...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ngx-skeleton-loader\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nlet SyncDataComponent = /*#__PURE__*/(() => {\n  class SyncDataComponent {\n    constructor(api, cd, router, auth, dialog, notify, baseData, sharedData) {\n      this.api = api;\n      this.cd = cd;\n      this.router = router;\n      this.auth = auth;\n      this.dialog = dialog;\n      this.notify = notify;\n      this.baseData = baseData;\n      this.sharedData = sharedData;\n      this.dataSource = new MatTableDataSource([]);\n      this.stages = [{\n        title: \"Provision\",\n        status: true\n      }, {\n        title: \"Quality\",\n        status: false\n      }, {\n        title: \"Deployment\",\n        status: false\n      }];\n      this.dataReady = false;\n      this.intervalMs = 5000;\n      this.hasPendingOrCompleted = true;\n      this.user = this.auth.getCurrentUser();\n      this.getTenantData();\n      this.isSmallScreen = window.innerWidth < 740;\n      const currentUrl = window.location.href.split('/');\n      const currentPage = currentUrl[currentUrl.length - 1];\n      const pageType = currentPage.includes('?') ? currentPage.split('?')[0] : currentPage;\n      switch (pageType) {\n        case 'recipe':\n        case 'Subrecipe Master':\n          this.syncType = 'recipeManagement';\n          this.syncTypeName = \"Recipe\";\n          this.sessionType = \"recipe\";\n          this.navigateData = 'menu master';\n          break;\n        case 'user':\n          this.syncType = 'userManagement';\n          this.syncTypeName = \"User\";\n          this.sessionType = \"user\";\n          this.navigateData = 'user';\n          break;\n        default:\n          this.syncType = 'inventoryManagement';\n          this.syncTypeName = \"Inventory\";\n          this.sessionType = \"inventory\";\n          this.navigateData = 'inventoryList';\n          break;\n      }\n    }\n    onResize(event) {\n      this.isSmallScreen = window.innerWidth < 768;\n    }\n    ngOnInit() {\n      this.displayedColumns = ['id', 'userName', 'syncEmail', 'createdDate', 'status', 'errorLog'];\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n    }\n    return() {\n      this.dialog.closeAll();\n      window.location.reload();\n    }\n    getTenantData() {\n      this.api.getTenantConfigDetails(this.user.tenantId).subscribe({\n        next: res => {\n          if (res['success']) {\n            this.tenantDetails = res['data'];\n            this.getHistory(this.tenantDetails['full']);\n          } else {\n            this.notify.snackBarShowInfo(res['message']);\n            this.tenantDetails = undefined;\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n    getHistory(client) {\n      let obj = this.user;\n      obj['client'] = client;\n      obj['type'] = this.syncType;\n      this.api.retrieveHistory(obj).subscribe({\n        next: res => {\n          if (res['success'] && res['data'].length > 0) {\n            this.retrieveData = res['data'];\n            this.statusData = res['data'][0].history;\n            this.activeRowIndex = res['data'][0];\n            this.dataSource.data = this.retrieveData;\n            this.dataReady = true;\n            var counter = 0;\n            this.interval = interval(3000).subscribe(_x => {\n              this.refreshApiCall(client);\n              counter += 1;\n              if (counter > 24 || !this.hasPendingOrCompleted) this.interval.unsubscribe();\n            });\n            this.cd.detectChanges();\n          } else {\n            counter = 24;\n            this.retrieveData = [];\n            this.dataSource.data = [];\n            this.dataReady = true;\n            this.cd.detectChanges();\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n    refreshApiCall(client) {\n      this.ids = this.dataSource.data.filter(item => item.status === 'pending').map(item => item._id['$oid']);\n      let obj = this.user;\n      obj['type'] = this.syncType;\n      obj['client'] = client;\n      obj['ids'] = this.ids[0];\n      if (this.ids.length > 0) {\n        this.api.refreshApiCall(obj).subscribe({\n          next: res => {\n            if (res['success']) {\n              this.hasPendingOrCompleted = res['data'].some(item => item.status === 'pending');\n              this.retrieveData = res['data'];\n              this.statusData = res['data'][0].history;\n              this.activeRowIndex = res['data'][0];\n              this.cd.detectChanges();\n              if (!this.hasPendingOrCompleted) {\n                let obj = this.user;\n                obj['client'] = client;\n                obj['type'] = this.syncType;\n                this.api.retrieveHistory(obj).subscribe({\n                  next: res => {\n                    if (res['success'] && res['data'].length > 0) {\n                      this.retrieveData = res['data'];\n                      this.statusData = res['data'][0].history;\n                      this.activeRowIndex = res['data'][0];\n                      this.dataSource.data = this.retrieveData;\n                      this.dataReady = true;\n                      this.cd.detectChanges();\n                    }\n                  },\n                  error: err => {\n                    console.log(err);\n                  }\n                });\n              }\n            } else {\n              this.hasPendingOrCompleted = false;\n            }\n          },\n          error: err => {\n            console.log(err);\n          }\n        });\n      }\n    }\n    ngOnDestroy() {\n      if (this.interval) {\n        this.interval.unsubscribe();\n      }\n    }\n    getErrorlog(event) {\n      let obj = {};\n      obj['masterDataUpdateId'] = event.id;\n      this.api.getErrorlog(obj).subscribe({\n        next: res => {\n          if (res['success']) {\n            let encodedData = res.eFile;\n            let textData = atob(encodedData);\n            let rows = textData.split('\\n');\n            for (let i = 1; i < rows.length; i++) {\n              let row = rows[i].split('|');\n              if (row.length > 1) {\n                let rowNumber = parseInt(row[1], 10);\n                if (!isNaN(rowNumber)) {\n                  rowNumber++;\n                  row[1] = rowNumber.toString();\n                }\n                rows[i] = row.join('|');\n              }\n            }\n            let modifiedTextData = rows.join('\\n');\n            const newWindow = window.open('', '_blank');\n            if (newWindow) {\n              newWindow.document.open();\n              newWindow.document.write('<pre>' + modifiedTextData + '</pre>');\n              newWindow.document.close();\n            }\n          } else {\n            this.notify.snackBarShowInfo(res['message']);\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n    isLastStage(index) {\n      return index === this.stages.length - 1;\n    }\n    viewStatus(element) {\n      this.retrieveData = [element];\n      this.statusData = [element][0].history;\n      this.activeRowIndex = element;\n    }\n    static {\n      this.ɵfac = function SyncDataComponent_Factory(t) {\n        return new (t || SyncDataComponent)(i0.ɵɵdirectiveInject(i1.InventoryService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i6.ShareDataService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SyncDataComponent,\n        selectors: [[\"app-sync-data\"]],\n        viewQuery: function SyncDataComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        hostBindings: function SyncDataComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"resize\", function SyncDataComponent_resize_HostBindingHandler($event) {\n              return ctx.onResize($event);\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[4, \"ngIf\"], [\"class\", \"loader\", 4, \"ngIf\"], [1, \"closeBtn\"], [\"matTooltip\", \"close\", 1, \"closeBtnIcon\", 3, \"click\"], [1, \"registration-form\", \"py-2\", \"px-3\"], [\"class\", \"evaluation-container pb-3\", 4, \"ngIf\"], [\"class\", \"section mt-3\", 4, \"ngIf\"], [1, \"evaluation-container\", \"pb-3\"], [1, \"text-center\", \"m-2\", \"p-2\", \"bottomTitles\"], [4, \"ngIf\", \"ngIfElse\"], [\"bulkUpdate\", \"\"], [\"_ngcontent-knm-c68\", \"\", 1, \"candidate-evaluation\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"progress_bar\"], [1, \"stepper-item\", \"stepper-item1\", 3, \"ngClass\"], [1, \"step-counter\"], [\"class\", \"spinner-border spinner_class\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-name\"], [1, \"date_time\", \"d-flex\"], [\"class\", \"time ms-1\", 4, \"ngIf\"], [1, \"stepper-item\", \"stepper-item2\", 3, \"ngClass\"], [\"class\", \"timerIcon\", 4, \"ngIf\"], [1, \"stepper-item\", 3, \"ngClass\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner_class\"], [1, \"sr-only\"], [1, \"time\", \"ms-1\"], [1, \"timerIcon\"], [1, \"section\", \"mt-3\"], [\"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"action\"], [\"class\", \"custom-header\", 4, \"matHeaderCellDef\"], [\"class\", \"custom-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"id\"], [\"class\", \"custom-header\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"client\"], [\"matColumnDef\", \"userName\"], [\"class\", \"custom-cell\", \"style\", \"word-break: break-word !important;\", 4, \"matCellDef\"], [\"matColumnDef\", \"syncEmail\"], [\"matColumnDef\", \"createdDate\"], [\"matColumnDef\", \"category\"], [\"matColumnDef\", \"status\"], [\"class\", \"custom-cell justify-content-start\", 4, \"matCellDef\"], [\"matColumnDef\", \"errorLog\"], [\"class\", \"tableActionCol\", 4, \"matHeaderCellDef\"], [\"class\", \"tableActionCol\", 4, \"matCellDef\"], [4, \"matHeaderRowDef\"], [4, \"matRowDef\", \"matRowDefColumns\"], [1, \"mat-paginator-sticky\", 3, \"pageSize\", \"pageSizeOptions\"], [1, \"custom-header\"], [1, \"custom-cell\"], [\"backgroundColor\", \"primary\", 1, \"mx-2\", \"mt-3\", \"editIconBtn\"], [\"mat-sort-header\", \"\", 1, \"custom-header\"], [1, \"custom-cell\", 2, \"word-break\", \"break-word !important\"], [1, \"custom-cell\", \"justify-content-start\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [1, \"check_circleIcon\"], [1, \"error_circleIcon\"], [1, \"tableActionCol\"], [\"backgroundColor\", \"primary\", \"matTooltip\", \"view status\", 1, \"mx-2\", \"editIconBtn\", \"editIconBtnSync\", 3, \"ngClass\", \"click\"], [1, \"mt-1\"], [\"backgroundColor\", \"primary\", \"matTooltip\", \"view error\", 1, \"mx-2\", \"editIconBtn\", \"editIconBtnSync\", 3, \"click\"], [\"icon\", \"history\", \"title\", \"No Sync History Found\", \"message\", \"There is no sync history to display at the moment.\"], [1, \"loader\"], [\"count\", \"5\", \"animation\", \"pulse\", 3, \"theme\"]],\n        template: function SyncDataComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SyncDataComponent_div_0_Template, 11, 3, \"div\", 0);\n            i0.ɵɵtemplate(1, SyncDataComponent_div_1_Template, 4, 2, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.dataReady);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.dataReady);\n          }\n        },\n        dependencies: [FormsModule, NgxSkeletonLoaderModule, i7.NgxSkeletonLoaderComponent, MatDialogModule, CommonModule, i8.NgClass, i8.NgIf, i8.DatePipe, ReactiveFormsModule, MatFormFieldModule, MatNativeDateModule, MatDatepickerModule, MatInputModule, MatSliderModule, MatButtonModule, MatIconModule, i9.MatIcon, MatCardModule, i10.MatCard, MatSelectModule, MatRadioModule, MatAutocompleteModule, MatDividerModule, MatTableModule, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, NgxMatSelectSearchModule, MatCheckboxModule, MatExpansionModule, MatTooltipModule, i12.MatTooltip, MatPaginatorModule, i13.MatPaginator, MatSortModule, i14.MatSort, i14.MatSortHeader, EmptyStateComponent],\n        styles: [\".section[_ngcontent-%COMP%]{width:100%;max-height:435px;overflow:auto}.check_circleIcon[_ngcontent-%COMP%]{color:#4caf50}.error_circleIcon[_ngcontent-%COMP%]{color:red}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]{width:100%;height:5rem}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:40px;color:green}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .progress_bar[_ngcontent-%COMP%]{font-family:Arial;margin-top:50px;display:contents;justify-content:space-between;margin-bottom:20px}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;flex:1;margin:-2px}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%]{font-size:.9rem}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:before, .evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";border-bottom:2px solid #ccc!important;width:100%;top:20px;z-index:2}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:before{left:-50%}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:after{left:50%}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]   .step-counter[_ngcontent-%COMP%]{position:relative;z-index:5;display:flex;justify-content:center;align-items:center;width:37px;height:37px;border-radius:50%;background:#ccc;margin-bottom:6px}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item.active[_ngcontent-%COMP%]{font-weight:700}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item.completed[_ngcontent-%COMP%]   .step-counter[_ngcontent-%COMP%]{background-color:green;color:#fff}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item.error[_ngcontent-%COMP%]   .step-counter[_ngcontent-%COMP%]{background-color:red;color:#fff}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]   .final_step[_ngcontent-%COMP%]{outline:2px solid green;border:2px solid #fff}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item.completed[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";border-bottom:2px solid #ccc!important;width:100%;top:20px;left:50%;z-index:3}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:first-child:before{content:none}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]:last-child:after{content:none}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]   .date_time[_ngcontent-%COMP%]{height:.75rem;font-size:.7rem;color:#767070}.evaluation-container[_ngcontent-%COMP%]   .candidate-evaluation[_ngcontent-%COMP%]   .stepper-item[_ngcontent-%COMP%]   .date_time[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:.7rem;color:#767070}.timerIcon[_ngcontent-%COMP%]{color:#6a6a6a}.viewBtn[_ngcontent-%COMP%]{background-color:#008bbd}.editIconBtnSync[_ngcontent-%COMP%]{padding-right:4px;padding-left:3px}.spinner-border1[_ngcontent-%COMP%]{height:2.5rem!important;width:2.5rem!important}.banner[_ngcontent-%COMP%]{background-color:#fafafa;padding-top:9px;padding-bottom:1px;font-size:large;text-align:center;color:#dc143c;font-weight:700}.parentCornerCancelBtn[_ngcontent-%COMP%]{position:relative;top:-18px;margin-right:10px;text-align:end}.loader[_ngcontent-%COMP%]{height:300px;width:650px;padding-left:10px;padding-right:10px}.loader[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-top:20px;margin-bottom:20px;font-size:25px}\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return SyncDataComponent;\n})();\nexport { SyncDataComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}