{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_2_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Start\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Resume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startConversation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 25)(2, \"mat-icon\", 26);\n    i0.ɵɵtext(3, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Restaurant Onboarding Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"I'll help you collect information about your restaurant outlets, cuisines, and more.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 27)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ChatBotComponent_div_2_span_11_Template, 2, 0, \"span\", 23);\n    i0.ɵɵtemplate(12, ChatBotComponent_div_2_span_12_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n  }\n}\nfunction ChatBotComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r9.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1,\n    \"number-container\": a2,\n    \"short-answer-container\": a3,\n    \"system-message-container\": a4\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"number-message\": a0,\n    \"short-answer-message\": a1,\n    \"system-message\": a2\n  };\n};\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"div\", 31);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_14_div_5_Template, 3, 4, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, message_r9.sender === \"user\", message_r9.sender === \"bot\", message_r9.messageType === \"number\", message_r9.messageType === \"short-answer\", message_r9.messageType === \"system-message\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c1, message_r9.messageType === \"number\", message_r9.messageType === \"short-answer\", message_r9.messageType === \"system-message\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 4, message_r9.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r9.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 37);\n    i0.ɵɵtext(7, \"DIGI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Restaurant Data Yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"As you provide information about your restaurant through the chat, it will appear here.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.restaurantData.commonCuisinesAcrossOutlets.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.restaurantData.signatureElements.signatureDishes.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_35_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r22 = ctx.$implicit;\n    const last_r23 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", area_r22, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r23);\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template, 3, 2, \"span\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const outlet_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", outlet_r17.outletWorkAreas);\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_35_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"h3\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"span\", 41);\n    i0.ɵɵtext(7, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 42);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"span\", 41);\n    i0.ɵɵtext(12, \"Work Areas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 42);\n    i0.ɵɵtemplate(14, ChatBotComponent_ng_container_35_div_33_span_14_Template, 2, 1, \"span\", 23);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_35_div_33_span_15_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const outlet_r17 = ctx.$implicit;\n    const i_r18 = ctx.index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" Outlet \", i_r18 + 1, \": \", outlet_r17.outletName, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(outlet_r17.outletAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", outlet_r17.outletWorkAreas && outlet_r17.outletWorkAreas.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !outlet_r17.outletWorkAreas || outlet_r17.outletWorkAreas.length === 0);\n  }\n}\nfunction ChatBotComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"h3\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 40)(7, \"span\", 41);\n    i0.ɵɵtext(8, \"Total Outlets:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"span\", 41);\n    i0.ɵɵtext(13, \"Cuisines:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 42);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_35_span_15_Template, 2, 1, \"span\", 23);\n    i0.ɵɵtemplate(16, ChatBotComponent_ng_container_35_span_16_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 40)(18, \"span\", 41);\n    i0.ɵɵtext(19, \"Signature Dishes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 42);\n    i0.ɵɵtemplate(21, ChatBotComponent_ng_container_35_span_21_Template, 2, 1, \"span\", 23);\n    i0.ɵɵtemplate(22, ChatBotComponent_ng_container_35_span_22_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 40)(24, \"span\", 41);\n    i0.ɵɵtext(25, \"Alcohol Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 42);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 40)(29, \"span\", 41);\n    i0.ɵɵtext(30, \"Tobacco Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 42);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ChatBotComponent_ng_container_35_div_33_Template, 16, 5, \"div\", 43);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.restaurantData.totalOutlets);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.commonCuisinesAcrossOutlets && ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.commonCuisinesAcrossOutlets || ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.signatureElements && ctx_r4.restaurantData.signatureElements.signatureDishes && ctx_r4.restaurantData.signatureElements.signatureDishes.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.signatureElements || !ctx_r4.restaurantData.signatureElements.signatureDishes || ctx_r4.restaurantData.signatureElements.signatureDishes.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.beverageInfo == null ? null : ctx_r4.restaurantData.beverageInfo.alcoholService) || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.tobaccoInfo == null ? null : ctx_r4.restaurantData.tobaccoInfo.tobaccoService) || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantData.outletDetails);\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    this.restaurantData = null;\n    this.conversationStarted = false;\n    this.isRefreshing = false;\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    this.dataUpdateSubscription = null;\n    this.loadingHistory = false;\n  }\n  ngOnChanges(changes) {\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      if (newTenantId && prevTenantId !== newTenantId) {\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    this.messages = [];\n    if (this.tenantId) {\n      this.loadingHistory = false;\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      this.loadConversationHistory();\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else if (this.conversationStarted) {\n          this.initiateConversation();\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000);\n    } else {\n      this.messages = [];\n    }\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      if (message.sender === 'system') {\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        return;\n      }\n      if (message.sender === 'bot') {\n        if (!message.text.trim()) {\n          return;\n        }\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          if (message.text !== 'DIGI is thinking...') {\n            this.messages[existingMessageIndex] = message;\n          }\n        } else {\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          const thinkingIndex = this.messages.findIndex(m => m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id);\n          if (thinkingIndex !== -1) {\n            this.messages[thinkingIndex] = message;\n          } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n            this.messages.push(message);\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n        if (!message.text.includes('blinking-cursor')) {\n          this.isWaitingForResponse = false;\n        }\n        if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n          const lastMessage = this.messages[this.messages.length - 1];\n          if (lastMessage.sender !== 'user') {\n            return;\n          }\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      } else if (message.sender === 'user') {\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000);\n        if (!isDuplicate) {\n          this.messages.push(message);\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {}\n      }\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    });\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(data => {\n      if (data && data.type === 'restaurant_data') {\n        if (data.data) {\n          this.restaurantData = data.data;\n        } else {\n          this.restaurantData = null;\n        }\n        setTimeout(() => {\n          this.cd.detectChanges();\n        }, 0);\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    this.sseService.disconnect();\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    let messageType = 'text';\n    if (/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'number';\n    } else if (messageToSend.trim().length <= 3 && !/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'short-answer';\n    }\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date(),\n      messageType: messageType\n    };\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: _error => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  scrollToBottom() {\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  trackById(_index, message) {\n    return message.id;\n  }\n  loadRestaurantData() {\n    if (!this.tenantId) {\n      console.log('Not loading restaurant data: no tenantId');\n      return;\n    }\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data loaded:', data);\n        if (data) {\n          this.restaurantData = data;\n        } else {\n          console.warn('No restaurant data available');\n          this.restaurantData = null;\n        }\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading restaurant data:', error);\n        this.restaurantData = null;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  loadConversationHistory() {\n    if (!this.tenantId || this.loadingHistory) {\n      return;\n    }\n    this.loadingHistory = true;\n    this.isConnecting = true;\n    this.messages = [];\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        if (messages && messages.length > 0) {\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          this.messages = messages;\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          this.messages = [];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n        this.loadRestaurantData();\n      },\n      error: error => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.messages = [];\n        this.cd.detectChanges();\n        this.loadRestaurantData();\n      }\n    });\n  }\n  clearConversationHistory() {\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  startConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    this.conversationStarted = true;\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    this.initiateConversation();\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n  initiateConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {},\n      error: error => {\n        console.error('Error initiating conversation:', error);\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  refreshRestaurantData() {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: error => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 12,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [\"class\", \"chat-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-data-panel\"], [1, \"panel-header\"], [1, \"header-left\"], [1, \"header-right\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh restaurant data\", 1, \"action-button\", \"refresh-button\", 3, \"disabled\", \"click\"], [1, \"panel-content\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"chat-overlay\", 3, \"click\"], [1, \"overlay-content\"], [1, \"overlay-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-button\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\", 3, \"ngClass\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-text\"], [1, \"no-data-message\"], [1, \"data-section\", \"summary-section\"], [1, \"data-item\"], [1, \"label\"], [1, \"value\"], [\"class\", \"data-section outlet-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"data-section\", \"outlet-section\"], [\"class\", \"work-area-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"work-area-tag\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ChatBotComponent_div_2_Template, 13, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_10_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 6, 16, \"div\", 10);\n          i0.ɵɵtemplate(15, ChatBotComponent_div_15_Template, 8, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"mat-form-field\", 13)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_18_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_19_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"div\", 18)(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"restaurant_menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h2\");\n          i0.ɵɵtext(28, \"Restaurant Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_30_listener() {\n            return ctx.refreshRestaurantData();\n          });\n          i0.ɵɵelementStart(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"div\", 21);\n          i0.ɵɵtemplate(34, ChatBotComponent_div_34_Template, 7, 0, \"div\", 22);\n          i0.ɵɵtemplate(35, ChatBotComponent_ng_container_35_Template, 34, 8, \"ng-container\", 23);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.conversationStarted);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"rotating\", ctx.isRefreshing);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantData);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MatDialogModule, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  height: 100%;\\n  gap: 20px;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 0.6; \\n\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n  position: relative; \\n\\n}\\n\\n.restaurant-data-panel[_ngcontent-%COMP%] {\\n  flex: 0.4; \\n\\n  width: auto; \\n\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 80%;\\n}\\n\\n.overlay-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f57c00; \\n\\n  margin-bottom: 1rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-size: 1rem;\\n  background-color: #f57c00; \\n\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  transition: background-color 0.3s;\\n}\\n\\n.start-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800; \\n\\n}\\n\\n.start-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: auto; \\n\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n  flex-wrap: wrap; \\n\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n  display: flex;\\n  justify-content: flex-end; \\n\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n  display: flex;\\n  justify-content: flex-end; \\n\\n  max-width: 85%; \\n\\n  width: auto; \\n\\n}\\n\\n\\n\\n.user-message.number-container[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  width: auto !important;\\n  padding: 4px 12px !important;\\n  border-radius: 16px !important;\\n  background-color: #e8e8e8;\\n  text-align: center;\\n}\\n\\n\\n\\n.user-message.short-answer-container[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  width: auto !important;\\n  padding: 4px 12px !important;\\n  border-radius: 16px !important;\\n  background-color: #e8e8e8;\\n  text-align: center;\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 10px 14px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  width: auto; \\n\\n  word-wrap: break-word;\\n  word-break: break-word; \\n\\n  overflow-wrap: break-word; \\n\\n  line-height: 1.5;\\n  \\n\\n  \\n\\n  \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap; \\n\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n  width: 100%; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.6rem;\\n  margin-top: 2px;\\n  margin-left: 0 !important;\\n  text-align: center;\\n  opacity: 0.7;\\n  align-self: center;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  flex: 1;\\n  width: 100%; \\n\\n  max-width: 100%; \\n\\n  display: inline-block; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   .number-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   .short-answer-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-align: center;\\n  padding: 0 !important;\\n  margin: 0 !important;\\n  flex: none;\\n  width: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.8em; \\n\\n  font-size: 16px; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.8em; \\n\\n  margin-bottom: 0.8em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5em; \\n\\n  font-size: 16px; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n  white-space: pre-wrap; \\n\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  max-width: 100%;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n  white-space: pre-wrap; \\n\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  max-width: 100%;\\n  width: 100%;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  line-height: 1.5;\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  white-space: normal;\\n  width: 100%;\\n  max-width: 100%;\\n}\\n\\n\\n\\n.number-message[_ngcontent-%COMP%] {\\n  min-width: 30px !important;\\n  max-width: 50px !important;\\n  min-height: 28px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2px 8px !important;\\n  border-radius: 14px !important;\\n}\\n\\n\\n\\n.short-answer-message[_ngcontent-%COMP%] {\\n  min-width: 30px !important;\\n  max-width: 80px !important;\\n  min-height: 28px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2px 8px !important;\\n  border-radius: 14px !important;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #757575;\\n  height: 100%;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 20px;\\n  transition: all 0.3s ease;\\n}\\n.no-data-message[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #f57c00; \\n\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n  max-width: 240px;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  transition: all 0.3s ease;\\n  margin-left: 4px;\\n}\\n.panel-header[_ngcontent-%COMP%]   .refresh-button[_ngcontent-%COMP%]:hover {\\n  color: #2196f3;\\n}\\n.panel-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\\n  color: #4caf50;\\n}\\n.panel-header[_ngcontent-%COMP%]   .delete-button[_ngcontent-%COMP%]:hover {\\n  color: #f44336;\\n}\\n.panel-header[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1.5s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n  color: #f57c00; \\n\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  overflow-y: auto;\\n  flex: 1;\\n  background-color: #f5f5f5;\\n  gap: 20px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n}\\n.data-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.data-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n}\\n\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n  border-bottom: 1px solid #f0f0f0;\\n  padding-bottom: 12px;\\n}\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #f57c00;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  background-color: #fff9f0;\\n}\\n\\n.outlet-section[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n}\\n\\n.work-area-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  padding: 6px 0;\\n  border-bottom: 1px dashed #f0f0f0;\\n}\\n.data-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #444;\\n  min-width: 140px;\\n  padding-right: 12px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #333;\\n  flex: 1;\\n  word-break: break-word;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: auto;\\n  justify-content: flex-start;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 3px;\\n  background-color: #57705d;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  font-size: 13px;\\n  color: #555;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-4px) scale(1.2);\\n    opacity: 0.9;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MatDialogModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatBotComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "startConversation", "ɵɵtemplate", "ChatBotComponent_div_2_span_11_Template", "ChatBotComponent_div_2_span_12_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "messages", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r9", "timestamp", "ɵɵelement", "ChatBotComponent_div_14_div_5_Template", "ɵɵpureFunction5", "_c0", "sender", "messageType", "ɵɵpureFunction3", "_c1", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate1", "ctx_r12", "restaurantData", "commonCuisinesAcrossOutlets", "join", "ctx_r14", "signatureElements", "signatureDishes", "ChatBotComponent_ng_container_35_div_33_span_14_span_1_span_2_Template", "area_r22", "last_r23", "ChatBotComponent_ng_container_35_div_33_span_14_span_1_Template", "outlet_r17", "outletWorkAreas", "ChatBotComponent_ng_container_35_div_33_span_14_Template", "ChatBotComponent_ng_container_35_div_33_span_15_Template", "ɵɵtextInterpolate2", "i_r18", "outletName", "outletAddress", "ɵɵelementContainerStart", "ChatBotComponent_ng_container_35_span_15_Template", "ChatBotComponent_ng_container_35_span_16_Template", "ChatBotComponent_ng_container_35_span_21_Template", "ChatBotComponent_ng_container_35_span_22_Template", "ChatBotComponent_ng_container_35_div_33_Template", "ɵɵelementContainerEnd", "ctx_r4", "totalOutlets", "beverageInfo", "alcoholService", "tobaccoInfo", "tobaccoService", "outletDetails", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "currentMessage", "isConnecting", "isWaitingForResponse", "conversationStarted", "isRefreshing", "messageSubscription", "connectionSubscription", "dataUpdateSubscription", "loadingHistory", "ngOnChanges", "changes", "newTenantId", "currentValue", "prevTenantId", "previousValue", "loadConversationHistory", "ngOnInit", "conversationStarted<PERSON><PERSON>", "localStorage", "getItem", "setTimeout", "setItem", "initiateConversation", "detectChanges", "scrollToBottom", "messages$", "subscribe", "message", "id", "startsWith", "trim", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "thinkingIndex", "push", "sort", "a", "b", "getTime", "lastMessage", "isDuplicate", "some", "Math", "abs", "dataUpdates$", "data", "type", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "open", "duration", "messageToSend", "test", "userMessage", "generateId", "Date", "next", "error", "_error", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "loadRestaurantData", "console", "log", "fetchRestaurantData", "warn", "clearConversationHistory", "removeItem", "refreshRestaurantData", "Object", "keys", "panelClass", "onAction", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_div_2_Template", "ChatBotComponent_Template_button_click_10_listener", "ChatBotComponent_div_14_Template", "ChatBotComponent_div_15_Template", "ChatBotComponent_Template_input_ngModelChange_18_listener", "$event", "ChatBotComponent_Template_input_keydown_18_listener", "ChatBotComponent_Template_button_click_19_listener", "ChatBotComponent_Template_button_click_30_listener", "ChatBotComponent_div_34_Template", "ChatBotComponent_ng_container_35_Template", "ɵɵclassProp", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    MatDialogModule,\n    SafeHtmlPipe,\n    MarkdownPipe,\n    ConfirmDialogComponent\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n  restaurantData: any = null;\n  conversationStarted: boolean = false;\n  isRefreshing: boolean = false;\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n  private dataUpdateSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar,\n  ) { }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      if (newTenantId && prevTenantId !== newTenantId) {\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  private loadingHistory = false;\n\n  ngOnInit(): void {\n    this.messages = [];\n    if (this.tenantId) {\n      this.loadingHistory = false;\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      this.loadConversationHistory();\n\n      setTimeout(() => {\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else if (this.conversationStarted) {\n          this.initiateConversation();\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000);\n    } else {\n      this.messages = [];\n    }\n\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        if (message.sender === 'system') {\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          return;\n        }\n\n        if (message.sender === 'bot') {\n          if (!message.text.trim()) {\n            return;\n          }\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            if (message.text !== 'DIGI is thinking...') {\n              this.messages[existingMessageIndex] = message;\n            }\n          } else {\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            const thinkingIndex = this.messages.findIndex(m =>\n              m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id\n            );\n\n            if (thinkingIndex !== -1) {\n              this.messages[thinkingIndex] = message;\n            } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n              this.messages.push(message);\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n\n          if (!message.text.includes('blinking-cursor')) {\n            this.isWaitingForResponse = false;\n          }\n\n          if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n            const lastMessage = this.messages[this.messages.length - 1];\n            if (lastMessage.sender !== 'user') {\n              return;\n            }\n          }\n\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        } else if (message.sender === 'user') {\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000\n          );\n\n          if (!isDuplicate) {\n            this.messages.push(message);\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n          }\n        }\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }\n    );\n\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(\n      (data: any) => {\n        if (data && data.type === 'restaurant_data') {\n          if (data.data) {\n            this.restaurantData = data.data;\n          } else {\n            this.restaurantData = null;\n          }\n          setTimeout(() => {\n            this.cd.detectChanges();\n          }, 0);\n        }\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    this.sseService.disconnect();\n  }\n\n\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    let messageType: 'text' | 'number' | 'short-answer' | 'system-message' = 'text';\n\n    if (/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'number';\n    }\n    else if (messageToSend.trim().length <= 3 && !/^\\d+$/.test(messageToSend.trim())) {\n      messageType = 'short-answer';\n    }\n\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date(),\n      messageType: messageType\n    };\n\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (_error) => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  private scrollToBottom(): void {\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n\n  loadRestaurantData(): void {\n    if (!this.tenantId) {\n      console.log('Not loading restaurant data: no tenantId');\n      return;\n    }\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data loaded:', data);\n        if (data) {\n          this.restaurantData = data;\n        } else {\n          console.warn('No restaurant data available');\n          this.restaurantData = null;\n        }\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error loading restaurant data:', error);\n        this.restaurantData = null;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.loadingHistory) {\n      return;\n    }\n\n    this.loadingHistory = true;\n    this.isConnecting = true;\n    this.messages = [];\n\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        if (messages && messages.length > 0) {\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          this.messages = messages;\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          this.messages = [];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n        this.loadRestaurantData();\n      },\n      error: (error) => {\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.messages = [];\n        this.cd.detectChanges();\n        this.loadRestaurantData();\n      }\n    });\n  }\n\n  clearConversationHistory(): void {\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          this.messages = [];\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          this.restaurantData = null;\n          this.loadingHistory = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n\n  startConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n    this.conversationStarted = true;\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    this.initiateConversation();\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n\n  private initiateConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n      },\n      error: (error) => {\n        console.error('Error initiating conversation:', error);\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n\n\n  refreshRestaurantData(): void {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: (error) => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n}\n", "<div class=\"chat-layout\">\n  <div class=\"chat-container\">\n  <!-- Start/Resume Overlay -->\n  <div class=\"chat-overlay\" *ngIf=\"!conversationStarted\" (click)=\"startConversation()\">\n    <div class=\"overlay-content\">\n      <mat-icon class=\"overlay-icon\">restaurant</mat-icon>\n      <h2>Restaurant Onboarding Assistant</h2>\n      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>\n      <button mat-raised-button color=\"primary\" class=\"start-button\">\n        <mat-icon>play_arrow</mat-icon>\n        <span *ngIf=\"messages.length === 0\">Click to Start</span>\n        <span *ngIf=\"messages.length > 0\">Click to Resume</span>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n      <span class=\"assistant-title\">Restaurant details</span>\n    </div>\n    <div class=\"chat-actions\">\n      <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n        <mat-icon>clear</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{\n        'user-message': message.sender === 'user',\n        'bot-message': message.sender === 'bot',\n        'number-container': message.messageType === 'number',\n        'short-answer-container': message.messageType === 'short-answer',\n        'system-message-container': message.messageType === 'system-message'\n      }\">\n      <div class=\"message-content\" [ngClass]=\"{\n        'number-message': message.messageType === 'number',\n        'short-answer-message': message.messageType === 'short-answer',\n        'system-message': message.messageType === 'system-message'\n      }\">\n        <div class=\"message-wrapper\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n          <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Improved loading indicator when waiting for a response -->\n    <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n      <div class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <div class=\"typing-text\">DIGI is thinking...</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"isConnecting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n</div>\n\n  <!-- Restaurant Data Panel -->\n  <div class=\"restaurant-data-panel\">\n    <div class=\"panel-header\">\n      <div class=\"header-left\">\n        <mat-icon>restaurant_menu</mat-icon>\n        <h2>Restaurant Information</h2>\n      </div>\n      <div class=\"header-right\">\n        <button mat-icon-button class=\"action-button refresh-button\" (click)=\"refreshRestaurantData()\" matTooltip=\"Refresh restaurant data\" [disabled]=\"isRefreshing\">\n          <mat-icon [class.rotating]=\"isRefreshing\">refresh</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"panel-content\">\n      <!-- No Data Message -->\n      <div class=\"no-data-message\" *ngIf=\"!restaurantData\">\n        <mat-icon>restaurant</mat-icon>\n        <h3>No Restaurant Data Yet</h3>\n        <p>As you provide information about your restaurant through the chat, it will appear here.</p>\n      </div>\n\n      <!-- Restaurant Data (shown when available) -->\n      <ng-container *ngIf=\"restaurantData\">\n        <!-- Summary Section -->\n        <div class=\"data-section summary-section\">\n          <h3><mat-icon>info</mat-icon> Restaurant Summary</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Total Outlets:</span>\n            <span class=\"value\">{{restaurantData.totalOutlets}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Cuisines:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.commonCuisinesAcrossOutlets && restaurantData.commonCuisinesAcrossOutlets.length > 0\">\n                {{restaurantData.commonCuisinesAcrossOutlets.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.commonCuisinesAcrossOutlets || restaurantData.commonCuisinesAcrossOutlets.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Signature Dishes:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.signatureElements && restaurantData.signatureElements.signatureDishes && restaurantData.signatureElements.signatureDishes.length > 0\">\n                {{restaurantData.signatureElements.signatureDishes.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.signatureElements || !restaurantData.signatureElements.signatureDishes || restaurantData.signatureElements.signatureDishes.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Alcohol Service:</span>\n            <span class=\"value\">{{restaurantData.beverageInfo?.alcoholService || 'Not specified'}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Tobacco Service:</span>\n            <span class=\"value\">{{restaurantData.tobaccoInfo?.tobaccoService || 'Not specified'}}</span>\n          </div>\n        </div>\n\n        <!-- Outlets Section -->\n        <div class=\"data-section outlet-section\" *ngFor=\"let outlet of restaurantData.outletDetails; let i = index\">\n          <h3><mat-icon>store</mat-icon> Outlet {{i+1}}: {{outlet.outletName}}</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Address:</span>\n            <span class=\"value\">{{outlet.outletAddress}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Work Areas:</span>\n            <span class=\"value\">\n              <span *ngIf=\"outlet.outletWorkAreas && outlet.outletWorkAreas.length > 0\">\n                <span *ngFor=\"let area of outlet.outletWorkAreas; let last = last\" class=\"work-area-tag\">\n                  {{area}}<span *ngIf=\"!last\">, </span>\n                </span>\n              </span>\n              <span *ngIf=\"!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,eAAe,QAAQ,0BAA0B;AAG1D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICJhDC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAR9DH,EAAA,CAAAC,cAAA,cAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,qDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAClFX,EAAA,CAAAC,cAAA,cAA6B;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2FAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3FH,EAAA,CAAAC,cAAA,iBAA+D;IACnDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAY,UAAA,KAAAC,uCAAA,mBAAyD;IACzDb,EAAA,CAAAY,UAAA,KAAAE,uCAAA,mBAAwD;IAC1Dd,EAAA,CAAAG,YAAA,EAAS;;;;IAFAH,EAAA,CAAAe,SAAA,IAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAA2B;IAC3BnB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAAyB;;;;;IAgC9BnB,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAC,UAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;;;;;;;;;;;IAdnHvB,EAAA,CAAAC,cAAA,cAMK;IAOCD,EAAA,CAAAwB,SAAA,cAAsE;;IACtExB,EAAA,CAAAY,UAAA,IAAAa,sCAAA,kBAAmH;IACrHzB,EAAA,CAAAG,YAAA,EAAM;;;;IAf0EH,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAM,MAAA,aAAAN,UAAA,CAAAM,MAAA,YAAAN,UAAA,CAAAO,WAAA,eAAAP,UAAA,CAAAO,WAAA,qBAAAP,UAAA,CAAAO,WAAA,uBAMhF;IAC2B7B,EAAA,CAAAe,SAAA,GAI3B;IAJ2Bf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA8B,eAAA,KAAAC,GAAA,EAAAT,UAAA,CAAAO,WAAA,eAAAP,UAAA,CAAAO,WAAA,qBAAAP,UAAA,CAAAO,WAAA,uBAI3B;IAE4B7B,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,cAAAhB,EAAA,CAAAgC,WAAA,OAAAV,UAAA,CAAAW,IAAA,GAAAjC,EAAA,CAAAkC,cAAA,CAAqC;IAC/BlC,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,SAAAM,UAAA,CAAAM,MAAA,cAAiC;;;;;IAMvE5B,EAAA,CAAAC,cAAA,cAAwE;IAGlED,EAAA,CAAAwB,SAAA,WAAa;IAGfxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAmCpDH,EAAA,CAAAC,cAAA,cAAqD;IACzCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8FAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAexFH,EAAA,CAAAC,cAAA,WAAkH;IAChHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmC,kBAAA,MAAAC,OAAA,CAAAC,cAAA,CAAAC,2BAAA,CAAAC,IAAA,YACF;;;;;IACAvC,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMPH,EAAA,CAAAC,cAAA,WAAkK;IAChKD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmC,kBAAA,MAAAK,OAAA,CAAAH,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAAH,IAAA,YACF;;;;;IACAvC,EAAA,CAAAC,cAAA,WAAsK;IACpKD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAyBKH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADvCH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAY,UAAA,IAAA+B,sEAAA,mBAA6B;IACvC3C,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAe,SAAA,GAAQ;IAARf,EAAA,CAAAmC,kBAAA,MAAAS,QAAA,KAAQ;IAAO5C,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAA6B,QAAA,CAAW;;;;;IAF9B7C,EAAA,CAAAC,cAAA,WAA0E;IACxED,EAAA,CAAAY,UAAA,IAAAkC,+DAAA,mBAEO;IACT9C,EAAA,CAAAG,YAAA,EAAO;;;;IAHkBH,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,YAAA+B,UAAA,CAAAC,eAAA,CAA2B;;;;;IAIpDhD,EAAA,CAAAC,cAAA,WAA6E;IAC3ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBbH,EAAA,CAAAC,cAAA,cAA4G;IAC5FD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAqC,wDAAA,mBAIO;IACPjD,EAAA,CAAAY,UAAA,KAAAsC,wDAAA,mBAEO;IACTlD,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBsBH,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAmD,kBAAA,aAAAC,KAAA,YAAAL,UAAA,CAAAM,UAAA,KAAqC;IAG9CrD,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAoB,iBAAA,CAAA2B,UAAA,CAAAO,aAAA,CAAwB;IAKnCtD,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAgB,UAAA,SAAA+B,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAA7B,MAAA,KAAiE;IAKjEnB,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,UAAA,UAAA+B,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAA7B,MAAA,OAAoE;;;;;IAvDnFnB,EAAA,CAAAuD,uBAAA,GAAqC;IAEnCvD,EAAA,CAAAC,cAAA,cAA0C;IAC1BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAA4C,iDAAA,mBAEO;IACPxD,EAAA,CAAAY,UAAA,KAAA6C,iDAAA,mBAEO;IACTzD,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAA8C,iDAAA,mBAEO;IACP1D,EAAA,CAAAY,UAAA,KAAA+C,iDAAA,mBAEO;IACT3D,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/FH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKhGH,EAAA,CAAAY,UAAA,KAAAgD,gDAAA,mBAmBM;IACR5D,EAAA,CAAA6D,qBAAA,EAAe;;;;IAvDW7D,EAAA,CAAAe,SAAA,IAA+B;IAA/Bf,EAAA,CAAAoB,iBAAA,CAAA0C,MAAA,CAAAzB,cAAA,CAAA0B,YAAA,CAA+B;IAK1C/D,EAAA,CAAAe,SAAA,GAAyG;IAAzGf,EAAA,CAAAgB,UAAA,SAAA8C,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAnB,MAAA,KAAyG;IAGzGnB,EAAA,CAAAe,SAAA,GAA4G;IAA5Gf,EAAA,CAAAgB,UAAA,UAAA8C,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAnB,MAAA,OAA4G;IAQ5GnB,EAAA,CAAAe,SAAA,GAAyJ;IAAzJf,EAAA,CAAAgB,UAAA,SAAA8C,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,IAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAAvB,MAAA,KAAyJ;IAGzJnB,EAAA,CAAAe,SAAA,GAA6J;IAA7Jf,EAAA,CAAAgB,UAAA,UAAA8C,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,KAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAAvB,MAAA,OAA6J;IAOlJnB,EAAA,CAAAe,SAAA,GAAkE;IAAlEf,EAAA,CAAAoB,iBAAA,EAAA0C,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,kBAAAF,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,CAAAC,cAAA,qBAAkE;IAIlEjE,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAoB,iBAAA,EAAA0C,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,kBAAAJ,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,CAAAC,cAAA,qBAAiE;IAK7BnE,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,YAAA8C,MAAA,CAAAzB,cAAA,CAAA+B,aAAA,CAAiC;;;ADxHrG,MAuBaC,gBAAgB;EAgB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAlBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAzD,QAAQ,GAAkB,EAAE;IAC5B,KAAA0D,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAzC,cAAc,GAAQ,IAAI;IAC1B,KAAA0C,mBAAmB,GAAY,KAAK;IACpC,KAAAC,YAAY,GAAY,KAAK;IAErB,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,sBAAsB,GAAwB,IAAI;IAmBlD,KAAAC,cAAc,GAAG,KAAK;EAb1B;EAEJC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAGD,OAAO,CAAC,UAAU,CAAC,CAACE,YAAY;MACpD,MAAMC,YAAY,GAAGH,OAAO,CAAC,UAAU,CAAC,CAACI,aAAa;MACtD,IAAIH,WAAW,IAAIE,YAAY,KAAKF,WAAW,EAAE;QAC/C,IAAI,CAACH,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACO,uBAAuB,EAAE;;;EAGpC;EAIAC,QAAQA,CAAA;IACN,IAAI,CAAC1E,QAAQ,GAAG,EAAE;IAClB,IAAI,IAAI,CAACwD,QAAQ,EAAE;MACjB,IAAI,CAACU,cAAc,GAAG,KAAK;MAC3B,MAAMS,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;MACtE,IAAI,CAACK,mBAAmB,GAAGe,YAAY,CAACC,OAAO,CAACF,sBAAsB,CAAC,KAAK,MAAM;MAClF,IAAI,CAACF,uBAAuB,EAAE;MAE9BK,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC9E,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAAC4D,mBAAmB,GAAG,IAAI;UAC/Be,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM,IAAI,IAAI,CAACd,mBAAmB,EAAE;UACnC,IAAI,CAACmB,oBAAoB,EAAE;;QAE7B,IAAI,CAAC1B,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC,EAAE,IAAI,CAAC;KACT,MAAM;MACL,IAAI,CAAClF,QAAQ,GAAG,EAAE;;IAGpB,IAAI,CAAC+D,mBAAmB,GAAG,IAAI,CAACV,UAAU,CAAC8B,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB,IAAIA,OAAO,CAAC3E,MAAM,KAAK,QAAQ,EAAE;QAC/B,IAAI2E,OAAO,CAACC,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAC3B,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;UACvB;;QAEF;;MAGF,IAAII,OAAO,CAAC3E,MAAM,KAAK,KAAK,EAAE;QAC5B,IAAI,CAAC2E,OAAO,CAACtE,IAAI,CAACyE,IAAI,EAAE,EAAE;UACxB;;QAEF,MAAMC,oBAAoB,GAAG,IAAI,CAACzF,QAAQ,CAAC0F,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC;QAE9E,IAAIG,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B,IAAIJ,OAAO,CAACtE,IAAI,KAAK,qBAAqB,EAAE;YAC1C,IAAI,CAACf,QAAQ,CAACyF,oBAAoB,CAAC,GAAGJ,OAAO;;SAEhD,MAAM;UACL,MAAMO,gBAAgB,GAAG,IAAI,CAAC5F,QAAQ,CAAC6F,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACjF,MAAM,KAAK,KAAK,IAAIiF,CAAC,CAAC5E,IAAI,KAAKsE,OAAO,CAACtE,IAAI,IAAI,CAAC4E,CAAC,CAAC5E,IAAI,CAAC+E,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,MAAMC,aAAa,GAAG,IAAI,CAAC/F,QAAQ,CAAC0F,SAAS,CAACC,CAAC,IAC7CA,CAAC,CAACjF,MAAM,KAAK,KAAK,IAAIiF,CAAC,CAAC5E,IAAI,KAAK,mBAAmB,IAAI4E,CAAC,CAACL,EAAE,KAAKD,OAAO,CAACC,EAAE,CAC5E;UAED,IAAIS,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC/F,QAAQ,CAAC+F,aAAa,CAAC,GAAGV,OAAO;WACvC,MAAM,IAAI,CAACO,gBAAgB,IAAIP,OAAO,CAACtE,IAAI,KAAK,mBAAmB,EAAE;YACpE,IAAI,CAACf,QAAQ,CAACgG,IAAI,CAACX,OAAO,CAAC;YAC3B,IAAI,CAACrF,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7F,SAAS,CAAC+F,OAAO,EAAE,GAAGD,CAAC,CAAC9F,SAAS,CAAC+F,OAAO,EAAE,CAAC;;;QAI/E,IAAI,CAACf,OAAO,CAACtE,IAAI,CAAC+E,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C,IAAI,CAAClC,oBAAoB,GAAG,KAAK;;QAGnC,IAAIyB,OAAO,CAACtE,IAAI,CAAC+E,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC9F,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACvE,MAAMoG,WAAW,GAAG,IAAI,CAACrG,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;UAC3D,IAAIoG,WAAW,CAAC3F,MAAM,KAAK,MAAM,EAAE;YACjC;;;QAIJ,IAAI,CAAC4C,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM,IAAIG,OAAO,CAAC3E,MAAM,KAAK,MAAM,EAAE;QACpC,MAAM4F,WAAW,GAAG,IAAI,CAACtG,QAAQ,CAACuG,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACjF,MAAM,KAAK,MAAM,IACnBiF,CAAC,CAAC5E,IAAI,KAAKsE,OAAO,CAACtE,IAAI,IACvByF,IAAI,CAACC,GAAG,CAACd,CAAC,CAACtF,SAAS,CAAC+F,OAAO,EAAE,GAAGf,OAAO,CAAChF,SAAS,CAAC+F,OAAO,EAAE,CAAC,GAAG,IAAI,CACrE;QAED,IAAI,CAACE,WAAW,EAAE;UAChB,IAAI,CAACtG,QAAQ,CAACgG,IAAI,CAACX,OAAO,CAAC;UAC3B,IAAI,CAACrF,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7F,SAAS,CAAC+F,OAAO,EAAE,GAAGD,CAAC,CAAC9F,SAAS,CAAC+F,OAAO,EAAE,CAAC;SAC5E,MAAM,C;;MAGT,IAAI,CAAC9C,EAAE,CAAC2B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED,IAAI,CAACjB,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAACqD,YAAY,CAACtB,SAAS,CACjEuB,IAAS,IAAI;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;QAC3C,IAAID,IAAI,CAACA,IAAI,EAAE;UACb,IAAI,CAACxF,cAAc,GAAGwF,IAAI,CAACA,IAAI;SAChC,MAAM;UACL,IAAI,CAACxF,cAAc,GAAG,IAAI;;QAE5B2D,UAAU,CAAC,MAAK;UACd,IAAI,CAACxB,EAAE,CAAC2B,aAAa,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC;;IAET,CAAC,CACF;EACH;EAEA4B,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC+C,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAAC9C,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAAC8C,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAAC7C,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAAC6C,WAAW,EAAE;;IAE3C,IAAI,CAACzD,UAAU,CAAC0D,UAAU,EAAE;EAC9B;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACtD,cAAc,CAAC8B,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC0D,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,CAACvD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,MAAMuD,aAAa,GAAG,IAAI,CAACzD,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACJ,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAItE,WAAW,GAA0D,MAAM;IAE/E,IAAI,OAAO,CAACyG,IAAI,CAACD,aAAa,CAAC3B,IAAI,EAAE,CAAC,EAAE;MACtC7E,WAAW,GAAG,QAAQ;KACvB,MACI,IAAIwG,aAAa,CAAC3B,IAAI,EAAE,CAACvF,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAACmH,IAAI,CAACD,aAAa,CAAC3B,IAAI,EAAE,CAAC,EAAE;MAChF7E,WAAW,GAAG,cAAc;;IAG9B,MAAM0G,WAAW,GAAgB;MAC/B/B,EAAE,EAAE,IAAI,CAACgC,UAAU,EAAE;MACrBvG,IAAI,EAAEoG,aAAa;MACnBzG,MAAM,EAAE,MAAM;MACdL,SAAS,EAAE,IAAIkH,IAAI,EAAE;MACrB5G,WAAW,EAAEA;KACd;IAED,MAAM2F,WAAW,GAAG,IAAI,CAACtG,QAAQ,CAACuG,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACjF,MAAM,KAAK,MAAM,IACnBiF,CAAC,CAAC5E,IAAI,KAAKoG,aAAa,CACzB;IAED,IAAI,CAACb,WAAW,EAAE;MAChB,IAAI,CAACtG,QAAQ,CAACgG,IAAI,CAACqB,WAAW,CAAC;;IAGjC,IAAI,CAACzD,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAAC7B,UAAU,CAAC2D,WAAW,CAAC,IAAI,CAACxD,QAAQ,EAAE2D,aAAa,CAAC,CAAC/B,SAAS,CAAC;MAClEoC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC7D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;MACzB,CAAC;MACDwC,KAAK,EAAGC,MAAM,IAAI;QAChB,IAAI,CAAC/D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACL,QAAQ,CAAC0D,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAC5D,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAGA0C,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACf,WAAW,EAAE;;EAEtB;EAEQ9B,cAAcA,CAAA;IACpB8C,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAGQf,UAAUA,CAAA;IAChB,OAAOd,IAAI,CAAC8B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGhC,IAAI,CAAC8B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEAC,SAASA,CAACC,MAAc,EAAErD,OAAoB;IAC5C,OAAOA,OAAO,CAACC,EAAE;EACnB;EAGAqD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACnF,QAAQ,EAAE;MAClBoF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAACxF,UAAU,CAACyF,mBAAmB,CAAC,IAAI,CAACtF,QAAQ,CAAC,CAAC4B,SAAS,CAAC;MAC3DoC,IAAI,EAAGb,IAAI,IAAI;QACbiC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAElC,IAAI,CAAC;QAC5C,IAAIA,IAAI,EAAE;UACR,IAAI,CAACxF,cAAc,GAAGwF,IAAI;SAC3B,MAAM;UACLiC,OAAO,CAACG,IAAI,CAAC,8BAA8B,CAAC;UAC5C,IAAI,CAAC5H,cAAc,GAAG,IAAI;;QAE5B,IAAI,CAACmC,EAAE,CAAC2B,aAAa,EAAE;MACzB,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfmB,OAAO,CAACnB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACtG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACmC,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAGAR,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACjB,QAAQ,IAAI,IAAI,CAACU,cAAc,EAAE;MACzC;;IAGF,IAAI,CAACA,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACP,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC3D,QAAQ,GAAG,EAAE;IAElB,IAAI,CAACqD,UAAU,CAACoB,uBAAuB,CAAC,IAAI,CAACjB,QAAQ,EAAE,KAAK,CAAC,CAAC4B,SAAS,CAAC;MACtEoC,IAAI,EAAGxH,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACnCD,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACrB,OAAO,IAAIoB,IAAI,CAACrB,CAAC,CAAC7F,SAAS,CAAC,CAAC+F,OAAO,EAAE,GAAG,IAAImB,IAAI,CAACpB,CAAC,CAAC9F,SAAS,CAAC,CAAC+F,OAAO,EAAE;UAC1E,CAAC,CAAC;UACF,IAAI,CAACpG,QAAQ,GAAGA,QAAQ;UACxB,IAAI,CAAC6D,mBAAmB,GAAG,IAAI;UAC/B,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM;UACL,IAAI,CAAC3E,QAAQ,GAAG,EAAE;;QAGpB,IAAI,CAAC2D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACyD,kBAAkB,EAAE;MAC3B,CAAC;MACDlB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC5D,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACsD,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAAC0D,kBAAkB,EAAE;MAC3B;KACD,CAAC;EACJ;EAEAK,wBAAwBA,CAAA;IACtB,IAAI,CAACrF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;IAEvB,IAAI,IAAI,CAACzB,QAAQ,EAAE;MACjBoF,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAACrF,QAAQ,CAAC;MAC3F,IAAI,CAACH,UAAU,CAAC2F,wBAAwB,CAAC,IAAI,CAACxF,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC4B,SAAS,CAAC;QAC5EoC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACxH,QAAQ,GAAG,EAAE;UAClB,IAAI,CAAC6D,mBAAmB,GAAG,KAAK;UAChC,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACqE,UAAU,CAACtE,sBAAsB,CAAC;UAC/C,IAAI,CAACxD,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC;QACDuC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACzH,QAAQ,GAAG,EAAE;UAClB,IAAI,CAAC6D,mBAAmB,GAAG,KAAK;UAChC,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;UACtEoB,YAAY,CAACqE,UAAU,CAACtE,sBAAsB,CAAC;UAC/C,IAAI,CAACxD,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAClF,QAAQ,GAAG,CACd;QACEsF,EAAE,EAAE,IAAI,CAACgC,UAAU,EAAE;QACrBvG,IAAI,EAAE,0DAA0D;QAChEL,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAIkH,IAAI;OACpB,CACF;MAED,IAAI,CAACrD,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;MACzB,IAAI,CAACL,EAAE,CAAC2B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;;EAEzB;EAGAzF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC+D,QAAQ,EAAE;MAClB;;IAEF,IAAI,CAACK,mBAAmB,GAAG,IAAI;IAC/B,MAAMc,sBAAsB,GAAG,wBAAwB,IAAI,CAACnB,QAAQ,EAAE;IACtEoB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;IACpD,IAAI,CAACK,oBAAoB,EAAE;IAC3B,IAAI,CAAC1B,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQF,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE;MAClB;;IAEF,IAAI,CAACI,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;IACvB,IAAI,CAAC5B,UAAU,CAAC2D,WAAW,CAAC,IAAI,CAACxD,QAAQ,EAAE,2BAA2B,CAAC,CAAC4B,SAAS,CAAC;MAChFoC,IAAI,EAAEA,CAAA,KAAK,CACX,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfmB,OAAO,CAACnB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC7D,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAAC2B,aAAa,EAAE;QACvB,IAAI,CAAC1B,QAAQ,CAAC0D,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;MACJ;KACD,CAAC;EACJ;EAGAgC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC1F,QAAQ,EAAE;MAClBoF,OAAO,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC5D,IAAI,CAACxF,QAAQ,CAAC0D,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;QAC1DC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,IAAI,CAACpD,YAAY,EAAE;MACrB8E,OAAO,CAACG,IAAI,CAAC,oCAAoC,CAAC;MAClD;;IAGF,IAAI,CAACjF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;IAEvB2D,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACrF,QAAQ,CAAC;IAEpE,IAAI,CAACH,UAAU,CAACyF,mBAAmB,CAAC,IAAI,CAACtF,QAAQ,CAAC,CAAC4B,SAAS,CAAC;MAC3DoC,IAAI,EAAGb,IAAI,IAAI;QACbiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAElC,IAAI,CAAC;QAC/C,IAAIA,IAAI,EAAE;UACRiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,MAAM,CAACC,IAAI,CAACzC,IAAI,CAAC,CAAC;UAC5D,IAAI,CAACxF,cAAc,GAAGwF,IAAI;UAC1B,IAAI,CAACpD,QAAQ,CAAC0D,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;YACpEC,QAAQ,EAAE,IAAI;YACdmC,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACLT,OAAO,CAACG,IAAI,CAAC,sCAAsC,CAAC;UACpD,IAAI,CAACxF,QAAQ,CAAC0D,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;;QAEJ,IAAI,CAACpD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;QACvB2D,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC1H,cAAc,CAAC;MACrE,CAAC;MACDsG,KAAK,EAAGA,KAAK,IAAI;QACfmB,OAAO,CAACnB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAClE,QAAQ,CAAC0D,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAC/DC,QAAQ,EAAE,IAAI;UACdmC,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAAClE,SAAS,CAAC,MAAK;UAC3B,IAAI,CAAC8D,qBAAqB,EAAE;QAC9B,CAAC,CAAC;QACF,IAAI,CAACpF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC2B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;;;uBA5bW9B,gBAAgB,EAAArE,EAAA,CAAAyK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA3K,EAAA,CAAAyK,iBAAA,CAAAzK,EAAA,CAAA4K,iBAAA,GAAA5K,EAAA,CAAAyK,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBzG,gBAAgB;MAAA0G,SAAA;MAAAC,MAAA;QAAAtG,QAAA;QAAAC,UAAA;MAAA;MAAAsG,UAAA;MAAAC,QAAA,GAAAlL,EAAA,CAAAmL,oBAAA,EAAAnL,EAAA,CAAAoL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1C7B1L,EAAA,CAAAC,cAAA,aAAyB;UAGvBD,EAAA,CAAAY,UAAA,IAAAgL,+BAAA,kBAWM;UAEN5L,EAAA,CAAAC,cAAA,aAAyB;UAEOD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAI,UAAA,mBAAAyL,mDAAA;YAAA,OAASF,GAAA,CAAAzB,wBAAA,EAA0B;UAAA,EAAC;UAClFlK,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKhCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAY,UAAA,KAAAkL,gCAAA,mBAiBM;UAGN9L,EAAA,CAAAY,UAAA,KAAAmL,gCAAA,kBASM;UACR/L,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAI,UAAA,2BAAA4L,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA/G,cAAA,GAAAqH,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAA9C,UAAA,CAAAoD,MAAA,CAAkB;UAAA,EAFD;UADnCjM,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAI,UAAA,mBAAA+L,mDAAA;YAAA,OAASR,GAAA,CAAAzD,WAAA,EAAa;UAAA,EAAC;UAC1DlI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7BH,EAAA,CAAAC,cAAA,eAAmC;UAGnBD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjCH,EAAA,CAAAC,cAAA,eAA0B;UACqCD,EAAA,CAAAI,UAAA,mBAAAgM,mDAAA;YAAA,OAAST,GAAA,CAAAvB,qBAAA,EAAuB;UAAA,EAAC;UAC5FpK,EAAA,CAAAC,cAAA,gBAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKlEH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAY,UAAA,KAAAyL,gCAAA,kBAIM;UAGNrM,EAAA,CAAAY,UAAA,KAAA0L,yCAAA,4BA6De;UACjBtM,EAAA,CAAAG,YAAA,EAAM;;;UA7JmBH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,UAAA2K,GAAA,CAAA5G,mBAAA,CAA0B;UA0B1B/E,EAAA,CAAAe,SAAA,IAAa;UAAbf,EAAA,CAAAgB,UAAA,YAAA2K,GAAA,CAAAzK,QAAA,CAAa,iBAAAyK,GAAA,CAAAhC,SAAA;UAoBhC3J,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAA2K,GAAA,CAAA7G,oBAAA,CAA0B;UAevB9E,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,YAAA2K,GAAA,CAAA/G,cAAA,CAA4B,aAAA+G,GAAA,CAAA9G,YAAA;UAKwB7E,EAAA,CAAAe,SAAA,GAAmD;UAAnDf,EAAA,CAAAgB,UAAA,cAAA2K,GAAA,CAAA/G,cAAA,CAAA8B,IAAA,MAAAiF,GAAA,CAAA9G,YAAA,CAAmD;UAcwB7E,EAAA,CAAAe,SAAA,IAAyB;UAAzBf,EAAA,CAAAgB,UAAA,aAAA2K,GAAA,CAAA3G,YAAA,CAAyB;UACjJhF,EAAA,CAAAe,SAAA,GAA+B;UAA/Bf,EAAA,CAAAuM,WAAA,aAAAZ,GAAA,CAAA3G,YAAA,CAA+B;UAOfhF,EAAA,CAAAe,SAAA,GAAqB;UAArBf,EAAA,CAAAgB,UAAA,UAAA2K,GAAA,CAAAtJ,cAAA,CAAqB;UAOpCrC,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAAgB,UAAA,SAAA2K,GAAA,CAAAtJ,cAAA,CAAoB;;;qBD3ErCjD,YAAY,EAAAoN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZvN,WAAW,EAAAwN,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1N,mBAAmB,EACnBC,eAAe,EAAA0N,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACf5N,aAAa,EACbC,kBAAkB,EAAA4N,EAAA,CAAAC,YAAA,EAClB5N,aAAa,EAAA6N,EAAA,CAAAC,OAAA,EACb7N,cAAc,EAAA8N,EAAA,CAAAC,QAAA,EACd9N,wBAAwB,EACxBC,gBAAgB,EAAA8N,EAAA,CAAAC,UAAA,EAChB9N,eAAe,EAEfC,YAAY;MAAA8N,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAOHzJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}