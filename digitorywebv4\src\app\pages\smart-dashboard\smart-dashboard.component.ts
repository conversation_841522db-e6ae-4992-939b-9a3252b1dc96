import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';

interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
}

interface ChartDataModel {
  id: string;
  title: string;
  type: string;
  data: ChartData;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];
  selectedLocation: string | null = null;
  
  // Form controls
  locationFilterCtrl = new FormControl('');
  startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago
  endDate = new FormControl(new Date());
  searchQuery = new FormControl('');
  
  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartDataModel[] = [];
  isLoading = false;
  
  // Chart configurations
  lineChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ff6b35',
        borderWidth: 1
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0,0,0,0.1)'
        },
        title: {
          display: false
        }
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0,0,0,0.1)'
        },
        title: {
          display: false
        },
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return '₹' + value.toLocaleString();
          }
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };

  barChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ff6b35',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return '₹' + context.parsed.x.toLocaleString();
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(0,0,0,0.1)'
        },
        title: {
          display: false
        },
        ticks: {
          callback: function(value: any) {
            return '₹' + value.toLocaleString();
          }
        }
      },
      y: {
        grid: {
          display: false
        },
        title: {
          display: false
        }
      }
    }
  };

  doughnutChartOptions: any = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'right',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ff6b35',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${percentage}%`;
          }
        }
      }
    },
    cutout: '60%'
  };

  quantityBarChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Items'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Quantity'
        }
      }
    }
  };

  constructor(
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private shareDataService: ShareDataService,
    private cdr: ChangeDetectorRef
  ) {
    this.user = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.loadBranches();
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });
  }

  private loadBranches(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];
        
        if (this.branches.length === 1) {
          this.selectedLocation = this.branches[0].restaurantIdOld;
        }
      });
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  loadDashboardData(): void {
    if (!this.startDate.value || !this.endDate.value) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: '',
      use_default_charts: true
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            // Load sample data for demonstration
            this.loadSampleData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
          // Load sample data on error for demonstration
          this.loadSampleData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private loadSampleData(): void {
    // Sample data matching the UI shown in the image
    const sampleData = {
      summary_items: [
        { icon: 'attach_money', value: '₹34,766', label: 'Total Purchase Amount', data_type: 'currency' },
        { icon: 'shopping_cart', value: '271', label: 'Total Orders', data_type: 'number' },
        { icon: 'trending_up', value: '₹1,973', label: 'Average Order Value', data_type: 'currency' },
        { icon: 'store', value: 'Fresh Mart', label: 'Top Vendor', data_type: 'vendor' }
      ],
      charts: [
        {
          id: 'purchase-trends',
          title: 'Purchase Trends (Last 30 Days)',
          type: 'line',
          data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
              label: 'Daily Purchase Amount',
              data: [15000, 22000, 18000, 25000],
              backgroundColor: ['#ff6b35'],
              borderColor: ['#ff6b35']
            }]
          }
        },
        {
          id: 'top-vendors',
          title: 'Top Vendors by Purchase Amount',
          type: 'bar',
          data: {
            labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],
            datasets: [{
              label: 'Total Purchase Amount',
              data: [180000, 150000, 140000, 120000, 100000],
              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],
              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']
            }]
          }
        },
        {
          id: 'category-spending',
          title: 'Category wise Spending Distribution',
          type: 'doughnut',
          data: {
            labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],
            datasets: [{
              label: 'Spending Distribution',
              data: [30, 25, 20, 15, 10],
              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],
              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']
            }]
          }
        }
      ]
    };

    this.processDashboardData(sampleData);
  }

  private processDashboardData(data: any): void {
    // Process summary cards
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),
      value: item.value,
      label: item.label,
      color: this.getCardColor(item.data_type)
    })) || [];

    // If no summary cards from API, create default ones
    if (this.summaryCards.length === 0) {
      this.summaryCards = this.createDefaultSummaryCards();
    }

    // Process charts with enhanced data
    this.charts = data.charts?.map((chart: any) => ({
      ...chart,
      data: this.smartDashboardService.processChartData(chart)
    })) || [];

    // If no charts from API, create default message
    if (this.charts.length === 0) {
      console.log('No charts data received from API');
    }
  }

  private createDefaultSummaryCards(): SummaryCard[] {
    return [
      {
        icon: 'attach_money',
        value: '₹34,766',
        label: 'Total Purchase Amount',
        color: '#ff6b35'
      },
      {
        icon: 'shopping_cart',
        value: '271',
        label: 'Total Orders',
        color: '#ffa66f'
      },
      {
        icon: 'trending_up',
        value: '₹1,973',
        label: 'Average Order Value',
        color: '#ff8b4d'
      },
      {
        icon: 'store',
        value: 'Fresh Mart',
        label: 'Top Vendor',
        color: '#ff9966'
      }
    ];
  }

  private getCardColor(dataType: string): string {
    const colorMap: { [key: string]: string } = {
      'currency': '#ff6b35',
      'number': '#ffa66f',
      'percentage': '#ff8b4d',
      'vendor': '#ff9966'
    };
    return colorMap[dataType] || '#ff6b35';
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  onLocationChange(): void {
    this.loadDashboardData();
  }

  onDateChange(): void {
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    if (!this.startDate.value || !this.endDate.value) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: query,
      use_default_charts: false
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success' && response.data.charts.length > 0) {
            this.processDashboardData(response.data);
          } else {
            // Query returned no results, show default data
            console.log('Query returned no data, loading default dashboard');
            this.loadDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.warn('Error with query, falling back to default dashboard:', error);
          this.loadDashboardData();
        }
      });
  }

  getChartData(chart: ChartDataModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartDataModel): ChartType {
    return chart.type as ChartType;
  }
}
