{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51)(1, \"div\", 52)(2, \"mat-icon\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 54)(5, \"span\", 55);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getReportIcon(i_r8));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tab_r7.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r8));\n  }\n}\nfunction SmartDashboardComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r9.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r10.displayName, \" \");\n  }\n}\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.selectedTab = 0;\n    // GRN Filter options\n    this.locations = [];\n    this.baseDates = [{\n      displayName: \"GRN Date(System Entry Date)\",\n      value: \"deliveryDate\"\n    }, {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    }, {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'Purchase Dashboard',\n      active: true\n    }, {\n      label: 'Sales Dashboard',\n      active: false\n    }];\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: false\n            }));\n          } else {\n            console.warn('No locations found for user');\n            this.locations = [];\n          }\n        },\n        error: err => {\n          console.error('Error loading locations:', err);\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  getReportIcon(index) {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 98,\n      vars: 20,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"dashboard-hint\"], [1, \"hint-icon\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-input-row\"], [1, \"ai-title-compact\"], [1, \"ai-icon\"], [1, \"ai-title\"], [1, \"ai-status\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask questions about your data in natural language...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"charts-container\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-icon\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"filter-badge\"], [1, \"selection-count\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(5, SmartDashboardComponent_mat_option_5_Template, 9, 4, \"mat-option\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-hint\", 6)(7, \"mat-icon\", 7);\n          i0.ɵɵtext(8, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \" This selection changes the entire dashboard view \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"mat-icon\", 11);\n          i0.ɵɵtext(14, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h3\", 12);\n          i0.ɵɵtext(16, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, SmartDashboardComponent_span_17_Template, 2, 1, \"span\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(28, SmartDashboardComponent_mat_option_28_Template, 2, 2, \"mat-option\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 15)(30, \"label\", 16)(31, \"mat-icon\", 17);\n          i0.ɵɵtext(32, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\", 18);\n          i0.ɵɵtext(34, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"mat-form-field\", 20)(36, \"mat-select\", 22);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_36_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(37, SmartDashboardComponent_mat_option_37_Template, 2, 2, \"mat-option\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 15)(39, \"label\", 16)(40, \"mat-icon\", 17);\n          i0.ɵɵtext(41, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 18);\n          i0.ɵɵtext(43, \"Start Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"mat-form-field\", 20)(45, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_45_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"mat-datepicker-toggle\", 24)(47, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"label\", 16)(51, \"mat-icon\", 17);\n          i0.ɵɵtext(52, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 18);\n          i0.ɵɵtext(54, \"End Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"mat-form-field\", 20)(56, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"mat-datepicker-toggle\", 24)(58, \"mat-datepicker\", null, 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 28)(61, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_61_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_65_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(66, \"mat-icon\");\n          i0.ɵɵtext(67, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(68, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"div\", 31)(70, \"div\", 32)(71, \"div\", 33)(72, \"div\", 34)(73, \"div\", 35)(74, \"mat-icon\", 36);\n          i0.ɵɵtext(75, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\", 37);\n          i0.ɵɵtext(77, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 38);\n          i0.ɵɵtext(79);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 39)(81, \"mat-form-field\", 40)(82, \"input\", 41);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_82_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_82_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_83_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(84, \"mat-icon\");\n          i0.ɵɵtext(85, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(86, \"div\", 43)(87, \"div\", 44)(88, \"div\", 45)(89, \"div\", 46)(90, \"div\", 47)(91, \"mat-icon\");\n          i0.ɵɵtext(92, \"insights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"h4\", 48);\n          i0.ɵɵtext(94, \"Your Dashboard Awaits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"p\", 49);\n          i0.ɵɵtext(96, \" Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(97, \"div\", 50);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(48);\n          const _r6 = i0.ɵɵreference(59);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(21);\n          i0.ɵɵclassProp(\"ready\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, MatCardModule, MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, MatFormFieldModule, i6.MatFormField, i6.MatHint, i6.MatSuffix, MatSelectModule, i7.MatSelect, i8.MatOption, MatInputModule, i9.MatInput, MatDatepickerModule, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, MatNativeDateModule, FormsModule, i11.DefaultValueAccessor, i11.NgControlStatus, i11.NgModel],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.75rem 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:first-child {\\n  border-bottom: 1px solid #f3f4f6;\\n  padding-bottom: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reports-section[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.25rem;\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.75rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  padding-top: 0.75rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #4b5563;\\n  border-color: #d1d5db;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n  transform: translateY(-1px);\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e55a2b, #ff6b35);\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0.5rem;\\n  gap: 0.5rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  padding: 0.5rem 0.75rem;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #ff6b35, #ff8c5a);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6b7280;\\n  padding: 1px 6px;\\n  border-radius: 0.375rem;\\n  background: #f3f4f6;\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 86px);\\n    min-height: calc(100vh - 86px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 50vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n    margin-left: auto;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.5rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.8rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.8rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 36px !important;\\n  line-height: 36px !important;\\n  padding: 0 12px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.5rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.875rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "first", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r8", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getReportIcon", "tab_r7", "label", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r9", "value", "baseDate_r10", "displayName", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "user", "getCurrentUser", "loadLocations", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "tenantId", "getLocations", "pipe", "subscribe", "next", "res", "result", "branches", "map", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "console", "warn", "error", "err", "resetFilters", "location", "count", "icons", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "ɵɵtemplate", "SmartDashboardComponent_mat_option_5_Template", "SmartDashboardComponent_span_17_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_valueChange_27_listener", "SmartDashboardComponent_mat_option_28_Template", "SmartDashboardComponent_Template_mat_select_valueChange_36_listener", "SmartDashboardComponent_mat_option_37_Template", "SmartDashboardComponent_Template_input_ngModelChange_45_listener", "ɵɵelement", "SmartDashboardComponent_Template_input_ngModelChange_56_listener", "SmartDashboardComponent_Template_button_click_61_listener", "SmartDashboardComponent_Template_button_click_65_listener", "SmartDashboardComponent_Template_input_ngModelChange_82_listener", "SmartDashboardComponent_Template_input_keydown_82_listener", "SmartDashboardComponent_Template_button_click_83_listener", "_r5", "_r6", "ɵɵclassProp", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "i6", "MatFormField", "MatHint", "MatSuffix", "i7", "MatSelect", "i8", "MatOption", "i9", "MatInput", "i10", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i11", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { first } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  selectedTab = 0;\n  user: any;\n\n  // GRN Filter options\n  locations: any[] = [];\n\n  baseDates = [\n    {\n      displayName: \"GRN Date(System Entry Date)\",\n      value: \"deliveryDate\"\n    },\n    {\n      displayName: \"Vendor Invoice Date\",\n      value: \"invoiceDate\"\n    },\n    {\n      displayName: \"Goods Received Date\",\n      value: \"grnDate\"\n    }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'deliveryDate';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'Purchase Dashboard', active: true },\n    { label: 'Sales Dashboard', active: false },\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.loadLocations();\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n  }\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: false\n              }));\n            } else {\n              console.warn('No locations found for user');\n              this.locations = [];\n            }\n          },\n          error: (err) => {\n            console.error('Error loading locations:', err);\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'deliveryDate';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'deliveryDate') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n\n  getReportIcon(index: number): string {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <mat-icon class=\"dashboard-icon\">{{ getReportIcon(i) }}</mat-icon>\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n        <mat-hint class=\"dashboard-hint\">\n          <mat-icon class=\"hint-icon\">info</mat-icon>\n          This selection changes the entire dashboard view\n        </mat-hint>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Assistant Section - Compact -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-input-row\">\n          <div class=\"ai-title-compact\">\n            <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n            <span class=\"ai-title\">Smart Dashboard Assistant</span>\n            <span class=\"ai-status\" [class.ready]=\"getActiveFiltersCount() > 0\">\n              {{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}\n            </span>\n          </div>\n\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask questions about your data in natural language...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Your Dashboard Awaits</h4>\n            <p class=\"empty-state-description\">\n              Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data.\n            </p>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICN5BC,EAAA,CAAAC,cAAA,qBAAgE;IAE3BD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClEH,EAAA,CAAAC,cAAA,cAA4B;IACGD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALtBH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAW;IAE1BL,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,IAAA,EAAsB;IAExBL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,KAAA,CAAe;IACfX,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAI,uBAAA,CAAAP,IAAA,EAAgC;;;;;IAkBnEL,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAC,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEf,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,OAAAG,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIElB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAe,WAAA,CAAAC,KAAA,CAAwB;IACrEpB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAM,WAAA,CAAAR,KAAA,MACF;;;;;IAaAX,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAiB,YAAA,CAAAD,KAAA,CAAwB;IACrEpB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAQ,YAAA,CAAAC,WAAA,MACF;;;ADlDd,MAkBaC,uBAAuB;EAmClCC,YACUC,WAAwB,EACxBC,gBAAkC;IADlC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IApC1B,KAAAC,WAAW,GAAG,CAAC;IAGf;IACA,KAAAC,SAAS,GAAU,EAAE;IAErB,KAAAC,SAAS,GAAG,CACV;MACEP,WAAW,EAAE,6BAA6B;MAC1CF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,EACD;MACEE,WAAW,EAAE,qBAAqB;MAClCF,KAAK,EAAE;KACR,CACF;IAED;IACA,KAAAH,iBAAiB,GAAa,EAAE;IAChC,KAAAa,gBAAgB,GAAG,cAAc;IACjC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAEvB,KAAK,EAAE,oBAAoB;MAAEwB,MAAM,EAAE;IAAI,CAAE,EAC7C;MAAExB,KAAK,EAAE,iBAAiB;MAAEwB,MAAM,EAAE;IAAK,CAAE,CAC5C;EAKG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,WAAW,CAACa,cAAc,EAAE;IAC7C,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACd,WAAW,GAAGc,KAAK;IACxB,IAAI,CAACP,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACR,MAAM,GAAGS,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;MAC3B;MACA,IAAI,CAACb,WAAW,GAAG,EAAE;;EAEzB;EAEAc,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;EAAA;EAGFb,aAAaA,CAAA;IACX,IAAI,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACgB,QAAQ,EAAE;MACnC,IAAI,CAAC3B,gBAAgB,CAAC4B,YAAY,CAAC,IAAI,CAACjB,IAAI,CAACgB,QAAQ,CAAC,CACnDE,IAAI,CAACxD,KAAK,EAAE,CAAC,CACbyD,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAChC,SAAS,GAAG8B,GAAG,CAACE,QAAQ,CAACC,GAAG,CAAEC,MAAW,KAAM;cAClD1C,KAAK,EAAE0C,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEtD,KAAK,EAAEmD,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;WACJ,MAAM;YACLC,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;YAC3C,IAAI,CAAC1C,SAAS,GAAG,EAAE;;QAEvB,CAAC;QACD2C,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;UAC9C,IAAI,CAAC5C,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEA6C,YAAYA,CAAA;IACV;IACA,IAAI,CAACxD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACa,gBAAgB,GAAG,cAAc;IACtC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACJ,SAAS,CAACc,OAAO,CAACgC,QAAQ,IAAIA,QAAQ,CAACN,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEArD,qBAAqBA,CAAA;IACnB,IAAI4D,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC1D,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAEyD,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC7C,gBAAgB,KAAK,cAAc,EAAE6C,KAAK,EAAE;IACrD,IAAI,IAAI,CAAC5C,SAAS,EAAE4C,KAAK,EAAE;IAC3B,IAAI,IAAI,CAAC3C,OAAO,EAAE2C,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;EAEAlE,aAAaA,CAACgC,KAAa;IACzB,MAAMmC,KAAK,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;IAC7E,OAAOA,KAAK,CAACnC,KAAK,CAAC,IAAI,YAAY;EACrC;;;uBApHWlB,uBAAuB,EAAAvB,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvB1D,uBAAuB;MAAA2D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApF,EAAA,CAAAqF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCpC3F,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAA6F,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAjE,WAAA,GAAAoE,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBH,GAAA,CAAApD,WAAA,CAAAuD,MAAA,CAAA3E,KAAA,CAAyB;UAAA,EAA7C;UACjCpB,EAAA,CAAAiG,UAAA,IAAAC,6CAAA,wBAQa;UACflG,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,kBAAiC;UACHD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3CH,EAAA,CAAAE,MAAA,yDACF;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKfH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAiG,UAAA,KAAAE,wCAAA,mBAEO;UACTnG,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAiG,UAAA,KAAAG,wCAAA,mBAEO;UACTpG,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA6F,UAAA,yBAAAQ,oEAAAN,MAAA;YAAA,OAAAH,GAAA,CAAA3E,iBAAA,GAAA8E,MAAA;UAAA,EAA6B;UACvC/F,EAAA,CAAAiG,UAAA,KAAAK,8CAAA,wBAEa;UACftG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA6F,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAH,GAAA,CAAA9D,gBAAA,GAAAiE,MAAA;UAAA,EAA4B;UACtC/F,EAAA,CAAAiG,UAAA,KAAAO,8CAAA,wBAEa;UACfxG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA6F,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAH,GAAA,CAAA7D,SAAA,GAAAgE,MAAA;UAAA,EAAuB;UAHzB/F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAA0G,SAAA,iCAA6E;UAE/E1G,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA6F,UAAA,2BAAAc,iEAAAZ,MAAA;YAAA,OAAAH,GAAA,CAAA5D,OAAA,GAAA+D,MAAA;UAAA,EAAqB;UAHvB/F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAA0G,SAAA,iCAA2E;UAE7E1G,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA6F,UAAA,mBAAAe,0DAAA;YAAA,OAAShB,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UACnEzE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAA6F,UAAA,mBAAAgB,0DAAA;YAAA,OAASjB,GAAA,CAAAxC,YAAA,EAAc;UAAA,EAAC;UAClFpD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvDH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGTH,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAA6F,UAAA,2BAAAiB,iEAAAf,MAAA;YAAA,OAAAH,GAAA,CAAA3D,WAAA,GAAA8D,MAAA;UAAA,EAAyB,qBAAAgB,2DAAAhB,MAAA;YAAA,OACdH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,CAAkB;UAAA,EADJ;UAJ3B/F,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAMC;UAFCD,EAAA,CAAA6F,UAAA,mBAAAmB,0DAAA;YAAA,OAASpB,GAAA,CAAA/C,WAAA,EAAa;UAAA,EAAC;UAGvB7C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAOXD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/BH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,aAAmC;UACjCD,EAAA,CAAAE,MAAA,sHACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAA0G,SAAA,eAEM;UACR1G,EAAA,CAAAG,YAAA,EAAM;;;;;UAjLQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAwF,GAAA,CAAAjE,WAAA,CAAuB;UACL3B,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAA1D,IAAA,CAAS;UAuBTlC,EAAA,CAAAM,SAAA,IAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAAwF,GAAA,CAAA7E,qBAAA,OAAiC;UAY5Bf,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAwF,GAAA,CAAA3E,iBAAA,CAAAC,MAAA,KAAkC;UAKrDlB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAwF,GAAA,CAAA3E,iBAAA,CAA6B;UACNjB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAAhE,SAAA,CAAY;UAcnC5B,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAwF,GAAA,CAAA9D,gBAAA,CAA4B;UACL9B,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAA/D,SAAA,CAAY;UAgB7C7B,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA6G,GAAA,CAA6B,YAAArB,GAAA,CAAA7D,SAAA;UAKE/B,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA6G,GAAA,CAAmB;UAclDjH,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAA2B,YAAAtB,GAAA,CAAA5D,OAAA;UAKIhC,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA8G,GAAA,CAAiB;UA6B1BlH,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAmH,WAAA,UAAAvB,GAAA,CAAA7E,qBAAA,OAA2C;UACjEf,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAa,kBAAA,MAAA+E,GAAA,CAAA7E,qBAAA,6DACF;UASIf,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAA3D,WAAA,CAAyB,aAAA2D,GAAA,CAAA7E,qBAAA;UAU3Bf,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAAwF,GAAA,CAAA3D,WAAA,CAAAa,IAAA,MAAA8C,GAAA,CAAA7E,qBAAA,SAAiE;;;qBDtI3E1B,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhI,aAAa,EACbC,eAAe,EAAAgI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjI,aAAa,EAAAkI,EAAA,CAAAC,OAAA,EACblI,kBAAkB,EAAAmI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,SAAA,EAClBrI,eAAe,EAAAsI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfxI,cAAc,EAAAyI,EAAA,CAAAC,QAAA,EACdzI,mBAAmB,EAAA0I,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB5I,mBAAmB,EACnBC,WAAW,EAAA4I,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFvH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}