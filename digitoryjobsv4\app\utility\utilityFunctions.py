import pandas
import csv
import os
import math
from datetime import datetime

def init(dbCon):
	global db
	db =dbCon

def errorValuesFilter(value, defaultValue):
	if pandas.isnull(value):
		return defaultValue
	if value in ['NaN',None,'','#DIV/0!','#N/A','NaN','NA','nan'] :
		return defaultValue
	try:
		return float(value)
	except:
		return defaultValue

def datacheck(value, defaultValue):
	return errorValuesFilter(value, defaultValue)

def errorLogging(messageStr, tenantId, restaurantId):
	directory = 'errorlog/'+ tenantId +'/'
	if not os.path.exists(directory):
		os.makedirs(directory)
	filePath=directory+'error.csv'
	if not os.path.exists(filePath):
		os.mknod(filePath)	
	with open(filePath, 'a') as f:
		writer = csv.writer(f)
		writer.writerow([messageStr])

def calculateBatchSizes(tenantId, restaurantId, date):
	metricsEntries = list(db.metricsCol.find({'tenantId' : tenantId, 'restaurantId' : restaurantId, 'date' : date}))
	itemDict = {}
	sessionEntries = {}
	for entry in metricsEntries:
		session = entry['session']
		sessionEntries[session] = entry
		for itemKey in entry['items']:
			if itemKey in itemDict:
				itemDict[itemKey][session] = entry['items'][itemKey]['estimated']
			else:
				itemDict[itemKey] = { session : entry['items'][itemKey]['estimated']}
	for itemKey in itemDict.keys():
		itemCode, servingSize = itemKey.split('|')
		dataaa = {'tenantId' : tenantId, 'menuItemCode' : itemCode, 'servingSize' : servingSize}
		batchSize = db.servingsizerecipesCol.find_one({'tenantId' : tenantId, 'menuItemCode' : itemCode, 'servingSize' : servingSize})
		if batchSize is None :
			continue
		batchSize = batchSize['batchSize']
		sessionsForItemKey = list(itemDict[itemKey].keys())
		total = 0
		for session in sessionsForItemKey:
			total += itemDict[itemKey][session]
		batchWiseTotal = math.ceil((total/batchSize) * batchSize)
		# batchWiseTotal = batchSize
		# batchSplitPerSession = batchWiseTotal / len(sessionsForItemKey)
		counter = 1
		for session in sessionsForItemKey:
			if counter!= len(sessionsForItemKey):
				batchWiseTotal = batchWiseTotal - sessionEntries[session]['items'][itemKey]['estimated']
			else:	
				sessionEntries[session]['items'][itemKey]['predicted'] = batchWiseTotal
				sessionEntries[session]['items'][itemKey]['estimated'] = batchWiseTotal
			counter += 1
	for session in sessionEntries.keys():
		entry = sessionEntries[session]
		db.metricsCol.update(
				{'_id':entry['_id']},
				{
					'$set' : {
								'items' : entry['items'],
								'modTs' : datetime.now(),

							}
				},
				upsert = False,
				multi = False
			)

def getWorkAreaStock(tenantId, restaurantId, workArea, itemCode):
	stockValuesEntries = db.stockvaluesCol.find({'tenantId':tenantId,'restaurantId':restaurantId,'itemCode':itemCode})
	totalQty = 0
	for entry in stockValuesEntries:
		if "packageQty" not in entry:
			packageQty =1
		else:
			packageQty = entry['packageQty']
		if workArea not in entry['workArea']:
			messageStr = 'Item with itemcode: '+itemCode+ ' does not contain workArea ' + workArea+'  for restaurantid: '+restaurantId+'\n'
			errorLogging(messageStr, tenantId, restaurantId)
			continue
		totalQty += errorValuesFilter(entry['workArea'][workArea],0) * packageQty
	return totalQty

def calculatePackaging(tenantId, restaurantId, itemCode, value, order):
	packagingEntries = list(db.packagingmastersCol.find({'tenantId' : tenantId, 'itemCode' : itemCode}))
	if len(packagingEntries) == 0:
		messageStr = 'Item with itemcode: '+itemCode+ ' has no package sizes  for tenantId: '+tenantId+'\n'
		errorLogging(messageStr, tenantId, restaurantId)
		return []
	sortedEntries = sortPackaging(packagingEntries, order)
	return assignQuantities(sortedEntries, value, order)


def sortPackaging(packagingEntries, order):
	sortedEntries = []
	if len(packagingEntries)>1:
		if order == 'ascending':
			while(len(packagingEntries)>0):
				minQty = 99999
				for stockValueEntry in packagingEntries:
					packageQty = stockValueEntry['packageQty']
					if packageQty <= minQty:
						optedEntry = stockValueEntry
						minQty = packageQty

				sortedEntries.append(optedEntry)
				packagingEntries.remove(optedEntry)
		else:
			while(len(packagingEntries)>0):
				maxQty = 0
				for stockValueEntry in packagingEntries:
					packageQty = stockValueEntry['packageQty']
					if packageQty >= maxQty:
						optedEntry = stockValueEntry
						maxQty = packageQty

				sortedEntries.append(optedEntry)
				packagingEntries.remove(optedEntry)
	else:
		sortedEntries = packagingEntries

	return sortedEntries

def assignQuantities(sortedEntries, value, order):
	entriesWithAssignments = []
	ptr = 0
	# for ptr in range(len(sortedEntries)):
	while(value!=0):
		entry = sortedEntries[ptr]
		packageQty = entry['packageQty']
		if value > packageQty:
			if ptr == len(sortedEntries) - 1:
				indentQty = math.ceil(value/packageQty)
				value = 0
			else:
				indentQty = math.floor(value/packageQty)
				value = value - indentQty
			ptr += 1
		else:
			if ptr == len(sortedEntries) - 1:
				indentQty = 1
				value = 0
				ptr += 1
			else:
				ptr += 1
				continue
		packageName = entry['packageName']
		entriesWithAssignments.append({
			'itemCode' : entry['itemCode'],
			'packageName' : packageName,
			'packageQty' : packageQty,
			'indentQty' : indentQty
			})
	return entriesWithAssignments

def truncate_and_floor(number, precision=2):
    if isinstance(number, str):
        number = float(number)
    if math.isnan(number):
        number = 0
    factor = 10 ** precision
    truncated_number = math.trunc(float(number) * factor) / factor
    return math.floor(truncated_number * (10 ** precision)) / (10 ** precision)