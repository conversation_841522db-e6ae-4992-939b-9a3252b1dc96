{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/loading.service\";\nclass LoadingInterceptor {\n  constructor(loadingService) {\n    this.loadingService = loadingService;\n    this.totalRequests = 0;\n  }\n  intercept(request, next) {\n    // Increment the request counter\n    this.totalRequests++;\n    // Show the loader\n    this.loadingService.setLoading(true);\n    return next.handle(request).pipe(finalize(() => {\n      // Decrement the request counter\n      this.totalRequests--;\n      // If there are no more pending requests, hide the loader\n      if (this.totalRequests === 0) {\n        this.loadingService.setLoading(false);\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function LoadingInterceptor_Factory(t) {\n      return new (t || LoadingInterceptor)(i0.ɵɵinject(i1.LoadingService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingInterceptor,\n      factory: LoadingInterceptor.ɵfac\n    });\n  }\n}\nexport { LoadingInterceptor };", "map": {"version": 3, "names": ["finalize", "LoadingInterceptor", "constructor", "loadingService", "totalRequests", "intercept", "request", "next", "setLoading", "handle", "pipe", "i0", "ɵɵinject", "i1", "LoadingService", "factory", "ɵfac"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/_interceptors/loading.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { <PERSON>ttpRequest, <PERSON>ttpHand<PERSON>, HttpEvent, HttpInterceptor } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { finalize } from 'rxjs/operators';\nimport { LoadingService } from '../services/loading.service';\n\n@Injectable()\nexport class LoadingInterceptor implements HttpInterceptor {\n  private totalRequests = 0;\n\n  constructor(private loadingService: LoadingService) {}\n\n  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {\n    // Increment the request counter\n    this.totalRequests++;\n    \n    // Show the loader\n    this.loadingService.setLoading(true);\n    \n    return next.handle(request).pipe(\n      finalize(() => {\n        // Decrement the request counter\n        this.totalRequests--;\n        \n        // If there are no more pending requests, hide the loader\n        if (this.totalRequests === 0) {\n          this.loadingService.setLoading(false);\n        }\n      })\n    );\n  }\n}\n"], "mappings": "AAGA,SAASA,QAAQ,QAAQ,gBAAgB;;;AAGzC,MACaC,kBAAkB;EAG7BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAF1B,KAAAC,aAAa,GAAG,CAAC;EAE4B;EAErDC,SAASA,CAACC,OAA6B,EAAEC,IAAiB;IACxD;IACA,IAAI,CAACH,aAAa,EAAE;IAEpB;IACA,IAAI,CAACD,cAAc,CAACK,UAAU,CAAC,IAAI,CAAC;IAEpC,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAC9BV,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACI,aAAa,EAAE;MAEpB;MACA,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACD,cAAc,CAACK,UAAU,CAAC,KAAK,CAAC;;IAEzC,CAAC,CAAC,CACH;EACH;;;uBAvBWP,kBAAkB,EAAAU,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAlBb,kBAAkB;MAAAc,OAAA,EAAlBd,kBAAkB,CAAAe;IAAA;EAAA;;SAAlBf,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}