from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from app.database import Stockvalues,dailybackupsCol,weightedaveragesCol,tenantsCol,purchaseordersCol,purchaserequestsCol,intrabranchtransfersCol,deletedgrnsCol,purchaseinvoicesCol,grnsCol,ibtsCol,indentlistsCol,roloposconfigsCol,salesdetailsCol,metricsCol,tmpclosingsCol,adjustinvCol
import os
import numpy as np
from datetime import datetime,timedelta,timezone
from app.utility import report_utility
import pandas as pd
import json
import  math
router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

@router.post('/report')
def report(job: dict, sortby: dict, token: str = Depends(authenticate)):

    if job['details']['type'] == 'ibtReport':
        return ibtReport(job,  sortby)
    elif job['details']['type'] == 'inventoryStatus':
        return inventoryStatus(job, sortby)    
    elif job['details']['type'] == 'indentIssueReport':
        return indentIssueReport(job, sortby)
    elif job['details']['type'] == 'grnStatus':
        return grnStatus(job, sortby)
    elif job['details']['type'] == 'systemClosingReport':
        return systemClosingReport(job,  sortby)
    elif job['details']['type'] == 'closingReport':
        return manualClosingReport(job,  sortby)
    elif job['details']['type'] == 'store_variance':
        return store_variance(job,  sortby)
    elif job['details']['type'] == 'prStatusReport':
        return prStatusReport(job,  sortby)
    elif job['details']['type'] == 'poStatusReport':
        return poStatusReport(job,  sortby)
    elif job['details']['type'] == 'rateVarianceReport':
        return rateVarianceReport(job,  sortby)
    elif job['details']['type'] == 'consolidated_purchase_indent':
        return consolidated_purchase_indent(job,  sortby)
    elif job['details']['type'] == 'profitMarginReport':
        return profitMarginReport(job,  sortby)
    elif job['details']['type'] == 'inventoryConsumptionNew':
        return inventoryConsumptionNew(job,  sortby)
    elif job['details']['type'] == 'intraBranchTransferReport':
        return intraBranchTransferReport(job,  sortby)
    elif job['details']['type'] == 'barVarianceReport':
        return barVarianceReport(job,  sortby)
    elif job['details']['type'] == 'flrReport':
        return flrReport(job,  sortby)
    elif job['details']['type'] == 'billWiseReport':
        return billWiseReport(job,  sortby)
    elif job['details']['type'] == 'accuracyReport':
        return accuracyReport(job,  sortby)
    elif job['details']['type'] == 'adjustInventoryReport':
        return adjustInventoryReport(job,  sortby)
    
    else:
        return {"status": "error", "message": "Unsupported report type"}


def inventoryStatus(job:dict,sortby:dict):
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','INVENTORY','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas
    location1 = report_utility.getLocation(job['tenantId'])
    stockvaluesitems = list(Stockvalues.find(invSearchFilter))
    invList = []
    workAreaDict = {}
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subcategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if isinstance(subCategory, str)]

    for item in stockvaluesitems:
        if str(item['category']).lower() not in _category:
            continue
        if item['subCategory'].lower() not in _subcategory:
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        if location not in workAreaDict.keys():
            workAreaDict[location] =[]
        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        packageName = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['taxRate'] = item.get("taxRate", 0)
        item['price'] = float(item.get("price", 0))
        item['inStock'] = item.get("inStock", 0)
        totalStockInHand = float(item['inStock'])
        subTotal = item['price'] * totalStockInHand
        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item.get("entryType","N/A"),
            'Package Name': packageName,
            'UOM': item['uom'],
            'Store Stock':  report_utility.truncate_and_floor(totalStockInHand, 2),
            "Tax(%)" : float(item['taxRate']),
            'Unit Price' : report_utility.truncate_and_floor(item['price'], 2),
            'Store Total(excl.tax)' : report_utility.truncate_and_floor(subTotal, 2),
            'Tax Amount' : report_utility.truncate_and_floor((subTotal*(float(item['taxRate'])/100)), 2),
            'Store Total(incl.tax,etc)' : report_utility.truncate_and_floor(subTotal + ((subTotal)*(float(item['taxRate'])/100)), 2)
        }
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if item['workArea'][workArea] is None or math.isnan(item['workArea'][workArea]):
                item['workArea'][workArea] = 0
            sub = item['price'] * item['workArea'][workArea]
            entry[workArea] =report_utility.truncate_and_floor(item['workArea'][workArea], 2)
            entry['{} Total(incl.tax)'.format(workArea)] = report_utility.truncate_and_floor(sub + ((sub)*(float(item['taxRate'])/100)), 2)
            totalStockInHand += item['workArea'][workArea]
            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax)'.format(workArea))
        entry['Grand Total(excl.tax)'] = report_utility.truncate_and_floor(item['price'] * totalStockInHand, 2)
        entry['Grand Tax Amount'] = report_utility.truncate_and_floor((item['price'] * totalStockInHand)*(float(item['taxRate'])/100), 2)
        entry['Grand Total(incl.tax,etc)'] = report_utility.truncate_and_floor((item['price'] * totalStockInHand) +((item['price'] * totalStockInHand)*(float(item['taxRate'])/100)), 2)
        entry['Total Stock(Store + Workarea)'] = report_utility.truncate_and_floor(totalStockInHand, 2)
        invList.append(entry)

    df = pd.DataFrame(invList)
    
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}



def prStatusReport(job:dict,sortby:dict):
    startDate = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    prSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'createTs' :{ '$gte': startDate, '$lte': endDate},
        'subPr' : False
    }
    prList = list(purchaserequestsCol.find(prSearchFilter))
    location1 = getLocation(job['tenantId'])

    finalList = []
    for pr in prList:
        temp ={}
        app = report_utility.get_pr_approval_status(pr)
        prStatus = ""
        wa = ""
        if pr['isPoCreated']:
            prStatus = "COMPLETED"
        else:
            prStatus = "PENDING" 

        if (('selectedWorkArea' in pr.keys()) and (pr['selectedWorkArea']) and (len(pr['selectedWorkArea'])>0)) :
            wa = pr['selectedWorkArea'][0]
        else:
            wa = "STORE"
        temp={
            'Location' : location1[pr['restaurantId']],
            'Created By': pr['creator'],
            'Workarea' : wa,
            'PR ID' : pr['prId'],
            'Created Date' : pr['createTs'].strftime("%d-%m-%Y"),
            'Created Time': report_utility.format_time_to_ampm(pr['createTs'].strftime("%H:%M:%S")), 
            'Delivery Date': (pr['deliveryDate'].strftime("%d-%m-%Y") if (pr.get('deliveryDate') is not None) else pr['eta'].strftime("%d-%m-%Y")) if ('deliveryDate' in pr) else pr['eta'].strftime("%d-%m-%Y"),
            'Approver': app[1],
            'Approval Status': app[0],
            'Req. Status': prStatus,
            'Amount' : pr.get('totalAmount', 'N/A')
        }
        finalList.append(temp)

    df = pd.DataFrame(finalList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records') )}    



def poStatusReport(job:dict,sortby:dict):
    tenantEntry = tenantsCol.find_one({'tenantId': job['tenantId']})
    startDate = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    vendorDict = {}
    for vendor in tenantEntry['vendors']:
        vendorDict[vendor['vendorId']] = vendor['vendorName']
    location1 = report_utility.getLocation(job['tenantId'])	
    poSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'vendorId' :  { '$in': job['details']['selectedVendors']},
        'createTs' :{ '$gte': startDate, '$lte': endDate}
    }
    poList = list(purchaseordersCol.find(poSearchFilter))
    current_date = datetime.now().date() + timedelta(days=3)
    finalList = []
    for po in poList:
        if len(po['grns']) > 0:
            for index, grn in enumerate(po['grns']):
                temp ={}
                temp={
                    'Location' : location1[po['restaurantId']],
                    'Vendor ID': po['vendorId'],
                    'Vendor Name': vendorDict[po['vendorId']] if po['vendorId'] in vendorDict else 'Vendor Not Found',
                    'PO ID' : po['poId'],
                    'PO Created By' : po['creator'] if 'creator' in po else '-',
                    'PO Created Date' : po['createTs'].strftime("%d-%m-%Y"),
                    'PO Created Time' : report_utility.format_time_to_ampm(po['createTs'].strftime("%H:%M:%S")),
                    'Delivery Date': po['validityDate'].strftime("%d-%m-%Y"),
                    'GRN ID': grn['grnId'],
                    'GRN Date': grn['createTs'].strftime("%d-%m-%Y"),
                    'PO Status': po['statusHistory'][index+1]['orderStatus'].upper()
                }
                finalList.append(temp)
        else:
            temp = {
                'Location': location1[po['restaurantId']],
                'Vendor ID': po['vendorId'],
                'Vendor Name': vendorDict.get(po['vendorId'], 'Vendor Not Found'),
                'PO ID': po['poId'],
                'PO Created By' : po['poId'],
                'PO Created Date': po['createTs'].strftime("%d-%m-%Y"),
                'PO Created Time' : report_utility.format_time_to_ampm(po['createTs'].strftime("%H:%M:%S")),
                'Delivery Date': po['validityDate'].strftime("%d-%m-%Y"),
                'GRN ID': 'NOT YET RECEIVED',
                'GRN Date': 'NOT YET RECEIVED',
                'PO Status': 'PENDING'
            }
            finalList.append(temp)

    df = pd.DataFrame(finalList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records') )}    

 

def rateVarianceReport(job:dict,sortby:dict):
    stockDict ={}
    selectedRids = job['details']['selectedRestaurants']
    base_year = job['details']['startYear']
    base_month = job['details']['startMonth']
    date_object = datetime(base_year, base_month, 1)
    location1 = report_utility.getLocation(job['tenantId'])	
    stockDict ={}
    for rId in selectedRids:
        report_utility.getStockDict(rId, stockDict)  
    Wac = list(weightedaveragesCol.find(
        {'restaurantId' :{"$in" : selectedRids},"date":{"$gte": date_object}},
        {"_id": 0, "items" : 0, "modAt" : 0, "createdAt": 0, "date" :0, "tenantId" : 0,
         "itemName": 0,"weightedAverageWithTax":0},sort=[('year', -1),('month', -1)])
    )
    df = pd.DataFrame(Wac)
    if df.empty:
      print("DataFrame is empty")
    else:
      print("DataFrame is not empty")
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])[selected_columns]

    return {"status": "success",job["details"]['type'] :  json.loads(df.to_json(orient='records') )}    


def consolidated_purchase_indent(job:dict,sortby:dict):
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'ItemType' : {'$in': ['Inventory', 'INVENTORY']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    stockvaluesitems = list(Stockvalues.find(invSearchFilter))
    purchaseList = []
    indentList = []
    location1 = getLocation(job['tenantId'])
    
    ########################## prerequists #############################
    startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)

    grnDict = report_utility.getGrnDict(job, startTime, endTime, qtyWise=True)
    indentDict, _sr = report_utility.getIndentDict(job, startTime, endTime, qtyWise= True, waWise= True)
    for item in stockvaluesitems:
        if item['category'].lower() not in _category:
            continue
        if item['subCategory'].lower() not in _subCategory:
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]	
        restaurantId = item['restaurantId']
        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A') 
        taxRate = item.get('taxRate', 0)
        packageQty = item.get('packageQty', 1)
        searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
        ######################### GRN ########################
        if restaurantId in grnDict:
            if searchKey in grnDict[restaurantId]:
                grnReceived = {
                    "order" : grnDict[restaurantId][searchKey]['ordered'],
                    "qty" : grnDict[restaurantId][searchKey]['received'],
                    "taxAmount" : grnDict[restaurantId][searchKey]['taxAmount'],
                    'total(excl.tax)' : grnDict[restaurantId][searchKey]['total(excl.tax)'],
                    'total(incl.tax)' : grnDict[restaurantId][searchKey]['total(incl.tax)']
                }
                purchaseList.append({
                    'Location' : location,
                    'Category': item['category'],
                    'Sub Category': item['subCategory'],
                    'Item Code': itemCode,
                    'Item Name': item['itemName'],
                    'Package Name': item['packageName'],
                    'WAC (Unit Cost)' : report_utility.truncate_and_floor(item.get('price', 0), 2),
                    'Ordered Qty':  grnReceived["order"],
                    'Received Qty': grnReceived["qty"],
                    'Total(excl.tax)' : grnReceived["total(excl.tax)"],
                    'Tax Amount' : grnReceived["taxAmount"],
                    'Total(incl.tax,etc)' : grnReceived["total(incl.tax)"]
                })


        ########################## indent ##########################
        if restaurantId in indentDict:
            if searchKey in indentDict[restaurantId]:
                for workArea in indentDict[restaurantId][searchKey].keys():
                    indentDict[restaurantId][searchKey][workArea]['completed'] = True
                    indentReqQty = indentDict[restaurantId][searchKey][workArea]['requested']
                    indentIssuedQty = indentDict[restaurantId][searchKey][workArea]['issued']
                    if indentIssuedQty <=0:
                        continue
                    indentPrice = indentDict[restaurantId][searchKey][workArea]['unitPrice'] / indentIssuedQty
                    subTotalI = indentIssuedQty * indentPrice
                    indentList.append({
                        'Location': location,
                        'WorkArea': workArea,
                        'Category': item['category'],
                        'Sub Category': item['subCategory'],
                        'Item Code': itemCode,
                        'Item Name': item['itemName'],
                        'Package Name': item['packageName'],
                        'WAC (Unit Cost)': report_utility.truncate_and_floor(indentPrice, 2),
                        'Requested Qty': report_utility.truncate_and_floor(indentReqQty, 2),
                        'Issued Qty': report_utility.truncate_and_floor(indentIssuedQty, 2),
                        'Total(excl.tax)': report_utility.truncate_and_floor(subTotalI, 2),
                        'Tax Amount': report_utility.truncate_and_floor((subTotalI * (taxRate / 100)), 2),
                        'Total(incl.tax,etc)': report_utility.truncate_and_floor(subTotalI + ((subTotalI) * (taxRate / 100)), 2)
                    })

    df1 = pd.DataFrame(purchaseList)
    if len(df1) >0:
        df1 = df1[df1['Received Qty'] > 0]
        df1 = df1.sort_values(by='Category', ascending=True)

    df2 = pd.DataFrame(indentList)
    if len(df2) > 0:
        df2 = df2[df2['Issued Qty'] > 0]
        df2 = df2.sort_values(by='Category', ascending=True)
    df1 = df1.fillna('')
    df2 = df2.fillna('')
    combined_df = pd.concat([df1, df2], ignore_index=True)
    if df1.empty and df2.empty :
      print("DataFrame is empty")
    else:
      combined_df = combined_df.sort_values(by=sortby["name"], ascending=sortby["type"])
    #   df2 = df2.sort_values(by=sortby["name"], ascending=sortby["type"])[selected_columns]    

    return {
        "status": "success", 
        "response" :  { 
             
            job["details"]['type'] : json.loads(combined_df.to_json(orient='records')) 
        }
    }
 

def ibtReport(job:dict,sortby:dict):
    stockDict ={}
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    selectedRestaurants = job['details']['selectedRestaurants']

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'dispatchedDate':
        baseDate = 'dispatchedDate'
        systemEntryDate = 'Requested Date'
        dispatchedDateHeader = 'Based on Dispatched Date'
        reportDateHeader = ' | Based on Dispatched Date'
        receivedDate = 'Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'receivedDate':
        baseDate = 'receivedDate'
        systemEntryDate = 'Requested Date'
        dispatchedDateHeader = 'Dispatched Date'
        receivedDate = 'Based on Received Date'
        reportDateHeader = ' | Based on Received Date'
    else:
        baseDate = 'createTs'
        systemEntryDate = 'Based on Requested Date'
        reportDateHeader = ' | Based on Requested Date'
        receivedDate = 'Received Date'
        dispatchedDateHeader = 'Dispatched Date'
        
    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        baseDate: {"$gte": startDate, "$lte": endDate},
    }
    if 'isSource' in job['details'] and job['details']['isSource']:
        ibtSearchFilter['fromBranch.restaurantId'] = {'$in': selectedRestaurants}
    else:
        ibtSearchFilter['toBranch.restaurantId'] = {'$in': selectedRestaurants}
    ibtList = list(ibtsCol.find(ibtSearchFilter))
    finalList =[]
    itemPriceDict ={}
    for ibt in ibtList:
        dispatchStatus = ibt['status']['dispatched'].capitalize()
        deliveredStatus = ibt['status']['delivered'].capitalize()
        dispatchedDate = ibt.get('dispatchedDate', 'N/A')
        if dispatchedDate == 'N/A':
            dispatchedTime = 'N/A'
        else :
            dispatchedTime = report_utility.format_time_to_ampm(dispatchedDate.strftime("%H:%M:%S"))
            dispatchedDate = dispatchedDate.date()
        if deliveredStatus == "Pending":
            deliveredDate = 'N/A'
        else :
            deliveredDate = ibt['receivedDate']

        if deliveredDate == 'N/A':
            deliveredTime = 'N/A'
        else :
            deliveredTime = report_utility.format_time_to_ampm(deliveredDate.strftime("%H:%M:%S"))
            deliveredDate = deliveredDate.date()
            
        restaurantId = ibt['fromBranch']['restaurantId']
        if restaurantId not in itemPriceDict.keys():
            itemPriceDict[restaurantId] ={}
        report_utility.getStockDict(restaurantId, stockDict)
        for item in ibt['items']:
            itemCode = item['itemCode']
            if item['itemName'] == 'undefined':
                continue
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            item['uom'] = item.get('uom', 'N/A')

            category ='N/A'
            subCategory ='N/A'
            taxRate = 0
            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                taxRate = sv['taxRate']
                
            caseType = 0
            if item['packageName'] != "N/A" and item['entryType'] == "package":
                caseType = 1
                searchKey = itemCode + "|" + item['packageName'].upper()
            elif item['entryType'] == "open":
                caseType = 3
                searchKey = itemCode + "|" + item['entryType'].upper()
            else:
                caseType = 3
                searchKey = itemCode

            price = float(item.get('unitPrice', 0) if item.get('unitPrice') is not None else 0)
            if price:
                price = price
            elif searchKey in itemPriceDict[restaurantId]:
                price = itemPriceDict[restaurantId][searchKey]
            else:
                svEntry = None
                if caseType == 1:
                    svEntry = db.stockvaluesCol.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': item['packageName']})
                elif caseType ==2:
                    svEntry = db.stockvaluesCol.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'})
                else:
                    svEntry = db.stockvaluesCol.find_one({'restaurantId': restaurantId, 'itemCode': itemCode})
                if svEntry:
                    price = svEntry.get('price', 0)
                    packageQty = svEntry.get('packageQty', 1)
                    itemPriceDict[restaurantId][searchKey] = price

            rececive = (item['quantity'] - item['recPendingQty']) - sum(item['shortageHistory'])
            rRubTotal = rececive * price
            dRubTotal = (item['quantity'] - item['pendingQty']) * price
            temp = {
                'Category': category,
                'Sub Category': subCategory,
                systemEntryDate: ibt['createTs'].date(),
                dispatchedDateHeader: dispatchedDate,
                receivedDate : deliveredDate,
                'ID': ibt['ibtId'],
                'Source': ibt['fromBranch']['location'],
                'Destination': ibt['toBranch']['location'],
                'Workarea' : ibt['workArea'],
                'Disptach Status': dispatchStatus,
                'Receive Status':  deliveredStatus,
                'Item Name': item['itemName'].split("|")[-1],
                'Item Code': item['itemCode'],
                'Package Name': item['packageName'],
                'Requested Qty': item['quantity'],
                'Dispatched Qty': item['quantity'] - item['pendingQty'],
                'Received Qty': rececive,
                'Pending Qty': item['pendingQty'],
                "Unit Price" : report_utility.truncate_and_floor(price, 2),
                'Tax%' : taxRate,
                'UOM':item['uom'],
                'Dispatched Total(excl.tax)' : report_utility.truncate_and_floor(dRubTotal, 2),
                'Dispatched Tax Amount' : report_utility.truncate_and_floor((dRubTotal*(taxRate/100)), 2),
                'Dispatched Total(incl.tax,etc)' :report_utility.truncate_and_floor(dRubTotal + ((dRubTotal)*(taxRate/100)), 2),
                'Received Total(excl.tax)' : report_utility.truncate_and_floor(rRubTotal, 2),
                'Received Tax Amount' : report_utility.truncate_and_floor((rRubTotal*(taxRate/100)), 2),
                'Received Total(incl.tax,etc)' :report_utility.truncate_and_floor(rRubTotal + ((rRubTotal)*(taxRate/100)), 2)
            }
            temp['Created By'] = '-' if 'creator' in ibt and ibt['creator'] == 'undefined' else ibt.get('creator', '-')
            temp['Requested Time'] = report_utility.format_time_to_ampm(ibt['createTs'].strftime("%H:%M:%S"))
            temp['Dispatched Time'] = dispatchedTime
            temp['Received Time'] = deliveredTime
            

            finalList.append(temp)
    df = pd.DataFrame(finalList)

    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}
 
  
def profitMarginReport(job:dict,sortby:dict):
    stockDict ={}
    startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    month = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').month
    year = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').year
    month_str = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').strftime("%m")
    production_cost =report_utility.getProductionCost(job, month, year)
    selling_price = report_utility.getSellingPrice(job, month_str, str(year))
    df = report_utility.getDailySales(job, startTime , endTime)
    finalList =[]
    for index, row in df.iterrows():
        searchKey = row['Menu Item']
        menu_item_code, serving_size = [item.strip() for item in row['Menu Item'].split("|", 1)]
        report_utility.getStockDict(row['Restaurant ID'], stockDict)
        sv = next((x for x in stockDict[row['Restaurant ID']] if x['itemCode'] == menu_item_code ), None)
        if sv:
            category = sv['category']
            sub_category = sv['subCategory']
            menu_item_name = sv['itemName'] 
        else:
            category = "N/A"
            sub_category = "N/A"
            menu_item_name = "N/A"
        non_modifier_production_cost, modifier_production_cost =report_utility.get_menu_value(production_cost, row['Restaurant ID'], searchKey, row['Modifier'])
        non_modifier_selling_price, modifier_selling_price = report_utility.get_menu_value(selling_price, row['Restaurant ID'], searchKey, row['Modifier'])
        total_sales_amount = report_utility.truncate_and_floor(((non_modifier_selling_price + modifier_selling_price) * row['Quantity']),2)
        total_production_cost = report_utility.truncate_and_floor(((non_modifier_production_cost + modifier_production_cost) * row['Quantity']),2)
        margin = report_utility.truncate_and_floor(total_sales_amount - total_production_cost,2)
        temp={
            "Location" : location1[row['Restaurant ID']],
            'Work Area' : row['Work Area'], 
            'Category' : category, 
            'Sub Category' : sub_category, 
            'Menu Item Code' : menu_item_code, 
            'Menu Item Name' : menu_item_name, 
            'Modifier Name' : row['Modifier'], 
            'Serving Size' : serving_size, 
            'Unit Cost' : report_utility.truncate_and_floor(non_modifier_production_cost,2), 
            'Modifier Cost' : report_utility.truncate_and_floor(modifier_production_cost,2),  
            'Selling Price' : report_utility.truncate_and_floor(non_modifier_selling_price,2),  
            'Selling Modifier Price' :report_utility.truncate_and_floor( modifier_selling_price,2),  
            'Total Sales Qty' : row['Quantity'], 
            'Total Sales Amount' : total_sales_amount, 
            'Total Production Cost' : total_production_cost, 
            'Margin Amount' : margin, 
            'Margin %' : report_utility.truncate_and_floor(((margin / total_sales_amount) * 100), 2), 
            'Cost %' : report_utility.truncate_and_floor(((total_production_cost/ total_sales_amount) *100), 2 ) 
        }
        finalList.append(temp)
    df = pd.DataFrame(finalList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records') )}
 

def inventoryConsumptionNew(job:dict,sortby:dict):
    startTime =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    spoilageDict = report_utility.getSpoilageDictWorkArea(job, startTime, endTime)
    indent = report_utility.getIndentDict(job, startTime, endTime)
    indentDict = indent[0]
    subrecipeIndentDict = indent[1]
    theoretical = report_utility.getTheoreticalConsumption(job, startTime, endTime)
    theoreticalConsumptionDict = theoretical[0]
    subrecipeConsumptionDict = theoretical[1]

    ibtDict = report_utility.getIntraBranchTransferDict(job, startTime, endTime)
    ibtInDict = ibtDict[0]
    ibtOutDict = ibtDict[1]

    openingUserDict = report_utility.getUserDictWorkArea(job, 'opening', 1)
    closingUserDict = report_utility.getUserDictWorkArea(job, 'closing', 1)
    location1 = report_utility.getLocation(job['tenantId'])	
    invOverallList = []
    subRecipeOverallList = []
    orQueryForWorkAreas=[]
    if "DEFAULT" not in job['details']['selectedWorkAreas']:
        job['details']['selectedWorkAreas'].append("DEFAULT")
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    for restaurantId in job['details']['selectedRestaurants']:
        queries=[
            {'restaurantId': restaurantId, 'entryType': 'open', 'ItemType': {'$in': ['Inventory', 'INVENTORY']},'$or': orQueryForWorkAreas},
            {'restaurantId': restaurantId, 'uom': {'$in':['Nos','nos','NOS']}, 'ItemType': {'$in': ['Inventory', 'INVENTORY']},'$or': orQueryForWorkAreas},
            {'restaurantId': restaurantId, 'ItemType': 'SubRecipe','$or': orQueryForWorkAreas},
        ]
        for query in queries:
            invItems = list(Stockvalues.find(query))
            for entry in invItems:
                if entry['category'].lower() not in _category:
                    continue
                if entry['subCategory'].lower() not in _subCategory:
                    continue    

                itemCode = entry['itemCode'].strip()
                uom = entry['uom'].strip()
                searchKey = itemCode + '|' + uom.upper()
                isSubRecipe = False
                if entry['ItemType'] =='SubRecipe':
                    isSubRecipe = True
                    searchSubRecipeKey = itemCode + '|' + 'N/A'

                ########## workarea transfer in #############
                if restaurantId in ibtInDict:
                    if searchKey in ibtInDict[restaurantId]:
                        ibtInQty = ibtInDict[restaurantId][searchKey]
                    else:
                        ibtInQty = 0
                else:
                    ibtInQty = 0

                ########## workarea transfer out ############
                if restaurantId in ibtOutDict:
                    if searchKey in ibtOutDict[restaurantId]:
                        ibtOutQty = ibtOutDict[restaurantId][searchKey]
                    else:
                        ibtOutQty = 0
                else:
                    ibtOutQty = 0

                ########## indent #############
                if isSubRecipe:
                    if restaurantId in subrecipeIndentDict:
                        if searchKey in subrecipeIndentDict[restaurantId]:
                            indentQty = subrecipeIndentDict[restaurantId][searchKey]
                        else:
                            indentQty = 0
                    else:
                        indentQty = 0
                else:
                    if restaurantId in indentDict:
                        if searchKey in indentDict[restaurantId]:
                            indentQty = indentDict[restaurantId][searchKey]
                        else:
                            indentQty = 0
                    else:
                        indentQty = 0


                ########## spoilage #############
                if restaurantId in spoilageDict:
                    if searchKey in spoilageDict[restaurantId]:
                        spoilageQty = spoilageDict[restaurantId][searchKey]
                    else:
                        spoilageQty = 0
                else:
                    spoilageQty = 0

                ########## Theoretical ConsumptionDict #############
                if isSubRecipe:
                    if restaurantId in subrecipeConsumptionDict:
                        if searchKey in subrecipeConsumptionDict[restaurantId]:
                            theoreticalConsumptionQty = subrecipeConsumptionDict[restaurantId][searchKey]
                        else:
                            theoreticalConsumptionQty = 0
                    else:
                        theoreticalConsumptionQty = 0
                else:
                    if restaurantId in theoreticalConsumptionDict:
                        if searchKey in theoreticalConsumptionDict[restaurantId]:
                            theoreticalConsumptionQty = theoreticalConsumptionDict[restaurantId][searchKey]
                        else:
                            theoreticalConsumptionQty = 0
                    else:
                        theoreticalConsumptionQty = 0


                ########## Opening User workarea #############
                if isSubRecipe:
                    if restaurantId in openingUserDict:
                        if searchSubRecipeKey in openingUserDict[restaurantId]:
                            openingUserQtyWorkArea = openingUserDict[restaurantId][searchSubRecipeKey]
                        else:
                            openingUserQtyWorkArea = None
                    else:
                        openingUserQtyWorkArea = None
                else:
                    if restaurantId in openingUserDict:
                        if searchKey in openingUserDict[restaurantId]:
                            openingUserQtyWorkArea = openingUserDict[restaurantId][searchKey]
                        else:
                            openingUserQtyWorkArea = None
                    else:
                        openingUserQtyWorkArea = None

                ########## Closing User workarea #############
                if isSubRecipe:
                    if restaurantId in closingUserDict:
                        if searchSubRecipeKey in closingUserDict[restaurantId]:
                            closingUserQtyWorkArea = closingUserDict[restaurantId][searchSubRecipeKey]
                        else:
                            closingUserQtyWorkArea = None
                    else:
                        closingUserQtyWorkArea = None
                else:
                    if restaurantId in closingUserDict:
                        if searchKey in closingUserDict[restaurantId]:
                            closingUserQtyWorkArea = closingUserDict[restaurantId][searchKey]
                        else:
                            closingUserQtyWorkArea = None
                    else:
                        closingUserQtyWorkArea = None

                entry['price'] = entry.get('price', 0)
                entry['packageQty'] = entry.get('packageQty', 1)
                if uom == "NOS":
                    entry['price'] = entry['price'] / entry['packageQty']

                totalStock = (( (report_utility.truncate_and_floor(openingUserQtyWorkArea, 3) if (openingUserQtyWorkArea is not None) else 0) +indentQty + ibtInQty + spoilageQty) - (ibtOutQty))
                actualConsumption = (totalStock -closingUserQtyWorkArea) if (closingUserQtyWorkArea is not None) else 0
                price = report_utility.truncate_and_floor(entry['price'], 2)
                tax = float(entry['taxRate'])
                subTotalOpen = report_utility.truncate_and_floor(totalStock * price, 2)
                subTotalClos = report_utility.truncate_and_floor(closingUserQtyWorkArea * price, 2) if closingUserQtyWorkArea is not None else 0
                subTotalActu = report_utility.truncate_and_floor((actualConsumption * price), 2)
                subTotalTheo = report_utility.truncate_and_floor((theoreticalConsumptionQty * price), 2)
                subTotalOver = report_utility.truncate_and_floor(((theoreticalConsumptionQty - actualConsumption)* price), 2)
                
                temp = {
                    'Location': location1[entry['restaurantId']],	
                    'Category': entry['category'],
                    'Sub Category': entry['subCategory'],
                    'Item Name': entry['itemName'],
                    'Item Code': entry['itemCode'],
                    'UOM': uom,
                    'WorkArea' : ','.join(job['details']['selectedWorkAreas']),
                    'WorkArea Opening': report_utility.truncate_and_floor(openingUserQtyWorkArea, 3) if openingUserQtyWorkArea is not None else 'N/A',
                    'WorkArea Indent': report_utility.truncate_and_floor(indentQty, 3),
                    'WorkArea Transfer In': report_utility.truncate_and_floor(ibtInQty, 3),
                    'WorkArea Transfer Out': report_utility.truncate_and_floor(ibtOutQty, 3),
                    'Spoilage/Adjustments': report_utility.truncate_and_floor(spoilageQty, 3),
                    'Total WorkArea Stock': report_utility.truncate_and_floor(totalStock, 3),
                    'WorkArea Closing': report_utility.truncate_and_floor(closingUserQtyWorkArea, 3) if closingUserQtyWorkArea is not None else 'N/A',
                    'Actual': report_utility.truncate_and_floor(actualConsumption, 3),
                    'Theoretical': report_utility.truncate_and_floor(theoreticalConsumptionQty, 3),
                    'Variance Qty': theoreticalConsumptionQty - actualConsumption if None not in (openingUserQtyWorkArea, closingUserQtyWorkArea) else 'N/A',
                    'Unit Price': price,
                    'Tax(%)': tax,
                    'WorkArea Opening(incl.tax,etc)': report_utility.truncate_and_floor(subTotalOpen + subTotalOpen * (tax / 100), 2),
                    'WorkArea Closing(incl.tax,etc)': report_utility.truncate_and_floor(subTotalClos + subTotalClos * (tax / 100), 2),
                    'Actual(incl.tax,etc)': report_utility.truncate_and_floor(subTotalActu + subTotalActu * (tax / 100), 2),
                    'Theoretical(incl.tax,etc)': report_utility.truncate_and_floor(subTotalTheo + subTotalTheo * (tax / 100), 2),
                    'Variance (incl.tax,etc)': report_utility.truncate_and_floor(subTotalOver + subTotalOver * (tax / 100), 2)
                }
                if isSubRecipe:
                    subRecipeOverallList.append(temp)
                else:
                    invOverallList.append(temp)


    df1 = pd.DataFrame(invOverallList)
    df1.insert(loc=16, column=' ', value='') if len(invOverallList) > 0 else None
    df2 = pd.DataFrame(subRecipeOverallList)
    df2.insert(loc=16, column=' ', value='') if len(subRecipeOverallList) > 0 else None
    if df1.empty and df2.empty :
      print("DataFrame is empty")
    else:
      df1 = df1.sort_values(by=sortby["name"], ascending=sortby["type"])  
      df2 = df2.sort_values(by=sortby["name"], ascending=sortby["type"])    
 

    return {"status": "success",job["details"]['type'] :  json.loads(df1.to_json(orient='records')),"res1":json.loads(df2.to_json(orient='records') )}
    
 
def grnStatus(job:dict,sortby:dict,delete=False, PI=False):
    stockDict ={}
    tenantEntry = tenantsCol.find_one({'tenantId': job['tenantId']})
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    vendorDict = {}
    location1 = report_utility.getLocation(job['tenantId'])

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'deliveryDate':
        baseDate = 'createTs'
        headerDateTitle = ' | Based on GRN Date(System Entry Date)'
        systemEntryDate = 'Based on GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Goods Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
        baseDate = 'invoiceDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        headerDateTitle = ' | Based on Vendor Invoice Date'
        vendorInvoiceDate = 'Based on Vendor Invoice Date'
        grnDate = 'Goods Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
        baseDate = 'grnDocumentDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        headerDateTitle = ' | Based on Goods Received Date'
        grnDate = 'Based on Goods Received Date'
    else: 
        baseDate = 'createTs'
        headerDateTitle = ' | Based on GRN Date(System Entry Date)'
        systemEntryDate = 'Based on GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Goods Received Date'

    for vendor in tenantEntry['vendors']:
        vendorDict[vendor['vendorId']] = vendor['vendorName']
    grnSearchFilter = {
        'restaurantId': { '$in': job['details']['selectedRestaurants']},
        'vendorId' :  { '$in': job['details']['selectedVendors']},
        'grnType': 'po',
        baseDate :{'$gte': startDate, '$lte': endDate}
    }
    if delete:
        grns = list(deletedgrnsCol.find(grnSearchFilter))
    elif PI:
        grns = list(purchaseinvoicesCol.find({
            'restaurantId': { '$in': job['details']['selectedRestaurants']},
            'vendorId' :  { '$in': job['details']['selectedVendors']},
            'createTs' :{'$gte': startDate, '$lte': endDate}
        }))
    else:
        grns = list(grnsCol.find(grnSearchFilter))

    grnList = []
    approverColumns =[]
    isApproval = False
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    for grn in grns:
        report_utility.getStockDict(grn['restaurantId'], stockDict)
        approvalStatus = None
        appStatus = None
        
        if PI :
            _entry = grn['items']
            _approval_key = "piApprovalSetting"
        else:
            _entry = grn['grnItems']
            _approval_key = "grnApprovalSetting"

        if _approval_key in grn and grn[_approval_key] is not None:
            approval_status_lines = [] 
            approvals =[]
            for row in grn[_approval_key]:
                cat = row.get('category', 'N/A').upper()
                level = row.get('level', 'N/A').upper()
                role = row.get('role', 'N/A')
                status = row.get('status', 'N/A').upper()
                row_string = f"{status}    : {cat} - {level} - {role}"
                approval_status_lines.append(row_string)
                approvals.append(status)
            if len(approval_status_lines) > 0:
                isApproval = True
                approvalStatus = '\n'.join(approval_status_lines)
                if "REJECTED" in approvals:
                    appStatus = "REJECTED"
                elif "PENDING" in approvals:
                    appStatus = "PENDING"
                elif "APPROVED" in approvals:
                    appStatus = "APPROVED"

        for index, item in enumerate(_entry):
            category = item.get('itemCategory', 'N/A')
            subCategory = 'N/A'
            itemCode = item['itemCode']
            sv = next((x for x in stockDict[grn['restaurantId']] if x['itemCode'] == itemCode ), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
            if category.lower() not in _category and category != 'N/A':
                continue
            if subCategory.lower() not in _subCategory and category != 'N/A':
                continue
            for package in item['packages']:
                packagePrice = float(package['packagePrice'] if 'packagePrice' in package else item['unitPrice'])
                taxRate = float(item['taxRate']) if ('taxRate' in item and item['taxRate']) else ((item['subTotal'] / item['taxAmount']) * 100) if item['taxAmount'] != 0 else 0
                cess = item['cessAmt'] if 'cessAmt' in item else 0
                discount = item['discAmt'] if 'discAmt' in item else 0
                extra = item['extraAmt'] if 'extraAmt' in item else 0
                item['receivedQty'] = item.get('receivedQty',0)
                if item['receivedQty'] is None:
                    item['receivedQty'] = 0
                item['quantity'] = item.get('quantity',item['receivedQty'])
                entry = {
                    'Location': location1[grn['restaurantId']],
                    'Category': category,
                    'Sub Category': subCategory,
                    'Vendor Id': grn['vendorId'],
                    'Vendor Name': vendorDict.get(grn['vendorId'], 'vendor not found'),
                    'Invoice Id': grn['invoiceId'],
                    'PO Id': grn['details']['poId'],
                    'GRN Id': grn['grnId'],
                    'PI Id': grn.get('piId', 'N/A'),
                    systemEntryDate: grn['createTs'].strftime("%Y-%m-%d"),
                    'Item Code': item['itemCode'],
                    'Item Name': item['itemName'],
                    'Package Name': package['packageName'],
                    'Order Qty' : item['quantity'],
                    'Received Qty': item['receivedQty'],
                    'Pending Qty': item['quantity'] - item['receivedQty'],
                    'Unit Cost': packagePrice,
                    'Tax(%)': taxRate,
                    'Total(excl.tax)' : report_utility.truncate_and_floor(item['subTotal'], 2),
                    'Tax Amount': item['taxAmount'],
                    'Cess': cess,
                    'Discount': discount,
                    'Extra Charges': extra,
                    'Total(incl.tax,etc)' : item['totalPrice'],
                    vendorInvoiceDate: report_utility.convert_datetime_to_date(grn.get('invoiceDate')),
                    grnDate: report_utility.convert_datetime_to_date(grn.get('grnDocumentDate')),
                    'Transportation': report_utility.truncate_and_floor(float(grn['otherCharges']) / len(_entry), 2),
                    'Approval Status': appStatus if appStatus is not None else np.nan,
                    'Approval Status Detailed': approvalStatus if approvalStatus is not None else np.nan,
                    'Remarks': grn['remarks'] if "remarks" in grn.keys() else "",
                    'Payment Terms': grn['paymentTerms'] if "paymentTerms" in grn.keys() else "",
                    'PO Terms': grn['poTerms'] if "poTerms" in grn.keys() else "",
                    'Payment Methods': grn['paymentMethod'] if "paymentMethod" in grn.keys() else ""
                }
                entry['Grand Total'] = report_utility.truncate_and_floor(entry['Total(incl.tax,etc)'] + entry['Transportation'],2)
                grnList.append(entry)
    df = pd.DataFrame(grnList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}
 

def indentIssueReport(job:dict,sortby:dict):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    location1 = report_utility.getLocation(job['tenantId'])

    queries = []
    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'modDate':
        systemEntryDate = 'Requested Date'
        indentIssuedDate = 'Based on Issued Date'
        reportDateHeader = ' | Based on Issued Date'
        indentDate = 'Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'modTs' : {'$gte': startDate, '$lte': endDate}
        })
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'documentDate' :
        systemEntryDate = 'Requested Date'
        indentIssuedDate = 'Issued Date'
        indentDate = 'Based on Indent Date'
        reportDateHeader = ' | Based on Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'indentDocumentDate' : {'$gte': startDate, '$lte': endDate}
        })
    else:
        systemEntryDate = 'Based on Requested Date'
        reportDateHeader = ' | Based on Requested Date'
        indentIssuedDate = 'Issued Date'
        indentDate = 'Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'createTs' : {'$gte': startDate, '$lte': endDate}
        })

    issueList = []
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    for query in queries :
        if len(job['details']['selectedWorkAreas']) > 0 :
            query['workArea'] = {'$in' : job['details']['selectedWorkAreas']}
        indents = list(indentlistsCol.find(query))
        for indent in indents:
            restaurantId = indent['restaurantId']
            report_utility.getStockDict(restaurantId, stockDict)
            for item in indent['indentItems']:
                itemCode = item['itemCode']
                packageName = item.get("packageName", item['packages'][0]["packageName"] if "packages" in item else "N/A")
                category ='N/A'
                subCategory ='N/A'
                taxRate = 0

                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == packageName), None)
                if sv:
                    category = sv['category']
                    subCategory = sv['subCategory']  
                    taxRate = sv['taxRate']   
                if category.lower() not in _category and category != 'N/A':
                    continue
                if subCategory.lower() not in _subCategory and subCategory != 'N/A':
                    continue
                
                item['entryType'] = item.get('entryType', 'N/A')
                item['issueQty'] = item.get('issueQty', 0) if item.get('issueQty') is not None else 0
                item['pendingQty'] = item.get('pendingQty', 0) if item.get('pendingQty') is not None else 0
                item['dispatchedQty'] = item['issueQty'] - item['pendingQty']
                if 'modTs' not in indent.keys(): indent['modTs'] = indent['createTs']
                
                entry ={}
                
                if 'type' in indent and indent['type'] == 'directIndent':
                    entry['Indent Type'] = "Direct issue"
                elif 'type' in indent and indent['type'] == 'centralStoreIndent':
                    entry['Indent Type'] = "Store issue(CSI)"
                else:
                    entry['Indent Type'] = "Store issue"

                entry['Location'] = location1[restaurantId]
                entry['WorkArea'] = indent['workArea']
                entry['Created By'] = '-' if 'creator' in indent and indent['creator'] == 'undefined' else indent.get('creator', '-')
                entry['Indent ID'] = indent['indentId']
                entry['Indent No'] = indent['indentNo']
                entry[systemEntryDate] = indent['createTs'].strftime("%d-%m-%y")
                entry['Requested Time'] = report_utility.format_time_to_ampm(indent['createTs'].strftime("%H:%M:%S"))
                entry['Category'] = category
                entry['Sub Category'] = subCategory
                entry['Item Code'] = itemCode
                entry['Item Name'] = item['itemName']
                entry['Package Name'] = packageName
                entry['Requested Qty'] = item['issueQty']
                entry['Issued Qty'] = item['dispatchedQty']
                entry['Pending Qty'] = item['pendingQty']
                entry['Entry Type']=  item['entryType']
                entry['Issued Time'] = report_utility.format_time_to_ampm(indent['modTs'].strftime("%H:%M:%S"))
                entry[indentIssuedDate] = indent['modTs'].strftime("%d-%m-%y")
                entry[indentDate] =  indent['indentDocumentDate'].strftime("%d-%m-%y")
                if item['issueQty'] == item['dispatchedQty']:
                    entry['Indent Status'] = "Completed"
                elif entry['Requested Qty'] == entry['Pending Qty'] :
                    entry['Indent Status'] = "Pending"
                    entry[indentIssuedDate] = "N/A"
                    entry['Issued Time'] = "N/A"
                elif item['issueQty'] > item['dispatchedQty']:
                    entry['Indent Status'] = "Partial" 
                
                price = float(item['price'] if 'price' in item.keys() else item['packages'][0]['packagePrice'])
                subTotal = item['dispatchedQty'] * price 
                entry['Tax (%)'] = taxRate
                entry['Unit Price'] = report_utility.truncate_and_floor(price, 2)
                entry['Total(excl.tax)'] = report_utility.truncate_and_floor(subTotal, 2)
                entry['Tax Amount'] = report_utility.truncate_and_floor((subTotal * (float(taxRate)/100)), 2)
                entry['Total(incl.tax,etc)'] = report_utility.truncate_and_floor(subTotal + ((subTotal)*(float(taxRate)/100)), 2)
                issueList.append(entry)
    df = pd.DataFrame(issueList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
    
    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}


def systemClosingReport(job:dict,sortby:dict):
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','INVENTORY','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas
    location1 = report_utility.getLocation(job['tenantId'])
    stockvaluesitems = list(Stockvalues.find(invSearchFilter))
    invList = []
    workAreaDict = {}
    systemDict = {}
    wacCache = {}
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    for item in stockvaluesitems:
        if item['category'].lower() not in _category:
            continue
        if item['subCategory'].lower() not in _subCategory:
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        if location not in systemDict:
            system = dailybackupsCol.find_one({
                'restaurantId': item['restaurantId'],
                'createTs': {
                    '$gte': datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0) , 
                    '$lte': datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)                     
                }
            })
            if system :
                systemDict[location] = system['items']
            else:
                systemDict[location] = []

        if location not in workAreaDict.keys():
            workAreaDict[location] =[]
        if 'packageName' not in item.keys():
            item['packageName'] = None
        if 'entryType' not in item.keys():
            item['entryType'] = None
        
        matching = next((i for i in systemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == item['packageName'] and i.get('entryType') == item['entryType']), None)
        if matching:
            item['inStock'] = matching['inStock']
            item['workArea'] = matching['workArea']
        else:
            continue
        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        packageName = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['taxRate'] = item.get("taxRate", 0)
        packageQty = item.get('packageQty', 1)
        price = item.get('price', 0) 

        item['inStock'] = item.get("inStock", 0)
        totalStockInHand = float(item['inStock'])
        subTotal = price * totalStockInHand
        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item.get("entryType","N/A"),
            'Package Name': packageName,
            'UOM': item['uom'],
            'Store Stock':  report_utility.truncate_and_floor(totalStockInHand, 2),
            "Tax(%)" : float(item['taxRate']),
            'Unit Price' : report_utility.truncate_and_floor(price, 2),
            'Store Total(excl.tax)' : report_utility.truncate_and_floor(subTotal, 2),
            'Tax Amount' : report_utility.truncate_and_floor((subTotal*(float(item['taxRate'])/100)), 2),
            'Store Total(incl.tax,etc)' : report_utility.truncate_and_floor(subTotal + ((subTotal)*(float(item['taxRate'])/100)), 2)
        }
        workAreaCount = 0
        workAreaTotalCount = 0
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if item['workArea'][workArea] == None:
                item['workArea'][workArea] = 0
            sub = price * item['workArea'][workArea]
            entry[workArea] = report_utility.truncate_and_floor(item['workArea'][workArea], 2)
            workAreaCount += entry[workArea]
            entry['{} Total(incl.tax)'.format(workArea)] = report_utility.truncate_and_floor(sub + ((sub)*(float(item['taxRate'])/100)), 2)
            workAreaTotalCount += report_utility.truncate_and_floor(sub + ((sub)*(float(item['taxRate'])/100)), 2)
            totalStockInHand += item['workArea'][workArea]
            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax)'.format(workArea))
        entry['All Work Area stock Total Qty'] = workAreaCount
        entry['All Work Area Stock Total(incl.tax)'] = workAreaTotalCount
        entry['Grand Total(excl.tax)'] = report_utility.truncate_and_floor(price * totalStockInHand, 2)
        entry['Grand Tax Amount'] = report_utility.truncate_and_floor((price * totalStockInHand)*(float(item['taxRate'])/100), 2)
        entry['Grand Total(incl.tax,etc)'] = report_utility.truncate_and_floor((price * totalStockInHand) +((price * totalStockInHand)*(float(item['taxRate'])/100)), 2)
        entry['Total Stock InHand(Store+work area)'] = report_utility.truncate_and_floor(totalStockInHand, 2)
        invList.append(entry)
    df = pd.DataFrame(invList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
          

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records') )}


def manualClosingReport(job:dict,sortby:dict):
    selectedRestaurants = job['details']['selectedRestaurants']
    if job['details']['startDate'] is None:
        job['details']['startDate'] = datetime.datetime.now()
    startTime = datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime   = datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    openDictWa, closingUserWorkAreaDict = report_utility.getUserDictWorkArea(job, 'closing', 2)
    openDictStore, closingUserStoreDict = report_utility.getUserDictStore(job, 'closing', 2)
    final = []
    workAreaDict = {}
    location1 = report_utility.getLocation(job['tenantId'])
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','INVENTORY','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas

    invItems = list(Stockvalues.find(invSearchFilter))
    for item in invItems:
        itemCode = item['itemCode']
        restaurantId = item['restaurantId']
        location = location1[restaurantId]
        if location not in workAreaDict.keys():
            workAreaDict[location] =[]
        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A') 
        taxRate = item.get('taxRate', 0)
        packageQty = item.get('packageQty', 1)
        price = item.get('price', 0) 

        if item['ItemType'] == "SubRecipe":
            searchKey = itemCode + '|' + 'N/A' + '|' + 'N/A'
            measure = "open"
        else:
            measure = item['entryType']
            searchKey = itemCode + '|' + item['uom'].upper() + '|' + item['packageName'].upper()

        ###################### store valuation ###################
        storeQty = 0
        if item['entryType'] == "open":
            if restaurantId in openDictStore:
                if itemCode in openDictStore[restaurantId]['store'].keys():
                    storeQty = openDictStore[restaurantId]['store'][itemCode]
        else:
            if restaurantId in closingUserStoreDict:
                if searchKey in closingUserStoreDict[restaurantId]['store'].keys():
                    storeQty = closingUserStoreDict[restaurantId]['store'][searchKey][measure]

        subTotal = price * storeQty
        entry = {
            'Location' : location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item['entryType'],
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            'Store Stock': storeQty,
            'Unit Price' : report_utility.truncate_and_floor(price, 2),
            'Tax%': report_utility.truncate_and_floor(taxRate, 2),
            'Total(excl.tax)': report_utility.truncate_and_floor(subTotal, 2),
            'Tax Amount': report_utility.truncate_and_floor((subTotal*(taxRate/100)), 2),
            'Store Total(incl.tax,etc)': report_utility.truncate_and_floor(subTotal + ((subTotal)*(taxRate/100)), 2)
        }

        ###################### workarea valuation ###################
        workAreaQty = 0
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            entry[workArea] = 0
            if item['entryType'] == "open":
                if restaurantId in openDictWa:
                    if workArea in openDictWa[restaurantId]:
                        if itemCode in openDictWa[restaurantId][workArea]:
                            entry[workArea] = openDictWa[restaurantId][workArea][itemCode]
            else:
                if restaurantId in closingUserWorkAreaDict:
                    if workArea in closingUserWorkAreaDict[restaurantId]:
                        if searchKey in closingUserWorkAreaDict[restaurantId][workArea]:
                            entry[workArea] = closingUserWorkAreaDict[restaurantId][workArea][searchKey][measure]

            workAreaQty += entry[workArea]
            sub = price * (entry[workArea])
            entry['{} Total(incl.tax)'.format(workArea)] = report_utility.truncate_and_floor(sub + ((sub)*(taxRate/100)), 2)
            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax)'.format(workArea))

        totalStockInHand = (storeQty + workAreaQty)
        entry['Grand Total(excl.tax)'] = report_utility.truncate_and_floor(price * totalStockInHand, 2)
        entry['Grand Tax Amount'] = report_utility.truncate_and_floor((price * totalStockInHand)*(taxRate/100), 2)
        entry['Grand Total(incl.tax,etc)'] = report_utility.truncate_and_floor((price * totalStockInHand) +((price * totalStockInHand)*(taxRate/100)), 2)
        entry['Total Quantity'] = report_utility.truncate_and_floor(totalStockInHand, 2)
        final.append(entry)
    df = pd.DataFrame(final)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records') )}



def store_variance(job:dict,sortby:dict):
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','INVENTORY','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    stockvaluesitems = list(Stockvalues.find(invSearchFilter))
    invList = []
    openingSystemDict = {}
    closingSystemDict = {}
    wacCache ={}    
    location1 = report_utility.getLocation(job['tenantId'])
    ########################## prerequists #############################

    startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    grnDict = report_utility.getGrnDict(job, startTime, endTime, qtyWise=True)
    indentDict, _sr = report_utility.getIndentDict(job, startTime, endTime, qtyWise=True)
    ibtInDict, ibtOutDict = report_utility.getIbtDict(job, startTime, endTime, qtyWise=True)
    spoilageDict = report_utility.getSpoilageDictStore(job, startTime, endTime, qtyWise=True)
    # closingUserStoreDict = getUserDictStore(job, 'closing', 1, qtyWise=True)
    openDictStore, closingUserStoreDict = report_utility.getUserDictStore(job, 'closing', 2)
    openDictStoreOpen, userStoreDictOpen = report_utility.getUserDictStore(job, 'opening', 2)
    for item in stockvaluesitems:
        if item['category'].lower() not in _category:
            continue
        if item['subCategory'].lower() not in _subCategory:
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        restaurantId = item['restaurantId']

        entryType = item.get('entryType', None)
        pkgName = item.get('packageName', None)

        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A') 
        taxRate = item.get('taxRate', 0)
        packageQty = item.get('packageQty', 1)
        withTaxPrice = item.get('withTaxPrice', 0)         

        if item['ItemType'] == "SubRecipe":
            searchKey1 = itemCode + '|' + 'N/A' + '|' + 'N/A'
            measure = "open"
        else:
            measure = item['entryType']
            searchKey1 = itemCode + '|' + item['uom'].upper() + '|' + item['packageName'].upper()

        ###################### user closing ###################
        physicalStoreQty = 0
        if item['entryType'] == "open":
            if item['restaurantId'] in openDictStore:
                if itemCode in openDictStore[item['restaurantId']]['store'].keys():
                    physicalStoreQty = openDictStore[item['restaurantId']]['store'][itemCode]
        else:
            if item['restaurantId'] in closingUserStoreDict:
                if searchKey1 in closingUserStoreDict[item['restaurantId']]['store'].keys():
                    physicalStoreQty = closingUserStoreDict[item['restaurantId']]['store'][searchKey1][measure]


        ###################### user opening ###################
        systemOpening = 0
        matchFound = False
        if item['entryType'] == "open":
            if item['restaurantId'] in openDictStoreOpen:
                if itemCode in openDictStoreOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : openDictStoreOpen[item['restaurantId']]['store'][itemCode]}
        else:
            if item['restaurantId'] in userStoreDictOpen:
                if searchKey1 in userStoreDictOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : userStoreDictOpen[item['restaurantId']]['store'][searchKey1][measure]}

        searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
        if not matchFound :
            ######################## system opening ########################
            if location not in openingSystemDict:
                system = dailybackupsCol.find_one({
                    'restaurantId': item['restaurantId'],
                    'createTs': {
                        '$gte': datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0) - timedelta(days=1),
                        '$lte':datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59) - timedelta(days=1)
                    }
                })
                openingSystemDict[location] = system['items'] if system else []
            if 'packageName' not in item.keys():
                item['packageName'] = None
            if 'entryType' not in item.keys():
                item['entryType'] = None
            systemOpening = next((i for i in openingSystemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == pkgName and i.get('entryType') == entryType), {})
        

        ######################## system closing ########################
        systemClosing = {}
        if endTime.replace(hour=0, minute=0, second=0) <= datetime.now().replace(tzinfo=timezone.utc) <= endTime:
            systemClosing['inStock'] = item['inStock']
        else:
            if location not in closingSystemDict:
                system = dailybackupsCol.find_one({
                    'restaurantId': item['restaurantId'],
                    'createTs': {
                        '$gte': datetime.strptime(job['details']['endDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0),
                        '$lte': datetime.strptime(job['details']['endDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
                    }
                })
                closingSystemDict[location] = system['items'] if system else []

            if 'packageName' not in item.keys():
                item['packageName'] = None
            if 'entryType' not in item.keys():
                item['entryType'] = None
            systemClosing = next((i for i in closingSystemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == pkgName and i.get('entryType') == entryType), {})


        ############################### GRN ##############################
        if restaurantId in grnDict:
            if searchKey in grnDict[restaurantId]:
                grnQty = grnDict[restaurantId][searchKey]['received']
            else:
                grnQty = 0
        else:
            grnQty = 0

        ############################# ibt in #############################
        if restaurantId in ibtInDict:
            if searchKey in ibtInDict[restaurantId]:
                ibtInQty = ibtInDict[restaurantId][searchKey]
            else:
                ibtInQty = 0
        else:
            ibtInQty = 0

        ############################# ibt out #############################
        if restaurantId in ibtOutDict:
            if searchKey in ibtOutDict[restaurantId]:
                ibtOutQty = ibtOutDict[restaurantId][searchKey]
            else:
                ibtOutQty = 0
        else:
            ibtOutQty = 0

        ############################# indent #############################
        if restaurantId in indentDict:
            if searchKey in indentDict[restaurantId]:
                indentQty = indentDict[restaurantId][searchKey]['issued']
            else:
                indentQty = 0
        else:
            indentQty = 0

        ############################# spoilage #############################
        if restaurantId in spoilageDict:
            if searchKey in spoilageDict[restaurantId]:
                spoilageQty = spoilageDict[restaurantId][searchKey]
            else:
                spoilageQty = 0
        else:
            spoilageQty = 0


        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item['entryType'],
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            "Tax(%)" : taxRate,
            'WAC with Tax' : report_utility.truncate_and_floor(withTaxPrice, 2),
            'Opening Qty':  report_utility.truncate_and_floor(systemOpening.get('inStock', 0), 2),
            'Purchase Qty':  report_utility.truncate_and_floor(grnQty, 2),
            'Indent Qty':  report_utility.truncate_and_floor(indentQty, 2),
            'Ibt In Qty':  report_utility.truncate_and_floor(ibtInQty, 2),
            'Ibt Out Qty':  report_utility.truncate_and_floor(ibtOutQty, 2),
            'Spoilage Qty':  report_utility.truncate_and_floor(spoilageQty, 2),
            'Closing Qty':  report_utility.truncate_and_floor(systemClosing.get('inStock', 0), 2),
            'Physical Closing Qty':  report_utility.truncate_and_floor(physicalStoreQty, 2),
            "Variance" : report_utility.truncate_and_floor((report_utility.truncate_and_floor(physicalStoreQty, 2) - report_utility.truncate_and_floor(systemClosing.get('inStock', 0), 2)),2),
            "Variance Amount" : report_utility.truncate_and_floor(((report_utility.truncate_and_floor(physicalStoreQty, 2) - report_utility.truncate_and_floor(systemClosing.get('inStock', 0), 2)) * withTaxPrice),2), 
            'Opening Amount':  report_utility.truncate_and_floor((systemOpening.get('inStock', 0) * withTaxPrice),2),
            'Purchase Amount':  report_utility.truncate_and_floor((grnQty * withTaxPrice),2),
            'Indent Amount':  report_utility.truncate_and_floor((indentQty * withTaxPrice),2),
            'Ibt In Amount':  report_utility.truncate_and_floor((ibtInQty * withTaxPrice),2),
            'Ibt Out Amount':  report_utility.truncate_and_floor((ibtOutQty * withTaxPrice),2),
            'Spoilage Amount':  report_utility.truncate_and_floor((spoilageQty * withTaxPrice),2),
            'Closing Amount':  report_utility.truncate_and_floor((systemClosing.get('inStock', 0) * withTaxPrice),2),
            'Physical Closing Amount':  report_utility.truncate_and_floor((physicalStoreQty * withTaxPrice), 2) 
        }
        invList.append(entry)
    df = pd.DataFrame(invList)

    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {
        "status": "success", 
        "response" :  { 
           job["details"]['type']  : json.loads(df.to_json(orient='records')), 
           
        }
    }



def intraBranchTransferReport(job:dict,sortby:dict):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    query = {
        'restaurantId': {'$in': selectedRestaurants},
        'date': {'$gte': startDate,'$lte': endDate}
    }
    location1 = report_utility.getLocation(job['tenantId'])    
    transferRecords = list(intrabranchtransfersCol.find(query, {"_id": 0}))
    transferList = []
    itemPriceDict ={}
    for record in transferRecords:
        restaurantId = record["restaurantId"]
        report_utility.getStockDict(restaurantId, stockDict)
        if restaurantId not in itemPriceDict.keys():
            itemPriceDict[restaurantId] ={}

        for item, value in record["items"].items():
            itemCode, packageName, entryType = item.split('|')
            category ='N/A'
            subCategory ='N/A'
            taxRate = 0
            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == packageName), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                taxRate = sv['taxRate'] 
                itemName = sv['itemName'] 
 
            if entryType == "open":
                searchKey = itemCode + "|" + entryType.upper()
            else:
                searchKey = itemCode + "|" + packageName.upper()

            if searchKey in itemPriceDict[restaurantId] :
                price = itemPriceDict[restaurantId] [searchKey]
            else:
                svEntry = None
                if packageName != "N/A" and entryType == "package":
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"price" : 1})
                else:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"price" : 1})
                if svEntry:
                    price = svEntry.get('price', 0)
                itemPriceDict[restaurantId] [searchKey] = price
            subTotal = value * price
            tempObj = {
                "Location" : location1[restaurantId],	
                "Source" : record["sourceWorkArea"],
                "Destination" : record["destinationWorkArea"],
                "Date" : report_utility.convert_datetime_to_date(record["date"] ),
                "Category" : category,
                "Sub Category" : subCategory,
                "Item Code" : itemCode,
                "Item Name" : itemName,
                "Package Name" : packageName,
                "Transfered Qty" : value,
                "Unit Price" : report_utility.truncate_and_floor(price, 2),
                'Tax%' : taxRate,
                'Total(excl.tax)' : report_utility.truncate_and_floor(subTotal, 2),
                'Tax Amount' : report_utility.truncate_and_floor((subTotal*(taxRate/100)), 2),
                'Total(incl.tax,etc)' :report_utility.truncate_and_floor(subTotal + ((subTotal)*(taxRate/100)), 2)
            }
            transferList.append(tempObj)
    df = pd.DataFrame(transferList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}



def barVarianceReport(job:dict,sortby:dict, internalCall = False, autobarList=[]):
    startTime =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    ###################### pre requists ##########################
    indent = report_utility.getIndentDict(job, startTime, endTime)
    indentDict = indent[0]
    subrecipeIndentDict = indent[1]
    theoretical = report_utility.getTheoreticalConsumption(job, startTime, endTime)
    theoreticalConsumptionDict = theoretical[0]
    subrecipeConsumptionDict = theoretical[1]
    openingUserDict = report_utility.getUserDictWorkArea(job, 'opening')
    closingUserDict = report_utility.getUserDictWorkArea(job, 'closing')
    ibtDict = report_utility.getIntraBranchTransferDict(job, startTime, endTime)
    ibtInDict = ibtDict[0]
    ibtOutDict = ibtDict[1]
    overallList = []
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    location1 = report_utility.getLocation(job['tenantId'])	
    for restaurantId in job['details']['selectedRestaurants']:
        queries=[
            {'restaurantId': restaurantId, 'entryType': 'open', 'ItemType': {'$in': ['Inventory', 'INVENTORY']}},
            {'restaurantId': restaurantId, 'uom': {'$in':['Nos','nos','NOS']}, 'ItemType': {'$in': ['Inventory', 'INVENTORY']}}
        ]
        for query in queries:
            orQueryForWorkAreas=[]
            for area in job['details']['selectedWorkAreas']:
                temp={}
                temp['workArea.{}'.format(area.strip())] = {'$exists': True }
                orQueryForWorkAreas.append(temp)
            if len(orQueryForWorkAreas) > 0:
                query['$or'] = orQueryForWorkAreas
            invItems = list(Stockvalues.find(query))
            for entry in invItems:
                if entry['itemCode'] in autobarList:
                    continue
                if entry['category'].lower() not in _category:
                    continue
                if entry['subCategory'].lower() not in _subCategory:
                    continue
                itemCode = entry['itemCode'].strip()
                uom = entry['uom']
                searchKey = itemCode + '|' + uom.upper()
                if uom.lower() == 'kg':
                    weight = 1000
                    updatedUom = "GM"
                elif uom.lower() == 'litre' or uom.lower() == 'ltr':
                    weight = 1000
                    updatedUom = 'ML'
                elif uom.lower() == 'nos':
                    weight = 1
                    updatedUom = 'NOS'
                else:
                    continue

                ########## ibt in #############
                if restaurantId in ibtInDict:
                    if searchKey in ibtInDict[restaurantId]:
                        ibtInQty = ibtInDict[restaurantId][searchKey]
                    else:
                        ibtInQty = 0
                else:
                    ibtInQty = 0

                ########## ibt out ############
                if restaurantId in ibtOutDict:
                    if searchKey in ibtOutDict[restaurantId]:
                        ibtOutQty = ibtOutDict[restaurantId][searchKey]
                    else:
                        ibtOutQty = 0
                else:
                    ibtOutQty = 0

                ########## indent #############
                if restaurantId in indentDict:
                    if searchKey in indentDict[restaurantId]:
                        indentQty = indentDict[restaurantId][searchKey]
                    else:
                        indentQty = 0
                else:
                    indentQty = 0

                ########## Theoretical ConsumptionDict #############
                if restaurantId in theoreticalConsumptionDict:
                    if searchKey in theoreticalConsumptionDict[restaurantId]:
                        theoreticalConsumptionQty = theoreticalConsumptionDict[restaurantId][searchKey]
                    else:
                        theoreticalConsumptionQty = 0
                else:
                    theoreticalConsumptionQty = 0

                ########## Opening User  #############
                if restaurantId in openingUserDict:
                    if searchKey in openingUserDict[restaurantId]:
                        openingUserQty = openingUserDict[restaurantId][searchKey]
                    else:
                        openingUserQty = 0
                else:
                    openingUserQty = 0

                ########## Closing User #############
                if restaurantId in closingUserDict:
                    if searchKey in closingUserDict[restaurantId]:
                        closingUserQty = closingUserDict[restaurantId][searchKey]
                    else:
                        closingUserQty = 0
                else:
                    closingUserQty = 0
                tempDict = {}
                tempDict['Location'] = location1[entry['restaurantId']]	
                tempDict['Item Code'] = entry['itemCode']
                tempDict['Item Name'] = entry['itemName']
                tempDict['Category'] = entry['category']
                tempDict['Sub Category'] = entry['subCategory']
                tempDict['UOM'] = updatedUom
                tempDict['Opening Stock'] = report_utility.truncate_and_floor(openingUserQty, 3)
                tempDict['Indent Qty'] = report_utility.truncate_and_floor((indentQty * weight), 3)
                tempDict['Intra Branch Transfer'] = report_utility.truncate_and_floor(((ibtInQty *weight) -(ibtOutQty*weight)), 3)
                tempDict['Total Stock'] = report_utility.truncate_and_floor((tempDict['Opening Stock'] + tempDict['Indent Qty'] +tempDict['Intra Branch Transfer']  ), 3)
                tempDict['Closing Stock'] = report_utility.truncate_and_floor(closingUserQty, 3)
                tempDict['Consumed Qty'] = report_utility.truncate_and_floor((tempDict['Total Stock'] - tempDict['Closing Stock']), 3)
                tempDict['Sold Qty'] = report_utility.truncate_and_floor((theoreticalConsumptionQty * weight), 3)
                tempDict['Variance Qty'] = report_utility.truncate_and_floor((tempDict['Sold Qty'] - tempDict['Consumed Qty'] ), 3)
                overallList.append(tempDict)
    df = pd.DataFrame(overallList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
    
    return {"status": "success", job["details"]['type'] :  json.loads(df.to_json(orient='records'))}



def flrReport(job:dict,sortby:dict):
    tenantId = job['tenantId']
    selectedRestaurants = job['details']['selectedRestaurants']
    startTime =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    tmpClosingFilter = {
        'restaurantId' : {'$in': selectedRestaurants},
        'active': False,
        'type' :'kitchenClosing',
        'date': {'$gte': startTime, '$lte': endTime}
    }
    location1 = report_utility.getLocation(job['tenantId'])	
    tmpClosingData = list(tmpclosingsCol.find(tmpClosingFilter))
    checkList = ["LIQUORS", "WINE", "BEER", "LIQUOR"]
    flrList = []
    backup = {}
    for entry in tmpClosingData:
        loc = location1[entry['restaurantId']]
        if loc not in backup.keys():
            system = db.dailybackupsCol.find_one({
                'restaurantId': entry['restaurantId'],
                'createTs': {'$gte': startTime, '$lte': endTime}
            })
            backup[loc] = system['items'] if system else []       
        for workArea in entry['workAreas']:
            for category in entry['workAreas'][workArea]['category']:
                if category in checkList:
                    for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                        if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                            for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                                if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                    for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                        flrDict = {}
                                        flrDict['id'] = entry['_id']
                                        flrDict['Location'] = loc
                                        flrDict['WorkArea'] = workArea
                                        flrDict['Category'] = category
                                        flrDict['Sub Category'] = subCategory
                                        flrDict['Item Code'] = itemCode
                                        flrDict['Item Name'] = data['itemName']
                                        flrDict['Pkg Name'] = data['pkgName']
                                        flrDict['Pkg Qty'] = data['pkgQty']
                                        check = report_utility.contains(flrList, lambda x:(x['Item Code'] == itemCode and x['Pkg Name'] == data['pkgName'] and x['WorkArea'] == workArea) )
                                        if check is not None:
                                            if ((check['id'] == entry['_id']) and (check['Location'] == loc)):
                                                check['Workarea Full Bottle (nos)'] += report_utility.truncate_and_floor(data['orderedPackages'], 2)
                                                check['Workarea Open Bottle (ml)'] += report_utility.truncate_and_floor(report_utility.checkBottleWeight(data) , 2)
                                            else:
                                                check['id'] = entry['_id']
                                                check['Location'] = loc
                                                check['Workarea Full Bottle (nos)'] = 0
                                                check['Workarea Open Bottle (ml)'] = 0
                                                check['Workarea Full Bottle (nos)'] = report_utility.truncate_and_floor(data['orderedPackages'], 2)
                                                check['Workarea Open Bottle (ml)'] = report_utility.truncate_and_floor(report_utility.checkBottleWeight(data) , 2)
                                        else:
                                            flrDict['Workarea Full Bottle (nos)'] = report_utility.truncate_and_floor(data['orderedPackages'], 2)
                                            flrDict['Workarea Open Bottle (ml)'] = report_utility.truncate_and_floor(report_utility.checkBottleWeight(data) , 2)
                                            flrList.append(flrDict)
    df = pd.DataFrame(flrList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
    
    # split_data = {group: group_df for group, group_df in df_selected.groupby("Item Name")}
    # # print(split_data)
    # # for group, group_df in split_data.items():
    # #   print(f"\nGroup: {group}")
    # #   print(group_df)
    return {"status": "success", job["details"]['type'] :json.loads(df.to_json(orient='records', force_ascii=False, default_handler=str)) }



def billWiseReport(job:dict,sortby:dict):
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    print(job['details']['selectedVendors'])
    filters={
        "restaurantId":{'$in': job['details']['selectedRestaurants']},
        'vendorId' :  { '$in': job['details']['selectedVendors']},
        "createTs":{"$gte":startDate,"$lte":endDate},
        'grnType': 'po'
    }
    location1 = report_utility.getLocation(job['tenantId'])	
    
    
    grnList = list(grnsCol.aggregate([
        { '$match': filters},
        { "$addFields": { "amount": {"$sum": "$grnItems.totalPrice" }}},
        { "$addFields": { "status": "$status.inwardStatus"}},
        { "$lookup" : {
            "from" :  "tenants",
            "let"  :  {"tenantId" :  "$vendorId", "servingTenantId" :  "$tenantId"},
            "pipeline" :  [{"$match" : {"$expr" : {"$and" : [
                    {"$eq" : ["$tenantId","$$tenantId"]},
                    {"$eq" : ["$servingTenantId","$$servingTenantId"]}
            ]}}}],
            "as" :  "vendorDetails"
            }
        },
        { "$unwind" :"$vendorDetails"},
        { "$addFields": { "vendorName": "$vendorDetails.tenantName"}},
        { "$unwind": "$grnItems" },
        { "$group": {
            "_id": "$_id",
            "Location": { "$first": "$restaurantId" },	
            "Vendor Id": { "$first": "$vendorId" },
            "Vendor Name": { "$first": "$vendorName" },
            "Invoice Id": { "$first": "$invoiceId" },
            "Invoice Date": { "$first": {
                "$dateToString": {
                    "format": "%d-%m-%Y",
                    "date": "$invoiceDate"
                }
            }},
            "GRN Id": { "$first": "$grnId" },
            'Total(excl tax)': { "$sum": "$grnItems.subTotal" },
            "Tax Amount": { "$sum": "$grnItems.taxAmount" },
            'Total(incl tax,etc)': { "$first": "$amount" },
            "Transportation": { "$first": { "$toDouble": "$otherCharges" } },
            "Labour": { "$first": { "$toDouble": "$labourCharges" } },
            "Status": { "$first": "$status" }
        }},
        { "$addFields": {
            "grandTotal": {
                "$sum": [
                    '$Total(incl tax,etc)',
                    { "$toDouble": "$Transportation" },
                    { "$toDouble": "$Labour" }
                ]
            }
        }},
        { "$project":{
            "_id":0,
            "Location": 1,
            "Vendor Id" : 1,
            "Vendor Name" : 1,
            "Invoice Id" : 1,
            "Invoice Date": 1,
            "GRN Id": 1,
            'Total(excl tax)' : 1,
            "Tax Amount" : 1,
            'Total(incl tax,etc)': 1,
            "Transportation" : 1,
            "Labour": 1,
            "Grand Total" : "$grandTotal",
            "Status": 1
            }
        },
        { "$sort": { "Invoice Date": 1 } }
    ]))

    df = pd.DataFrame(grnList)
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type']:json.loads(df.to_json(orient='records', force_ascii=False, default_handler=str)) }




def accuracyReport(job:dict,sortby:dict):
    tenantId = job['tenantId']
    categories = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    subCategories = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    selectedRestaurants = job['details']['selectedRestaurants']
    startTime =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endTime = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)

    clientRec = roloposconfigsCol.find_one({"tenantId": tenantId})
    if clientRec and ('predictForNext' in clientRec.keys()):
        predictForNext = clientRec['predictForNext']
    else:
        predictForNext = 0
    searchFilter = {
        'createTs': {'$gte': startTime, '$lte': endTime},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    salesItems = list(salesdetailsCol.find(searchFilter))
    metricsItems = list(metricsCol.find({
        'date': {
            '$gte': startTime - timedelta(days=predictForNext), 
            '$lte': endTime - timedelta(days=predictForNext)
        },
        'restaurantId' : {'$in': selectedRestaurants}
    }))
    salesData = report_utility.consolidatedSalesData(salesItems)
    metricsData = report_utility.calculateMetricsData(metricsItems)
    salesDf = pd.DataFrame(list(salesData.values()))
    salesDf.sort_values(by=['itemCode'], inplace=True)
    metricsDf = pd.DataFrame(list(metricsData.values()))
    metricsDf.sort_values(by=['itemCode'], inplace=True)
    new_df = pd.merge(salesDf, metricsDf, on='itemCode')
    new_df['deviation'] = ((new_df['actual']/new_df['predicted'])-1)*100
    group = {'deviation': 'median', 'category': 'first'}
    medianDf = pd.DataFrame(new_df.groupby(['category']).agg(group))
    medianDf = medianDf.sort_values(by=sortby["name"], ascending=sortby["type"])[selected_columns]    
    
    return {"status": "success", job["details"]['type'] :  json.loads(medianDf.to_json(orient='records'))}




def adjustInventoryReport(job:dict,sortby:dict):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate =datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
    endDate = datetime.strptime(job['details']['endDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    adjustInvFilter = {
        'restaurantId': {'$in': selectedRestaurants},
        'createTs': {'$gte': startDate, '$lte': endDate}
    }
    location1 = report_utility.getLocation(job['tenantId'])	
    adjustinvItems = list(adjustinvCol.find(adjustInvFilter))
    adjustInvDict = {}
    count = 0
    for entry in adjustinvItems:
        report_utility.getStockDict(entry['restaurantId'], stockDict)
        location = location1[entry['restaurantId']]	
        workArea = entry['workArea']
        reqNo = entry['adjustId']
        for item in entry['adjustInvItems']:
            itemCode = item['itemCode']
            item['packageName'] = item.get("packageName", item['packages'][0]["packageName"] if "packages" in item else "N/A")
            item['entryType'] = item.get('entryType', 'N/A')
            item['uom'] = item.get('uom', 'N/A')
            category ='N/A'
            subCategory ='N/A'
            taxRate = 0
            sv = next((x for x in stockDict[entry['restaurantId']] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                taxRate = sv['taxRate'] 
            price = item.get('price', 0)
            if price == 0:
                svEntry = None
                if item['packageName'] != "N/A" and item['entryType'] == "package":
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': item['packageName']})
                elif entryType == "open":
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'})
                else:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode})
                if svEntry:
                    price = svEntry.get('price', 0)
            subTotal = item['adjustQty'] * price
            adjType = "Increment" if item['adjustType'] == "Inc" else "Decrement"   
            adjustInvDict[count] = {
                'Location': location,
                'ID' : reqNo,
                'Adjusted Date' : entry['createTs'].strftime("%d-%m-%Y"),	
                'Adjusted Time' : format_time_to_ampm(entry['createTs'].strftime("%H:%M:%S")),	
                'WorkArea/Store': workArea,
                'Category' : category,
                'Sub Category' : subCategory,
                'Item Code': item['itemCode'],
                'Item Name': item['itemName'],
                'Entry Type': item['entryType'],
                'Package Name': item['packageName'],
                'UOM': item['uom'],
                'Type': adjType,
                'Qty': item['adjustQty'],
                'Unit Price': report_utility.truncate_and_floor(price, 2),
                'Tax%' : taxRate,
                'Total(excl.tax)' : report_utility.truncate_and_floor(subTotal, 2),
                'Tax Amount' : report_utility.truncate_and_floor((subTotal*(float(taxRate)/100)), 2),
                'Total(incl.tax,etc)' :report_utility.truncate_and_floor(subTotal + ((subTotal)*(float(taxRate)/100)), 2),
                'Remark Type': item.get('shortageType','Others'),
                'Remarks': item['reason']
            }
            count += 1
    df = pd.DataFrame(list(adjustInvDict.values()))
    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])
    
    return {"status": "success", job["details"]['type'] :json.loads(df.to_json(orient='records', force_ascii=False, default_handler=str)) }


def rtvReport(job:dict,sortby:dict):
    rtvDict = {}
    tenantId = job['tenantId']
    location1 = getLocation(job['tenantId'])
    categories = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    subCategories = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    selectedRestaurants = job['details']['selectedRestaurants']
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    rtvFilter = {'createTs': {'$gte': startTime, '$lte': endTime}}
    rtvFilter['tenantId'] = tenantId
    rtvFilter['restaurantId'] = {'$in': selectedRestaurants}
    rtvItems = list(rtvCol.find())
    count = 0
    for entry in rtvItems:
        location = location1[entry['restaurantId']]
        vendorId = entry['vendorId']
        date = str(entry['createTs']).split()[0]
        vendorItems = vendorinventorymastersCol.find_one({'tenantId': vendorId})
        vendorName = vendorItems['vendor']['name']
        for items in entry['items']:
            rtvDict[count] = {
                'location': location,
                'date': date,
                'itemCode': items['itemCode'],
                'itemName': items['itemName'],
                'vendorName': vendorName,
                'packageName': items['packageName'],
                'receivedQty': items['receivedQty'],
                'grnUnitPriceWithTax': items['grnUnitPriceWithTax'],
                'returnUnitPrice': items['returnUnitPrice'],
                'returnQty': items['returnQty'],
                'subTotal': items['subTotal']}
            count += 1
    rtvDf = pd.DataFrame(list(rtvDict.values()))
    rtvDf = rtvDf.reindex(columns=['date', 'itemCode', 'itemName', 'location', 'packageName',
                                   'receivedQuantity', 'grnUnitPriceWithTax', 'returnQty', 'returnUnitPrice', 'subTotal', 'vendorName'])
    df = pd.DataFrame(list(adjustInvDict.values()))

    if df.empty:
      print("DataFrame is empty")
    else:
      df = df.sort_values(by=sortby["name"], ascending=sortby["type"])

    return {"status": "success", job["details"]['type']:json.loads(df.to_json(orient='records', force_ascii=False, default_handler=str)) }


def costBreakUpReport(job:dict,sortby:dict):
    selectedRestaurants = job['details']['selectedRestaurants']
    menuDf = pandas.DataFrame(list(Stockvalues.aggregate([
        {"$match":{'ItemType':"Menu",'restaurantId':{"$in":selectedRestaurants}}},
        {"$unwind":"$costSplitUp"},
        {"$project":{"price":0, "withTaxPrice":0}}
    ])))
    if len(menuDf) > 0:
        menuDf = menuDf.join(pandas.json_normalize(menuDf['costSplitUp'])).drop('costSplitUp', axis='columns')
        menuDf = menuDf[["restaurantId","category","subCategory","itemCode","itemName","servingSize","servingSizeRatio","ingredientCode","ingredientName","ingredientUom","initialWeight","weightedAvgWoTax","woTaxPrice","weightedAvgWithTax","withTaxPrice"]]
        menuSubTotalDf = menuDf.groupby('itemCode', as_index=False).agg({'restaurantId':'first', 'initialWeight':'sum','woTaxPrice':'sum','withTaxPrice':'sum'})
        menuSubTotalDf['itemCode'] = menuSubTotalDf['itemCode'] + '-Production Cost'
        menuOverallDf = pd.concat([menuDf, menuSubTotalDf], axis=0, ignore_index=True)
        menuOverallDf = menuOverallDf.sort_values(['itemCode'])
        menuOverallDf.rename(columns={
            "restaurantId": "Location", 
            "category" : "Category",
            "subCategory": "Sub Category",
            "itemCode" : "Menu Code",
            "itemName" : "Menu Name",
            "servingSize" : "Serving Size",
            "servingSizeRatio" : "Serving Size Ratio",
            "ingredientCode" : "Ingredient Code",
            "ingredientName" : "Ingredient Name",
            "ingredientUom" : "Ingredient UOM",
            "initialWeight" : "Initial Weight",
            "weightedAvgWoTax" : "WAC (excl tax)",
            "woTaxPrice" : "COP (excl tax)",
            "weightedAvgWithTax" : "WAC (incl tax)",
            "withTaxPrice" : "COP (incl tax)",
        }, inplace=True)
        menuOverallDf[["WAC (excl tax)", "COP (excl tax)", "WAC (incl tax)", "COP (incl tax)"]] = \
            menuOverallDf[["WAC (excl tax)", "COP (excl tax)", "WAC (incl tax)", "COP (incl tax)"]].truncate_and_floor(2)
    else:
        menuOverallDf = pandas.DataFrame()

    subRecipeDf = pandas.DataFrame(list(Stockvalues.aggregate([
        {"$match":{'ItemType':"SubRecipe",'restaurantId':{"$in":selectedRestaurants}}},
        {"$unwind":"$costSplitUp"},
        {"$project":{"price":0, "withTaxPrice":0}}
    ])))
    if len(menuDf) > 0:
        subRecipeDf = subRecipeDf.join(pandas.json_normalize(subRecipeDf['costSplitUp'])).drop('costSplitUp', axis='columns')
        subRecipeDf = subRecipeDf[["restaurantId","category","subCategory","itemCode","itemName","uom","ingredientCode","ingredientName","ingredientUom","initialWeight","weightedAvgWoTax","woTaxPrice","weightedAvgWithTax","withTaxPrice"]]
        subRecipeSubTotalDf = subRecipeDf.groupby('itemCode', as_index=False).agg({'restaurantId':'first', 'initialWeight':'sum','woTaxPrice':'sum','withTaxPrice':'sum'})
        subRecipeSubTotalDf['itemCode'] = subRecipeSubTotalDf['itemCode'] + '-Production Cost'
        subRecipeOverallDf = pandas.concat([subRecipeDf, subRecipeSubTotalDf], axis=0, ignore_index=True)
        subRecipeOverallDf = subRecipeOverallDf.sort_values(['itemCode'])
        subRecipeOverallDf.rename(columns={
            "restaurantId": "Location", 
            "category" : "Category",
            "subCategory": "Sub Category",
            "itemCode" : "SubRecipe Code",
            "itemName" : "SubRecipe Name",
            "uom" : "SubRecipe UOM",
            "ingredientCode" : "Ingredient Code",
            "ingredientName" : "Ingredient Name",
            "ingredientUom" : "Ingredient UOM",
            "initialWeight" : "Initial Weight",
            "weightedAvgWoTax" : "WAC (excl tax)",
            "woTaxPrice" : "COP (excl tax)",
            "weightedAvgWithTax" : "WAC (incl tax)",
            "withTaxPrice" : "COP (incl tax)",
        }, inplace=True)
        subRecipeOverallDf[["WAC (excl tax)", "COP (excl tax)", "WAC (incl tax)", "COP (incl tax)"]] = \
            subRecipeOverallDf[["WAC (excl tax)", "COP (excl tax)", "WAC (incl tax)", "COP (incl tax)"]].truncate_and_floor(2)
    else:
        subRecipeOverallDf = pd.DataFrame()

    dateAndTime = getCreatedTime(job)
    output_file = f"{userReportsDirectory}/{reportNo}_{reportType}_{dateAndTime}.xlsx"
    try:
        dataframes = {'MENU': menuOverallDf, 'SUBRECIPE': subRecipeOverallDf}
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            workbook = writer.book
            branch = list(branchesCol.find({'restaurantIdOld': job['details']['selectedRestaurants'][0]}))
            tenantName = branch[0]['tenantName'] if branch else ""
            for key, df in dataframes.items():
                if df.empty: continue 
                grouped = df.groupby('Location')
                for sheet_name, df in grouped:
                    if df.empty: continue 
                    sheet_name = sanitize_sheet_name(sheet_name.upper() +key)
                    df.insert(0, 'S.No', range(1, len(df) + 1))
                    df.to_excel(writer, sheet_name=key + ' - ' +sheet_name, startrow=3, index=False)
                    sheet = writer.sheets[key + ' - ' +sheet_name]
                    sheet.set_zoom(90)
                    adjustWidth(df, writer, key + ' - ' +sheet_name)
                    createHeader(workbook, sheet, job, tenantName, key + ' - ' +sheet_name)
                    for col_num, value in enumerate(df.columns.values):
                        sheet.write(3, col_num, value, workbook.add_format(header_format))
                    sheet.freeze_panes(4, 0)
    except:
        with pandas.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            workbook = writer.book
