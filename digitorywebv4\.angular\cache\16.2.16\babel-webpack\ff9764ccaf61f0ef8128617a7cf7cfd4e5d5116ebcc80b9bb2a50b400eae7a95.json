{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_2_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Start\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Resume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startConversation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"mat-icon\", 27);\n    i0.ɵɵtext(3, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Restaurant Onboarding Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"I'll help you collect information about your restaurant outlets, cuisines, and more.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 28)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ChatBotComponent_div_2_span_11_Template, 2, 0, \"span\", 24);\n    i0.ɵɵtemplate(12, ChatBotComponent_div_2_span_12_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n  }\n}\nfunction ChatBotComponent_div_17_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r9.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"div\", 32);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_17_div_5_Template, 3, 4, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r9.sender === \"user\", message_r9.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r9.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r9.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtext(7, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Restaurant Data Yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"As you provide information about your restaurant through the chat, it will appear here.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatBotComponent_ng_container_38_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.restaurantData.commonCuisinesAcrossOutlets.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_38_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_38_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.restaurantData.signatureElements.signatureDishes.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_38_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_38_div_33_span_14_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_38_div_33_span_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ChatBotComponent_ng_container_38_div_33_span_14_span_1_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r22 = ctx.$implicit;\n    const last_r23 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", area_r22, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r23);\n  }\n}\nfunction ChatBotComponent_ng_container_38_div_33_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ng_container_38_div_33_span_14_span_1_Template, 3, 2, \"span\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const outlet_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", outlet_r17.outletWorkAreas);\n  }\n}\nfunction ChatBotComponent_ng_container_38_div_33_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_38_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h3\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"span\", 42);\n    i0.ɵɵtext(7, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 43);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 41)(11, \"span\", 42);\n    i0.ɵɵtext(12, \"Work Areas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 43);\n    i0.ɵɵtemplate(14, ChatBotComponent_ng_container_38_div_33_span_14_Template, 2, 1, \"span\", 24);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_38_div_33_span_15_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const outlet_r17 = ctx.$implicit;\n    const i_r18 = ctx.index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" Outlet \", i_r18 + 1, \": \", outlet_r17.outletName, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(outlet_r17.outletAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", outlet_r17.outletWorkAreas && outlet_r17.outletWorkAreas.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !outlet_r17.outletWorkAreas || outlet_r17.outletWorkAreas.length === 0);\n  }\n}\nfunction ChatBotComponent_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 40)(2, \"h3\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 41)(7, \"span\", 42);\n    i0.ɵɵtext(8, \"Total Outlets:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 41)(12, \"span\", 42);\n    i0.ɵɵtext(13, \"Cuisines:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 43);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_38_span_15_Template, 2, 1, \"span\", 24);\n    i0.ɵɵtemplate(16, ChatBotComponent_ng_container_38_span_16_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 41)(18, \"span\", 42);\n    i0.ɵɵtext(19, \"Signature Dishes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 43);\n    i0.ɵɵtemplate(21, ChatBotComponent_ng_container_38_span_21_Template, 2, 1, \"span\", 24);\n    i0.ɵɵtemplate(22, ChatBotComponent_ng_container_38_span_22_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 41)(24, \"span\", 42);\n    i0.ɵɵtext(25, \"Alcohol Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 43);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 41)(29, \"span\", 42);\n    i0.ɵɵtext(30, \"Tobacco Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 43);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ChatBotComponent_ng_container_38_div_33_Template, 16, 5, \"div\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.restaurantData.totalOutlets);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.commonCuisinesAcrossOutlets && ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.commonCuisinesAcrossOutlets || ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.signatureElements && ctx_r4.restaurantData.signatureElements.signatureDishes && ctx_r4.restaurantData.signatureElements.signatureDishes.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.signatureElements || !ctx_r4.restaurantData.signatureElements.signatureDishes || ctx_r4.restaurantData.signatureElements.signatureDishes.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.beverageInfo == null ? null : ctx_r4.restaurantData.beverageInfo.alcoholService) || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.tobaccoInfo == null ? null : ctx_r4.restaurantData.tobaccoInfo.tobaccoService) || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantData.outletDetails);\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    this.restaurantData = null;\n    this.conversationStarted = false;\n    this.isRefreshing = false;\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    this.dataUpdateSubscription = null;\n    // Flag to track if we're in the process of loading conversation history\n    this.loadingHistory = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n      // Reset the flag to ensure history is loaded\n      this.loadingHistory = false;\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      // Load conversation history\n      this.loadConversationHistory();\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        console.log('Checking messages after timeout:', this.messages.length);\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n          console.log('Conversation marked as started due to existing messages');\n        } else if (this.conversationStarted) {\n          // If conversation was started but no messages, initiate conversation\n          console.log('Conversation was started but no messages found, initiating conversation');\n          this.initiateConversation();\n        }\n        // Force UI update\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000); // Longer delay to ensure history is fully loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n        // Skip empty messages\n        if (!message.text.trim()) {\n          console.log('Skipping empty bot message');\n          return;\n        }\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Only update if the new message is not \"AI is thinking...\"\n          if (message.text !== 'AI is thinking...') {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n            console.log('Updated existing message');\n          }\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          // Replace \"AI is thinking...\" with actual content\n          const thinkingIndex = this.messages.findIndex(m => m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id);\n          if (thinkingIndex !== -1) {\n            // Replace the thinking message with the actual content\n            this.messages[thinkingIndex] = message;\n            console.log('Replaced thinking message with actual content');\n          } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n            // Add new bot message (but not \"AI is thinking...\" messages)\n            this.messages.push(message);\n            console.log('Added new bot message');\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n        // If this is a complete message (no blinking cursor), mark response as complete\n        if (!message.text.includes('blinking-cursor')) {\n          this.isWaitingForResponse = false;\n        }\n        // Don't show the \"AI is thinking...\" message if there's no user message before it\n        if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n          const lastMessage = this.messages[this.messages.length - 1];\n          if (lastMessage.sender !== 'user') {\n            // Skip this message\n            console.log('Skipping \"AI is thinking\" message because there is no user message before it');\n            return;\n          }\n        }\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(data => {\n      console.log('Received data update:', data);\n      if (data && data.type === 'restaurant_data' && data.data) {\n        console.log('Setting restaurant data:', data.data);\n        this.restaurantData = data.data;\n        console.log('Restaurant data received, restaurantData:', this.restaurantData);\n        // Force change detection\n        setTimeout(() => {\n          this.cd.detectChanges();\n          console.log('Change detection triggered after restaurant data update');\n        }, 0);\n        // The backend will automatically send the next question\n        // No need to request it from the frontend\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // No need to update restaurant summary\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n        // The backend will automatically handle the conversation flow\n        // No need to do anything special after sending a message\n      },\n\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  // Restaurant summary methods removed\n  // Removed unused methods for extracting information from messages\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.loadingHistory) {\n      console.log('Not loading conversation history: no tenantId or already loading');\n      return;\n    }\n    console.log('Loading conversation history for tenant:', this.tenantId);\n    // Set the flag to prevent duplicate API calls\n    this.loadingHistory = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Clear existing messages to avoid duplicates\n    this.messages = [];\n    // Load conversation history from the server\n    // Set addToStream to false so we can handle the messages ourselves\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        console.log('Received history messages:', messages.length);\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Sort messages by timestamp to ensure correct order\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          console.log('Sorted messages by timestamp');\n          // Add all messages to our local array without filtering\n          this.messages = messages;\n          console.log('Added all messages to local array, count:', this.messages.length);\n          // Mark conversation as started since we have history\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          // If no messages, initialize with empty array\n          this.messages = [];\n          console.log('No history found, initialized with empty array');\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, initialize with empty array\n        this.messages = [];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n    // No need to reset restaurant summary\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to empty array\n          this.messages = [];\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          // Reset restaurant data\n          this.restaurantData = null;\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [];\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          // Reset restaurant data\n          this.restaurantData = null;\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  // ngOnDestroy is already defined above\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Mark conversation as started\n    this.conversationStarted = true;\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    // The backend will send the welcome message and start the conversation\n    // Just initiate the conversation with the backend\n    this.initiateConversation();\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n  /**\n   * Initiate or continue the conversation with the backend\n   * This simply sends a signal to the backend to continue the conversation flow\n   */\n  initiateConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    console.log('Initiating conversation with backend for tenant:', this.tenantId);\n    // Send a special message to the backend to continue the conversation\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n        console.log('Continue conversation signal sent successfully');\n        // The response will come through the normal message subscription\n      },\n\n      error: error => {\n        console.error('Error initiating conversation:', error);\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Removed unused methods for checking data completeness and updating questions\n  /**\n   * Debug function to log the current messages\n   */\n  debugMessages() {\n    console.log('Current messages count:', this.messages.length);\n    this.messages.forEach((msg, index) => {\n      console.log(`Message ${index}:`, msg.id, msg.sender, msg.text.substring(0, 30) + '...');\n    });\n    // Force reload of conversation history\n    this.loadingHistory = false;\n    this.loadConversationHistory();\n  }\n  /**\n   * Refresh restaurant data from the backend\n   */\n  refreshRestaurantData() {\n    if (!this.tenantId || this.isRefreshing) {\n      return;\n    }\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 39,\n      vars: 12,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [\"class\", \"chat-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Debug Messages\", 1, \"debug-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-data-panel\"], [1, \"panel-header\"], [1, \"header-left\"], [1, \"header-right\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh restaurant data\", 1, \"refresh-button\", 3, \"disabled\", \"click\"], [1, \"panel-content\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"chat-overlay\", 3, \"click\"], [1, \"overlay-content\"], [1, \"overlay-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-button\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-text\"], [1, \"no-data-message\"], [1, \"data-section\", \"summary-section\"], [1, \"data-item\"], [1, \"label\"], [1, \"value\"], [\"class\", \"data-section outlet-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"data-section\", \"outlet-section\"], [\"class\", \"work-area-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"work-area-tag\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ChatBotComponent_div_2_Template, 13, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_10_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"clear\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_13_listener() {\n            return ctx.debugMessages();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"bug_report\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 10);\n          i0.ɵɵtemplate(17, ChatBotComponent_div_17_Template, 6, 8, \"div\", 11);\n          i0.ɵɵtemplate(18, ChatBotComponent_div_18_Template, 8, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"mat-form-field\", 14)(21, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_21_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_22_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18)(27, \"div\", 19)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"restaurant_menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"h2\");\n          i0.ɵɵtext(31, \"Restaurant Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 20)(33, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_33_listener() {\n            return ctx.refreshRestaurantData();\n          });\n          i0.ɵɵelementStart(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 22);\n          i0.ɵɵtemplate(37, ChatBotComponent_div_37_Template, 7, 0, \"div\", 23);\n          i0.ɵɵtemplate(38, ChatBotComponent_ng_container_38_Template, 34, 8, \"ng-container\", 24);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.conversationStarted);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"rotating\", ctx.isRefreshing);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantData);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, ReactiveFormsModule, MatButtonModule, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, MatCardModule, MatFormFieldModule, i6.MatFormField, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, MatProgressSpinnerModule, MatTooltipModule, i9.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  height: 100%;\\n  gap: 20px;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 0.6; \\n\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n  position: relative; \\n\\n}\\n\\n.restaurant-data-panel[_ngcontent-%COMP%] {\\n  flex: 0.4; \\n\\n  width: auto; \\n\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.chat-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 80%;\\n}\\n\\n.overlay-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f57c00; \\n\\n  margin-bottom: 1rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-size: 1rem;\\n  background-color: #f57c00; \\n\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  transition: background-color 0.3s;\\n}\\n\\n.start-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800; \\n\\n}\\n\\n.start-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #757575;\\n  height: 100%;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #f57c00; \\n\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n  max-width: 240px;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .refresh-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  transition: all 0.3s ease;\\n}\\n.panel-header[_ngcontent-%COMP%]   .refresh-button[_ngcontent-%COMP%]:hover {\\n  color: #f57c00;\\n}\\n.panel-header[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1.5s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n  color: #f57c00; \\n\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  overflow-y: auto;\\n  flex: 1;\\n  background-color: #f5f5f5;\\n}\\n\\n.data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 16px;\\n  border-bottom: 1px solid #f0f0f0;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.data-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.data-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n}\\n\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n  border-bottom: 1px solid #f0f0f0;\\n  padding-bottom: 10px;\\n}\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #f57c00;\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  background-color: #fff9f0;\\n}\\n\\n.outlet-section[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n}\\n\\n.work-area-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  padding: 4px 0;\\n  border-bottom: 1px dashed #f0f0f0;\\n}\\n.data-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #555;\\n  min-width: 130px;\\n  padding-right: 10px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #333;\\n  flex: 1;\\n  word-break: break-word;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: auto;\\n  justify-content: flex-start;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 3px;\\n  background-color: #57705d;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  font-size: 13px;\\n  color: #555;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-4px) scale(1.2);\\n    opacity: 0.9;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatBotComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "startConversation", "ɵɵtemplate", "ChatBotComponent_div_2_span_11_Template", "ChatBotComponent_div_2_span_12_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "messages", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r9", "timestamp", "ɵɵelement", "ChatBotComponent_div_17_div_5_Template", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate1", "ctx_r12", "restaurantData", "commonCuisinesAcrossOutlets", "join", "ctx_r14", "signatureElements", "signatureDishes", "ChatBotComponent_ng_container_38_div_33_span_14_span_1_span_2_Template", "area_r22", "last_r23", "ChatBotComponent_ng_container_38_div_33_span_14_span_1_Template", "outlet_r17", "outletWorkAreas", "ChatBotComponent_ng_container_38_div_33_span_14_Template", "ChatBotComponent_ng_container_38_div_33_span_15_Template", "ɵɵtextInterpolate2", "i_r18", "outletName", "outletAddress", "ɵɵelementContainerStart", "ChatBotComponent_ng_container_38_span_15_Template", "ChatBotComponent_ng_container_38_span_16_Template", "ChatBotComponent_ng_container_38_span_21_Template", "ChatBotComponent_ng_container_38_span_22_Template", "ChatBotComponent_ng_container_38_div_33_Template", "ɵɵelementContainerEnd", "ctx_r4", "totalOutlets", "beverageInfo", "alcoholService", "tobaccoInfo", "tobaccoService", "outletDetails", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "tenantId", "tenantName", "currentMessage", "isConnecting", "isWaitingForResponse", "conversationStarted", "isRefreshing", "messageSubscription", "connectionSubscription", "dataUpdateSubscription", "loadingHistory", "ngOnChanges", "changes", "newTenantId", "currentValue", "prevTenantId", "previousValue", "console", "log", "loadConversationHistory", "ngOnInit", "conversationStarted<PERSON><PERSON>", "localStorage", "getItem", "setTimeout", "setItem", "initiateConversation", "detectChanges", "scrollToBottom", "messages$", "subscribe", "message", "id", "startsWith", "substring", "trim", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "thinkingIndex", "push", "sort", "a", "b", "getTime", "lastMessage", "isDuplicate", "some", "Math", "abs", "dataUpdates$", "data", "type", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "open", "duration", "messageToSend", "userMessage", "generateId", "Date", "next", "error", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "trackById", "_index", "clearConversationHistory", "removeItem", "debugMessages", "for<PERSON>ach", "msg", "index", "refreshRestaurantData", "fetchRestaurantData", "panelClass", "onAction", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_div_2_Template", "ChatBotComponent_Template_button_click_10_listener", "ChatBotComponent_Template_button_click_13_listener", "ChatBotComponent_div_17_Template", "ChatBotComponent_div_18_Template", "ChatBotComponent_Template_input_ngModelChange_21_listener", "$event", "ChatBotComponent_Template_input_keydown_21_listener", "ChatBotComponent_Template_button_click_22_listener", "ChatBotComponent_Template_button_click_33_listener", "ChatBotComponent_div_37_Template", "ChatBotComponent_ng_container_38_Template", "ɵɵclassProp", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "MatMiniFabButton", "i6", "MatFormField", "i7", "MatIcon", "i8", "MatInput", "i9", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n  restaurantData: any = null;\n  conversationStarted: boolean = false;\n  isRefreshing: boolean = false;\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n  private dataUpdateSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if we're in the process of loading conversation history\n  private loadingHistory = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n\n      // Reset the flag to ensure history is loaded\n      this.loadingHistory = false;\n\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n\n      // Load conversation history\n      this.loadConversationHistory();\n\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        console.log('Checking messages after timeout:', this.messages.length);\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n          console.log('Conversation marked as started due to existing messages');\n        } else if (this.conversationStarted) {\n          // If conversation was started but no messages, initiate conversation\n          console.log('Conversation was started but no messages found, initiating conversation');\n          this.initiateConversation();\n        }\n\n        // Force UI update\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000); // Longer delay to ensure history is fully loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n\n          // Skip empty messages\n          if (!message.text.trim()) {\n            console.log('Skipping empty bot message');\n            return;\n          }\n\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Only update if the new message is not \"AI is thinking...\"\n            if (message.text !== 'AI is thinking...') {\n              // Update existing message\n              this.messages[existingMessageIndex] = message;\n              console.log('Updated existing message');\n            }\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            // Replace \"AI is thinking...\" with actual content\n            const thinkingIndex = this.messages.findIndex(m =>\n              m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id\n            );\n\n            if (thinkingIndex !== -1) {\n              // Replace the thinking message with the actual content\n              this.messages[thinkingIndex] = message;\n              console.log('Replaced thinking message with actual content');\n            } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n              // Add new bot message (but not \"AI is thinking...\" messages)\n              this.messages.push(message);\n              console.log('Added new bot message');\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n\n          // If this is a complete message (no blinking cursor), mark response as complete\n          if (!message.text.includes('blinking-cursor')) {\n            this.isWaitingForResponse = false;\n          }\n\n          // Don't show the \"AI is thinking...\" message if there's no user message before it\n          if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n            const lastMessage = this.messages[this.messages.length - 1];\n            if (lastMessage.sender !== 'user') {\n              // Skip this message\n              console.log('Skipping \"AI is thinking\" message because there is no user message before it');\n              return;\n            }\n          }\n\n          // Force change detection to update the UI immediately\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(\n      (data: any) => {\n        console.log('Received data update:', data);\n        if (data && data.type === 'restaurant_data' && data.data) {\n          console.log('Setting restaurant data:', data.data);\n          this.restaurantData = data.data;\n          console.log('Restaurant data received, restaurantData:', this.restaurantData);\n          // Force change detection\n          setTimeout(() => {\n            this.cd.detectChanges();\n            console.log('Change detection triggered after restaurant data update');\n          }, 0);\n\n          // The backend will automatically send the next question\n          // No need to request it from the frontend\n        }\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // No need to update restaurant summary\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n\n        // The backend will automatically handle the conversation flow\n        // No need to do anything special after sending a message\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  // Restaurant summary methods removed\n\n  // Removed unused methods for extracting information from messages\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.loadingHistory) {\n      console.log('Not loading conversation history: no tenantId or already loading');\n      return;\n    }\n\n    console.log('Loading conversation history for tenant:', this.tenantId);\n\n    // Set the flag to prevent duplicate API calls\n    this.loadingHistory = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Clear existing messages to avoid duplicates\n    this.messages = [];\n\n    // Load conversation history from the server\n    // Set addToStream to false so we can handle the messages ourselves\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        console.log('Received history messages:', messages.length);\n\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Sort messages by timestamp to ensure correct order\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n\n          console.log('Sorted messages by timestamp');\n\n          // Add all messages to our local array without filtering\n          this.messages = messages;\n          console.log('Added all messages to local array, count:', this.messages.length);\n\n          // Mark conversation as started since we have history\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          // If no messages, initialize with empty array\n          this.messages = [];\n          console.log('No history found, initialized with empty array');\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, initialize with empty array\n        this.messages = [];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n\n    // No need to reset restaurant summary\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to empty array\n          this.messages = [];\n\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n\n          // Reset restaurant data\n          this.restaurantData = null;\n\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n\n          // No pending questions to clear\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [];\n\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n\n          // Reset restaurant data\n          this.restaurantData = null;\n\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n  // ngOnDestroy is already defined above\n\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Mark conversation as started\n    this.conversationStarted = true;\n\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n\n    // The backend will send the welcome message and start the conversation\n    // Just initiate the conversation with the backend\n    this.initiateConversation();\n\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n\n  /**\n   * Initiate or continue the conversation with the backend\n   * This simply sends a signal to the backend to continue the conversation flow\n   */\n  private initiateConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n\n    console.log('Initiating conversation with backend for tenant:', this.tenantId);\n\n    // Send a special message to the backend to continue the conversation\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n        console.log('Continue conversation signal sent successfully');\n        // The response will come through the normal message subscription\n      },\n      error: (error) => {\n        console.error('Error initiating conversation:', error);\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n\n  // Removed unused methods for checking data completeness and updating questions\n\n  /**\n   * Debug function to log the current messages\n   */\n  debugMessages(): void {\n    console.log('Current messages count:', this.messages.length);\n    this.messages.forEach((msg, index) => {\n      console.log(`Message ${index}:`, msg.id, msg.sender, msg.text.substring(0, 30) + '...');\n    });\n\n    // Force reload of conversation history\n    this.loadingHistory = false;\n    this.loadConversationHistory();\n  }\n\n  /**\n   * Refresh restaurant data from the backend\n   */\n  refreshRestaurantData(): void {\n    if (!this.tenantId || this.isRefreshing) {\n      return;\n    }\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n}\n", "<div class=\"chat-layout\">\n  <div class=\"chat-container\">\n  <!-- Start/Resume Overlay -->\n  <div class=\"chat-overlay\" *ngIf=\"!conversationStarted\" (click)=\"startConversation()\">\n    <div class=\"overlay-content\">\n      <mat-icon class=\"overlay-icon\">restaurant</mat-icon>\n      <h2>Restaurant Onboarding Assistant</h2>\n      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>\n      <button mat-raised-button color=\"primary\" class=\"start-button\">\n        <mat-icon>play_arrow</mat-icon>\n        <span *ngIf=\"messages.length === 0\">Click to Start</span>\n        <span *ngIf=\"messages.length > 0\">Click to Resume</span>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n      <span class=\"assistant-title\">Restaurant details</span>\n    </div>\n    <div class=\"chat-actions\">\n      <!-- Preview button removed -->\n      <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n        <mat-icon>clear</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Debug Messages\" (click)=\"debugMessages()\" class=\"debug-btn\">\n        <mat-icon>bug_report</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n      <div class=\"message-content\">\n        <div class=\"message-wrapper\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n          <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Improved loading indicator when waiting for a response -->\n    <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n      <div class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <div class=\"typing-text\">AI is thinking...</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"isConnecting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n</div>\n\n  <!-- Restaurant Data Panel -->\n  <div class=\"restaurant-data-panel\">\n    <div class=\"panel-header\">\n      <div class=\"header-left\">\n        <mat-icon>restaurant_menu</mat-icon>\n        <h2>Restaurant Information</h2>\n      </div>\n      <div class=\"header-right\">\n        <button mat-icon-button class=\"refresh-button\" (click)=\"refreshRestaurantData()\" matTooltip=\"Refresh restaurant data\" [disabled]=\"isRefreshing\">\n          <mat-icon [class.rotating]=\"isRefreshing\">refresh</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"panel-content\">\n      <!-- No Data Message -->\n      <div class=\"no-data-message\" *ngIf=\"!restaurantData\">\n        <mat-icon>restaurant</mat-icon>\n        <h3>No Restaurant Data Yet</h3>\n        <p>As you provide information about your restaurant through the chat, it will appear here.</p>\n      </div>\n\n      <!-- Restaurant Data (shown when available) -->\n      <ng-container *ngIf=\"restaurantData\">\n        <!-- Summary Section -->\n        <div class=\"data-section summary-section\">\n          <h3><mat-icon>info</mat-icon> Restaurant Summary</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Total Outlets:</span>\n            <span class=\"value\">{{restaurantData.totalOutlets}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Cuisines:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.commonCuisinesAcrossOutlets && restaurantData.commonCuisinesAcrossOutlets.length > 0\">\n                {{restaurantData.commonCuisinesAcrossOutlets.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.commonCuisinesAcrossOutlets || restaurantData.commonCuisinesAcrossOutlets.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Signature Dishes:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.signatureElements && restaurantData.signatureElements.signatureDishes && restaurantData.signatureElements.signatureDishes.length > 0\">\n                {{restaurantData.signatureElements.signatureDishes.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.signatureElements || !restaurantData.signatureElements.signatureDishes || restaurantData.signatureElements.signatureDishes.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Alcohol Service:</span>\n            <span class=\"value\">{{restaurantData.beverageInfo?.alcoholService || 'Not specified'}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Tobacco Service:</span>\n            <span class=\"value\">{{restaurantData.tobaccoInfo?.tobaccoService || 'Not specified'}}</span>\n          </div>\n        </div>\n\n        <!-- Outlets Section -->\n        <div class=\"data-section outlet-section\" *ngFor=\"let outlet of restaurantData.outletDetails; let i = index\">\n          <h3><mat-icon>store</mat-icon> Outlet {{i+1}}: {{outlet.outletName}}</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Address:</span>\n            <span class=\"value\">{{outlet.outletAddress}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Work Areas:</span>\n            <span class=\"value\">\n              <span *ngIf=\"outlet.outletWorkAreas && outlet.outletWorkAreas.length > 0\">\n                <span *ngFor=\"let area of outlet.outletWorkAreas; let last = last\" class=\"work-area-tag\">\n                  {{area}}<span *ngIf=\"!last\">, </span>\n                </span>\n              </span>\n              <span *ngIf=\"!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;ICFhDC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAR9DH,EAAA,CAAAC,cAAA,cAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,qDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAClFX,EAAA,CAAAC,cAAA,cAA6B;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2FAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3FH,EAAA,CAAAC,cAAA,iBAA+D;IACnDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAY,UAAA,KAAAC,uCAAA,mBAAyD;IACzDb,EAAA,CAAAY,UAAA,KAAAE,uCAAA,mBAAwD;IAC1Dd,EAAA,CAAAG,YAAA,EAAS;;;;IAFAH,EAAA,CAAAe,SAAA,IAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAA2B;IAC3BnB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAAyB;;;;;IA0B9BnB,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAC,UAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnHvB,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAAwB,SAAA,cAAsE;;IACtExB,EAAA,CAAAY,UAAA,IAAAa,sCAAA,kBAAmH;IACrHzB,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAM,MAAA,aAAAN,UAAA,CAAAM,MAAA,YAAgG;IAGpJ5B,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,cAAAhB,EAAA,CAAA6B,WAAA,OAAAP,UAAA,CAAAQ,IAAA,GAAA9B,EAAA,CAAA+B,cAAA,CAAqC;IAC/B/B,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,SAAAM,UAAA,CAAAM,MAAA,cAAiC;;;;;IAMvE5B,EAAA,CAAAC,cAAA,cAAwE;IAGlED,EAAA,CAAAwB,SAAA,WAAa;IAGfxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAmClDH,EAAA,CAAAC,cAAA,cAAqD;IACzCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8FAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAexFH,EAAA,CAAAC,cAAA,WAAkH;IAChHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgC,kBAAA,MAAAC,OAAA,CAAAC,cAAA,CAAAC,2BAAA,CAAAC,IAAA,YACF;;;;;IACApC,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMPH,EAAA,CAAAC,cAAA,WAAkK;IAChKD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgC,kBAAA,MAAAK,OAAA,CAAAH,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAAH,IAAA,YACF;;;;;IACApC,EAAA,CAAAC,cAAA,WAAsK;IACpKD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAyBKH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADvCH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAY,UAAA,IAAA4B,sEAAA,mBAA6B;IACvCxC,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAe,SAAA,GAAQ;IAARf,EAAA,CAAAgC,kBAAA,MAAAS,QAAA,KAAQ;IAAOzC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAA0B,QAAA,CAAW;;;;;IAF9B1C,EAAA,CAAAC,cAAA,WAA0E;IACxED,EAAA,CAAAY,UAAA,IAAA+B,+DAAA,mBAEO;IACT3C,EAAA,CAAAG,YAAA,EAAO;;;;IAHkBH,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,YAAA4B,UAAA,CAAAC,eAAA,CAA2B;;;;;IAIpD7C,EAAA,CAAAC,cAAA,WAA6E;IAC3ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBbH,EAAA,CAAAC,cAAA,cAA4G;IAC5FD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAkC,wDAAA,mBAIO;IACP9C,EAAA,CAAAY,UAAA,KAAAmC,wDAAA,mBAEO;IACT/C,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBsBH,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgD,kBAAA,aAAAC,KAAA,YAAAL,UAAA,CAAAM,UAAA,KAAqC;IAG9ClD,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAoB,iBAAA,CAAAwB,UAAA,CAAAO,aAAA,CAAwB;IAKnCnD,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAgB,UAAA,SAAA4B,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAA1B,MAAA,KAAiE;IAKjEnB,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,UAAA,UAAA4B,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAA1B,MAAA,OAAoE;;;;;IAvDnFnB,EAAA,CAAAoD,uBAAA,GAAqC;IAEnCpD,EAAA,CAAAC,cAAA,cAA0C;IAC1BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAyC,iDAAA,mBAEO;IACPrD,EAAA,CAAAY,UAAA,KAAA0C,iDAAA,mBAEO;IACTtD,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAA2C,iDAAA,mBAEO;IACPvD,EAAA,CAAAY,UAAA,KAAA4C,iDAAA,mBAEO;IACTxD,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/FH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKhGH,EAAA,CAAAY,UAAA,KAAA6C,gDAAA,mBAmBM;IACRzD,EAAA,CAAA0D,qBAAA,EAAe;;;;IAvDW1D,EAAA,CAAAe,SAAA,IAA+B;IAA/Bf,EAAA,CAAAoB,iBAAA,CAAAuC,MAAA,CAAAzB,cAAA,CAAA0B,YAAA,CAA+B;IAK1C5D,EAAA,CAAAe,SAAA,GAAyG;IAAzGf,EAAA,CAAAgB,UAAA,SAAA2C,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAhB,MAAA,KAAyG;IAGzGnB,EAAA,CAAAe,SAAA,GAA4G;IAA5Gf,EAAA,CAAAgB,UAAA,UAAA2C,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAhB,MAAA,OAA4G;IAQ5GnB,EAAA,CAAAe,SAAA,GAAyJ;IAAzJf,EAAA,CAAAgB,UAAA,SAAA2C,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,IAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAApB,MAAA,KAAyJ;IAGzJnB,EAAA,CAAAe,SAAA,GAA6J;IAA7Jf,EAAA,CAAAgB,UAAA,UAAA2C,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,KAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAApB,MAAA,OAA6J;IAOlJnB,EAAA,CAAAe,SAAA,GAAkE;IAAlEf,EAAA,CAAAoB,iBAAA,EAAAuC,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,kBAAAF,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,CAAAC,cAAA,qBAAkE;IAIlE9D,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAoB,iBAAA,EAAAuC,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,kBAAAJ,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,CAAAC,cAAA,qBAAiE;IAK7BhE,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,YAAA2C,MAAA,CAAAzB,cAAA,CAAA+B,aAAA,CAAiC;;;ADpHrG,MAqBaC,gBAAgB;EAgB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IAlBT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAtD,QAAQ,GAAkB,EAAE;IAC5B,KAAAuD,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAzC,cAAc,GAAQ,IAAI;IAC1B,KAAA0C,mBAAmB,GAAY,KAAK;IACpC,KAAAC,YAAY,GAAY,KAAK;IAErB,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,sBAAsB,GAAwB,IAAI;IAyB1D;IACQ,KAAAC,cAAc,GAAG,KAAK;EApB3B;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAGD,OAAO,CAAC,UAAU,CAAC,CAACE,YAAY;MACpD,MAAMC,YAAY,GAAGH,OAAO,CAAC,UAAU,CAAC,CAACI,aAAa;MAEtDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,YAAY,EAAE,IAAI,EAAEF,WAAW,CAAC;MAErE;MACA,IAAIA,WAAW,IAAIE,YAAY,KAAKF,WAAW,EAAE;QAC/CI,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAEL,WAAW,CAAC;QACnG,IAAI,CAACH,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACS,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACzE,QAAQ,GAAG,EAAE;IAElBsE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAAClB,QAAQ,CAAC;IAEzE;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBiB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD;MACA,IAAI,CAACR,cAAc,GAAG,KAAK;MAE3B;MACA,MAAMW,sBAAsB,GAAG,wBAAwB,IAAI,CAACrB,QAAQ,EAAE;MACtE,IAAI,CAACK,mBAAmB,GAAGiB,YAAY,CAACC,OAAO,CAACF,sBAAsB,CAAC,KAAK,MAAM;MAElF;MACA,IAAI,CAACF,uBAAuB,EAAE;MAE9B;MACAK,UAAU,CAAC,MAAK;QACdP,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACvE,QAAQ,CAACC,MAAM,CAAC;QACrE,IAAI,IAAI,CAACD,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACyD,mBAAmB,GAAG,IAAI;UAC/BiB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;UACpDJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;SACvE,MAAM,IAAI,IAAI,CAACb,mBAAmB,EAAE;UACnC;UACAY,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;UACtF,IAAI,CAACQ,oBAAoB,EAAE;;QAG7B;QACA,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACX,MAAM;MACLX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACvE,QAAQ,GAAG,EAAE;;IAGpB;IACA,IAAI,CAAC4D,mBAAmB,GAAG,IAAI,CAACV,UAAU,CAACgC,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAC1E,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAI0E,OAAO,CAACC,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAC7B,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAII,OAAO,CAAC1E,MAAM,KAAK,KAAK,EAAE;QAC5B4D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEa,OAAO,CAACC,EAAE,EAAED,OAAO,CAACxE,IAAI,CAAC2E,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAEvF;QACA,IAAI,CAACH,OAAO,CAACxE,IAAI,CAAC4E,IAAI,EAAE,EAAE;UACxBlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC;;QAGF;QACA,MAAMkB,oBAAoB,GAAG,IAAI,CAACzF,QAAQ,CAAC0F,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC;QAE9E,IAAII,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAIL,OAAO,CAACxE,IAAI,KAAK,mBAAmB,EAAE;YACxC;YACA,IAAI,CAACZ,QAAQ,CAACyF,oBAAoB,CAAC,GAAGL,OAAO;YAC7Cd,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;;SAE1C,MAAM;UACL;UACA,MAAMqB,gBAAgB,GAAG,IAAI,CAAC5F,QAAQ,CAAC6F,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACjF,MAAM,KAAK,KAAK,IAAIiF,CAAC,CAAC/E,IAAI,KAAKwE,OAAO,CAACxE,IAAI,IAAI,CAAC+E,CAAC,CAAC/E,IAAI,CAACkF,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED;UACA,MAAMC,aAAa,GAAG,IAAI,CAAC/F,QAAQ,CAAC0F,SAAS,CAACC,CAAC,IAC7CA,CAAC,CAACjF,MAAM,KAAK,KAAK,IAAIiF,CAAC,CAAC/E,IAAI,KAAK,mBAAmB,IAAI+E,CAAC,CAACN,EAAE,KAAKD,OAAO,CAACC,EAAE,CAC5E;UAED,IAAIU,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB;YACA,IAAI,CAAC/F,QAAQ,CAAC+F,aAAa,CAAC,GAAGX,OAAO;YACtCd,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;WAC7D,MAAM,IAAI,CAACqB,gBAAgB,IAAIR,OAAO,CAACxE,IAAI,KAAK,mBAAmB,EAAE;YACpE;YACA,IAAI,CAACZ,QAAQ,CAACgG,IAAI,CAACZ,OAAO,CAAC;YAC3Bd,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;YAEpC;YACA,IAAI,CAACvE,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7F,SAAS,CAAC+F,OAAO,EAAE,GAAGD,CAAC,CAAC9F,SAAS,CAAC+F,OAAO,EAAE,CAAC;;;QAI/E;QACA,IAAI,CAAChB,OAAO,CAACxE,IAAI,CAACkF,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C,IAAI,CAACrC,oBAAoB,GAAG,KAAK;;QAGnC;QACA,IAAI2B,OAAO,CAACxE,IAAI,CAACkF,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC9F,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACvE,MAAMoG,WAAW,GAAG,IAAI,CAACrG,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;UAC3D,IAAIoG,WAAW,CAAC3F,MAAM,KAAK,MAAM,EAAE;YACjC;YACA4D,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;YAC3F;;;QAIJ;QACA,IAAI,CAACpB,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM,IAAIG,OAAO,CAAC1E,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAM4F,WAAW,GAAG,IAAI,CAACtG,QAAQ,CAACuG,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACjF,MAAM,KAAK,MAAM,IACnBiF,CAAC,CAAC/E,IAAI,KAAKwE,OAAO,CAACxE,IAAI,IACvB4F,IAAI,CAACC,GAAG,CAACd,CAAC,CAACtF,SAAS,CAAC+F,OAAO,EAAE,GAAGhB,OAAO,CAAC/E,SAAS,CAAC+F,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACE,WAAW,EAAE;UAChBhC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEa,OAAO,CAACxE,IAAI,CAAC;UACjD;UACA,IAAI,CAACZ,QAAQ,CAACgG,IAAI,CAACZ,OAAO,CAAC;UAE3B;UACA,IAAI,CAACpF,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7F,SAAS,CAAC+F,OAAO,EAAE,GAAGD,CAAC,CAAC9F,SAAS,CAAC+F,OAAO,EAAE,CAAC;SAC5E,MAAM;UACL9B,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEa,OAAO,CAACxE,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACuC,EAAE,CAAC6B,aAAa,EAAE;MAEvB;MACA,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED;IACA,IAAI,CAACnB,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAACwD,YAAY,CAACvB,SAAS,CACjEwB,IAAS,IAAI;MACZrC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoC,IAAI,CAAC;MAC1C,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,IAAID,IAAI,CAACA,IAAI,EAAE;QACxDrC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoC,IAAI,CAACA,IAAI,CAAC;QAClD,IAAI,CAAC3F,cAAc,GAAG2F,IAAI,CAACA,IAAI;QAC/BrC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACvD,cAAc,CAAC;QAC7E;QACA6D,UAAU,CAAC,MAAK;UACd,IAAI,CAAC1B,EAAE,CAAC6B,aAAa,EAAE;UACvBV,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACxE,CAAC,EAAE,CAAC,CAAC;QAEL;QACA;;IAEJ,CAAC,CACF;EACH;;EAEAsC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACjD,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACkD,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACjD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACiD,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAAChD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACgD,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC5D,UAAU,CAAC6D,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACzD,cAAc,CAACiC,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC6D,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC1D,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM0D,aAAa,GAAG,IAAI,CAAC5D,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACJ,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA;IAEA;IAEA;IACA,MAAMoC,WAAW,GAAgB;MAC/B/B,EAAE,EAAE,IAAI,CAACgC,UAAU,EAAE;MACrBzG,IAAI,EAAEuG,aAAa;MACnBzG,MAAM,EAAE,MAAM;MACdL,SAAS,EAAE,IAAIiH,IAAI;KACpB;IAED;IACA,MAAMhB,WAAW,GAAG,IAAI,CAACtG,QAAQ,CAACuG,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACjF,MAAM,KAAK,MAAM,IACnBiF,CAAC,CAAC/E,IAAI,KAAKuG,aAAa,CACzB;IAED,IAAI,CAACb,WAAW,EAAE;MAChB,IAAI,CAACtG,QAAQ,CAACgG,IAAI,CAACoB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC3D,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAAC/B,UAAU,CAAC8D,WAAW,CAAC,IAAI,CAAC3D,QAAQ,EAAE8D,aAAa,CAAC,CAAChC,SAAS,CAAC;MAClEoC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAAC/D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;QAEvB;QACA;MACF,CAAC;;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACflD,OAAO,CAACkD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACL,QAAQ,CAAC6D,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAC/D,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EAEA;EAEA;;;;EAIAyC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACb,WAAW,EAAE;;EAEtB;EAEA;;;EAGQ/B,cAAcA,CAAA;IACpB;IACA6C,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQd,UAAUA,CAAA;IAChB,OAAOb,IAAI,CAAC4B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGiB,IAAI,CAAC4B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGA+C,SAASA,CAACC,MAAc,EAAEnD,OAAoB;IAC5C,OAAOA,OAAO,CAACC,EAAE;EACnB;EAEA;;;EAGAb,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACnB,QAAQ,IAAI,IAAI,CAACU,cAAc,EAAE;MACzCO,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E;;IAGFD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAClB,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACU,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACP,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACxD,QAAQ,GAAG,EAAE;IAElB;IACA;IACA,IAAI,CAACkD,UAAU,CAACsB,uBAAuB,CAAC,IAAI,CAACnB,QAAQ,EAAE,KAAK,CAAC,CAAC8B,SAAS,CAAC;MACtEoC,IAAI,EAAGvH,QAAQ,IAAI;QACjBsE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEvE,QAAQ,CAACC,MAAM,CAAC;QAE1D;QACA,IAAID,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACnC;UACAD,QAAQ,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACrB,OAAO,IAAImB,IAAI,CAACpB,CAAC,CAAC7F,SAAS,CAAC,CAAC+F,OAAO,EAAE,GAAG,IAAIkB,IAAI,CAACnB,CAAC,CAAC9F,SAAS,CAAC,CAAC+F,OAAO,EAAE;UAC1E,CAAC,CAAC;UAEF9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAE3C;UACA,IAAI,CAACvE,QAAQ,GAAGA,QAAQ;UACxBsE,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACvE,QAAQ,CAACC,MAAM,CAAC;UAE9E;UACA,IAAI,CAACyD,mBAAmB,GAAG,IAAI;UAC/B,MAAMgB,sBAAsB,GAAG,wBAAwB,IAAI,CAACrB,QAAQ,EAAE;UACtEsB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM;UACL;UACA,IAAI,CAAC1E,QAAQ,GAAG,EAAE;UAClBsE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;QAG/D,IAAI,CAACf,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACflD,OAAO,CAACkD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAChE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACzD,QAAQ,GAAG,EAAE;QAElB,IAAI,CAACmD,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAwD,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAAChF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;IAEvBV,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAClB,QAAQ,CAAC;IAEvE;IAEA;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBiB,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAAClB,QAAQ,CAAC;MAC3F,IAAI,CAACH,UAAU,CAACsF,wBAAwB,CAAC,IAAI,CAACnF,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC8B,SAAS,CAAC;QAC5EoC,IAAI,EAAEA,CAAA,KAAK;UACTjD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAACvE,QAAQ,GAAG,EAAE;UAElB;UACA,IAAI,CAAC0D,mBAAmB,GAAG,KAAK;UAChC,MAAMgB,sBAAsB,GAAG,wBAAwB,IAAI,CAACrB,QAAQ,EAAE;UACtEsB,YAAY,CAAC8D,UAAU,CAAC/D,sBAAsB,CAAC;UAE/C;UACA,IAAI,CAAC1D,cAAc,GAAG,IAAI;UAE1B;UACA,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAE3B;UAEA,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC;QACDuC,KAAK,EAAGA,KAAK,IAAI;UACflD,OAAO,CAACkD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAACxH,QAAQ,GAAG,EAAE;UAElB;UACA,IAAI,CAAC0D,mBAAmB,GAAG,KAAK;UAChC,MAAMgB,sBAAsB,GAAG,wBAAwB,IAAI,CAACrB,QAAQ,EAAE;UACtEsB,YAAY,CAAC8D,UAAU,CAAC/D,sBAAsB,CAAC;UAE/C;UACA,IAAI,CAAC1D,cAAc,GAAG,IAAI;UAE1B;UACA,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAE3B;UACA,IAAI,CAACP,YAAY,GAAG,KAAK;UACzB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACjF,QAAQ,GAAG,CACd;QACEqF,EAAE,EAAE,IAAI,CAACgC,UAAU,EAAE;QACrBzG,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAIiH,IAAI;OACpB,CACF;MAED,IAAI,CAACvD,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACP,YAAY,GAAG,KAAK;MACzB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;;EAEzB;EAEA;EAEA;;;EAGAxF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC4D,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAAC6D,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;QACzEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAACxD,mBAAmB,GAAG,IAAI;IAE/B;IACA,MAAMgB,sBAAsB,GAAG,wBAAwB,IAAI,CAACrB,QAAQ,EAAE;IACtEsB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;IAEpD;IACA;IACA,IAAI,CAACK,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;;EAIQF,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAC1B,QAAQ,EAAE;MAClB;;IAGFiB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAAClB,QAAQ,CAAC;IAE9E;IACA,IAAI,CAACH,UAAU,CAAC8D,WAAW,CAAC,IAAI,CAAC3D,QAAQ,EAAE,2BAA2B,CAAC,CAAC8B,SAAS,CAAC;MAChFoC,IAAI,EAAEA,CAAA,KAAK;QACTjD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D;MACF,CAAC;;MACDiD,KAAK,EAAGA,KAAK,IAAI;QACflD,OAAO,CAACkD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACpE,QAAQ,CAAC6D,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EAEA;;;EAGAwB,aAAaA,CAAA;IACXpE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACvE,QAAQ,CAACC,MAAM,CAAC;IAC5D,IAAI,CAACD,QAAQ,CAAC2I,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAI;MACnCvE,OAAO,CAACC,GAAG,CAAC,WAAWsE,KAAK,GAAG,EAAED,GAAG,CAACvD,EAAE,EAAEuD,GAAG,CAAClI,MAAM,EAAEkI,GAAG,CAAChI,IAAI,CAAC2E,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;IACzF,CAAC,CAAC;IAEF;IACA,IAAI,CAACxB,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACS,uBAAuB,EAAE;EAChC;EAEA;;;EAGAsE,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACzF,QAAQ,IAAI,IAAI,CAACM,YAAY,EAAE;MACvC;;IAGF,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,EAAE,CAAC6B,aAAa,EAAE;IAEvBV,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAClB,QAAQ,CAAC;IAEpE,IAAI,CAACH,UAAU,CAAC6F,mBAAmB,CAAC,IAAI,CAAC1F,QAAQ,CAAC,CAAC8B,SAAS,CAAC;MAC3DoC,IAAI,EAAGZ,IAAI,IAAI;QACbrC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoC,IAAI,CAAC;QAC/C,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC3F,cAAc,GAAG2F,IAAI;UAC1B,IAAI,CAACvD,QAAQ,CAAC6D,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;YACpEC,QAAQ,EAAE,IAAI;YACd8B,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACL,IAAI,CAAC5F,QAAQ,CAAC6D,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;;QAEJ,IAAI,CAACvD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACflD,OAAO,CAACkD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACpE,QAAQ,CAAC6D,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAC/DC,QAAQ,EAAE,IAAI;UACd8B,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAAC9D,SAAS,CAAC,MAAK;UAC3B,IAAI,CAAC2D,qBAAqB,EAAE;QAC9B,CAAC,CAAC;QACF,IAAI,CAACnF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACR,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;;;uBAjmBWhC,gBAAgB,EAAAlE,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAApK,EAAA,CAAAuK,iBAAA,GAAAvK,EAAA,CAAAoK,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBvG,gBAAgB;MAAAwG,SAAA;MAAAC,MAAA;QAAApG,QAAA;QAAAC,UAAA;MAAA;MAAAoG,UAAA;MAAAC,QAAA,GAAA7K,EAAA,CAAA8K,oBAAA,EAAA9K,EAAA,CAAA+K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtC7BrL,EAAA,CAAAC,cAAA,aAAyB;UAGvBD,EAAA,CAAAY,UAAA,IAAA2K,+BAAA,kBAWM;UAENvL,EAAA,CAAAC,cAAA,aAAyB;UAEOD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UAEwBD,EAAA,CAAAI,UAAA,mBAAAoL,mDAAA;YAAA,OAASF,GAAA,CAAA5B,wBAAA,EAA0B;UAAA,EAAC;UAClF1J,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE5BH,EAAA,CAAAC,cAAA,iBAAgG;UAA5CD,EAAA,CAAAI,UAAA,mBAAAqL,mDAAA;YAAA,OAASH,GAAA,CAAA1B,aAAA,EAAe;UAAA,EAAC;UAC3E5J,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKrCH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAY,UAAA,KAAA8K,gCAAA,kBAOM;UAGN1L,EAAA,CAAAY,UAAA,KAAA+K,gCAAA,kBASM;UACR3L,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAI,UAAA,2BAAAwL,0DAAAC,MAAA;YAAA,OAAAP,GAAA,CAAA7G,cAAA,GAAAoH,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBP,GAAA,CAAA3C,UAAA,CAAAkD,MAAA,CAAkB;UAAA,EAFD;UADnC7L,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAI,UAAA,mBAAA2L,mDAAA;YAAA,OAAST,GAAA,CAAApD,WAAA,EAAa;UAAA,EAAC;UAC1DlI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7BH,EAAA,CAAAC,cAAA,eAAmC;UAGnBD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjCH,EAAA,CAAAC,cAAA,eAA0B;UACuBD,EAAA,CAAAI,UAAA,mBAAA4L,mDAAA;YAAA,OAASV,GAAA,CAAAtB,qBAAA,EAAuB;UAAA,EAAC;UAC9EhK,EAAA,CAAAC,cAAA,gBAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKlEH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAY,UAAA,KAAAqL,gCAAA,kBAIM;UAGNjM,EAAA,CAAAY,UAAA,KAAAsL,yCAAA,4BA6De;UACjBlM,EAAA,CAAAG,YAAA,EAAM;;;UAvJmBH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,UAAAsK,GAAA,CAAA1G,mBAAA,CAA0B;UA8B1B5E,EAAA,CAAAe,SAAA,IAAa;UAAbf,EAAA,CAAAgB,UAAA,YAAAsK,GAAA,CAAApK,QAAA,CAAa,iBAAAoK,GAAA,CAAA9B,SAAA;UAUhCxJ,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAAsK,GAAA,CAAA3G,oBAAA,CAA0B;UAevB3E,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,YAAAsK,GAAA,CAAA7G,cAAA,CAA4B,aAAA6G,GAAA,CAAA5G,YAAA;UAKwB1E,EAAA,CAAAe,SAAA,GAAmD;UAAnDf,EAAA,CAAAgB,UAAA,cAAAsK,GAAA,CAAA7G,cAAA,CAAAiC,IAAA,MAAA4E,GAAA,CAAA5G,YAAA,CAAmD;UAcU1E,EAAA,CAAAe,SAAA,IAAyB;UAAzBf,EAAA,CAAAgB,UAAA,aAAAsK,GAAA,CAAAzG,YAAA,CAAyB;UACnI7E,EAAA,CAAAe,SAAA,GAA+B;UAA/Bf,EAAA,CAAAmM,WAAA,aAAAb,GAAA,CAAAzG,YAAA,CAA+B;UAOf7E,EAAA,CAAAe,SAAA,GAAqB;UAArBf,EAAA,CAAAgB,UAAA,UAAAsK,GAAA,CAAApJ,cAAA,CAAqB;UAOpClC,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAAgB,UAAA,SAAAsK,GAAA,CAAApJ,cAAA,CAAoB;;;qBDvErC7C,YAAY,EAAA+M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZlN,WAAW,EAAAmN,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXrN,mBAAmB,EACnBC,eAAe,EAAAqN,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACfvN,aAAa,EACbC,kBAAkB,EAAAuN,EAAA,CAAAC,YAAA,EAClBvN,aAAa,EAAAwN,EAAA,CAAAC,OAAA,EACbxN,cAAc,EAAAyN,EAAA,CAAAC,QAAA,EACdzN,wBAAwB,EACxBC,gBAAgB,EAAAyN,EAAA,CAAAC,UAAA,EAEhBzN,YAAY;MAAA0N,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMHxJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}