{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/sidenav\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/button\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"ngx-markdown\";\nconst _c0 = [\"chatContainer\"];\nfunction SmartDashboardComponent_mat_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", period_r6, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_27_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const category_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onFilterChange(\"categories\", category_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", category_r7.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r7.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_33_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const region_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onFilterChange(\"regions\", region_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", region_r10.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r10.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_checkbox_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27);\n    i0.ɵɵlistener(\"change\", function SmartDashboardComponent_mat_checkbox_39_Template_mat_checkbox_change_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const metric_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onFilterChange(\"keyMetrics\", metric_r13));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const metric_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"checked\", metric_r13.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", metric_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"psychology\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h1\");\n    i0.ɵɵtext(6, \"AI-Powered Data Analysis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Configure your report filters and generate a dashboard to start analyzing your data with AI.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 31)(10, \"div\", 32)(11, \"div\", 33)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"h3\");\n    i0.ɵɵtext(15, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 32)(17, \"div\", 33)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"h3\");\n    i0.ɵɵtext(21, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 32)(23, \"div\", 33)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"h3\");\n    i0.ɵɵtext(27, \"Natural Language Queries\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 32)(29, \"div\", 33)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"h3\");\n    i0.ɵɵtext(33, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 34)(35, \"div\", 35)(36, \"div\", 36)(37, \"mat-icon\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"Select your filters (categories/regions)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 35)(42, \"div\", 36)(43, \"mat-icon\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46, \"Ask the AI assistant what you'd like to visualize\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(35);\n    i0.ɵɵclassProp(\"completed\", ctx_r4.getActiveFiltersCount() > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getActiveFiltersCount() > 0 ? \"check_circle\" : \"radio_button_unchecked\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"completed\", ctx_r4.chatMessages.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.chatMessages.length > 0 ? \"check_circle\" : \"radio_button_unchecked\");\n  }\n}\nfunction SmartDashboardComponent_div_53_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵtext(2, \"Chart visualization would appear here\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"ai-message\": a1\n  };\n};\nfunction SmartDashboardComponent_div_53_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 44)(3, \"span\", 45)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 47);\n    i0.ɵɵelement(11, \"markdown\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SmartDashboardComponent_div_53_div_3_div_12_Template, 3, 0, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, message_r19.type === \"human\", message_r19.type === \"ai\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(message_r19.type === \"human\" ? \"person\" : \"psychology\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r19.type === \"human\" ? \"You\" : \"AI Assistant\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 6, message_r19.created_at, \"short\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"data\", message_r19.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r19.has_visualization && message_r19.chart_data);\n  }\n}\nfunction SmartDashboardComponent_div_53_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"mat-spinner\", 53);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"DIGI is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37, 38)(2, \"div\", 39);\n    i0.ɵɵtemplate(3, SmartDashboardComponent_div_53_div_3_Template, 13, 12, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_53_div_4_Template, 4, 0, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.chatMessages)(\"ngForTrackBy\", ctx_r5.trackByIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading);\n  }\n}\nclass SmartDashboardComponent {\n  constructor(cd, smartDashboardService, authService, snackBar) {\n    this.cd = cd;\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    // Form Controls\n    this.messageControl = new FormControl('');\n    this.reportTypeControl = new FormControl('GRN Report');\n    // State\n    this.isLoading = false;\n    this.currentSession = null;\n    this.chatMessages = [];\n    this.destroy$ = new Subject();\n    // Smart Filters\n    this.smartFilters = {\n      timePeriod: 'Last 30 days',\n      categories: [{\n        value: 'ecommerce',\n        label: 'E-commerce',\n        checked: false\n      }, {\n        value: 'saas',\n        label: 'SaaS',\n        checked: false\n      }, {\n        value: 'mobile-apps',\n        label: 'Mobile Apps',\n        checked: false\n      }, {\n        value: 'marketing',\n        label: 'Marketing',\n        checked: false\n      }, {\n        value: 'support',\n        label: 'Support',\n        checked: false\n      }],\n      regions: [{\n        value: 'north-america',\n        label: 'North America',\n        checked: false\n      }, {\n        value: 'europe',\n        label: 'Europe',\n        checked: true\n      }, {\n        value: 'asia-pacific',\n        label: 'Asia Pacific',\n        checked: false\n      }, {\n        value: 'latin-america',\n        label: 'Latin America',\n        checked: false\n      }, {\n        value: 'africa',\n        label: 'Africa',\n        checked: false\n      }],\n      keyMetrics: [{\n        value: 'revenue',\n        label: 'Revenue',\n        checked: false\n      }, {\n        value: 'users',\n        label: 'Users',\n        checked: false\n      }, {\n        value: 'conversions',\n        label: 'Conversions',\n        checked: false\n      }, {\n        value: 'engagement',\n        label: 'Engagement',\n        checked: false\n      }, {\n        value: 'retention',\n        label: 'Retention',\n        checked: false\n      }]\n    };\n    // Time period options\n    this.timePeriodOptions = ['Last 7 days', 'Last 30 days', 'Last 90 days', 'Last 6 months', 'Last year', 'Custom range'];\n    // Example queries for quick actions\n    this.exampleQueries = ['Show me revenue trends by region', 'Compare user engagement across categories', 'What are the top performing metrics?', 'Create a conversion funnel analysis'];\n    this.user = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    // Initialize component\n    this.setupInitialState();\n    // Subscribe to current session changes\n    this.smartDashboardService.currentSession$.pipe(takeUntil(this.destroy$)).subscribe(session => {\n      this.currentSession = session;\n      this.cd.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupInitialState() {\n    // Set Europe as selected by default (as shown in the image)\n    const europeFilter = this.smartFilters.regions.find(r => r.value === 'europe');\n    if (europeFilter) {\n      europeFilter.checked = true;\n    }\n  }\n  // Filter management\n  getActiveFiltersCount() {\n    let count = 0;\n    count += this.smartFilters.categories.filter(c => c.checked).length;\n    count += this.smartFilters.regions.filter(r => r.checked).length;\n    count += this.smartFilters.keyMetrics.filter(m => m.checked).length;\n    return count;\n  }\n  onFilterChange(filterType, option) {\n    if (filterType !== 'timePeriod') {\n      option.checked = !option.checked;\n      this.cd.detectChanges();\n    }\n  }\n  onTimePeriodChange(period) {\n    this.smartFilters.timePeriod = period;\n  }\n  // Chat functionality\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message || this.isLoading) return;\n    // Add user message\n    this.chatMessages.push({\n      type: 'human',\n      content: message,\n      created_at: new Date()\n    });\n    this.messageControl.setValue('');\n    this.isLoading = true;\n    // Simulate AI response (replace with actual API call)\n    setTimeout(() => {\n      this.chatMessages.push({\n        type: 'ai',\n        content: 'I understand you want to analyze the data. Let me process your request and create the appropriate visualization.',\n        created_at: new Date(),\n        has_visualization: false\n      });\n      this.isLoading = false;\n      this.scrollToBottom();\n      this.cd.detectChanges();\n    }, 1500);\n    this.scrollToBottom();\n  }\n  sendExampleQuery(query) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.chatContainer) {\n        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;\n      }\n    }, 100);\n  }\n  // Utility methods\n  trackByIndex(index, _item) {\n    return index;\n  }\n  onFiltersApplied(filters) {\n    // Handle filter application\n    console.log('Filters applied:', filters);\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 11,\n      consts: [[1, \"dashboard-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"filters-sidenav\"], [1, \"filters-content\"], [1, \"filters-header\"], [1, \"filters-title\"], [1, \"filter-icon\"], [1, \"filter-count\"], [1, \"expand-icon\"], [1, \"filter-section\"], [1, \"filter-label\"], [1, \"section-icon\"], [\"appearance\", \"outline\", 1, \"time-period-select\"], [3, \"value\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"checkbox-group\"], [\"class\", \"filter-checkbox\", 3, \"checked\", \"change\", 4, \"ngFor\", \"ngForOf\"], [1, \"main-content\"], [1, \"ai-input-section\"], [1, \"ai-input-container\"], [\"color\", \"primary\", 1, \"ai-icon\"], [1, \"ai-label\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask AI to create a chart...\", 3, \"formControl\", \"disabled\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-button\", 3, \"disabled\", \"click\"], [\"class\", \"welcome-state\", 4, \"ngIf\"], [\"class\", \"chat-messages\", 4, \"ngIf\"], [3, \"value\"], [1, \"filter-checkbox\", 3, \"checked\", \"change\"], [1, \"welcome-state\"], [1, \"welcome-content\"], [1, \"ai-brain-icon\"], [1, \"features-grid\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"steps-section\"], [1, \"step-item\"], [1, \"step-icon\"], [1, \"chat-messages\"], [\"chatContainer\", \"\"], [1, \"messages-list\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"loading-message\", 4, \"ngIf\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-header\"], [1, \"message-sender\"], [1, \"message-time\"], [1, \"message-text\"], [3, \"data\"], [\"class\", \"message-chart\", 4, \"ngIf\"], [1, \"message-chart\"], [1, \"chart-placeholder\"], [1, \"loading-message\"], [\"diameter\", \"20\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-sidenav-container\", 0)(1, \"mat-sidenav\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-icon\", 7);\n          i0.ɵɵtext(12, \"expand_less\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"mat-icon\", 10);\n          i0.ɵɵtext(16, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"Time Period\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 11)(20, \"mat-select\", 12);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_20_listener($event) {\n            return ctx.onTimePeriodChange($event.value);\n          });\n          i0.ɵɵtemplate(21, SmartDashboardComponent_mat_option_21_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 9)(24, \"span\");\n          i0.ɵɵtext(25, \"Categories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14);\n          i0.ɵɵtemplate(27, SmartDashboardComponent_mat_checkbox_27_Template, 2, 2, \"mat-checkbox\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 8)(29, \"div\", 9)(30, \"span\");\n          i0.ɵɵtext(31, \"Regions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 14);\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_checkbox_33_Template, 2, 2, \"mat-checkbox\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 8)(35, \"div\", 9)(36, \"span\");\n          i0.ɵɵtext(37, \"Key Metrics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 14);\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_checkbox_39_Template, 2, 2, \"mat-checkbox\", 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"mat-sidenav-content\", 16)(41, \"div\", 17)(42, \"div\", 18)(43, \"mat-icon\", 19);\n          i0.ɵɵtext(44, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 20);\n          i0.ɵɵtext(46, \"Ask AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 21)(48, \"input\", 22);\n          i0.ɵɵlistener(\"keydown\", function SmartDashboardComponent_Template_input_keydown_48_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_49_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(52, SmartDashboardComponent_div_52_Template, 47, 6, \"div\", 24);\n          i0.ɵɵtemplate(53, SmartDashboardComponent_div_53_Template, 5, 3, \"div\", 25);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.getActiveFiltersCount());\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.smartFilters.timePeriod);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.timePeriodOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.categories);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.regions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.smartFilters.keyMetrics);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.messageControl)(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !(ctx.messageControl.value == null ? null : ctx.messageControl.value.trim()) || ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentSession && ctx.chatMessages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.chatMessages.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, ReactiveFormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, MatSidenavModule, i6.MatSidenav, i6.MatSidenavContainer, i6.MatSidenavContent, MatCardModule, MatIconModule, i7.MatIcon, MatChipsModule, MatExpansionModule, MatFormFieldModule, i8.MatFormField, MatSelectModule, i9.MatSelect, i10.MatOption, MatCheckboxModule, i11.MatCheckbox, MatButtonModule, i12.MatIconButton, MatInputModule, i13.MatInput, MatTabsModule, MatProgressSpinnerModule, i14.MatProgressSpinner, MatSnackBarModule, MarkdownModule, i15.MarkdownComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  font-family: \\\"Google Sans\\\", Roboto, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif !important;\\n}\\n[_nghost-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: auto !important;\\n}\\n[_nghost-%COMP%]   button.mat-mdc-raised-button[_ngcontent-%COMP%] {\\n  height: auto !important;\\n  line-height: normal !important;\\n  font-size: inherit !important;\\n  padding: 8px 16px !important;\\n}\\n[_nghost-%COMP%]   .mat-mdc-icon-button[_ngcontent-%COMP%] {\\n  width: auto !important;\\n  height: auto !important;\\n  padding: 8px !important;\\n}\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  background-color: #f5f6fa;\\n  font-family: \\\"Google Sans\\\", Roboto, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif !important;\\n}\\n\\n.filters-sidenav[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background-color: #ffffff;\\n  border-right: 1px solid #e1e5e9;\\n  box-shadow: 0 0 8px rgba(0, 0, 0, 0.04);\\n  overflow: hidden;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100vh;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px 12px 20px;\\n  border-bottom: 1px solid #e1e5e9;\\n  background-color: #ffffff;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #3c4043;\\n  cursor: pointer;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #4285f4;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #4285f4;\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 500;\\n  min-width: 18px;\\n  height: 18px;\\n  line-height: 18px;\\n  text-align: center;\\n  border-radius: 9px;\\n  padding: 0 5px;\\n  margin-left: auto;\\n  margin-right: 8px;\\n}\\n.filters-title[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #5f6368;\\n}\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background-color: #ffffff;\\n  border-bottom: 1px solid #f1f3f4;\\n}\\n.filter-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.filter-section[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #3c4043;\\n  margin-bottom: 12px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #4285f4;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 32px !important;\\n  background-color: #ffffff !important;\\n  border: 1px solid #dadce0 !important;\\n  border-radius: 4px !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  font-size: 13px !important;\\n  color: #3c4043 !important;\\n  padding: 0 12px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-select-arrow {\\n  color: #5f6368 !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .time-period-select[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 0 !important;\\n  border: none !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%] {\\n  font-size: 13px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mdc-checkbox {\\n  padding: 4px !important;\\n  --mdc-checkbox-unselected-icon-color: #dadce0 !important;\\n  --mdc-checkbox-selected-icon-color: #4285f4 !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  color: #3c4043 !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox-touch-target {\\n  width: 32px !important;\\n  height: 32px !important;\\n}\\n.filter-section[_ngcontent-%COMP%]   .checkbox-group[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field > label {\\n  padding-left: 6px !important;\\n  font-size: 13px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  background-color: #f5f6fa;\\n  padding: 12px 16px;\\n  overflow-y: auto;\\n  height: 100vh;\\n}\\n\\n.ai-input-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  margin-bottom: 12px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e8eaed;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 16px !important;\\n  color: #4285f4 !important;\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-label[_ngcontent-%COMP%] {\\n  font-size: 12px !important;\\n  font-weight: 500 !important;\\n  color: #4285f4 !important;\\n  white-space: nowrap !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1 !important;\\n  width: auto !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field {\\n  width: 100% !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 28px !important;\\n  background-color: #f8f9fa !important;\\n  border: 1px solid #dadce0 !important;\\n  border-radius: 14px !important;\\n  padding: 0 !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 0 12px !important;\\n  border: none !important;\\n  min-height: 26px !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {\\n  padding: 0 !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n  font-size: 12px !important;\\n  height: 26px !important;\\n  line-height: 26px !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     input {\\n  font-size: 12px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]     input::placeholder {\\n  color: #5f6368 !important;\\n  font-size: 12px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%] {\\n  width: 28px !important;\\n  height: 28px !important;\\n  min-width: 28px !important;\\n  background-color: #4285f4 !important;\\n  color: white !important;\\n  border-radius: 50% !important;\\n  padding: 0 !important;\\n  line-height: 1 !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px !important;\\n  width: 14px !important;\\n  height: 14px !important;\\n}\\n.ai-input-container[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]:disabled {\\n  background-color: #f1f3f4 !important;\\n  color: #9aa0a6 !important;\\n}\\n\\n.welcome-state[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 6px;\\n  padding: 32px 24px;\\n  text-align: center;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e8eaed;\\n}\\n\\n.welcome-content[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n  margin: 0 auto;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .ai-brain-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  color: #4285f4 !important;\\n  background-color: #e8f0fe !important;\\n  border-radius: 50% !important;\\n  padding: 10px !important;\\n  display: inline-flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n}\\n.welcome-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 20px !important;\\n  font-weight: 500 !important;\\n  color: #202124 !important;\\n  margin-bottom: 8px !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 13px !important;\\n  color: #5f6368 !important;\\n  margin-bottom: 24px !important;\\n  line-height: 1.4 !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 12px;\\n  margin-bottom: 24px;\\n}\\n@media (max-width: 768px) {\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  padding: 16px 12px;\\n  text-align: center;\\n  background-color: #ffffff;\\n  border-radius: 6px;\\n  border: 1px solid #e8eaed;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-color: #4285f4;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px !important;\\n  width: 24px !important;\\n  height: 24px !important;\\n  color: #4285f4 !important;\\n}\\n.feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 12px !important;\\n  font-weight: 500 !important;\\n  color: #3c4043 !important;\\n  margin: 0 !important;\\n  line-height: 1.2 !important;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n\\n.steps-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  text-align: left;\\n  max-width: 360px;\\n  margin: 0 auto;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 12px !important;\\n  color: #5f6368 !important;\\n  padding: 8px 12px;\\n  border-radius: 16px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e8eaed;\\n  font-family: \\\"Google Sans\\\", Roboto, sans-serif !important;\\n}\\n.step-item.completed[_ngcontent-%COMP%] {\\n  color: #137333 !important;\\n  background-color: #e6f4ea !important;\\n  border-color: #c8e6c9 !important;\\n}\\n.step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #137333 !important;\\n}\\n.step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px !important;\\n  width: 14px !important;\\n  height: 14px !important;\\n  color: #dadce0 !important;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  padding: 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e8eaed;\\n  max-height: calc(100vh - 200px);\\n  overflow-y: auto;\\n}\\n\\n.loading-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  color: #5f6368;\\n  font-style: italic;\\n  font-size: 13px;\\n  justify-content: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "ReactiveFormsModule", "MatSidenavModule", "MatCardModule", "MatIconModule", "MatChipsModule", "MatExpansionModule", "MatFormFieldModule", "MatSelectModule", "MatCheckboxModule", "MatButtonModule", "MatInputModule", "MatTabsModule", "MatProgressSpinnerModule", "MarkdownModule", "MatSnackBarModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "period_r6", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵlistener", "SmartDashboardComponent_mat_checkbox_27_Template_mat_checkbox_change_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "category_r7", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "checked", "label", "SmartDashboardComponent_mat_checkbox_33_Template_mat_checkbox_change_0_listener", "_r12", "region_r10", "ctx_r11", "SmartDashboardComponent_mat_checkbox_39_Template_mat_checkbox_change_0_listener", "_r15", "metric_r13", "ctx_r14", "ɵɵclassProp", "ctx_r4", "getActiveFiltersCount", "ɵɵtextInterpolate", "chatMessages", "length", "ɵɵelement", "ɵɵtemplate", "SmartDashboardComponent_div_53_div_3_div_12_Template", "ɵɵpureFunction2", "_c1", "message_r19", "type", "ɵɵpipeBind2", "created_at", "content", "has_visualization", "chart_data", "SmartDashboardComponent_div_53_div_3_Template", "SmartDashboardComponent_div_53_div_4_Template", "ctx_r5", "trackByIndex", "isLoading", "SmartDashboardComponent", "constructor", "cd", "smartDashboardService", "authService", "snackBar", "messageControl", "reportTypeControl", "currentSession", "destroy$", "smartFilters", "timePeriod", "categories", "value", "regions", "keyMetrics", "timePeriodOptions", "exampleQueries", "user", "getCurrentUser", "ngOnInit", "setupInitialState", "currentSession$", "pipe", "subscribe", "session", "detectChanges", "ngOnDestroy", "next", "complete", "europeFilter", "find", "r", "count", "filter", "c", "m", "filterType", "option", "onTimePeriodChange", "period", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "sendMessage", "message", "trim", "push", "Date", "setValue", "setTimeout", "scrollToBottom", "send<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "query", "chatContainer", "nativeElement", "scrollTop", "scrollHeight", "index", "_item", "onFiltersApplied", "filters", "console", "log", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "MatSnackBar", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_selectionChange_20_listener", "$event", "SmartDashboardComponent_mat_option_21_Template", "SmartDashboardComponent_mat_checkbox_27_Template", "SmartDashboardComponent_mat_checkbox_33_Template", "SmartDashboardComponent_mat_checkbox_39_Template", "SmartDashboardComponent_Template_input_keydown_48_listener", "SmartDashboardComponent_Template_button_click_49_listener", "SmartDashboardComponent_div_52_Template", "SmartDashboardComponent_div_53_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "i6", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i7", "MatIcon", "i8", "MatFormField", "i9", "MatSelect", "i10", "MatOption", "i11", "MatCheckbox", "i12", "MatIconButton", "i13", "MatInput", "i14", "MatProgressSpinner", "i15", "MarkdownComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { Subject, takeUntil } from 'rxjs';\nimport {\n  SmartDashboardService,\n  SmartFilters,\n  ChatMessage,\n  DashboardSession,\n  FilterOption\n} from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatSidenavModule,\n    MatCardModule,\n    MatIconModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatButtonModule,\n    MatInputModule,\n    MatTabsModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MarkdownModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  @ViewChild('chatContainer') chatContainer!: ElementRef;\n\n  // Form Controls\n  messageControl = new FormControl('');\n  reportTypeControl = new FormControl('GRN Report');\n\n  // State\n  isLoading = false;\n  currentSession: DashboardSession | null = null;\n  chatMessages: ChatMessage[] = [];\n  private destroy$ = new Subject<void>();\n  private user: any;\n\n  // Smart Filters\n  smartFilters: SmartFilters = {\n    timePeriod: 'Last 30 days',\n    categories: [\n      { value: 'ecommerce', label: 'E-commerce', checked: false },\n      { value: 'saas', label: 'SaaS', checked: false },\n      { value: 'mobile-apps', label: 'Mobile Apps', checked: false },\n      { value: 'marketing', label: 'Marketing', checked: false },\n      { value: 'support', label: 'Support', checked: false }\n    ],\n    regions: [\n      { value: 'north-america', label: 'North America', checked: false },\n      { value: 'europe', label: 'Europe', checked: true },\n      { value: 'asia-pacific', label: 'Asia Pacific', checked: false },\n      { value: 'latin-america', label: 'Latin America', checked: false },\n      { value: 'africa', label: 'Africa', checked: false }\n    ],\n    keyMetrics: [\n      { value: 'revenue', label: 'Revenue', checked: false },\n      { value: 'users', label: 'Users', checked: false },\n      { value: 'conversions', label: 'Conversions', checked: false },\n      { value: 'engagement', label: 'Engagement', checked: false },\n      { value: 'retention', label: 'Retention', checked: false }\n    ]\n  };\n\n  // Time period options\n  timePeriodOptions = [\n    'Last 7 days',\n    'Last 30 days',\n    'Last 90 days',\n    'Last 6 months',\n    'Last year',\n    'Custom range'\n  ];\n\n  // Example queries for quick actions\n  exampleQueries = [\n    'Show me revenue trends by region',\n    'Compare user engagement across categories',\n    'What are the top performing metrics?',\n    'Create a conversion funnel analysis'\n  ];\n\n  constructor(\n    private cd: ChangeDetectorRef,\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.user = this.authService.getCurrentUser();\n  }\n\n  ngOnInit() {\n    // Initialize component\n    this.setupInitialState();\n\n    // Subscribe to current session changes\n    this.smartDashboardService.currentSession$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(session => {\n        this.currentSession = session;\n        this.cd.detectChanges();\n      });\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private setupInitialState() {\n    // Set Europe as selected by default (as shown in the image)\n    const europeFilter = this.smartFilters.regions.find(r => r.value === 'europe');\n    if (europeFilter) {\n      europeFilter.checked = true;\n    }\n  }\n\n  // Filter management\n  getActiveFiltersCount(): number {\n    let count = 0;\n    count += this.smartFilters.categories.filter(c => c.checked).length;\n    count += this.smartFilters.regions.filter(r => r.checked).length;\n    count += this.smartFilters.keyMetrics.filter(m => m.checked).length;\n    return count;\n  }\n\n  onFilterChange(filterType: keyof SmartFilters, option: FilterOption) {\n    if (filterType !== 'timePeriod') {\n      option.checked = !option.checked;\n      this.cd.detectChanges();\n    }\n  }\n\n  onTimePeriodChange(period: string) {\n    this.smartFilters.timePeriod = period;\n  }\n\n  // Chat functionality\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message || this.isLoading) return;\n\n    // Add user message\n    this.chatMessages.push({\n      type: 'human',\n      content: message,\n      created_at: new Date()\n    });\n\n    this.messageControl.setValue('');\n    this.isLoading = true;\n\n    // Simulate AI response (replace with actual API call)\n    setTimeout(() => {\n      this.chatMessages.push({\n        type: 'ai',\n        content: 'I understand you want to analyze the data. Let me process your request and create the appropriate visualization.',\n        created_at: new Date(),\n        has_visualization: false\n      });\n      this.isLoading = false;\n      this.scrollToBottom();\n      this.cd.detectChanges();\n    }, 1500);\n\n    this.scrollToBottom();\n  }\n\n  sendExampleQuery(query: string) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.chatContainer) {\n        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // Utility methods\n  trackByIndex(index: number, _item: any): number {\n    return index;\n  }\n\n  onFiltersApplied(filters: any) {\n    // Handle filter application\n    console.log('Filters applied:', filters);\n  }\n}\n", "<mat-sidenav-container class=\"dashboard-container\">\n  <!-- Left Sidebar - Smart Filters -->\n  <mat-sidenav mode=\"side\" opened=\"true\" class=\"filters-sidenav\">\n    <div class=\"filters-content\">\n      <!-- Smart Filters Header -->\n      <div class=\"filters-header\">\n        <div class=\"filters-title\">\n          <mat-icon class=\"filter-icon\">tune</mat-icon>\n          <span>Smart Filters</span>\n          <span class=\"filter-count\">{{ getActiveFiltersCount() }}</span>\n          <mat-icon class=\"expand-icon\">expand_less</mat-icon>\n        </div>\n      </div>\n\n      <!-- Time Period Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <mat-icon class=\"section-icon\">schedule</mat-icon>\n          <span>Time Period</span>\n        </div>\n        <mat-form-field appearance=\"outline\" class=\"time-period-select\">\n          <mat-select\n            [value]=\"smartFilters.timePeriod\"\n            (selectionChange)=\"onTimePeriodChange($event.value)\">\n            <mat-option *ngFor=\"let period of timePeriodOptions\" [value]=\"period\">\n              {{ period }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- Categories Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Categories</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox\n            *ngFor=\"let category of smartFilters.categories\"\n            [checked]=\"category.checked\"\n            (change)=\"onFilterChange('categories', category)\"\n            class=\"filter-checkbox\">\n            {{ category.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Regions Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Regions</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox\n            *ngFor=\"let region of smartFilters.regions\"\n            [checked]=\"region.checked\"\n            (change)=\"onFilterChange('regions', region)\"\n            class=\"filter-checkbox\">\n            {{ region.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n\n      <!-- Key Metrics Filter -->\n      <div class=\"filter-section\">\n        <div class=\"filter-label\">\n          <span>Key Metrics</span>\n        </div>\n        <div class=\"checkbox-group\">\n          <mat-checkbox\n            *ngFor=\"let metric of smartFilters.keyMetrics\"\n            [checked]=\"metric.checked\"\n            (change)=\"onFilterChange('keyMetrics', metric)\"\n            class=\"filter-checkbox\">\n            {{ metric.label }}\n          </mat-checkbox>\n        </div>\n      </div>\n    </div>\n  </mat-sidenav>\n\n  <!-- Main Content Area -->\n  <mat-sidenav-content class=\"main-content\">\n    <!-- AI Assistant Input -->\n    <div class=\"ai-input-section\">\n      <div class=\"ai-input-container\">\n        <mat-icon class=\"ai-icon\" color=\"primary\">psychology</mat-icon>\n        <span class=\"ai-label\">Ask AI Assistant</span>\n        <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n          <input\n            matInput\n            [formControl]=\"messageControl\"\n            placeholder=\"Ask AI to create a chart...\"\n            (keydown)=\"onKeyPress($event)\"\n            [disabled]=\"isLoading\">\n        </mat-form-field>\n        <button\n          mat-icon-button\n          color=\"primary\"\n          (click)=\"sendMessage()\"\n          [disabled]=\"!messageControl.value?.trim() || isLoading\"\n          class=\"send-button\">\n          <mat-icon>send</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <!-- Welcome State -->\n    <div class=\"welcome-state\" *ngIf=\"!currentSession && chatMessages.length === 0\">\n      <div class=\"welcome-content\">\n        <div class=\"ai-brain-icon\">\n          <mat-icon>psychology</mat-icon>\n        </div>\n        <h1>AI-Powered Data Analysis</h1>\n        <p>Configure your report filters and generate a dashboard to start analyzing your data with AI.</p>\n\n        <div class=\"features-grid\">\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>bar_chart</mat-icon>\n            </div>\n            <h3>Interactive Charts</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h3>Smart Insights</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>chat</mat-icon>\n            </div>\n            <h3>Natural Language Queries</h3>\n          </div>\n          <div class=\"feature-card\">\n            <div class=\"feature-icon\">\n              <mat-icon>speed</mat-icon>\n            </div>\n            <h3>Real-time Analysis</h3>\n          </div>\n        </div>\n\n        <!-- Steps -->\n        <div class=\"steps-section\">\n          <div class=\"step-item\" [class.completed]=\"getActiveFiltersCount() > 0\">\n            <div class=\"step-icon\">\n              <mat-icon>{{ getActiveFiltersCount() > 0 ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <span>Select your filters (categories/regions)</span>\n          </div>\n          <div class=\"step-item\" [class.completed]=\"chatMessages.length > 0\">\n            <div class=\"step-icon\">\n              <mat-icon>{{ chatMessages.length > 0 ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <span>Ask the AI assistant what you'd like to visualize</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Chat Messages -->\n    <div class=\"chat-messages\" #chatContainer *ngIf=\"chatMessages.length > 0\">\n      <div class=\"messages-list\">\n        <div\n          *ngFor=\"let message of chatMessages; trackBy: trackByIndex\"\n          class=\"message\"\n          [ngClass]=\"{'user-message': message.type === 'human', 'ai-message': message.type === 'ai'}\">\n\n          <div class=\"message-content\">\n            <div class=\"message-header\">\n              <span class=\"message-sender\">\n                <mat-icon>{{ message.type === 'human' ? 'person' : 'psychology' }}</mat-icon>\n                {{ message.type === 'human' ? 'You' : 'AI Assistant' }}\n              </span>\n              <span class=\"message-time\">\n                {{ message.created_at | date:'short' }}\n              </span>\n            </div>\n\n            <div class=\"message-text\">\n              <markdown [data]=\"message.content\"></markdown>\n            </div>\n\n            <!-- Chart Visualization -->\n            <div *ngIf=\"message.has_visualization && message.chart_data\" class=\"message-chart\">\n              <!-- Chart component would go here -->\n              <div class=\"chart-placeholder\">Chart visualization would appear here</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading indicator -->\n      <div *ngIf=\"isLoading\" class=\"loading-message\">\n        <mat-spinner diameter=\"20\"></mat-spinner>\n        <span>DIGI is thinking...</span>\n      </div>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICO7BC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IACnEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,SAAA,MACF;;;;;;IAWFL,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAC,gFAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAe,YAAY,EAAAL,WAAA,CAAW;IAAA,EAAC;IAEjDb,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAS,WAAA,CAAAM,OAAA,CAA4B;IAG5BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,WAAA,CAAAO,KAAA,MACF;;;;;;IAUApB,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAa,gFAAA;MAAA,MAAAX,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAW,IAAA;MAAA,MAAAC,UAAA,GAAAb,WAAA,CAAAI,SAAA;MAAA,MAAAU,OAAA,GAAAxB,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAO,OAAA,CAAAN,cAAA,CAAe,SAAS,EAAAK,UAAA,CAAS;IAAA,EAAC;IAE5CvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAmB,UAAA,CAAAJ,OAAA,CAA0B;IAG1BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAgB,UAAA,CAAAH,KAAA,MACF;;;;;;IAUApB,EAAA,CAAAC,cAAA,uBAI0B;IADxBD,EAAA,CAAAQ,UAAA,oBAAAiB,gFAAA;MAAA,MAAAf,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAe,IAAA;MAAA,MAAAC,UAAA,GAAAjB,WAAA,CAAAI,SAAA;MAAA,MAAAc,OAAA,GAAA5B,EAAA,CAAAgB,aAAA;MAAA,OAAUhB,EAAA,CAAAiB,WAAA,CAAAW,OAAA,CAAAV,cAAA,CAAe,YAAY,EAAAS,UAAA,CAAS;IAAA,EAAC;IAE/C3B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;;;;IAJbH,EAAA,CAAAI,UAAA,YAAAuB,UAAA,CAAAR,OAAA,CAA0B;IAG1BnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAoB,UAAA,CAAAP,KAAA,MACF;;;;;IAiCNpB,EAAA,CAAAC,cAAA,cAAgF;IAGhED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mGAA4F;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnGH,EAAA,CAAAC,cAAA,cAA2B;IAGXD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAK/BH,EAAA,CAAAC,cAAA,eAA2B;IAGXD,EAAA,CAAAE,MAAA,IAA6E;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,eAAmE;IAErDD,EAAA,CAAAE,MAAA,IAAyE;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yDAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAVzCH,EAAA,CAAAM,SAAA,IAA+C;IAA/CN,EAAA,CAAA6B,WAAA,cAAAC,MAAA,CAAAC,qBAAA,OAA+C;IAExD/B,EAAA,CAAAM,SAAA,GAA6E;IAA7EN,EAAA,CAAAgC,iBAAA,CAAAF,MAAA,CAAAC,qBAAA,mDAA6E;IAIpE/B,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA6B,WAAA,cAAAC,MAAA,CAAAG,YAAA,CAAAC,MAAA,KAA2C;IAEpDlC,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAgC,iBAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAC,MAAA,iDAAyE;;;;;IAgCrFlC,EAAA,CAAAC,cAAA,cAAmF;IAElDD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAvBhFH,EAAA,CAAAC,cAAA,cAG8F;IAK5ED,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7EH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAmC,SAAA,oBAA8C;IAChDnC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoC,UAAA,KAAAC,oDAAA,kBAGM;IACRrC,EAAA,CAAAG,YAAA,EAAM;;;;IAtBNH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,cAAAD,WAAA,CAAAC,IAAA,WAA2F;IAK3EzC,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAAgC,iBAAA,CAAAQ,WAAA,CAAAC,IAAA,uCAAwD;IAClEzC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAiC,WAAA,CAAAC,IAAA,2CACF;IAEEzC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA0C,WAAA,OAAAF,WAAA,CAAAG,UAAA,gBACF;IAIU3C,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAoC,WAAA,CAAAI,OAAA,CAAwB;IAI9B5C,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAI,UAAA,SAAAoC,WAAA,CAAAK,iBAAA,IAAAL,WAAA,CAAAM,UAAA,CAAqD;;;;;IASjE9C,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAmC,SAAA,sBAAyC;IACzCnC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAlCpCH,EAAA,CAAAC,cAAA,kBAA0E;IAEtED,EAAA,CAAAoC,UAAA,IAAAW,6CAAA,oBA0BM;IACR/C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoC,UAAA,IAAAY,6CAAA,kBAGM;IACRhD,EAAA,CAAAG,YAAA,EAAM;;;;IAjCoBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAA6C,MAAA,CAAAhB,YAAA,CAAiB,iBAAAgB,MAAA,CAAAC,YAAA;IA6BnClD,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,SAAA6C,MAAA,CAAAE,SAAA,CAAe;;;ADvK3B,MAwBaC,uBAAuB;EA0DlCC,YACUC,EAAqB,EACrBC,qBAA4C,EAC5CC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IA3DlB;IACA,KAAAC,cAAc,GAAG,IAAI5E,WAAW,CAAC,EAAE,CAAC;IACpC,KAAA6E,iBAAiB,GAAG,IAAI7E,WAAW,CAAC,YAAY,CAAC;IAEjD;IACA,KAAAqE,SAAS,GAAG,KAAK;IACjB,KAAAS,cAAc,GAA4B,IAAI;IAC9C,KAAA3B,YAAY,GAAkB,EAAE;IACxB,KAAA4B,QAAQ,GAAG,IAAI/D,OAAO,EAAQ;IAGtC;IACA,KAAAgE,YAAY,GAAiB;MAC3BC,UAAU,EAAE,cAAc;MAC1BC,UAAU,EAAE,CACV;QAAEC,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,YAAY;QAAED,OAAO,EAAE;MAAK,CAAE,EAC3D;QAAE8C,KAAK,EAAE,MAAM;QAAE7C,KAAK,EAAE,MAAM;QAAED,OAAO,EAAE;MAAK,CAAE,EAChD;QAAE8C,KAAK,EAAE,aAAa;QAAE7C,KAAK,EAAE,aAAa;QAAED,OAAO,EAAE;MAAK,CAAE,EAC9D;QAAE8C,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,WAAW;QAAED,OAAO,EAAE;MAAK,CAAE,EAC1D;QAAE8C,KAAK,EAAE,SAAS;QAAE7C,KAAK,EAAE,SAAS;QAAED,OAAO,EAAE;MAAK,CAAE,CACvD;MACD+C,OAAO,EAAE,CACP;QAAED,KAAK,EAAE,eAAe;QAAE7C,KAAK,EAAE,eAAe;QAAED,OAAO,EAAE;MAAK,CAAE,EAClE;QAAE8C,KAAK,EAAE,QAAQ;QAAE7C,KAAK,EAAE,QAAQ;QAAED,OAAO,EAAE;MAAI,CAAE,EACnD;QAAE8C,KAAK,EAAE,cAAc;QAAE7C,KAAK,EAAE,cAAc;QAAED,OAAO,EAAE;MAAK,CAAE,EAChE;QAAE8C,KAAK,EAAE,eAAe;QAAE7C,KAAK,EAAE,eAAe;QAAED,OAAO,EAAE;MAAK,CAAE,EAClE;QAAE8C,KAAK,EAAE,QAAQ;QAAE7C,KAAK,EAAE,QAAQ;QAAED,OAAO,EAAE;MAAK,CAAE,CACrD;MACDgD,UAAU,EAAE,CACV;QAAEF,KAAK,EAAE,SAAS;QAAE7C,KAAK,EAAE,SAAS;QAAED,OAAO,EAAE;MAAK,CAAE,EACtD;QAAE8C,KAAK,EAAE,OAAO;QAAE7C,KAAK,EAAE,OAAO;QAAED,OAAO,EAAE;MAAK,CAAE,EAClD;QAAE8C,KAAK,EAAE,aAAa;QAAE7C,KAAK,EAAE,aAAa;QAAED,OAAO,EAAE;MAAK,CAAE,EAC9D;QAAE8C,KAAK,EAAE,YAAY;QAAE7C,KAAK,EAAE,YAAY;QAAED,OAAO,EAAE;MAAK,CAAE,EAC5D;QAAE8C,KAAK,EAAE,WAAW;QAAE7C,KAAK,EAAE,WAAW;QAAED,OAAO,EAAE;MAAK,CAAE;KAE7D;IAED;IACA,KAAAiD,iBAAiB,GAAG,CAClB,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,WAAW,EACX,cAAc,CACf;IAED;IACA,KAAAC,cAAc,GAAG,CACf,kCAAkC,EAClC,2CAA2C,EAC3C,sCAAsC,EACtC,qCAAqC,CACtC;IAQC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACd,WAAW,CAACe,cAAc,EAAE;EAC/C;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAClB,qBAAqB,CAACmB,eAAe,CACvCC,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAAC8D,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAACC,OAAO,IAAG;MACnB,IAAI,CAACjB,cAAc,GAAGiB,OAAO;MAC7B,IAAI,CAACvB,EAAE,CAACwB,aAAa,EAAE;IACzB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClB,QAAQ,CAACmB,IAAI,EAAE;IACpB,IAAI,CAACnB,QAAQ,CAACoB,QAAQ,EAAE;EAC1B;EAEQR,iBAAiBA,CAAA;IACvB;IACA,MAAMS,YAAY,GAAG,IAAI,CAACpB,YAAY,CAACI,OAAO,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,KAAK,QAAQ,CAAC;IAC9E,IAAIiB,YAAY,EAAE;MAChBA,YAAY,CAAC/D,OAAO,GAAG,IAAI;;EAE/B;EAEA;EACAY,qBAAqBA,CAAA;IACnB,IAAIsD,KAAK,GAAG,CAAC;IACbA,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACE,UAAU,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpE,OAAO,CAAC,CAACe,MAAM;IACnEmD,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACI,OAAO,CAACoB,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACjE,OAAO,CAAC,CAACe,MAAM;IAChEmD,KAAK,IAAI,IAAI,CAACvB,YAAY,CAACK,UAAU,CAACmB,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACrE,OAAO,CAAC,CAACe,MAAM;IACnE,OAAOmD,KAAK;EACd;EAEAnE,cAAcA,CAACuE,UAA8B,EAAEC,MAAoB;IACjE,IAAID,UAAU,KAAK,YAAY,EAAE;MAC/BC,MAAM,CAACvE,OAAO,GAAG,CAACuE,MAAM,CAACvE,OAAO;MAChC,IAAI,CAACmC,EAAE,CAACwB,aAAa,EAAE;;EAE3B;EAEAa,kBAAkBA,CAACC,MAAc;IAC/B,IAAI,CAAC9B,YAAY,CAACC,UAAU,GAAG6B,MAAM;EACvC;EAEA;EACAC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEAA,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAI,CAACzC,cAAc,CAACO,KAAK,EAAEmC,IAAI,EAAE;IACjD,IAAI,CAACD,OAAO,IAAI,IAAI,CAAChD,SAAS,EAAE;IAEhC;IACA,IAAI,CAAClB,YAAY,CAACoE,IAAI,CAAC;MACrB5D,IAAI,EAAE,OAAO;MACbG,OAAO,EAAEuD,OAAO;MAChBxD,UAAU,EAAE,IAAI2D,IAAI;KACrB,CAAC;IAEF,IAAI,CAAC5C,cAAc,CAAC6C,QAAQ,CAAC,EAAE,CAAC;IAChC,IAAI,CAACpD,SAAS,GAAG,IAAI;IAErB;IACAqD,UAAU,CAAC,MAAK;MACd,IAAI,CAACvE,YAAY,CAACoE,IAAI,CAAC;QACrB5D,IAAI,EAAE,IAAI;QACVG,OAAO,EAAE,kHAAkH;QAC3HD,UAAU,EAAE,IAAI2D,IAAI,EAAE;QACtBzD,iBAAiB,EAAE;OACpB,CAAC;MACF,IAAI,CAACM,SAAS,GAAG,KAAK;MACtB,IAAI,CAACsD,cAAc,EAAE;MACrB,IAAI,CAACnD,EAAE,CAACwB,aAAa,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAAC2B,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAACC,KAAa;IAC5B,IAAI,CAACjD,cAAc,CAAC6C,QAAQ,CAACI,KAAK,CAAC;IACnC,IAAI,CAACT,WAAW,EAAE;EACpB;EAEQO,cAAcA,CAAA;IACpBD,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACI,aAAa,EAAE;QACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,YAAY;;IAE9F,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA7D,YAAYA,CAAC8D,KAAa,EAAEC,KAAU;IACpC,OAAOD,KAAK;EACd;EAEAE,gBAAgBA,CAACC,OAAY;IAC3B;IACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;EAC1C;;;uBA5KW/D,uBAAuB,EAAApD,EAAA,CAAAsH,iBAAA,CAAAtH,EAAA,CAAAuH,iBAAA,GAAAvH,EAAA,CAAAsH,iBAAA,CAAAE,EAAA,CAAAC,qBAAA,GAAAzH,EAAA,CAAAsH,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAsH,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBzE,uBAAuB;MAAA0E,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCnDpCjI,EAAA,CAAAC,cAAA,+BAAmD;UAOXD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,mBAA8B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKxDH,EAAA,CAAAC,cAAA,cAA4B;UAEOD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,0BAAgE;UAG5DD,EAAA,CAAAQ,UAAA,6BAAA2H,wEAAAC,MAAA;YAAA,OAAmBF,GAAA,CAAAvC,kBAAA,CAAAyC,MAAA,CAAAnE,KAAA,CAAgC;UAAA,EAAC;UACpDjE,EAAA,CAAAoC,UAAA,KAAAiG,8CAAA,yBAEa;UACfrI,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAkG,gDAAA,2BAMe;UACjBtI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtBH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAmG,gDAAA,2BAMe;UACjBvI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAElBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAoC,UAAA,KAAAoG,gDAAA,2BAMe;UACjBxI,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,+BAA0C;UAIMD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/DH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,0BAA4D;UAKxDD,EAAA,CAAAQ,UAAA,qBAAAiI,2DAAAL,MAAA;YAAA,OAAWF,GAAA,CAAArC,UAAA,CAAAuC,MAAA,CAAkB;UAAA,EAAC;UAJhCpI,EAAA,CAAAG,YAAA,EAKyB;UAE3BH,EAAA,CAAAC,cAAA,kBAKsB;UAFpBD,EAAA,CAAAQ,UAAA,mBAAAkI,0DAAA;YAAA,OAASR,GAAA,CAAAhC,WAAA,EAAa;UAAA,EAAC;UAGvBlG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAoC,UAAA,KAAAuG,uCAAA,mBAmDM;UAGN3I,EAAA,CAAAoC,UAAA,KAAAwG,uCAAA,kBAoCM;UACR5I,EAAA,CAAAG,YAAA,EAAsB;;;UA9LaH,EAAA,CAAAM,SAAA,IAA6B;UAA7BN,EAAA,CAAAgC,iBAAA,CAAAkG,GAAA,CAAAnG,qBAAA,GAA6B;UAatD/B,EAAA,CAAAM,SAAA,IAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAA8H,GAAA,CAAApE,YAAA,CAAAC,UAAA,CAAiC;UAEF/D,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAA9D,iBAAA,CAAoB;UAc9BpE,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAE,UAAA,CAA0B;UAgB5BhE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAI,OAAA,CAAuB;UAgBvBlE,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAApE,YAAA,CAAAK,UAAA,CAA0B;UAqB7CnE,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,gBAAA8H,GAAA,CAAAxE,cAAA,CAA8B,aAAAwE,GAAA,CAAA/E,SAAA;UAShCnD,EAAA,CAAAM,SAAA,GAAuD;UAAvDN,EAAA,CAAAI,UAAA,eAAA8H,GAAA,CAAAxE,cAAA,CAAAO,KAAA,kBAAAiE,GAAA,CAAAxE,cAAA,CAAAO,KAAA,CAAAmC,IAAA,OAAA8B,GAAA,CAAA/E,SAAA,CAAuD;UAQjCnD,EAAA,CAAAM,SAAA,GAAkD;UAAlDN,EAAA,CAAAI,UAAA,UAAA8H,GAAA,CAAAtE,cAAA,IAAAsE,GAAA,CAAAjG,YAAA,CAAAC,MAAA,OAAkD;UAsDnClC,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,SAAA8H,GAAA,CAAAjG,YAAA,CAAAC,MAAA,KAA6B;;;qBDnIxErD,YAAY,EAAAgK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZlK,mBAAmB,EAAAmK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EACnBrK,gBAAgB,EAAAsK,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAChBxK,aAAa,EACbC,aAAa,EAAAwK,EAAA,CAAAC,OAAA,EACbxK,cAAc,EACdC,kBAAkB,EAClBC,kBAAkB,EAAAuK,EAAA,CAAAC,YAAA,EAClBvK,eAAe,EAAAwK,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf1K,iBAAiB,EAAA2K,GAAA,CAAAC,WAAA,EACjB3K,eAAe,EAAA4K,GAAA,CAAAC,aAAA,EACf5K,cAAc,EAAA6K,GAAA,CAAAC,QAAA,EACd7K,aAAa,EACbC,wBAAwB,EAAA6K,GAAA,CAAAC,kBAAA,EACxB5K,iBAAiB,EACjBD,cAAc,EAAA8K,GAAA,CAAAC,iBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKLxH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}