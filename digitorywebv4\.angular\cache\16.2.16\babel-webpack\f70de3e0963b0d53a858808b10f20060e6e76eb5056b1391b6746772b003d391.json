{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"../../services/smart-dashboard.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/forms\";\nconst _c0 = [\"chartsContainer\"];\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48)(1, \"div\", 49)(2, \"div\", 50)(3, \"span\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r10.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r11));\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.displayName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"mat-icon\", 57);\n    i0.ɵɵtext(4, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 58);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.getLoadingMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getLoadingMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 64);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r7.getEmptyStateMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getEmptyStateMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_86_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 70)(5, \"div\", 71);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 72);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.label);\n  }\n}\nfunction SmartDashboardComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_86_div_2_Template, 9, 3, \"div\", 67);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getSummaryItems());\n  }\n}\nfunction SmartDashboardComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 73, 74);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService, smartDashboardService, cdr) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.smartDashboardService = smartDashboardService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.tabs = [];\n    this.selectedTab = 0;\n    this.locations = [];\n    this.baseDates = [];\n    this.selectedLocations = [];\n    this.selectedBaseDate = '';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    this.dashboardData = null;\n    this.charts = [];\n    this.isLoading = false;\n    this.dashboardConfig = null;\n    this.locationFilterCtrl = new FormControl();\n    this.filteredLocations = new Observable();\n    this.allLocationsSelected = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n  initializeComponent() {\n    this.loadDashboardTabs();\n    this.loadBaseDateOptions();\n    this.loadDashboardConfig();\n    this.loadLocations();\n    this.smartDashboardService.dashboardData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.dashboardData = data;\n      if (data) {\n        this.renderCharts();\n      }\n      this.cdr.detectChanges();\n    });\n    this.smartDashboardService.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.detectChanges();\n    });\n  }\n  loadDashboardTabs() {\n    this.smartDashboardService.getDashboardTabs().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.tabs = response.data;\n          if (this.tabs.length > 0) {\n            this.tabs[0].active = true;\n          }\n        }\n      },\n      error: () => {\n        this.setDefaultTabs();\n      }\n    });\n  }\n  loadBaseDateOptions() {\n    this.smartDashboardService.getBaseDateOptions().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.baseDates = response.data;\n          if (this.baseDates.length > 0) {\n            this.selectedBaseDate = this.baseDates[0].value;\n          }\n        }\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setDefaultBaseDates();\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadDashboardConfig() {\n    this.smartDashboardService.getDashboardConfig().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.dashboardConfig = response.data;\n        }\n      },\n      error: () => {}\n    });\n  }\n  setDefaultTabs() {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n  setDefaultBaseDates() {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n  setDefaultDates() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    // Create first day of current month\n    const firstDayOfMonth = new Date(year, month, 1);\n    // Format dates properly\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n  sendMessage() {\n    if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n      this.generateDashboard();\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: true\n            }));\n            this.selectedLocations = this.locations.map(location => location.value);\n            this.setupLocationFilter();\n          } else {\n            this.locations = [];\n            this.selectedLocations = [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.locations.forEach(location => location.checked = true);\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n  getDashboardDescription(index) {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n  areAllFiltersValid() {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n  setupLocationFilter() {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(startWith(''), map(value => this.filterLocations(value || '')));\n  }\n  filterLocations(value) {\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location => location.label.toLowerCase().includes(filterValue));\n  }\n  toggleSelectAllLocations() {\n    this.allLocationsSelected = !this.allLocationsSelected;\n    if (this.allLocationsSelected) {\n      this.selectedLocations = this.locations.map(location => location.value);\n      this.locations.forEach(location => location.checked = true);\n    } else {\n      this.selectedLocations = [];\n      this.locations.forEach(location => location.checked = false);\n    }\n    this.cdr.detectChanges();\n  }\n  onLocationSelectionChange(selectedValues) {\n    this.selectedLocations = selectedValues;\n    this.locations.forEach(location => {\n      location.checked = selectedValues.includes(location.value);\n    });\n    this.allLocationsSelected = selectedValues.length === this.locations.length;\n    this.cdr.detectChanges();\n  }\n  getLoadingMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n  getEmptyStateMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n  generateDashboard() {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    this.clearCharts();\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || ''\n    };\n    this.smartDashboardService.generateDashboard(request).pipe(first()).subscribe({\n      next: () => {},\n      error: () => {}\n    });\n  }\n  clearCharts() {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n  renderCharts() {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n    this.clearCharts();\n    setTimeout(() => {\n      this.dashboardData.charts.forEach(chartConfig => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n  getSummaryItems() {\n    if (!this.dashboardData?.summary) {\n      return [];\n    }\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getSummaryItems(this.dashboardData.summary, dashboardType);\n  }\n  createChart(chartConfig) {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n      const chart = new Chart(ctx, {\n        type: chartConfig.type,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n      this.charts.push(chart);\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.SmartDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartsContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 88,\n      vars: 24,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask me about your business data...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"dashboard-summary\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"selection-count\"], [1, \"loading-state\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"dashboard-summary\"], [1, \"summary-cards\"], [\"class\", \"summary-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-value\"], [1, \"summary-label\"], [1, \"charts-container\"], [\"chartsContainer\", \"\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(28, SmartDashboardComponent_mat_option_28_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 15)(30, \"label\", 16)(31, \"mat-icon\", 17);\n          i0.ɵɵtext(32, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\", 18);\n          i0.ɵɵtext(34, \"Base Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"mat-form-field\", 20)(36, \"mat-select\", 22);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_36_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(37, SmartDashboardComponent_mat_option_37_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 15)(39, \"label\", 16)(40, \"mat-icon\", 17);\n          i0.ɵɵtext(41, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 18);\n          i0.ɵɵtext(43, \"Start Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"mat-form-field\", 20)(45, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_45_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"mat-datepicker-toggle\", 24)(47, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"label\", 16)(51, \"mat-icon\", 17);\n          i0.ɵɵtext(52, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 18);\n          i0.ɵɵtext(54, \"End Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"mat-form-field\", 20)(56, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"mat-datepicker-toggle\", 24)(58, \"mat-datepicker\", null, 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 28)(61, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_61_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Reset Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(65, \"div\", 30)(66, \"div\", 31)(67, \"div\", 32)(68, \"div\", 33)(69, \"mat-icon\", 34);\n          i0.ɵɵtext(70, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 35);\n          i0.ɵɵtext(72, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\", 36);\n          i0.ɵɵtext(74);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 37)(76, \"div\", 38)(77, \"mat-form-field\", 39)(78, \"input\", 40);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_78_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_78_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_79_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(80, \"mat-icon\");\n          i0.ɵɵtext(81, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(82, \"div\", 42)(83, \"div\", 43);\n          i0.ɵɵtemplate(84, SmartDashboardComponent_div_84_Template, 9, 2, \"div\", 44);\n          i0.ɵɵtemplate(85, SmartDashboardComponent_div_85_Template, 9, 2, \"div\", 45);\n          i0.ɵɵtemplate(86, SmartDashboardComponent_div_86_Template, 3, 1, \"div\", 46);\n          i0.ɵɵtemplate(87, SmartDashboardComponent_div_87_Template, 2, 0, \"div\", 47);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(48);\n          const _r5 = i0.ɵɵreference(59);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(16);\n          i0.ɵɵclassProp(\"ready\", ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.areAllFiltersValid() ? \"Ready to analyze\" : \"Please fill all required filters\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", !ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || !ctx.areAllFiltersValid());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.dashboardData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.dashboardData == null ? null : ctx.dashboardData.summary));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatSuffix, MatSelectModule, i8.MatSelect, i8.MatSelectTrigger, i9.MatOption, MatInputModule, i10.MatInput, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, FormsModule, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, ReactiveFormsModule, NgxMatSelectSearchModule],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 270px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f3f4f6;\\n  margin-bottom: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 0.5rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  margin-bottom: 0.75rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem !important;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  padding-right: 0.25rem;\\n  margin-bottom: 0.5rem;\\n  max-height: calc(100vh - 280px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 1px 4px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #374151;\\n  border-color: #d1d5db;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n  background: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #ff6b35;\\n  color: #ff6b35;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0;\\n  gap: 0;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4b5563;\\n  padding: 3px 8px;\\n  border-radius: 0.375rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  width: 100%;\\n  max-width: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n  border: 2px solid #ff6b35;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  border-color: #e55a2b;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  background: #d1d5db;\\n  border-color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #ff6b35;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%] {\\n  padding: 1rem 1rem 0.75rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #4b5563;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  align-content: start;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header {\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header h3 {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container canvas {\\n  max-width: 100%;\\n  height: 300px !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    align-self: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 8px 12px !important;\\n  min-height: 20px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.85rem !important;\\n  min-height: 48px !important;\\n  line-height: 1.2 !important;\\n  padding: 8px 12px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYWktZGFzaGJvYXJkL3NtYXJ0LWRhc2hib2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrREE7RUFDRSxhQUFBO0VBQ0EsMEJBQUE7RUFDQSw4QkFBQTtFQUNBLDZEQUFBO0VBQ0EsK0VBQUE7RUFDQSxjQXhDUztFQXlDVCxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7QUFqREY7QUFtREU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsNEZBQUE7RUFDQSxvQkFBQTtFQUNBLFVBQUE7QUFqREo7O0FBc0RBO0VBQ0UsWUFBQTtFQUNBLG1CQXRFTTtFQXVFTiwrQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLDJDQWxEVTtFQW1EVixnQkFBQTtBQW5ERjtBQXFERTtFQUNFLGVBbEVTO0FBZWI7QUFxREk7RUFDRSxnQ0FBQTtFQUNBLGdCQUFBO0FBbkROO0FBc0RJO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQTdFTztBQXlCYjtBQXVESTtFQUNFLG1CQWhGTztFQWlGUCxzQkFqRk87QUE0QmI7QUF1RE07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQXhGSztFQXlGTCxrQkFBQTtBQXJEUjtBQXVEUTtFQUNFLGNBL0dBO0VBZ0hBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtBQXJEVjtBQXdEUTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQTlHQztFQStHRCxxQkFBQTtBQXREVjtBQXlEUTtFQUNFLG1CQWxJQTtFQW1JQSxjQTdIRjtFQThIRSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsMkNBN0dFO0FBc0RaO0FBOERFO0VBQ0UsdUZBQUE7RUFDQSwwQ0FBQTtFQUNBLHNCQTFIUTtFQTJIUiwwQkFBQTtFQUNBLGNBcklTO0VBc0lULGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSw4Q0FBQTtBQTVESjtBQThESTtFQUNFLHVGQUFBO0VBQ0EscUNBQUE7RUFDQSwrQ0FBQTtBQTVETjtBQStESTtFQUNFLFdBQUE7QUE3RE47QUFnRU07RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EseURBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx3Q0FBQTtBQTlEUjtBQWdFUTtFQUNFLGdDQUFBO0VBQ0EsMERBQUE7QUE5RFY7QUFrRU07RUFDRSxnQ0FBQTtFQUNBLDZGQUFBO0FBaEVSO0FBbUVNO0VBQ0UsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7QUFqRVI7QUF3RUk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0F2TE87RUF3TFAsZ0JBQUE7RUFDQSxzQkExTE87RUEyTFAscUJBMUxPO0VBMkxQLCtCQUFBO0FBdEVOO0FBeUVNO0VBQ0UsVUFBQTtBQXZFUjtBQTBFTTtFQUNFLHVCQUFBO0FBeEVSO0FBMkVNO0VBQ0UsbUNBQUE7RUFDQSxrQkFBQTtBQXpFUjtBQTJFUTtFQUNFLG1DQUFBO0FBekVWO0FBOEVRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFwTkc7RUFxTkgsdUJBdE5HO0VBdU5ILGdCQUFBO0VBQ0EsY0E1TkM7RUE2TkQsaUJBQUE7RUFDQSxxQkFBQTtBQTVFVjtBQThFVTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQWxQRjtFQW1QRSxjQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUE1RVo7QUErRVU7RUFDRSxPQUFBO0VBQ0EsY0FBQTtBQTdFWjtBQWdGVTtFQUNFLGtCQUFBO0VBQ0EsY0FsUUY7RUFtUUUsZ0JBQUE7RUFDQSxtQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBM09BO0VBNE9BLGNBQUE7QUE5RVo7QUFrRlE7RUFDRSxXQUFBO0FBaEZWO0FBcUZJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBaFFPO0VBaVFQLDZCQUFBO0VBQ0EsZ0JBQUE7QUFuRk47QUFxRk07RUFDRSxZQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQS9QSTtFQWdRSixjQTlRRztFQStRSCxxQkFuUkc7RUFvUkgsa0JBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBM1JBO0VBNFJBLDJDQWxRSTtBQStLWjtBQXFGUTtFQUNFLG1CQTlSQTtFQStSQSxxQkF0U0E7RUF1U0EsY0F2U0E7RUF3U0EsaUZBdlFFO0FBb0xaO0FBc0ZRO0VBQ0UsZUFBQTtFQUNBLHFCQTFSRztBQXNNYjs7QUE0RkE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsVUFBQTtFQUNBLE1BQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxvQkFBQTtFQUNBLGdCQUFBO0FBekZGO0FBNEZFO0VBQ0UsY0FBQTtFQUNBLFNBQUE7RUFDQSx1RkFBQTtFQUNBLDBDQUFBO0VBQ0Esc0JBelNRO0VBMFNSLGNBblRTO0VBb1RULGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSw4Q0FBQTtBQTFGSjtBQTRGSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBM1RPO0VBNFRQLGVBNVRPO0VBNlRQLGdCQUFBO0FBMUZOO0FBNEZNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFuVUs7RUFvVUwsY0FBQTtFQUNBLGdCQUFBO0FBMUZSO0FBNEZRO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtFQUNBLGNBOVZBO0VBK1ZBLHNCQTdVRztBQW1QYjtBQTZGUTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXRWQztFQXVWRCxtQkFBQTtFQUNBLHNCQXJWRztBQTBQYjtBQThGUTtFQUNFLGtCQUFBO0VBQ0EsY0EvVkM7RUFnV0QsZ0JBQUE7RUFDQSx1QkFuVkU7RUFvVkYsbUNBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0FBNUZWO0FBOEZVO0VBQ0UsbUNBQUE7RUFDQSxjQWxYRjtBQXNSVjtBQWlHTTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQS9GUjtBQWlHUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBL1dHO0VBZ1hILFdBQUE7RUFDQSxlQUFBO0FBL0ZWO0FBaUdVO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUEvRlo7QUFrR1k7RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EseURBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx3Q0FBQTtBQWhHZDtBQWtHYztFQUNFLGdDQUFBO0VBQ0EsMERBQUE7QUFoR2hCO0FBb0dZO0VBQ0UsZ0NBQUE7RUFDQSw2RkFBQTtBQWxHZDtBQXFHWTtFQUNFLDZCQUFBO0VBQ0EsMkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0FBbkdkO0FBc0dZO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBcEdkO0FBd0dVO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFqYkY7RUFrYkUsY0E1YUo7RUE2YUksNkJBQUE7RUFDQSxjQUFBO0VBQ0EscUJBelpBO0VBMFpBLHlCQUFBO0VBQ0EsOENBQUE7QUF0R1o7QUF3R1k7RUFDRSxtQkF4YkM7RUF5YkQscUJBemJDO0VBMGJELCtDQUFBO0FBdEdkO0FBeUdZO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBeGJIO0VBeWJHLHFCQXpiSDtBQWtWWDtBQTBHWTtFQUNFLGlCQUFBO0FBeEdkO0FBaUhFO0VBQ0UsT0FBQTtFQXZhRixtQkFwQ007RUFxQ04sc0JBZFU7RUFlVixpRkFYVTtFQVlWLDBDQUFBO0VBc2FFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FsY1M7QUFzVmI7QUE4R0k7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUE1R047QUErR007RUFDRSxVQUFBO0FBN0dSO0FBZ0hNO0VBQ0UsbUJBN2RFO0FBK1dWO0FBaUhNO0VBQ0UsbUNBQUE7RUFDQSxrQkFBQTtBQS9HUjtBQWlIUTtFQUNFLG1DQUFBO0FBL0dWO0FBb0hNO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBL2RNO0FBNldkO0FBb0hRO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQWxIVjtBQW9IVTtFQUNFLHNCQXhlQztBQXNYYjtBQW9IWTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBemZIO0FBdVlYO0FBc0hVO0VBQ0UscUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0E1ZkQ7QUF3WVg7QUF1SFU7RUFDRSxTQUFBO0VBQ0EsY0FuZ0JEO0VBb2dCQyxlQUFBO0VBQ0EsZ0JBQUE7QUFySFo7QUEySE07RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkF0Z0JNO0FBNllkO0FBMkhRO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQXpIVjtBQTJIVTtFQUNFLHNCQS9nQkM7QUFzWmI7QUEySFk7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQTFpQko7RUEyaUJJLG9DQUFBO0FBekhkO0FBNkhVO0VBQ0UscUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FwaUJEO0FBeWFYO0FBOEhVO0VBQ0UsU0FBQTtFQUNBLGNBM2lCRDtFQTRpQkMsZUFBQTtFQUNBLGdCQUFBO0FBNUhaO0FBa0lNO0VBQ0UsK0JBQUE7RUFDQSxnQ0FBQTtBQWhJUjtBQWtJUTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFlBbGpCRztBQWtiYjtBQWtJVTtFQUNFLG1CQXBrQko7RUFxa0JJLHlCQUFBO0VBQ0Esc0JBL2lCQTtFQWdqQkEsZ0JBeGpCQztFQXlqQkQsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0E1akJDO0VBNmpCRCw2QkFBQTtFQUNBLDJDQWxqQkE7QUFrYlo7QUFrSVk7RUFDRSxpRkFwakJGO0VBcWpCRSxxQ0FBQTtBQWhJZDtBQW1JWTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EscUJBaGtCRjtFQWlrQkUsc0ZBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGNBQUE7QUFqSWQ7QUFtSWM7RUFDRSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0VBQ0EsY0F2bUJOO0FBc2VWO0FBcUlZO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUFuSWQ7QUFxSWM7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FubUJMO0VBb21CSyxrQkFBQTtFQUNBLGdCQUFBO0FBbkloQjtBQXNJYztFQUNFLGtCQUFBO0VBQ0EsY0E1bUJMO0VBNm1CSyxnQkFBQTtFQUNBLGdCQUFBO0FBcEloQjtBQTRJTTtFQUNFLE9BQUE7RUFDQSxhQS9tQks7RUFnbkJMLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBbG5CSztFQW1uQkwsb0JBQUE7QUExSVI7QUE0SVE7RUFDRSxtQkF0b0JGO0VBdW9CRSx5QkFBQTtFQUNBLHNCQWpuQkU7RUFrbkJGLGdCQTFuQkc7RUEybkJILDJDQWhuQkU7RUFpbkJGLDZCQUFBO0FBMUlWO0FBNElVO0VBQ0UsaUZBbm5CQTtFQW9uQkEscUNBQUE7QUExSVo7QUE2SVU7RUFDRSxzQkFwb0JDO0VBcW9CRCxzQkF0b0JDO0VBdW9CRCxnQ0FBQTtBQTNJWjtBQTZJWTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FscEJIO0FBdWdCWDtBQStJVTtFQUNFLGVBQUE7RUFDQSx3QkFBQTtBQTdJWjs7QUFzSkE7RUFDRTtJQUFLLHVCQUFBO0VBbEpMO0VBbUpBO0lBQU8seUJBQUE7RUFoSlA7QUFDRjtBQW1KQTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSwwQkFBQTtJQUNBLDhCQUFBO0VBakpGO0VBb0pBO0lBQ0UsV0FBQTtJQUNBLGtCQUFBO0lBQ0EsZ0NBQUE7SUFDQSxjQUFBO0lBQ0EsZ0JBQUE7SUFDQSxnQkFBQTtFQWxKRjtFQW9KRTtJQUNFLHVCQUFBO0VBbEpKO0VBcUpFO0lBQ0UsZUF2ckJPO0VBb2lCWDtFQXVKSTtJQUNFLGlCQUFBO0lBQ0EsWUE3ckJLO0VBd2lCWDtFQTBKQTtJQUNFLGdCQW5zQlM7SUFvc0JULE9BQUE7SUFDQSxhQUFBO0VBeEpGO0FBQ0Y7QUEySkE7RUFFSTtJQUNFLGdCQTVzQk87RUFrakJYO0VBOEpJO0lBQ0UsV0FodEJLO0VBb2pCWDtFQStKSTtJQUNFLHNCQUFBO0lBQ0EsWUF0dEJLO0VBeWpCWDtFQStKTTs7SUFFRSxVQUFBO0lBQ0EsWUFBQTtFQTdKUjtFQW1LQTtJQUNFLGdCQWx1QlM7SUFtdUJULFlBbnVCUztFQWtrQlg7RUFvS0k7SUFDRSxzQkFBQTtJQUNBLFdBdnVCSztJQXd1QkwsZUF4dUJLO0lBeXVCTCxvQkFBQTtFQWxLTjtFQW9LTTtJQUNFLHVCQUFBO0lBQ0Esa0JBQUE7RUFsS1I7RUFxS007SUFDRSxXQUFBO0lBQ0EsdUJBQUE7RUFuS1I7RUFxS1E7SUFDRSxlQUFBO0VBbktWO0VBcUtVO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUFuS1o7RUE0S007SUFDRSxnQkFsd0JHO0VBd2xCWDtBQUNGO0FBbUxFO0VBQ0UsV0FBQTtBQWpMSjtBQW1MSTtFQUNFLDhCQUFBO0VBQ0Esa0NBQUE7RUFDQSxvQ0FBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0FBakxOO0FBbUxNO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHNEQUFBO0FBakxSO0FBcUxJO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHdEQUFBO0FBbkxOO0FBc0xJO0VBQ0UsNEJBQUE7RUFDQSwyQkFBQTtBQXBMTjtBQXVMSTtFQUNFLHdCQUFBO0FBckxOO0FBd0xJO0VBQ0Usd0JBQUE7QUF0TE47QUEwTEk7RUFDRSw2QkFBQTtFQUNBLDRCQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtBQXhMTjtBQTBMTTtFQUNFLHlCQUFBO0VBQ0EsMkJBQUE7QUF4TFI7QUEyTE07RUFDRSx5QkFBQTtFQUNBLGtDQUFBO0FBekxSO0FBOExJO0VBQ0UsNkJBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseUJBQUE7QUE1TE47QUFnTUk7RUFDRSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0EsMkJBQUE7RUFDQSxxQkFBQTtBQTlMTjtBQWlNSTtFQUNFLDBCQUFBO0FBL0xOO0FBa01JO0VBQ0UseUJBQUE7RUFDQSwwQkFBQTtFQUNBLDBDQUFBO0FBaE1OO0FBbU1JO0VBQ0UseUJBQUE7QUFqTU47QUFvTUk7RUFDRSx5QkFBQTtBQWxNTjtBQXVNTTtFQUNFLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSxnQ0FBQTtBQXJNUjtBQTJNRTtFQUNFLGtDQUFBO0VBQ0Esd0JBQUE7RUFDQSw2QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseUNBQUE7QUF6TUo7QUEyTUk7RUFDRSwrQ0FBQTtFQUNBLHFDQUFBO0FBek1OO0FBNE1JO0VBQ0UsK0NBQUE7RUFDQSx5QkFBQTtFQUNBLDJCQUFBO0FBMU1OO0FBNk1JO0VBQ0UsZ0NBQUE7RUFDQSw0QkFBQTtFQUNBLHlCQUFBO0FBM01OO0FBK01JO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGNBQUE7QUE3TU47QUErTU07RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxRQUFBO0VBQ0EsT0FBQTtBQTdNUjtBQStNUTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQWw2QkM7RUFtNkJELGdCQUFBO0FBN01WO0FBZ05RO0VBQ0Usa0JBQUE7RUFDQSxjQTM2QkM7RUE0NkJELGdCQUFBO0VBQ0EsZ0JBQUE7QUE5TVY7QUFvTkk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsVUFBQTtBQWxOTjtBQW9OTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0FBbE5SO0FBb05RO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBLzdCQztFQWc4QkQsZ0JBQUE7QUFsTlY7QUF5TkU7OztFQUdFLGtDQUFBO0VBQ0EsMkJBQUE7RUFDQSwrQkFBQTtFQUNBLDBCQUFBO0VBQ0EsNkJBQUE7QUF2Tko7QUEwTkU7RUFDRSxzREFBQTtBQXhOSjtBQTBOSTtFQUNFLDRGQUFBO0FBeE5OO0FBNE5FO0VBQ0UsNEJBQUE7QUExTko7QUE4TkU7RUFDRSw4QkFBQTtBQTVOSjtBQStORTtFQUNFLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtBQTdOSjtBQStOSTtFQUNFLHlCQUFBO0FBN05OO0FBaU9FO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtBQS9OSiIsInNvdXJjZXNDb250ZW50IjpbIi8vIENvbG9yIFBhbGV0dGUgLSBXaGl0ZSAmIE1pbGQgT3JhbmdlIFRoZW1lXG4kcHJpbWFyeTogI2ZmNmIzNTtcbiRwcmltYXJ5LWxpZ2h0OiAjZmY4YzVhO1xuJHByaW1hcnktZGFyazogI2U1NWEyYjtcbiRzdWNjZXNzOiAjMTBiOTgxO1xuXG4vLyBOZXV0cmFsIENvbG9yc1xuJHdoaXRlOiAjZmZmZmZmO1xuJGdyYXktNTA6ICNmOWZhZmI7XG4kZ3JheS0xMDA6ICNmM2Y0ZjY7XG4kZ3JheS0yMDA6ICNlNWU3ZWI7XG4kZ3JheS0zMDA6ICNkMWQ1ZGI7XG4kZ3JheS00MDA6ICM5Y2EzYWY7XG4kZ3JheS01MDA6ICM2YjcyODA7XG4kZ3JheS02MDA6ICM0YjU1NjM7XG4kZ3JheS03MDA6ICMzNzQxNTE7XG4kZ3JheS04MDA6ICMxZjI5Mzc7XG5cbi8vIFNwYWNpbmcgLSBSZWR1Y2VkIGZvciBwcm9mZXNzaW9uYWwgY29tcGFjdCBsb29rXG4kc3BhY2luZy14czogMC4xMjVyZW07XG4kc3BhY2luZy1zbTogMC4yNXJlbTtcbiRzcGFjaW5nLW1kOiAwLjVyZW07XG4kc3BhY2luZy1sZzogMC43NXJlbTtcbiRzcGFjaW5nLXhsOiAxcmVtO1xuJHNwYWNpbmctMnhsOiAxLjI1cmVtO1xuJHNwYWNpbmctM3hsOiAxLjVyZW07XG5cbi8vIEJvcmRlciBSYWRpdXNcbiRyYWRpdXMtc206IDAuMzc1cmVtO1xuJHJhZGl1cy1tZDogMC41cmVtO1xuJHJhZGl1cy1sZzogMC43NXJlbTtcblxuLy8gU2hhZG93c1xuJHNoYWRvdy1zbTogMCAxcHggMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA1KTtcbiRzaGFkb3ctbWQ6IDAgNHB4IDZweCAtMXB4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAycHggNHB4IC0xcHggcmdiYSgwLCAwLCAwLCAwLjA2KTtcbiRzaGFkb3ctbGc6IDAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuXG4vLyBUcmFuc2l0aW9uc1xuJHRyYW5zaXRpb24tZmFzdDogMC4xNXMgZWFzZS1vdXQ7XG4kdHJhbnNpdGlvbi1ub3JtYWw6IDAuM3MgZWFzZS1vdXQ7XG5cbi8vIE1peGluc1xuQG1peGluIGNhcmQtc3R5bGUge1xuICBiYWNrZ3JvdW5kOiAkd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbGc7XG4gIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoJGdyYXktMjAwLCAwLjgpO1xufVxuXG4vLyBNYWluIENvbnRhaW5lclxuLnNtYXJ0LWRhc2hib2FyZC1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4NnB4KTtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg2cHgpO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkZ3JheS01MCAwJSwgJHdoaXRlIDEwMCUpO1xuICBmb250LWZhbWlseTogJ0ludGVyJywgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAnU2Vnb2UgVUknLCBzYW5zLXNlcmlmO1xuICBjb2xvcjogJGdyYXktODAwO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGFsaWduLWl0ZW1zOiBzdHJldGNoO1xuXG4gICY6OmJlZm9yZSB7XG4gICAgY29udGVudDogJyc7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogMDtcbiAgICBsZWZ0OiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGJvdHRvbTogMDtcbiAgICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSAyMCUsIHJnYmEoJHByaW1hcnksIDAuMDgpIDAlLCB0cmFuc3BhcmVudCA1MCUpO1xuICAgIHBvaW50ZXItZXZlbnRzOiBub25lO1xuICAgIHotaW5kZXg6IDA7XG4gIH1cbn1cblxuLy8gTGVmdCBTaWRlYmFyXG4ubGVmdC1zaWRlYmFyIHtcbiAgd2lkdGg6IDI3MHB4O1xuICBiYWNrZ3JvdW5kOiAkd2hpdGU7XG4gIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICRncmF5LTIwMDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxMDtcbiAgYm94LXNoYWRvdzogJHNoYWRvdy1zbTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAuc2lkZWJhci1zZWN0aW9uIHtcbiAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcblxuICAgICYuZGFzaGJvYXJkLXNlbGVjdG9yLXNlY3Rpb24ge1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRncmF5LTEwMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgfVxuXG4gICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIHBhZGRpbmctdG9wOiAkc3BhY2luZy1tZDtcbiAgICB9XG5cbiAgICAuc2VjdGlvbi1oZWFkZXIge1xuICAgICAgbWFyZ2luLXRvcDogJHNwYWNpbmctbGc7XG4gICAgICBtYXJnaW4tYm90dG9tOiAkc3BhY2luZy1sZztcblxuICAgICAgLmhlYWRlci1jb250ZW50IHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcbiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAgIC5zZWN0aW9uLWljb24ge1xuICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICAgICAgaGVpZ2h0OiAxOHB4O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5zZWN0aW9uLXRpdGxlIHtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4ycHg7XG4gICAgICAgIH1cblxuICAgICAgICAuZmlsdGVyLWJhZGdlIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkcHJpbWFyeTtcbiAgICAgICAgICBjb2xvcjogJHdoaXRlO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgICB3aWR0aDogMS4xMjVyZW07XG4gICAgICAgICAgaGVpZ2h0OiAxLjEyNXJlbTtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjY1cmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgbWFyZ2luLWxlZnQ6IGF1dG87XG4gICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIERhc2hib2FyZCBTZWxlY3RvciBTZWN0aW9uXG4gIC5kYXNoYm9hcmQtc2VsZWN0b3Itc2VjdGlvbiB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgkcHJpbWFyeSwgMC4wNiksIHJnYmEoJHByaW1hcnksIDAuMDIpKTtcbiAgICBib3JkZXI6IDJweCBzb2xpZCByZ2JhKCRwcmltYXJ5LCAwLjE1KTtcbiAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLWxnO1xuICAgIHBhZGRpbmc6ICRzcGFjaW5nLW1kICFpbXBvcnRhbnQ7XG4gICAgbWFyZ2luOiAkc3BhY2luZy1tZDtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgkcHJpbWFyeSwgMC4wOCk7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoJHByaW1hcnksIDAuMDgpLCByZ2JhKCRwcmltYXJ5LCAwLjA0KSk7XG4gICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuMik7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoJHByaW1hcnksIDAuMTIpO1xuICAgIH1cblxuICAgIC5kYXNoYm9hcmQtc2VsZWN0b3Ige1xuICAgICAgd2lkdGg6IDEwMCU7XG5cbiAgICAgIC8vIEVuaGFuY2VkIHN0eWxpbmcgZm9yIHRoZSBkYXNoYm9hcmQgc2VsZWN0b3IgZmllbGRcbiAgICAgIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgICAgaGVpZ2h0OiA0NHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIG1pbi1oZWlnaHQ6IDQ0cHggIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZCAhaW1wb3J0YW50O1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSAhaW1wb3J0YW50O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSwgMCA0cHggMTJweCByZ2JhKCRwcmltYXJ5LCAwLjIpICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgIC5tYXQtbWRjLWZvcm0tZmllbGQtaW5maXgge1xuICAgICAgICBwYWRkaW5nOiAxMnB4IDE2cHggIWltcG9ydGFudDtcbiAgICAgICAgbWluLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBGaWx0ZXJzIFNlY3Rpb25cbiAgLmZpbHRlcnMtc2VjdGlvbiB7XG4gICAgLmZpbHRlcnMtY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgb3ZlcmZsb3cteTogYXV0bztcbiAgICAgIHBhZGRpbmctcmlnaHQ6ICRzcGFjaW5nLXNtO1xuICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbWQ7XG4gICAgICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjgwcHgpO1xuXG4gICAgICAvLyBDdXN0b20gc2Nyb2xsYmFyXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgIHdpZHRoOiA0cHg7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4yKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMyk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmZpbHRlci1ncm91cCB7XG4gICAgICAgIC5maWx0ZXItbGFiZWwge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLXhzO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgY29sb3I6ICRncmF5LTcwMDtcbiAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4ycHg7XG5cbiAgICAgICAgICAubGFiZWwtaWNvbiB7XG4gICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgICAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmxhYmVsLXRleHQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5zZWxlY3Rpb24tY291bnQge1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjY1cmVtO1xuICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMSk7XG4gICAgICAgICAgICBwYWRkaW5nOiAxcHggNHB4O1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbTtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5maWx0ZXItZmllbGQge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmZpbHRlcnMtYWN0aW9ucyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBwYWRkaW5nLXRvcDogJHNwYWNpbmctbWQ7XG4gICAgICBib3JkZXItdG9wOiAxcHggc29saWQgJGdyYXktMTAwO1xuICAgICAgbWFyZ2luLXRvcDogYXV0bztcblxuICAgICAgLnJlc2V0LWJ0biB7XG4gICAgICAgIHdpZHRoOiAxNDBweDtcbiAgICAgICAgaGVpZ2h0OiAzNnB4O1xuICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kO1xuICAgICAgICBjb2xvcjogJGdyYXktNzAwO1xuICAgICAgICBib3JkZXItY29sb3I6ICRncmF5LTMwMDtcbiAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGU7XG4gICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJGdyYXktNTA7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZDtcbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy1zbTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBNYWluIENvbnRlbnQgQXJlYVxuLm1haW4tY29udGVudCB7XG4gIGZsZXg6IDE7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIHBhZGRpbmc6IDA7XG4gIGdhcDogMDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xuICBhbGlnbi1pdGVtczogc3RyZXRjaDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAvLyBHcmFkaWVudCBIaWdobGlnaHQgQmFyXG4gIC5ncmFkaWVudC1oaWdobGlnaHQtYmFyIHtcbiAgICBmbGV4LXNocmluazogMDtcbiAgICBtYXJnaW46IDA7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgkcHJpbWFyeSwgMC4wNiksIHJnYmEoJHByaW1hcnksIDAuMDIpKTtcbiAgICBib3JkZXI6IDJweCBzb2xpZCByZ2JhKCRwcmltYXJ5LCAwLjE1KTtcbiAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLWxnO1xuICAgIG1hcmdpbjogJHNwYWNpbmctbWQ7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoJHByaW1hcnksIDAuMDgpO1xuXG4gICAgLmhpZ2hsaWdodC1jb250ZW50IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLW1kO1xuICAgICAgbWluLWhlaWdodDogNjBweDtcblxuICAgICAgLmhpZ2hsaWdodC1sZWZ0IHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcbiAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIG1pbi13aWR0aDogMjgwcHg7XG5cbiAgICAgICAgLmhpZ2hsaWdodC1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgICB3aWR0aDogMS4xcmVtO1xuICAgICAgICAgIGhlaWdodDogMS4xcmVtO1xuICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6ICRzcGFjaW5nLXhzO1xuICAgICAgICB9XG5cbiAgICAgICAgLmhpZ2hsaWdodC10aXRsZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjk1cmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogJHNwYWNpbmcteHM7XG4gICAgICAgIH1cblxuICAgICAgICAuaGlnaGxpZ2h0LXN0YXR1cyB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgcGFkZGluZzogM3B4IDhweDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLXNtO1xuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMSk7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuXG4gICAgICAgICAgJi5yZWFkeSB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRzdWNjZXNzLCAwLjEpO1xuICAgICAgICAgICAgY29sb3I6ICRzdWNjZXNzO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuaGlnaGxpZ2h0LXJpZ2h0IHtcbiAgICAgICAgZmxleDogMTtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcblxuICAgICAgICAuYWktaW5wdXQtY29udGFpbmVyIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICBtYXgtd2lkdGg6IG5vbmU7XG5cbiAgICAgICAgICAuYWktaW5wdXQtZmllbGQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgICAgICAgLy8gRW5oYW5jZWQgc3R5bGluZyB0byBtYXRjaCBkYXNoYm9hcmQgc2VsZWN0b3JcbiAgICAgICAgICAgIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0NHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDQ0cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSwgMCA0cHggMTJweCByZ2JhKCRwcmltYXJ5LCAwLjIpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5tYXQtbWRjLWZvcm0tZmllbGQtaW5maXgge1xuICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDE2cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgbWluLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaW5wdXQge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XG4gICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLnNlbmQtYnRuIHtcbiAgICAgICAgICAgIHdpZHRoOiA0NHB4O1xuICAgICAgICAgICAgaGVpZ2h0OiA0NHB4O1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJHByaW1hcnk7XG4gICAgICAgICAgICBjb2xvcjogJHdoaXRlO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1tZDtcbiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICRwcmltYXJ5O1xuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoJHByaW1hcnksIDAuMDgpO1xuXG4gICAgICAgICAgICAmOmhvdmVyOm5vdChbZGlzYWJsZWRdKSB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRwcmltYXJ5LWRhcms7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyaztcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTBweCByZ2JhKCRwcmltYXJ5LCAwLjE1KTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJltkaXNhYmxlZF0ge1xuICAgICAgICAgICAgICBvcGFjaXR5OiAwLjU7XG4gICAgICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTMwMDtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gRGFzaGJvYXJkIFNlY3Rpb25cbiAgLmRhc2hib2FyZC1zZWN0aW9uIHtcbiAgICBmbGV4OiAxO1xuICAgIEBpbmNsdWRlIGNhcmQtc3R5bGU7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgbWFyZ2luOiAkc3BhY2luZy1tZDtcblxuICAgIC5kYXNoYm9hcmQtY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBvdmVyZmxvdy15OiBhdXRvO1xuXG4gICAgICAvLyBDdXN0b20gc2Nyb2xsYmFyXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgIHdpZHRoOiA2cHg7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICAgICAgYmFja2dyb3VuZDogJGdyYXktNTA7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4yKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMyk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRW1wdHkgU3RhdGVcbiAgICAgIC5lbXB0eS1zdGF0ZSB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy0yeGw7XG5cbiAgICAgICAgLmVtcHR5LXN0YXRlLWNvbnRlbnQge1xuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICBtYXgtd2lkdGg6IDQ1MHB4O1xuXG4gICAgICAgICAgLmVtcHR5LXN0YXRlLWljb24ge1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbGc7XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAzcmVtO1xuICAgICAgICAgICAgICB3aWR0aDogM3JlbTtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAzcmVtO1xuICAgICAgICAgICAgICBjb2xvcjogJGdyYXktMzAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5lbXB0eS1zdGF0ZS10aXRsZSB7XG4gICAgICAgICAgICBtYXJnaW46IDAgMCAkc3BhY2luZy1zbSAwO1xuICAgICAgICAgICAgZm9udC1zaXplOiAxLjM1cmVtO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmVtcHR5LXN0YXRlLWRlc2NyaXB0aW9uIHtcbiAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBMb2FkaW5nIFN0YXRlXG4gICAgICAubG9hZGluZy1zdGF0ZSB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy0yeGw7XG5cbiAgICAgICAgLmxvYWRpbmctY29udGVudCB7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgIG1heC13aWR0aDogNDAwcHg7XG5cbiAgICAgICAgICAubG9hZGluZy1zcGlubmVyIHtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLWxnO1xuXG4gICAgICAgICAgICAuc3BpbiB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogM3JlbTtcbiAgICAgICAgICAgICAgd2lkdGg6IDNyZW07XG4gICAgICAgICAgICAgIGhlaWdodDogM3JlbTtcbiAgICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgICBhbmltYXRpb246IHNwaW4gMS41cyBsaW5lYXIgaW5maW5pdGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmxvYWRpbmctdGl0bGUge1xuICAgICAgICAgICAgbWFyZ2luOiAwIDAgJHNwYWNpbmctc20gMDtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4zNXJlbTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5sb2FkaW5nLWRlc2NyaXB0aW9uIHtcbiAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBEYXNoYm9hcmQgU3VtbWFyeVxuICAgICAgLmRhc2hib2FyZC1zdW1tYXJ5IHtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmcteGwgJHNwYWNpbmcteGwgJHNwYWNpbmctbGcgJHNwYWNpbmcteGw7XG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0yMDA7XG5cbiAgICAgICAgLnN1bW1hcnktY2FyZHMge1xuICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7XG4gICAgICAgICAgZ2FwOiAkc3BhY2luZy1sZztcblxuICAgICAgICAgIC5zdW1tYXJ5LWNhcmQge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlO1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGdyYXktMjAwO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1sZztcbiAgICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLWxnO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG5cbiAgICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuMik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5zdW1tYXJ5LWljb24ge1xuICAgICAgICAgICAgICB3aWR0aDogNDhweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0OHB4O1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kO1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKCRwcmltYXJ5LCAwLjEpLCByZ2JhKCRwcmltYXJ5LCAwLjA1KSk7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgICBmbGV4LXNocmluazogMDtcblxuICAgICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgICAgICAgICAgICAgd2lkdGg6IDEuNXJlbTtcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDEuNXJlbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnN1bW1hcnktY29udGVudCB7XG4gICAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgICAgICAgICAuc3VtbWFyeS12YWx1ZSB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG4gICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC5zdW1tYXJ5LWxhYmVsIHtcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTYwMDtcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQ2hhcnRzIENvbnRhaW5lclxuICAgICAgLmNoYXJ0cy1jb250YWluZXIge1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy14bDtcbiAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCg0MDBweCwgMWZyKSk7XG4gICAgICAgIGdhcDogJHNwYWNpbmcteGw7XG4gICAgICAgIGFsaWduLWNvbnRlbnQ6IHN0YXJ0O1xuXG4gICAgICAgIDo6bmctZGVlcCAuY2hhcnQtY29udGFpbmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGU7XG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGdyYXktMjAwO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbGc7XG4gICAgICAgICAgcGFkZGluZzogJHNwYWNpbmctbGc7XG4gICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbTtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuXG4gICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjIpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5jaGFydC1oZWFkZXIge1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbGc7XG4gICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogJHNwYWNpbmctbWQ7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGdyYXktMTAwO1xuXG4gICAgICAgICAgICBoMyB7XG4gICAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY2FudmFzIHtcbiAgICAgICAgICAgIG1heC13aWR0aDogMTAwJTtcbiAgICAgICAgICAgIGhlaWdodDogMzAwcHggIWltcG9ydGFudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gQW5pbWF0aW9uc1xuQGtleWZyYW1lcyBzcGluIHtcbiAgMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxufVxuXG4vLyBSZXNwb25zaXZlIERlc2lnblxuQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAuc21hcnQtZGFzaGJvYXJkLWNvbnRhaW5lciB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0NHB4KTtcbiAgICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDRweCk7XG4gIH1cblxuICAubGVmdC1zaWRlYmFyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRncmF5LTIwMDtcbiAgICBmbGV4LXNocmluazogMDtcbiAgICBtYXgtaGVpZ2h0OiA0NXZoO1xuICAgIG92ZXJmbG93LXk6IGF1dG87XG5cbiAgICAuc2lkZWJhci1zZWN0aW9uIHtcbiAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtICRzcGFjaW5nLW1kO1xuICAgIH1cblxuICAgIC5kYXNoYm9hcmQtc2VsZWN0b3Itc2VjdGlvbiB7XG4gICAgICBtYXJnaW46ICRzcGFjaW5nLXNtO1xuICAgIH1cblxuICAgIC5maWx0ZXJzLXNlY3Rpb24ge1xuICAgICAgLmZpbHRlcnMtY29udGVudCB7XG4gICAgICAgIG1heC1oZWlnaHQ6IDIwMHB4O1xuICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtO1xuICAgIGZsZXg6IDE7XG4gICAgbWluLWhlaWdodDogMDtcbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmxlZnQtc2lkZWJhciB7XG4gICAgLnNpZGViYXItc2VjdGlvbiB7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1zbTtcbiAgICB9XG5cbiAgICAuZmlsdGVycy1zZWN0aW9uIHtcbiAgICAgIC5maWx0ZXJzLWNvbnRlbnQge1xuICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgfVxuXG4gICAgICAuZmlsdGVycy1hY3Rpb25zIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcblxuICAgICAgICAucmVzZXQtYnRuLFxuICAgICAgICAuYXBwbHktYnRuIHtcbiAgICAgICAgICBmbGV4OiBub25lO1xuICAgICAgICAgIGhlaWdodDogMzJweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtO1xuICAgIGdhcDogJHNwYWNpbmctc207XG5cbiAgICAuZ3JhZGllbnQtaGlnaGxpZ2h0LWJhciB7XG4gICAgICAuaGlnaGxpZ2h0LWNvbnRlbnQge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcbiAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG5cbiAgICAgICAgLmhpZ2hsaWdodC1sZWZ0IHtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBhbGlnbi1zZWxmOiBjZW50ZXI7XG4gICAgICAgIH1cblxuICAgICAgICAuaGlnaGxpZ2h0LXJpZ2h0IHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgIC5haS1pbnB1dC1jb250YWluZXIge1xuICAgICAgICAgICAgbWF4LXdpZHRoOiBub25lO1xuXG4gICAgICAgICAgICAuc2VuZC1idG4ge1xuICAgICAgICAgICAgICB3aWR0aDogNDBweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5kYXNoYm9hcmQtc2VjdGlvbiB7XG4gICAgICAuZGFzaGJvYXJkLWNvbnRlbnQge1xuICAgICAgICAuZW1wdHktc3RhdGUge1xuICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLWxnO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIEFuZ3VsYXIgTWF0ZXJpYWwgT3ZlcnJpZGVzXG46Om5nLWRlZXAge1xuICAvLyBGb3JtIEZpZWxkIFN0eWxpbmdcbiAgLm1hdC1tZGMtZm9ybS1maWVsZCB7XG4gICAgd2lkdGg6IDEwMCU7XG5cbiAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgYmFja2dyb3VuZDogJGdyYXktNTAgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc20gIWltcG9ydGFudDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRncmF5LTIwMCAhaW1wb3J0YW50O1xuICAgICAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuICAgICAgaGVpZ2h0OiAzOHB4ICFpbXBvcnRhbnQ7XG4gICAgICBtaW4taGVpZ2h0OiAzOHB4ICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGUgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgIWltcG9ydGFudDtcbiAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cblxuICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoJHByaW1hcnksIDAuMSkgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAubWF0LW1kYy1mb3JtLWZpZWxkLWluZml4IHtcbiAgICAgIHBhZGRpbmc6IDhweCAxMnB4ICFpbXBvcnRhbnQ7XG4gICAgICBtaW4taGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtZm9ybS1maWVsZC1zdWJzY3JpcHQtd3JhcHBlciB7XG4gICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1kYy1ub3RjaGVkLW91dGxpbmUge1xuICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC8vIElucHV0IHN0eWxpbmdcbiAgICAubWF0LW1kYy1pbnB1dC1lbGVtZW50IHtcbiAgICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkZ3JheS04MDAgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDtcblxuICAgICAgJjo6cGxhY2Vob2xkZXIge1xuICAgICAgICBjb2xvcjogJGdyYXktNDAwICFpbXBvcnRhbnQ7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDAgIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgJjpkaXNhYmxlZCB7XG4gICAgICAgIGNvbG9yOiAkZ3JheS00MDAgIWltcG9ydGFudDtcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTZWxlY3Qgc3R5bGluZ1xuICAgIC5tYXQtbWRjLXNlbGVjdCB7XG4gICAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogJGdyYXktODAwICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLy8gU2VsZWN0IHRyaWdnZXIgc3R5bGluZyBmb3IgZGFzaGJvYXJkIHNlbGVjdG9yXG4gICAgJi5kYXNoYm9hcmQtc2VsZWN0b3IgLm1hdC1tZGMtc2VsZWN0LXRyaWdnZXIge1xuICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgbWluLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5tYXQtbWRjLXNlbGVjdC1hcnJvdy13cmFwcGVyIHtcbiAgICAgIHRyYW5zZm9ybTogbm9uZSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5tYXQtbWRjLXNlbGVjdC1hcnJvdyB7XG4gICAgICBjb2xvcjogJGdyYXktNTAwICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXNpemU6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtc2VsZWN0LWFycm93IHtcbiAgICAgIGNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICY6aG92ZXIgLm1hdC1tZGMtc2VsZWN0LWFycm93IHtcbiAgICAgIGNvbG9yOiAkZ3JheS02MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAvLyBQcmVmaXggaWNvbiBzdHlsaW5nXG4gICAgLm1hdC1tZGMtZm9ybS1maWVsZC1pY29uLXByZWZpeCB7XG4gICAgICAuaW5wdXQtcHJlZml4LWljb24ge1xuICAgICAgICBjb2xvcjogJGdyYXktNTAwICFpbXBvcnRhbnQ7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW0gIWltcG9ydGFudDtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy1zbSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFNlbGVjdCBPcHRpb25zXG4gIC5tYXQtbWRjLW9wdGlvbiB7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbSAhaW1wb3J0YW50O1xuICAgIG1hcmdpbjogMCA0cHggIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgICBtaW4taGVpZ2h0OiA0OHB4ICFpbXBvcnRhbnQ7XG4gICAgbGluZS1oZWlnaHQ6IDEuMiAhaW1wb3J0YW50O1xuICAgIHBhZGRpbmc6IDhweCAxMnB4ICFpbXBvcnRhbnQ7XG4gICAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50O1xuICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1mYXN0ICFpbXBvcnRhbnQ7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMDgpICFpbXBvcnRhbnQ7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMnB4KSAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICYubWF0LW1kYy1vcHRpb24tYWN0aXZlIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMTIpICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAub3B0aW9uLWljb24ge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy1zbSAhaW1wb3J0YW50O1xuICAgICAgZm9udC1zaXplOiAwLjlyZW0gIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkZ3JheS01MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAvLyBEYXNoYm9hcmQgb3B0aW9uIHN0eWxpbmdcbiAgICAuZGFzaGJvYXJkLW9wdGlvbiB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgcGFkZGluZzogNHB4IDA7XG5cbiAgICAgIC5kYXNoYm9hcmQtaW5mbyB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogM3B4O1xuICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgIC5kYXNoYm9hcmQtbmFtZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjM7XG4gICAgICAgIH1cblxuICAgICAgICAuZGFzaGJvYXJkLWRlc2Mge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICAgICAgICBjb2xvcjogJGdyYXktNTAwO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjM7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFNlbGVjdGVkIGRhc2hib2FyZCBzdHlsaW5nIChjbG9zZWQgc3RhdGUpXG4gICAgLnNlbGVjdGVkLWRhc2hib2FyZCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgcGFkZGluZzogMDtcblxuICAgICAgLnNlbGVjdGVkLWluZm8ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBoZWlnaHQ6IDEwMCU7XG5cbiAgICAgICAgLnNlbGVjdGVkLW5hbWUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEJ1dHRvbiBTdHlsaW5nXG4gIC5tYXQtbWRjLWJ1dHRvbixcbiAgLm1hdC1tZGMtcmFpc2VkLWJ1dHRvbixcbiAgLm1hdC1tZGMtc3Ryb2tlZC1idXR0b24ge1xuICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc20gIWltcG9ydGFudDtcbiAgICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7XG4gICAgdGV4dC10cmFuc2Zvcm06IG5vbmUgIWltcG9ydGFudDtcbiAgICBtaW4td2lkdGg6IGF1dG8gIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgfVxuXG4gIC5tYXQtbWRjLXJhaXNlZC1idXR0b24ge1xuICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc20gIWltcG9ydGFudDtcblxuICAgICY6aG92ZXIge1xuICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZCAhaW1wb3J0YW50O1xuICAgIH1cbiAgfVxuXG4gIC5tYXQtbWRjLXN0cm9rZWQtYnV0dG9uIHtcbiAgICBib3JkZXItd2lkdGg6IDFweCAhaW1wb3J0YW50O1xuICB9XG5cbiAgLy8gRGF0ZXBpY2tlclxuICAubWF0LWRhdGVwaWNrZXItaW5wdXQge1xuICAgIGZvbnQtc2l6ZTogMC44NzVyZW0gIWltcG9ydGFudDtcbiAgfVxuXG4gIC5tYXQtZGF0ZXBpY2tlci10b2dnbGUge1xuICAgIHdpZHRoOiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgY29sb3I6ICRncmF5LTUwMCAhaW1wb3J0YW50O1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICB9XG4gIH1cblxuICAubWF0LWRhdGVwaWNrZXItdG9nZ2xlLWRlZmF1bHQtaWNvbiB7XG4gICAgd2lkdGg6IDE2cHggIWltcG9ydGFudDtcbiAgICBoZWlnaHQ6IDE2cHggIWltcG9ydGFudDtcbiAgfVxufVxuXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "ReactiveFormsModule", "FormControl", "first", "takeUntil", "startWith", "map", "Subject", "Observable", "Chart", "registerables", "NgxMatSelectSearchModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r11", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r10", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "selectedLocations", "length", "location_r12", "value", "baseDate_r13", "displayName", "ctx_r6", "getLoadingMessage", "title", "description", "ctx_r7", "getEmptyStateMessage", "item_r15", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_86_div_2_Template", "ctx_r8", "getSummaryItems", "ɵɵelement", "register", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "smartDashboardService", "cdr", "destroy$", "tabs", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "dashboardData", "charts", "isLoading", "dashboardConfig", "locationFilterCtrl", "filteredLocations", "allLocationsSelected", "ngOnInit", "user", "getCurrentUser", "setDefaultDates", "initializeComponent", "detectChanges", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "loadDashboardTabs", "loadBaseDateOptions", "loadDashboardConfig", "loadLocations", "dashboardData$", "pipe", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON>", "loading$", "loading", "getDashboardTabs", "response", "status", "active", "error", "setDefaultTabs", "getBaseDateOptions", "setDefaultBaseDates", "getDashboardConfig", "getDefaultDashboardTabs", "getDefaultBaseDateOptions", "today", "Date", "year", "getFullYear", "month", "getMonth", "firstDayOfMonth", "formatDateForInput", "date", "String", "padStart", "day", "getDate", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "clearDashboardData", "sendMessage", "trim", "areAllFiltersValid", "generateDashboard", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "tenantId", "getLocations", "res", "result", "branches", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "location", "setupLocationFilter", "resetFilters", "filters", "baseDate", "validateFilters", "valueChanges", "filterLocations", "filterValue", "toLowerCase", "filter", "includes", "toggleSelectAllLocations", "onLocationSelectionChange", "<PERSON><PERSON><PERSON><PERSON>", "currentTab", "dashboardType", "request", "user_query", "dashboard_type", "tenant_id", "chart", "destroy", "chartsContainer", "nativeElement", "innerHTML", "setTimeout", "chartConfig", "createChart", "summary", "canvas", "document", "createElement", "width", "height", "chartContainer", "className", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "chartOptions", "getChartConfig", "type", "mergedOptions", "options", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "i3", "SmartDashboardService", "ChangeDetectorRef", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_valueChange_27_listener", "SmartDashboardComponent_mat_option_28_Template", "SmartDashboardComponent_Template_mat_select_valueChange_36_listener", "SmartDashboardComponent_mat_option_37_Template", "SmartDashboardComponent_Template_input_ngModelChange_45_listener", "SmartDashboardComponent_Template_input_ngModelChange_56_listener", "SmartDashboardComponent_Template_button_click_61_listener", "SmartDashboardComponent_Template_input_ngModelChange_78_listener", "SmartDashboardComponent_Template_input_keydown_78_listener", "SmartDashboardComponent_Template_button_click_79_listener", "SmartDashboardComponent_div_84_Template", "SmartDashboardComponent_div_85_Template", "SmartDashboardComponent_div_86_Template", "SmartDashboardComponent_div_87_Template", "_r4", "_r5", "ɵɵclassProp", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "MatSuffix", "i8", "MatSelect", "MatSelectTrigger", "i9", "MatOption", "i10", "MatInput", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, OnDestroy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { SmartDashboardService, DashboardTab, BaseDate, DashboardFilters } from '../../services/smart-dashboard.service';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, ChartType, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NgxMatSelectSearchModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('chartsContainer', { static: false }) chartsContainer!: ElementRef;\n\n  private destroy$ = new Subject<void>();\n\n  tabs: DashboardTab[] = [];\n  selectedTab = 0;\n  user: any;\n  locations: any[] = [];\n  baseDates: BaseDate[] = [];\n  selectedLocations: string[] = [];\n  selectedBaseDate = '';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n  dashboardData: any = null;\n  charts: Chart[] = [];\n  isLoading = false;\n  dashboardConfig: any = null;\n\n  locationFilterCtrl = new FormControl();\n  filteredLocations: Observable<any[]> = new Observable();\n  allLocationsSelected = true;\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService,\n    private smartDashboardService: SmartDashboardService,\n    private cdr: ChangeDetectorRef\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.cdr.detectChanges();\n  }\n\n  ngAfterViewInit(): void {}\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n\n  private initializeComponent(): void {\n    this.loadDashboardTabs();\n    this.loadBaseDateOptions();\n    this.loadDashboardConfig();\n    this.loadLocations();\n\n    this.smartDashboardService.dashboardData$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.dashboardData = data;\n        if (data) {\n          this.renderCharts();\n        }\n        this.cdr.detectChanges();\n      });\n\n    this.smartDashboardService.loading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n        this.cdr.detectChanges();\n      });\n  }\n\n  private loadDashboardTabs(): void {\n    this.smartDashboardService.getDashboardTabs()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.tabs = response.data;\n            if (this.tabs.length > 0) {\n              this.tabs[0].active = true;\n            }\n          }\n        },\n        error: () => {\n          this.setDefaultTabs();\n        }\n      });\n  }\n\n  private loadBaseDateOptions(): void {\n    this.smartDashboardService.getBaseDateOptions()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.baseDates = response.data;\n            if (this.baseDates.length > 0) {\n              this.selectedBaseDate = this.baseDates[0].value;\n            }\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.setDefaultBaseDates();\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private loadDashboardConfig(): void {\n    this.smartDashboardService.getDashboardConfig()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.dashboardConfig = response.data;\n          }\n        },\n        error: () => {}\n      });\n  }\n\n  private setDefaultTabs(): void {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n\n  private setDefaultBaseDates(): void {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n\n  private setDefaultDates(): void {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n\n    // Create first day of current month\n    const firstDayOfMonth = new Date(year, month, 1);\n\n    // Format dates properly\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n      this.generateDashboard();\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: true\n              }));\n              this.selectedLocations = this.locations.map(location => location.value);\n              this.setupLocationFilter();\n            } else {\n              this.locations = [];\n              this.selectedLocations = [];\n            }\n            this.cdr.detectChanges();\n          },\n          error: () => {\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.locations.forEach(location => location.checked = true);\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n\n\n\n  getDashboardDescription(index: number): string {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n\n  areAllFiltersValid(): boolean {\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n\n  setupLocationFilter(): void {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(\n      startWith(''),\n      map(value => this.filterLocations(value || ''))\n    );\n  }\n\n  private filterLocations(value: string): any[] {\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location =>\n      location.label.toLowerCase().includes(filterValue)\n    );\n  }\n\n  toggleSelectAllLocations(): void {\n    this.allLocationsSelected = !this.allLocationsSelected;\n\n    if (this.allLocationsSelected) {\n      this.selectedLocations = this.locations.map(location => location.value);\n      this.locations.forEach(location => location.checked = true);\n    } else {\n      this.selectedLocations = [];\n      this.locations.forEach(location => location.checked = false);\n    }\n    this.cdr.detectChanges();\n  }\n\n  onLocationSelectionChange(selectedValues: any[]): void {\n    this.selectedLocations = selectedValues;\n    this.locations.forEach(location => {\n      location.checked = selectedValues.includes(location.value);\n    });\n    this.allLocationsSelected = selectedValues.length === this.locations.length;\n    this.cdr.detectChanges();\n  }\n\n  getLoadingMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n\n  getEmptyStateMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n\n  generateDashboard(): void {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n\n    this.clearCharts();\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || ''\n    };\n\n    this.smartDashboardService.generateDashboard(request)\n      .pipe(first())\n      .subscribe({\n        next: () => {},\n        error: () => {}\n      });\n  }\n\n  clearCharts(): void {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n\n  renderCharts(): void {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n\n    this.clearCharts();\n\n    setTimeout(() => {\n      this.dashboardData.charts.forEach((chartConfig: any) => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n\n  getSummaryItems(): any[] {\n    if (!this.dashboardData?.summary) {\n      return [];\n    }\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    return this.smartDashboardService.getSummaryItems(\n      this.dashboardData.summary,\n      dashboardType\n    );\n  }\n\n  createChart(chartConfig: any): void {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n\n      const chart = new Chart(ctx, {\n        type: chartConfig.type as ChartType,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n\n      this.charts.push(chart);\n    }\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants *</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Smart Dashboard Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"areAllFiltersValid()\">\n            {{ areAllFiltersValid() ? 'Ready to analyze' : 'Please fill all required filters' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask me about your business data...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"!areAllFiltersValid()\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || !areAllFiltersValid()\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-content\">\n        <!-- Loading State -->\n        <div class=\"loading-state\" *ngIf=\"isLoading\">\n          <div class=\"loading-content\">\n            <div class=\"loading-spinner\">\n              <mat-icon class=\"spin\">refresh</mat-icon>\n            </div>\n            <h4 class=\"loading-title\">{{ getLoadingMessage().title }}</h4>\n            <p class=\"loading-description\">\n              {{ getLoadingMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!isLoading && !dashboardData\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">{{ getEmptyStateMessage().title }}</h4>\n            <p class=\"empty-state-description\">\n              {{ getEmptyStateMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Dashboard Summary -->\n        <div class=\"dashboard-summary\" *ngIf=\"!isLoading && dashboardData?.summary\">\n          <div class=\"summary-cards\">\n            <div class=\"summary-card\" *ngFor=\"let item of getSummaryItems()\">\n              <div class=\"summary-icon\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n              </div>\n              <div class=\"summary-content\">\n                <div class=\"summary-value\">{{ item.value }}</div>\n                <div class=\"summary-label\">{{ item.label }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"!isLoading && dashboardData\" #chartsContainer>\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAI9E,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjE,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,KAAK,EAAaC,aAAa,QAAQ,UAAU;AAC1D,SAASC,wBAAwB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;ICHtDC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,KAAA,EAAgC;;;;;IAwBjEL,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEf,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAY,YAAA,CAAAC,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAI,YAAA,CAAAP,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAD,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,YAAA,CAAAC,WAAA,MACF;;;;;IAgGNnB,EAAA,CAAAC,cAAA,cAA6C;IAGhBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHsBH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,iBAAA,CAAAa,MAAA,CAAAC,iBAAA,GAAAC,KAAA,CAA+B;IAEvDtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,MAAA,CAAAC,iBAAA,GAAAE,WAAA,MACF;;;;;IAKJvB,EAAA,CAAAC,cAAA,cAA8D;IAG9CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAH0BH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,iBAAA,CAAAiB,MAAA,CAAAC,oBAAA,GAAAH,KAAA,CAAkC;IAE9DtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAY,MAAA,CAAAC,oBAAA,GAAAF,WAAA,MACF;;;;;IAOAvB,EAAA,CAAAC,cAAA,cAAiE;IAEnDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJvCH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAC,IAAA,CAAe;IAGE3B,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAT,KAAA,CAAgB;IAChBjB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAjB,KAAA,CAAgB;;;;;IARnDT,EAAA,CAAAC,cAAA,cAA4E;IAExED,EAAA,CAAA4B,UAAA,IAAAC,6CAAA,kBAQM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IATuCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAC,eAAA,GAAoB;;;;;IAanE/B,EAAA,CAAAgC,SAAA,kBAEM;;;ADzLdnC,KAAK,CAACoC,QAAQ,CAAC,GAAGnC,aAAa,CAAC;AAEhC,MAoBaoC,uBAAuB;EAwBlCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,qBAA4C,EAC5CC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IAzBL,KAAAC,QAAQ,GAAG,IAAI7C,OAAO,EAAQ;IAEtC,KAAA8C,IAAI,GAAmB,EAAE;IACzB,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA9B,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,eAAe,GAAQ,IAAI;IAE3B,KAAAC,kBAAkB,GAAG,IAAI/D,WAAW,EAAE;IACtC,KAAAgE,iBAAiB,GAAsB,IAAI1D,UAAU,EAAE;IACvD,KAAA2D,oBAAoB,GAAG,IAAI;EAOvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IAC7C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACrB,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA,GAAU;EAEzBC,WAAWA,CAAA;IACT,IAAI,CAACvB,QAAQ,CAACwB,IAAI,EAAE;IACpB,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQN,mBAAmBA,CAAA;IACzB,IAAI,CAACO,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAAChC,qBAAqB,CAACiC,cAAc,CACtCC,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACzB,aAAa,GAAGyB,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,YAAY,EAAE;;MAErB,IAAI,CAACpC,GAAG,CAACsB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACvB,qBAAqB,CAACsC,QAAQ,CAChCJ,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC,CAAC,CAC9BiC,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC1B,SAAS,GAAG0B,OAAO;MACxB,IAAI,CAACtC,GAAG,CAACsB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACN;EAEQM,iBAAiBA,CAAA;IACvB,IAAI,CAAC7B,qBAAqB,CAACwC,gBAAgB,EAAE,CAC1CN,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACvC,IAAI,GAAGsC,QAAQ,CAACL,IAAI;UACzB,IAAI,IAAI,CAACjC,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAACwC,MAAM,GAAG,IAAI;;;MAGhC,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACC,cAAc,EAAE;MACvB;KACD,CAAC;EACN;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAAC9B,qBAAqB,CAAC8C,kBAAkB,EAAE,CAC5CZ,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACpC,SAAS,GAAGmC,QAAQ,CAACL,IAAI;UAC9B,IAAI,IAAI,CAAC9B,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;;QAGnD,IAAI,CAACsB,GAAG,CAACsB,aAAa,EAAE;MAC1B,CAAC;MACDqB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACG,mBAAmB,EAAE;QAC1B,IAAI,CAAC9C,GAAG,CAACsB,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQQ,mBAAmBA,CAAA;IACzB,IAAI,CAAC/B,qBAAqB,CAACgD,kBAAkB,EAAE,CAC5Cd,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC5B,eAAe,GAAG2B,QAAQ,CAACL,IAAI;;MAExC,CAAC;MACDQ,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEQC,cAAcA,CAAA;IACpB,IAAI,CAAC1C,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAACiD,uBAAuB,EAAE;IAChE,IAAI,IAAI,CAAC9C,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAACwC,MAAM,GAAG,IAAI;;EAE9B;EAEQI,mBAAmBA,CAAA;IACzB,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAACkD,yBAAyB,EAAE;IACvE,IAAI,IAAI,CAAC5C,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;EAEnD;EAEQ0C,eAAeA,CAAA;IACrB,MAAM8B,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,EAAE;IAChC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,QAAQ,EAAE;IAE9B;IACA,MAAMC,eAAe,GAAG,IAAIL,IAAI,CAACC,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC/C,SAAS,GAAG,IAAI,CAACkD,kBAAkB,CAACD,eAAe,CAAC;IACzD,IAAI,CAAChD,OAAO,GAAG,IAAI,CAACiD,kBAAkB,CAACP,KAAK,CAAC;EAC/C;EAEQO,kBAAkBA,CAACC,IAAU;IACnC,MAAMN,IAAI,GAAGM,IAAI,CAACL,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGK,MAAM,CAACD,IAAI,CAACH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGR,IAAI,IAAIE,KAAK,IAAIO,GAAG,EAAE;EAClC;EAEAE,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC7D,WAAW,GAAG6D,KAAK;IACxB,IAAI,CAAC9D,IAAI,CAAC+D,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACxB,MAAM,GAAGyB,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF,IAAI,CAACjE,qBAAqB,CAACqE,kBAAkB,EAAE;IAC/C,IAAI,CAACzC,WAAW,EAAE;EACpB;EAEA0C,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5D,WAAW,CAAC6D,IAAI,EAAE,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;MACxD,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAAC/D,WAAW,GAAG,EAAE;;EAEzB;EAEAgE,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACR,WAAW,EAAE;;EAEtB;EAIAtC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4D,QAAQ,EAAE;MACnC,IAAI,CAAChF,gBAAgB,CAACiF,YAAY,CAAC,IAAI,CAAC7D,IAAI,CAAC4D,QAAQ,CAAC,CACnD7C,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;QACTT,IAAI,EAAGuD,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAAC9E,SAAS,GAAG4E,GAAG,CAACE,QAAQ,CAAC/H,GAAG,CAAEgI,MAAW,KAAM;cAClDzG,KAAK,EAAEyG,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEpH,KAAK,EAAEiH,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;YACH,IAAI,CAAClH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAACjD,GAAG,CAACuI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;YACvE,IAAI,CAACiH,mBAAmB,EAAE;WAC3B,MAAM;YACL,IAAI,CAACvF,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC7B,iBAAiB,GAAG,EAAE;;UAE7B,IAAI,CAACyB,GAAG,CAACsB,aAAa,EAAE;QAC1B,CAAC;QACDqB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvC,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEAwF,YAAYA,CAAA;IACV,IAAI,CAACrH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAACjD,GAAG,CAACuI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;IACvE,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC7B,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK,GAAG,EAAE;IAChF,IAAI,CAAC0C,eAAe,EAAE;IACtB,IAAI,CAAChB,SAAS,CAAC6D,OAAO,CAACyB,QAAQ,IAAIA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAAC;IAC3D,IAAI,CAAC1F,qBAAqB,CAACqE,kBAAkB,EAAE;IAC/C,IAAI,CAACzC,WAAW,EAAE;IAClB,IAAI,CAAC3B,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAIAlD,uBAAuBA,CAAC4F,KAAa;IACnC,IAAI,IAAI,CAAC9D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8D,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI,CAAC9D,IAAI,CAAC8D,KAAK,CAAC,CAAChF,WAAW;;IAErC,OAAO,8BAA8B;EACvC;EAEAuF,kBAAkBA,CAAA;IAChB,MAAMsB,OAAO,GAAqB;MAChCzF,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCuH,QAAQ,EAAE,IAAI,CAACxF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IACD,OAAO,IAAI,CAACT,qBAAqB,CAACgG,eAAe,CAACF,OAAO,CAAC;EAC5D;EAEAF,mBAAmBA,CAAA;IACjB,IAAI,CAAC5E,iBAAiB,GAAG,IAAI,CAACD,kBAAkB,CAACkF,YAAY,CAAC/D,IAAI,CAChE/E,SAAS,CAAC,EAAE,CAAC,EACbC,GAAG,CAACuB,KAAK,IAAI,IAAI,CAACuH,eAAe,CAACvH,KAAK,IAAI,EAAE,CAAC,CAAC,CAChD;EACH;EAEQuH,eAAeA,CAACvH,KAAa;IACnC,MAAMwH,WAAW,GAAGxH,KAAK,CAACyH,WAAW,EAAE;IACvC,OAAO,IAAI,CAAC/F,SAAS,CAACgG,MAAM,CAACV,QAAQ,IACnCA,QAAQ,CAACxH,KAAK,CAACiI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACnD;EACH;EAEAI,wBAAwBA,CAAA;IACtB,IAAI,CAACtF,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IAEtD,IAAI,IAAI,CAACA,oBAAoB,EAAE;MAC7B,IAAI,CAACzC,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAACjD,GAAG,CAACuI,QAAQ,IAAIA,QAAQ,CAAChH,KAAK,CAAC;MACvE,IAAI,CAAC0B,SAAS,CAAC6D,OAAO,CAACyB,QAAQ,IAAIA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAAC;KAC5D,MAAM;MACL,IAAI,CAAClH,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAAC6B,SAAS,CAAC6D,OAAO,CAACyB,QAAQ,IAAIA,QAAQ,CAACD,OAAO,GAAG,KAAK,CAAC;;IAE9D,IAAI,CAACzF,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAiF,yBAAyBA,CAACC,cAAqB;IAC7C,IAAI,CAACjI,iBAAiB,GAAGiI,cAAc;IACvC,IAAI,CAACpG,SAAS,CAAC6D,OAAO,CAACyB,QAAQ,IAAG;MAChCA,QAAQ,CAACD,OAAO,GAAGe,cAAc,CAACH,QAAQ,CAACX,QAAQ,CAAChH,KAAK,CAAC;IAC5D,CAAC,CAAC;IACF,IAAI,CAACsC,oBAAoB,GAAGwF,cAAc,CAAChI,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;IAC3E,IAAI,CAACwB,GAAG,CAACsB,aAAa,EAAE;EAC1B;EAEAxC,iBAAiBA,CAAA;IACf,MAAM2H,UAAU,GAAG,IAAI,CAACvG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMuG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAC/H,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACjB,iBAAiB,CAAC4H,aAAa,CAAC;EACpE;EAEAxH,oBAAoBA,CAAA;IAClB,MAAMuH,UAAU,GAAG,IAAI,CAACvG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMuG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAC/H,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACb,oBAAoB,CAACwH,aAAa,CAAC;EACvE;EAEAlC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE,EAAE;MAC9B;;IAGF,MAAMsB,OAAO,GAAqB;MAChCzF,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjCuH,QAAQ,EAAE,IAAI,CAACxF,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IAED,IAAI,CAACmB,WAAW,EAAE;IAElB,MAAM8E,UAAU,GAAG,IAAI,CAACvG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMuG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAC/H,KAAK,GAAG,UAAU;IAEhE,MAAMiI,OAAO,GAAG;MACdd,OAAO,EAAEA,OAAO;MAChBe,UAAU,EAAE,IAAI,CAACnG,WAAW;MAC5BoG,cAAc,EAAEH,aAAa;MAC7BI,SAAS,EAAE,IAAI,CAAC5F,IAAI,EAAE4D,QAAQ,IAAI;KACnC;IAED,IAAI,CAAC/E,qBAAqB,CAACyE,iBAAiB,CAACmC,OAAO,CAAC,CAClD1E,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdkB,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEAhB,WAAWA,CAAA;IACT,IAAI,CAAChB,MAAM,CAACsD,OAAO,CAAC8C,KAAK,IAAG;MAC1B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;IACF,IAAI,CAACrG,MAAM,GAAG,EAAE;IAEhB,IAAI,IAAI,CAACsG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;;EAErD;EAEA/E,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC1B,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,EAAE;MACrD;;IAGF,IAAI,CAACgB,WAAW,EAAE;IAElByF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1G,aAAa,CAACC,MAAM,CAACsD,OAAO,CAAEoD,WAAgB,IAAI;QACrD,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA7H,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAE6G,OAAO,EAAE;MAChC,OAAO,EAAE;;IAGX,MAAMd,UAAU,GAAG,IAAI,CAACvG,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMuG,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAAC/H,KAAK,GAAG,UAAU;IAEhE,OAAO,IAAI,CAACqB,qBAAqB,CAACP,eAAe,CAC/C,IAAI,CAACkB,aAAa,CAAC6G,OAAO,EAC1Bb,aAAa,CACd;EACH;EAEAY,WAAWA,CAACD,WAAgB;IAC1B,MAAMG,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAAClC,EAAE,GAAG,SAAS+B,WAAW,CAAC/B,EAAE,EAAE;IACrCkC,MAAM,CAACG,KAAK,GAAG,GAAG;IAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;IAEnB,MAAMC,cAAc,GAAGJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACpDG,cAAc,CAACC,SAAS,GAAG,iBAAiB;IAC5CD,cAAc,CAACV,SAAS,GAAG;;cAEjBE,WAAW,CAACtI,KAAK;;KAE1B;IACD8I,cAAc,CAACE,WAAW,CAACP,MAAM,CAAC;IAElC,IAAI,IAAI,CAACP,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACa,WAAW,CAACF,cAAc,CAAC;;IAGhE,MAAMG,GAAG,GAAGR,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;IACnC,IAAID,GAAG,EAAE;MACP,MAAME,YAAY,GAAG,IAAI,CAACnI,qBAAqB,CAACoI,cAAc,CAACd,WAAW,CAACe,IAAI,CAAC;MAEhF,MAAMC,aAAa,GAAG;QACpB,GAAGH,YAAY;QACf,IAAIb,WAAW,CAACiB,OAAO,IAAI,EAAE;OAC9B;MAED,MAAMvB,KAAK,GAAG,IAAIzJ,KAAK,CAAC0K,GAAG,EAAE;QAC3BI,IAAI,EAAEf,WAAW,CAACe,IAAiB;QACnCjG,IAAI,EAAEkF,WAAW,CAAClF,IAAI;QACtBmG,OAAO,EAAED;OACV,CAAC;MAEF,IAAI,CAAC1H,MAAM,CAAC4H,IAAI,CAACxB,KAAK,CAAC;;EAE3B;;;uBAzYWpH,uBAAuB,EAAAlC,EAAA,CAAA+K,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjL,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAnL,EAAA,CAAA+K,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAArL,EAAA,CAAA+K,iBAAA,CAAA/K,EAAA,CAAAsL,iBAAA;IAAA;EAAA;;;YAAvBpJ,uBAAuB;MAAAqJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAnB,GAAA;QAAA,IAAAmB,EAAA;;;;;;;;;;;;;;;UCzCpC1L,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAA2L,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAtB,GAAA,CAAA7H,WAAA,GAAAmJ,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBtB,GAAA,CAAAjE,WAAA,CAAAuF,MAAA,CAAA5K,KAAA,CAAyB;UAAA,EAA7C;UACjCjB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAA4B,UAAA,KAAAmK,8CAAA,wBAOa;UACf/L,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIhDH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAA4B,UAAA,KAAAoK,wCAAA,mBAEO;UACThM,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA2L,UAAA,yBAAAM,oEAAAJ,MAAA;YAAA,OAAAtB,GAAA,CAAAzJ,iBAAA,GAAA+K,MAAA;UAAA,EAA6B;UACvC7L,EAAA,CAAA4B,UAAA,KAAAsK,8CAAA,wBAEa;UACflM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAA2L,UAAA,yBAAAQ,oEAAAN,MAAA;YAAA,OAAAtB,GAAA,CAAA1H,gBAAA,GAAAgJ,MAAA;UAAA,EAA4B;UACtC7L,EAAA,CAAA4B,UAAA,KAAAwK,8CAAA,wBAEa;UACfpM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA2L,UAAA,2BAAAU,iEAAAR,MAAA;YAAA,OAAAtB,GAAA,CAAAzH,SAAA,GAAA+I,MAAA;UAAA,EAAuB;UAHzB7L,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA6E;UAE/EhC,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAA2L,UAAA,2BAAAW,iEAAAT,MAAA;YAAA,OAAAtB,GAAA,CAAAxH,OAAA,GAAA8I,MAAA;UAAA,EAAqB;UAHvB7L,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAgC,SAAA,iCAA2E;UAE7EhC,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAA2L,UAAA,mBAAAY,0DAAA;YAAA,OAAShC,GAAA,CAAApC,YAAA,EAAc;UAAA,EAAC;UACnEnI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,eAA6B;UAOrBD,EAAA,CAAA2L,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAtB,GAAA,CAAAvH,WAAA,GAAA6I,MAAA;UAAA,EAAyB,qBAAAY,2DAAAZ,MAAA;YAAA,OACdtB,GAAA,CAAAvD,UAAA,CAAA6E,MAAA,CAAkB;UAAA,EADJ;UAJ3B7L,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAMC;UAFCD,EAAA,CAAA2L,UAAA,mBAAAe,0DAAA;YAAA,OAASnC,GAAA,CAAA3D,WAAA,EAAa;UAAA,EAAC;UAGvB5G,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAG3BD,EAAA,CAAA4B,UAAA,KAAA+K,uCAAA,kBAUM;UAGN3M,EAAA,CAAA4B,UAAA,KAAAgL,uCAAA,kBAUM;UAGN5M,EAAA,CAAA4B,UAAA,KAAAiL,uCAAA,kBAYM;UAGN7M,EAAA,CAAA4B,UAAA,KAAAkL,uCAAA,kBAEM;UACR9M,EAAA,CAAAG,YAAA,EAAM;;;;;UAvMQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAA7H,WAAA,CAAuB;UAIC1C,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAAgK,GAAA,CAAA9H,IAAA,CAAA8H,GAAA,CAAA7H,WAAA,mBAAA6H,GAAA,CAAA9H,IAAA,CAAA8H,GAAA,CAAA7H,WAAA,EAAAjC,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAmK,GAAA,CAAA9H,IAAA,CAAS;UA4BJzC,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAmK,GAAA,CAAAzJ,iBAAA,CAAAC,MAAA,KAAkC;UAKrDf,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAAzJ,iBAAA,CAA6B;UACNd,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAmK,GAAA,CAAA5H,SAAA,CAAY;UAcnC3C,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAA1H,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAmK,GAAA,CAAA3H,SAAA,CAAY;UAgB7C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA2M,GAAA,CAA6B,YAAAxC,GAAA,CAAAzH,SAAA;UAKE9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA2M,GAAA,CAAmB;UAclD/M,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA4M,GAAA,CAA2B,YAAAzC,GAAA,CAAAxH,OAAA;UAKI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA4M,GAAA,CAAiB;UAwBrBhN,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAAiN,WAAA,UAAA1C,GAAA,CAAAzD,kBAAA,GAAoC;UACjE9G,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAA2J,GAAA,CAAAzD,kBAAA,kEACF;UASM9G,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAmK,GAAA,CAAAvH,WAAA,CAAyB,cAAAuH,GAAA,CAAAzD,kBAAA;UAU3B9G,EAAA,CAAAM,SAAA,GAAyD;UAAzDN,EAAA,CAAAI,UAAA,cAAAmK,GAAA,CAAAvH,WAAA,CAAA6D,IAAA,OAAA0D,GAAA,CAAAzD,kBAAA,GAAyD;UAanC9G,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAAmK,GAAA,CAAApH,SAAA,CAAe;UAajBnD,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAApH,SAAA,KAAAoH,GAAA,CAAAtH,aAAA,CAAkC;UAa5BjD,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAApH,SAAA,KAAAoH,GAAA,CAAAtH,aAAA,kBAAAsH,GAAA,CAAAtH,aAAA,CAAA6G,OAAA,EAA0C;UAe3C9J,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAAmK,GAAA,CAAApH,SAAA,IAAAoH,GAAA,CAAAtH,aAAA,CAAiC;;;qBDjLpEtE,YAAY,EAAAuO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxO,aAAa,EACbC,eAAe,EAAAwO,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfzO,aAAa,EAAA0O,EAAA,CAAAC,OAAA,EACb1O,kBAAkB,EAAA2O,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClB5O,eAAe,EAAA6O,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACfhP,cAAc,EAAAiP,GAAA,CAAAC,QAAA,EACdjP,mBAAmB,EAAAkP,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnBpP,mBAAmB,EACnBC,WAAW,EAAAoP,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACXtP,mBAAmB,EACnBU,wBAAwB;MAAA6O,MAAA;IAAA;EAAA;;SAKf1M,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}