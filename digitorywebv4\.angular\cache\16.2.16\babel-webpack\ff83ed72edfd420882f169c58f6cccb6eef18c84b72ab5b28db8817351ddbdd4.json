{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { Chart, registerables } from 'chart.js';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FilterPanelComponent } from './filter-panel.component';\nimport { ChartWrapperComponent } from './chart-wrapper.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/agno-agent.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/chips\";\nimport * as i14 from \"@angular/material/tooltip\";\nimport * as i15 from \"ngx-markdown\";\nconst _c0 = [\"chatContainer\"];\nconst _c1 = [\"messageInput\"];\nfunction AgnoAgentComponent_mat_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const report_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", report_r10.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", report_r10.display_name, \" \");\n  }\n}\nfunction AgnoAgentComponent_div_38_mat_chip_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 42);\n    i0.ɵɵlistener(\"click\", function AgnoAgentComponent_div_38_mat_chip_option_5_Template_mat_chip_option_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const query_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.sendExampleQuery(query_r12));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const query_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", query_r12, \" \");\n  }\n}\nfunction AgnoAgentComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"h4\");\n    i0.ɵɵtext(2, \"Example Queries:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(5, AgnoAgentComponent_div_38_mat_chip_option_5_Template, 4, 1, \"mat-chip-option\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.exampleQueries);\n  }\n}\nfunction AgnoAgentComponent_div_41_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"app-chart-wrapper\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"chartData\", message_r15.chart_data)(\"chartType\", message_r15.chart_type || \"bar\");\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"ai-message\": a1\n  };\n};\nfunction AgnoAgentComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"span\", 46)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 48);\n    i0.ɵɵelement(11, \"markdown\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AgnoAgentComponent_div_41_div_12_Template, 2, 2, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c2, message_r15.type === \"human\", message_r15.type === \"ai\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(message_r15.type === \"human\" ? \"person\" : \"smart_toy\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r15.type === \"human\" ? \"You\" : \"DIGI\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 6, message_r15.created_at, \"short\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"data\", message_r15.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r15.has_visualization && message_r15.chart_data);\n  }\n}\nfunction AgnoAgentComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"mat-spinner\", 54);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"DIGI is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgnoAgentComponent_mat_hint_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1, \"Generate a dashboard first to start chatting\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgnoAgentComponent_div_62_mat_chip_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", column_r20, \" \");\n  }\n}\nfunction AgnoAgentComponent_div_62_mat_chip_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r19.currentSession.dataframe_summary.column_names.length - 10, \" more \");\n  }\n}\nfunction AgnoAgentComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"h3\");\n    i0.ɵɵtext(2, \"Dashboard Generated Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Report Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Data Rows:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Columns:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"h4\");\n    i0.ɵɵtext(20, \"Available Columns:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 58)(22, \"mat-chip-listbox\");\n    i0.ɵɵtemplate(23, AgnoAgentComponent_div_62_mat_chip_option_23_Template, 2, 1, \"mat-chip-option\", 59);\n    i0.ɵɵtemplate(24, AgnoAgentComponent_div_62_mat_chip_option_24_Template, 2, 1, \"mat-chip-option\", 60);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 61)(26, \"mat-icon\", 62);\n    i0.ɵɵtext(27, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29, \"Dashboard is ready! You can now ask questions about your data.\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.reportTypeControl.value, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 5, ctx_r7.currentSession.dataframe_summary.rows), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 7, ctx_r7.currentSession.dataframe_summary.columns), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.currentSession.dataframe_summary.column_names.slice(0, 10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.currentSession.dataframe_summary.column_names.length > 10);\n  }\n}\nfunction AgnoAgentComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h3\");\n    i0.ɵɵtext(2, \"Welcome to Agno Agent Dashboard!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"This is where your data visualizations and previews will appear.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"h4\");\n    i0.ɵɵtext(7, \"Features:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\")(9, \"li\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Dynamic Parameter Collection:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" AI automatically gathers required inputs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"li\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Real-time Dashboard Generation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Instant data visualization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Interactive Chat:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Ask questions and get visual answers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"li\")(22, \"strong\");\n    i0.ɵɵtext(23, \"Multi-Report Support:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Works with all existing report functions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"li\")(26, \"strong\");\n    i0.ɵɵtext(27, \"Persistent Sessions:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Data saved across browser sessions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 66)(30, \"h4\");\n    i0.ɵɵtext(31, \"How to Use:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"ol\")(33, \"li\");\n    i0.ɵɵtext(34, \"Select a report type from the dropdown\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"li\");\n    i0.ɵɵtext(36, \"Describe what data you want to see\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"li\");\n    i0.ɵɵtext(38, \"Let the AI collect required parameters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"li\");\n    i0.ɵɵtext(40, \"View your interactive dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"li\");\n    i0.ɵɵtext(42, \"Ask questions and get visual insights\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AgnoAgentComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"h4\");\n    i0.ɵɵtext(2, \"Latest Visualization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"app-chart-wrapper\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"chartData\", ctx_r9.chartData)(\"chartType\", ctx_r9.chartType);\n  }\n}\nChart.register(...registerables);\nclass AgnoAgentComponent {\n  constructor(agnoService, snackBar) {\n    this.agnoService = agnoService;\n    this.snackBar = snackBar;\n    // Form controls\n    this.reportTypeControl = new FormControl('');\n    this.messageControl = new FormControl('');\n    // Component state\n    this.availableReports = [];\n    this.chatMessages = [];\n    this.currentSession = null;\n    this.isLoading = false;\n    this.currentFilters = null;\n    // Chart data\n    this.chartData = null;\n    this.chartType = 'bar';\n    this.chartConfig = null;\n    // Example queries for data analysis\n    this.exampleQueries = ['Create a bar chart of total purchases by vendor', 'What is the average order quantity by category?', 'Show me a pie chart of inventory by location', 'Generate a line chart of purchase trends over time', 'What are the top 10 items by value?', 'Show me cost variance analysis'];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadAvailableReports();\n    this.subscribeToServiceState();\n  }\n  ngAfterViewInit() {\n    this.scrollToBottom();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  subscribeToServiceState() {\n    this.agnoService.chatMessages$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n      this.chatMessages = messages;\n      setTimeout(() => this.scrollToBottom(), 100);\n    });\n    this.agnoService.currentSession$.pipe(takeUntil(this.destroy$)).subscribe(session => {\n      this.currentSession = session;\n    });\n    this.agnoService.isLoading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  loadAvailableReports() {\n    this.agnoService.getAvailableReports().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.availableReports = response.reports;\n        }\n      },\n      error: error => {\n        this.showError('Failed to load available reports');\n        console.error('Error loading reports:', error);\n      }\n    });\n  }\n  onReportTypeChange() {\n    const reportType = this.reportTypeControl.value;\n    if (reportType) {\n      const selectedReport = this.availableReports.find(r => r.name === reportType);\n      if (selectedReport) {\n        this.addChatMessage({\n          content: `Great! I'll help you generate a ${selectedReport.display_name}. Please configure the filters and click \"Generate Dashboard\" to start.`,\n          type: 'ai',\n          created_at: new Date()\n        });\n      }\n    }\n  }\n  onFiltersApplied(filters) {\n    this.currentFilters = filters;\n    this.generateDashboardFromFilters();\n  }\n  sendMessage() {\n    const message = this.messageControl.value?.trim();\n    if (!message) return;\n    if (!this.currentSession) {\n      this.showError('Please generate a dashboard first by selecting filters and clicking \"Generate Dashboard\"');\n      return;\n    }\n    // Add user message\n    this.addChatMessage({\n      content: message,\n      type: 'human',\n      created_at: new Date()\n    });\n    this.messageControl.setValue('');\n    this.agnoService.setLoading(true);\n    // Chat with dashboard\n    this.chatWithDashboard(message);\n  }\n  generateDashboardFromFilters() {\n    if (!this.currentFilters || !this.reportTypeControl.value) {\n      this.showError('Please select filters and report type');\n      return;\n    }\n    this.agnoService.setLoading(true);\n    // Convert filters to parameters format\n    const parameters = this.convertFiltersToParameters(this.currentFilters);\n    this.generateDashboard(this.reportTypeControl.value, parameters);\n  }\n  convertFiltersToParameters(filters) {\n    return {\n      selectedRestaurants: filters.selectedRestaurants,\n      selectedVendors: filters.selectedVendors || [],\n      selectedCategories: filters.selectedCategories || [],\n      selectedSubCategories: filters.selectedSubCategories || [],\n      selectedWorkAreas: filters.selectedWorkAreas || [],\n      startDate: filters.startDate?.toISOString(),\n      endDate: filters.endDate?.toISOString(),\n      selectedBaseDate: filters.selectedBaseDate || 'deliveryDate'\n    };\n  }\n  generateDashboard(reportType, parameters) {\n    const request = {\n      tenant_id: this.agnoService.getTenantId(),\n      report_type: reportType,\n      parameters: parameters,\n      session_id: this.currentSession?.session_id\n    };\n    this.agnoService.generateDashboard(request).subscribe({\n      next: response => {\n        this.handleDashboardResponse(response);\n      },\n      error: error => {\n        this.handleError('Error generating dashboard', error);\n      }\n    });\n  }\n  handleDashboardResponse(response) {\n    if (response.status === 'success') {\n      // Create session object\n      const session = {\n        session_id: response.session_id,\n        tenant_id: this.agnoService.getTenantId(),\n        report_type: this.reportTypeControl.value,\n        parameters: this.convertFiltersToParameters(this.currentFilters),\n        dataframe_summary: response.dataframe_info,\n        created_at: new Date().toISOString(),\n        last_activity: new Date().toISOString()\n      };\n      this.agnoService.setCurrentSession(session);\n      this.addChatMessage({\n        content: 'Dashboard generated successfully! You can now ask questions about your data.',\n        type: 'ai',\n        created_at: new Date()\n      });\n      this.showSuccess('Dashboard generated successfully!');\n    } else {\n      this.showError(`Error generating dashboard: ${response.message}`);\n    }\n    this.agnoService.setLoading(false);\n  }\n  chatWithDashboard(message) {\n    if (!this.currentSession) return;\n    const request = {\n      tenant_id: this.agnoService.getTenantId(),\n      session_id: this.currentSession.session_id,\n      message: message\n    };\n    this.agnoService.chatWithDashboard(request).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          const chatResponse = response.data;\n          this.addChatMessage({\n            content: chatResponse.message,\n            type: 'ai',\n            created_at: new Date(),\n            chart_data: chatResponse.chart_data,\n            chart_type: chatResponse.chart_type,\n            has_visualization: chatResponse.has_visualization\n          });\n          // Handle visualization\n          if (chatResponse.has_visualization && chatResponse.chart_data) {\n            this.updateChart(chatResponse.chart_data, chatResponse.chart_type);\n          }\n        }\n        this.agnoService.setLoading(false);\n      },\n      error: error => {\n        this.handleError('Error processing chat request', error);\n      }\n    });\n  }\n  updateChart(chartData, chartType) {\n    try {\n      this.chartType = chartType || 'bar';\n      // Convert PandasAI data to Chart.js format\n      this.chartData = ChartWrapperComponent.convertPandasAIToChartJS(chartData, this.chartType);\n      this.chartConfig = this.agnoService.generateChartConfig(this.chartData, this.chartType);\n    } catch (error) {\n      console.error('Error updating chart:', error);\n      this.showError('Error displaying chart');\n    }\n  }\n  sendExampleQuery(query) {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n  clearSession() {\n    if (this.currentSession) {\n      this.agnoService.clearSession(this.currentSession.session_id).subscribe({\n        next: () => {\n          this.resetSession();\n          this.showSuccess('Session cleared successfully');\n        },\n        error: error => {\n          console.error('Error clearing session:', error);\n          this.resetSession(); // Reset anyway\n        }\n      });\n    } else {\n      this.resetSession();\n    }\n  }\n  resetSession() {\n    this.agnoService.setCurrentSession(null);\n    this.agnoService.clearChatMessages();\n    this.currentFilters = null;\n    this.chartData = null;\n    this.chartConfig = null;\n    this.reportTypeControl.setValue('');\n  }\n  addChatMessage(message) {\n    this.agnoService.addChatMessage(message);\n  }\n  scrollToBottom() {\n    if (this.chatContainer) {\n      const element = this.chatContainer.nativeElement;\n      element.scrollTop = element.scrollHeight;\n    }\n  }\n  handleError(message, error) {\n    console.error(message, error);\n    this.showError(message);\n    this.agnoService.setLoading(false);\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  trackByIndex(index) {\n    return index;\n  }\n  getStatusText() {\n    if (this.isLoading) return 'Processing...';\n    if (this.currentSession) return 'Dashboard ready';\n    return 'Ready';\n  }\n  getStatusClass() {\n    if (this.isLoading) return 'status-loading';\n    if (this.currentSession) return 'status-ready';\n    return 'status-ready';\n  }\n  static {\n    this.ɵfac = function AgnoAgentComponent_Factory(t) {\n      return new (t || AgnoAgentComponent)(i0.ɵɵdirectiveInject(i1.AgnoAgentService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgnoAgentComponent,\n      selectors: [[\"app-agno-agent\"]],\n      viewQuery: function AgnoAgentComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(ChartWrapperComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartWrapper = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 65,\n      vars: 16,\n      consts: [[1, \"agno-dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-title\"], [1, \"header-icon\"], [1, \"header-status\"], [1, \"status-indicator\", 3, \"ngClass\"], [1, \"main-content\"], [1, \"filter-panel-container\"], [1, \"report-selector\"], [\"appearance\", \"outline\", 1, \"full-width\"], [3, \"formControl\", \"selectionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"reportType\", \"filtersApplied\"], [1, \"chat-panel\"], [1, \"chat-card\"], [1, \"chat-header\"], [1, \"chat-header-content\"], [1, \"chat-title\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Clear Session\", 3, \"click\"], [\"class\", \"example-queries\", 4, \"ngIf\"], [1, \"chat-messages\"], [\"chatContainer\", \"\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"loading-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-input\"], [\"matInput\", \"\", \"rows\", \"2\", \"placeholder\", \"Type your message here...\", 3, \"formControl\", \"disabled\", \"keydown\"], [\"messageInput\", \"\"], [4, \"ngIf\"], [\"mat-fab\", \"\", \"color\", \"primary\", 1, \"send-button\", 3, \"disabled\", \"click\"], [1, \"preview-panel\"], [1, \"preview-card\"], [1, \"preview-content\"], [\"class\", \"session-info\", 4, \"ngIf\"], [\"class\", \"welcome-content\", 4, \"ngIf\"], [\"class\", \"main-chart\", 4, \"ngIf\"], [3, \"value\"], [1, \"example-queries\"], [1, \"query-chips\"], [\"class\", \"example-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"example-chip\", 3, \"click\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-header\"], [1, \"message-sender\"], [1, \"message-time\"], [1, \"message-text\"], [3, \"data\"], [\"class\", \"message-chart\", 4, \"ngIf\"], [1, \"message-chart\"], [3, \"chartData\", \"chartType\"], [1, \"loading-message\"], [\"diameter\", \"20\"], [1, \"session-info\"], [1, \"session-details\"], [1, \"column-info\"], [1, \"column-chips\"], [\"disabled\", \"\", 4, \"ngFor\", \"ngForOf\"], [\"disabled\", \"\", 4, \"ngIf\"], [1, \"ready-indicator\"], [\"color\", \"primary\"], [\"disabled\", \"\"], [1, \"welcome-content\"], [1, \"features-list\"], [1, \"how-to-use\"], [1, \"main-chart\"]],\n      template: function AgnoAgentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Agno Agent Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"AI-Powered Dynamic Report Analysis & Visualization\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"span\", 6);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"mat-form-field\", 10)(17, \"mat-label\");\n          i0.ɵɵtext(18, \"Select Report Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-select\", 11);\n          i0.ɵɵlistener(\"selectionChange\", function AgnoAgentComponent_Template_mat_select_selectionChange_19_listener() {\n            return ctx.onReportTypeChange();\n          });\n          i0.ɵɵelementStart(20, \"mat-option\", 12);\n          i0.ɵɵtext(21, \"Choose a report type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, AgnoAgentComponent_mat_option_22_Template, 2, 2, \"mat-option\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-hint\");\n          i0.ɵɵtext(24, \"Select the type of report you want to analyze\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"app-filter-panel\", 14);\n          i0.ɵɵlistener(\"filtersApplied\", function AgnoAgentComponent_Template_app_filter_panel_filtersApplied_25_listener($event) {\n            return ctx.onFiltersApplied($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 15)(27, \"mat-card\", 16)(28, \"mat-card-header\", 17)(29, \"div\", 18)(30, \"div\", 19)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34, \"Chat with Your Data\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function AgnoAgentComponent_Template_button_click_35_listener() {\n            return ctx.clearSession();\n          });\n          i0.ɵɵelementStart(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(38, AgnoAgentComponent_div_38_Template, 6, 1, \"div\", 21);\n          i0.ɵɵelementStart(39, \"div\", 22, 23);\n          i0.ɵɵtemplate(41, AgnoAgentComponent_div_41_Template, 13, 12, \"div\", 24);\n          i0.ɵɵtemplate(42, AgnoAgentComponent_div_42_Template, 4, 0, \"div\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 26)(44, \"mat-form-field\", 27)(45, \"mat-label\");\n          i0.ɵɵtext(46, \"Ask me anything about your data...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"textarea\", 28, 29);\n          i0.ɵɵlistener(\"keydown\", function AgnoAgentComponent_Template_textarea_keydown_47_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵtext(49, \"            \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, AgnoAgentComponent_mat_hint_50_Template, 2, 0, \"mat-hint\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function AgnoAgentComponent_Template_button_click_51_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"send\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(54, \"div\", 32)(55, \"mat-card\", 33)(56, \"mat-card-header\")(57, \"mat-card-title\")(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Data Preview & Visualizations \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-card-content\", 34);\n          i0.ɵɵtemplate(62, AgnoAgentComponent_div_62_Template, 30, 9, \"div\", 35);\n          i0.ɵɵtemplate(63, AgnoAgentComponent_div_63_Template, 43, 0, \"div\", 36);\n          i0.ɵɵtemplate(64, AgnoAgentComponent_div_64_Template, 4, 2, \"div\", 37);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(), \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formControl\", ctx.reportTypeControl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableReports);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"reportType\", ctx.reportTypeControl.value || \"\");\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentSession);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.chatMessages)(\"ngForTrackBy\", ctx.trackByIndex);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formControl\", ctx.messageControl)(\"disabled\", !ctx.currentSession);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentSession);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !(ctx.messageControl.value == null ? null : ctx.messageControl.value.trim()) || !ctx.currentSession || ctx.isLoading);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentSession);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentSession);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.chartData);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DecimalPipe, i3.DatePipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, ReactiveFormsModule, i4.FormControlDirective, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatIconButton, i6.MatFabButton, MatInputModule, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatHint, MatSelectModule, i9.MatSelect, i10.MatOption, MatIconModule, i11.MatIcon, MatProgressSpinnerModule, i12.MatProgressSpinner, MatChipsModule, i13.MatChipListbox, i13.MatChipOption, MatDividerModule, MatTooltipModule, i14.MatTooltip, MatSnackBarModule, MarkdownModule, i15.MarkdownComponent, FilterPanelComponent, ChartWrapperComponent],\n      styles: [\".agno-dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f5f5f5;\\n  overflow: hidden;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 20px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  margin-bottom: 8px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 600;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 14px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-status[_ngcontent-%COMP%]   .status-indicator.status-ready[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.2);\\n  color: #4caf50;\\n  border: 1px solid rgba(76, 175, 80, 0.3);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-status[_ngcontent-%COMP%]   .status-indicator.status-collecting[_ngcontent-%COMP%] {\\n  background: rgba(255, 193, 7, 0.2);\\n  color: #ffc107;\\n  border: 1px solid rgba(255, 193, 7, 0.3);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-status[_ngcontent-%COMP%]   .status-indicator.status-loading[_ngcontent-%COMP%] {\\n  background: rgba(33, 150, 243, 0.2);\\n  color: #2196f3;\\n  border: 1px solid rgba(33, 150, 243, 0.3);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 30% 40% 30%;\\n  gap: 20px;\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n  overflow: hidden;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .filter-panel-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  gap: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .filter-panel-container[_ngcontent-%COMP%]   .report-selector[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 16px 20px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-header-content[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .example-queries[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e7f3ff;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .example-queries[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #0066cc;\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .example-queries[_ngcontent-%COMP%]   .query-chips[_ngcontent-%COMP%]   .example-chip[_ngcontent-%COMP%] {\\n  margin: 4px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .example-queries[_ngcontent-%COMP%]   .query-chips[_ngcontent-%COMP%]   .example-chip[_ngcontent-%COMP%]:hover {\\n  background-color: #0066cc;\\n  color: white;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .example-queries[_ngcontent-%COMP%]   .query-chips[_ngcontent-%COMP%]   .example-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  margin-right: 4px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  overflow-y: auto;\\n  max-height: 400px;\\n  background: white;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%] {\\n  align-items: flex-end;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  max-width: 80%;\\n  border-radius: 18px 18px 4px 18px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message.ai-message[_ngcontent-%COMP%] {\\n  align-items: flex-start;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  max-width: 90%;\\n  border-radius: 18px 18px 18px 4px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n  font-size: 12px;\\n  opacity: 0.8;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-weight: 600;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  line-height: 1.5;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     p {\\n  margin: 0 0 8px 0;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     p:last-child {\\n  margin-bottom: 0;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     ul, .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     ol {\\n  margin: 8px 0;\\n  padding-left: 20px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     li {\\n  margin-bottom: 4px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]     strong {\\n  font-weight: 600;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-chart[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-chart[_ngcontent-%COMP%]   app-chart-wrapper[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%]   .loading-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  color: #666;\\n  font-style: italic;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-input[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  gap: 12px;\\n  align-items: flex-end;\\n  background: #fafafa;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-input[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-input[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 16px 20px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .session-details[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 16px;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .session-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .session-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .column-info[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .column-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #333;\\n  font-size: 14px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .column-info[_ngcontent-%COMP%]   .column-chips[_ngcontent-%COMP%]   mat-chip-option[_ngcontent-%COMP%] {\\n  margin: 2px;\\n  font-size: 12px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .session-info[_ngcontent-%COMP%]   .ready-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background: #e8f5e8;\\n  border-radius: 8px;\\n  color: #28a745;\\n  font-weight: 500;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 12px;\\n  font-size: 16px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  line-height: 1.5;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .how-to-use[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .main-chart[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .main-chart[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  color: #333;\\n}\\n.agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .main-chart[_ngcontent-%COMP%]   app-chart-wrapper[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n@media (max-width: 1200px) {\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n    grid-template-rows: auto 1fr;\\n    gap: 16px;\\n    padding: 16px;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .filter-panel-container[_ngcontent-%COMP%] {\\n    grid-column: 1/-1;\\n    grid-row: 1;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    grid-row: 2;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%] {\\n    grid-column: 2;\\n    grid-row: 2;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    text-align: center;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    grid-template-rows: auto auto 1fr;\\n    padding: 12px;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .filter-panel-container[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    grid-row: 1;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    grid-row: 2;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-panel[_ngcontent-%COMP%]   .chat-card[_ngcontent-%COMP%]   .chat-messages[_ngcontent-%COMP%] {\\n    max-height: 300px;\\n  }\\n  .agno-dashboard-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .preview-panel[_ngcontent-%COMP%] {\\n    grid-column: 1;\\n    grid-row: 3;\\n  }\\n}\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar, .preview-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track, .preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover, .preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n  .error-snackbar {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n  .success-snackbar {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { AgnoAgentComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "FormControl", "MatCardModule", "MatButtonModule", "MatInputModule", "MatSelectModule", "MatIconModule", "MatProgressSpinnerModule", "MatChipsModule", "MatDividerModule", "MatTooltipModule", "MatSnackBarModule", "MarkdownModule", "Chart", "registerables", "Subject", "takeUntil", "FilterPanelComponent", "ChartWrapperComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "report_r10", "name", "ɵɵadvance", "ɵɵtextInterpolate1", "display_name", "ɵɵlistener", "AgnoAgentComponent_div_38_mat_chip_option_5_Template_mat_chip_option_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r14", "query_r12", "$implicit", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "send<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtemplate", "AgnoAgentComponent_div_38_mat_chip_option_5_Template", "ctx_r1", "exampleQueries", "ɵɵelement", "message_r15", "chart_data", "chart_type", "AgnoAgentComponent_div_41_div_12_Template", "ɵɵpureFunction2", "_c2", "type", "ɵɵtextInterpolate", "ɵɵpipeBind2", "created_at", "content", "has_visualization", "column_r20", "ctx_r19", "currentSession", "dataframe_summary", "column_names", "length", "AgnoAgentComponent_div_62_mat_chip_option_23_Template", "AgnoAgentComponent_div_62_mat_chip_option_24_Template", "ctx_r7", "reportTypeControl", "value", "ɵɵpipeBind1", "rows", "columns", "slice", "ctx_r9", "chartData", "chartType", "register", "AgnoAgentComponent", "constructor", "agnoService", "snackBar", "messageControl", "availableReports", "chatMessages", "isLoading", "currentFilters", "chartConfig", "destroy$", "ngOnInit", "loadAvailableReports", "subscribeToServiceState", "ngAfterViewInit", "scrollToBottom", "ngOnDestroy", "next", "complete", "chatMessages$", "pipe", "subscribe", "messages", "setTimeout", "currentSession$", "session", "isLoading$", "loading", "getAvailableReports", "response", "status", "reports", "error", "showError", "console", "onReportTypeChange", "reportType", "selectedReport", "find", "r", "addChatMessage", "Date", "onFiltersApplied", "filters", "generateDashboardFromFilters", "sendMessage", "message", "trim", "setValue", "setLoading", "chatWithDashboard", "parameters", "convertFiltersToParameters", "generateDashboard", "selectedRestaurants", "selectedVendors", "selectedCategories", "selectedSubCategories", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate", "toISOString", "endDate", "selectedBaseDate", "request", "tenant_id", "getTenantId", "report_type", "session_id", "handleDashboardResponse", "handleError", "dataframe_info", "last_activity", "setCurrentSession", "showSuccess", "chatResponse", "data", "updateChart", "convertPandasAIToChartJS", "generateChartConfig", "query", "clearSession", "resetSession", "clearChatMessages", "chatContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "open", "duration", "panelClass", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "trackByIndex", "index", "getStatusText", "getStatusClass", "ɵɵdirectiveInject", "i1", "AgnoAgentService", "i2", "MatSnackBar", "selectors", "viewQuery", "AgnoAgentComponent_Query", "rf", "ctx", "AgnoAgentComponent_Template_mat_select_selectionChange_19_listener", "AgnoAgentComponent_mat_option_22_Template", "AgnoAgentComponent_Template_app_filter_panel_filtersApplied_25_listener", "$event", "AgnoAgentComponent_Template_button_click_35_listener", "AgnoAgentComponent_div_38_Template", "AgnoAgentComponent_div_41_Template", "AgnoAgentComponent_div_42_Template", "AgnoAgentComponent_Template_textarea_keydown_47_listener", "AgnoAgentComponent_mat_hint_50_Template", "AgnoAgentComponent_Template_button_click_51_listener", "AgnoAgentComponent_div_62_Template", "AgnoAgentComponent_div_63_Template", "AgnoAgentComponent_div_64_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i4", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i6", "MatIconButton", "Mat<PERSON>ab<PERSON><PERSON><PERSON>", "i7", "MatInput", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "i9", "MatSelect", "i10", "MatOption", "i11", "MatIcon", "i12", "MatProgressSpinner", "i13", "MatChipListbox", "MatChipOption", "i14", "MatTooltip", "i15", "MarkdownComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/agno-agent.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/agno-agent.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport {\n  AgnoAgentService,\n  ReportType,\n  ChatMessage,\n  DashboardSession,\n  DashboardResponse\n} from '../../services/agno-agent.service';\n\nimport { FilterPanelComponent, ReportFilters } from './filter-panel.component';\nimport { ChartWrapperComponent } from './chart-wrapper.component';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-agno-agent',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatInputModule,\n    MatSelectModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatChipsModule,\n    MatDividerModule,\n    MatTooltipModule,\n    MatSnackBarModule,\n    MarkdownModule,\n    FilterPanelComponent,\n    ChartWrapperComponent\n  ],\n  templateUrl: './agno-agent.component.html',\n  styleUrls: ['./agno-agent.component.scss']\n})\nexport class AgnoAgentComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('chatContainer') chatContainer!: ElementRef;\n  @ViewChild('messageInput') messageInput!: ElementRef;\n  @ViewChild(ChartWrapperComponent) chartWrapper!: ChartWrapperComponent;\n\n  // Form controls\n  reportTypeControl = new FormControl('');\n  messageControl = new FormControl('');\n\n  // Component state\n  availableReports: ReportType[] = [];\n  chatMessages: ChatMessage[] = [];\n  currentSession: DashboardSession | null = null;\n  isLoading = false;\n  currentFilters: ReportFilters | null = null;\n\n  // Chart data\n  chartData: any = null;\n  chartType: ChartType | string = 'bar';\n  chartConfig: ChartConfiguration | null = null;\n\n  // Example queries for data analysis\n  exampleQueries = [\n    'Create a bar chart of total purchases by vendor',\n    'What is the average order quantity by category?',\n    'Show me a pie chart of inventory by location',\n    'Generate a line chart of purchase trends over time',\n    'What are the top 10 items by value?',\n    'Show me cost variance analysis'\n  ];\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private agnoService: AgnoAgentService,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadAvailableReports();\n    this.subscribeToServiceState();\n  }\n\n  ngAfterViewInit(): void {\n    this.scrollToBottom();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private subscribeToServiceState(): void {\n    this.agnoService.chatMessages$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(messages => {\n        this.chatMessages = messages;\n        setTimeout(() => this.scrollToBottom(), 100);\n      });\n\n    this.agnoService.currentSession$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(session => {\n        this.currentSession = session;\n      });\n\n    this.agnoService.isLoading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n      });\n  }\n\n  private loadAvailableReports(): void {\n    this.agnoService.getAvailableReports().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.availableReports = response.reports;\n        }\n      },\n      error: (error) => {\n        this.showError('Failed to load available reports');\n        console.error('Error loading reports:', error);\n      }\n    });\n  }\n\n  onReportTypeChange(): void {\n    const reportType = this.reportTypeControl.value;\n    if (reportType) {\n      const selectedReport = this.availableReports.find(r => r.name === reportType);\n      if (selectedReport) {\n        this.addChatMessage({\n          content: `Great! I'll help you generate a ${selectedReport.display_name}. Please configure the filters and click \"Generate Dashboard\" to start.`,\n          type: 'ai',\n          created_at: new Date()\n        });\n      }\n    }\n  }\n\n  onFiltersApplied(filters: ReportFilters): void {\n    this.currentFilters = filters;\n    this.generateDashboardFromFilters();\n  }\n\n  sendMessage(): void {\n    const message = this.messageControl.value?.trim();\n    if (!message) return;\n\n    if (!this.currentSession) {\n      this.showError('Please generate a dashboard first by selecting filters and clicking \"Generate Dashboard\"');\n      return;\n    }\n\n    // Add user message\n    this.addChatMessage({\n      content: message,\n      type: 'human',\n      created_at: new Date()\n    });\n\n    this.messageControl.setValue('');\n    this.agnoService.setLoading(true);\n\n    // Chat with dashboard\n    this.chatWithDashboard(message);\n  }\n\n  private generateDashboardFromFilters(): void {\n    if (!this.currentFilters || !this.reportTypeControl.value) {\n      this.showError('Please select filters and report type');\n      return;\n    }\n\n    this.agnoService.setLoading(true);\n\n    // Convert filters to parameters format\n    const parameters = this.convertFiltersToParameters(this.currentFilters);\n\n    this.generateDashboard(this.reportTypeControl.value, parameters);\n  }\n\n  private convertFiltersToParameters(filters: ReportFilters): any {\n    return {\n      selectedRestaurants: filters.selectedRestaurants,\n      selectedVendors: filters.selectedVendors || [],\n      selectedCategories: filters.selectedCategories || [],\n      selectedSubCategories: filters.selectedSubCategories || [],\n      selectedWorkAreas: filters.selectedWorkAreas || [],\n      startDate: filters.startDate?.toISOString(),\n      endDate: filters.endDate?.toISOString(),\n      selectedBaseDate: filters.selectedBaseDate || 'deliveryDate'\n    };\n  }\n\n  private generateDashboard(reportType: string, parameters: any): void {\n    const request = {\n      tenant_id: this.agnoService.getTenantId(),\n      report_type: reportType,\n      parameters: parameters,\n      session_id: this.currentSession?.session_id\n    };\n\n    this.agnoService.generateDashboard(request).subscribe({\n      next: (response) => {\n        this.handleDashboardResponse(response);\n      },\n      error: (error) => {\n        this.handleError('Error generating dashboard', error);\n      }\n    });\n  }\n\n  private handleDashboardResponse(response: DashboardResponse): void {\n    if (response.status === 'success') {\n      // Create session object\n      const session: DashboardSession = {\n        session_id: response.session_id,\n        tenant_id: this.agnoService.getTenantId(),\n        report_type: this.reportTypeControl.value!,\n        parameters: this.convertFiltersToParameters(this.currentFilters!),\n        dataframe_summary: response.dataframe_info!,\n        created_at: new Date().toISOString(),\n        last_activity: new Date().toISOString()\n      };\n\n      this.agnoService.setCurrentSession(session);\n\n      this.addChatMessage({\n        content: 'Dashboard generated successfully! You can now ask questions about your data.',\n        type: 'ai',\n        created_at: new Date()\n      });\n\n      this.showSuccess('Dashboard generated successfully!');\n    } else {\n      this.showError(`Error generating dashboard: ${response.message}`);\n    }\n\n    this.agnoService.setLoading(false);\n  }\n\n  private chatWithDashboard(message: string): void {\n    if (!this.currentSession) return;\n\n    const request = {\n      tenant_id: this.agnoService.getTenantId(),\n      session_id: this.currentSession.session_id,\n      message: message\n    };\n\n    this.agnoService.chatWithDashboard(request).subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          const chatResponse = response.data;\n\n          this.addChatMessage({\n            content: chatResponse.message,\n            type: 'ai',\n            created_at: new Date(),\n            chart_data: chatResponse.chart_data,\n            chart_type: chatResponse.chart_type,\n            has_visualization: chatResponse.has_visualization\n          });\n\n          // Handle visualization\n          if (chatResponse.has_visualization && chatResponse.chart_data) {\n            this.updateChart(chatResponse.chart_data, chatResponse.chart_type);\n          }\n        }\n        this.agnoService.setLoading(false);\n      },\n      error: (error) => {\n        this.handleError('Error processing chat request', error);\n      }\n    });\n  }\n\n  private updateChart(chartData: any, chartType?: string): void {\n    try {\n      this.chartType = chartType || 'bar';\n      // Convert PandasAI data to Chart.js format\n      this.chartData = ChartWrapperComponent.convertPandasAIToChartJS(chartData, this.chartType);\n      this.chartConfig = this.agnoService.generateChartConfig(this.chartData, this.chartType);\n    } catch (error) {\n      console.error('Error updating chart:', error);\n      this.showError('Error displaying chart');\n    }\n  }\n\n  sendExampleQuery(query: string): void {\n    this.messageControl.setValue(query);\n    this.sendMessage();\n  }\n\n  clearSession(): void {\n    if (this.currentSession) {\n      this.agnoService.clearSession(this.currentSession.session_id).subscribe({\n        next: () => {\n          this.resetSession();\n          this.showSuccess('Session cleared successfully');\n        },\n        error: (error) => {\n          console.error('Error clearing session:', error);\n          this.resetSession(); // Reset anyway\n        }\n      });\n    } else {\n      this.resetSession();\n    }\n  }\n\n  private resetSession(): void {\n    this.agnoService.setCurrentSession(null);\n    this.agnoService.clearChatMessages();\n    this.currentFilters = null;\n    this.chartData = null;\n    this.chartConfig = null;\n    this.reportTypeControl.setValue('');\n  }\n\n  private addChatMessage(message: ChatMessage): void {\n    this.agnoService.addChatMessage(message);\n  }\n\n  private scrollToBottom(): void {\n    if (this.chatContainer) {\n      const element = this.chatContainer.nativeElement;\n      element.scrollTop = element.scrollHeight;\n    }\n  }\n\n  private handleError(message: string, error: any): void {\n    console.error(message, error);\n    this.showError(message);\n    this.agnoService.setLoading(false);\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  trackByIndex(index: number): number {\n    return index;\n  }\n\n  getStatusText(): string {\n    if (this.isLoading) return 'Processing...';\n    if (this.currentSession) return 'Dashboard ready';\n    return 'Ready';\n  }\n\n  getStatusClass(): string {\n    if (this.isLoading) return 'status-loading';\n    if (this.currentSession) return 'status-ready';\n    return 'status-ready';\n  }\n}\n", "<div class=\"agno-dashboard-container\">\n  <!-- Header -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-title\">\n        <mat-icon class=\"header-icon\">smart_toy</mat-icon>\n        <h1>Agno Agent Dashboard</h1>\n        <p>AI-Powered Dynamic Report Analysis & Visualization</p>\n      </div>\n      <div class=\"header-status\">\n        <span class=\"status-indicator\" [ngClass]=\"getStatusClass()\">\n          {{ getStatusText() }}\n        </span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <div class=\"main-content\">\n    <!-- Filter Panel (30%) -->\n    <div class=\"filter-panel-container\">\n      <div class=\"report-selector\">\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Select Report Type</mat-label>\n          <mat-select [formControl]=\"reportTypeControl\" (selectionChange)=\"onReportTypeChange()\">\n            <mat-option value=\"\">Choose a report type</mat-option>\n            <mat-option *ngFor=\"let report of availableReports\" [value]=\"report.name\">\n              {{ report.display_name }}\n            </mat-option>\n          </mat-select>\n          <mat-hint>Select the type of report you want to analyze</mat-hint>\n        </mat-form-field>\n      </div>\n\n      <app-filter-panel\n        [reportType]=\"reportTypeControl.value || ''\"\n        (filtersApplied)=\"onFiltersApplied($event)\">\n      </app-filter-panel>\n    </div>\n\n    <!-- Chat Panel (40%) -->\n    <div class=\"chat-panel\">\n      <mat-card class=\"chat-card\">\n        <!-- Chat Header -->\n        <mat-card-header class=\"chat-header\">\n          <div class=\"chat-header-content\">\n            <div class=\"chat-title\">\n              <mat-icon>chat</mat-icon>\n              <span>Chat with Your Data</span>\n            </div>\n            <button\n              mat-icon-button\n              color=\"warn\"\n              (click)=\"clearSession()\"\n              matTooltip=\"Clear Session\">\n              <mat-icon>clear</mat-icon>\n            </button>\n          </div>\n        </mat-card-header>\n\n        <!-- Example Queries -->\n        <div class=\"example-queries\" *ngIf=\"currentSession\">\n          <h4>Example Queries:</h4>\n          <div class=\"query-chips\">\n            <mat-chip-listbox>\n              <mat-chip-option\n                *ngFor=\"let query of exampleQueries\"\n                (click)=\"sendExampleQuery(query)\"\n                class=\"example-chip\">\n                <mat-icon>lightbulb</mat-icon>\n                {{ query }}\n              </mat-chip-option>\n            </mat-chip-listbox>\n          </div>\n        </div>\n\n        <!-- Chat Messages -->\n        <div class=\"chat-messages\" #chatContainer>\n          <div\n            *ngFor=\"let message of chatMessages; trackBy: trackByIndex\"\n            class=\"message\"\n            [ngClass]=\"{'user-message': message.type === 'human', 'ai-message': message.type === 'ai'}\">\n\n            <div class=\"message-content\">\n              <div class=\"message-header\">\n                <span class=\"message-sender\">\n                  <mat-icon>{{ message.type === 'human' ? 'person' : 'smart_toy' }}</mat-icon>\n                  {{ message.type === 'human' ? 'You' : 'DIGI' }}\n                </span>\n                <span class=\"message-time\">\n                  {{ message.created_at | date:'short' }}\n                </span>\n              </div>\n\n              <div class=\"message-text\">\n                <markdown [data]=\"message.content\"></markdown>\n              </div>\n\n              <!-- Chart Visualization -->\n              <div *ngIf=\"message.has_visualization && message.chart_data\" class=\"message-chart\">\n                <app-chart-wrapper\n                  [chartData]=\"message.chart_data\"\n                  [chartType]=\"message.chart_type || 'bar'\">\n                </app-chart-wrapper>\n              </div>\n            </div>\n          </div>\n\n          <!-- Loading Indicator -->\n          <div *ngIf=\"isLoading\" class=\"loading-message\">\n            <mat-spinner diameter=\"20\"></mat-spinner>\n            <span>DIGI is thinking...</span>\n          </div>\n        </div>\n\n        <!-- Chat Input -->\n        <div class=\"chat-input\">\n          <mat-form-field appearance=\"outline\" class=\"message-input\">\n            <mat-label>Ask me anything about your data...</mat-label>\n            <textarea\n              matInput\n              #messageInput\n              [formControl]=\"messageControl\"\n              (keydown)=\"onKeyPress($event)\"\n              rows=\"2\"\n              placeholder=\"Type your message here...\"\n              [disabled]=\"!currentSession\">\n            </textarea>\n            <mat-hint *ngIf=\"!currentSession\">Generate a dashboard first to start chatting</mat-hint>\n          </mat-form-field>\n\n          <button\n            mat-fab\n            color=\"primary\"\n            (click)=\"sendMessage()\"\n            [disabled]=\"!messageControl.value?.trim() || !currentSession || isLoading\"\n            class=\"send-button\">\n            <mat-icon>send</mat-icon>\n          </button>\n        </div>\n      </mat-card>\n    </div>\n\n    <!-- Preview Panel (40%) -->\n    <div class=\"preview-panel\">\n      <mat-card class=\"preview-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>dashboard</mat-icon>\n            Data Preview & Visualizations\n          </mat-card-title>\n        </mat-card-header>\n\n        <mat-card-content class=\"preview-content\">\n          <!-- Session Info -->\n          <div *ngIf=\"currentSession\" class=\"session-info\">\n            <h3>Dashboard Generated Successfully!</h3>\n            <div class=\"session-details\">\n              <p><strong>Report Type:</strong> {{ reportTypeControl.value }}</p>\n              <p><strong>Data Rows:</strong> {{ currentSession.dataframe_summary.rows | number }}</p>\n              <p><strong>Columns:</strong> {{ currentSession.dataframe_summary.columns | number }}</p>\n            </div>\n\n            <div class=\"column-info\">\n              <h4>Available Columns:</h4>\n              <div class=\"column-chips\">\n                <mat-chip-listbox>\n                  <mat-chip-option\n                    *ngFor=\"let column of currentSession.dataframe_summary.column_names.slice(0, 10)\"\n                    disabled>\n                    {{ column }}\n                  </mat-chip-option>\n                  <mat-chip-option\n                    *ngIf=\"currentSession.dataframe_summary.column_names.length > 10\"\n                    disabled>\n                    +{{ currentSession.dataframe_summary.column_names.length - 10 }} more\n                  </mat-chip-option>\n                </mat-chip-listbox>\n              </div>\n            </div>\n\n            <div class=\"ready-indicator\">\n              <mat-icon color=\"primary\">check_circle</mat-icon>\n              <span>Dashboard is ready! You can now ask questions about your data.</span>\n            </div>\n          </div>\n\n          <!-- Welcome Message -->\n          <div *ngIf=\"!currentSession\" class=\"welcome-content\">\n            <h3>Welcome to Agno Agent Dashboard!</h3>\n            <p>This is where your data visualizations and previews will appear.</p>\n\n            <div class=\"features-list\">\n              <h4>Features:</h4>\n              <ul>\n                <li><strong>Dynamic Parameter Collection:</strong> AI automatically gathers required inputs</li>\n                <li><strong>Real-time Dashboard Generation:</strong> Instant data visualization</li>\n                <li><strong>Interactive Chat:</strong> Ask questions and get visual answers</li>\n                <li><strong>Multi-Report Support:</strong> Works with all existing report functions</li>\n                <li><strong>Persistent Sessions:</strong> Data saved across browser sessions</li>\n              </ul>\n            </div>\n\n            <div class=\"how-to-use\">\n              <h4>How to Use:</h4>\n              <ol>\n                <li>Select a report type from the dropdown</li>\n                <li>Describe what data you want to see</li>\n                <li>Let the AI collect required parameters</li>\n                <li>View your interactive dashboard</li>\n                <li>Ask questions and get visual insights</li>\n              </ol>\n            </div>\n          </div>\n\n          <!-- Main Chart Display -->\n          <div *ngIf=\"chartData\" class=\"main-chart\">\n            <h4>Latest Visualization</h4>\n            <app-chart-wrapper\n              [chartData]=\"chartData\"\n              [chartType]=\"chartType\">\n            </app-chart-wrapper>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,KAAK,EAAiCC,aAAa,QAAQ,UAAU;AAC9E,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAUzC,SAASC,oBAAoB,QAAuB,0BAA0B;AAC9E,SAASC,qBAAqB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;;;;ICArDC,EAAA,CAAAC,cAAA,qBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,IAAA,CAAqB;IACvEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,UAAA,CAAAI,YAAA,MACF;;;;;;IAqCET,EAAA,CAAAC,cAAA,0BAGuB;IADrBD,EAAA,CAAAU,UAAA,mBAAAC,sFAAA;MAAA,MAAAC,WAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAAL,SAAA,CAAuB;IAAA,EAAC;IAEjCf,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;IADhBH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAO,SAAA,MACF;;;;;IAVNf,EAAA,CAAAC,cAAA,cAAoD;IAC9CD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,cAAyB;IAErBD,EAAA,CAAAqB,UAAA,IAAAC,oDAAA,8BAMkB;IACpBtB,EAAA,CAAAG,YAAA,EAAmB;;;;IANGH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAC,cAAA,CAAiB;;;;;IAiCrCxB,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAyB,SAAA,4BAGoB;IACtBzB,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,cAAAsB,WAAA,CAAAC,UAAA,CAAgC,cAAAD,WAAA,CAAAE,UAAA;;;;;;;;;;;IAvBxC5B,EAAA,CAAAC,cAAA,cAG8F;IAK5ED,EAAA,CAAAE,MAAA,GAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5EH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAyB,SAAA,oBAA8C;IAChDzB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAqB,UAAA,KAAAQ,yCAAA,kBAKM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IAxBNH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAL,WAAA,CAAAM,IAAA,cAAAN,WAAA,CAAAM,IAAA,WAA2F;IAK3EhC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAiC,iBAAA,CAAAP,WAAA,CAAAM,IAAA,sCAAuD;IACjEhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAkB,WAAA,CAAAM,IAAA,mCACF;IAEEhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAkC,WAAA,OAAAR,WAAA,CAAAS,UAAA,gBACF;IAIUnC,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAsB,WAAA,CAAAU,OAAA,CAAwB;IAI9BpC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAI,UAAA,SAAAsB,WAAA,CAAAW,iBAAA,IAAAX,WAAA,CAAAC,UAAA,CAAqD;;;;;IAU/D3B,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAyB,SAAA,sBAAyC;IACzCzB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBhCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAuCnFH,EAAA,CAAAC,cAAA,0BAEW;IACTD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;IADhBH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA8B,UAAA,MACF;;;;;IACAtC,EAAA,CAAAC,cAAA,0BAEW;IACTD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;IADhBH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,OAAA+B,OAAA,CAAAC,cAAA,CAAAC,iBAAA,CAAAC,YAAA,CAAAC,MAAA,gBACF;;;;;IArBR3C,EAAA,CAAAC,cAAA,cAAiD;IAC3CD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAA6B;IAChBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClEH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvFH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAuD;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG1FH,EAAA,CAAAC,cAAA,eAAyB;IACnBD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAA0B;IAEtBD,EAAA,CAAAqB,UAAA,KAAAuB,qDAAA,8BAIkB;IAClB5C,EAAA,CAAAqB,UAAA,KAAAwB,qDAAA,8BAIkB;IACpB7C,EAAA,CAAAG,YAAA,EAAmB;IAIvBH,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAzB1CH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,kBAAA,MAAAsC,MAAA,CAAAC,iBAAA,CAAAC,KAAA,KAA6B;IAC/BhD,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAiD,WAAA,QAAAH,MAAA,CAAAN,cAAA,CAAAC,iBAAA,CAAAS,IAAA,MAAoD;IACtDlD,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAiD,WAAA,QAAAH,MAAA,CAAAN,cAAA,CAAAC,iBAAA,CAAAU,OAAA,MAAuD;IAQ3DnD,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,YAAA0C,MAAA,CAAAN,cAAA,CAAAC,iBAAA,CAAAC,YAAA,CAAAU,KAAA,QAA6D;IAK/EpD,EAAA,CAAAO,SAAA,GAA+D;IAA/DP,EAAA,CAAAI,UAAA,SAAA0C,MAAA,CAAAN,cAAA,CAAAC,iBAAA,CAAAC,YAAA,CAAAC,MAAA,MAA+D;;;;;IAe1E3C,EAAA,CAAAC,cAAA,cAAqD;IAC/CD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uEAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEvEH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IACUD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChGH,EAAA,CAAAC,cAAA,UAAI;IAAQD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,mCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpFH,EAAA,CAAAC,cAAA,UAAI;IAAQD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,6CAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChFH,EAAA,CAAAC,cAAA,UAAI;IAAQD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxFH,EAAA,CAAAC,cAAA,UAAI;IAAQD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2CAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIrFH,EAAA,CAAAC,cAAA,eAAwB;IAClBD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,UAAI;IACED,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0CAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAMpDH,EAAA,CAAAC,cAAA,cAA0C;IACpCD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAyB,SAAA,4BAGoB;IACtBzB,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,cAAAiD,MAAA,CAAAC,SAAA,CAAuB,cAAAD,MAAA,CAAAE,SAAA;;;AD/LrC7D,KAAK,CAAC8D,QAAQ,CAAC,GAAG7D,aAAa,CAAC;AAEhC,MAwBa8D,kBAAkB;EAiC7BC,YACUC,WAA6B,EAC7BC,QAAqB;IADrB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IA9BlB;IACA,KAAAb,iBAAiB,GAAG,IAAIjE,WAAW,CAAC,EAAE,CAAC;IACvC,KAAA+E,cAAc,GAAG,IAAI/E,WAAW,CAAC,EAAE,CAAC;IAEpC;IACA,KAAAgF,gBAAgB,GAAiB,EAAE;IACnC,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAvB,cAAc,GAA4B,IAAI;IAC9C,KAAAwB,SAAS,GAAG,KAAK;IACjB,KAAAC,cAAc,GAAyB,IAAI;IAE3C;IACA,KAAAX,SAAS,GAAQ,IAAI;IACrB,KAAAC,SAAS,GAAuB,KAAK;IACrC,KAAAW,WAAW,GAA8B,IAAI;IAE7C;IACA,KAAA1C,cAAc,GAAG,CACf,iDAAiD,EACjD,iDAAiD,EACjD,8CAA8C,EAC9C,oDAAoD,EACpD,qCAAqC,EACrC,gCAAgC,CACjC;IAEO,KAAA2C,QAAQ,GAAG,IAAIvE,OAAO,EAAQ;EAKnC;EAEHwE,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,QAAQ,CAACO,IAAI,EAAE;IACpB,IAAI,CAACP,QAAQ,CAACQ,QAAQ,EAAE;EAC1B;EAEQL,uBAAuBA,CAAA;IAC7B,IAAI,CAACX,WAAW,CAACiB,aAAa,CAC3BC,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAAChB,YAAY,GAAGgB,QAAQ;MAC5BC,UAAU,CAAC,MAAM,IAAI,CAACR,cAAc,EAAE,EAAE,GAAG,CAAC;IAC9C,CAAC,CAAC;IAEJ,IAAI,CAACb,WAAW,CAACsB,eAAe,CAC7BJ,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC1C,cAAc,GAAG0C,OAAO;IAC/B,CAAC,CAAC;IAEJ,IAAI,CAACvB,WAAW,CAACwB,UAAU,CACxBN,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACM,OAAO,IAAG;MACnB,IAAI,CAACpB,SAAS,GAAGoB,OAAO;IAC1B,CAAC,CAAC;EACN;EAEQf,oBAAoBA,CAAA;IAC1B,IAAI,CAACV,WAAW,CAAC0B,mBAAmB,EAAE,CAACP,SAAS,CAAC;MAC/CJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACzB,gBAAgB,GAAGwB,QAAQ,CAACE,OAAO;;MAE5C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,kCAAkC,CAAC;QAClDC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAG,kBAAkBA,CAAA;IAChB,MAAMC,UAAU,GAAG,IAAI,CAAC9C,iBAAiB,CAACC,KAAK;IAC/C,IAAI6C,UAAU,EAAE;MACd,MAAMC,cAAc,GAAG,IAAI,CAAChC,gBAAgB,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1F,IAAI,KAAKuF,UAAU,CAAC;MAC7E,IAAIC,cAAc,EAAE;QAClB,IAAI,CAACG,cAAc,CAAC;UAClB7D,OAAO,EAAE,mCAAmC0D,cAAc,CAACrF,YAAY,yEAAyE;UAChJuB,IAAI,EAAE,IAAI;UACVG,UAAU,EAAE,IAAI+D,IAAI;SACrB,CAAC;;;EAGR;EAEAC,gBAAgBA,CAACC,OAAsB;IACrC,IAAI,CAACnC,cAAc,GAAGmC,OAAO;IAC7B,IAAI,CAACC,4BAA4B,EAAE;EACrC;EAEAC,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAI,CAAC1C,cAAc,CAACb,KAAK,EAAEwD,IAAI,EAAE;IACjD,IAAI,CAACD,OAAO,EAAE;IAEd,IAAI,CAAC,IAAI,CAAC/D,cAAc,EAAE;MACxB,IAAI,CAACkD,SAAS,CAAC,0FAA0F,CAAC;MAC1G;;IAGF;IACA,IAAI,CAACO,cAAc,CAAC;MAClB7D,OAAO,EAAEmE,OAAO;MAChBvE,IAAI,EAAE,OAAO;MACbG,UAAU,EAAE,IAAI+D,IAAI;KACrB,CAAC;IAEF,IAAI,CAACrC,cAAc,CAAC4C,QAAQ,CAAC,EAAE,CAAC;IAChC,IAAI,CAAC9C,WAAW,CAAC+C,UAAU,CAAC,IAAI,CAAC;IAEjC;IACA,IAAI,CAACC,iBAAiB,CAACJ,OAAO,CAAC;EACjC;EAEQF,4BAA4BA,CAAA;IAClC,IAAI,CAAC,IAAI,CAACpC,cAAc,IAAI,CAAC,IAAI,CAAClB,iBAAiB,CAACC,KAAK,EAAE;MACzD,IAAI,CAAC0C,SAAS,CAAC,uCAAuC,CAAC;MACvD;;IAGF,IAAI,CAAC/B,WAAW,CAAC+C,UAAU,CAAC,IAAI,CAAC;IAEjC;IACA,MAAME,UAAU,GAAG,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC5C,cAAc,CAAC;IAEvE,IAAI,CAAC6C,iBAAiB,CAAC,IAAI,CAAC/D,iBAAiB,CAACC,KAAK,EAAE4D,UAAU,CAAC;EAClE;EAEQC,0BAA0BA,CAACT,OAAsB;IACvD,OAAO;MACLW,mBAAmB,EAAEX,OAAO,CAACW,mBAAmB;MAChDC,eAAe,EAAEZ,OAAO,CAACY,eAAe,IAAI,EAAE;MAC9CC,kBAAkB,EAAEb,OAAO,CAACa,kBAAkB,IAAI,EAAE;MACpDC,qBAAqB,EAAEd,OAAO,CAACc,qBAAqB,IAAI,EAAE;MAC1DC,iBAAiB,EAAEf,OAAO,CAACe,iBAAiB,IAAI,EAAE;MAClDC,SAAS,EAAEhB,OAAO,CAACgB,SAAS,EAAEC,WAAW,EAAE;MAC3CC,OAAO,EAAElB,OAAO,CAACkB,OAAO,EAAED,WAAW,EAAE;MACvCE,gBAAgB,EAAEnB,OAAO,CAACmB,gBAAgB,IAAI;KAC/C;EACH;EAEQT,iBAAiBA,CAACjB,UAAkB,EAAEe,UAAe;IAC3D,MAAMY,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9D,WAAW,CAAC+D,WAAW,EAAE;MACzCC,WAAW,EAAE9B,UAAU;MACvBe,UAAU,EAAEA,UAAU;MACtBgB,UAAU,EAAE,IAAI,CAACpF,cAAc,EAAEoF;KAClC;IAED,IAAI,CAACjE,WAAW,CAACmD,iBAAiB,CAACU,OAAO,CAAC,CAAC1C,SAAS,CAAC;MACpDJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAI,CAACuC,uBAAuB,CAACvC,QAAQ,CAAC;MACxC,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqC,WAAW,CAAC,4BAA4B,EAAErC,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEQoC,uBAAuBA,CAACvC,QAA2B;IACzD,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;MACjC;MACA,MAAML,OAAO,GAAqB;QAChC0C,UAAU,EAAEtC,QAAQ,CAACsC,UAAU;QAC/BH,SAAS,EAAE,IAAI,CAAC9D,WAAW,CAAC+D,WAAW,EAAE;QACzCC,WAAW,EAAE,IAAI,CAAC5E,iBAAiB,CAACC,KAAM;QAC1C4D,UAAU,EAAE,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC5C,cAAe,CAAC;QACjExB,iBAAiB,EAAE6C,QAAQ,CAACyC,cAAe;QAC3C5F,UAAU,EAAE,IAAI+D,IAAI,EAAE,CAACmB,WAAW,EAAE;QACpCW,aAAa,EAAE,IAAI9B,IAAI,EAAE,CAACmB,WAAW;OACtC;MAED,IAAI,CAAC1D,WAAW,CAACsE,iBAAiB,CAAC/C,OAAO,CAAC;MAE3C,IAAI,CAACe,cAAc,CAAC;QAClB7D,OAAO,EAAE,8EAA8E;QACvFJ,IAAI,EAAE,IAAI;QACVG,UAAU,EAAE,IAAI+D,IAAI;OACrB,CAAC;MAEF,IAAI,CAACgC,WAAW,CAAC,mCAAmC,CAAC;KACtD,MAAM;MACL,IAAI,CAACxC,SAAS,CAAC,+BAA+BJ,QAAQ,CAACiB,OAAO,EAAE,CAAC;;IAGnE,IAAI,CAAC5C,WAAW,CAAC+C,UAAU,CAAC,KAAK,CAAC;EACpC;EAEQC,iBAAiBA,CAACJ,OAAe;IACvC,IAAI,CAAC,IAAI,CAAC/D,cAAc,EAAE;IAE1B,MAAMgF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9D,WAAW,CAAC+D,WAAW,EAAE;MACzCE,UAAU,EAAE,IAAI,CAACpF,cAAc,CAACoF,UAAU;MAC1CrB,OAAO,EAAEA;KACV;IAED,IAAI,CAAC5C,WAAW,CAACgD,iBAAiB,CAACa,OAAO,CAAC,CAAC1C,SAAS,CAAC;MACpDJ,IAAI,EAAGY,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,MAAM4C,YAAY,GAAG7C,QAAQ,CAAC8C,IAAI;UAElC,IAAI,CAACnC,cAAc,CAAC;YAClB7D,OAAO,EAAE+F,YAAY,CAAC5B,OAAO;YAC7BvE,IAAI,EAAE,IAAI;YACVG,UAAU,EAAE,IAAI+D,IAAI,EAAE;YACtBvE,UAAU,EAAEwG,YAAY,CAACxG,UAAU;YACnCC,UAAU,EAAEuG,YAAY,CAACvG,UAAU;YACnCS,iBAAiB,EAAE8F,YAAY,CAAC9F;WACjC,CAAC;UAEF;UACA,IAAI8F,YAAY,CAAC9F,iBAAiB,IAAI8F,YAAY,CAACxG,UAAU,EAAE;YAC7D,IAAI,CAAC0G,WAAW,CAACF,YAAY,CAACxG,UAAU,EAAEwG,YAAY,CAACvG,UAAU,CAAC;;;QAGtE,IAAI,CAAC+B,WAAW,CAAC+C,UAAU,CAAC,KAAK,CAAC;MACpC,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqC,WAAW,CAAC,+BAA+B,EAAErC,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEQ4C,WAAWA,CAAC/E,SAAc,EAAEC,SAAkB;IACpD,IAAI;MACF,IAAI,CAACA,SAAS,GAAGA,SAAS,IAAI,KAAK;MACnC;MACA,IAAI,CAACD,SAAS,GAAGvD,qBAAqB,CAACuI,wBAAwB,CAAChF,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;MAC1F,IAAI,CAACW,WAAW,GAAG,IAAI,CAACP,WAAW,CAAC4E,mBAAmB,CAAC,IAAI,CAACjF,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;KACxF,CAAC,OAAOkC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC;;EAE5C;EAEAtE,gBAAgBA,CAACoH,KAAa;IAC5B,IAAI,CAAC3E,cAAc,CAAC4C,QAAQ,CAAC+B,KAAK,CAAC;IACnC,IAAI,CAAClC,WAAW,EAAE;EACpB;EAEAmC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjG,cAAc,EAAE;MACvB,IAAI,CAACmB,WAAW,CAAC8E,YAAY,CAAC,IAAI,CAACjG,cAAc,CAACoF,UAAU,CAAC,CAAC9C,SAAS,CAAC;QACtEJ,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACgE,YAAY,EAAE;UACnB,IAAI,CAACR,WAAW,CAAC,8BAA8B,CAAC;QAClD,CAAC;QACDzC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACiD,YAAY,EAAE,CAAC,CAAC;QACvB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAAC/E,WAAW,CAACsE,iBAAiB,CAAC,IAAI,CAAC;IACxC,IAAI,CAACtE,WAAW,CAACgF,iBAAiB,EAAE;IACpC,IAAI,CAAC1E,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACY,WAAW,GAAG,IAAI;IACvB,IAAI,CAACnB,iBAAiB,CAAC0D,QAAQ,CAAC,EAAE,CAAC;EACrC;EAEQR,cAAcA,CAACM,OAAoB;IACzC,IAAI,CAAC5C,WAAW,CAACsC,cAAc,CAACM,OAAO,CAAC;EAC1C;EAEQ/B,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACoE,aAAa,EAAE;MACtB,MAAMC,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa;MAChDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;EAE5C;EAEQlB,WAAWA,CAACvB,OAAe,EAAEd,KAAU;IAC7CE,OAAO,CAACF,KAAK,CAACc,OAAO,EAAEd,KAAK,CAAC;IAC7B,IAAI,CAACC,SAAS,CAACa,OAAO,CAAC;IACvB,IAAI,CAAC5C,WAAW,CAAC+C,UAAU,CAAC,KAAK,CAAC;EACpC;EAEQhB,SAASA,CAACa,OAAe;IAC/B,IAAI,CAAC3C,QAAQ,CAACqF,IAAI,CAAC1C,OAAO,EAAE,OAAO,EAAE;MACnC2C,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEQjB,WAAWA,CAAC3B,OAAe;IACjC,IAAI,CAAC3C,QAAQ,CAACqF,IAAI,CAAC1C,OAAO,EAAE,OAAO,EAAE;MACnC2C,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEAC,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAClD,WAAW,EAAE;;EAEtB;EAEAmD,YAAYA,CAACC,KAAa;IACxB,OAAOA,KAAK;EACd;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC3F,SAAS,EAAE,OAAO,eAAe;IAC1C,IAAI,IAAI,CAACxB,cAAc,EAAE,OAAO,iBAAiB;IACjD,OAAO,OAAO;EAChB;EAEAoH,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5F,SAAS,EAAE,OAAO,gBAAgB;IAC3C,IAAI,IAAI,CAACxB,cAAc,EAAE,OAAO,cAAc;IAC9C,OAAO,cAAc;EACvB;;;uBA9UWiB,kBAAkB,EAAAzD,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBxG,kBAAkB;MAAAyG,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAGlBtK,qBAAqB;;;;;;;;;;;;;;;;UCzDlCC,EAAA,CAAAC,cAAA,aAAsC;UAKAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,2BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,yDAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3DH,EAAA,CAAAC,cAAA,cAA2B;UAEvBD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMbH,EAAA,CAAAC,cAAA,cAA0B;UAKPD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,sBAAuF;UAAzCD,EAAA,CAAAU,UAAA,6BAAA6J,mEAAA;YAAA,OAAmBD,GAAA,CAAA1E,kBAAA,EAAoB;UAAA,EAAC;UACpF5F,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAqB,UAAA,KAAAmJ,yCAAA,yBAEa;UACfxK,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,qDAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAItEH,EAAA,CAAAC,cAAA,4BAE8C;UAA5CD,EAAA,CAAAU,UAAA,4BAAA+J,wEAAAC,MAAA;YAAA,OAAkBJ,GAAA,CAAAnE,gBAAA,CAAAuE,MAAA,CAAwB;UAAA,EAAC;UAC7C1K,EAAA,CAAAG,YAAA,EAAmB;UAIrBH,EAAA,CAAAC,cAAA,eAAwB;UAMJD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAElCH,EAAA,CAAAC,cAAA,kBAI6B;UAD3BD,EAAA,CAAAU,UAAA,mBAAAiK,qDAAA;YAAA,OAASL,GAAA,CAAA7B,YAAA,EAAc;UAAA,EAAC;UAExBzI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAMhCH,EAAA,CAAAqB,UAAA,KAAAuJ,kCAAA,kBAaM;UAGN5K,EAAA,CAAAC,cAAA,mBAA0C;UACxCD,EAAA,CAAAqB,UAAA,KAAAwJ,kCAAA,oBA4BM;UAGN7K,EAAA,CAAAqB,UAAA,KAAAyJ,kCAAA,kBAGM;UACR9K,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAwB;UAETD,EAAA,CAAAE,MAAA,0CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzDH,EAAA,CAAAC,cAAA,wBAO+B;UAH7BD,EAAA,CAAAU,UAAA,qBAAAqK,yDAAAL,MAAA;YAAA,OAAWJ,GAAA,CAAAlB,UAAA,CAAAsB,MAAA,CAAkB;UAAA,EAAC;UAIhC1K,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAqB,UAAA,KAAA2J,uCAAA,uBAAyF;UAC3FhL,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,kBAKsB;UAFpBD,EAAA,CAAAU,UAAA,mBAAAuK,qDAAA;YAAA,OAASX,GAAA,CAAAhE,WAAA,EAAa;UAAA,EAAC;UAGvBtG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAOjCH,EAAA,CAAAC,cAAA,eAA2B;UAITD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,uCACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,4BAA0C;UAExCD,EAAA,CAAAqB,UAAA,KAAA6J,kCAAA,mBA8BM;UAGNlL,EAAA,CAAAqB,UAAA,KAAA8J,kCAAA,mBAyBM;UAGNnL,EAAA,CAAAqB,UAAA,KAAA+J,kCAAA,kBAMM;UACRpL,EAAA,CAAAG,YAAA,EAAmB;;;UArNYH,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,YAAAkK,GAAA,CAAAV,cAAA,GAA4B;UACzD5J,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAA8J,GAAA,CAAAX,aAAA,QACF;UAYc3J,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAI,UAAA,gBAAAkK,GAAA,CAAAvH,iBAAA,CAAiC;UAEZ/C,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAkK,GAAA,CAAAxG,gBAAA,CAAmB;UAStD9D,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,eAAAkK,GAAA,CAAAvH,iBAAA,CAAAC,KAAA,OAA4C;UA0BdhD,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,SAAAkK,GAAA,CAAA9H,cAAA,CAAoB;UAkB1BxC,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAkK,GAAA,CAAAvG,YAAA,CAAiB,iBAAAuG,GAAA,CAAAb,YAAA;UA8BjCzJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAkK,GAAA,CAAAtG,SAAA,CAAe;UAajBhE,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,gBAAAkK,GAAA,CAAAzG,cAAA,CAA8B,cAAAyG,GAAA,CAAA9H,cAAA;UAMrBxC,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAkK,GAAA,CAAA9H,cAAA,CAAqB;UAOhCxC,EAAA,CAAAO,SAAA,GAA0E;UAA1EP,EAAA,CAAAI,UAAA,eAAAkK,GAAA,CAAAzG,cAAA,CAAAb,KAAA,kBAAAsH,GAAA,CAAAzG,cAAA,CAAAb,KAAA,CAAAwD,IAAA,QAAA8D,GAAA,CAAA9H,cAAA,IAAA8H,GAAA,CAAAtG,SAAA,CAA0E;UAoBtEhE,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,SAAAkK,GAAA,CAAA9H,cAAA,CAAoB;UAiCpBxC,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAkK,GAAA,CAAA9H,cAAA,CAAqB;UA4BrBxC,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAkK,GAAA,CAAAhH,SAAA,CAAe;;;qBDtL3B3E,YAAY,EAAA0M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EAAAJ,EAAA,CAAAK,QAAA,EACZ9M,WAAW,EAAA+M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EACXhN,mBAAmB,EAAA8M,EAAA,CAAAG,oBAAA,EACnB/M,aAAa,EAAAgN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbnN,eAAe,EAAAoN,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,YAAA,EACfrN,cAAc,EAAAsN,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EACd1N,eAAe,EAAA2N,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf7N,aAAa,EAAA8N,GAAA,CAAAC,OAAA,EACb9N,wBAAwB,EAAA+N,GAAA,CAAAC,kBAAA,EACxB/N,cAAc,EAAAgO,GAAA,CAAAC,cAAA,EAAAD,GAAA,CAAAE,aAAA,EACdjO,gBAAgB,EAChBC,gBAAgB,EAAAiO,GAAA,CAAAC,UAAA,EAChBjO,iBAAiB,EACjBC,cAAc,EAAAiO,GAAA,CAAAC,iBAAA,EACd7N,oBAAoB,EACpBC,qBAAqB;MAAA6N,MAAA;IAAA;EAAA;;SAKZnK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}