{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NgxSkeletonLoaderModule } from \"ngx-skeleton-loader\";\nimport { MatCardModule } from '@angular/material/card';\nimport { FormsModule } from '@angular/forms';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { first } from 'rxjs';\nimport { HttpTableComponent } from 'src/app/components/http-table/http-table.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/inventory.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/share-data.service\";\nimport * as i4 from \"@angular/material/bottom-sheet\";\nimport * as i5 from \"src/app/services/auth.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/notification.service\";\nimport * as i8 from \"@angular/material/dialog\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/tabs\";\nimport * as i11 from \"ngx-skeleton-loader\";\nimport * as i12 from \"@angular/material/card\";\nconst _c0 = [\"openResetDialog\"];\nfunction ParentComponent_mat_tab_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", tab_r1.label, \" \");\n  }\n}\nfunction ParentComponent_mat_tab_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-http-table\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"page\", tab_r1.page)(\"data\", ctx_r3.baseData);\n  }\n}\nconst _c1 = function () {\n  return {\n    \"border-radius\": \"4px\",\n    \"height\": \"30px\",\n    \"margin-bottom\": \"8px\",\n    \"width\": \"19%\",\n    \"margin-right\": \"1%\",\n    \"display\": \"inline-block\",\n    \"opacity\": \"0.85\"\n  };\n};\nfunction ParentComponent_mat_tab_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction ParentComponent_mat_tab_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\");\n    i0.ɵɵtemplate(1, ParentComponent_mat_tab_2_ng_template_1_Template, 1, 1, \"ng-template\", 2);\n    i0.ɵɵtemplate(2, ParentComponent_mat_tab_2_div_2_Template, 2, 2, \"div\", 3);\n    i0.ɵɵtemplate(3, ParentComponent_mat_tab_2_div_3_Template, 2, 2, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDataReady);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDataReady);\n  }\n}\nlet ParentComponent = /*#__PURE__*/(() => {\n  class ParentComponent {\n    constructor(api, router, sharedData, cd, _bottomSheet, auth, masterDataService, notify, dialog) {\n      this.api = api;\n      this.router = router;\n      this.sharedData = sharedData;\n      this.cd = cd;\n      this._bottomSheet = _bottomSheet;\n      this.auth = auth;\n      this.masterDataService = masterDataService;\n      this.notify = notify;\n      this.dialog = dialog;\n      this.selectedTabIndex = -1;\n      this.selectedTabPage = '';\n      this.tabs = [{\n        label: 'Accounts',\n        page: 'account',\n        index: 1,\n        icon: \"add_to_photos\"\n      }];\n      this.isChecked = false;\n      this.isDataReady = false;\n      this.selectedTabClass = 'selected-tab';\n      this.user = this.auth.getCurrentUser();\n    }\n    ngOnInit() {\n      this.masterDataService.refreshTable$.subscribe(() => {\n        this.getBaseData();\n      });\n      this.getBaseData();\n    }\n    getBaseData() {\n      this.isDataReady = false;\n      this.baseData = [];\n      this.cd.detectChanges();\n      this.baseData = this.sharedData.getBaseData().value;\n      let obj = {\n        tenantId: this.user.tenantId\n      };\n      this.masterDataService.route$.pipe(first()).subscribe({\n        next: () => {\n          this.api.getRoloposConfig(obj).pipe(first()).subscribe({\n            next: res => {\n              this.baseData = res.data;\n              this.sharedData.setBaseData(this.baseData);\n              this.isDataReady = true;\n              this.cd.detectChanges();\n            }\n          });\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n    tabClick(tab) {\n      this.selectedTabPage = 'account';\n    }\n    static {\n      this.ɵfac = function ParentComponent_Factory(t) {\n        return new (t || ParentComponent)(i0.ɵɵdirectiveInject(i1.InventoryService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.MatBottomSheet), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.NotificationService), i0.ɵɵdirectiveInject(i8.MatDialog));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ParentComponent,\n        selectors: [[\"app-parent\"]],\n        viewQuery: function ParentComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.openResetDialog = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[3, \"selectedIndex\", \"selectedIndexChange\", \"selectedTabChange\"], [4, \"ngFor\", \"ngForOf\"], [\"mat-tab-label\", \"\"], [4, \"ngIf\"], [\"class\", \"my-3\", 4, \"ngIf\"], [3, \"page\", \"data\"], [1, \"my-3\"], [\"count\", \"50\", \"animation\", \"pulse\", 3, \"theme\"]],\n        template: function ParentComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-card\")(1, \"mat-tab-group\", 0);\n            i0.ɵɵlistener(\"selectedIndexChange\", function ParentComponent_Template_mat_tab_group_selectedIndexChange_1_listener($event) {\n              return ctx.selectedTabIndex = $event;\n            })(\"selectedTabChange\", function ParentComponent_Template_mat_tab_group_selectedTabChange_1_listener($event) {\n              return ctx.tabClick($event);\n            });\n            i0.ɵɵtemplate(2, ParentComponent_mat_tab_2_Template, 4, 2, \"mat-tab\", 1);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTabIndex);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          }\n        },\n        dependencies: [CommonModule, i9.NgForOf, i9.NgIf, MatFormFieldModule, MatInputModule, FormsModule, MatTabsModule, i10.MatTabLabel, i10.MatTab, i10.MatTabGroup, HttpTableComponent, MatButtonModule, MatIconModule, NgxSkeletonLoaderModule, i11.NgxSkeletonLoaderComponent, MatCardModule, i12.MatCard, MatBottomSheetModule],\n        styles: [\".mat-mdc-tab-group[_ngcontent-%COMP%]{margin:5px!important}  .mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:.1!important}\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return ParentComponent;\n})();\nexport { ParentComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}