{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45)(1, \"mat-icon\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r10);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getReportIcon(i_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r9.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActiveFiltersCount(), \" \");\n  }\n}\nfunction SmartDashboardComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r11.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"div\", 51)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 52);\n    i0.ɵɵtext(6, \"Your Dashboard Awaits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 53);\n    i0.ɵɵtext(8, \" Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"div\", 55)(11, \"div\", 56)(12, \"mat-icon\", 57);\n    i0.ɵɵtext(13, \"bar_chart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 58)(15, \"h5\");\n    i0.ɵɵtext(16, \"Interactive Charts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18, \"Dynamic visualizations that respond to your queries\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 55)(20, \"div\", 56)(21, \"mat-icon\", 57);\n    i0.ɵɵtext(22, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 58)(24, \"h5\");\n    i0.ɵɵtext(25, \"Smart Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\");\n    i0.ɵɵtext(27, \"AI-powered analysis and recommendations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 55)(29, \"div\", 56)(30, \"mat-icon\", 57);\n    i0.ɵɵtext(31, \"speed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 58)(33, \"h5\");\n    i0.ɵɵtext(34, \"Real-time Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\");\n    i0.ɵɵtext(36, \"Live updates and instant visualization\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n}\nclass SmartDashboardComponent {\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {\n    // Auto-detect header height from DOM, fallback to default\n    setTimeout(() => {\n      this.detectHeaderHeight();\n    }, 0);\n  }\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  getReportIcon(index) {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n  getSuggestions() {\n    const reportSuggestions = ['Show me top 10 items by quantity', 'Compare costs by location', 'Display monthly trends', 'Analyze vendor performance', 'Show category breakdown'];\n    return reportSuggestions;\n  }\n  applySuggestion(suggestion) {\n    this.chatMessage = suggestion;\n  }\n  /**\n   * Set the header height for proper dashboard height calculation\n   * @param height - Header height in pixels (default: 64)\n   */\n  setHeaderHeight(height = 64) {\n    this.renderer.setStyle(this.elementRef.nativeElement, '--header-height', `${height}px`);\n  }\n  /**\n   * Auto-detect header height from DOM\n   */\n  detectHeaderHeight() {\n    // Try to find common header selectors\n    const headerSelectors = ['mat-toolbar', '.mat-toolbar', 'header', '.header', '.app-header', '.navbar'];\n    for (const selector of headerSelectors) {\n      const headerElement = document.querySelector(selector);\n      if (headerElement) {\n        const height = headerElement.getBoundingClientRect().height;\n        this.setHeaderHeight(height);\n        break;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 92,\n      vars: 22,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"reports-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [\"appearance\", \"outline\", 1, \"report-dropdown\"], [\"placeholder\", \"Select Report Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [\"class\", \"filter-badge\", 4, \"ngIf\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"value\", \"valueChange\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [1, \"filters-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"ai-section\"], [1, \"ai-container\"], [1, \"ai-input-row\"], [1, \"ai-title-compact\"], [1, \"ai-icon\"], [1, \"ai-title\"], [1, \"ai-status\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Ask questions about your data in natural language...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"Send message\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"option-icon\"], [1, \"filter-badge\"], [1, \"selection-count\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"feature-showcase\"], [1, \"feature-item\"], [1, \"feature-icon-wrapper\"], [1, \"feature-icon\"], [1, \"feature-content\"], [1, \"charts-container\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"Reports\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-form-field\", 7)(10, \"mat-select\", 8);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_10_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_10_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵtemplate(11, SmartDashboardComponent_mat_option_11_Template, 4, 3, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 3)(14, \"div\", 4)(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"h3\", 6);\n          i0.ɵɵtext(18, \"Smart Filters\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, SmartDashboardComponent_span_19_Template, 2, 1, \"span\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 13)(22, \"label\", 14)(23, \"mat-icon\", 15);\n          i0.ɵɵtext(24, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 16);\n          i0.ɵɵtext(26, \"Restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, SmartDashboardComponent_span_27_Template, 2, 1, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-form-field\", 18)(29, \"mat-select\", 19);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_29_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵtemplate(30, SmartDashboardComponent_mat_option_30_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 14)(33, \"mat-icon\", 15);\n          i0.ɵɵtext(34, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 16);\n          i0.ɵɵtext(36, \"Base Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-form-field\", 18)(38, \"mat-select\", 20);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_38_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          });\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 13)(41, \"label\", 14)(42, \"mat-icon\", 15);\n          i0.ɵɵtext(43, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 16);\n          i0.ɵɵtext(45, \"Start Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 18)(47, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_47_listener($event) {\n            return ctx.startDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"mat-datepicker-toggle\", 22)(49, \"mat-datepicker\", null, 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 13)(52, \"label\", 14)(53, \"mat-icon\", 15);\n          i0.ɵɵtext(54, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 16);\n          i0.ɵɵtext(56, \"End Date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"mat-form-field\", 18)(58, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_58_listener($event) {\n            return ctx.endDate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"mat-datepicker-toggle\", 22)(60, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(62, \"div\", 26)(63, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_63_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(64, \"mat-icon\");\n          i0.ɵɵtext(65, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_67_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Apply Filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"div\", 29)(72, \"div\", 30)(73, \"div\", 31)(74, \"div\", 32)(75, \"div\", 33)(76, \"mat-icon\", 34);\n          i0.ɵɵtext(77, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 35);\n          i0.ɵɵtext(79, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"span\", 36);\n          i0.ɵɵtext(81);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 37)(83, \"mat-form-field\", 38)(84, \"input\", 39);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_84_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_84_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_85_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(86, \"mat-icon\");\n          i0.ɵɵtext(87, \"send\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(88, \"div\", 41)(89, \"div\", 42);\n          i0.ɵɵtemplate(90, SmartDashboardComponent_div_90_Template, 37, 0, \"div\", 43);\n          i0.ɵɵtemplate(91, SmartDashboardComponent_div_91_Template, 1, 0, \"div\", 44);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(50);\n          const _r6 = i0.ɵɵreference(61);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.locations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(21);\n          i0.ɵɵclassProp(\"ready\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getActiveFiltersCount() > 0 ? \"Ready to analyze\" : \"Configure filters first\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.chatMessage)(\"disabled\", ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.chatMessage.trim() || ctx.getActiveFiltersCount() === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, MatTooltipModule, i9.MatTooltip, FormsModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel],\n      styles: [\"[_nghost-%COMP%] {\\n  --header-height: 86px;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - var(--header-height));\\n  min-height: calc(100vh - var(--header-height));\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(37, 99, 235, 0.06) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n.smart-dashboard-container.header-56[_ngcontent-%COMP%] {\\n  --header-height: 56px;\\n}\\n.smart-dashboard-container.header-64[_ngcontent-%COMP%] {\\n  --header-height: 64px;\\n}\\n.smart-dashboard-container.header-72[_ngcontent-%COMP%] {\\n  --header-height: 72px;\\n}\\n.smart-dashboard-container.header-80[_ngcontent-%COMP%] {\\n  --header-height: 80px;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:first-child {\\n  border-bottom: 1px solid #f3f4f6;\\n  padding-bottom: 1rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: 1rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  position: relative;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1.125rem;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reports-section[_ngcontent-%COMP%]   .report-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.25rem;\\n  overflow-y: auto;\\n  padding-right: 0.5rem;\\n  margin-bottom: 1.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.75rem;\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 0.9rem;\\n  height: 0.9rem;\\n  color: #ff6b35;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 0.375rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n  padding-top: 1.25rem;\\n  border-top: 1px solid #f3f4f6;\\n  margin-top: auto;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 40px;\\n  border-radius: 0.5rem;\\n  color: #4b5563;\\n  border-color: #d1d5db;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n  transform: translateY(-1px);\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  height: 40px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e55a2b, #ff6b35);\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 1rem;\\n  gap: 1rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  padding: 1rem 1.25rem;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #ff6b35, #ff8c5a);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  padding: 2px 8px;\\n  border-radius: 0.375rem;\\n  background: #f3f4f6;\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b);\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1.25rem 1.25rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .dashboard-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .dashboard-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 0.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  color: #4b5563;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.75rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0 0 2rem 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #f9fafb;\\n  border-radius: 0.75rem;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 53, 0.05);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: linear-gradient(135deg, #ff6b35, #ff8c5a);\\n  border-radius: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-icon-wrapper[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ffffff;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1.25rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 300px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - var(--header-height));\\n    min-height: calc(100vh - var(--header-height));\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 50vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-title-compact[_ngcontent-%COMP%]   .ai-status[_ngcontent-%COMP%] {\\n    margin-left: auto;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .ai-section[_ngcontent-%COMP%]   .ai-container[_ngcontent-%COMP%]   .ai-input-row[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    align-self: stretch;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 1.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .feature-showcase[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.5rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 10px 14px !important;\\n  min-height: 24px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.875rem !important;\\n  line-height: 24px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.875rem !important;\\n  line-height: 24px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.5rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 4px !important;\\n  font-size: 0.875rem !important;\\n  min-height: 36px !important;\\n  line-height: 36px !important;\\n  padding: 0 12px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.5rem !important;\\n  font-size: 1rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button,   .mat-mdc-fab {\\n  border-radius: 0.5rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.875rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-mdc-fab {\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-fab:hover {\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .mat-mdc-tooltip {\\n  background: #1f2937 !important;\\n  color: #ffffff !important;\\n  font-size: 0.75rem !important;\\n  border-radius: 0.375rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "MatTooltipModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r10", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getReportIcon", "ɵɵtextInterpolate1", "tab_r9", "label", "ctx_r1", "getActiveFiltersCount", "ctx_r2", "selectedLocations", "length", "location_r11", "value", "baseDate_r12", "ɵɵelement", "SmartDashboardComponent", "constructor", "elementRef", "renderer", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "locations", "checked", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "tabs", "active", "ngOnInit", "setTimeout", "detectHeaderHeight", "toggleFilters", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "console", "log", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "icons", "getSuggestions", "reportSuggestions", "applySuggestion", "suggestion", "setHeaderHeight", "height", "setStyle", "nativeElement", "headerSelectors", "selector", "headerElement", "document", "querySelector", "getBoundingClientRect", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_10_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_10_listener", "ɵɵtemplate", "SmartDashboardComponent_mat_option_11_Template", "SmartDashboardComponent_span_19_Template", "SmartDashboardComponent_span_27_Template", "SmartDashboardComponent_Template_mat_select_valueChange_29_listener", "SmartDashboardComponent_mat_option_30_Template", "SmartDashboardComponent_Template_mat_select_valueChange_38_listener", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_input_ngModelChange_47_listener", "SmartDashboardComponent_Template_input_ngModelChange_58_listener", "SmartDashboardComponent_Template_button_click_63_listener", "SmartDashboardComponent_Template_button_click_67_listener", "SmartDashboardComponent_Template_input_ngModelChange_84_listener", "SmartDashboardComponent_Template_input_keydown_84_listener", "SmartDashboardComponent_Template_button_click_85_listener", "SmartDashboardComponent_div_90_Template", "SmartDashboardComponent_div_91_Template", "_r5", "_r6", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatFormField", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "MatTooltip", "i10", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, Renderer2 } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatTooltipModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor(\n    private elementRef: ElementRef,\n    private renderer: Renderer2\n  ) { }\n\n  ngOnInit(): void {\n    // Auto-detect header height from DOM, fallback to default\n    setTimeout(() => {\n      this.detectHeaderHeight();\n    }, 0);\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    // Handle report type change logic here\n    console.log('Selected report:', this.tabs[index].label);\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n\n  getReportIcon(index: number): string {\n    const icons = ['receipt_long', 'shopping_cart', 'point_of_sale', 'inventory'];\n    return icons[index] || 'assessment';\n  }\n\n  getSuggestions(): string[] {\n    const reportSuggestions = [\n      'Show me top 10 items by quantity',\n      'Compare costs by location',\n      'Display monthly trends',\n      'Analyze vendor performance',\n      'Show category breakdown'\n    ];\n    return reportSuggestions;\n  }\n\n  applySuggestion(suggestion: string): void {\n    this.chatMessage = suggestion;\n  }\n\n  /**\n   * Set the header height for proper dashboard height calculation\n   * @param height - Header height in pixels (default: 64)\n   */\n  setHeaderHeight(height: number = 64): void {\n    this.renderer.setStyle(\n      this.elementRef.nativeElement,\n      '--header-height',\n      `${height}px`\n    );\n  }\n\n  /**\n   * Auto-detect header height from DOM\n   */\n  detectHeaderHeight(): void {\n    // Try to find common header selectors\n    const headerSelectors = [\n      'mat-toolbar',\n      '.mat-toolbar',\n      'header',\n      '.header',\n      '.app-header',\n      '.navbar'\n    ];\n\n    for (const selector of headerSelectors) {\n      const headerElement = document.querySelector(selector);\n      if (headerElement) {\n        const height = headerElement.getBoundingClientRect().height;\n        this.setHeaderHeight(height);\n        break;\n      }\n    }\n  }\n}\n", "<!--\n  Smart Dashboard Container\n  Header height is auto-detected. To manually set header height, use:\n  - CSS class: header-56, header-64, header-72, header-80\n  - Or call: setHeaderHeight(heightInPixels) from component\n-->\n<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Report Type Selection -->\n    <div class=\"sidebar-section reports-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">assessment</mat-icon>\n          <h3 class=\"section-title\">Reports</h3>\n        </div>\n      </div>\n      <mat-form-field appearance=\"outline\" class=\"report-dropdown\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Select Report Type\">\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <mat-icon class=\"option-icon\">{{ getReportIcon(i) }}</mat-icon>\n            {{ tab.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n          <span class=\"filter-badge\" *ngIf=\"getActiveFiltersCount() > 0\">\n            {{ getActiveFiltersCount() }}\n          </span>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedLocations\" multiple placeholder=\"Select restaurants\">\n              <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Start Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">End Date</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Filter Actions -->\n      <div class=\"filters-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply Filters\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- AI Assistant Section - Compact -->\n    <div class=\"ai-section\">\n      <div class=\"ai-container\">\n        <div class=\"ai-input-row\">\n          <div class=\"ai-title-compact\">\n            <mat-icon class=\"ai-icon\">auto_awesome</mat-icon>\n            <span class=\"ai-title\">Smart Dashboard Assistant</span>\n            <span class=\"ai-status\" [class.ready]=\"getActiveFiltersCount() > 0\">\n              {{ getActiveFiltersCount() > 0 ? 'Ready to analyze' : 'Configure filters first' }}\n            </span>\n          </div>\n\n          <div class=\"ai-input-container\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                placeholder=\"Ask questions about your data in natural language...\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"getActiveFiltersCount() === 0\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"!chatMessage.trim() || getActiveFiltersCount() === 0\"\n              matTooltip=\"Send message\"\n            >\n              <mat-icon>send</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <!-- <div class=\"dashboard-header\">\n        <div class=\"header-left\">\n          <mat-icon class=\"dashboard-icon\">dashboard</mat-icon>\n          <div class=\"header-text\">\n            <h3 class=\"dashboard-title\">Smart Dashboard</h3>\n            <p class=\"dashboard-subtitle\">AI-powered data visualization</p>\n          </div>\n        </div>\n        <div class=\"header-actions\" *ngIf=\"hasGeneratedCharts\">\n          <button mat-icon-button matTooltip=\"Refresh charts\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Export data\">\n            <mat-icon>download</mat-icon>\n          </button>\n          <button mat-icon-button matTooltip=\"Full screen\">\n            <mat-icon>fullscreen</mat-icon>\n          </button>\n        </div>\n      </div> -->\n\n      <div class=\"dashboard-content\">\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!hasGeneratedCharts\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>insights</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">Your Dashboard Awaits</h4>\n            <p class=\"empty-state-description\">\n              Set up your filters and ask the AI assistant to create beautiful, interactive visualizations from your data.\n            </p>\n\n            <div class=\"feature-showcase\">\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Interactive Charts</h5>\n                  <p>Dynamic visualizations that respond to your queries</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Smart Insights</h5>\n                  <p>AI-powered analysis and recommendations</p>\n                </div>\n              </div>\n\n              <div class=\"feature-item\">\n                <div class=\"feature-icon-wrapper\">\n                  <mat-icon class=\"feature-icon\">speed</mat-icon>\n                </div>\n                <div class=\"feature-content\">\n                  <h5>Real-time Data</h5>\n                  <p>Live updates and instant visualization</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"hasGeneratedCharts\">\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICKlCC,EAAA,CAAAC,cAAA,qBAAgE;IAChCD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IAHuCH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAC/BL,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAJ,KAAA,EAAsB;IACpDL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAWAZ,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAG,MAAA,CAAAC,qBAAA,QACF;;;;;IAUEd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,OAAAK,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IAIEjB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAC,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAQ,YAAA,CAAAN,KAAA,MACF;;;;;IAaAZ,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAgB,YAAA,CAAAD,KAAA,CAAwB;IACrEnB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAU,YAAA,CAAAR,KAAA,MACF;;;;;IA2HNZ,EAAA,CAAAC,cAAA,cAAqD;IAGrCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,qHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,cAA8B;IAGOD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAErDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,2DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9DH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEvDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIlDH,EAAA,CAAAC,cAAA,eAA0B;IAESD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjDH,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQvDH,EAAA,CAAAqB,SAAA,cAEM;;;AD9Nd,MAsBaC,uBAAuB;EAqClCC,YACUC,UAAsB,EACtBC,QAAmB;IADnB,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,QAAQ,GAARA,QAAQ;IAtClB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CACV;MAAEV,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,wBAAwB;MAAEkB,OAAO,EAAE;IAAK,CAAE,EACzE;MAAEX,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,iBAAiB;MAAEkB,OAAO,EAAE;IAAK,CAAE,EAClE;MAAEX,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,aAAa;MAAEkB,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAEX,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,gBAAgB;MAAEkB,OAAO,EAAE;IAAK,CAAE,EACjE;MAAEX,KAAK,EAAE,aAAa;MAAEP,KAAK,EAAE,oBAAoB;MAAEkB,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAAC,SAAS,GAAG,CACV;MAAEZ,KAAK,EAAE,OAAO;MAAEP,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEO,KAAK,EAAE,YAAY;MAAEP,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEO,KAAK,EAAE,QAAQ;MAAEP,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAI,iBAAiB,GAAa,EAAE;IAChC,KAAAgB,gBAAgB,GAAG,OAAO;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAC,IAAI,GAAG,CACL;MAAExB,KAAK,EAAE,YAAY;MAAEyB,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEzB,KAAK,EAAE,iBAAiB;MAAEyB,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEzB,KAAK,EAAE,cAAc;MAAEyB,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEzB,KAAK,EAAE,kBAAkB;MAAEyB,MAAM,EAAE;IAAK,CAAE,CAC7C;EAKG;EAEJC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,CAAC,CAAC;EACP;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACf,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAgB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAChB,WAAW,GAAGgB,KAAK;IACxB,IAAI,CAACP,IAAI,CAACQ,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACR,MAAM,GAAGS,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF;IACAI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACZ,IAAI,CAACO,KAAK,CAAC,CAAC/B,KAAK,CAAC;EACzD;EAEAqC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACd,WAAW,CAACe,IAAI,EAAE,EAAE;MAC3B;MACAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,WAAW,CAAC;MAE9C;MACA,IAAI,CAACP,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACO,WAAW,GAAG,EAAE;;EAEzB;EAEAgB,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAEAO,YAAYA,CAAA;IACV;IACAT,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACtB,aAAa,GAAG,KAAK;EAC5B;EAEA+B,YAAYA,CAAA;IACV;IACA,IAAI,CAACzC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACgB,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACL,SAAS,CAACe,OAAO,CAACc,QAAQ,IAAIA,QAAQ,CAAC5B,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEAhB,qBAAqBA,CAAA;IACnB,IAAI6C,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC3C,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE0C,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC3B,gBAAgB,KAAK,OAAO,EAAE2B,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC1B,SAAS,EAAE0B,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACzB,OAAO,EAAEyB,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;EAEAlD,aAAaA,CAACkC,KAAa;IACzB,MAAMiB,KAAK,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;IAC7E,OAAOA,KAAK,CAACjB,KAAK,CAAC,IAAI,YAAY;EACrC;EAEAkB,cAAcA,CAAA;IACZ,MAAMC,iBAAiB,GAAG,CACxB,kCAAkC,EAClC,2BAA2B,EAC3B,wBAAwB,EACxB,4BAA4B,EAC5B,yBAAyB,CAC1B;IACD,OAAOA,iBAAiB;EAC1B;EAEAC,eAAeA,CAACC,UAAkB;IAChC,IAAI,CAAC7B,WAAW,GAAG6B,UAAU;EAC/B;EAEA;;;;EAIAC,eAAeA,CAACC,MAAA,GAAiB,EAAE;IACjC,IAAI,CAACzC,QAAQ,CAAC0C,QAAQ,CACpB,IAAI,CAAC3C,UAAU,CAAC4C,aAAa,EAC7B,iBAAiB,EACjB,GAAGF,MAAM,IAAI,CACd;EACH;EAEA;;;EAGA1B,kBAAkBA,CAAA;IAChB;IACA,MAAM6B,eAAe,GAAG,CACtB,aAAa,EACb,cAAc,EACd,QAAQ,EACR,SAAS,EACT,aAAa,EACb,SAAS,CACV;IAED,KAAK,MAAMC,QAAQ,IAAID,eAAe,EAAE;MACtC,MAAME,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAACH,QAAQ,CAAC;MACtD,IAAIC,aAAa,EAAE;QACjB,MAAML,MAAM,GAAGK,aAAa,CAACG,qBAAqB,EAAE,CAACR,MAAM;QAC3D,IAAI,CAACD,eAAe,CAACC,MAAM,CAAC;QAC5B;;;EAGN;;;uBA/JW5C,uBAAuB,EAAAtB,EAAA,CAAA2E,iBAAA,CAAA3E,EAAA,CAAA4E,UAAA,GAAA5E,EAAA,CAAA2E,iBAAA,CAAA3E,EAAA,CAAA6E,SAAA;IAAA;EAAA;;;YAAvBvD,uBAAuB;MAAAwD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhF,EAAA,CAAAiF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCpCvF,EAAA,CAAAC,cAAA,aAAuC;UAOED,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAAA,CAAAC,cAAA,wBAA6D;UAC/CD,EAAA,CAAAyF,UAAA,yBAAAC,oEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA7D,WAAA,GAAAgE,MAAA;UAAA,EAAuB,6BAAAC,wEAAAD,MAAA;YAAA,OAAoBH,GAAA,CAAA9C,WAAA,CAAAiD,MAAA,CAAAxE,KAAA,CAAyB;UAAA,EAA7C;UACjCnB,EAAA,CAAA6F,UAAA,KAAAC,8CAAA,wBAGa;UACf9F,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAA6F,UAAA,KAAAE,wCAAA,mBAEO;UACT/F,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAA6F,UAAA,KAAAG,wCAAA,mBAEO;UACThG,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAyF,UAAA,yBAAAQ,oEAAAN,MAAA;YAAA,OAAAH,GAAA,CAAAxE,iBAAA,GAAA2E,MAAA;UAAA,EAA6B;UACvC3F,EAAA,CAAA6F,UAAA,KAAAK,8CAAA,wBAEa;UACflG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE3CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAyF,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAH,GAAA,CAAAxD,gBAAA,GAAA2D,MAAA;UAAA,EAA4B;UACtC3F,EAAA,CAAA6F,UAAA,KAAAO,8CAAA,wBAEa;UACfpG,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAyF,UAAA,2BAAAY,iEAAAV,MAAA;YAAA,OAAAH,GAAA,CAAAvD,SAAA,GAAA0D,MAAA;UAAA,EAAuB;UAHzB3F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAqB,SAAA,iCAA6E;UAE/ErB,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAyF,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAH,GAAA,CAAAtD,OAAA,GAAAyD,MAAA;UAAA,EAAqB;UAHvB3F,EAAA,CAAAG,YAAA,EAMC;UACDH,EAAA,CAAAqB,SAAA,iCAA2E;UAE7ErB,EAAA,CAAAG,YAAA,EAAiB;UAKrBH,EAAA,CAAAC,cAAA,eAA6B;UACkBD,EAAA,CAAAyF,UAAA,mBAAAc,0DAAA;YAAA,OAASf,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UACnEzD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAyF,UAAA,mBAAAe,0DAAA;YAAA,OAAShB,GAAA,CAAAhC,YAAA,EAAc;UAAA,EAAC;UAClFxD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMUD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvDH,EAAA,CAAAC,cAAA,gBAAoE;UAClED,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGTH,EAAA,CAAAC,cAAA,eAAgC;UAM1BD,EAAA,CAAAyF,UAAA,2BAAAgB,iEAAAd,MAAA;YAAA,OAAAH,GAAA,CAAArD,WAAA,GAAAwD,MAAA;UAAA,EAAyB,qBAAAe,2DAAAf,MAAA;YAAA,OACdH,GAAA,CAAArC,UAAA,CAAAwC,MAAA,CAAkB;UAAA,EADJ;UAJ3B3F,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAyF,UAAA,mBAAAkB,0DAAA;YAAA,OAASnB,GAAA,CAAAvC,WAAA,EAAa;UAAA,EAAC;UAIvBjD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQnCH,EAAA,CAAAC,cAAA,eAA+B;UAwB3BD,EAAA,CAAA6F,UAAA,KAAAe,uCAAA,mBA0CM;UAGN5G,EAAA,CAAA6F,UAAA,KAAAgB,uCAAA,kBAEM;UACR7G,EAAA,CAAAG,YAAA,EAAM;;;;;UA7NQH,EAAA,CAAAM,SAAA,IAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAAoF,GAAA,CAAA7D,WAAA,CAAuB;UACL3B,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAApD,IAAA,CAAS;UAcTpC,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,SAAAoF,GAAA,CAAA1E,qBAAA,OAAiC;UAY5Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAAoF,GAAA,CAAAxE,iBAAA,CAAAC,MAAA,KAAkC;UAKrDjB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,UAAAoF,GAAA,CAAAxE,iBAAA,CAA6B;UACNhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAA3D,SAAA,CAAY;UAcnC7B,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAAoF,GAAA,CAAAxD,gBAAA,CAA4B;UACLhC,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAAzD,SAAA,CAAY;UAgB7C/B,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA0G,GAAA,CAA6B,YAAAtB,GAAA,CAAAvD,SAAA;UAKEjC,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA0G,GAAA,CAAmB;UAclD9G,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA2G,GAAA,CAA2B,YAAAvB,GAAA,CAAAtD,OAAA;UAKIlC,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA2G,GAAA,CAAiB;UA6B1B/G,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAgH,WAAA,UAAAxB,GAAA,CAAA1E,qBAAA,OAA2C;UACjEd,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAU,kBAAA,MAAA8E,GAAA,CAAA1E,qBAAA,6DACF;UASId,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAArD,WAAA,CAAyB,aAAAqD,GAAA,CAAA1E,qBAAA;UAU3Bd,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,cAAAoF,GAAA,CAAArD,WAAA,CAAAe,IAAA,MAAAsC,GAAA,CAAA1E,qBAAA,SAAiE;UAmC7Cd,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,UAAAoF,GAAA,CAAA5D,kBAAA,CAAyB;UA6CpB5B,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAoF,GAAA,CAAA5D,kBAAA,CAAwB;;;qBDxN3D1C,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhI,aAAa,EACbC,eAAe,EAAAgI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjI,aAAa,EAAAkI,EAAA,CAAAC,OAAA,EACblI,kBAAkB,EAAAmI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBpI,eAAe,EAAAqI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfvI,cAAc,EAAAwI,EAAA,CAAAC,QAAA,EACdxI,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAAsI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBxI,mBAAmB,EACnBC,gBAAgB,EAAAwI,EAAA,CAAAC,UAAA,EAChBxI,WAAW,EAAAyI,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKFtH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}