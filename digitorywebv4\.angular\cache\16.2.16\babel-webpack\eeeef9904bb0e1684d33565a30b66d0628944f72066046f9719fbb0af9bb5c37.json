{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/share-data.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/auth.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/toolbar\";\nimport * as i10 from \"@angular/material/menu\";\nconst _c0 = [\"allSelected\"];\nfunction DashboardToolbarComponent_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DashboardToolbarComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 20)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", item_r3.path);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n  }\n}\nclass DashboardToolbarComponent {\n  constructor(selectedBranchesService, dialog, router, auth, sharedData, cd, notify) {\n    this.selectedBranchesService = selectedBranchesService;\n    this.dialog = dialog;\n    this.router = router;\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.showNavbarToggleButton = false;\n    this.menuItems = [];\n    this.logoUrl = '';\n    this.toggleMenu = new EventEmitter();\n    this.branchList = [];\n    this.branches = new FormControl('');\n    this.globalLocation = new FormControl();\n    this.VendorBank = [];\n    this.vendorFilterCtrl = new FormControl();\n    this.vendorsBanks = new ReplaySubject(1);\n    this._onDestroy = new Subject();\n    this.cardDesc = '';\n    this.showBanner = false;\n    this.message = 'Update to latest version by pressing';\n    this.rolesList = [];\n    this.links = [];\n    this.filteredBranches = [];\n    // No hardcoded categories needed\n    this.categories = [];\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    this.globalLocation.setValue(this.user.restaurantAccess[0]);\n    this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    this.sharedData.getVersionNumber.subscribe(data => {\n      this.versionNumber = data;\n    });\n    this.sharedData.checkSettingAvailable.subscribe(data => {\n      this.enableSettingBtn = data;\n    });\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      if (this.versionNumber !== data['versionUI']) {\n        this.showBanner = true;\n        // this.notify.openSnackBar(\n        //   'Update to latest version by pressing CTL + SHIFT + R'\n        // );\n      } else {\n        this.showBanner = false;\n      }\n      this.cd.detectChanges();\n    });\n  }\n  ngOnInit() {\n    this.VendorBank = this.user.restaurantAccess.filter(branch => branch && branch.branchName);\n    this.vendorsBanks.next(this.VendorBank.slice());\n    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\\\s]*'));\n    this.selectedBranchesService.updateSelectedBranches(this.user.restaurantAccess);\n    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe(newValue => {\n      this.vendorfilterBanks(newValue);\n    });\n  }\n  applyFilter(event) {\n    const filterValue = event.target.value;\n    this.filteredBranches = this.user.restaurantAccess.filter(branch => branch && branch.branchName.toLowerCase().includes(filterValue.toLowerCase()));\n  }\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n  vendorfilterBanks(newValue) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(this.VendorBank.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)));\n    } else {\n      const searchTerms = search.split(' ').filter(term => term.trim() !== '');\n      this.vendorsBanks.next(this.VendorBank.filter(branch => {\n        const branchNameLowerCase = branch.branchName.toLowerCase();\n        return searchTerms.every(term => branchNameLowerCase.includes(term));\n      }));\n    }\n  }\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n  static {\n    this.ɵfac = function DashboardToolbarComponent_Factory(t) {\n      return new (t || DashboardToolbarComponent)(i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i1.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardToolbarComponent,\n      selectors: [[\"app-dashboard-toolbar\"]],\n      viewQuery: function DashboardToolbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatSidenav, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allSelected = _t.first);\n        }\n      },\n      inputs: {\n        showNavbarToggleButton: \"showNavbarToggleButton\",\n        menuItems: \"menuItems\",\n        logoUrl: \"logoUrl\",\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      outputs: {\n        toggleMenu: \"toggleMenu\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 28,\n      vars: 7,\n      consts: [[1, \"toolbar-left\"], [1, \"logo-container\"], [\"alt\", \"Company Logo\", \"class\", \"company-logo\", 3, \"src\", 4, \"ngIf\"], [1, \"nav-menu\"], [4, \"ngFor\", \"ngForOf\"], [1, \"example-spacer\"], [1, \"formal-text\"], [1, \"beta-tag\"], [1, \"user-info\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"], [\"xPosition\", \"before\"], [\"beforeMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-gear\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fa-solid\", \"fa-right-from-bracket\"], [\"alt\", \"Company Logo\", 1, \"company-logo\", 3, \"src\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"]],\n      template: function DashboardToolbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵtemplate(3, DashboardToolbarComponent_img_3_Template, 1, 1, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtemplate(5, DashboardToolbarComponent_ng_container_5_Template, 6, 3, \"ng-container\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementStart(9, \"span\", 7);\n          i0.ɵɵtext(10, \"Beta\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9)(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"span\", 11);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\", 12);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"mat-menu\", 13, 14)(22, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_22_listener() {\n            return ctx.setting();\n          });\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵtext(24, \" \\u00A0 Setting \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardToolbarComponent_Template_button_click_25_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(26, \"i\", 18);\n          i0.ɵɵtext(27, \" \\u00A0 Logout \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(21);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.versionNumber, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r2);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.user == null ? null : ctx.user.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.cardDesc);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.enableSettingBtn);\n        }\n      },\n      dependencies: [FormsModule, ReactiveFormsModule, MatDialogModule, CommonModule, i6.NgForOf, i6.NgIf, MatIconModule, i7.MatIcon, MatButtonModule, i8.MatAnchor, i8.MatButton, MatFormFieldModule, MatToolbarModule, i9.MatToolbar, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatSelectModule, MatTooltipModule, MatCardModule, RouterModule, i3.RouterLink, i3.RouterLinkActive],\n      styles: [\"mat-toolbar[_ngcontent-%COMP%] {\\n  background-color: white; \\n\\n  color: #333;\\n  padding: 0 10px; \\n\\n  height: 52px; \\n\\n  min-height: 52px;\\n  display: flex;\\n  align-items: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid #ff9100; \\n\\n  border-bottom: 1px solid #e0e0e0; \\n\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  padding-left: 4px; \\n\\n}\\n\\n\\n\\n\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 0; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  padding: 0 12px; \\n\\n  height: 52px; \\n\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0;\\n  color: #333;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  margin: 0 1px; \\n\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:first-child {\\n  padding-left: 8px; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]     .mat-mdc-button-touch-target {\\n  height: 100%;\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #ff9100; \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  background-color: rgba(255, 145, 0, 0.05);\\n  \\n\\n}\\n.nav-menu[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -1px; \\n\\n  left: 0;\\n  width: 100%;\\n  height: 2px; \\n\\n  background-color: #ff9100;\\n}\\n\\n\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n\\n  .mat-toolbar-row, .mat-toolbar-single-row[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n  height: 52px !important;\\n  max-height: 52px !important;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 12px;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0 10px;\\n  border-radius: 4px;\\n  transition: background-color 0.2s ease;\\n  height: 42px;\\n}\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 145, 0, 0.05);\\n}\\n.user-menu-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n  color: #ff9100; \\n\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  line-height: 1.2;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-size: 14px; \\n\\n  font-weight: 500;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  font-size: 12px; \\n\\n  opacity: 0.8;\\n}\\n\\n.formal-text[_ngcontent-%COMP%] {\\n  font-family: Arial, sans-serif;\\n  font-size: 14px; \\n\\n  text-align: right;\\n  line-height: 1.5;\\n  margin: 0 12px;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n\\n.beta-tag[_ngcontent-%COMP%] {\\n  color: #ff9100;\\n  font-weight: bold;\\n  margin-left: 3px;\\n  font-size: 12px; \\n\\n  background-color: rgba(255, 145, 0, 0.1);\\n  padding: 2px 6px; \\n\\n  border-radius: 3px;\\n}\\n\\n.mat-mdc-button[_ngcontent-%COMP%]    > .mat-icon[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%]     .mdc-text-field--filled:not(.mdc-text-field--disabled) {\\n  background-color: #ebebeb;\\n}\\n\\n.globalSelectFormField[_ngcontent-%COMP%] {\\n  width: 215px;\\n  height: 45px;\\n  margin-bottom: 10px;\\n  margin-right: 5px;\\n}\\n\\n.globalSelectInput[_ngcontent-%COMP%] {\\n  height: 30px;\\n  padding: 10px;\\n}\\n\\n.mdc-text-field--no-label[_ngcontent-%COMP%]:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea)   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding-top: 14px;\\n  padding-bottom: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardToolbarComponent };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "MatIconModule", "MatButtonModule", "MatToolbarModule", "MatDialogModule", "DialogComponent", "MatMenuModule", "RouterModule", "MatSelectModule", "MatFormFieldModule", "FormControl", "FormsModule", "ReactiveFormsModule", "MatTooltipModule", "MatCardModule", "<PERSON><PERSON><PERSON><PERSON>", "ReplaySubject", "Subject", "debounceTime", "distinctUntilChanged", "Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "logoUrl", "ɵɵsanitizeUrl", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "item_r3", "path", "ɵɵtextInterpolate", "icon", "title", "DashboardToolbarComponent", "constructor", "selectedBranchesService", "dialog", "router", "auth", "sharedData", "cd", "notify", "showNavbarToggleButton", "menuItems", "toggleMenu", "branchList", "branches", "globalLocation", "VendorBank", "vendorFilterCtrl", "vendorsBanks", "_onD<PERSON>roy", "cardDesc", "showBanner", "message", "rolesList", "links", "filteredBranches", "categories", "user", "getCurrentUser", "role", "setValue", "restaurantAccess", "setGlLocation", "getVersionNumber", "subscribe", "data", "versionNumber", "checkSettingAvailable", "enableSettingBtn", "getRolesList", "tenantId", "detectChanges", "ngOnInit", "filter", "branch", "branchName", "next", "slice", "pattern", "updateSelectedBranches", "valueChanges", "pipe", "newValue", "vendorfilterBanks", "applyFilter", "event", "filterValue", "target", "value", "toLowerCase", "includes", "setting", "navigate", "search", "replace", "searchTerms", "split", "term", "trim", "branchNameLowerCase", "every", "logout", "dialogRef", "open", "autoFocus", "afterClosed", "result", "localStorage", "clear", "sessionStorage", "window", "location", "reload", "restaurantChange", "ɵɵdirectiveInject", "i1", "ShareDataService", "i2", "MatDialog", "i3", "Router", "i4", "AuthService", "ChangeDetectorRef", "i5", "NotificationService", "selectors", "viewQuery", "DashboardToolbarComponent_Query", "rf", "ctx", "ɵɵtemplate", "DashboardToolbarComponent_img_3_Template", "DashboardToolbarComponent_ng_container_5_Template", "ɵɵlistener", "DashboardToolbarComponent_Template_button_click_22_listener", "DashboardToolbarComponent_Template_button_click_25_listener", "ɵɵtextInterpolate1", "_r2", "name", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatIcon", "i8", "<PERSON><PERSON><PERSON><PERSON>", "MatButton", "i9", "MatToolbar", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "RouterLink", "RouterLinkActive", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/dashboard-toolbar/dashboard-toolbar.component.html"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  Output,\n  OnInit,\n  EventEmitter,\n  ViewChild,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIcon, MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { GlobalsService } from 'src/app/services/globals.service';\nimport { DialogComponent } from 'src/app/pages/dialog/dialog.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCardModule } from '@angular/material/card';\nimport { first } from 'rxjs';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatOption } from '@angular/material/core';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { Validators } from '@angular/forms';\n@Component({\n  selector: 'app-dashboard-toolbar',\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    CommonModule,\n    MatIconModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatToolbarModule,\n    MatMenuModule,\n    MatSelectModule,\n    MatTooltipModule,\n    MatCardModule,\n    RouterModule,\n  ],\n  templateUrl: './dashboard-toolbar.component.html',\n  styleUrls: ['./dashboard-toolbar.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class DashboardToolbarComponent {\n  user: any;\n  @Input() showNavbarToggleButton = false;\n  @Input() menuItems: any[] = [];\n  @Input() logoUrl: string = '';\n  @Output() toggleMenu = new EventEmitter();\n  public branchList = [];\n  public branches = new FormControl('');\n  public globalLocation: FormControl = new FormControl();\n  public VendorBank: any[] = [];\n  public vendorFilterCtrl: FormControl = new FormControl();\n  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);\n  protected _onDestroy = new Subject<void>();\n  cardDesc: string = '';\n  enableSettingBtn: boolean;\n  @ViewChild(MatSidenav)\n  sidenav!: MatSidenav;\n  @Input() showBanner: boolean = false;\n  @ViewChild('allSelected') private allSelected: MatOption;\n  @Input() message: string = 'Update to latest version by pressing';\n  versionNumber: string;\n  rolesList: any = [];\n  links: any = [];\n  access: any;\n  filteredBranches: any[] = [];\n\n  // No hardcoded categories needed\n  categories = [];\n  constructor(\n    private selectedBranchesService: ShareDataService,\n    private dialog: MatDialog,\n    private router: Router,\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService\n  ) {\n    this.user = this.auth.getCurrentUser();\n    this.cardDesc += this.user.role;\n    this.globalLocation.setValue(this.user.restaurantAccess[0]);\n    this.sharedData.setGlLocation(this.user.restaurantAccess[0]);\n    this.sharedData.getVersionNumber.subscribe((data) => {\n      this.versionNumber = data;\n    });\n    this.sharedData.checkSettingAvailable.subscribe((data) => {\n      this.enableSettingBtn = data;\n    });\n    this.auth\n      .getRolesList({ tenantId: this.user.tenantId })\n      .subscribe((data) => {\n        if (this.versionNumber !== data['versionUI']) {\n          this.showBanner = true;\n          // this.notify.openSnackBar(\n          //   'Update to latest version by pressing CTL + SHIFT + R'\n          // );\n        } else {\n          this.showBanner = false;\n        }\n        this.cd.detectChanges();\n      });\n  }\n  ngOnInit() {\n    this.VendorBank = this.user.restaurantAccess.filter(\n      (branch) => branch && branch.branchName\n    );\n    this.vendorsBanks.next(this.VendorBank.slice());\n    this.vendorFilterCtrl = new FormControl(\n      '',\n      Validators.pattern('[a-zA-Z0-9\\\\s]*')\n    );\n    this.selectedBranchesService.updateSelectedBranches(\n      this.user.restaurantAccess\n    );\n    this.vendorFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged()\n      )\n      .subscribe((newValue) => {\n        this.vendorfilterBanks(newValue);\n      });\n  }\n\n\n\n  applyFilter(event: Event) {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.filteredBranches = this.user.restaurantAccess.filter(\n      (branch) =>\n        branch &&\n        branch.branchName.toLowerCase().includes(filterValue.toLowerCase())\n    );\n  }\n\n  setting() {\n    this.router.navigate(['/dashboard/setting']);\n  }\n\n  protected vendorfilterBanks(newValue?: unknown) {\n    if (!this.VendorBank) {\n      return;\n    }\n    let search = this.vendorFilterCtrl.value;\n    if (!search) {\n      this.vendorsBanks.next(this.VendorBank.slice());\n      return;\n    } else {\n      search = search.toLowerCase();\n    }\n    if (!search.includes(' ')) {\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) =>\n          branch.branchName.toLowerCase().replace(/\\s/g, '').includes(search)\n        )\n      );\n    } else {\n      const searchTerms = search\n        .split(' ')\n        .filter((term) => term.trim() !== '');\n      this.vendorsBanks.next(\n        this.VendorBank.filter((branch) => {\n          const branchNameLowerCase = branch.branchName.toLowerCase();\n          return searchTerms.every((term) =>\n            branchNameLowerCase.includes(term)\n          );\n        })\n      );\n    }\n  }\n\n  logout() {\n    const dialogRef = this.dialog.open(DialogComponent, {\n      autoFocus: false,\n      data: {\n        message: 'Are you sure you want to logout?',\n        title: 'Logout',\n      },\n    });\n\n    dialogRef.afterClosed().subscribe((result) => {\n      if (result) {\n        localStorage.clear();\n        sessionStorage.clear();\n        window.location.reload();\n        this.router.navigate(['/signin']);\n      }\n    });\n  }\n\n  restaurantChange(event) {\n    this.sharedData.setGlLocation(event);\n  }\n}\n", "<mat-toolbar>\n  <div class=\"toolbar-left\">\n    <div class=\"logo-container\">\n      <img *ngIf=\"logoUrl\" [src]=\"logoUrl\" alt=\"Company Logo\" class=\"company-logo\">\n    </div>\n\n    <!-- Main Navigation Menu -->\n    <div class=\"nav-menu\">\n      <ng-container *ngFor=\"let item of menuItems\">\n        <a mat-button [routerLink]=\"item.path\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>{{item.icon}}</mat-icon>\n          <span>{{item.title}}</span>\n        </a>\n      </ng-container>\n    </div>\n  </div>\n\n  <span class=\"example-spacer\"></span>\n\n  <p class=\"formal-text\">{{ versionNumber }} <span class=\"beta-tag\">Beta</span></p>\n  <div class=\"user-info\">\n    <button mat-button [matMenuTriggerFor]=\"beforeMenu\" class=\"user-menu-button\">\n      <mat-icon>account_circle</mat-icon>\n      <div class=\"user-details\">\n        <span class=\"user-name\">{{ user?.name }}</span>\n        <span class=\"user-role\">{{ cardDesc }}</span>\n      </div>\n    </button>\n  </div>\n\n  <mat-menu #beforeMenu=\"matMenu\" xPosition=\"before\">\n    <button mat-menu-item (click)=\"setting()\" [disabled]=\"!enableSettingBtn\">\n      <i class=\"fa-solid fa-gear\"></i> &nbsp; Setting\n    </button>\n    <button mat-menu-item (click)=\"logout()\">\n      <i class=\"fa-solid fa-right-from-bracket\"></i> &nbsp; Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>"], "mappings": "AAAA,SAMEA,YAAY,QAGP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAkBC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAoBC,eAAe,QAAQ,0BAA0B;AAErE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAG9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAItD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,oBAAoB,QAAmB,gBAAgB;AAC9E,SAASC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;IC7BrCC,EAAA,CAAAC,SAAA,cAA6E;;;;IAAxDD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAJ,EAAA,CAAAK,aAAA,CAAe;;;;;IAKpCL,EAAA,CAAAM,uBAAA,GAA6C;IAC3CN,EAAA,CAAAO,cAAA,YAAkF;IACtEP,EAAA,CAAAQ,MAAA,GAAa;IAAAR,EAAA,CAAAS,YAAA,EAAW;IAClCT,EAAA,CAAAO,cAAA,WAAM;IAAAP,EAAA,CAAAQ,MAAA,GAAc;IAAAR,EAAA,CAAAS,YAAA,EAAO;IAE/BT,EAAA,CAAAU,qBAAA,EAAe;;;;IAJCV,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAE,UAAA,eAAAU,OAAA,CAAAC,IAAA,CAAwB;IAC1Bb,EAAA,CAAAW,SAAA,GAAa;IAAbX,EAAA,CAAAc,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAa;IACjBf,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAc,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAc;;;ADsB9B,MAsBaC,yBAAyB;EA4BpCC,YACUC,uBAAyC,EACzCC,MAAiB,EACjBC,MAAc,EACdC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B;IAN3B,KAAAN,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IAjCP,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAvB,OAAO,GAAW,EAAE;IACnB,KAAAwB,UAAU,GAAG,IAAIlD,YAAY,EAAE;IAClC,KAAAmD,UAAU,GAAG,EAAE;IACf,KAAAC,QAAQ,GAAG,IAAIzC,WAAW,CAAC,EAAE,CAAC;IAC9B,KAAA0C,cAAc,GAAgB,IAAI1C,WAAW,EAAE;IAC/C,KAAA2C,UAAU,GAAU,EAAE;IACtB,KAAAC,gBAAgB,GAAgB,IAAI5C,WAAW,EAAE;IACjD,KAAA6C,YAAY,GAAyB,IAAIvC,aAAa,CAAQ,CAAC,CAAC;IAC7D,KAAAwC,UAAU,GAAG,IAAIvC,OAAO,EAAQ;IAC1C,KAAAwC,QAAQ,GAAW,EAAE;IAIZ,KAAAC,UAAU,GAAY,KAAK;IAE3B,KAAAC,OAAO,GAAW,sCAAsC;IAEjE,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,KAAK,GAAQ,EAAE;IAEf,KAAAC,gBAAgB,GAAU,EAAE;IAE5B;IACA,KAAAC,UAAU,GAAG,EAAE;IAUb,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,IAAI,CAACsB,cAAc,EAAE;IACtC,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACO,IAAI,CAACE,IAAI;IAC/B,IAAI,CAACd,cAAc,CAACe,QAAQ,CAAC,IAAI,CAACH,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACxB,UAAU,CAACyB,aAAa,CAAC,IAAI,CAACL,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACxB,UAAU,CAAC0B,gBAAgB,CAACC,SAAS,CAAEC,IAAI,IAAI;MAClD,IAAI,CAACC,aAAa,GAAGD,IAAI;IAC3B,CAAC,CAAC;IACF,IAAI,CAAC5B,UAAU,CAAC8B,qBAAqB,CAACH,SAAS,CAAEC,IAAI,IAAI;MACvD,IAAI,CAACG,gBAAgB,GAAGH,IAAI;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC7B,IAAI,CACNiC,YAAY,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACb,IAAI,CAACa;IAAQ,CAAE,CAAC,CAC9CN,SAAS,CAAEC,IAAI,IAAI;MAClB,IAAI,IAAI,CAACC,aAAa,KAAKD,IAAI,CAAC,WAAW,CAAC,EAAE;QAC5C,IAAI,CAACd,UAAU,GAAG,IAAI;QACtB;QACA;QACA;OACD,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,KAAK;;MAEzB,IAAI,CAACb,EAAE,CAACiC,aAAa,EAAE;IACzB,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA;IACN,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAACW,IAAI,CAACI,gBAAgB,CAACY,MAAM,CAChDC,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAACC,UAAU,CACxC;IACD,IAAI,CAAC3B,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;IAC/C,IAAI,CAAC9B,gBAAgB,GAAG,IAAI5C,WAAW,CACrC,EAAE,EACFU,UAAU,CAACiE,OAAO,CAAC,iBAAiB,CAAC,CACtC;IACD,IAAI,CAAC7C,uBAAuB,CAAC8C,sBAAsB,CACjD,IAAI,CAACtB,IAAI,CAACI,gBAAgB,CAC3B;IACD,IAAI,CAACd,gBAAgB,CAACiC,YAAY,CAC/BC,IAAI,CACHtE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACAoD,SAAS,CAAEkB,QAAQ,IAAI;MACtB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC;EACN;EAIAE,WAAWA,CAACC,KAAY;IACtB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK;IAC5D,IAAI,CAACjC,gBAAgB,GAAG,IAAI,CAACE,IAAI,CAACI,gBAAgB,CAACY,MAAM,CACtDC,MAAM,IACLA,MAAM,IACNA,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE,CAACC,QAAQ,CAACJ,WAAW,CAACG,WAAW,EAAE,CAAC,CACtE;EACH;EAEAE,OAAOA,CAAA;IACL,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEUT,iBAAiBA,CAACD,QAAkB;IAC5C,IAAI,CAAC,IAAI,CAACpC,UAAU,EAAE;MACpB;;IAEF,IAAI+C,MAAM,GAAG,IAAI,CAAC9C,gBAAgB,CAACyC,KAAK;IACxC,IAAI,CAACK,MAAM,EAAE;MACX,IAAI,CAAC7C,YAAY,CAAC4B,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE,CAAC;MAC/C;KACD,MAAM;MACLgB,MAAM,GAAGA,MAAM,CAACJ,WAAW,EAAE;;IAE/B,IAAI,CAACI,MAAM,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC1C,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAC5BA,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACJ,QAAQ,CAACG,MAAM,CAAC,CACpE,CACF;KACF,MAAM;MACL,MAAME,WAAW,GAAGF,MAAM,CACvBG,KAAK,CAAC,GAAG,CAAC,CACVvB,MAAM,CAAEwB,IAAI,IAAKA,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACvC,IAAI,CAAClD,YAAY,CAAC4B,IAAI,CACpB,IAAI,CAAC9B,UAAU,CAAC2B,MAAM,CAAEC,MAAM,IAAI;QAChC,MAAMyB,mBAAmB,GAAGzB,MAAM,CAACC,UAAU,CAACc,WAAW,EAAE;QAC3D,OAAOM,WAAW,CAACK,KAAK,CAAEH,IAAI,IAC5BE,mBAAmB,CAACT,QAAQ,CAACO,IAAI,CAAC,CACnC;MACH,CAAC,CAAC,CACH;;EAEL;EAEAI,MAAMA,CAAA;IACJ,MAAMC,SAAS,GAAG,IAAI,CAACpE,MAAM,CAACqE,IAAI,CAACzG,eAAe,EAAE;MAClD0G,SAAS,EAAE,KAAK;MAChBvC,IAAI,EAAE;QACJb,OAAO,EAAE,kCAAkC;QAC3CtB,KAAK,EAAE;;KAEV,CAAC;IAEFwE,SAAS,CAACG,WAAW,EAAE,CAACzC,SAAS,CAAE0C,MAAM,IAAI;MAC3C,IAAIA,MAAM,EAAE;QACVC,YAAY,CAACC,KAAK,EAAE;QACpBC,cAAc,CAACD,KAAK,EAAE;QACtBE,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QACxB,IAAI,CAAC7E,MAAM,CAACyD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;IAErC,CAAC,CAAC;EACJ;EAEAqB,gBAAgBA,CAAC5B,KAAK;IACpB,IAAI,CAAChD,UAAU,CAACyB,aAAa,CAACuB,KAAK,CAAC;EACtC;;;uBAvJWtD,yBAAyB,EAAAjB,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA1G,EAAA,CAAAoG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA5G,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAApG,EAAA,CAAA6G,iBAAA,GAAA7G,EAAA,CAAAoG,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzB9F,yBAAyB;MAAA+F,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAezBzH,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;UCtEvBM,EAAA,CAAAO,cAAA,kBAAa;UAGPP,EAAA,CAAAqH,UAAA,IAAAC,wCAAA,iBAA6E;UAC/EtH,EAAA,CAAAS,YAAA,EAAM;UAGNT,EAAA,CAAAO,cAAA,aAAsB;UACpBP,EAAA,CAAAqH,UAAA,IAAAE,iDAAA,0BAKe;UACjBvH,EAAA,CAAAS,YAAA,EAAM;UAGRT,EAAA,CAAAC,SAAA,cAAoC;UAEpCD,EAAA,CAAAO,cAAA,WAAuB;UAAAP,EAAA,CAAAQ,MAAA,GAAoB;UAAAR,EAAA,CAAAO,cAAA,cAAuB;UAAAP,EAAA,CAAAQ,MAAA,YAAI;UAAAR,EAAA,CAAAS,YAAA,EAAO;UAC7ET,EAAA,CAAAO,cAAA,cAAuB;UAETP,EAAA,CAAAQ,MAAA,sBAAc;UAAAR,EAAA,CAAAS,YAAA,EAAW;UACnCT,EAAA,CAAAO,cAAA,eAA0B;UACAP,EAAA,CAAAQ,MAAA,IAAgB;UAAAR,EAAA,CAAAS,YAAA,EAAO;UAC/CT,EAAA,CAAAO,cAAA,gBAAwB;UAAAP,EAAA,CAAAQ,MAAA,IAAc;UAAAR,EAAA,CAAAS,YAAA,EAAO;UAKnDT,EAAA,CAAAO,cAAA,wBAAmD;UAC3BP,EAAA,CAAAwH,UAAA,mBAAAC,4DAAA;YAAA,OAASL,GAAA,CAAAvC,OAAA,EAAS;UAAA,EAAC;UACvC7E,EAAA,CAAAC,SAAA,aAAgC;UAACD,EAAA,CAAAQ,MAAA,wBACnC;UAAAR,EAAA,CAAAS,YAAA,EAAS;UACTT,EAAA,CAAAO,cAAA,kBAAyC;UAAnBP,EAAA,CAAAwH,UAAA,mBAAAE,4DAAA;YAAA,OAASN,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UACtCvF,EAAA,CAAAC,SAAA,aAA8C;UAACD,EAAA,CAAAQ,MAAA,uBACjD;UAAAR,EAAA,CAAAS,YAAA,EAAS;;;;UAjCDT,EAAA,CAAAW,SAAA,GAAa;UAAbX,EAAA,CAAAE,UAAA,SAAAkH,GAAA,CAAAhH,OAAA,CAAa;UAKYJ,EAAA,CAAAW,SAAA,GAAY;UAAZX,EAAA,CAAAE,UAAA,YAAAkH,GAAA,CAAAzF,SAAA,CAAY;UAWxB3B,EAAA,CAAAW,SAAA,GAAoB;UAApBX,EAAA,CAAA2H,kBAAA,KAAAP,GAAA,CAAAhE,aAAA,MAAoB;UAEtBpD,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAE,UAAA,sBAAA0H,GAAA,CAAgC;UAGvB5H,EAAA,CAAAW,SAAA,GAAgB;UAAhBX,EAAA,CAAAc,iBAAA,CAAAsG,GAAA,CAAAzE,IAAA,kBAAAyE,GAAA,CAAAzE,IAAA,CAAAkF,IAAA,CAAgB;UAChB7H,EAAA,CAAAW,SAAA,GAAc;UAAdX,EAAA,CAAAc,iBAAA,CAAAsG,GAAA,CAAAhF,QAAA,CAAc;UAMApC,EAAA,CAAAW,SAAA,GAA8B;UAA9BX,EAAA,CAAAE,UAAA,cAAAkH,GAAA,CAAA9D,gBAAA,CAA8B;;;qBDMxEhE,WAAW,EACXC,mBAAmB,EACnBR,eAAe,EACfJ,YAAY,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpJ,aAAa,EAAAqJ,EAAA,CAAAC,OAAA,EACbrJ,eAAe,EAAAsJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfjJ,kBAAkB,EAClBN,gBAAgB,EAAAwJ,EAAA,CAAAC,UAAA,EAChBtJ,aAAa,EAAAuJ,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACbxJ,eAAe,EACfK,gBAAgB,EAChBC,aAAa,EACbP,YAAY,EAAAuH,EAAA,CAAAmC,UAAA,EAAAnC,EAAA,CAAAoC,gBAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH9H,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}