{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { FormsModule } from '@angular/forms';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';\nimport { HttpTableComponent } from 'src/app/components/http-table/http-table.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { BottomSheetComponent } from '../../bottom-sheet/bottom-sheet.component';\nimport { first } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/inventory.service\";\nimport * as i2 from \"src/app/services/share-data.service\";\nimport * as i3 from \"@angular/material/bottom-sheet\";\nimport * as i4 from \"src/app/services/auth.service\";\nimport * as i5 from \"src/app/services/master-data.service\";\nimport * as i6 from \"src/app/services/notification.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@angular/material/dialog\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/tabs\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"ngx-skeleton-loader\";\nimport * as i14 from \"@angular/material/card\";\nconst _c0 = [\"openResetDialog\"];\nfunction ParentComponent_mat_tab_12_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", tab_r3.label, \" \");\n  }\n}\nfunction ParentComponent_mat_tab_12_div_2_app_http_table_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-http-table\", 11);\n  }\n  if (rf & 2) {\n    const tab_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"page\", tab_r3.page)(\"data\", ctx_r8.baseData[tab_r3.page]);\n  }\n}\nfunction ParentComponent_mat_tab_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ParentComponent_mat_tab_12_div_2_app_http_table_1_Template, 1, 2, \"app-http-table\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.selectedTabIndex == tab_r3.index);\n  }\n}\nconst _c1 = function () {\n  return {\n    \"border-radius\": \"5px\",\n    height: \"30px\"\n  };\n};\nfunction ParentComponent_mat_tab_12_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"ngx-skeleton-loader\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"theme\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction ParentComponent_mat_tab_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-tab\");\n    i0.ɵɵtemplate(1, ParentComponent_mat_tab_12_ng_template_1_Template, 1, 1, \"ng-template\", 7);\n    i0.ɵɵtemplate(2, ParentComponent_mat_tab_12_div_2_Template, 2, 1, \"div\", 8);\n    i0.ɵɵtemplate(3, ParentComponent_mat_tab_12_div_3_Template, 2, 2, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDataReady);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDataReady);\n  }\n}\nfunction ParentComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"mat-icon\", 15);\n    i0.ɵɵlistener(\"click\", function ParentComponent_ng_template_13_Template_mat_icon_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.closeResetDialog());\n    });\n    i0.ɵɵtext(2, \"close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"div\", 17)(5, \"span\");\n    i0.ɵɵtext(6, \"Reset UI\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18);\n    i0.ɵɵtext(8, \" Would you like to reset the session? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ParentComponent_ng_template_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.resetUI());\n    });\n    i0.ɵɵtext(11, \" Yes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ParentComponent_ng_template_13_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.closeResetDialog());\n    });\n    i0.ɵɵtext(13, \" No\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nclass ParentComponent {\n  constructor(api, sharedData, cd, _bottomSheet, auth, masterDataService, notify, router, dialog) {\n    this.api = api;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this._bottomSheet = _bottomSheet;\n    this.auth = auth;\n    this.masterDataService = masterDataService;\n    this.notify = notify;\n    this.router = router;\n    this.dialog = dialog;\n    this.selectedTabIndex = -1;\n    this.selectedTabPage = '';\n    this.tabs = [{\n      label: 'User',\n      page: 'users',\n      index: 0,\n      icon: \"inventory\"\n    }, {\n      label: 'Role',\n      page: 'Roles',\n      index: 1,\n      icon: \"store\"\n    }, {\n      label: 'Branch',\n      page: 'branches',\n      index: 2,\n      icon: \"store\"\n    }];\n    this.isDataReady = false;\n    this.user = this.auth.getCurrentUser();\n  }\n  ngOnInit() {\n    this.getBaseData();\n  }\n  getBaseData() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['userEmail'] = this.user.email;\n    obj['type'] = 'user';\n    this.masterDataService.route$.pipe(first()).subscribe(tab => {\n      if (tab && tab === 'user') {\n        if ('user' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'user';\n        }\n        this.selectedTabIndex = 0;\n      } else if (tab && tab === 'Roles') {\n        if ('Roles' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'Roles';\n        }\n        this.selectedTabIndex = 1;\n      } else if (tab && tab === 'branches') {\n        if ('branches' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'branches';\n        }\n        this.selectedTabIndex = 2;\n      } else {\n        this.selectedTabIndex = 0;\n      }\n      this.api.getPresentData(obj).pipe(first()).subscribe({\n        next: res => {\n          if (res['success'] && (res['data'].length > 0 || Object.keys(res['data']).length > 0)) {\n            this.entireData = res;\n            if (obj['specific'] == 'user') {\n              let previousBaseData = this.sharedData.getBaseData().value['user'];\n              let currentBaseData = res['data'][0] ?? res['data']['user'];\n              currentBaseData.forEach(item => {\n                const exist = previousBaseData.findIndex(el => el.email == item['email']);\n                if (exist !== -1) {\n                  previousBaseData[exist] = item;\n                } else {\n                  previousBaseData.push(item);\n                }\n              });\n              this.baseData['user'] = previousBaseData;\n            } else if (obj['specific'] == 'Roles') {\n              this.baseData['Roles'] = res['data'][0] ?? res['data']['Roles'];\n            } else if (obj['specific'] == 'branches') {\n              this.baseData['branches'] = res['data'][0] ?? res['data']['branches'];\n            } else {\n              this.baseData = res['data'][0] ?? res['data'];\n            }\n            this.cd.detectChanges();\n            this.sharedData.setUserData(res['data']);\n            const uniqueRoles = [...new Set(this.baseData.Roles.map(item => item.role))];\n            this.sharedData.setRoles(uniqueRoles);\n            this.sharedData.setItemNames(obj, this.baseData);\n            this.isDataReady = true;\n            this.cd.detectChanges();\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    });\n  }\n  tabClick(tab) {\n    this.selectedTabIndex = tab.index;\n    this.selectedTabPage = this.tabs[tab.index].page;\n  }\n  openBottomSheet() {\n    this._bottomSheet.open(BottomSheetComponent);\n  }\n  resetData() {\n    this.dialogRef = this.dialog.open(this.openResetDialog, {\n      width: '500px'\n    });\n    this.dialogRef.afterClosed().subscribe(result => {});\n  }\n  resetUI() {\n    let obj = {};\n    obj['tenantId'] = this.user.tenantId;\n    obj['type'] = 'user';\n    obj['sessionId'] = this.entireData.sessionId;\n    this.api.resetSession(obj).pipe(first()).subscribe({\n      next: res => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('The session was successfully reset.');\n          this.closeResetDialog();\n          this.masterDataService.setNavigation('');\n          this.router.navigate(['/dashboard/home']);\n          setTimeout(() => {\n            this.router.navigate(['/dashboard/user']);\n          }, 1000);\n        }\n      },\n      error: err => {\n        console.log(err);\n      }\n    });\n  }\n  closeResetDialog() {\n    if (this.dialogRef) {\n      this.dialogRef.close();\n    }\n  }\n  static {\n    this.ɵfac = function ParentComponent_Factory(t) {\n      return new (t || ParentComponent)(i0.ɵɵdirectiveInject(i1.InventoryService), i0.ɵɵdirectiveInject(i2.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.MatBottomSheet), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.MasterDataService), i0.ɵɵdirectiveInject(i6.NotificationService), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ParentComponent,\n      selectors: [[\"app-parent\"]],\n      viewQuery: function ParentComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.openResetDialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[3, \"backgroundSrc\"], [1, \"headingBtns\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"reset\", 1, \"reset\", \"my-1\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"sync option\", 1, \"sync\", \"my-1\", 3, \"disabled\", \"click\"], [3, \"selectedIndex\", \"selectedIndexChange\", \"selectedTabChange\"], [4, \"ngFor\", \"ngForOf\"], [\"openResetDialog\", \"\"], [\"mat-tab-label\", \"\"], [4, \"ngIf\"], [\"class\", \"my-3\", 4, \"ngIf\"], [3, \"page\", \"data\", 4, \"ngIf\"], [3, \"page\", \"data\"], [1, \"my-3\"], [\"count\", \"10\", \"animation\", \"progress-dark\", 3, \"theme\"], [1, \"closeBtn\"], [\"matTooltip\", \"close\", 1, \"closeBtnIcon\", 3, \"click\"], [1, \"registration-form\", \"m-1\", \"py-2\", \"px-3\"], [1, \"text-center\", \"my-2\", \"p-2\", \"bottomTitles\"], [1, \"m-3\", \"infoText\"], [1, \"text-end\", \"m-2\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Update\", 1, \"m-1\", 3, \"click\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"close\", 1, \"m-1\", 3, \"click\"]],\n      template: function ParentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-background-image-card\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ParentComponent_Template_button_click_2_listener() {\n            return ctx.resetData();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \" clear_all\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \"Reset\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ParentComponent_Template_button_click_6_listener() {\n            return ctx.openBottomSheet();\n          });\n          i0.ɵɵelementStart(7, \"mat-icon\");\n          i0.ɵɵtext(8, \"sync\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \"Sync Options \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"mat-card\")(11, \"mat-tab-group\", 4);\n          i0.ɵɵlistener(\"selectedIndexChange\", function ParentComponent_Template_mat_tab_group_selectedIndexChange_11_listener($event) {\n            return ctx.selectedTabIndex = $event;\n          })(\"selectedTabChange\", function ParentComponent_Template_mat_tab_group_selectedTabChange_11_listener($event) {\n            return ctx.tabClick($event);\n          });\n          i0.ɵɵtemplate(12, ParentComponent_mat_tab_12_Template, 4, 2, \"mat-tab\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, ParentComponent_ng_template_13_Template, 14, 0, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isDataReady || ctx.entireData.sessionId === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.isDataReady);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTabIndex);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n        }\n      },\n      dependencies: [CommonModule, i9.NgForOf, i9.NgIf, MatFormFieldModule, MatInputModule, FormsModule, MatTabsModule, i10.MatTabLabel, i10.MatTab, i10.MatTabGroup, BackgroundImageCardComponent, HttpTableComponent, MatButtonModule, i11.MatButton, MatIconModule, i12.MatIcon, NgxSkeletonLoaderModule, i13.NgxSkeletonLoaderComponent, MatCardModule, i14.MatCard, MatBottomSheetModule],\n      styles: [\"\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdXNlci1tYW5hZ2VtZW50L3BhcmVudC9wYXJlbnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsdUNBQUEiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBUYWIgc3R5bGluZyBtb3ZlZCB0byBnbG9iYWwgc3R5bGVzICovIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ParentComponent };", "map": {"version": 3, "names": ["CommonModule", "MatFormFieldModule", "MatInputModule", "FormsModule", "MatTabsModule", "BackgroundImageCardComponent", "HttpTableComponent", "MatButtonModule", "MatIconModule", "NgxSkeletonLoaderModule", "MatCardModule", "MatBottomSheetModule", "BottomSheetComponent", "first", "i0", "ɵɵtext", "ɵɵtextInterpolate1", "tab_r3", "label", "ɵɵelement", "ɵɵproperty", "page", "ctx_r8", "baseData", "ɵɵelementStart", "ɵɵtemplate", "ParentComponent_mat_tab_12_div_2_app_http_table_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ctx_r5", "selectedTabIndex", "index", "ɵɵpureFunction0", "_c1", "ParentComponent_mat_tab_12_ng_template_1_Template", "ParentComponent_mat_tab_12_div_2_Template", "ParentComponent_mat_tab_12_div_3_Template", "ctx_r0", "isDataReady", "ɵɵlistener", "ParentComponent_ng_template_13_Template_mat_icon_click_1_listener", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "closeResetDialog", "ParentComponent_ng_template_13_Template_button_click_10_listener", "ctx_r13", "resetUI", "ParentComponent_ng_template_13_Template_button_click_12_listener", "ctx_r14", "ParentComponent", "constructor", "api", "sharedData", "cd", "_bottomSheet", "auth", "masterDataService", "notify", "router", "dialog", "selectedTabPage", "tabs", "icon", "user", "getCurrentUser", "ngOnInit", "getBaseData", "value", "obj", "tenantId", "email", "route$", "pipe", "subscribe", "tab", "getPresentData", "next", "res", "length", "Object", "keys", "entireData", "previousBaseData", "currentBaseData", "for<PERSON>ach", "item", "exist", "findIndex", "el", "push", "detectChanges", "setUserData", "uniqueRoles", "Set", "Roles", "map", "role", "setRoles", "setItemNames", "error", "err", "console", "log", "tabClick", "openBottomSheet", "open", "resetData", "dialogRef", "openResetDialog", "width", "afterClosed", "result", "sessionId", "resetSession", "snackBarShowSuccess", "setNavigation", "navigate", "setTimeout", "close", "ɵɵdirectiveInject", "i1", "InventoryService", "i2", "ShareDataService", "ChangeDetectorRef", "i3", "MatBottomSheet", "i4", "AuthService", "i5", "MasterDataService", "i6", "NotificationService", "i7", "Router", "i8", "MatDialog", "selectors", "viewQuery", "ParentComponent_Query", "rf", "ctx", "ParentComponent_Template_button_click_2_listener", "ParentComponent_Template_button_click_6_listener", "ParentComponent_Template_mat_tab_group_selectedIndexChange_11_listener", "$event", "ParentComponent_Template_mat_tab_group_selectedTabChange_11_listener", "ParentComponent_mat_tab_12_Template", "ParentComponent_ng_template_13_Template", "ɵɵtemplateRefExtractor", "i9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i10", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "i11", "MatButton", "i12", "MatIcon", "i13", "NgxSkeletonLoaderComponent", "i14", "MatCard", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/user-management/parent/parent.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/user-management/parent/parent.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, TemplateRef, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { FormsModule } from '@angular/forms';\nimport { GradientCardComponent } from 'src/app/components/shared/gradient-card/gradient-card.component';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';\nimport { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';\nimport { HttpTableComponent } from 'src/app/components/http-table/http-table.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { BottomSheetComponent } from '../../bottom-sheet/bottom-sheet.component';\nimport { first } from 'rxjs';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Router } from '@angular/router';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\n\n@Component({\n  selector: 'app-parent',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatFormFieldModule,\n    MatInputModule,\n    FormsModule,\n    GradientCardComponent,\n    MatTabsModule,\n    BackgroundImageCardComponent,\n    BackgroundImageCardHeaderComponent,\n    HttpTableComponent,\n    MatButtonModule,\n    MatIconModule,\n    NgxSkeletonLoaderModule,\n    MatIconModule,\n    MatCardModule,\n    MatBottomSheetModule\n  ],\n  templateUrl: './parent.component.html',\n  styleUrls: ['./parent.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ParentComponent {\n  public selectedTabIndex: number = -1;\n  public selectedTabPage: string = '';\n  public baseData: any;\n  public tabs: { label: string; page: string; index: number; icon: string }[] = [\n    { label: 'User', page: 'users', index: 0, icon: \"inventory\" },\n    { label: 'Role', page: 'Roles', index: 1, icon: \"store\" },\n    { label: 'Branch', page: 'branches', index: 2, icon: \"store\" },\n  ];\n  user: any;\n  isDataReady: boolean = false;\n  entireData: any;\n  @ViewChild('openResetDialog') openResetDialog: TemplateRef<any>;\n  dialogRef: MatDialogRef<any>;\n  constructor(\n    private api: InventoryService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private _bottomSheet: MatBottomSheet,\n    private auth: AuthService,\n    private masterDataService: MasterDataService,\n    private notify: NotificationService,\n    private router: Router,\n    public dialog: MatDialog,\n  ) {\n    this.user = this.auth.getCurrentUser();\n  }\n\n  ngOnInit(): void {\n    this.getBaseData();\n\n  }\n\n  getBaseData() {\n    this.baseData = this.sharedData.getBaseData().value;\n    let obj = {}\n    obj['tenantId'] = this.user.tenantId\n    obj['userEmail'] = this.user.email\n    obj['type'] = 'user'\n    this.masterDataService.route$.pipe(first()).subscribe(tab => {\n      if (tab && tab === 'user') {\n        if ('user' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'user'\n        }\n        this.selectedTabIndex = 0;\n      } else if (tab && tab === 'Roles') {\n        if ('Roles' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'Roles'\n        }\n        this.selectedTabIndex = 1;\n      } else if (tab && tab === 'branches') {\n        if ('branches' in this.sharedData.getBaseData().value) {\n          obj['specific'] = 'branches'\n        }\n        this.selectedTabIndex = 2;\n      } else {\n        this.selectedTabIndex = 0;\n      }\n      this.api.getPresentData(obj).pipe(first()).subscribe({\n        next: (res) => {\n          if (res['success'] && (res['data'].length > 0 || Object.keys(res['data']).length > 0)) {\n            this.entireData = res\n            if (obj['specific'] == 'user') {\n              let previousBaseData = this.sharedData.getBaseData().value['user'];\n              let currentBaseData = res['data'][0] ?? res['data']['user'];\n\n              currentBaseData.forEach(item => {\n                const exist = previousBaseData.findIndex(el => el.email == item['email']);\n                if (exist !== -1) {\n                  previousBaseData[exist] = item;\n                } else {\n                  previousBaseData.push(item);\n                }\n              });\n              this.baseData['user'] = previousBaseData;\n            } else if (obj['specific'] == 'Roles') {\n              this.baseData['Roles'] = res['data'][0] ?? res['data']['Roles'];\n\n            } else if (obj['specific'] == 'branches') {\n              this.baseData['branches'] = res['data'][0] ?? res['data']['branches'];\n\n            } else {\n              this.baseData = res['data'][0] ?? res['data'];\n            }\n            this.cd.detectChanges();\n            this.sharedData.setUserData(res['data']);\n            const uniqueRoles = [...new Set(this.baseData.Roles.map(item => item.role))];\n            this.sharedData.setRoles(uniqueRoles);\n            this.sharedData.setItemNames(obj, this.baseData)\n            this.isDataReady = true;\n            this.cd.detectChanges();\n            \n          }\n        },\n        error: (err) => { console.log(err) }\n      })\n    });\n  }\n\n\n  tabClick(tab: any) {\n    this.selectedTabIndex = tab.index;\n    this.selectedTabPage = this.tabs[tab.index].page;\n  }\n\n  openBottomSheet(): void {\n    this._bottomSheet.open(BottomSheetComponent);\n  }\n\n  resetData(){\n    this.dialogRef = this.dialog.open(this.openResetDialog, {\n      width: '500px', \n    });\n\n    this.dialogRef.afterClosed().subscribe(result => {\n    });\n  }\n\n  resetUI(){\n    let obj = {}\n    obj['tenantId'] = this.user.tenantId\n    obj['type'] = 'user'\n    obj['sessionId'] = this.entireData.sessionId\n    this.api.resetSession(obj).pipe(first()).subscribe({\n      next: (res) => {\n        if (res['success']) {\n          this.notify.snackBarShowSuccess('The session was successfully reset.');\n          this.closeResetDialog();\n          this.masterDataService.setNavigation('');\n          this.router.navigate(['/dashboard/home']);\n          setTimeout(() => {\n            this.router.navigate(['/dashboard/user']);\n          }, 1000);\n        }\n      },\n      error: (err) => { console.log(err) }\n    })\n  }\n\n  closeResetDialog(){\n    if (this.dialogRef) {\n      this.dialogRef.close();\n    }\n  }\n\n}\n", "<app-background-image-card [backgroundSrc]=\"\">\n  <div class=\"headingBtns\">\n    <button (click)=\"resetData()\" mat-raised-button color=\"warn\" class=\"reset my-1\" matTooltip=\"reset\" [disabled]=\"!isDataReady || this.entireData.sessionId === 0\">\n      <mat-icon> clear_all</mat-icon>Reset</button>\n      <button (click)=\"openBottomSheet()\" mat-raised-button color=\"warn\" class=\"sync my-1\" matTooltip=\"sync option\" [disabled]=\"!isDataReady\">\n        <mat-icon>sync</mat-icon>Sync Options\n      </button>\n  </div>\n</app-background-image-card>\n\n<mat-card>\n  <mat-tab-group [(selectedIndex)]=\"selectedTabIndex\" (selectedTabChange)=\"tabClick($event)\">\n    <mat-tab *ngFor=\"let tab of tabs\">\n      <ng-template mat-tab-label>\n        {{tab.label}}\n      </ng-template>\n      <div *ngIf=\"isDataReady\">\n        <app-http-table [page]=\"tab.page\" [data]=\"baseData[tab.page]\" *ngIf=\"selectedTabIndex == tab.index\"></app-http-table>\n      </div>\n      <div *ngIf=\"!isDataReady\" class=\"my-3\">\n        <ngx-skeleton-loader count=\"10\" animation=\"progress-dark\" [theme]=\"{ \n              'border-radius': '5px',\n              height: '30px'\n            }\"></ngx-skeleton-loader>\n      </div>\n    </mat-tab>\n  </mat-tab-group>\n</mat-card>\n\n<ng-template #openResetDialog>\n  <div class=\"closeBtn\">\n    <mat-icon class=\"closeBtnIcon\" matTooltip=\"close\" (click)=\"closeResetDialog()\">close</mat-icon>\n  </div>\n  <div class=\"registration-form m-1 py-2 px-3\">\n    <div class=\"text-center my-2 p-2 bottomTitles\">\n      <span>Reset UI</span>\n    </div>\n    <div class=\"m-3 infoText\">\n      Would you like to reset the session?\n    </div>\n    <div class=\"text-end m-2\">\n      <button (click)=\"resetUI()\" mat-raised-button color=\"accent\" matTooltip=\"Update\" class=\"m-1\">\n        Yes</button>\n        <button (click)=\"closeResetDialog()\" mat-raised-button matTooltip=\"close\" class=\"m-1\">\n        No</button>\n    </div>\n  </div>\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,4BAA4B,QAAQ,0EAA0E;AAEvH,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAyBC,oBAAoB,QAAQ,gCAAgC;AAKrF,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICNpBC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAEEJ,EAAA,CAAAK,SAAA,yBAAqH;;;;;IAArGL,EAAA,CAAAM,UAAA,SAAAH,MAAA,CAAAI,IAAA,CAAiB,SAAAC,MAAA,CAAAC,QAAA,CAAAN,MAAA,CAAAI,IAAA;;;;;IADnCP,EAAA,CAAAU,cAAA,UAAyB;IACvBV,EAAA,CAAAW,UAAA,IAAAC,0DAAA,6BAAqH;IACvHZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAD2Db,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAM,UAAA,SAAAS,MAAA,CAAAC,gBAAA,IAAAb,MAAA,CAAAc,KAAA,CAAmC;;;;;;;;;;;IAEpGjB,EAAA,CAAAU,cAAA,cAAuC;IACrCV,EAAA,CAAAK,SAAA,8BAG6B;IAC/BL,EAAA,CAAAa,YAAA,EAAM;;;IAJsDb,EAAA,CAAAc,SAAA,GAGpD;IAHoDd,EAAA,CAAAM,UAAA,UAAAN,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAGpD;;;;;IAXVnB,EAAA,CAAAU,cAAA,cAAkC;IAChCV,EAAA,CAAAW,UAAA,IAAAS,iDAAA,yBAEc;IACdpB,EAAA,CAAAW,UAAA,IAAAU,yCAAA,iBAEM;IACNrB,EAAA,CAAAW,UAAA,IAAAW,yCAAA,iBAKM;IACRtB,EAAA,CAAAa,YAAA,EAAU;;;;IATFb,EAAA,CAAAc,SAAA,GAAiB;IAAjBd,EAAA,CAAAM,UAAA,SAAAiB,MAAA,CAAAC,WAAA,CAAiB;IAGjBxB,EAAA,CAAAc,SAAA,GAAkB;IAAlBd,EAAA,CAAAM,UAAA,UAAAiB,MAAA,CAAAC,WAAA,CAAkB;;;;;;IAW5BxB,EAAA,CAAAU,cAAA,cAAsB;IAC8BV,EAAA,CAAAyB,UAAA,mBAAAC,kEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAAChC,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAa,YAAA,EAAW;IAEjGb,EAAA,CAAAU,cAAA,cAA6C;IAEnCV,EAAA,CAAAC,MAAA,eAAQ;IAAAD,EAAA,CAAAa,YAAA,EAAO;IAEvBb,EAAA,CAAAU,cAAA,cAA0B;IACxBV,EAAA,CAAAC,MAAA,6CACF;IAAAD,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAU,cAAA,cAA0B;IAChBV,EAAA,CAAAyB,UAAA,mBAAAQ,iEAAA;MAAAjC,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAlC,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAG,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IACzBnC,EAAA,CAAAC,MAAA,YAAG;IAAAD,EAAA,CAAAa,YAAA,EAAS;IACZb,EAAA,CAAAU,cAAA,kBAAsF;IAA9EV,EAAA,CAAAyB,UAAA,mBAAAW,iEAAA;MAAApC,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAArC,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAM,OAAA,CAAAL,gBAAA,EAAkB;IAAA,EAAC;IACpChC,EAAA,CAAAC,MAAA,WAAE;IAAAD,EAAA,CAAAa,YAAA,EAAS;;;ADnBnB,MAwBayB,eAAe;EAc1BC,YACUC,GAAqB,EACrBC,UAA4B,EAC5BC,EAAqB,EACrBC,YAA4B,EAC5BC,IAAiB,EACjBC,iBAAoC,EACpCC,MAA2B,EAC3BC,MAAc,EACfC,MAAiB;IARhB,KAAAR,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,MAAM,GAANA,MAAM;IAtBR,KAAAhC,gBAAgB,GAAW,CAAC,CAAC;IAC7B,KAAAiC,eAAe,GAAW,EAAE;IAE5B,KAAAC,IAAI,GAAmE,CAC5E;MAAE9C,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE,OAAO;MAAEU,KAAK,EAAE,CAAC;MAAEkC,IAAI,EAAE;IAAW,CAAE,EAC7D;MAAE/C,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE,OAAO;MAAEU,KAAK,EAAE,CAAC;MAAEkC,IAAI,EAAE;IAAO,CAAE,EACzD;MAAE/C,KAAK,EAAE,QAAQ;MAAEG,IAAI,EAAE,UAAU;MAAEU,KAAK,EAAE,CAAC;MAAEkC,IAAI,EAAE;IAAO,CAAE,CAC/D;IAED,KAAA3B,WAAW,GAAY,KAAK;IAe1B,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAACR,IAAI,CAACS,cAAc,EAAE;EACxC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EAEpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC9C,QAAQ,GAAG,IAAI,CAACgC,UAAU,CAACc,WAAW,EAAE,CAACC,KAAK;IACnD,IAAIC,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACL,IAAI,CAACM,QAAQ;IACpCD,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACL,IAAI,CAACO,KAAK;IAClCF,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM;IACpB,IAAI,CAACZ,iBAAiB,CAACe,MAAM,CAACC,IAAI,CAAC9D,KAAK,EAAE,CAAC,CAAC+D,SAAS,CAACC,GAAG,IAAG;MAC1D,IAAIA,GAAG,IAAIA,GAAG,KAAK,MAAM,EAAE;QACzB,IAAI,MAAM,IAAI,IAAI,CAACtB,UAAU,CAACc,WAAW,EAAE,CAACC,KAAK,EAAE;UACjDC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM;;QAE1B,IAAI,CAACzC,gBAAgB,GAAG,CAAC;OAC1B,MAAM,IAAI+C,GAAG,IAAIA,GAAG,KAAK,OAAO,EAAE;QACjC,IAAI,OAAO,IAAI,IAAI,CAACtB,UAAU,CAACc,WAAW,EAAE,CAACC,KAAK,EAAE;UAClDC,GAAG,CAAC,UAAU,CAAC,GAAG,OAAO;;QAE3B,IAAI,CAACzC,gBAAgB,GAAG,CAAC;OAC1B,MAAM,IAAI+C,GAAG,IAAIA,GAAG,KAAK,UAAU,EAAE;QACpC,IAAI,UAAU,IAAI,IAAI,CAACtB,UAAU,CAACc,WAAW,EAAE,CAACC,KAAK,EAAE;UACrDC,GAAG,CAAC,UAAU,CAAC,GAAG,UAAU;;QAE9B,IAAI,CAACzC,gBAAgB,GAAG,CAAC;OAC1B,MAAM;QACL,IAAI,CAACA,gBAAgB,GAAG,CAAC;;MAE3B,IAAI,CAACwB,GAAG,CAACwB,cAAc,CAACP,GAAG,CAAC,CAACI,IAAI,CAAC9D,KAAK,EAAE,CAAC,CAAC+D,SAAS,CAAC;QACnDG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,KAAKA,GAAG,CAAC,MAAM,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;YACrF,IAAI,CAACG,UAAU,GAAGJ,GAAG;YACrB,IAAIT,GAAG,CAAC,UAAU,CAAC,IAAI,MAAM,EAAE;cAC7B,IAAIc,gBAAgB,GAAG,IAAI,CAAC9B,UAAU,CAACc,WAAW,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;cAClE,IAAIgB,eAAe,GAAGN,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;cAE3DM,eAAe,CAACC,OAAO,CAACC,IAAI,IAAG;gBAC7B,MAAMC,KAAK,GAAGJ,gBAAgB,CAACK,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAClB,KAAK,IAAIe,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzE,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChBJ,gBAAgB,CAACI,KAAK,CAAC,GAAGD,IAAI;iBAC/B,MAAM;kBACLH,gBAAgB,CAACO,IAAI,CAACJ,IAAI,CAAC;;cAE/B,CAAC,CAAC;cACF,IAAI,CAACjE,QAAQ,CAAC,MAAM,CAAC,GAAG8D,gBAAgB;aACzC,MAAM,IAAId,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,EAAE;cACrC,IAAI,CAAChD,QAAQ,CAAC,OAAO,CAAC,GAAGyD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;aAEhE,MAAM,IAAIT,GAAG,CAAC,UAAU,CAAC,IAAI,UAAU,EAAE;cACxC,IAAI,CAAChD,QAAQ,CAAC,UAAU,CAAC,GAAGyD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC;aAEtE,MAAM;cACL,IAAI,CAACzD,QAAQ,GAAGyD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,MAAM,CAAC;;YAE/C,IAAI,CAACxB,EAAE,CAACqC,aAAa,EAAE;YACvB,IAAI,CAACtC,UAAU,CAACuC,WAAW,CAACd,GAAG,CAAC,MAAM,CAAC,CAAC;YACxC,MAAMe,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACzE,QAAQ,CAAC0E,KAAK,CAACC,GAAG,CAACV,IAAI,IAAIA,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC5C,UAAU,CAAC6C,QAAQ,CAACL,WAAW,CAAC;YACrC,IAAI,CAACxC,UAAU,CAAC8C,YAAY,CAAC9B,GAAG,EAAE,IAAI,CAAChD,QAAQ,CAAC;YAChD,IAAI,CAACe,WAAW,GAAG,IAAI;YACvB,IAAI,CAACkB,EAAE,CAACqC,aAAa,EAAE;;QAG3B,CAAC;QACDS,KAAK,EAAGC,GAAG,IAAI;UAAGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;QAAC;OACpC,CAAC;IACJ,CAAC,CAAC;EACJ;EAGAG,QAAQA,CAAC7B,GAAQ;IACf,IAAI,CAAC/C,gBAAgB,GAAG+C,GAAG,CAAC9C,KAAK;IACjC,IAAI,CAACgC,eAAe,GAAG,IAAI,CAACC,IAAI,CAACa,GAAG,CAAC9C,KAAK,CAAC,CAACV,IAAI;EAClD;EAEAsF,eAAeA,CAAA;IACb,IAAI,CAAClD,YAAY,CAACmD,IAAI,CAAChG,oBAAoB,CAAC;EAC9C;EAEAiG,SAASA,CAAA;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAAChD,MAAM,CAAC8C,IAAI,CAAC,IAAI,CAACG,eAAe,EAAE;MACtDC,KAAK,EAAE;KACR,CAAC;IAEF,IAAI,CAACF,SAAS,CAACG,WAAW,EAAE,CAACrC,SAAS,CAACsC,MAAM,IAAG,CAChD,CAAC,CAAC;EACJ;EAEAjE,OAAOA,CAAA;IACL,IAAIsB,GAAG,GAAG,EAAE;IACZA,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACL,IAAI,CAACM,QAAQ;IACpCD,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM;IACpBA,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACa,UAAU,CAAC+B,SAAS;IAC5C,IAAI,CAAC7D,GAAG,CAAC8D,YAAY,CAAC7C,GAAG,CAAC,CAACI,IAAI,CAAC9D,KAAK,EAAE,CAAC,CAAC+D,SAAS,CAAC;MACjDG,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAIA,GAAG,CAAC,SAAS,CAAC,EAAE;UAClB,IAAI,CAACpB,MAAM,CAACyD,mBAAmB,CAAC,qCAAqC,CAAC;UACtE,IAAI,CAACvE,gBAAgB,EAAE;UACvB,IAAI,CAACa,iBAAiB,CAAC2D,aAAa,CAAC,EAAE,CAAC;UACxC,IAAI,CAACzD,MAAM,CAAC0D,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;UACzCC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3D,MAAM,CAAC0D,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;UAC3C,CAAC,EAAE,IAAI,CAAC;;MAEZ,CAAC;MACDjB,KAAK,EAAGC,GAAG,IAAI;QAAGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAAC;KACpC,CAAC;EACJ;EAEAzD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACgE,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACW,KAAK,EAAE;;EAE1B;;;uBA/IWrE,eAAe,EAAAtC,EAAA,CAAA4G,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA9G,EAAA,CAAA4G,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAhH,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAAiH,iBAAA,GAAAjH,EAAA,CAAA4G,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAnH,EAAA,CAAA4G,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAArH,EAAA,CAAA4G,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAAvH,EAAA,CAAA4G,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAAzH,EAAA,CAAA4G,iBAAA,CAAAc,EAAA,CAAAC,MAAA,GAAA3H,EAAA,CAAA4G,iBAAA,CAAAgB,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAfvF,eAAe;MAAAwF,SAAA;MAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCjD5BjI,EAAA,CAAAU,cAAA,mCAA8C;UAElCV,EAAA,CAAAyB,UAAA,mBAAA0G,iDAAA;YAAA,OAASD,GAAA,CAAAnC,SAAA,EAAW;UAAA,EAAC;UAC3B/F,EAAA,CAAAU,cAAA,eAAU;UAACV,EAAA,CAAAC,MAAA,iBAAS;UAAAD,EAAA,CAAAa,YAAA,EAAW;UAAAb,EAAA,CAAAC,MAAA,YAAK;UAAAD,EAAA,CAAAa,YAAA,EAAS;UAC7Cb,EAAA,CAAAU,cAAA,gBAAwI;UAAhIV,EAAA,CAAAyB,UAAA,mBAAA2G,iDAAA;YAAA,OAASF,GAAA,CAAArC,eAAA,EAAiB;UAAA,EAAC;UACjC7F,EAAA,CAAAU,cAAA,eAAU;UAAAV,EAAA,CAAAC,MAAA,WAAI;UAAAD,EAAA,CAAAa,YAAA,EAAW;UAAAb,EAAA,CAAAC,MAAA,oBAC3B;UAAAD,EAAA,CAAAa,YAAA,EAAS;UAIfb,EAAA,CAAAU,cAAA,gBAAU;UACOV,EAAA,CAAAyB,UAAA,iCAAA4G,uEAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAlH,gBAAA,GAAAsH,MAAA;UAAA,EAAoC,+BAAAC,qEAAAD,MAAA;YAAA,OAAsBJ,GAAA,CAAAtC,QAAA,CAAA0C,MAAA,CAAgB;UAAA,EAAtC;UACjDtI,EAAA,CAAAW,UAAA,KAAA6H,mCAAA,qBAaU;UACZxI,EAAA,CAAAa,YAAA,EAAgB;UAGlBb,EAAA,CAAAW,UAAA,KAAA8H,uCAAA,iCAAAzI,EAAA,CAAA0I,sBAAA,CAkBc;;;UA7CyF1I,EAAA,CAAAc,SAAA,GAA4D;UAA5Dd,EAAA,CAAAM,UAAA,cAAA4H,GAAA,CAAA1G,WAAA,IAAA0G,GAAA,CAAA5D,UAAA,CAAA+B,SAAA,OAA4D;UAE/CrG,EAAA,CAAAc,SAAA,GAAyB;UAAzBd,EAAA,CAAAM,UAAA,cAAA4H,GAAA,CAAA1G,WAAA,CAAyB;UAO5HxB,EAAA,CAAAc,SAAA,GAAoC;UAApCd,EAAA,CAAAM,UAAA,kBAAA4H,GAAA,CAAAlH,gBAAA,CAAoC;UACxBhB,EAAA,CAAAc,SAAA,GAAO;UAAPd,EAAA,CAAAM,UAAA,YAAA4H,GAAA,CAAAhF,IAAA,CAAO;;;qBDiBhChE,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1J,kBAAkB,EAClBC,cAAc,EACdC,WAAW,EAEXC,aAAa,EAAAwJ,GAAA,CAAAC,WAAA,EAAAD,GAAA,CAAAE,MAAA,EAAAF,GAAA,CAAAG,WAAA,EACb1J,4BAA4B,EAE5BC,kBAAkB,EAClBC,eAAe,EAAAyJ,GAAA,CAAAC,SAAA,EACfzJ,aAAa,EAAA0J,GAAA,CAAAC,OAAA,EACb1J,uBAAuB,EAAA2J,GAAA,CAAAC,0BAAA,EAEvB3J,aAAa,EAAA4J,GAAA,CAAAC,OAAA,EACb5J,oBAAoB;MAAA6J,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMXrH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}