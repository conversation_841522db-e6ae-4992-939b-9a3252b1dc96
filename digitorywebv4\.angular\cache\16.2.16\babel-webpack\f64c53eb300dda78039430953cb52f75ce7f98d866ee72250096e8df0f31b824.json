{"ast": null, "code": "import { catchError, map, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/notification.service\";\nclass AuthInterceptor {\n  constructor(auth, router, utils) {\n    this.auth = auth;\n    this.router = router;\n    this.utils = utils;\n    this.engineToken = 'Bearer eyJhbGciOiJIUzI1NiJ9.InJwYV9ib3Qi.baB1eGAWiYsC58FAgKqnQ52RYon50cs6wygMvmPS6PM';\n  }\n  intercept(req, next) {\n    if (this.auth.getCurrentUser()) {\n      const parsedUrl = new URL(req.url);\n      const port = parsedUrl.port;\n      let token = port === '5000' ? this.engineToken : this.auth.getCurrentUser().token;\n      if (req.url != \"https://api.bigdatacloud.net/data/client-ip\") {\n        req = req.clone({\n          setHeaders: {\n            'Content-Type': 'application/json; charset=utf-8',\n            'Accept': 'application/json',\n            'Authorization': `${token}`\n          }\n        });\n      }\n    }\n    return next.handle(req).pipe(map(response => {\n      let res = response;\n      if (res.body && res.body.newObj) {\n        if (Object.keys(res.body.newObj.obj).length === 0) {\n          if (Object.keys(res.body.newObj).length === 0) throwError({});\n        }\n      }\n      return res;\n    }), catchError(error => {\n      let err = {};\n      if (error.error instanceof ErrorEvent) {\n        err.msg = error.error.message;\n        err.status = 'Client Side Error';\n      } else {\n        err.status = error.status;\n        switch (err.status) {\n          case 403:\n            err.msg = 'Access Forbidden';\n            this.router.navigate(['/dashboard/unauthorized'], {\n              queryParams: {\n                key: false,\n                message: \"You're not on the authorized IP. Contact the responsible person to get it authorized\"\n              }\n            });\n            break;\n          case 400:\n            err.msg = 'Session already taken';\n            this.router.navigate(['/dashboard/unauthorized'], {\n              queryParams: {\n                key: true,\n                message: error.error.message\n              }\n            });\n            break;\n          case 401:\n            err.msg = 'Your session has expired. Please Login again';\n            this.auth.logout();\n            this.router.navigate(['/signin']);\n            break;\n          case 404:\n            err.msg = 'Invalid credentials';\n            this.auth.logout();\n            this.router.navigate(['/signin']);\n            break;\n          case 500:\n            err.msg = 'We seem to be having trouble right now. Please try again after sometime';\n            break;\n          case 0:\n            err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';\n            break;\n          case 409:\n            err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';\n            break;\n          case undefined:\n            err.msg = 'No data found for the current selections';\n            break;\n          default:\n            err.msg = 'Internal error';\n        }\n      }\n      this.utils.snackBarShowError(err.msg);\n      return throwError(err);\n    }));\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}\nexport { AuthInterceptor };", "map": {"version": 3, "names": ["catchError", "map", "throwError", "AuthInterceptor", "constructor", "auth", "router", "utils", "engineToken", "intercept", "req", "next", "getCurrentUser", "parsedUrl", "URL", "url", "port", "token", "clone", "setHeaders", "handle", "pipe", "response", "res", "body", "newObj", "Object", "keys", "obj", "length", "error", "err", "ErrorEvent", "msg", "message", "status", "navigate", "queryParams", "key", "logout", "undefined", "snackBarShowError", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "i3", "NotificationService", "factory", "ɵfac"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/_interceptors/auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';\nimport { Observable, catchError, map, throwError } from 'rxjs';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nimport { NotificationService } from '../services/notification.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  engineToken : string = 'Bearer eyJhbGciOiJIUzI1NiJ9.InJwYV9ib3Qi.baB1eGAWiYsC58FAgKqnQ52RYon50cs6wygMvmPS6PM'  \n  constructor(private auth: AuthService,private router : Router,private utils: NotificationService) {\n  }\n\n  intercept( req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    if (this.auth.getCurrentUser()){\n      const parsedUrl = new URL(req.url);\n      const port = parsedUrl.port;\n      let token = (port === '5000') ? this.engineToken : this.auth.getCurrentUser().token\n      if (req.url != \"https://api.bigdatacloud.net/data/client-ip\"){\n        req = req.clone({\n          setHeaders: {\n            'Content-Type': 'application/json; charset=utf-8',\n            'Accept': 'application/json',\n            'Authorization': `${token}`,\n          },\n        });\n      }\n    }\n\n\n    return next.handle(req).pipe(map(response => {\n      let res : any = response;\n      if(res.body && res.body.newObj){\n        if(Object.keys(res.body.newObj.obj).length === 0){\n          if(Object.keys(res.body.newObj).length === 0)\n          throwError({});\n        }\n      }\n      return res;\n    }),catchError((error: HttpErrorResponse) => {\n      let err: any = {};\n      if (error.error instanceof ErrorEvent) {\n        err.msg = error.error.message;\n        err.status = 'Client Side Error'\n      }\n      else { \n        err.status = error.status;\n        switch (err.status) {\n          case 403:\n            err.msg = 'Access Forbidden';\n            this.router.navigate(['/dashboard/unauthorized'], { queryParams: { key: false , message: \"You're not on the authorized IP. Contact the responsible person to get it authorized\"} });\n            break;\n          case 400:\n            err.msg = 'Session already taken';\n            this.router.navigate(['/dashboard/unauthorized'], { queryParams: { key: true , message: error.error.message} });\n            break;\n          case 401:\n            err.msg = 'Your session has expired. Please Login again'\n            this.auth.logout();\n            this.router.navigate(['/signin']);\n            break;\n          case 404:\n            err.msg = 'Invalid credentials';\n            this.auth.logout();\n            this.router.navigate(['/signin']);\n            break;\n          case 500:\n            err.msg = 'We seem to be having trouble right now. Please try again after sometime';\n            break;\n          case 0:\n            err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';\n            break;\n          case 409:\n            err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';\n            break;\n          case undefined:\n            err.msg = 'No data found for the current selections';\n          break;\n          default:\n          err.msg = 'Internal error';\n        }\n      }\n      this.utils.snackBarShowError(err.msg);\n      return throwError(err);\n    }));\n  }\n}\n\n\n\n"], "mappings": "AAEA,SAAqBA,UAAU,EAAEC,GAAG,EAAEC,UAAU,QAAQ,MAAM;;;;;AAK9D,MACaC,eAAe;EAE1BC,YAAoBC,IAAiB,EAASC,MAAe,EAASC,KAA0B;IAA5E,KAAAF,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,KAAK,GAALA,KAAK;IAD3E,KAAAC,WAAW,GAAY,sFAAsF;EAE7G;EAEAC,SAASA,CAAEC,GAAqB,EAAEC,IAAiB;IACjD,IAAI,IAAI,CAACN,IAAI,CAACO,cAAc,EAAE,EAAC;MAC7B,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAACJ,GAAG,CAACK,GAAG,CAAC;MAClC,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI;MAC3B,IAAIC,KAAK,GAAID,IAAI,KAAK,MAAM,GAAI,IAAI,CAACR,WAAW,GAAG,IAAI,CAACH,IAAI,CAACO,cAAc,EAAE,CAACK,KAAK;MACnF,IAAIP,GAAG,CAACK,GAAG,IAAI,6CAA6C,EAAC;QAC3DL,GAAG,GAAGA,GAAG,CAACQ,KAAK,CAAC;UACdC,UAAU,EAAE;YACV,cAAc,EAAE,iCAAiC;YACjD,QAAQ,EAAE,kBAAkB;YAC5B,eAAe,EAAE,GAAGF,KAAK;;SAE5B,CAAC;;;IAKN,OAAON,IAAI,CAACS,MAAM,CAACV,GAAG,CAAC,CAACW,IAAI,CAACpB,GAAG,CAACqB,QAAQ,IAAG;MAC1C,IAAIC,GAAG,GAASD,QAAQ;MACxB,IAAGC,GAAG,CAACC,IAAI,IAAID,GAAG,CAACC,IAAI,CAACC,MAAM,EAAC;QAC7B,IAAGC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAACC,IAAI,CAACC,MAAM,CAACG,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC,EAAC;UAC/C,IAAGH,MAAM,CAACC,IAAI,CAACJ,GAAG,CAACC,IAAI,CAACC,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC,EAC5C3B,UAAU,CAAC,EAAE,CAAC;;;MAGlB,OAAOqB,GAAG;IACZ,CAAC,CAAC,EAACvB,UAAU,CAAE8B,KAAwB,IAAI;MACzC,IAAIC,GAAG,GAAQ,EAAE;MACjB,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;QACrCD,GAAG,CAACE,GAAG,GAAGH,KAAK,CAACA,KAAK,CAACI,OAAO;QAC7BH,GAAG,CAACI,MAAM,GAAG,mBAAmB;OACjC,MACI;QACHJ,GAAG,CAACI,MAAM,GAAGL,KAAK,CAACK,MAAM;QACzB,QAAQJ,GAAG,CAACI,MAAM;UAChB,KAAK,GAAG;YACNJ,GAAG,CAACE,GAAG,GAAG,kBAAkB;YAC5B,IAAI,CAAC3B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;cAAEC,WAAW,EAAE;gBAAEC,GAAG,EAAE,KAAK;gBAAGJ,OAAO,EAAE;cAAsF;YAAC,CAAE,CAAC;YACnL;UACF,KAAK,GAAG;YACNH,GAAG,CAACE,GAAG,GAAG,uBAAuB;YACjC,IAAI,CAAC3B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;cAAEC,WAAW,EAAE;gBAAEC,GAAG,EAAE,IAAI;gBAAGJ,OAAO,EAAEJ,KAAK,CAACA,KAAK,CAACI;cAAO;YAAC,CAAE,CAAC;YAC/G;UACF,KAAK,GAAG;YACNH,GAAG,CAACE,GAAG,GAAG,8CAA8C;YACxD,IAAI,CAAC5B,IAAI,CAACkC,MAAM,EAAE;YAClB,IAAI,CAACjC,MAAM,CAAC8B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;YACjC;UACF,KAAK,GAAG;YACNL,GAAG,CAACE,GAAG,GAAG,qBAAqB;YAC/B,IAAI,CAAC5B,IAAI,CAACkC,MAAM,EAAE;YAClB,IAAI,CAACjC,MAAM,CAAC8B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;YACjC;UACF,KAAK,GAAG;YACNL,GAAG,CAACE,GAAG,GAAG,yEAAyE;YACnF;UACF,KAAK,CAAC;YACJF,GAAG,CAACE,GAAG,GAAG,8FAA8F;YACxG;UACF,KAAK,GAAG;YACNF,GAAG,CAACE,GAAG,GAAG,8FAA8F;YACxG;UACF,KAAKO,SAAS;YACZT,GAAG,CAACE,GAAG,GAAG,0CAA0C;YACtD;UACA;YACAF,GAAG,CAACE,GAAG,GAAG,gBAAgB;;;MAG9B,IAAI,CAAC1B,KAAK,CAACkC,iBAAiB,CAACV,GAAG,CAACE,GAAG,CAAC;MACrC,OAAO/B,UAAU,CAAC6B,GAAG,CAAC;IACxB,CAAC,CAAC,CAAC;EACL;;;uBA7EW5B,eAAe,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;aAAf9C,eAAe;MAAA+C,OAAA,EAAf/C,eAAe,CAAAgD;IAAA;EAAA;;SAAfhD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}