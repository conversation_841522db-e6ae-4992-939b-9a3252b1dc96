{"ast": null, "code": "import { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/share-data.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"src/app/services/time-out.service\";\nimport * as i5 from \"src/app/services/inventory.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nfunction DashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\", 8);\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Loading application...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"mat-card\")(3, \"div\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshPage());\n    });\n    i0.ɵɵtext(7, \" click to update \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nclass DashboardComponent {\n  constructor(auth, sharedData, cd, notify, sessionTimeoutService, api) {\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.sessionTimeoutService = sessionTimeoutService;\n    this.api = api;\n    this.dashboardMenuService = inject(DashboardMenuService);\n    this.isReady = false;\n    this.dashboardPanel = [];\n    this.menuItems = [];\n    this.showBanner = false;\n    this.message = 'A new version of our software is now available!';\n    this.versionNumber = 'v2.14.0';\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber);\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      this.sharedData.checkMapping(data['bulkMapping']);\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']) {\n        this.showBanner = true;\n        this.sharedData.sendVersionNumber(this.versionNumber);\n      } else {\n        this.showBanner = false;\n      }\n      if (data['result'] === 'success') {\n        this.sharedData.sendTimeOutData(600);\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges();\n    });\n  }\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n  ngOnInit() {\n    // Set initial loading state\n    this.isReady = false;\n    try {\n      // Safely parse access data from session storage\n      let data = {};\n      try {\n        const accessData = sessionStorage.getItem('access');\n        if (accessData) {\n          data = JSON.parse(accessData);\n        }\n      } catch (error) {\n        console.error('Error parsing access data:', error);\n      }\n      // Handle settings access\n      if (data['settings'] || this.access && this.access['settings']) {\n        const lowercasedRoles = data['settings'] ?? this.access['settings'].map(role => role.toLowerCase());\n        const lowercasedData = this.user.role.toLowerCase();\n        this.sharedData.checkSetting(lowercasedRoles.includes(lowercasedData));\n      } else {\n        this.sharedData.checkSetting(false);\n      }\n      // Handle bulk excel upload access\n      if (data && data['bulkExcel'] || this.access && this.access['bulkExcel']) {\n        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map(role => role.toLowerCase());\n        const lowercasedUploadData = this.user.role.toLowerCase();\n        this.sharedData.checkUploads(lowercasedUpload.includes(lowercasedUploadData));\n      } else {\n        this.sharedData.checkUploads(false);\n      }\n      // Check if we have cached navigation items\n      const cachedNavItems = this.sharedData.getNavigationItems();\n      if (cachedNavItems && cachedNavItems.length > 0) {\n        console.log('Using cached navigation items');\n        this.menuItems = cachedNavItems;\n        this.isReady = true;\n        this.cd.markForCheck();\n        // Still refresh in the background to ensure data is up to date\n        this.getNavigationItems(0, 3, false);\n      } else {\n        // No cached items, load from API\n        this.getNavigationItems();\n      }\n    } catch (error) {\n      console.error('Error in dashboard initialization:', error);\n      // Ensure we still try to load navigation items even if there's an error\n      this.getNavigationItems();\n    }\n  }\n  getNavigationItems(retryCount = 0, maxRetries = 3, updateUI = true) {\n    const ROUTES = [{\n      path: '/dashboard/inventory',\n      title: 'Inventory Management',\n      icon: 'table_chart',\n      class: '',\n      dbAccess: \"inventory\"\n    }, {\n      path: '/dashboard/user',\n      title: 'User & Branch Management',\n      icon: 'person',\n      class: '',\n      dbAccess: \"user\"\n    }, {\n      path: '/dashboard/recipe',\n      title: 'Recipe Management',\n      icon: 'fastfood',\n      class: '',\n      dbAccess: \"recipe\"\n    }, {\n      path: '/dashboard/party',\n      title: 'Party Management',\n      icon: 'event_note',\n      class: '',\n      dbAccess: \"party\"\n    }, {\n      path: '/dashboard/account',\n      title: 'Account Setup',\n      icon: 'add_to_photos',\n      class: '',\n      dbAccess: \"accountSetup\"\n    }];\n    // Clear menu items before loading to avoid duplicates on retry\n    this.menuItems = [];\n    if (this.user.tenantId != '100000') {\n      this.api.getUIAccess(this.user.tenantId).subscribe({\n        next: res => {\n          if (res['success']) {\n            this.access = res['access'];\n            ROUTES.forEach(el => {\n              if (this.access.hasOwnProperty(el['dbAccess'])) {\n                this.access[el['dbAccess']]['status'] === true && this.access[el['dbAccess']]['access'].map(access => access.toLowerCase()).includes(this.user.role.toLowerCase()) ? this.menuItems.push(el) : undefined;\n              }\n            });\n          } else {\n            this.access = {};\n          }\n          // Cache the navigation items\n          this.sharedData.setNavigationItems(this.menuItems);\n          // Only update UI if needed (not when refreshing in background)\n          if (updateUI) {\n            this.isReady = true;\n            this.cd.markForCheck();\n          }\n          // If no menu items were loaded and we haven't exceeded max retries, try again\n          if (this.menuItems.length === 0 && retryCount < maxRetries) {\n            console.log(`Retrying navigation items load, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries, updateUI);\n            }, 1000); // Wait 1 second before retrying\n          }\n        },\n\n        error: err => {\n          console.error('Error loading navigation items:', err);\n          // On error, if we haven't exceeded max retries, try again\n          if (retryCount < maxRetries) {\n            console.log(`Retrying navigation items load after error, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries, updateUI);\n            }, 1000); // Wait 1 second before retrying\n          } else {\n            // If we've exceeded max retries, set default navigation\n            console.log('Max retries exceeded, setting default navigation');\n            this.setDefaultNavigation();\n            // Cache the default navigation items\n            this.sharedData.setNavigationItems(this.menuItems);\n            // Only update UI if needed\n            if (updateUI) {\n              this.isReady = true;\n              this.cd.markForCheck();\n            }\n          }\n        }\n      });\n    } else {\n      this.menuItems = [{\n        path: '/dashboard/account',\n        title: 'Account Setup',\n        icon: 'add_to_photos',\n        class: '',\n        dbAccess: \"accountSetup\"\n      }];\n      this.isReady = true;\n      this.cd.markForCheck();\n    }\n  }\n  // Set default navigation items if API fails\n  setDefaultNavigation() {\n    this.menuItems = [{\n      path: '/dashboard/home',\n      title: 'Dashboard',\n      icon: 'dashboard',\n      class: ''\n    }];\n    // Add account setup for admin users\n    if (this.user.tenantId === '100000') {\n      this.menuItems.push({\n        path: '/dashboard/account',\n        title: 'Account Setup',\n        icon: 'add_to_photos',\n        class: ''\n      });\n    }\n  }\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n  generateLinks(module) {\n    const links = module.map(label => {\n      return {\n        label,\n        routerLink: `/dashboard/${label}`\n      };\n    });\n    return links;\n  }\n  onCtrlShiftR(event) {\n    event.preventDefault();\n    window.location.reload();\n  }\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.TimeOutService), i0.ɵɵdirectiveInject(i5.InventoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      hostBindings: function DashboardComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.control.shift.r\", function DashboardComponent_keydown_control_shift_r_HostBindingHandler($event) {\n            return ctx.onCtrlShiftR($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: DashboardMenuService,\n        useFactory: responsiveService => {\n          return new DashboardMenuService(responsiveService);\n        },\n        deps: [ResponsiveService]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 5,\n      consts: [[1, \"dashboard-container\"], [3, \"menuItems\", \"logoUrl\"], [\"class\", \"content mat-elevation-z8\", 4, \"ngIf\"], [\"class\", \"content mat-elevation-z8 loading-container\", 4, \"ngIf\"], [\"class\", \"closingContainer\", 4, \"ngIf\"], [1, \"content\", \"mat-elevation-z8\"], [1, \"content\", \"mat-elevation-z8\", \"loading-container\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"closingContainer\"], [1, \"closingContainerDatas\"], [1, \"closeMsg\"], [1, \"text-align-center\", \"text-center\", \"m-3\"], [\"mat-button\", \"\", \"mat-raised-button\", \"\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-dashboard-toolbar\", 1);\n          i0.ɵɵtemplate(2, DashboardComponent_div_2_Template, 2, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, DashboardComponent_div_3_Template, 6, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, DashboardComponent_div_4_Template, 8, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"menuItems\", ctx.menuItems)(\"logoUrl\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner && ctx.isReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner && !ctx.isReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showBanner);\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, RouterOutlet, MatDividerModule, MatIconModule, i7.MatIcon, DashboardToolbarComponent, MatCardModule, i8.MatCard, MatButtonModule, i9.MatButton],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  height: calc(100vh - 64px);\\n  border-radius: 8px;\\n  margin: 10px;\\n  padding: 0.5rem;\\n  overflow: auto;\\n  background-color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: calc(100vh - 64px);\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #ff9100;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  font-size: 16px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.closingContainer[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10vh;\\n  justify-content: center;\\n  min-height: 100vh;\\n}\\n\\n.closingContainerDatas[_ngcontent-%COMP%] {\\n  max-width: 85vw;\\n  pointer-events: auto;\\n  width: 550px;\\n  position: static;\\n}\\n\\n.closeMsg[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: large;\\n  font-weight: bold;\\n  padding-top: 2rem;\\n  padding-bottom: 1rem;\\n}\\n\\n.closeMsgBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardComponent };", "map": {"version": 3, "names": ["inject", "CommonModule", "RouterOutlet", "DashboardToolbarComponent", "ResponsiveService", "DashboardMenuService", "MatDividerModule", "MatIconModule", "MatButtonModule", "MatCardModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "DashboardComponent_div_4_Template_button_click_6_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshPage", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "message", "DashboardComponent", "constructor", "auth", "sharedData", "cd", "notify", "sessionTimeoutService", "api", "dashboardMenuService", "isReady", "dashboardPanel", "menuItems", "showBanner", "versionNumber", "user", "getCurrentUser", "userRole", "getCurrRole", "sendVersionNumber", "getRolesList", "tenantId", "subscribe", "data", "checkMapping", "logoUrl", "tenantDetails", "logo", "sendTimeOutData", "start", "detectChanges", "showSidenav$", "sidenavType$", "showSmallDeviceMenuButton$", "ngOnInit", "accessData", "sessionStorage", "getItem", "JSON", "parse", "error", "console", "access", "lowercasedRoles", "map", "role", "toLowerCase", "lowercasedData", "checkSetting", "includes", "lowercasedUpload", "lowercasedUploadData", "checkUploads", "cachedNavItems", "getNavigationItems", "length", "log", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retryCount", "maxRetries", "updateUI", "ROUTES", "path", "title", "icon", "class", "dbAccess", "getUIAccess", "next", "res", "for<PERSON>ach", "el", "hasOwnProperty", "push", "undefined", "setNavigationItems", "setTimeout", "err", "setDefaultNavigation", "toggleMenu", "generateLinks", "module", "links", "label", "routerLink", "onCtrlShiftR", "event", "preventDefault", "window", "location", "reload", "KeyboardEvent", "key", "code", "ctrl<PERSON>ey", "shift<PERSON>ey", "document", "dispatchEvent", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ShareDataService", "ChangeDetectorRef", "i3", "NotificationService", "i4", "TimeOutService", "i5", "InventoryService", "selectors", "hostBindings", "DashboardComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument", "provide", "useFactory", "responsiveService", "deps", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "ɵɵtemplate", "DashboardComponent_div_2_Template", "DashboardComponent_div_3_Template", "DashboardComponent_div_4_Template", "ɵɵproperty", "i6", "NgIf", "i7", "MatIcon", "i8", "MatCard", "i9", "MatButton", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, OnInit, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterOutlet } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { FixedFabButtonComponent } from 'src/app/components/fixed-fab-button/fixed-fab-button.component';\nimport { DashboardMenuComponent } from 'src/app/components/dashboard-menu/dashboard-menu.component';\n\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\n\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { TimeOutService } from 'src/app/services/time-out.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatDividerModule,\n    MatIconModule,\n    DashboardMenuComponent,\n    FixedFabButtonComponent,\n    DashboardToolbarComponent,\n    MatCardModule,\n    MatButtonModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {\n      provide: DashboardMenuService,\n      useFactory: (responsiveService: ResponsiveService) => {\n        return new DashboardMenuService(responsiveService);\n      },\n      deps: [ResponsiveService],\n    },\n  ],\n})\nexport class DashboardComponent implements OnInit {\n  private dashboardMenuService = inject(DashboardMenuService);\n  public isReady = false;\n  public dashboardPanel: {\n    icon: string;\n    title: string;\n    links: { label: string; routerLink: string }[];\n  }[] = [];\n  public menuItems: any[] = [];\n  public user: any;\n  public userRole: any;\n  public access: any;\n  @Input() showBanner: boolean = false;\n  @Input() message: string = 'A new version of our software is now available!';\n  versionNumber: string = 'v2.14.0';\n  logoUrl: string\n  constructor(\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService,\n    private sessionTimeoutService: TimeOutService,\n    private api: InventoryService,\n\n ) {\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber)\n    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {\n      this.sharedData.checkMapping(data['bulkMapping'])\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']){\n        this.showBanner = true\n        this.sharedData.sendVersionNumber(this.versionNumber)\n      }else{\n        this.showBanner = false\n      }\n      if(data['result'] === 'success'){\n        this.sharedData.sendTimeOutData(600)\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges()\n    });\n\n  }\n\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n\n  ngOnInit(): void {\n    // Set initial loading state\n    this.isReady = false;\n\n    try {\n      // Safely parse access data from session storage\n      let data = {};\n      try {\n        const accessData = sessionStorage.getItem('access');\n        if (accessData) {\n          data = JSON.parse(accessData);\n        }\n      } catch (error) {\n        console.error('Error parsing access data:', error);\n      }\n\n      // Handle settings access\n      if(data['settings'] || (this.access && this.access['settings'])){\n        const lowercasedRoles = data['settings'] ?? this.access['settings'].map((role: string) => role.toLowerCase());\n        const lowercasedData = this.user.role.toLowerCase();\n        this.sharedData.checkSetting(lowercasedRoles.includes(lowercasedData));\n      } else {\n        this.sharedData.checkSetting(false);\n      }\n\n      // Handle bulk excel upload access\n      if((data && data['bulkExcel']) || (this.access && this.access['bulkExcel'])){\n        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map((role: string) => role.toLowerCase());\n        const lowercasedUploadData = this.user.role.toLowerCase();\n        this.sharedData.checkUploads(lowercasedUpload.includes(lowercasedUploadData));\n      } else {\n        this.sharedData.checkUploads(false);\n      }\n\n      // Check if we have cached navigation items\n      const cachedNavItems = this.sharedData.getNavigationItems();\n      if (cachedNavItems && cachedNavItems.length > 0) {\n        console.log('Using cached navigation items');\n        this.menuItems = cachedNavItems;\n        this.isReady = true;\n        this.cd.markForCheck();\n\n        // Still refresh in the background to ensure data is up to date\n        this.getNavigationItems(0, 3, false);\n      } else {\n        // No cached items, load from API\n        this.getNavigationItems();\n      }\n\n    } catch (error) {\n      console.error('Error in dashboard initialization:', error);\n\n      // Ensure we still try to load navigation items even if there's an error\n      this.getNavigationItems();\n    }\n  }\n\n  getNavigationItems(retryCount = 0, maxRetries = 3, updateUI = true) {\n    const ROUTES = [\n      { path: '/dashboard/inventory', title: 'Inventory Management', icon: 'table_chart', class: '', dbAccess: \"inventory\" },\n      { path: '/dashboard/user', title: 'User & Branch Management', icon: 'person', class: '', dbAccess: \"user\" },\n      { path: '/dashboard/recipe', title: 'Recipe Management', icon: 'fastfood', class: '', dbAccess: \"recipe\" },\n      { path: '/dashboard/party', title: 'Party Management', icon: 'event_note', class: '', dbAccess: \"party\" },\n      { path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: \"accountSetup\" },\n    ];\n\n    // Clear menu items before loading to avoid duplicates on retry\n    this.menuItems = [];\n\n    if (this.user.tenantId != '100000') {\n      this.api.getUIAccess(this.user.tenantId).subscribe({\n        next: (res) => {\n          if(res['success']) {\n            this.access = res['access'];\n            ROUTES.forEach((el) => {\n              if(this.access.hasOwnProperty(el['dbAccess'])) {\n                (this.access[el['dbAccess']]['status'] === true &&\n                 this.access[el['dbAccess']]['access'].map(access => access.toLowerCase()).includes(this.user.role.toLowerCase()))\n                  ? this.menuItems.push(el)\n                  : undefined;\n              }\n            });\n          } else {\n            this.access = {};\n          }\n\n          // Cache the navigation items\n          this.sharedData.setNavigationItems(this.menuItems);\n\n          // Only update UI if needed (not when refreshing in background)\n          if (updateUI) {\n            this.isReady = true;\n            this.cd.markForCheck();\n          }\n\n          // If no menu items were loaded and we haven't exceeded max retries, try again\n          if (this.menuItems.length === 0 && retryCount < maxRetries) {\n            console.log(`Retrying navigation items load, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries, updateUI);\n            }, 1000); // Wait 1 second before retrying\n          }\n        },\n        error: (err) => {\n          console.error('Error loading navigation items:', err);\n\n          // On error, if we haven't exceeded max retries, try again\n          if (retryCount < maxRetries) {\n            console.log(`Retrying navigation items load after error, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries, updateUI);\n            }, 1000); // Wait 1 second before retrying\n          } else {\n            // If we've exceeded max retries, set default navigation\n            console.log('Max retries exceeded, setting default navigation');\n            this.setDefaultNavigation();\n\n            // Cache the default navigation items\n            this.sharedData.setNavigationItems(this.menuItems);\n\n            // Only update UI if needed\n            if (updateUI) {\n              this.isReady = true;\n              this.cd.markForCheck();\n            }\n          }\n        }\n      });\n    } else {\n      this.menuItems = [{ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: \"accountSetup\" }];\n      this.isReady = true;\n      this.cd.markForCheck();\n    }\n  }\n\n  // Set default navigation items if API fails\n  setDefaultNavigation() {\n    this.menuItems = [\n      { path: '/dashboard/home', title: 'Dashboard', icon: 'dashboard', class: '' }\n    ];\n\n    // Add account setup for admin users\n    if (this.user.tenantId === '100000') {\n      this.menuItems.push({ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '' });\n    }\n  }\n\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n\n  generateLinks(module) {\n    const links = module.map(label => {\n      return { label, routerLink: `/dashboard/${label}` };\n    });\n    return links;\n  }\n\n  @HostListener('document:keydown.control.shift.r', ['$event'])\n  onCtrlShiftR(event: KeyboardEvent) {\n    event.preventDefault();\n    window.location.reload();\n  }\n\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n}\n\n\n", "<div class=\"dashboard-container\">\n  <app-dashboard-toolbar\n    [menuItems]=\"menuItems\"\n    [logoUrl]=\"logoUrl\">\n  </app-dashboard-toolbar>\n\n  <!-- Main content area -->\n  <div class=\"content mat-elevation-z8\" *ngIf=\"!showBanner && isReady\">\n    <router-outlet></router-outlet>\n  </div>\n\n  <!-- Loading indicator when navigation is not ready -->\n  <div class=\"content mat-elevation-z8 loading-container\" *ngIf=\"!showBanner && !isReady\">\n    <div class=\"loading-spinner\">\n      <mat-icon class=\"spin\">refresh</mat-icon>\n      <p>Loading application...</p>\n    </div>\n  </div>\n\n  <!-- Update banner -->\n  <div class=\"closingContainer\" *ngIf=\"showBanner\">\n    <div class=\"closingContainerDatas\">\n      <mat-card>\n        <div class=\"closeMsg\">\n          {{message}}\n        </div>\n        <div class=\"text-align-center text-center m-3\">\n          <button mat-button mat-raised-button (click)=\"refreshPage()\">\n            click to update\n          </button>\n        </div>\n      </mat-card>\n    </div>\n  </div>\n</div>"], "mappings": "AAAA,SAA6FA,MAAM,QAAQ,eAAe;AAC1H,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,yBAAyB,QAAQ,kEAAkE;AAI5G,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAKtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;ICTpDC,EAAA,CAAAC,cAAA,aAAqE;IACnED,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,aAAwF;IAE7DD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAKjCH,EAAA,CAAAC,cAAA,aAAiD;IAIzCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA+C;IACRD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC1DZ,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALTH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;ADNR,MA2BaC,kBAAkB;EAgB7BC,YACUC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B,EAC3BC,qBAAqC,EACrCC,GAAqB;IALrB,KAAAL,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IArBL,KAAAC,oBAAoB,GAAGnC,MAAM,CAACK,oBAAoB,CAAC;IACpD,KAAA+B,OAAO,GAAG,KAAK;IACf,KAAAC,cAAc,GAIf,EAAE;IACD,KAAAC,SAAS,GAAU,EAAE;IAInB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAb,OAAO,GAAW,iDAAiD;IAC5E,KAAAc,aAAa,GAAW,SAAS;IAW/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACa,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACd,IAAI,CAACe,WAAW,EAAE;IACvC,IAAI,CAACd,UAAU,CAACe,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;IACrD,IAAI,CAACX,IAAI,CAACiB,YAAY,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACN,IAAI,CAACM;IAAQ,CAAE,CAAC,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC1E,IAAI,CAACnB,UAAU,CAACoB,YAAY,CAACD,IAAI,CAAC,aAAa,CAAC,CAAC;MACjD,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACG,aAAa,CAACC,IAAI;MACtC,IAAI,IAAI,CAACb,aAAa,KAAKS,IAAI,CAAC,WAAW,CAAC,EAAC;QAC3C,IAAI,CAACV,UAAU,GAAG,IAAI;QACtB,IAAI,CAACT,UAAU,CAACe,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;OACtD,MAAI;QACH,IAAI,CAACD,UAAU,GAAG,KAAK;;MAEzB,IAAGU,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAC;QAC9B,IAAI,CAACnB,UAAU,CAACwB,eAAe,CAAC,GAAG,CAAC;QACpC,IAAI,CAACrB,qBAAqB,CAACsB,KAAK,EAAE;;MAEpC,IAAI,CAACxB,EAAE,CAACyB,aAAa,EAAE;IACzB,CAAC,CAAC;EAEJ;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACtB,oBAAoB,CAACsB,YAAY;EAC/C;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACvB,oBAAoB,CAACuB,YAAY;EAC/C;EAEA,IAAIC,0BAA0BA,CAAA;IAC5B,OAAO,IAAI,CAACxB,oBAAoB,CAACwB,0BAA0B;EAC7D;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACxB,OAAO,GAAG,KAAK;IAEpB,IAAI;MACF;MACA,IAAIa,IAAI,GAAG,EAAE;MACb,IAAI;QACF,MAAMY,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC;QACnD,IAAIF,UAAU,EAAE;UACdZ,IAAI,GAAGe,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;;OAEhC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAGpD;MACA,IAAGjB,IAAI,CAAC,UAAU,CAAC,IAAK,IAAI,CAACmB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,UAAU,CAAE,EAAC;QAC9D,MAAMC,eAAe,GAAGpB,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAACmB,MAAM,CAAC,UAAU,CAAC,CAACE,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC;QAC7G,MAAMC,cAAc,GAAG,IAAI,CAAChC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE;QACnD,IAAI,CAAC1C,UAAU,CAAC4C,YAAY,CAACL,eAAe,CAACM,QAAQ,CAACF,cAAc,CAAC,CAAC;OACvE,MAAM;QACL,IAAI,CAAC3C,UAAU,CAAC4C,YAAY,CAAC,KAAK,CAAC;;MAGrC;MACA,IAAIzB,IAAI,IAAIA,IAAI,CAAC,WAAW,CAAC,IAAM,IAAI,CAACmB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,WAAW,CAAE,EAAC;QAC1E,MAAMQ,gBAAgB,GAAG3B,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAACmB,MAAM,CAAC,WAAW,CAAC,CAACE,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC;QAChH,MAAMK,oBAAoB,GAAG,IAAI,CAACpC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE;QACzD,IAAI,CAAC1C,UAAU,CAACgD,YAAY,CAACF,gBAAgB,CAACD,QAAQ,CAACE,oBAAoB,CAAC,CAAC;OAC9E,MAAM;QACL,IAAI,CAAC/C,UAAU,CAACgD,YAAY,CAAC,KAAK,CAAC;;MAGrC;MACA,MAAMC,cAAc,GAAG,IAAI,CAACjD,UAAU,CAACkD,kBAAkB,EAAE;MAC3D,IAAID,cAAc,IAAIA,cAAc,CAACE,MAAM,GAAG,CAAC,EAAE;QAC/Cd,OAAO,CAACe,GAAG,CAAC,+BAA+B,CAAC;QAC5C,IAAI,CAAC5C,SAAS,GAAGyC,cAAc;QAC/B,IAAI,CAAC3C,OAAO,GAAG,IAAI;QACnB,IAAI,CAACL,EAAE,CAACoD,YAAY,EAAE;QAEtB;QACA,IAAI,CAACH,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;OACrC,MAAM;QACL;QACA,IAAI,CAACA,kBAAkB,EAAE;;KAG5B,CAAC,OAAOd,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAE1D;MACA,IAAI,CAACc,kBAAkB,EAAE;;EAE7B;EAEAA,kBAAkBA,CAACI,UAAU,GAAG,CAAC,EAAEC,UAAU,GAAG,CAAC,EAAEC,QAAQ,GAAG,IAAI;IAChE,MAAMC,MAAM,GAAG,CACb;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE,EACtH;MAAEJ,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,0BAA0B;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAE,EAC3G;MAAEJ,IAAI,EAAE,mBAAmB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAE,EAC1G;MAAEJ,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAO,CAAE,EACzG;MAAEJ,IAAI,EAAE,oBAAoB;MAAEC,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAc,CAAE,CACnH;IAED;IACA,IAAI,CAACtD,SAAS,GAAG,EAAE;IAEnB,IAAI,IAAI,CAACG,IAAI,CAACM,QAAQ,IAAI,QAAQ,EAAE;MAClC,IAAI,CAACb,GAAG,CAAC2D,WAAW,CAAC,IAAI,CAACpD,IAAI,CAACM,QAAQ,CAAC,CAACC,SAAS,CAAC;QACjD8C,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAGA,GAAG,CAAC,SAAS,CAAC,EAAE;YACjB,IAAI,CAAC3B,MAAM,GAAG2B,GAAG,CAAC,QAAQ,CAAC;YAC3BR,MAAM,CAACS,OAAO,CAAEC,EAAE,IAAI;cACpB,IAAG,IAAI,CAAC7B,MAAM,CAAC8B,cAAc,CAACD,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE;gBAC5C,IAAI,CAAC7B,MAAM,CAAC6B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,IAAI,IAC9C,IAAI,CAAC7B,MAAM,CAAC6B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC3B,GAAG,CAACF,MAAM,IAAIA,MAAM,CAACI,WAAW,EAAE,CAAC,CAACG,QAAQ,CAAC,IAAI,CAAClC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE,CAAC,GAC7G,IAAI,CAAClC,SAAS,CAAC6D,IAAI,CAACF,EAAE,CAAC,GACvBG,SAAS;;YAEjB,CAAC,CAAC;WACH,MAAM;YACL,IAAI,CAAChC,MAAM,GAAG,EAAE;;UAGlB;UACA,IAAI,CAACtC,UAAU,CAACuE,kBAAkB,CAAC,IAAI,CAAC/D,SAAS,CAAC;UAElD;UACA,IAAIgD,QAAQ,EAAE;YACZ,IAAI,CAAClD,OAAO,GAAG,IAAI;YACnB,IAAI,CAACL,EAAE,CAACoD,YAAY,EAAE;;UAGxB;UACA,IAAI,IAAI,CAAC7C,SAAS,CAAC2C,MAAM,KAAK,CAAC,IAAIG,UAAU,GAAGC,UAAU,EAAE;YAC1DlB,OAAO,CAACe,GAAG,CAAC,2CAA2CE,UAAU,GAAG,CAAC,EAAE,CAAC;YACxEkB,UAAU,CAAC,MAAK;cACd,IAAI,CAACtB,kBAAkB,CAACI,UAAU,GAAG,CAAC,EAAEC,UAAU,EAAEC,QAAQ,CAAC;YAC/D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEd,CAAC;;QACDpB,KAAK,EAAGqC,GAAG,IAAI;UACbpC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEqC,GAAG,CAAC;UAErD;UACA,IAAInB,UAAU,GAAGC,UAAU,EAAE;YAC3BlB,OAAO,CAACe,GAAG,CAAC,uDAAuDE,UAAU,GAAG,CAAC,EAAE,CAAC;YACpFkB,UAAU,CAAC,MAAK;cACd,IAAI,CAACtB,kBAAkB,CAACI,UAAU,GAAG,CAAC,EAAEC,UAAU,EAAEC,QAAQ,CAAC;YAC/D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;WACX,MAAM;YACL;YACAnB,OAAO,CAACe,GAAG,CAAC,kDAAkD,CAAC;YAC/D,IAAI,CAACsB,oBAAoB,EAAE;YAE3B;YACA,IAAI,CAAC1E,UAAU,CAACuE,kBAAkB,CAAC,IAAI,CAAC/D,SAAS,CAAC;YAElD;YACA,IAAIgD,QAAQ,EAAE;cACZ,IAAI,CAAClD,OAAO,GAAG,IAAI;cACnB,IAAI,CAACL,EAAE,CAACoD,YAAY,EAAE;;;QAG5B;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC7C,SAAS,GAAG,CAAC;QAAEkD,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAc,CAAE,CAAC;MACrI,IAAI,CAACxD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACL,EAAE,CAACoD,YAAY,EAAE;;EAE1B;EAEA;EACAqB,oBAAoBA,CAAA;IAClB,IAAI,CAAClE,SAAS,GAAG,CACf;MAAEkD,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAE,CAAE,CAC9E;IAED;IACA,IAAI,IAAI,CAAClD,IAAI,CAACM,QAAQ,KAAK,QAAQ,EAAE;MACnC,IAAI,CAACT,SAAS,CAAC6D,IAAI,CAAC;QAAEX,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAE,CAAE,CAAC;;EAEjH;EAEAc,UAAUA,CAAA;IACR,IAAI,CAACtE,oBAAoB,CAACsE,UAAU,EAAE;EACxC;EAEAC,aAAaA,CAACC,MAAM;IAClB,MAAMC,KAAK,GAAGD,MAAM,CAACrC,GAAG,CAACuC,KAAK,IAAG;MAC/B,OAAO;QAAEA,KAAK;QAAEC,UAAU,EAAE,cAAcD,KAAK;MAAE,CAAE;IACrD,CAAC,CAAC;IACF,OAAOD,KAAK;EACd;EAGAG,YAAYA,CAACC,KAAoB;IAC/BA,KAAK,CAACC,cAAc,EAAE;IACtBC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEA9F,WAAWA,CAAA;IACT,MAAM0F,KAAK,GAAG,IAAIK,aAAa,CAAC,SAAS,EAAE;MACzCC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX,CAAC;IACFC,QAAQ,CAACC,aAAa,CAACX,KAAK,CAAC;EAC/B;;;uBAtOWrF,kBAAkB,EAAAjB,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAtH,EAAA,CAAAkH,iBAAA,CAAAlH,EAAA,CAAAuH,iBAAA,GAAAvH,EAAA,CAAAkH,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAzH,EAAA,CAAAkH,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA3H,EAAA,CAAAkH,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAlB5G,kBAAkB;MAAA6G,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAlBC,GAAA,CAAA7B,YAAA,CAAA8B,MAAA,CAAoB;UAAA,UAAAnI,EAAA,CAAAoI,iBAAA;;;;;;;;uCAVpB,CACT;QACEC,OAAO,EAAE1I,oBAAoB;QAC7B2I,UAAU,EAAGC,iBAAoC,IAAI;UACnD,OAAO,IAAI5I,oBAAoB,CAAC4I,iBAAiB,CAAC;QACpD,CAAC;QACDC,IAAI,EAAE,CAAC9I,iBAAiB;OACzB,CACF,GAAAM,EAAA,CAAAyI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CHjI,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAE,SAAA,+BAGwB;UAGxBF,EAAA,CAAA+I,UAAA,IAAAC,iCAAA,iBAEM;UAGNhJ,EAAA,CAAA+I,UAAA,IAAAE,iCAAA,iBAKM;UAGNjJ,EAAA,CAAA+I,UAAA,IAAAG,iCAAA,iBAaM;UACRlJ,EAAA,CAAAG,YAAA,EAAM;;;UAhCFH,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAmJ,UAAA,cAAAjB,GAAA,CAAAtG,SAAA,CAAuB,YAAAsG,GAAA,CAAAzF,OAAA;UAKczC,EAAA,CAAAa,SAAA,GAA4B;UAA5Bb,EAAA,CAAAmJ,UAAA,UAAAjB,GAAA,CAAArG,UAAA,IAAAqG,GAAA,CAAAxG,OAAA,CAA4B;UAKV1B,EAAA,CAAAa,SAAA,GAA6B;UAA7Bb,EAAA,CAAAmJ,UAAA,UAAAjB,GAAA,CAAArG,UAAA,KAAAqG,GAAA,CAAAxG,OAAA,CAA6B;UAQvD1B,EAAA,CAAAa,SAAA,GAAgB;UAAhBb,EAAA,CAAAmJ,UAAA,SAAAjB,GAAA,CAAArG,UAAA,CAAgB;;;qBDG7CtC,YAAY,EAAA6J,EAAA,CAAAC,IAAA,EACZ7J,YAAY,EACZI,gBAAgB,EAChBC,aAAa,EAAAyJ,EAAA,CAAAC,OAAA,EAGb9J,yBAAyB,EACzBM,aAAa,EAAAyJ,EAAA,CAAAC,OAAA,EACb3J,eAAe,EAAA4J,EAAA,CAAAC,SAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAeN5I,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}