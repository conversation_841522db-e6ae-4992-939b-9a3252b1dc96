{"ast": null, "code": "import { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { DashboardMenuLinksPanelComponent } from 'src/app/components/dashboard-menu-links-panel/dashboard-menu-links-panel.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/share-data.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"src/app/services/time-out.service\";\nimport * as i5 from \"src/app/services/inventory.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/sidenav\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nfunction DashboardComponent_app_dashboard_menu_links_panel_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-dashboard-menu-links-panel\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dashboardPanel\", ctx_r1.dashboardPanel)(\"minimized\", true);\n  }\n}\nfunction DashboardComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"mat-card\")(3, \"div\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.refreshPage());\n    });\n    i0.ɵɵtext(7, \" click to update \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\nclass DashboardComponent {\n  constructor(auth, sharedData, cd, notify, sessionTimeoutService, api) {\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.sessionTimeoutService = sessionTimeoutService;\n    this.api = api;\n    this.dashboardMenuService = inject(DashboardMenuService);\n    this.isReady = false;\n    this.dashboardPanel = [];\n    this.showBanner = false;\n    this.message = 'A new version of our software is now available!';\n    this.versionNumber = 'v2.13.0';\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber);\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      this.sharedData.checkMapping(data['bulkMapping']);\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']) {\n        this.showBanner = true;\n        this.sharedData.sendVersionNumber(this.versionNumber);\n      } else {\n        this.showBanner = false;\n      }\n      if (data['result'] === 'success') {\n        this.sharedData.sendTimeOutData(600);\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges();\n    });\n  }\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n  ngOnInit() {\n    let data = JSON.parse(sessionStorage.getItem('access'));\n    if (data['settings'] || this.access && this.access['settings']) {\n      const lowercasedRoles = data['settings'] ?? this.access['settings'].map(role => role.toLowerCase());\n      const lowercasedData = this.user.role.toLowerCase();\n      if (lowercasedRoles.includes(lowercasedData)) {\n        this.sharedData.checkSetting(true);\n      } else {\n        this.sharedData.checkSetting(false);\n      }\n    } else {\n      this.sharedData.checkSetting(false);\n    }\n    if (data && data['bulkExcel'] || this.access && this.access['bulkExcel']) {\n      const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map(role => role.toLowerCase());\n      const lowercasedUploadData = this.user.role.toLowerCase();\n      if (lowercasedUpload.includes(lowercasedUploadData)) {\n        this.sharedData.checkUploads(true);\n      } else {\n        this.sharedData.checkUploads(false);\n      }\n    } else {\n      this.sharedData.checkUploads(false);\n    }\n    this.isReady = true;\n  }\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n  generateLinks(module) {\n    const links = module.map(label => {\n      return {\n        label,\n        routerLink: `/dashboard/${label}`\n      };\n    });\n    return links;\n  }\n  onCtrlShiftR(event) {\n    event.preventDefault();\n    window.location.reload();\n  }\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.TimeOutService), i0.ɵɵdirectiveInject(i5.InventoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      hostBindings: function DashboardComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.control.shift.r\", function DashboardComponent_keydown_control_shift_r_HostBindingHandler($event) {\n            return ctx.onCtrlShiftR($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: DashboardMenuService,\n        useFactory: responsiveService => {\n          return new DashboardMenuService(responsiveService);\n        },\n        deps: [ResponsiveService]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 14,\n      consts: [[3, \"backdropClick\"], [1, \"minimized-sidenav\", 3, \"opened\", \"mode\", \"disableClose\"], [\"sidenav\", \"\"], [1, \"sidenav-icons-container\"], [3, \"dashboardPanel\", \"minimized\", 4, \"ngIf\"], [3, \"showNavbarToggleButton\", \"logoUrl\", \"toggleMenu\"], [\"class\", \"content mat-elevation-z8\", 4, \"ngIf\"], [\"class\", \"closingContainer\", 4, \"ngIf\"], [3, \"dashboardPanel\", \"minimized\"], [1, \"content\", \"mat-elevation-z8\"], [1, \"closingContainer\"], [1, \"closingContainerDatas\"], [1, \"closeMsg\"], [1, \"text-align-center\", \"text-center\", \"m-3\"], [\"mat-button\", \"\", \"mat-raised-button\", \"\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-sidenav-container\", 0);\n          i0.ɵɵlistener(\"backdropClick\", function DashboardComponent_Template_mat_sidenav_container_backdropClick_0_listener() {\n            return ctx.toggleMenu();\n          });\n          i0.ɵɵelementStart(1, \"mat-sidenav\", 1, 2);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, DashboardComponent_app_dashboard_menu_links_panel_6_Template, 1, 2, \"app-dashboard-menu-links-panel\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-sidenav-content\")(8, \"app-dashboard-toolbar\", 5);\n          i0.ɵɵlistener(\"toggleMenu\", function DashboardComponent_Template_app_dashboard_toolbar_toggleMenu_8_listener() {\n            return ctx.toggleMenu();\n          });\n          i0.ɵɵpipe(9, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, DashboardComponent_div_10_Template, 2, 0, \"div\", 6);\n          i0.ɵɵtemplate(11, DashboardComponent_div_11_Template, 8, 1, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"opened\", i0.ɵɵpipeBind1(3, 8, ctx.showSidenav$))(\"mode\", i0.ɵɵpipeBind1(4, 10, ctx.sidenavType$))(\"disableClose\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showNavbarToggleButton\", i0.ɵɵpipeBind1(9, 12, ctx.showSmallDeviceMenuButton$))(\"logoUrl\", ctx.logoUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showBanner);\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, i6.AsyncPipe, RouterOutlet, MatDividerModule, MatIconModule, MatSidenavModule, i7.MatSidenav, i7.MatSidenavContainer, i7.MatSidenavContent, DashboardMenuLinksPanelComponent, DashboardToolbarComponent, MatCardModule, i8.MatCard, MatButtonModule, i9.MatButton],\n      styles: [\"mat-sidenav[_ngcontent-%COMP%] {\\n  margin: 5px 5px 1px 5px;\\n  width: 15rem;\\n  border-right: none;\\n  color: white;\\n  border-radius: 8px;\\n  padding: 0.5rem;\\n  text-align: center;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  height: calc(100vh - 65px);\\n  border-radius: 8px;\\n  margin: 5px;\\n  margin-left: 10px;\\n  padding: 0.5rem;\\n  overflow: auto;\\n}\\n\\n.menu-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  font-size: 1rem;\\n}\\n.menu-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  height: 5.5rem;\\n  max-width: 9rem;\\n  min-width: 6rem;\\n  border-radius: 8px;\\n  margin: 5px;\\n}\\n\\n.name[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 1.2rem;\\n  font-weight: bold;\\n}\\n\\nmat-divider[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  margin-bottom: 10px;\\n  background-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.closingContainer[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10vh;\\n  justify-content: center;\\n  min-height: 100vh;\\n}\\n\\n.closingContainerDatas[_ngcontent-%COMP%] {\\n  max-width: 85vw;\\n  pointer-events: auto;\\n  width: 550px;\\n  position: static;\\n}\\n\\n.closeMsg[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: large;\\n  font-weight: bold;\\n  padding-top: 2rem;\\n  padding-bottom: 1rem;\\n}\\n\\n.closeMsgBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardComponent };", "map": {"version": 3, "names": ["inject", "CommonModule", "RouterOutlet", "DashboardToolbarComponent", "MatSidenavModule", "ResponsiveService", "DashboardMenuService", "MatDividerModule", "MatIconModule", "DashboardMenuLinksPanelComponent", "MatButtonModule", "MatCardModule", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "dashboardPanel", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "DashboardComponent_div_11_Template_button_click_6_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "refreshPage", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r3", "message", "DashboardComponent", "constructor", "auth", "sharedData", "cd", "notify", "sessionTimeoutService", "api", "dashboardMenuService", "isReady", "showBanner", "versionNumber", "user", "getCurrentUser", "userRole", "getCurrRole", "sendVersionNumber", "getRolesList", "tenantId", "subscribe", "data", "checkMapping", "logoUrl", "tenantDetails", "logo", "sendTimeOutData", "start", "detectChanges", "showSidenav$", "sidenavType$", "showSmallDeviceMenuButton$", "ngOnInit", "JSON", "parse", "sessionStorage", "getItem", "access", "lowercasedRoles", "map", "role", "toLowerCase", "lowercasedData", "includes", "checkSetting", "lowercasedUpload", "lowercasedUploadData", "checkUploads", "toggleMenu", "generateLinks", "module", "links", "label", "routerLink", "onCtrlShiftR", "event", "preventDefault", "window", "location", "reload", "KeyboardEvent", "key", "code", "ctrl<PERSON>ey", "shift<PERSON>ey", "document", "dispatchEvent", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ShareDataService", "ChangeDetectorRef", "i3", "NotificationService", "i4", "TimeOutService", "i5", "InventoryService", "selectors", "hostBindings", "DashboardComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument", "provide", "useFactory", "responsiveService", "deps", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "DashboardComponent_Template_mat_sidenav_container_backdropClick_0_listener", "ɵɵtemplate", "DashboardComponent_app_dashboard_menu_links_panel_6_Template", "DashboardComponent_Template_app_dashboard_toolbar_toggleMenu_8_listener", "DashboardComponent_div_10_Template", "DashboardComponent_div_11_Template", "ɵɵpipeBind1", "i6", "NgIf", "AsyncPipe", "i7", "<PERSON><PERSON><PERSON><PERSON>", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i8", "MatCard", "i9", "MatButton", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, OnInit, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterOutlet } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { FixedFabButtonComponent } from 'src/app/components/fixed-fab-button/fixed-fab-button.component';\nimport { DashboardMenuComponent } from 'src/app/components/dashboard-menu/dashboard-menu.component';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { DashboardMenuLinksPanelComponent } from 'src/app/components/dashboard-menu-links-panel/dashboard-menu-links-panel.component';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { TimeOutService } from 'src/app/services/time-out.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatDividerModule,\n    MatIconModule,\n    MatSidenavModule,\n    DashboardMenuComponent,\n    DashboardMenuLinksPanelComponent,\n    FixedFabButtonComponent,\n    DashboardToolbarComponent,\n    MatCardModule,\n    MatButtonModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {\n      provide: DashboardMenuService,\n      useFactory: (responsiveService: ResponsiveService) => {\n        return new DashboardMenuService(responsiveService);\n      },\n      deps: [ResponsiveService],\n    },\n  ],\n})\nexport class DashboardComponent implements OnInit {\n  private dashboardMenuService = inject(DashboardMenuService);\n  public isReady = false;\n  public dashboardPanel: {\n    icon: string;\n    title: string;\n    links: { label: string; routerLink: string }[];\n  }[] = [];\n  public user: any;\n  public userRole: any;\n  public access: any;\n  @Input() showBanner: boolean = false;\n  @Input() message: string = 'A new version of our software is now available!';\n  versionNumber: string = 'v2.13.0';\n  logoUrl: string\n  constructor(\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService,\n    private sessionTimeoutService: TimeOutService,\n    private api: InventoryService,\n\n ) {\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber)\n    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {\n      this.sharedData.checkMapping(data['bulkMapping'])\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']){\n        this.showBanner = true\n        this.sharedData.sendVersionNumber(this.versionNumber)\n      }else{\n        this.showBanner = false\n      }\n      if(data['result'] === 'success'){\n        this.sharedData.sendTimeOutData(600)\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges()\n    });\n\n  }\n\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n\n  ngOnInit(): void {\n      let data = JSON.parse(sessionStorage.getItem(('access')))\n      if(data['settings'] || (this.access && this.access['settings'])){\n        const lowercasedRoles = data['settings'] ?? this.access['settings'].map(role => role.toLowerCase());\n        const lowercasedData = this.user.role.toLowerCase();\n        if (lowercasedRoles.includes(lowercasedData)) {\n          this.sharedData.checkSetting(true)\n        } else {\n          this.sharedData.checkSetting(false)\n        }\n      }else{\n        this.sharedData.checkSetting(false)\n      }\n\n      if((data && data['bulkExcel']) || (this.access && this.access['bulkExcel'])){\n        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map(role => role.toLowerCase());\n        const lowercasedUploadData = this.user.role.toLowerCase();\n\n        if (lowercasedUpload.includes(lowercasedUploadData)) {\n          this.sharedData.checkUploads(true)\n        } else {\n          this.sharedData.checkUploads(false)\n        }\n      }else{\n        this.sharedData.checkUploads(false)\n      }\n    this.isReady = true;\n  }\n\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n\n  generateLinks(module) {\n    const links = module.map(label => {\n      return { label, routerLink: `/dashboard/${label}` };\n    });\n    return links;\n  }\n\n  @HostListener('document:keydown.control.shift.r', ['$event'])\n  onCtrlShiftR(event: KeyboardEvent) {\n    event.preventDefault();\n    window.location.reload();\n  }\n\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n}\n\n\n", "<!-- <mat-sidenav-container (backdropClick)=\"toggleMenu()\">\n  <mat-sidenav #sidenav [opened]=\"showSidenav$|async\" [mode]=\"(sidenavType$|async)!\" [disableClose]=\"true\">\n    <div class=\"sidenav-content\">\n      <app-dashboard-menu></app-dashboard-menu>\n    </div>\n  </mat-sidenav>\n  <mat-sidenav-content>\n    <div class=\"sidenav-content-container\">\n      <app-dashboard-toolbar (toggleMenu)=\"toggleMenu()\"\n        [showNavbarToggleButton]=\"(showSmallDeviceMenuButton$|async)!\"></app-dashboard-toolbar>\n      <router-outlet></router-outlet>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container> -->\n\n\n<!-- <mat-toolbar class=\"mat-elevation-z8\">\n  <button mat-icon-button (click)=\"sidenav.toggle()\">\n    <mat-icon> menu </mat-icon>\n  </button>\n  Digitory\n</mat-toolbar> -->\n\n\n<mat-sidenav-container (backdropClick)=\"toggleMenu()\">\n  <mat-sidenav #sidenav [opened]=\"showSidenav$|async\" [mode]=\"(sidenavType$|async)!\" [disableClose]=\"true\" class=\"minimized-sidenav\">\n    <div class=\"sidenav-icons-container\">\n      <app-dashboard-menu-links-panel [dashboardPanel]=\"dashboardPanel\" [minimized]=\"true\" *ngIf=\"!showBanner\"></app-dashboard-menu-links-panel>\n    </div>\n  </mat-sidenav>\n  <mat-sidenav-content>\n    <app-dashboard-toolbar\n      (toggleMenu)=\"toggleMenu()\"\n      [showNavbarToggleButton]=\"(showSmallDeviceMenuButton$|async)!\"\n      [logoUrl]=\"logoUrl\">\n    </app-dashboard-toolbar>\n    <div class=\"content mat-elevation-z8\" *ngIf=\"!showBanner\">\n      <router-outlet></router-outlet>\n    </div>\n\n    <div class=\"closingContainer\" *ngIf=\"showBanner\">\n      <div class=\"closingContainerDatas\">\n          <mat-card >\n              <div class=\"closeMsg\">\n                  {{message}}\n              </div>\n              <!-- <div class=\"closeMsgBtn mt-2\">\n                  <img src=\"../../assets/cache.png\" alt=\"cache\">\n              </div> -->\n              <div class=\"text-align-center text-center m-3\">\n                  <button mat-button mat-raised-button (click)=\"refreshPage()\">\n                   click to update </button>\n              </div>\n          </mat-card>\n      </div>\n    </div>\n\n  </mat-sidenav-content>\n</mat-sidenav-container>"], "mappings": "AAAA,SAA6FA,MAAM,QAAQ,eAAe;AAC1H,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,yBAAyB,QAAQ,kEAAkE;AAG5G,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gCAAgC,QAAQ,oFAAoF;AAIrI,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;ICWhDC,EAAA,CAAAC,SAAA,wCAA0I;;;;IAA1GD,EAAA,CAAAE,UAAA,mBAAAC,MAAA,CAAAC,cAAA,CAAiC;;;;;IASnEJ,EAAA,CAAAK,cAAA,aAA0D;IACxDL,EAAA,CAAAC,SAAA,oBAA+B;IACjCD,EAAA,CAAAM,YAAA,EAAM;;;;;;IAENN,EAAA,CAAAK,cAAA,cAAiD;IAInCL,EAAA,CAAAO,MAAA,GACJ;IAAAP,EAAA,CAAAM,YAAA,EAAM;IAINN,EAAA,CAAAK,cAAA,cAA+C;IACNL,EAAA,CAAAQ,UAAA,mBAAAC,2DAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC3Df,EAAA,CAAAO,MAAA,wBAAgB;IAAAP,EAAA,CAAAM,YAAA,EAAS;;;;IAP1BN,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAiB,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACJ;;;AD1Bd,MA6BaC,kBAAkB;EAe7BC,YACUC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B,EAC3BC,qBAAqC,EACrCC,GAAqB;IALrB,KAAAL,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IApBL,KAAAC,oBAAoB,GAAGxC,MAAM,CAACM,oBAAoB,CAAC;IACpD,KAAAmC,OAAO,GAAG,KAAK;IACf,KAAAzB,cAAc,GAIf,EAAE;IAIC,KAAA0B,UAAU,GAAY,KAAK;IAC3B,KAAAX,OAAO,GAAW,iDAAiD;IAC5E,KAAAY,aAAa,GAAW,SAAS;IAW/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,IAAI,CAACW,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACZ,IAAI,CAACa,WAAW,EAAE;IACvC,IAAI,CAACZ,UAAU,CAACa,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;IACrD,IAAI,CAACT,IAAI,CAACe,YAAY,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACN,IAAI,CAACM;IAAQ,CAAE,CAAC,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC1E,IAAI,CAACjB,UAAU,CAACkB,YAAY,CAACD,IAAI,CAAC,aAAa,CAAC,CAAC;MACjD,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACG,aAAa,CAACC,IAAI;MACtC,IAAI,IAAI,CAACb,aAAa,KAAKS,IAAI,CAAC,WAAW,CAAC,EAAC;QAC3C,IAAI,CAACV,UAAU,GAAG,IAAI;QACtB,IAAI,CAACP,UAAU,CAACa,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;OACtD,MAAI;QACH,IAAI,CAACD,UAAU,GAAG,KAAK;;MAEzB,IAAGU,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAC;QAC9B,IAAI,CAACjB,UAAU,CAACsB,eAAe,CAAC,GAAG,CAAC;QACpC,IAAI,CAACnB,qBAAqB,CAACoB,KAAK,EAAE;;MAEpC,IAAI,CAACtB,EAAE,CAACuB,aAAa,EAAE;IACzB,CAAC,CAAC;EAEJ;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACpB,oBAAoB,CAACoB,YAAY;EAC/C;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACrB,oBAAoB,CAACqB,YAAY;EAC/C;EAEA,IAAIC,0BAA0BA,CAAA;IAC5B,OAAO,IAAI,CAACtB,oBAAoB,CAACsB,0BAA0B;EAC7D;EAEAC,QAAQA,CAAA;IACJ,IAAIX,IAAI,GAAGY,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAE,QAAS,CAAC,CAAC;IACzD,IAAGf,IAAI,CAAC,UAAU,CAAC,IAAK,IAAI,CAACgB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,UAAU,CAAE,EAAC;MAC9D,MAAMC,eAAe,GAAGjB,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAACgB,MAAM,CAAC,UAAU,CAAC,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,EAAE,CAAC;MACnG,MAAMC,cAAc,GAAG,IAAI,CAAC7B,IAAI,CAAC2B,IAAI,CAACC,WAAW,EAAE;MACnD,IAAIH,eAAe,CAACK,QAAQ,CAACD,cAAc,CAAC,EAAE;QAC5C,IAAI,CAACtC,UAAU,CAACwC,YAAY,CAAC,IAAI,CAAC;OACnC,MAAM;QACL,IAAI,CAACxC,UAAU,CAACwC,YAAY,CAAC,KAAK,CAAC;;KAEtC,MAAI;MACH,IAAI,CAACxC,UAAU,CAACwC,YAAY,CAAC,KAAK,CAAC;;IAGrC,IAAIvB,IAAI,IAAIA,IAAI,CAAC,WAAW,CAAC,IAAM,IAAI,CAACgB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,WAAW,CAAE,EAAC;MAC1E,MAAMQ,gBAAgB,GAAGxB,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAACgB,MAAM,CAAC,WAAW,CAAC,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,EAAE,CAAC;MACtG,MAAMK,oBAAoB,GAAG,IAAI,CAACjC,IAAI,CAAC2B,IAAI,CAACC,WAAW,EAAE;MAEzD,IAAII,gBAAgB,CAACF,QAAQ,CAACG,oBAAoB,CAAC,EAAE;QACnD,IAAI,CAAC1C,UAAU,CAAC2C,YAAY,CAAC,IAAI,CAAC;OACnC,MAAM;QACL,IAAI,CAAC3C,UAAU,CAAC2C,YAAY,CAAC,KAAK,CAAC;;KAEtC,MAAI;MACH,IAAI,CAAC3C,UAAU,CAAC2C,YAAY,CAAC,KAAK,CAAC;;IAEvC,IAAI,CAACrC,OAAO,GAAG,IAAI;EACrB;EAEAsC,UAAUA,CAAA;IACR,IAAI,CAACvC,oBAAoB,CAACuC,UAAU,EAAE;EACxC;EAEAC,aAAaA,CAACC,MAAM;IAClB,MAAMC,KAAK,GAAGD,MAAM,CAACX,GAAG,CAACa,KAAK,IAAG;MAC/B,OAAO;QAAEA,KAAK;QAAEC,UAAU,EAAE,cAAcD,KAAK;MAAE,CAAE;IACrD,CAAC,CAAC;IACF,OAAOD,KAAK;EACd;EAGAG,YAAYA,CAACC,KAAoB;IAC/BA,KAAK,CAACC,cAAc,EAAE;IACtBC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEA/D,WAAWA,CAAA;IACT,MAAM2D,KAAK,GAAG,IAAIK,aAAa,CAAC,SAAS,EAAE;MACzCC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX,CAAC;IACFC,QAAQ,CAACC,aAAa,CAACX,KAAK,CAAC;EAC/B;;;uBA/GWtD,kBAAkB,EAAApB,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1F,EAAA,CAAAsF,iBAAA,CAAAtF,EAAA,CAAA2F,iBAAA,GAAA3F,EAAA,CAAAsF,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAA7F,EAAA,CAAAsF,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA/F,EAAA,CAAAsF,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAlB7E,kBAAkB;MAAA8E,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAlBC,GAAA,CAAA7B,YAAA,CAAA8B,MAAA,CAAoB;UAAA,UAAAvG,EAAA,CAAAwG,iBAAA;;;;;;;;uCAVpB,CACT;QACEC,OAAO,EAAE/G,oBAAoB;QAC7BgH,UAAU,EAAGC,iBAAoC,IAAI;UACnD,OAAO,IAAIjH,oBAAoB,CAACiH,iBAAiB,CAAC;QACpD,CAAC;QACDC,IAAI,EAAE,CAACnH,iBAAiB;OACzB,CACF,GAAAO,EAAA,CAAA6G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBHrG,EAAA,CAAAK,cAAA,+BAAsD;UAA/BL,EAAA,CAAAQ,UAAA,2BAAA2G,2EAAA;YAAA,OAAiBb,GAAA,CAAAnC,UAAA,EAAY;UAAA,EAAC;UACnDnE,EAAA,CAAAK,cAAA,wBAAmI;;;UACjIL,EAAA,CAAAK,cAAA,aAAqC;UACnCL,EAAA,CAAAoH,UAAA,IAAAC,4DAAA,4CAA0I;UAC5IrH,EAAA,CAAAM,YAAA,EAAM;UAERN,EAAA,CAAAK,cAAA,0BAAqB;UAEjBL,EAAA,CAAAQ,UAAA,wBAAA8G,wEAAA;YAAA,OAAchB,GAAA,CAAAnC,UAAA,EAAY;UAAA,EAAC;;UAG7BnE,EAAA,CAAAM,YAAA,EAAwB;UACxBN,EAAA,CAAAoH,UAAA,KAAAG,kCAAA,iBAEM;UAENvH,EAAA,CAAAoH,UAAA,KAAAI,kCAAA,iBAeM;UAERxH,EAAA,CAAAM,YAAA,EAAsB;;;UAhCAN,EAAA,CAAAgB,SAAA,GAA6B;UAA7BhB,EAAA,CAAAE,UAAA,WAAAF,EAAA,CAAAyH,WAAA,OAAAnB,GAAA,CAAAtD,YAAA,EAA6B,SAAAhD,EAAA,CAAAyH,WAAA,QAAAnB,GAAA,CAAArD,YAAA;UAEuCjD,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAE,UAAA,UAAAoG,GAAA,CAAAxE,UAAA,CAAiB;UAMvG9B,EAAA,CAAAgB,SAAA,GAA8D;UAA9DhB,EAAA,CAAAE,UAAA,2BAAAF,EAAA,CAAAyH,WAAA,QAAAnB,GAAA,CAAApD,0BAAA,EAA8D,YAAAoD,GAAA,CAAA5D,OAAA;UAGzB1C,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAE,UAAA,UAAAoG,GAAA,CAAAxE,UAAA,CAAiB;UAIzB9B,EAAA,CAAAgB,SAAA,GAAgB;UAAhBhB,EAAA,CAAAE,UAAA,SAAAoG,GAAA,CAAAxE,UAAA,CAAgB;;;qBDjB/CzC,YAAY,EAAAqI,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,SAAA,EACZtI,YAAY,EACZK,gBAAgB,EAChBC,aAAa,EACbJ,gBAAgB,EAAAqI,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,iBAAA,EAEhBnI,gCAAgC,EAEhCN,yBAAyB,EACzBQ,aAAa,EAAAkI,EAAA,CAAAC,OAAA,EACbpI,eAAe,EAAAqI,EAAA,CAAAC,SAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAeNlH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}