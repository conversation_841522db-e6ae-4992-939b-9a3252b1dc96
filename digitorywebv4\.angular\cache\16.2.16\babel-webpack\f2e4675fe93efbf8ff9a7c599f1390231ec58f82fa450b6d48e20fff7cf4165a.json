{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nclass MarkdownPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.initializeRenderer();\n  }\n  initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n    // Base font size for better readability\n    const baseFontSize = '0.95em';\n    const baseMargin = '0.6em';\n    // Paragraph with slightly reduced spacing\n    this.markedRenderer.paragraph = text => `<p style=\"margin-bottom: ${baseMargin}; font-size: ${baseFontSize};\">${text}</p>`;\n    // Lists with proper spacing and bullet styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = ordered && start !== 1 ? ` start=\"${start}\"` : '';\n      return `<${type}${startAttr} style=\"margin-bottom: ${baseMargin}; padding-left: 1.8em; font-size: ${baseFontSize};\">${body}</${type}>`;\n    };\n    // List items with reduced spacing\n    this.markedRenderer.listitem = text => {\n      return `<li style=\"margin-bottom: 0.3em;\">${text}</li>`;\n    };\n    // Code blocks with slightly reduced padding\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 0.8em; border-radius: 4px; margin: 0.8em 0; font-size: ${baseFontSize};\"><code class=\"language-${language || 'text'}\">${code}</code></pre>`;\n    };\n    // Inline code styling\n    this.markedRenderer.codespan = code => {\n      return `<code style=\"background-color: #f5f5f5; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.95em;\">${code}</code>`;\n    };\n    // Headings with adjusted spacing and sizes\n    this.markedRenderer.heading = (text, level) => {\n      const fontSize = 1.4 - level * 0.1;\n      return `<h${level} style=\"margin-top: 0.9em; margin-bottom: 0.4em; font-weight: 600; font-size: ${fontSize}em;\">${text}</h${level}>`;\n    };\n    // Blockquote styling\n    this.markedRenderer.blockquote = quote => {\n      return `<blockquote style=\"border-left: 4px solid #ddd; padding-left: 1em; margin-left: 0; margin-right: 0; margin-bottom: ${baseMargin}; font-size: ${baseFontSize}; color: #555;\">${quote}</blockquote>`;\n    };\n    // Horizontal rule\n    this.markedRenderer.hr = () => {\n      return `<hr style=\"border: 0; border-top: 1px solid #ddd; margin: 1em 0;\">`;\n    };\n    // Strong text\n    this.markedRenderer.strong = text => {\n      return `<strong style=\"font-weight: 600;\">${text}</strong>`;\n    };\n    // Emphasis text\n    this.markedRenderer.em = text => {\n      return `<em style=\"font-style: italic;\">${text}</em>`;\n    };\n    // Links\n    this.markedRenderer.link = (href, title, text) => {\n      const titleAttr = title ? ` title=\"${title}\"` : '';\n      return `<a href=\"${href}\"${titleAttr} style=\"color: #0366d6; text-decoration: none;\">${text}</a>`;\n    };\n    // Table with improved styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: ${baseMargin}; border: 1px solid #ddd; font-size: ${baseFontSize};\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n    // Table rows\n    this.markedRenderer.tablerow = content => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n    // Table cells\n    this.markedRenderer.tablecell = (content, {\n      header,\n      align\n    }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 6px 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true,\n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false\n    });\n  }\n  transform(value) {\n    if (!value) {\n      return '';\n    }\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n      const html = marked(preprocessedMarkdown);\n      const processedHtml = html.replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n      .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n      .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n      .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n      .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n      .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n      // Preserve table structure\n      .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (_match, tag, attrs, content) => {\n        // Skip this replacement for table cells to preserve alignment attributes\n        if (tag === 'th' || tag === 'td') {\n          return _match;\n        }\n        const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n        return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n      });\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      const safeText = value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n  preprocessMarkdown(text) {\n    if (!text) return '';\n    let processed = text.replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2').replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2').replace(/```(\\w*)\\s*\\n/g, '```$1\\n').replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```').replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n').replace(/(\\n|^)([-*+]\\s.+?)\\n(?![-*+]\\s|\\n)/g, '$1$2\\n\\n')\n    // Don't apply paragraph spacing to table rows\n    .replace(/(\\n|^)([^\\n#>*+\\-|].+?)\\n(?!\\n|#|>|\\*|\\+|\\-|\\|)/g, '$1$2\\n\\n').replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>').replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n    return processed;\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport { MarkdownPipe };", "map": {"version": 3, "names": ["marked", "MarkdownPipe", "constructor", "sanitizer", "initialize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "baseFontSize", "base<PERSON><PERSON>gin", "paragraph", "text", "list", "body", "ordered", "start", "type", "startAttr", "listitem", "code", "language", "codespan", "heading", "level", "fontSize", "blockquote", "quote", "hr", "strong", "em", "link", "href", "title", "titleAttr", "table", "header", "tablerow", "content", "tablecell", "align", "tag", "style", "setOptions", "renderer", "gfm", "breaks", "pedantic", "smartLists", "smartypants", "xhtml", "headerIds", "transform", "value", "hasCursor", "includes", "textWithoutCursor", "replace", "preprocessedMarkdown", "preprocessMarkdown", "html", "processedHtml", "_match", "attrs", "cleanAttrs", "finalHtml", "bypassSecurityTrustHtml", "error", "console", "safeText", "processed", "codeBlockMatches", "match", "length", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pipes/markdown.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { marked } from 'marked';\n\n@Pipe({\n  name: 'markdown',\n  standalone: true\n})\nexport class MarkdownPipe implements PipeTransform {\n  private markedRenderer: marked.Renderer;\n\n  constructor(private sanitizer: DomSanitizer) {\n    this.initializeRenderer();\n  }\n\n  private initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n\n    // Base font size for better readability\n    const baseFontSize = '0.95em';\n    const baseMargin = '0.6em';\n\n    // Paragraph with slightly reduced spacing\n    this.markedRenderer.paragraph = (text) => `<p style=\"margin-bottom: ${baseMargin}; font-size: ${baseFontSize};\">${text}</p>`;\n\n    // Lists with proper spacing and bullet styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = (ordered && start !== 1) ? ` start=\"${start}\"` : '';\n      return `<${type}${startAttr} style=\"margin-bottom: ${baseMargin}; padding-left: 1.8em; font-size: ${baseFontSize};\">${body}</${type}>`;\n    };\n\n    // List items with reduced spacing\n    this.markedRenderer.listitem = (text) => {\n      return `<li style=\"margin-bottom: 0.3em;\">${text}</li>`;\n    };\n\n    // Code blocks with slightly reduced padding\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 0.8em; border-radius: 4px; margin: 0.8em 0; font-size: ${baseFontSize};\"><code class=\"language-${language || 'text'}\">${code}</code></pre>`;\n    };\n\n    // Inline code styling\n    this.markedRenderer.codespan = (code) => {\n      return `<code style=\"background-color: #f5f5f5; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.95em;\">${code}</code>`;\n    };\n\n    // Headings with adjusted spacing and sizes\n    this.markedRenderer.heading = (text, level) => {\n      const fontSize = 1.4 - (level * 0.1);\n      return `<h${level} style=\"margin-top: 0.9em; margin-bottom: 0.4em; font-weight: 600; font-size: ${fontSize}em;\">${text}</h${level}>`;\n    };\n\n    // Blockquote styling\n    this.markedRenderer.blockquote = (quote) => {\n      return `<blockquote style=\"border-left: 4px solid #ddd; padding-left: 1em; margin-left: 0; margin-right: 0; margin-bottom: ${baseMargin}; font-size: ${baseFontSize}; color: #555;\">${quote}</blockquote>`;\n    };\n\n    // Horizontal rule\n    this.markedRenderer.hr = () => {\n      return `<hr style=\"border: 0; border-top: 1px solid #ddd; margin: 1em 0;\">`;\n    };\n\n    // Strong text\n    this.markedRenderer.strong = (text) => {\n      return `<strong style=\"font-weight: 600;\">${text}</strong>`;\n    };\n\n    // Emphasis text\n    this.markedRenderer.em = (text) => {\n      return `<em style=\"font-style: italic;\">${text}</em>`;\n    };\n\n    // Links\n    this.markedRenderer.link = (href, title, text) => {\n      const titleAttr = title ? ` title=\"${title}\"` : '';\n      return `<a href=\"${href}\"${titleAttr} style=\"color: #0366d6; text-decoration: none;\">${text}</a>`;\n    };\n\n    // Table with improved styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: ${baseMargin}; border: 1px solid #ddd; font-size: ${baseFontSize};\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n\n    // Table rows\n    this.markedRenderer.tablerow = (content) => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n\n    // Table cells\n    this.markedRenderer.tablecell = (content, { header, align }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 6px 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true,\n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false\n    });\n  }\n\n  transform(value: string): SafeHtml {\n    if (!value) {\n      return '';\n    }\n\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n      const html = marked(preprocessedMarkdown);\n      const processedHtml = html\n        .replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n        .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n        .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n        .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n        .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n        .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n        // Preserve table structure\n        .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (_match, tag, attrs, content) => {\n          // Skip this replacement for table cells to preserve alignment attributes\n          if (tag === 'th' || tag === 'td') {\n            return _match;\n          }\n          const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n          return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n        });\n\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      const safeText = value\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n\n\n  private preprocessMarkdown(text: string): string {\n    if (!text) return '';\n\n    let processed = text\n      .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n      .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n      .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n      .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```')\n      .replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n')\n      .replace(/(\\n|^)([-*+]\\s.+?)\\n(?![-*+]\\s|\\n)/g, '$1$2\\n\\n')\n      // Don't apply paragraph spacing to table rows\n      .replace(/(\\n|^)([^\\n#>*+\\-|].+?)\\n(?!\\n|#|>|\\*|\\+|\\-|\\|)/g, '$1$2\\n\\n')\n      .replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>')\n      .replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n\n    return processed;\n  }\n}\n"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;AAE/B,MAIaC,YAAY;EAGvBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIL,MAAM,CAACM,QAAQ,EAAE;IAE3C;IACA,MAAMC,YAAY,GAAG,QAAQ;IAC7B,MAAMC,UAAU,GAAG,OAAO;IAE1B;IACA,IAAI,CAACH,cAAc,CAACI,SAAS,GAAIC,IAAI,IAAK,4BAA4BF,UAAU,gBAAgBD,YAAY,MAAMG,IAAI,MAAM;IAE5H;IACA,IAAI,CAACL,cAAc,CAACM,IAAI,GAAG,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,KAAI;MAClD,MAAMC,IAAI,GAAGF,OAAO,GAAG,IAAI,GAAG,IAAI;MAClC,MAAMG,SAAS,GAAIH,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAI,WAAWA,KAAK,GAAG,GAAG,EAAE;MACrE,OAAO,IAAIC,IAAI,GAAGC,SAAS,0BAA0BR,UAAU,qCAAqCD,YAAY,MAAMK,IAAI,KAAKG,IAAI,GAAG;IACxI,CAAC;IAED;IACA,IAAI,CAACV,cAAc,CAACY,QAAQ,GAAIP,IAAI,IAAI;MACtC,OAAO,qCAAqCA,IAAI,OAAO;IACzD,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAACa,IAAI,GAAG,CAACA,IAAI,EAAEC,QAAQ,KAAI;MAC5C,OAAO,uNAAuNZ,YAAY,4BAA4BY,QAAQ,IAAI,MAAM,KAAKD,IAAI,eAAe;IAClT,CAAC;IAED;IACA,IAAI,CAACb,cAAc,CAACe,QAAQ,GAAIF,IAAI,IAAI;MACtC,OAAO,yGAAyGA,IAAI,SAAS;IAC/H,CAAC;IAED;IACA,IAAI,CAACb,cAAc,CAACgB,OAAO,GAAG,CAACX,IAAI,EAAEY,KAAK,KAAI;MAC5C,MAAMC,QAAQ,GAAG,GAAG,GAAID,KAAK,GAAG,GAAI;MACpC,OAAO,KAAKA,KAAK,iFAAiFC,QAAQ,QAAQb,IAAI,MAAMY,KAAK,GAAG;IACtI,CAAC;IAED;IACA,IAAI,CAACjB,cAAc,CAACmB,UAAU,GAAIC,KAAK,IAAI;MACzC,OAAO,sHAAsHjB,UAAU,gBAAgBD,YAAY,mBAAmBkB,KAAK,eAAe;IAC5M,CAAC;IAED;IACA,IAAI,CAACpB,cAAc,CAACqB,EAAE,GAAG,MAAK;MAC5B,OAAO,oEAAoE;IAC7E,CAAC;IAED;IACA,IAAI,CAACrB,cAAc,CAACsB,MAAM,GAAIjB,IAAI,IAAI;MACpC,OAAO,qCAAqCA,IAAI,WAAW;IAC7D,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAACuB,EAAE,GAAIlB,IAAI,IAAI;MAChC,OAAO,mCAAmCA,IAAI,OAAO;IACvD,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAACwB,IAAI,GAAG,CAACC,IAAI,EAAEC,KAAK,EAAErB,IAAI,KAAI;MAC/C,MAAMsB,SAAS,GAAGD,KAAK,GAAG,WAAWA,KAAK,GAAG,GAAG,EAAE;MAClD,OAAO,YAAYD,IAAI,IAAIE,SAAS,mDAAmDtB,IAAI,MAAM;IACnG,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAAC4B,KAAK,GAAG,CAACC,MAAM,EAAEtB,IAAI,KAAI;MAC3C,OAAO,wEAAwEJ,UAAU,wCAAwCD,YAAY;oDAC/F2B,MAAM;iBACzCtB,IAAI;eACN;IACX,CAAC;IAED;IACA,IAAI,CAACP,cAAc,CAAC8B,QAAQ,GAAIC,OAAO,IAAI;MACzC,OAAO,8CAA8CA,OAAO,OAAO;IACrE,CAAC;IAED;IACA,IAAI,CAAC/B,cAAc,CAACgC,SAAS,GAAG,CAACD,OAAO,EAAE;MAAEF,MAAM;MAAEI;IAAK,CAAE,KAAI;MAC7D,MAAMC,GAAG,GAAGL,MAAM,GAAG,IAAI,GAAG,IAAI;MAChC,MAAMM,KAAK,GAAG,iCAAiCF,KAAK,IAAI,MAAM,iCAAiC;MAC/F,OAAO,IAAIC,GAAG,WAAWC,KAAK,KAAKJ,OAAO,KAAKG,GAAG,GAAG;IACvD,CAAC;IAEDvC,MAAM,CAACyC,UAAU,CAAC;MAChBC,QAAQ,EAAE,IAAI,CAACrC,cAAc;MAC7BsC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX,IAAI;MACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,QAAQ,CAAC,wCAAwC,CAAC;MAC1E,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,OAAO,CAAC,2CAA2C,EAAE,EAAE,CAAC;MACxF,MAAMC,oBAAoB,GAAG,IAAI,CAACC,kBAAkB,CAACH,iBAAiB,CAAC;MACvE,MAAMI,IAAI,GAAG1D,MAAM,CAACwD,oBAAoB,CAAC;MACzC,MAAMG,aAAa,GAAGD,IAAI,CACvBH,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;MAAA,CACnCA,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;MAAA,CACvCA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;MAAA,CACzBA,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;MAC9B;MAAA,CACCA,OAAO,CAAC,iCAAiC,EAAE,CAACK,MAAM,EAAErB,GAAG,EAAEsB,KAAK,EAAEzB,OAAO,KAAI;QAC1E;QACA,IAAIG,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,IAAI,EAAE;UAChC,OAAOqB,MAAM;;QAEf,MAAME,UAAU,GAAGD,KAAK,CAACN,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC;QAC7F,OAAO,IAAIhB,GAAG,GAAGuB,UAAU,IAAI1B,OAAO,KAAKG,GAAG,GAAG;MACnD,CAAC,CAAC;MAEJ,MAAMwB,SAAS,GAAGX,SAAS,GAAGO,aAAa,GAAG,wCAAwC,GAAGA,aAAa;MAEtG,OAAO,IAAI,CAACxD,SAAS,CAAC6D,uBAAuB,CAACD,SAAS,CAAC;KACzD,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAME,QAAQ,GAAGhB,KAAK,CACnBI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACzB,OAAO,IAAI,CAACpD,SAAS,CAAC6D,uBAAuB,CAACG,QAAQ,CAAC;;EAE3D;EAGQV,kBAAkBA,CAAC/C,IAAY;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI0D,SAAS,GAAG1D,IAAI,CACjB6C,OAAO,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAC3CA,OAAO,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAC7CA,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CACpCA,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CACjDA,OAAO,CAAC,8BAA8B,EAAE,UAAU,CAAC,CACnDA,OAAO,CAAC,qCAAqC,EAAE,UAAU;IAC1D;IAAA,CACCA,OAAO,CAAC,kDAAkD,EAAE,UAAU,CAAC,CACvEA,OAAO,CAAC,sBAAsB,EAAE,8BAA8B,CAAC,CAC/DA,OAAO,CAAC,+BAA+B,EAAE,YAAY,CAAC;IAEzD,MAAMc,gBAAgB,GAAGD,SAAS,CAACE,KAAK,CAAC,YAAY,CAAC;IACtD,IAAID,gBAAgB,IAAIA,gBAAgB,CAACE,MAAM,GAAG,CAAC,EAAE;MACnDH,SAAS,IAAI,OAAO;;IAGtBA,SAAS,GAAGA,SAAS,CAACb,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IAEhD,OAAOa,SAAS;EAClB;;;uBAvKWnE,YAAY,EAAAuE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAAZ1E,YAAY;MAAA2E,IAAA;MAAAC,UAAA;IAAA;EAAA;;SAAZ5E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}