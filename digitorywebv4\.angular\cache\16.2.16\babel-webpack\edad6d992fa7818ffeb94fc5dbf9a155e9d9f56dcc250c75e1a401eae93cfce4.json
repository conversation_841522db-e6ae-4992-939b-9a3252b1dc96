{"ast": null, "code": "import { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/share-data.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"src/app/services/time-out.service\";\nimport * as i5 from \"src/app/services/inventory.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/services/loading.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/button\";\nfunction DashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\", 8);\n    i0.ɵɵtext(3, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Loading application...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"mat-card\")(3, \"div\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshPage());\n    });\n    i0.ɵɵtext(7, \" click to update \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nclass DashboardComponent {\n  constructor(auth, sharedData, cd, notify, sessionTimeoutService, api, router, loadingService) {\n    this.auth = auth;\n    this.sharedData = sharedData;\n    this.cd = cd;\n    this.notify = notify;\n    this.sessionTimeoutService = sessionTimeoutService;\n    this.api = api;\n    this.router = router;\n    this.loadingService = loadingService;\n    this.dashboardMenuService = inject(DashboardMenuService);\n    this.isReady = false;\n    this.isLoading = true; // Global loading state for the application\n    this.dashboardPanel = [];\n    this.menuItems = [];\n    this.showBanner = false;\n    this.message = 'A new version of our software is now available!';\n    this.versionNumber = 'v2.13.0';\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber);\n    this.auth.getRolesList({\n      tenantId: this.user.tenantId\n    }).subscribe(data => {\n      this.sharedData.checkMapping(data['bulkMapping']);\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']) {\n        this.showBanner = true;\n        this.sharedData.sendVersionNumber(this.versionNumber);\n      } else {\n        this.showBanner = false;\n      }\n      if (data['result'] === 'success') {\n        this.sharedData.sendTimeOutData(600);\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges();\n    });\n  }\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n  ngOnInit() {\n    // Set initial loading states\n    this.isReady = false;\n    this.isLoading = true;\n    try {\n      // Safely parse access data from session storage\n      let data = {};\n      try {\n        const accessData = sessionStorage.getItem('access');\n        if (accessData) {\n          data = JSON.parse(accessData);\n        }\n      } catch (error) {\n        console.error('Error parsing access data:', error);\n      }\n      // Handle settings access\n      if (data['settings'] || this.access && this.access['settings']) {\n        const lowercasedRoles = data['settings'] ?? this.access['settings'].map(role => role.toLowerCase());\n        const lowercasedData = this.user.role.toLowerCase();\n        this.sharedData.checkSetting(lowercasedRoles.includes(lowercasedData));\n      } else {\n        this.sharedData.checkSetting(false);\n      }\n      // Handle bulk excel upload access\n      if (data && data['bulkExcel'] || this.access && this.access['bulkExcel']) {\n        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map(role => role.toLowerCase());\n        const lowercasedUploadData = this.user.role.toLowerCase();\n        this.sharedData.checkUploads(lowercasedUpload.includes(lowercasedUploadData));\n      } else {\n        this.sharedData.checkUploads(false);\n      }\n      // Get navigation menu items with a timeout to ensure they load\n      setTimeout(() => {\n        this.getNavigationItems();\n        // Set ready state but keep loading state active for child components\n        this.isReady = true;\n        // Create a loading service to track loading state across components\n        this.setupLoadingStateListener();\n        this.cd.detectChanges();\n      }, 100);\n    } catch (error) {\n      console.error('Error in dashboard initialization:', error);\n      // Ensure we still try to load navigation items even if there's an error\n      this.getNavigationItems();\n      // Set ready state and force change detection\n      this.isReady = true;\n      this.cd.detectChanges();\n    }\n  }\n  // Set up a listener for loading state changes\n  setupLoadingStateListener() {\n    // Subscribe to the loading service\n    this.loadingSubscription = this.loadingService.isLoading$.subscribe(isLoading => {\n      this.isLoading = isLoading;\n      this.cd.detectChanges();\n    });\n    // Listen for route changes to update loading state\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Set loading to true when navigation starts\n        this.isLoading = true;\n        this.cd.detectChanges();\n      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\n        // Set loading to false when navigation ends (success or error)\n        // Add a small delay to allow components to initialize\n        setTimeout(() => {\n          // Only set loading to false if there are no active API requests\n          if (!this.loadingService.isLoading$.value) {\n            this.isLoading = false;\n            this.cd.detectChanges();\n          }\n        }, 500);\n      }\n    });\n    // Set initial loading state to false after a delay\n    setTimeout(() => {\n      this.isLoading = false;\n      this.cd.detectChanges();\n    }, 1000);\n  }\n  // Clean up subscriptions when component is destroyed\n  ngOnDestroy() {\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n  getNavigationItems(retryCount = 0, maxRetries = 3) {\n    const ROUTES = [{\n      path: '/dashboard/inventory',\n      title: 'Inventory Management',\n      icon: 'table_chart',\n      class: '',\n      dbAccess: \"inventory\"\n    }, {\n      path: '/dashboard/user',\n      title: 'User Management',\n      icon: 'person',\n      class: '',\n      dbAccess: \"user\"\n    }, {\n      path: '/dashboard/recipe',\n      title: 'Recipe Management',\n      icon: 'fastfood',\n      class: '',\n      dbAccess: \"recipe\"\n    }, {\n      path: '/dashboard/party',\n      title: 'Party Management',\n      icon: 'event_note',\n      class: '',\n      dbAccess: \"party\"\n    }, {\n      path: '/dashboard/account',\n      title: 'Account Setup',\n      icon: 'add_to_photos',\n      class: '',\n      dbAccess: \"accountSetup\"\n    }];\n    // Clear menu items before loading to avoid duplicates on retry\n    this.menuItems = [];\n    if (this.user.tenantId != '100000') {\n      this.api.getUIAccess(this.user.tenantId).subscribe({\n        next: res => {\n          if (res['success']) {\n            this.access = res['access'];\n            ROUTES.forEach(el => {\n              if (this.access.hasOwnProperty(el['dbAccess'])) {\n                this.access[el['dbAccess']]['status'] === true && this.access[el['dbAccess']]['access'].map(access => access.toLowerCase()).includes(this.user.role.toLowerCase()) ? this.menuItems.push(el) : undefined;\n              }\n            });\n          } else {\n            this.access = {};\n          }\n          // Force change detection to update the UI\n          this.cd.detectChanges();\n          // If no menu items were loaded and we haven't exceeded max retries, try again\n          if (this.menuItems.length === 0 && retryCount < maxRetries) {\n            console.log(`Retrying navigation items load, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries);\n            }, 1000); // Wait 1 second before retrying\n          }\n        },\n\n        error: err => {\n          console.error('Error loading navigation items:', err);\n          // On error, if we haven't exceeded max retries, try again\n          if (retryCount < maxRetries) {\n            console.log(`Retrying navigation items load after error, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries);\n            }, 1000); // Wait 1 second before retrying\n          } else {\n            // If we've exceeded max retries, set default navigation\n            console.log('Max retries exceeded, setting default navigation');\n            this.setDefaultNavigation();\n            this.cd.detectChanges();\n          }\n        },\n        complete: () => {\n          // Ensure UI is updated when request completes\n          this.cd.detectChanges();\n        }\n      });\n    } else {\n      this.menuItems = [{\n        path: '/dashboard/account',\n        title: 'Account Setup',\n        icon: 'add_to_photos',\n        class: '',\n        dbAccess: \"accountSetup\"\n      }];\n      this.cd.detectChanges();\n    }\n  }\n  // Set default navigation items if API fails\n  setDefaultNavigation() {\n    this.menuItems = [{\n      path: '/dashboard/home',\n      title: 'Dashboard',\n      icon: 'dashboard',\n      class: ''\n    }];\n    // Add account setup for admin users\n    if (this.user.tenantId === '100000') {\n      this.menuItems.push({\n        path: '/dashboard/account',\n        title: 'Account Setup',\n        icon: 'add_to_photos',\n        class: ''\n      });\n    }\n  }\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n  generateLinks(module) {\n    const links = module.map(label => {\n      return {\n        label,\n        routerLink: `/dashboard/${label}`\n      };\n    });\n    return links;\n  }\n  onCtrlShiftR(event) {\n    event.preventDefault();\n    window.location.reload();\n  }\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.TimeOutService), i0.ɵɵdirectiveInject(i5.InventoryService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.LoadingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      hostBindings: function DashboardComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.control.shift.r\", function DashboardComponent_keydown_control_shift_r_HostBindingHandler($event) {\n            return ctx.onCtrlShiftR($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        showBanner: \"showBanner\",\n        message: \"message\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: DashboardMenuService,\n        useFactory: responsiveService => {\n          return new DashboardMenuService(responsiveService);\n        },\n        deps: [ResponsiveService]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 5,\n      consts: [[1, \"dashboard-container\"], [3, \"menuItems\", \"logoUrl\"], [\"class\", \"content mat-elevation-z8\", 4, \"ngIf\"], [\"class\", \"content mat-elevation-z8 loading-container\", 4, \"ngIf\"], [\"class\", \"closingContainer\", 4, \"ngIf\"], [1, \"content\", \"mat-elevation-z8\"], [1, \"content\", \"mat-elevation-z8\", \"loading-container\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"closingContainer\"], [1, \"closingContainerDatas\"], [1, \"closeMsg\"], [1, \"text-align-center\", \"text-center\", \"m-3\"], [\"mat-button\", \"\", \"mat-raised-button\", \"\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-dashboard-toolbar\", 1);\n          i0.ɵɵtemplate(2, DashboardComponent_div_2_Template, 2, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, DashboardComponent_div_3_Template, 6, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, DashboardComponent_div_4_Template, 8, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"menuItems\", ctx.menuItems)(\"logoUrl\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner && ctx.isReady);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showBanner && (!ctx.isReady || ctx.isLoading));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showBanner);\n        }\n      },\n      dependencies: [CommonModule, i8.NgIf, RouterOutlet, MatDividerModule, MatIconModule, i9.MatIcon, DashboardToolbarComponent, MatCardModule, i10.MatCard, MatButtonModule, i11.MatButton],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  height: calc(100vh - 64px);\\n  border-radius: 8px;\\n  margin: 10px;\\n  padding: 0.5rem;\\n  overflow: auto;\\n  background-color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: calc(100vh - 64px);\\n  position: fixed;\\n  top: 50px;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(255, 255, 255, 0.9);\\n  z-index: 1000;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: white;\\n  padding: 30px;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  height: 64px;\\n  width: 64px;\\n  color: #ff9100;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 18px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.closingContainer[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10vh;\\n  justify-content: center;\\n  min-height: 100vh;\\n}\\n\\n.closingContainerDatas[_ngcontent-%COMP%] {\\n  max-width: 85vw;\\n  pointer-events: auto;\\n  width: 550px;\\n  position: static;\\n}\\n\\n.closeMsg[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: large;\\n  font-weight: bold;\\n  padding-top: 2rem;\\n  padding-bottom: 1rem;\\n}\\n\\n.closeMsgBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { DashboardComponent };", "map": {"version": 3, "names": ["inject", "CommonModule", "RouterOutlet", "NavigationStart", "NavigationEnd", "NavigationCancel", "NavigationError", "DashboardToolbarComponent", "ResponsiveService", "DashboardMenuService", "MatDividerModule", "MatIconModule", "MatButtonModule", "MatCardModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "DashboardComponent_div_4_Template_button_click_6_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "refreshPage", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "message", "DashboardComponent", "constructor", "auth", "sharedData", "cd", "notify", "sessionTimeoutService", "api", "router", "loadingService", "dashboardMenuService", "isReady", "isLoading", "dashboardPanel", "menuItems", "showBanner", "versionNumber", "user", "getCurrentUser", "userRole", "getCurrRole", "sendVersionNumber", "getRolesList", "tenantId", "subscribe", "data", "checkMapping", "logoUrl", "tenantDetails", "logo", "sendTimeOutData", "start", "detectChanges", "showSidenav$", "sidenavType$", "showSmallDeviceMenuButton$", "ngOnInit", "accessData", "sessionStorage", "getItem", "JSON", "parse", "error", "console", "access", "lowercasedRoles", "map", "role", "toLowerCase", "lowercasedData", "checkSetting", "includes", "lowercasedUpload", "lowercasedUploadData", "checkUploads", "setTimeout", "getNavigationItems", "setupLoadingStateListener", "loadingSubscription", "isLoading$", "events", "event", "value", "ngOnDestroy", "unsubscribe", "retryCount", "maxRetries", "ROUTES", "path", "title", "icon", "class", "dbAccess", "getUIAccess", "next", "res", "for<PERSON>ach", "el", "hasOwnProperty", "push", "undefined", "length", "log", "err", "setDefaultNavigation", "complete", "toggleMenu", "generateLinks", "module", "links", "label", "routerLink", "onCtrlShiftR", "preventDefault", "window", "location", "reload", "KeyboardEvent", "key", "code", "ctrl<PERSON>ey", "shift<PERSON>ey", "document", "dispatchEvent", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ShareDataService", "ChangeDetectorRef", "i3", "NotificationService", "i4", "TimeOutService", "i5", "InventoryService", "i6", "Router", "i7", "LoadingService", "selectors", "hostBindings", "DashboardComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument", "provide", "useFactory", "responsiveService", "deps", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "ɵɵtemplate", "DashboardComponent_div_2_Template", "DashboardComponent_div_3_Template", "DashboardComponent_div_4_Template", "ɵɵproperty", "i8", "NgIf", "i9", "MatIcon", "i10", "MatCard", "i11", "MatButton", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/dashboard/dashboard.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, OnInit, OnDestroy, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterOutlet, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';\nimport { FixedFabButtonComponent } from 'src/app/components/fixed-fab-button/fixed-fab-button.component';\nimport { DashboardMenuComponent } from 'src/app/components/dashboard-menu/dashboard-menu.component';\n\nimport { ResponsiveService } from 'src/app/services/responsive-service.service';\nimport { DashboardMenuService } from 'src/app/services/dashboard-menu.service';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\n\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { TimeOutService } from 'src/app/services/time-out.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { LoadingService } from 'src/app/services/loading.service';\nimport { Subscription } from 'rxjs';\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterOutlet,\n    MatDividerModule,\n    MatIconModule,\n    DashboardMenuComponent,\n    FixedFabButtonComponent,\n    DashboardToolbarComponent,\n    MatCardModule,\n    MatButtonModule\n  ],\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {\n      provide: DashboardMenuService,\n      useFactory: (responsiveService: ResponsiveService) => {\n        return new DashboardMenuService(responsiveService);\n      },\n      deps: [ResponsiveService],\n    },\n  ],\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n  private dashboardMenuService = inject(DashboardMenuService);\n  public isReady = false;\n  public isLoading = true; // Global loading state for the application\n  private loadingSubscription: Subscription;\n  public dashboardPanel: {\n    icon: string;\n    title: string;\n    links: { label: string; routerLink: string }[];\n  }[] = [];\n  public menuItems: any[] = [];\n  public user: any;\n  public userRole: any;\n  public access: any;\n  @Input() showBanner: boolean = false;\n  @Input() message: string = 'A new version of our software is now available!';\n  versionNumber: string = 'v2.13.0';\n  logoUrl: string;\n  constructor(\n    private auth: AuthService,\n    private sharedData: ShareDataService,\n    private cd: ChangeDetectorRef,\n    private notify: NotificationService,\n    private sessionTimeoutService: TimeOutService,\n    private api: InventoryService,\n    private router: Router,\n    private loadingService: LoadingService\n ) {\n    this.user = this.auth.getCurrentUser();\n    this.userRole = this.auth.getCurrRole();\n    this.sharedData.sendVersionNumber(this.versionNumber)\n    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {\n      this.sharedData.checkMapping(data['bulkMapping'])\n      this.logoUrl = data.tenantDetails.logo;\n      if (this.versionNumber !== data['versionUI']){\n        this.showBanner = true\n        this.sharedData.sendVersionNumber(this.versionNumber)\n      }else{\n        this.showBanner = false\n      }\n      if(data['result'] === 'success'){\n        this.sharedData.sendTimeOutData(600)\n        this.sessionTimeoutService.start();\n      }\n      this.cd.detectChanges()\n    });\n\n  }\n\n  get showSidenav$() {\n    return this.dashboardMenuService.showSidenav$;\n  }\n\n  get sidenavType$() {\n    return this.dashboardMenuService.sidenavType$;\n  }\n\n  get showSmallDeviceMenuButton$() {\n    return this.dashboardMenuService.showSmallDeviceMenuButton$;\n  }\n\n  ngOnInit(): void {\n    // Set initial loading states\n    this.isReady = false;\n    this.isLoading = true;\n\n    try {\n      // Safely parse access data from session storage\n      let data = {};\n      try {\n        const accessData = sessionStorage.getItem('access');\n        if (accessData) {\n          data = JSON.parse(accessData);\n        }\n      } catch (error) {\n        console.error('Error parsing access data:', error);\n      }\n\n      // Handle settings access\n      if(data['settings'] || (this.access && this.access['settings'])){\n        const lowercasedRoles = data['settings'] ?? this.access['settings'].map((role: string) => role.toLowerCase());\n        const lowercasedData = this.user.role.toLowerCase();\n        this.sharedData.checkSetting(lowercasedRoles.includes(lowercasedData));\n      } else {\n        this.sharedData.checkSetting(false);\n      }\n\n      // Handle bulk excel upload access\n      if((data && data['bulkExcel']) || (this.access && this.access['bulkExcel'])){\n        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map((role: string) => role.toLowerCase());\n        const lowercasedUploadData = this.user.role.toLowerCase();\n        this.sharedData.checkUploads(lowercasedUpload.includes(lowercasedUploadData));\n      } else {\n        this.sharedData.checkUploads(false);\n      }\n\n      // Get navigation menu items with a timeout to ensure they load\n      setTimeout(() => {\n        this.getNavigationItems();\n\n        // Set ready state but keep loading state active for child components\n        this.isReady = true;\n\n        // Create a loading service to track loading state across components\n        this.setupLoadingStateListener();\n\n        this.cd.detectChanges();\n      }, 100);\n\n    } catch (error) {\n      console.error('Error in dashboard initialization:', error);\n\n      // Ensure we still try to load navigation items even if there's an error\n      this.getNavigationItems();\n\n      // Set ready state and force change detection\n      this.isReady = true;\n      this.cd.detectChanges();\n    }\n  }\n\n  // Set up a listener for loading state changes\n  setupLoadingStateListener(): void {\n    // Subscribe to the loading service\n    this.loadingSubscription = this.loadingService.isLoading$.subscribe(\n      (isLoading) => {\n        this.isLoading = isLoading;\n        this.cd.detectChanges();\n      }\n    );\n\n    // Listen for route changes to update loading state\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Set loading to true when navigation starts\n        this.isLoading = true;\n        this.cd.detectChanges();\n      } else if (event instanceof NavigationEnd ||\n                event instanceof NavigationCancel ||\n                event instanceof NavigationError) {\n        // Set loading to false when navigation ends (success or error)\n        // Add a small delay to allow components to initialize\n        setTimeout(() => {\n          // Only set loading to false if there are no active API requests\n          if (!this.loadingService.isLoading$.value) {\n            this.isLoading = false;\n            this.cd.detectChanges();\n          }\n        }, 500);\n      }\n    });\n\n    // Set initial loading state to false after a delay\n    setTimeout(() => {\n      this.isLoading = false;\n      this.cd.detectChanges();\n    }, 1000);\n  }\n\n  // Clean up subscriptions when component is destroyed\n  ngOnDestroy(): void {\n    if (this.loadingSubscription) {\n      this.loadingSubscription.unsubscribe();\n    }\n  }\n\n  getNavigationItems(retryCount = 0, maxRetries = 3) {\n    const ROUTES = [\n      { path: '/dashboard/inventory', title: 'Inventory Management', icon: 'table_chart', class: '', dbAccess: \"inventory\" },\n      { path: '/dashboard/user', title: 'User Management', icon: 'person', class: '', dbAccess: \"user\" },\n      { path: '/dashboard/recipe', title: 'Recipe Management', icon: 'fastfood', class: '', dbAccess: \"recipe\" },\n      { path: '/dashboard/party', title: 'Party Management', icon: 'event_note', class: '', dbAccess: \"party\" },\n      { path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: \"accountSetup\" },\n    ];\n\n    // Clear menu items before loading to avoid duplicates on retry\n    this.menuItems = [];\n\n    if (this.user.tenantId != '100000') {\n      this.api.getUIAccess(this.user.tenantId).subscribe({\n        next: (res) => {\n          if(res['success']) {\n            this.access = res['access']\n            ROUTES.forEach((el) => {\n              if(this.access.hasOwnProperty(el['dbAccess'])) {\n                (this.access[el['dbAccess']]['status'] === true &&\n                 this.access[el['dbAccess']]['access'].map(access => access.toLowerCase()).includes(this.user.role.toLowerCase()))\n                  ? this.menuItems.push(el)\n                  : undefined;\n              }\n            });\n          } else {\n            this.access = {}\n          }\n\n          // Force change detection to update the UI\n          this.cd.detectChanges();\n\n          // If no menu items were loaded and we haven't exceeded max retries, try again\n          if (this.menuItems.length === 0 && retryCount < maxRetries) {\n            console.log(`Retrying navigation items load, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries);\n            }, 1000); // Wait 1 second before retrying\n          }\n        },\n        error: (err) => {\n          console.error('Error loading navigation items:', err);\n\n          // On error, if we haven't exceeded max retries, try again\n          if (retryCount < maxRetries) {\n            console.log(`Retrying navigation items load after error, attempt ${retryCount + 1}`);\n            setTimeout(() => {\n              this.getNavigationItems(retryCount + 1, maxRetries);\n            }, 1000); // Wait 1 second before retrying\n          } else {\n            // If we've exceeded max retries, set default navigation\n            console.log('Max retries exceeded, setting default navigation');\n            this.setDefaultNavigation();\n            this.cd.detectChanges();\n          }\n        },\n        complete: () => {\n          // Ensure UI is updated when request completes\n          this.cd.detectChanges();\n        }\n      });\n    } else {\n      this.menuItems = [{ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: \"accountSetup\" }];\n      this.cd.detectChanges();\n    }\n  }\n\n  // Set default navigation items if API fails\n  setDefaultNavigation() {\n    this.menuItems = [\n      { path: '/dashboard/home', title: 'Dashboard', icon: 'dashboard', class: '' }\n    ];\n\n    // Add account setup for admin users\n    if (this.user.tenantId === '100000') {\n      this.menuItems.push({ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '' });\n    }\n  }\n\n  toggleMenu() {\n    this.dashboardMenuService.toggleMenu();\n  }\n\n  generateLinks(module) {\n    const links = module.map(label => {\n      return { label, routerLink: `/dashboard/${label}` };\n    });\n    return links;\n  }\n\n  @HostListener('document:keydown.control.shift.r', ['$event'])\n  onCtrlShiftR(event: KeyboardEvent) {\n    event.preventDefault();\n    window.location.reload();\n  }\n\n  refreshPage() {\n    const event = new KeyboardEvent('keydown', {\n      key: 'r',\n      code: 'KeyR',\n      ctrlKey: true,\n      shiftKey: true\n    });\n    document.dispatchEvent(event);\n  }\n}\n\n\n", "<!-- <mat-sidenav-container (backdropClick)=\"toggleMenu()\">\n  <mat-sidenav #sidenav [opened]=\"showSidenav$|async\" [mode]=\"(sidenavType$|async)!\" [disableClose]=\"true\">\n    <div class=\"sidenav-content\">\n      <app-dashboard-menu></app-dashboard-menu>\n    </div>\n  </mat-sidenav>\n  <mat-sidenav-content>\n    <div class=\"sidenav-content-container\">\n      <app-dashboard-toolbar (toggleMenu)=\"toggleMenu()\"\n        [showNavbarToggleButton]=\"(showSmallDeviceMenuButton$|async)!\"></app-dashboard-toolbar>\n      <router-outlet></router-outlet>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container> -->\n\n\n<!-- <mat-toolbar class=\"mat-elevation-z8\">\n  <button mat-icon-button (click)=\"sidenav.toggle()\">\n    <mat-icon> menu </mat-icon>\n  </button>\n  Digitory\n</mat-toolbar> -->\n\n\n<div class=\"dashboard-container\">\n  <!-- Always render the toolbar to ensure navigation tabs are visible -->\n  <app-dashboard-toolbar [menuItems]=\"menuItems\" [logoUrl]=\"logoUrl\">\n  </app-dashboard-toolbar>\n\n  <!-- Main content area -->\n  <div class=\"content mat-elevation-z8\" *ngIf=\"!showBanner && isReady\">\n    <router-outlet></router-outlet>\n  </div>\n\n  <!-- Loading indicator when navigation is not ready or any component is loading -->\n  <div class=\"content mat-elevation-z8 loading-container\" *ngIf=\"!showBanner && (!isReady || isLoading)\">\n    <div class=\"loading-spinner\">\n      <mat-icon class=\"spin\">refresh</mat-icon>\n      <p>Loading application...</p>\n    </div>\n  </div>\n\n  <!-- Update banner -->\n  <div class=\"closingContainer\" *ngIf=\"showBanner\">\n    <div class=\"closingContainerDatas\">\n      <mat-card>\n        <div class=\"closeMsg\">\n          {{message}}\n        </div>\n        <div class=\"text-align-center text-center m-3\">\n          <button mat-button mat-raised-button (click)=\"refreshPage()\">\n            click to update\n          </button>\n        </div>\n      </mat-card>\n    </div>\n  </div>\n</div>"], "mappings": "AAAA,SAAwGA,MAAM,QAAQ,eAAe;AACrI,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;AACzH,SAASC,yBAAyB,QAAQ,kEAAkE;AAI5G,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AAKtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;;;ICcpDC,EAAA,CAAAC,cAAA,aAAqE;IACnED,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,aAAuG;IAE5ED,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAKjCH,EAAA,CAAAC,cAAA,aAAiD;IAIzCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA+C;IACRD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC1DZ,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALTH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;AD3BR,MA2BaC,kBAAkB;EAkB7BC,YACUC,IAAiB,EACjBC,UAA4B,EAC5BC,EAAqB,EACrBC,MAA2B,EAC3BC,qBAAqC,EACrCC,GAAqB,EACrBC,MAAc,EACdC,cAA8B;IAP9B,KAAAP,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAzBhB,KAAAC,oBAAoB,GAAGzC,MAAM,CAACS,oBAAoB,CAAC;IACpD,KAAAiC,OAAO,GAAG,KAAK;IACf,KAAAC,SAAS,GAAG,IAAI,CAAC,CAAC;IAElB,KAAAC,cAAc,GAIf,EAAE;IACD,KAAAC,SAAS,GAAU,EAAE;IAInB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAhB,OAAO,GAAW,iDAAiD;IAC5E,KAAAiB,aAAa,GAAW,SAAS;IAY/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACf,IAAI,CAACgB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACjB,IAAI,CAACkB,WAAW,EAAE;IACvC,IAAI,CAACjB,UAAU,CAACkB,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;IACrD,IAAI,CAACd,IAAI,CAACoB,YAAY,CAAC;MAAEC,QAAQ,EAAE,IAAI,CAACN,IAAI,CAACM;IAAQ,CAAE,CAAC,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC1E,IAAI,CAACtB,UAAU,CAACuB,YAAY,CAACD,IAAI,CAAC,aAAa,CAAC,CAAC;MACjD,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACG,aAAa,CAACC,IAAI;MACtC,IAAI,IAAI,CAACb,aAAa,KAAKS,IAAI,CAAC,WAAW,CAAC,EAAC;QAC3C,IAAI,CAACV,UAAU,GAAG,IAAI;QACtB,IAAI,CAACZ,UAAU,CAACkB,iBAAiB,CAAC,IAAI,CAACL,aAAa,CAAC;OACtD,MAAI;QACH,IAAI,CAACD,UAAU,GAAG,KAAK;;MAEzB,IAAGU,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAC;QAC9B,IAAI,CAACtB,UAAU,CAAC2B,eAAe,CAAC,GAAG,CAAC;QACpC,IAAI,CAACxB,qBAAqB,CAACyB,KAAK,EAAE;;MAEpC,IAAI,CAAC3B,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,CAAC;EAEJ;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACvB,oBAAoB,CAACuB,YAAY;EAC/C;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACxB,oBAAoB,CAACwB,YAAY;EAC/C;EAEA,IAAIC,0BAA0BA,CAAA;IAC5B,OAAO,IAAI,CAACzB,oBAAoB,CAACyB,0BAA0B;EAC7D;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACzB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI;MACF;MACA,IAAIa,IAAI,GAAG,EAAE;MACb,IAAI;QACF,MAAMY,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC;QACnD,IAAIF,UAAU,EAAE;UACdZ,IAAI,GAAGe,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;;OAEhC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAGpD;MACA,IAAGjB,IAAI,CAAC,UAAU,CAAC,IAAK,IAAI,CAACmB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,UAAU,CAAE,EAAC;QAC9D,MAAMC,eAAe,GAAGpB,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAACmB,MAAM,CAAC,UAAU,CAAC,CAACE,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC;QAC7G,MAAMC,cAAc,GAAG,IAAI,CAAChC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE;QACnD,IAAI,CAAC7C,UAAU,CAAC+C,YAAY,CAACL,eAAe,CAACM,QAAQ,CAACF,cAAc,CAAC,CAAC;OACvE,MAAM;QACL,IAAI,CAAC9C,UAAU,CAAC+C,YAAY,CAAC,KAAK,CAAC;;MAGrC;MACA,IAAIzB,IAAI,IAAIA,IAAI,CAAC,WAAW,CAAC,IAAM,IAAI,CAACmB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,WAAW,CAAE,EAAC;QAC1E,MAAMQ,gBAAgB,GAAG3B,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAACmB,MAAM,CAAC,WAAW,CAAC,CAACE,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC;QAChH,MAAMK,oBAAoB,GAAG,IAAI,CAACpC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE;QACzD,IAAI,CAAC7C,UAAU,CAACmD,YAAY,CAACF,gBAAgB,CAACD,QAAQ,CAACE,oBAAoB,CAAC,CAAC;OAC9E,MAAM;QACL,IAAI,CAAClD,UAAU,CAACmD,YAAY,CAAC,KAAK,CAAC;;MAGrC;MACAC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,kBAAkB,EAAE;QAEzB;QACA,IAAI,CAAC7C,OAAO,GAAG,IAAI;QAEnB;QACA,IAAI,CAAC8C,yBAAyB,EAAE;QAEhC,IAAI,CAACrD,EAAE,CAAC4B,aAAa,EAAE;MACzB,CAAC,EAAE,GAAG,CAAC;KAER,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAE1D;MACA,IAAI,CAACc,kBAAkB,EAAE;MAEzB;MACA,IAAI,CAAC7C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACP,EAAE,CAAC4B,aAAa,EAAE;;EAE3B;EAEA;EACAyB,yBAAyBA,CAAA;IACvB;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACjD,cAAc,CAACkD,UAAU,CAACnC,SAAS,CAChEZ,SAAS,IAAI;MACZ,IAAI,CAACA,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACR,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,CACF;IAED;IACA,IAAI,CAACxB,MAAM,CAACoD,MAAM,CAACpC,SAAS,CAACqC,KAAK,IAAG;MACnC,IAAIA,KAAK,YAAYzF,eAAe,EAAE;QACpC;QACA,IAAI,CAACwC,SAAS,GAAG,IAAI;QACrB,IAAI,CAACR,EAAE,CAAC4B,aAAa,EAAE;OACxB,MAAM,IAAI6B,KAAK,YAAYxF,aAAa,IAC/BwF,KAAK,YAAYvF,gBAAgB,IACjCuF,KAAK,YAAYtF,eAAe,EAAE;QAC1C;QACA;QACAgF,UAAU,CAAC,MAAK;UACd;UACA,IAAI,CAAC,IAAI,CAAC9C,cAAc,CAACkD,UAAU,CAACG,KAAK,EAAE;YACzC,IAAI,CAAClD,SAAS,GAAG,KAAK;YACtB,IAAI,CAACR,EAAE,CAAC4B,aAAa,EAAE;;QAE3B,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC;IAEF;IACAuB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3C,SAAS,GAAG,KAAK;MACtB,IAAI,CAACR,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACA+B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACL,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACM,WAAW,EAAE;;EAE1C;EAEAR,kBAAkBA,CAACS,UAAU,GAAG,CAAC,EAAEC,UAAU,GAAG,CAAC;IAC/C,MAAMC,MAAM,GAAG,CACb;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE,EACtH;MAAEJ,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAM,CAAE,EAClG;MAAEJ,IAAI,EAAE,mBAAmB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAE,EAC1G;MAAEJ,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAO,CAAE,EACzG;MAAEJ,IAAI,EAAE,oBAAoB;MAAEC,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAc,CAAE,CACnH;IAED;IACA,IAAI,CAAC1D,SAAS,GAAG,EAAE;IAEnB,IAAI,IAAI,CAACG,IAAI,CAACM,QAAQ,IAAI,QAAQ,EAAE;MAClC,IAAI,CAAChB,GAAG,CAACkE,WAAW,CAAC,IAAI,CAACxD,IAAI,CAACM,QAAQ,CAAC,CAACC,SAAS,CAAC;QACjDkD,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAGA,GAAG,CAAC,SAAS,CAAC,EAAE;YACjB,IAAI,CAAC/B,MAAM,GAAG+B,GAAG,CAAC,QAAQ,CAAC;YAC3BR,MAAM,CAACS,OAAO,CAAEC,EAAE,IAAI;cACpB,IAAG,IAAI,CAACjC,MAAM,CAACkC,cAAc,CAACD,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE;gBAC5C,IAAI,CAACjC,MAAM,CAACiC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,IAAI,IAC9C,IAAI,CAACjC,MAAM,CAACiC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC/B,GAAG,CAACF,MAAM,IAAIA,MAAM,CAACI,WAAW,EAAE,CAAC,CAACG,QAAQ,CAAC,IAAI,CAAClC,IAAI,CAAC8B,IAAI,CAACC,WAAW,EAAE,CAAC,GAC7G,IAAI,CAAClC,SAAS,CAACiE,IAAI,CAACF,EAAE,CAAC,GACvBG,SAAS;;YAEjB,CAAC,CAAC;WACH,MAAM;YACL,IAAI,CAACpC,MAAM,GAAG,EAAE;;UAGlB;UACA,IAAI,CAACxC,EAAE,CAAC4B,aAAa,EAAE;UAEvB;UACA,IAAI,IAAI,CAAClB,SAAS,CAACmE,MAAM,KAAK,CAAC,IAAIhB,UAAU,GAAGC,UAAU,EAAE;YAC1DvB,OAAO,CAACuC,GAAG,CAAC,2CAA2CjB,UAAU,GAAG,CAAC,EAAE,CAAC;YACxEV,UAAU,CAAC,MAAK;cACd,IAAI,CAACC,kBAAkB,CAACS,UAAU,GAAG,CAAC,EAAEC,UAAU,CAAC;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEd,CAAC;;QACDxB,KAAK,EAAGyC,GAAG,IAAI;UACbxC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEyC,GAAG,CAAC;UAErD;UACA,IAAIlB,UAAU,GAAGC,UAAU,EAAE;YAC3BvB,OAAO,CAACuC,GAAG,CAAC,uDAAuDjB,UAAU,GAAG,CAAC,EAAE,CAAC;YACpFV,UAAU,CAAC,MAAK;cACd,IAAI,CAACC,kBAAkB,CAACS,UAAU,GAAG,CAAC,EAAEC,UAAU,CAAC;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;WACX,MAAM;YACL;YACAvB,OAAO,CAACuC,GAAG,CAAC,kDAAkD,CAAC;YAC/D,IAAI,CAACE,oBAAoB,EAAE;YAC3B,IAAI,CAAChF,EAAE,CAAC4B,aAAa,EAAE;;QAE3B,CAAC;QACDqD,QAAQ,EAAEA,CAAA,KAAK;UACb;UACA,IAAI,CAACjF,EAAE,CAAC4B,aAAa,EAAE;QACzB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAClB,SAAS,GAAG,CAAC;QAAEsD,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAc,CAAE,CAAC;MACrI,IAAI,CAACpE,EAAE,CAAC4B,aAAa,EAAE;;EAE3B;EAEA;EACAoD,oBAAoBA,CAAA;IAClB,IAAI,CAACtE,SAAS,GAAG,CACf;MAAEsD,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAE,CAAE,CAC9E;IAED;IACA,IAAI,IAAI,CAACtD,IAAI,CAACM,QAAQ,KAAK,QAAQ,EAAE;MACnC,IAAI,CAACT,SAAS,CAACiE,IAAI,CAAC;QAAEX,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAE,CAAE,CAAC;;EAEjH;EAEAe,UAAUA,CAAA;IACR,IAAI,CAAC5E,oBAAoB,CAAC4E,UAAU,EAAE;EACxC;EAEAC,aAAaA,CAACC,MAAM;IAClB,MAAMC,KAAK,GAAGD,MAAM,CAAC1C,GAAG,CAAC4C,KAAK,IAAG;MAC/B,OAAO;QAAEA,KAAK;QAAEC,UAAU,EAAE,cAAcD,KAAK;MAAE,CAAE;IACrD,CAAC,CAAC;IACF,OAAOD,KAAK;EACd;EAGAG,YAAYA,CAAC/B,KAAoB;IAC/BA,KAAK,CAACgC,cAAc,EAAE;IACtBC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEArG,WAAWA,CAAA;IACT,MAAMkE,KAAK,GAAG,IAAIoC,aAAa,CAAC,SAAS,EAAE;MACzCC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX,CAAC;IACFC,QAAQ,CAACC,aAAa,CAAC1C,KAAK,CAAC;EAC/B;;;uBA9QW7D,kBAAkB,EAAAjB,EAAA,CAAAyH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAyH,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7H,EAAA,CAAAyH,iBAAA,CAAAzH,EAAA,CAAA8H,iBAAA,GAAA9H,EAAA,CAAAyH,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAhI,EAAA,CAAAyH,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAlI,EAAA,CAAAyH,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAApI,EAAA,CAAAyH,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAAtI,EAAA,CAAAyH,iBAAA,CAAAc,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAlBvH,kBAAkB;MAAAwH,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAlBC,GAAA,CAAAhC,YAAA,CAAAiC,MAAA,CAAoB;UAAA,UAAA9I,EAAA,CAAA+I,iBAAA;;;;;;;;uCAVpB,CACT;QACEC,OAAO,EAAErJ,oBAAoB;QAC7BsJ,UAAU,EAAGC,iBAAoC,IAAI;UACnD,OAAO,IAAIvJ,oBAAoB,CAACuJ,iBAAiB,CAAC;QACpD,CAAC;QACDC,IAAI,EAAE,CAACzJ,iBAAiB;OACzB,CACF,GAAAM,EAAA,CAAAoJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBH5I,EAAA,CAAAC,cAAA,aAAiC;UAE/BD,EAAA,CAAAE,SAAA,+BACwB;UAGxBF,EAAA,CAAA0J,UAAA,IAAAC,iCAAA,iBAEM;UAGN3J,EAAA,CAAA0J,UAAA,IAAAE,iCAAA,iBAKM;UAGN5J,EAAA,CAAA0J,UAAA,IAAAG,iCAAA,iBAaM;UACR7J,EAAA,CAAAG,YAAA,EAAM;;;UA/BmBH,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAA8J,UAAA,cAAAjB,GAAA,CAAA9G,SAAA,CAAuB,YAAA8G,GAAA,CAAAjG,OAAA;UAIP5C,EAAA,CAAAa,SAAA,GAA4B;UAA5Bb,EAAA,CAAA8J,UAAA,UAAAjB,GAAA,CAAA7G,UAAA,IAAA6G,GAAA,CAAAjH,OAAA,CAA4B;UAKV5B,EAAA,CAAAa,SAAA,GAA4C;UAA5Cb,EAAA,CAAA8J,UAAA,UAAAjB,GAAA,CAAA7G,UAAA,MAAA6G,GAAA,CAAAjH,OAAA,IAAAiH,GAAA,CAAAhH,SAAA,EAA4C;UAQtE7B,EAAA,CAAAa,SAAA,GAAgB;UAAhBb,EAAA,CAAA8J,UAAA,SAAAjB,GAAA,CAAA7G,UAAA,CAAgB;;;qBDlB7C7C,YAAY,EAAA4K,EAAA,CAAAC,IAAA,EACZ5K,YAAY,EACZQ,gBAAgB,EAChBC,aAAa,EAAAoK,EAAA,CAAAC,OAAA,EAGbzK,yBAAyB,EACzBM,aAAa,EAAAoK,GAAA,CAAAC,OAAA,EACbtK,eAAe,EAAAuK,GAAA,CAAAC,SAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAeNvJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}