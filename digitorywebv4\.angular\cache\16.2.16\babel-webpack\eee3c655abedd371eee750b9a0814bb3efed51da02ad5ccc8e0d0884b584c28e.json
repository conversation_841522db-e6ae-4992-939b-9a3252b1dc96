{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSum from './_baseSum.js';\n\n/**\n * This method is like `_.sum` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be summed.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the sum.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.sumBy(objects, function(o) { return o.n; });\n * // => 20\n *\n * // The `_.property` iteratee shorthand.\n * _.sumBy(objects, 'n');\n * // => 20\n */\nfunction sumBy(array, iteratee) {\n  return array && array.length ? baseSum(array, baseIteratee(iteratee, 2)) : 0;\n}\nexport default sumBy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}