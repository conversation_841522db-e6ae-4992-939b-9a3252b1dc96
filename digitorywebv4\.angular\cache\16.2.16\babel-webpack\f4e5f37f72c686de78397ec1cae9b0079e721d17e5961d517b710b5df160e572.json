{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Swahili [sw]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/fadsel\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var sw = moment.defineLocale('sw', {\n    months: 'Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba'.split('_'),\n    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des'.split('_'),\n    weekdays: 'Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi'.split('_'),\n    weekdaysShort: 'Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos'.split('_'),\n    weekdaysMin: 'J2_J3_J4_J5_Al_Ij_J1'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'hh:mm A',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[leo saa] LT',\n      nextDay: '[kesho saa] LT',\n      nextWeek: '[wiki ijayo] dddd [saat] LT',\n      lastDay: '[jana] LT',\n      lastWeek: '[wiki iliyopita] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s baadaye',\n      past: 'tokea %s',\n      s: 'hivi punde',\n      ss: 'sekunde %d',\n      m: 'dakika moja',\n      mm: 'dakika %d',\n      h: 'saa limoja',\n      hh: 'masaa %d',\n      d: 'siku moja',\n      dd: 'siku %d',\n      M: 'mwezi mmoja',\n      MM: 'miezi %d',\n      y: 'mwaka mmoja',\n      yy: 'miaka %d'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n\n  return sw;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "sw", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["/home/<USER>/other/digi/digitorywebv4/node_modules/moment/locale/sw.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Swahili [sw]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/fadsel\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var sw = moment.defineLocale('sw', {\n        months: 'Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des'.split('_'),\n        weekdays:\n            'Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi'.split(\n                '_'\n            ),\n        weekdaysShort: 'Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos'.split('_'),\n        weekdaysMin: 'J2_J3_J4_J5_Al_Ij_J1'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'hh:mm A',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[leo saa] LT',\n            nextDay: '[kesho saa] LT',\n            nextWeek: '[wiki ijayo] dddd [saat] LT',\n            lastDay: '[jana] LT',\n            lastWeek: '[wiki iliyopita] dddd [saat] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s baadaye',\n            past: 'tokea %s',\n            s: 'hivi punde',\n            ss: 'sekunde %d',\n            m: 'dakika moja',\n            mm: 'dakika %d',\n            h: 'saa limoja',\n            hh: 'masaa %d',\n            d: 'siku moja',\n            dd: 'siku %d',\n            M: 'mwezi mmoja',\n            MM: 'miezi %d',\n            y: 'mwaka mmoja',\n            yy: 'miaka %d',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return sw;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,qFAAqF,CAACC,KAAK,CAC/F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EACJ,6DAA6D,CAACF,KAAK,CAC/D,GACJ,CAAC;IACLG,aAAa,EAAE,iCAAiC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC3DI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,SAAS;MACbC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,6BAA6B;MACvCC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,iCAAiC;MAC3CC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOxC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}