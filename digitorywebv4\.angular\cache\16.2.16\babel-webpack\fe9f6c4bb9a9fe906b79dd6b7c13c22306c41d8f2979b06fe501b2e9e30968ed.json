{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { finalize, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/loading.service\";\nclass LoadingInterceptor {\n  constructor(loadingService) {\n    this.loadingService = loadingService;\n    this.totalRequests = 0;\n    this.pendingRequests = new Set();\n  }\n  intercept(request, next) {\n    // Skip certain requests if needed (like analytics or background tasks)\n    if (this.shouldSkip(request)) {\n      return next.handle(request);\n    }\n    // Add request to pending set\n    this.pendingRequests.add(request);\n    // Increment the request counter\n    this.totalRequests++;\n    // Show the loader\n    this.loadingService.setLoading(true);\n    return next.handle(request).pipe(\n    // Add a small delay to ensure loader is visible for very fast requests\n    tap(event => {\n      if (event instanceof HttpResponse) {\n        // Remove request from pending set\n        this.pendingRequests.delete(request);\n      }\n    }), finalize(() => {\n      // Decrement the request counter\n      this.totalRequests--;\n      // Remove request from pending set (in case of error)\n      this.pendingRequests.delete(request);\n      // If there are no more pending requests, hide the loader\n      if (this.totalRequests === 0) {\n        // Add a small delay to prevent flickering for sequential requests\n        setTimeout(() => {\n          if (this.totalRequests === 0) {\n            this.loadingService.setLoading(false);\n          }\n        }, 100);\n      }\n    }));\n  }\n  shouldSkip(request) {\n    // Skip certain endpoints if needed\n    const skipUrls = [\n      // Add URLs to skip here if needed\n      // '/api/analytics',\n      // '/api/heartbeat'\n    ];\n    return skipUrls.some(url => request.url.includes(url));\n  }\n  static {\n    this.ɵfac = function LoadingInterceptor_Factory(t) {\n      return new (t || LoadingInterceptor)(i0.ɵɵinject(i1.LoadingService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingInterceptor,\n      factory: LoadingInterceptor.ɵfac\n    });\n  }\n}\nexport { LoadingInterceptor };", "map": {"version": 3, "names": ["HttpResponse", "finalize", "tap", "LoadingInterceptor", "constructor", "loadingService", "totalRequests", "pendingRequests", "Set", "intercept", "request", "next", "shouldSkip", "handle", "add", "setLoading", "pipe", "event", "delete", "setTimeout", "skipUrls", "some", "url", "includes", "i0", "ɵɵinject", "i1", "LoadingService", "factory", "ɵfac"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/_interceptors/loading.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { <PERSON>ttp<PERSON>e<PERSON>, HttpHandler, HttpEvent, HttpInterceptor, HttpResponse } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { finalize, tap } from 'rxjs/operators';\nimport { LoadingService } from '../services/loading.service';\nimport { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\n\n@Injectable()\nexport class LoadingInterceptor implements HttpInterceptor {\n  private totalRequests = 0;\n  private pendingRequests = new Set<HttpRequest<any>>();\n\n  constructor(private loadingService: LoadingService) {}\n\n  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {\n    // Skip certain requests if needed (like analytics or background tasks)\n    if (this.shouldSkip(request)) {\n      return next.handle(request);\n    }\n\n    // Add request to pending set\n    this.pendingRequests.add(request);\n\n    // Increment the request counter\n    this.totalRequests++;\n\n    // Show the loader\n    this.loadingService.setLoading(true);\n\n    return next.handle(request).pipe(\n      // Add a small delay to ensure loader is visible for very fast requests\n      tap(event => {\n        if (event instanceof HttpResponse) {\n          // Remove request from pending set\n          this.pendingRequests.delete(request);\n        }\n      }),\n      finalize(() => {\n        // Decrement the request counter\n        this.totalRequests--;\n\n        // Remove request from pending set (in case of error)\n        this.pendingRequests.delete(request);\n\n        // If there are no more pending requests, hide the loader\n        if (this.totalRequests === 0) {\n          // Add a small delay to prevent flickering for sequential requests\n          setTimeout(() => {\n            if (this.totalRequests === 0) {\n              this.loadingService.setLoading(false);\n            }\n          }, 100);\n        }\n      })\n    );\n  }\n\n  private shouldSkip(request: HttpRequest<any>): boolean {\n    // Skip certain endpoints if needed\n    const skipUrls = [\n      // Add URLs to skip here if needed\n      // '/api/analytics',\n      // '/api/heartbeat'\n    ];\n\n    return skipUrls.some(url => request.url.includes(url));\n  }\n}\n"], "mappings": "AACA,SAA+DA,YAAY,QAAQ,sBAAsB;AAEzG,SAASC,QAAQ,EAAEC,GAAG,QAAQ,gBAAgB;;;AAI9C,MACaC,kBAAkB;EAI7BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAH1B,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAAG,IAAIC,GAAG,EAAoB;EAEA;EAErDC,SAASA,CAACC,OAA6B,EAAEC,IAAiB;IACxD;IACA,IAAI,IAAI,CAACC,UAAU,CAACF,OAAO,CAAC,EAAE;MAC5B,OAAOC,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC;;IAG7B;IACA,IAAI,CAACH,eAAe,CAACO,GAAG,CAACJ,OAAO,CAAC;IAEjC;IACA,IAAI,CAACJ,aAAa,EAAE;IAEpB;IACA,IAAI,CAACD,cAAc,CAACU,UAAU,CAAC,IAAI,CAAC;IAEpC,OAAOJ,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACM,IAAI;IAC9B;IACAd,GAAG,CAACe,KAAK,IAAG;MACV,IAAIA,KAAK,YAAYjB,YAAY,EAAE;QACjC;QACA,IAAI,CAACO,eAAe,CAACW,MAAM,CAACR,OAAO,CAAC;;IAExC,CAAC,CAAC,EACFT,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACK,aAAa,EAAE;MAEpB;MACA,IAAI,CAACC,eAAe,CAACW,MAAM,CAACR,OAAO,CAAC;MAEpC;MACA,IAAI,IAAI,CAACJ,aAAa,KAAK,CAAC,EAAE;QAC5B;QACAa,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACb,aAAa,KAAK,CAAC,EAAE;YAC5B,IAAI,CAACD,cAAc,CAACU,UAAU,CAAC,KAAK,CAAC;;QAEzC,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC,CACH;EACH;EAEQH,UAAUA,CAACF,OAAyB;IAC1C;IACA,MAAMU,QAAQ,GAAG;MACf;MACA;MACA;IAAA,CACD;IAED,OAAOA,QAAQ,CAACC,IAAI,CAACC,GAAG,IAAIZ,OAAO,CAACY,GAAG,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC;EACxD;;;uBA1DWnB,kBAAkB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAlBxB,kBAAkB;MAAAyB,OAAA,EAAlBzB,kBAAkB,CAAA0B;IAAA;EAAA;;SAAlB1B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}