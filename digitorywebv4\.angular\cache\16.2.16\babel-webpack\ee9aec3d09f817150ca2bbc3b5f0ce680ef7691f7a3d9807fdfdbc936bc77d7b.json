{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/tooltip\";\nimport * as i16 from \"@angular/material/radio\";\nimport * as i17 from \"@angular/material/button\";\nimport * as i18 from \"@angular/material/card\";\nimport * as i19 from \"@angular/material/progress-bar\";\nfunction AccountSetupComponent_mat_error_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵelement(1, \"img\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 47);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"h3\")(4, \"mat-icon\", 58);\n    i0.ɵɵtext(5, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 59)(14, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Get Started \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_4_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 70);\n    i0.ɵɵtext(1, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_4_app_chat_bot_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-bot\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"tenantId\", ctx_r17.registrationForm.value.tenantId)(\"tenantName\", ctx_r17.registrationForm.value.tenantName);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"chat-bot-minimized\": a0\n  };\n};\nfunction AccountSetupComponent_mat_card_110_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"h3\")(3, \"mat-icon\", 63);\n    i0.ɵɵtext(4, \"smart_toy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Information Assistant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 64)(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.toggleChatBot());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.startAIProcessing());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"skip_next\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 67);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_110_div_4_p_14_Template, 2, 0, \"p\", 68);\n    i0.ɵɵtemplate(15, AccountSetupComponent_mat_card_110_div_4_app_chat_bot_15_Template, 1, 2, \"app-chat-bot\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r10.chatBotMinimized ? \"Expand\" : \"Minimize\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.chatBotMinimized ? \"expand_more\" : \"expand_less\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r10.chatBotMinimized));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.chatBotMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.chatBotMinimized);\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r21.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"span\", 94);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_110_div_5_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_110_div_5_div_16_div_3_Template, 3, 0, \"div\", 89);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 90)(6, \"div\", 91);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 92);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r25 = ctx.$implicit;\n    const i_r26 = ctx.index;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c1, step_r25.completed, ctx_r24.activeStep === i_r26, !step_r25.completed && ctx_r24.activeStep !== i_r26));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r25.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r25.completed && ctx_r24.activeStep === i_r26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r25.completed && ctx_r24.activeStep !== i_r26);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r25.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r25.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"h3\", 73)(2, \"mat-icon\", 74);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 75);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 76);\n    i0.ɵɵelementStart(7, \"div\", 77);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 78)(10, \"mat-icon\", 79);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_110_div_5_span_12_Template, 3, 4, \"span\", 19);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_110_div_5_span_13_Template, 2, 0, \"span\", 19);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_110_div_5_span_14_Template, 2, 0, \"span\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 81);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_110_div_5_div_16_Template, 10, 10, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"div\", 84)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 85);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r11.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 0 && ctx_r11.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"mat-icon\", 97);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 98);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 99)(9, \"div\", 100)(10, \"mat-card\", 101)(11, \"mat-card-header\")(12, \"div\", 102)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-card-title\");\n    i0.ɵɵtext(16, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-subtitle\");\n    i0.ɵɵtext(18, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_6_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.downloadInventory());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Download \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 100)(25, \"mat-card\", 101)(26, \"mat-card-header\")(27, \"div\", 104)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-card-title\");\n    i0.ɵɵtext(31, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-card-subtitle\");\n    i0.ɵɵtext(33, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card-actions\")(35, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_6_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.downloadPackaging());\n    });\n    i0.ɵɵelementStart(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Download \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"mat-icon\", 107);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 108);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 109)(9, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_110_div_7_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 48)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"AI-Powered Data Generation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_110_div_3_Template, 18, 0, \"div\", 50);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_110_div_4_Template, 16, 7, \"div\", 51);\n    i0.ɵɵtemplate(5, AccountSetupComponent_mat_card_110_div_5_Template, 25, 6, \"div\", 52);\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_110_div_6_Template, 39, 0, \"div\", 53);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_110_div_7_Template, 13, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.isDownloading && !ctx_r8.downloadComplete && !ctx_r8.downloadFailed && !ctx_r8.showChatBot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.showChatBot && !ctx_r8.isDownloading && !ctx_r8.downloadComplete && !ctx_r8.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.downloadFailed);\n  }\n}\nconst _c2 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c3 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // Chat bot related properties\n    this.showChatBot = false;\n    this.chatBotMinimized = false;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  toggleChatBot() {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 111,\n      vars: 18,\n      consts: [[1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"form-row\", \"logo-status-row\"], [1, \"compact-logo-section\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [1, \"compact-status-section\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\"], [\"value\", \"no\", \"color\", \"primary\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"text-center\", \"p-2\", \"my-2\", \"bottomTitles\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"ai-intro-panel\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-8\"], [1, \"ai-icon\"], [1, \"col-md-4\", \"text-center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-ai-btn\", 3, \"click\"], [1, \"chat-bot-section\"], [1, \"chat-bot-header\"], [1, \"chat-bot-icon\"], [1, \"chat-bot-actions\"], [\"mat-icon-button\", \"\", 3, \"matTooltip\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Skip to AI Processing\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"chat-bot-description\", 4, \"ngIf\"], [3, \"tenantId\", \"tenantName\", 4, \"ngIf\"], [1, \"chat-bot-description\"], [3, \"tenantId\", \"tenantName\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"row\", \"download-options\"], [1, \"col-md-6\", \"mb-3\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r35 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 6)(16, \"mat-icon\", 4);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_21_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"mat-card\", 10)(27, \"mat-card-content\")(28, \"form\", 11)(29, \"h3\", 12);\n          i0.ɵɵtext(30, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"div\", 14)(33, \"mat-form-field\", 15)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 16);\n          i0.ɵɵelementStart(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 15)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 18);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_42_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-icon\", 17);\n          i0.ɵɵtext(44, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, AccountSetupComponent_mat_error_45_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 15)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 20);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_49_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-icon\", 17);\n          i0.ɵɵtext(51, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, AccountSetupComponent_mat_error_52_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 14)(54, \"mat-form-field\", 15)(55, \"mat-label\");\n          i0.ɵɵtext(56, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_57_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-icon\", 17);\n          i0.ɵɵtext(59, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, AccountSetupComponent_mat_error_60_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 15)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 22);\n          i0.ɵɵelementStart(65, \"mat-icon\", 17);\n          i0.ɵɵtext(66, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"mat-form-field\", 15)(68, \"mat-label\");\n          i0.ɵɵtext(69, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 23);\n          i0.ɵɵelementStart(71, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_71_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(72, \"mat-icon\");\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26);\n          i0.ɵɵtemplate(76, AccountSetupComponent_div_76_Template, 2, 1, \"div\", 27);\n          i0.ɵɵelementStart(77, \"div\", 28)(78, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_78_listener() {\n            i0.ɵɵrestoreView(_r35);\n            const _r6 = i0.ɵɵreference(83);\n            return i0.ɵɵresetView(_r6.click());\n          });\n          i0.ɵɵtemplate(79, AccountSetupComponent_div_79_Template, 3, 0, \"div\", 30);\n          i0.ɵɵtemplate(80, AccountSetupComponent_mat_icon_80_Template, 2, 0, \"mat-icon\", 19);\n          i0.ɵɵtext(81, \" Logo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"input\", 31, 32);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_82_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(84, AccountSetupComponent_mat_error_84_Template, 2, 0, \"mat-error\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(85, \"div\", 34)(86, \"div\", 35)(87, \"label\", 36);\n          i0.ɵɵtext(88, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"mat-radio-group\", 37)(90, \"mat-radio-button\", 38);\n          i0.ɵɵtext(91, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"mat-radio-button\", 39);\n          i0.ɵɵtext(93, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 35)(95, \"label\", 36);\n          i0.ɵɵtext(96, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"mat-radio-group\", 40)(98, \"mat-radio-button\", 38);\n          i0.ɵɵtext(99, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"mat-radio-button\", 39);\n          i0.ɵɵtext(101, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(102, \"div\", 35)(103, \"label\", 36);\n          i0.ɵɵtext(104, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"mat-radio-group\", 41)(106, \"mat-radio-button\", 38);\n          i0.ɵɵtext(107, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"mat-radio-button\", 39);\n          i0.ɵɵtext(109, \"Disabled\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(110, AccountSetupComponent_mat_card_110_Template, 8, 5, \"mat-card\", 42);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(16, _c2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c3));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, i15.MatTooltip, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i16.MatRadioGroup, i16.MatRadioButton, MatButtonModule, i17.MatButton, i17.MatIconButton, MatCardModule, i18.MatCard, i18.MatCardActions, i18.MatCardAvatar, i18.MatCardContent, i18.MatCardHeader, i18.MatCardSubtitle, i18.MatCardTitle, MatSelectModule, MatProgressBarModule, i19.MatProgressBar, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 6px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  align-items: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-logo-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-logo-section[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-logo-section[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: contain;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-logo-section[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  height: 36px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-logo-section[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-status-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  flex: 1;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-status-section[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-status-section[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  font-weight: 500;\\n  font-size: 13px;\\n  color: #555;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-status-section[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .logo-status-row[_ngcontent-%COMP%]   .compact-status-section[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 20px;\\n  background-color: #e8eaf6;\\n  border-bottom: 1px solid #c5cae9;\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-bot-minimized[_ngcontent-%COMP%] {\\n  height: 0;\\n  overflow: hidden;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 500px;\\n  padding: 0 20px 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_110_div_3_Template_button_click_14_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "ctx_r17", "registrationForm", "value", "tenantId", "tenantName", "AccountSetupComponent_mat_card_110_div_4_Template_button_click_7_listener", "_r19", "ctx_r18", "toggleChatBot", "AccountSetupComponent_mat_card_110_div_4_Template_button_click_10_listener", "ctx_r20", "startAIProcessing", "ɵɵtemplate", "AccountSetupComponent_mat_card_110_div_4_p_14_Template", "AccountSetupComponent_mat_card_110_div_4_app_chat_bot_15_Template", "ɵɵpropertyInterpolate", "ctx_r10", "chatBotMinimized", "ɵɵtextInterpolate", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r21", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_110_div_5_div_16_div_3_Template", "AccountSetupComponent_mat_card_110_div_5_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c1", "step_r25", "completed", "ctx_r24", "activeStep", "i_r26", "name", "description", "AccountSetupComponent_mat_card_110_div_5_span_12_Template", "AccountSetupComponent_mat_card_110_div_5_span_13_Template", "AccountSetupComponent_mat_card_110_div_5_span_14_Template", "AccountSetupComponent_mat_card_110_div_5_div_16_Template", "ctx_r11", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_110_div_6_Template_button_click_20_listener", "_r31", "ctx_r30", "downloadInventory", "AccountSetupComponent_mat_card_110_div_6_Template_button_click_35_listener", "ctx_r32", "downloadPackaging", "AccountSetupComponent_mat_card_110_div_7_Template_button_click_9_listener", "_r34", "ctx_r33", "AccountSetupComponent_mat_card_110_div_3_Template", "AccountSetupComponent_mat_card_110_div_4_Template", "AccountSetupComponent_mat_card_110_div_5_Template", "AccountSetupComponent_mat_card_110_div_6_Template", "AccountSetupComponent_mat_card_110_div_7_Template", "ctx_r8", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_21_listener", "AccountSetupComponent_Template_input_keyup_42_listener", "$event", "AccountSetupComponent_mat_error_45_Template", "AccountSetupComponent_Template_input_keyup_49_listener", "AccountSetupComponent_mat_error_52_Template", "AccountSetupComponent_Template_input_keyup_57_listener", "AccountSetupComponent_mat_error_60_Template", "AccountSetupComponent_Template_button_click_71_listener", "AccountSetupComponent_div_76_Template", "AccountSetupComponent_Template_button_click_78_listener", "_r35", "_r6", "ɵɵreference", "click", "AccountSetupComponent_div_79_Template", "AccountSetupComponent_mat_icon_80_Template", "AccountSetupComponent_Template_input_change_82_listener", "AccountSetupComponent_mat_error_84_Template", "AccountSetupComponent_mat_card_110_Template", "ɵɵpureFunction0", "_c2", "_c3", "<PERSON><PERSON><PERSON><PERSON>", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i15", "MatTooltip", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i16", "MatRadioGroup", "MatRadioButton", "i17", "MatButton", "MatIconButton", "i18", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i19", "MatProgressBar", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport {ProgressBarMode, MatProgressBarModule} from '@angular/material/progress-bar';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // Chat bot related properties\n  showChatBot: boolean = false;\n  chatBotMinimized: boolean = false;\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  toggleChatBot(): void {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n          <h3 class=\"form-section-title\">Account Information</h3>\n          <div class=\"compact-form-grid\">\n            <!-- First row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant Name</mat-label>\n                <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                <mat-icon matSuffix>business</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant ID</mat-label>\n                <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                <mat-icon matSuffix>fingerprint</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                  Tenant ID already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Account Number</mat-label>\n                <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                <mat-icon matSuffix>account_balance</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                  Account number already exists\n                </mat-error>\n              </mat-form-field>\n      </div>\n\n            <!-- Second row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>G-Sheet</mat-label>\n                <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                  (keyup)=\"checkGSheet($event)\">\n                <mat-icon matSuffix>table_chart</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                  G-Sheet number already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Email</mat-label>\n                <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                <mat-icon matSuffix>email</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Password</mat-label>\n                <input formControlName=\"password\" matInput placeholder=\"Enter password\" [type]=\"hidePassword ? 'password' : 'text'\" />\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n              </mat-form-field>\n      </div>\n\n            <!-- Third row with logo and status options -->\n            <div class=\"form-row logo-status-row\">\n              <!-- Logo upload -->\n              <div class=\"compact-logo-section\">\n                <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                  <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                </div>\n                <div class=\"logo-actions\">\n                  <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                    <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border\" role=\"status\">\n                      <span class=\"sr-only\">Loading...</span>\n                    </div>\n                    <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                    Logo\n                  </button>\n                  <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\" multiple>\n                  <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                    Please upload a logo\n                  </mat-error>\n                </div>\n              </div>\n\n            </div>\n\n            <!-- Status options in the third row -->\n            <div class=\"compact-status-section\">\n              <div class=\"status-option\">\n                <label class=\"status-label\">Account</label>\n                <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                  <mat-radio-button value=\"yes\" color=\"primary\">Active</mat-radio-button>\n                  <mat-radio-button value=\"no\" color=\"primary\">Inactive</mat-radio-button>\n                </mat-radio-group>\n              </div>\n\n              <div class=\"status-option\">\n                <label class=\"status-label\">Forecast</label>\n                <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                  <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n                  <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n                </mat-radio-group>\n              </div>\n\n              <div class=\"status-option\">\n                <label class=\"status-label\">Sales</label>\n                <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                  <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n                  <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n                </mat-radio-group>\n              </div>\n            </div>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n<!-- AI Data Download Section - Shows after tenant creation -->\n<mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n  <div class=\"text-center p-2 my-2 bottomTitles\">AI-Powered Data Generation</div>\n\n  <!-- Initial state - before starting process -->\n  <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed && !showChatBot\" class=\"ai-intro-panel\">\n    <div class=\"row align-items-center\">\n      <div class=\"col-md-8\">\n        <h3><mat-icon class=\"ai-icon\">auto_awesome</mat-icon> Generate AI-Powered Datasets</h3>\n        <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.</p>\n        <p><strong>Note:</strong> This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.</p>\n      </div>\n      <div class=\"col-md-4 text-center\">\n        <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"start-ai-btn\">\n          <mat-icon>play_arrow</mat-icon>\n          Get Started\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Chat Bot Section -->\n  <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n    <div class=\"chat-bot-header\">\n      <h3>\n        <mat-icon class=\"chat-bot-icon\">smart_toy</mat-icon>\n        Restaurant Information Assistant\n      </h3>\n      <div class=\"chat-bot-actions\">\n        <button mat-icon-button (click)=\"toggleChatBot()\" matTooltip=\"{{ chatBotMinimized ? 'Expand' : 'Minimize' }}\">\n          <mat-icon>{{ chatBotMinimized ? 'expand_more' : 'expand_less' }}</mat-icon>\n        </button>\n        <button mat-icon-button (click)=\"startAIProcessing()\" matTooltip=\"Skip to AI Processing\">\n          <mat-icon>skip_next</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div [ngClass]=\"{'chat-bot-minimized': chatBotMinimized}\">\n      <p class=\"chat-bot-description\" *ngIf=\"!chatBotMinimized\">\n        Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n      </p>\n\n      <app-chat-bot\n        *ngIf=\"!chatBotMinimized\"\n        [tenantId]=\"registrationForm.value.tenantId\"\n        [tenantName]=\"registrationForm.value.tenantName\">\n      </app-chat-bot>\n    </div>\n  </div>\n\n  <!-- Processing state -->\n  <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n    <h3 class=\"processing-title\">\n      <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n      Processing Your Data\n    </h3>\n\n    <!-- Progress indicator -->\n    <div class=\"progress-container\">\n      <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n      <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n    </div>\n\n    <!-- Estimated time -->\n    <div class=\"estimated-time\">\n      <mat-icon class=\"icon\">access_time</mat-icon>\n      <span *ngIf=\"estimatedTimeRemaining > 60\">\n        Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n      </span>\n      <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n        Estimated time remaining: less than a minute\n      </span>\n      <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n        Calculating...\n      </span>\n    </div>\n\n    <!-- Processing steps -->\n    <div class=\"processing-steps\">\n      <div *ngFor=\"let step of downloadSteps; let i = index\"\n           class=\"step-row\"\n           [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n        <div class=\"step-status\">\n          <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n          <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n        </div>\n\n        <div class=\"step-details\">\n          <div class=\"step-name\">{{step.name}}</div>\n          <div class=\"step-description\">{{step.description}}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Helpful tips section -->\n    <div class=\"tips-section\">\n      <div class=\"tip-header\">\n        <mat-icon>lightbulb</mat-icon>\n        <span>Did You Know?</span>\n      </div>\n      <div class=\"tip-content\">\n        AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights\n      </div>\n    </div>\n  </div>\n\n  <!-- Download complete state -->\n  <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n    <div class=\"success-header\">\n      <mat-icon class=\"success-icon\">task_alt</mat-icon>\n      <h3>Processing Complete!</h3>\n    </div>\n\n    <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.</p>\n\n    <div class=\"row download-options\">\n      <!-- Inventory Dataset Card -->\n      <div class=\"col-md-6 mb-3\">\n        <mat-card class=\"download-card\">\n          <mat-card-header>\n            <div mat-card-avatar class=\"inventory-icon\">\n              <mat-icon>inventory_2</mat-icon>\n            </div>\n            <mat-card-title>Inventory Dataset</mat-card-title>\n            <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\">\n              <mat-icon>download</mat-icon> Download\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n\n      <!-- Packaging Dataset Card -->\n      <div class=\"col-md-6 mb-3\">\n        <mat-card class=\"download-card\">\n          <mat-card-header>\n            <div mat-card-avatar class=\"packaging-icon\">\n              <mat-icon>category</mat-icon>\n            </div>\n            <mat-card-title>Packaging Dataset</mat-card-title>\n            <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n          </mat-card-header>\n\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\">\n              <mat-icon>download</mat-icon> Download\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error state -->\n  <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n    <div class=\"error-header\">\n      <mat-icon class=\"error-icon\">error_outline</mat-icon>\n      <h3>Processing Failed</h3>\n    </div>\n\n    <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.</p>\n\n    <div class=\"error-actions\">\n      <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\">\n        <mat-icon>refresh</mat-icon> Try Again\n      </button>\n    </div>\n  </div>\n</mat-card>\n</div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAAyBC,oBAAoB,QAAO,gCAAgC;AACpF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICuBZC,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAsBZH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAIlBT,EAAA,CAAAC,cAAA,cAAqE;IAC7CD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEzCH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAI/DH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IA0C5BH,EAAA,CAAAC,cAAA,cAA2G;IAGvED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvFH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLAA0K;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjLH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,4IAAmI;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnKH,EAAA,CAAAC,cAAA,eAAkC;IACUD,EAAA,CAAAU,UAAA,mBAAAC,2EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAuBXH,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAEJH,EAAA,CAAAI,SAAA,uBAIe;;;;IAFbJ,EAAA,CAAAM,UAAA,aAAAY,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;;;;;;;IAvBlDtB,EAAA,CAAAC,cAAA,cAA4G;IAGtED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA8B;IACJD,EAAA,CAAAU,UAAA,mBAAAa,0EAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAS,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC/C1B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7EH,EAAA,CAAAC,cAAA,kBAAyF;IAAjED,EAAA,CAAAU,UAAA,mBAAAiB,2EAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAY,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnD7B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAKpCH,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAA8B,UAAA,KAAAC,sDAAA,gBAEI;IAEJ/B,EAAA,CAAA8B,UAAA,KAAAE,iEAAA,2BAIe;IACjBhC,EAAA,CAAAG,YAAA,EAAM;;;;IAnBgDH,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAiC,qBAAA,eAAAC,OAAA,CAAAC,gBAAA,yBAA2D;IACjGnC,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAoC,iBAAA,CAAAF,OAAA,CAAAC,gBAAA,iCAAsD;IAQjEnC,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAJ,OAAA,CAAAC,gBAAA,EAAoD;IACtBnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;IAKrDnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;;;;;IAuB1BnC,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuC,kBAAA,gCAAAvC,EAAA,CAAAwC,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACA1C,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAUHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,cAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAT3FH,EAAA,CAAAC,cAAA,cAE0I;IAGtID,EAAA,CAAA8B,UAAA,IAAAa,mEAAA,uBAAwD;IACxD3C,EAAA,CAAA8B,UAAA,IAAAc,8DAAA,kBAEM;IACN5C,EAAA,CAAA8B,UAAA,IAAAe,mEAAA,uBAAuF;IACzF7C,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZvDH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAG1HpD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAA0C,QAAA,CAAAC,SAAA,CAAoB;IACzBjD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpCpD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7BpD,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAK,IAAA,CAAa;IACNrD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAM,WAAA,CAAoB;;;;;IA1C1DtD,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAA8B,UAAA,KAAAyB,yDAAA,mBAEO;IACPvD,EAAA,CAAA8B,UAAA,KAAA0B,yDAAA,mBAEO;IACPxD,EAAA,CAAA8B,UAAA,KAAA2B,yDAAA,mBAEO;IACTzD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAA8B,UAAA,KAAA4B,wDAAA,oBAgBM;IACR1D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAqD,OAAA,CAAAC,gBAAA,CAA0B;IACnC5D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAuC,kBAAA,KAAAoB,OAAA,CAAAC,gBAAA,eAA8B;IAMnD5D,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,MAAiC;IAGjC1C,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,QAAAiB,OAAA,CAAAjB,sBAAA,OAAgE;IAGhE1C,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,OAAkC;IAOnB1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAqD,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5C7D,EAAA,CAAAC,cAAA,cAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,4FAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEpHH,EAAA,CAAAC,cAAA,cAAkC;IAMdD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAoD,2EAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEjE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,gBAA2B;IAITD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAwD,2EAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAI,OAAA,GAAAnE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEpE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQnBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHAAiH;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9IH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAA2D,0EAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuD,OAAA,CAAAtD,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA3KfH,EAAA,CAAAC,cAAA,mBAA2D;IACVD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/EH,EAAA,CAAA8B,UAAA,IAAA0C,iDAAA,mBAcM;IAGNxE,EAAA,CAAA8B,UAAA,IAAA2C,iDAAA,mBA2BM;IAGNzE,EAAA,CAAA8B,UAAA,IAAA4C,iDAAA,mBAyDM;IAGN1E,EAAA,CAAA8B,UAAA,IAAA6C,iDAAA,mBA8CM;IAGN3E,EAAA,CAAA8B,UAAA,IAAA8C,iDAAA,mBAaM;IACR5E,EAAA,CAAAG,YAAA,EAAW;;;;IA1KHH,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAM,UAAA,UAAAuE,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAI,WAAA,CAA4E;IAiB5EjF,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAI,WAAA,KAAAJ,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,CAA2E;IA8B3EhF,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAC,aAAA,CAAmB;IA4DnB9E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAE,gBAAA,CAAsB;IAiDtB/E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAG,cAAA,CAAoB;;;;;;;;;ADtR5B,MAuBaE,qBAAqB;EAqChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IA/CpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAA7F,OAAO,GAAkB,IAAI;IAC7B,KAAA8F,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAG3B,KAAA1C,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA2C,gBAAgB,GAAY,IAAI;IAChC,KAAA1B,aAAa,GAAY,KAAK;IAC9B,KAAAlB,gBAAgB,GAAW,CAAC;IAC5B,KAAAmB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA7B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAuC,WAAW,GAAY,KAAK;IAC5B,KAAA9C,gBAAgB,GAAY,KAAK;IAkB/B,IAAI,CAACsE,IAAI,GAAG,IAAI,CAACb,IAAI,CAACc,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACnB,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAEnD;IACA,IAAI,IAAI,CAAC0E,UAAU,EAAE;MACnB,IAAI,CAACe,WAAW,GAAG,IAAI,CAACf,UAAU,CAACgB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC1F,gBAAgB,GAAG,IAAI,CAACoE,EAAE,CAACwB,KAAK,CAAC;MACpC1F,QAAQ,EAAE,IAAI1C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAE;MAC3D1F,UAAU,EAAE,IAAI3C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAItI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAIvI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAIxI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIzI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAI1I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAI3I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAI5I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAI7I,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN;IACA,IAAI,CAAClB,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACmB,WAAW,CAAC,IAAI,CAAC5B,UAAU,CAAC6B,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAACrC,KAAK,CAACsC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACvB,UAAU,GAAG,IAAI,CAAC,CAAC;UAExB;UACA,IAAI,CAACZ,GAAG,CAACoC,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAAC5B,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAAC2C,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAAChC,UAAU,GAAG,KAAK,CAAC,CAAC;cACzBiC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAAC9C,MAAM,CAAC2C,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAAChH,gBAAgB,CAACsH,UAAU,CAAC;MAC/BnH,UAAU,EAAE6G,IAAI,CAAC7G,UAAU;MAC3BD,QAAQ,EAAE8G,IAAI,CAAC9G,QAAQ;MACvB4F,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAChH,OAAO,GAAG2H,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAACnB,aAAa,GAAG,CAAC;QACpBuC,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAAC3B,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACzD,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAAC5H,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAAC7H,gBAAgB,CAAC8H,gBAAgB,EAAE;MACxC,IAAI,CAACxD,MAAM,CAAC2C,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAAC/H,gBAAgB,CAACC,KAAK;MACxC,IAAI+H,GAAG,GAAQ;QACT9H,QAAQ,EAAE6H,MAAM,CAAC7H,QAAQ;QACzBC,UAAU,EAAE4H,MAAM,CAAC5H,UAAU;QAC7B6F,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAACnB,aAAa,CAAC+C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC/C,aAAa,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACjD,GAAG,CAAC0D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAACzC,MAAM,CAAC6D,mBAAmB,CAAC,IAAI,CAAC/C,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAAClB,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAC3C,iBAAiB,CAAC6D,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAAC9D,MAAM,CAAC2C,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACzI,QAAQ,KAAMqI,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3C,SAAS,KAAMuC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAMwC,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACjE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9BoE,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACpL,OAAO,GAAGmL,MAAM;YACrB,IAAI,CAACxK,gBAAgB,CAACsH,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAChH;YAAO,CAAE,CAAC;YACxD,IAAI,CAAC6F,aAAa,CAACwF,IAAI,CAAC;cAAEjD,GAAG,EAAE+C;YAAM,CAAE,CAAC;YACxC,IAAI,CAACxF,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACgD,aAAa,EAAE;UACzB,CAAC;UACDoC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACpI,aAAa,CAAC4G,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACjJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEClC,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACE,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAACvD,MAAM,CAAC2C,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACnD,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC9C,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;IAEvB;IACAsD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA1K,iBAAiBA,CAAA;IACf,IAAI,CAACiD,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAAClB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACqC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACiH,UAAU,EAAE;IACjB,MAAM5K,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACsE,GAAG,CAAC6G,eAAe,CAACnL,QAAQ,CAAC,CAACoL,IAAI,CACrC/M,UAAU,CAACgN,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAO9M,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAACgI,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC1E,OAAO,EAAE;QAChC,IAAI,CAAC2E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACzL,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACsL,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAjL,aAAaA,CAAA;IACX,IAAI,CAACS,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAiE,kBAAkBA,CAACzL,QAAgB;IACjC,IAAI,CAAC0L,aAAa,GAAGjN,QAAQ,CAAC,KAAK,CAAC,CAAC2M,IAAI,CACvC9M,SAAS,CAAC,MAAM,IAAI,CAACgG,GAAG,CAACqH,SAAS,CAAC3L,QAAQ,CAAC,CAAC,EAC7CzB,SAAS,CAAEgN,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAAClE,MAAM,KAAK,UAAU,IAAIkE,QAAQ,CAAClE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAAClE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACiE,mBAAmB,CAACC,QAAQ,CAACK,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACrJ,gBAAgB,GAAGgJ,QAAQ,CAACM,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAACxK,sBAAsB,GAAGkK,QAAQ,CAACO,wBAAwB,IAAI,CAAC;MAEpE,IAAIP,QAAQ,CAACQ,WAAW,KAAKC,SAAS,IAAIT,QAAQ,CAACQ,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACjK,UAAU,GAAGyJ,QAAQ,CAACQ,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,QAAQ,CAACQ,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAACzJ,aAAa,CAACuF,MAAM,EAAE;YACjC,IAAI,CAACvF,aAAa,CAACyJ,CAAC,CAAC,CAACrK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAAC4C,EAAE,CAACgD,aAAa,EAAE;MAEvB,IAAI+D,QAAQ,CAAClE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC6E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEb,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAY,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACc,EAAE,CAACgD,aAAa,EAAE;IACvB,IAAI,CAAC9C,QAAQ,CAAC0H,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAjB,mBAAmBA,CAACM,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACe,QAAQ,CAAC0H,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAChG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC5G,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAgD,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,GAAG,CAACmI,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC/EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGAtJ,iBAAiBA,CAAA;IACf,IAAI,CAACuB,GAAG,CAACmI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC7EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAhbWtI,qBAAqB,EAAAlF,EAAA,CAAAyO,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA3O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA9O,EAAA,CAAAyO,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAhP,EAAA,CAAAyO,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAAyO,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAApP,EAAA,CAAAyO,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAtP,EAAA,CAAAyO,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAxP,EAAA,CAAAyO,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA1P,EAAA,CAAAyO,iBAAA,CAAAzO,EAAA,CAAA2P,iBAAA,GAAA3P,EAAA,CAAAyO,iBAAA,CAgDVvP,eAAe,MAAAc,EAAA,CAAAyO,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAAyO,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAhD1B7K,qBAAqB;MAAA8K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlQ,EAAA,CAAAmQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAnF,GAAA;QAAA,IAAAmF,EAAA;;UClDlCzQ,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAgQ,wDAAA;YAAA,OAASpF,GAAA,CAAAvC,IAAA,EAAM;UAAA,EAAC;UACtB/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA6B;UAIUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAiQ,uDAAAC,MAAA;YAAA,OAAStF,GAAA,CAAA7B,aAAA,CAAAmH,MAAA,CAAqB;UAAA,EAAC;UADjF5Q,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAA+O,2CAAA,wBAEY;UACd7Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAoQ,uDAAAF,MAAA;YAAA,OAAStF,GAAA,CAAArB,cAAA,CAAA2G,MAAA,CAAsB;UAAA,EAAC;UADlF5Q,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAA8B,UAAA,KAAAiP,2CAAA,wBAEY;UACd/Q,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAsQ,uDAAAJ,MAAA;YAAA,OAAStF,GAAA,CAAApB,WAAA,CAAA0G,MAAA,CAAmB;UAAA,EAAC;UAD/B5Q,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAAmP,2CAAA,wBAEY;UACdjR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBAAsH;UACtHJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAwQ,wDAAA;YAAA,OAAA5F,GAAA,CAAAhF,YAAA,IAAAgF,GAAA,CAAAhF,YAAA;UAAA,EAAsC;UACtEtG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAAsC;UAGlCD,EAAA,CAAA8B,UAAA,KAAAqP,qCAAA,kBAEM;UACNnR,EAAA,CAAAC,cAAA,eAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA0Q,wDAAA;YAAApR,EAAA,CAAAY,aAAA,CAAAyQ,IAAA;YAAA,MAAAC,GAAA,GAAAtR,EAAA,CAAAuR,WAAA;YAAA,OAASvR,EAAA,CAAAgB,WAAA,CAAAsQ,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnExR,EAAA,CAAA8B,UAAA,KAAA2P,qCAAA,kBAEM;UACNzR,EAAA,CAAA8B,UAAA,KAAA4P,0CAAA,uBAA6D;UAC7D1R,EAAA,CAAAE,MAAA,cACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,qBAAiH;UAAnFD,EAAA,CAAAU,UAAA,oBAAAiR,wDAAAf,MAAA;YAAA,OAAUtF,GAAA,CAAAnB,cAAA,CAAAyG,MAAA,CAAsB;UAAA,EAAC;UAA/D5Q,EAAA,CAAAG,YAAA,EAAiH;UACjHH,EAAA,CAAA8B,UAAA,KAAA8P,2CAAA,wBAEY;UACd5R,EAAA,CAAAG,YAAA,EAAM;UAMVH,EAAA,CAAAC,cAAA,eAAoC;UAEJD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UAC7DD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACvEH,EAAA,CAAAC,cAAA,4BAA6C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UAC/DD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACzDD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAU1FH,EAAA,CAAA8B,UAAA,MAAA+P,2CAAA,uBA8KW;UACX7R,EAAA,CAAAG,YAAA,EAAM;;;UA5TGH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA8R,eAAA,KAAAC,GAAA,EAAkC;UAIlC/R,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA8R,eAAA,KAAAE,GAAA,EAAqC;UAMJhS,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,yBAAsC;UAClEvG,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,kCAA+C;UAOrDvG,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAuC,kBAAA,MAAA+I,GAAA,CAAA/E,UAAA,4BACF;UAO6BvG,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAgL,GAAA,CAAAnK,gBAAA,CAA8B;UAgBrCnB,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,aAAAkI,QAAA,mBAAiE;UAUjEjS,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,cAAAkI,QAAA,oBAAmE;UAanEjS,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,WAAAkI,QAAA,iBAA6D;UAaDjS,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAhF,YAAA,uBAA2C;UAEvGtG,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAAhF,YAAA,mCAAkD;UASnCtG,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9K,OAAA,CAAa;UAK9BR,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnF,kBAAA,CAAwB;UAGnBnG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAAgL,GAAA,CAAAnF,kBAAA,CAAyB;UAI1BnG,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAf,OAAA,IAAAsC,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAmI,OAAA,CAAkF;UAwCrGlS,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9E,gBAAA,CAAsB;;;qBD/G7B9H,YAAY,EAAAyT,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZxT,aAAa,EAAAyT,GAAA,CAAAC,OAAA,EACbzT,cAAc,EAAA0T,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACd/T,gBAAgB,EAAAgU,GAAA,CAAAC,UAAA,EAChBtU,WAAW,EAAAmQ,EAAA,CAAAoE,aAAA,EAAApE,EAAA,CAAAqE,oBAAA,EAAArE,EAAA,CAAAsE,eAAA,EAAAtE,EAAA,CAAAuE,oBAAA,EACXzU,mBAAmB,EAAAkQ,EAAA,CAAAwE,kBAAA,EAAAxE,EAAA,CAAAyE,eAAA,EACnBnU,cAAc,EAAAoU,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdrU,eAAe,EAAAsU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfvU,aAAa,EAAAwU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACb9U,eAAe,EACfL,oBAAoB,EAAAoV,GAAA,CAAAC,cAAA,EACpB/U,gBAAgB,EAChBL,YAAY,EAAAwP,EAAA,CAAA6F,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHzP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}