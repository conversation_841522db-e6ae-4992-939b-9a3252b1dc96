{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport { SmartDashboardUtility } from '../utilities/smart-dashboard.utility';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nclass SmartDashboardService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = environment.apiUrl;\n    this.apiEndpoint = '/api/smart-dashboard';\n    this.dashboardDataSubject = new BehaviorSubject(null);\n    this.dashboardData$ = this.dashboardDataSubject.asObservable();\n    this.loadingSubject = new BehaviorSubject(false);\n    this.loading$ = this.loadingSubject.asObservable();\n  }\n  getHeaders() {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getDashboardTabs() {\n    const url = `${this.apiUrl}${this.apiEndpoint}/dashboard-types`;\n    return this.http.get(url, {\n      headers: this.getHeaders()\n    });\n  }\n  getBaseDateOptions() {\n    const url = `${this.apiUrl}${this.apiEndpoint}/base-date-options`;\n    return this.http.get(url, {\n      headers: this.getHeaders()\n    });\n  }\n  generateDashboard(request) {\n    this.loadingSubject.next(true);\n    const url = `${this.apiUrl}${this.apiEndpoint}/generate-dummy-dashboard`;\n    return new Observable(observer => {\n      this.http.post(url, request, {\n        headers: this.getHeaders()\n      }).subscribe({\n        next: response => {\n          this.loadingSubject.next(false);\n          this.dashboardDataSubject.next(response.data);\n          observer.next(response);\n          observer.complete();\n        },\n        error: error => {\n          this.loadingSubject.next(false);\n          this.dashboardDataSubject.next(null);\n          observer.error(error);\n        }\n      });\n    });\n  }\n  getDashboardConfig() {\n    const url = `${this.apiUrl}${this.apiEndpoint}/config`;\n    return this.http.get(url, {\n      headers: this.getHeaders()\n    });\n  }\n  clearDashboardData() {\n    this.dashboardDataSubject.next(null);\n  }\n  getCurrentDashboardData() {\n    return this.dashboardDataSubject.value;\n  }\n  isLoading() {\n    return this.loadingSubject.value;\n  }\n  getSummaryItems(summary, dashboardType) {\n    if (!summary) {\n      return [];\n    }\n    if (dashboardType === 'purchase') {\n      return [{\n        icon: 'attach_money',\n        value: this.formatCurrency(summary['total_purchase_amount'] || 0),\n        label: 'Total Purchase Amount'\n      }, {\n        icon: 'shopping_cart',\n        value: summary['total_orders'] || 0,\n        label: 'Total Orders'\n      }, {\n        icon: 'trending_up',\n        value: this.formatCurrency(summary['avg_order_value'] || 0),\n        label: 'Average Order Value'\n      }, {\n        icon: 'business',\n        value: summary['top_vendor'] || 'N/A',\n        label: 'Top Vendor'\n      }];\n    } else if (dashboardType === 'sales') {\n      return [{\n        icon: 'attach_money',\n        value: this.formatCurrency(summary['total_sales'] || 0),\n        label: 'Total Sales'\n      }, {\n        icon: 'trending_up',\n        value: this.formatCurrency(summary['avg_daily_sales'] || 0),\n        label: 'Average Daily Sales'\n      }, {\n        icon: 'restaurant',\n        value: summary['top_selling_item'] || 'N/A',\n        label: 'Top Selling Item'\n      }, {\n        icon: 'schedule',\n        value: summary['peak_hour'] || 'N/A',\n        label: 'Peak Hour'\n      }];\n    }\n    return [];\n  }\n  formatCurrency(amount, config) {\n    return SmartDashboardUtility.formatCurrency(amount, config);\n  }\n  getChartConfig(chartType) {\n    return SmartDashboardUtility.getChartConfiguration(chartType);\n  }\n  getChartColors() {\n    return SmartDashboardUtility.getDefaultChartColors();\n  }\n  getDefaultDashboardTabs() {\n    return SmartDashboardUtility.getDefaultDashboardTabs();\n  }\n  getDefaultBaseDateOptions() {\n    return SmartDashboardUtility.getDefaultBaseDateOptions();\n  }\n  getDefaultDashboardConfig() {\n    return SmartDashboardUtility.getDefaultDashboardConfig();\n  }\n  validateFilters(filters) {\n    return SmartDashboardUtility.validateFilters(filters);\n  }\n  getEmptyStateMessage(dashboardType) {\n    return SmartDashboardUtility.getEmptyStateMessage(dashboardType);\n  }\n  getLoadingMessage(dashboardType) {\n    return SmartDashboardUtility.getLoadingMessage(dashboardType);\n  }\n  formatDate(date, config) {\n    return SmartDashboardUtility.formatDate(date, config);\n  }\n  formatNumber(value, config) {\n    return SmartDashboardUtility.formatNumber(value, config);\n  }\n  static {\n    this.ɵfac = function SmartDashboardService_Factory(t) {\n      return new (t || SmartDashboardService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SmartDashboardService,\n      factory: SmartDashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SmartDashboardService };", "map": {"version": 3, "names": ["HttpHeaders", "Observable", "BehaviorSubject", "environment", "SmartDashboardUtility", "SmartDashboardService", "constructor", "http", "authService", "apiUrl", "apiEndpoint", "dashboardDataSubject", "dashboardData$", "asObservable", "loadingSubject", "loading$", "getHeaders", "token", "getToken", "getDashboardTabs", "url", "get", "headers", "getBaseDateOptions", "generateDashboard", "request", "next", "observer", "post", "subscribe", "response", "data", "complete", "error", "getDashboardConfig", "clearDashboardData", "getCurrentDashboardData", "value", "isLoading", "getSummaryItems", "summary", "dashboardType", "icon", "formatCurrency", "label", "amount", "config", "getChartConfig", "chartType", "getChartConfiguration", "getChartColors", "getDefaultChartColors", "getDefaultDashboardTabs", "getDefaultBaseDateOptions", "getDefaultDashboardConfig", "validateFilters", "filters", "getEmptyStateMessage", "getLoadingMessage", "formatDate", "date", "formatNumber", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/services/smart-dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport { AuthService } from './auth.service';\nimport { SmartDashboardUtility } from '../utilities/smart-dashboard.utility';\n\nexport interface DashboardFilters {\n  locations: string[];\n  baseDate: string;\n  startDate: string;\n  endDate: string;\n}\n\nexport interface DashboardRequest {\n  filters: DashboardFilters;\n  user_query: string;\n  dashboard_type: string;\n  tenant_id: string;\n}\n\nexport interface ChartData {\n  id: string;\n  title: string;\n  type: string;\n  data: any;\n}\n\nexport interface DashboardSummary {\n  [key: string]: any;\n}\n\nexport interface DashboardResponse {\n  status: string;\n  data: {\n    charts: ChartData[];\n    summary: DashboardSummary;\n  };\n  filters_applied: DashboardFilters;\n  user_query: string;\n}\n\nexport interface DashboardTab {\n  label: string;\n  value: string;\n  description: string;\n  active: boolean;\n}\n\nexport interface BaseDate {\n  displayName: string;\n  value: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmartDashboardService {\n  private readonly apiUrl = environment.apiUrl;\n  private readonly apiEndpoint = '/api/smart-dashboard';\n\n  private dashboardDataSubject = new BehaviorSubject<any>(null);\n  public dashboardData$ = this.dashboardDataSubject.asObservable();\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  public loading$ = this.loadingSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  private getHeaders(): HttpHeaders {\n    const token = this.authService.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n\n  getDashboardTabs(): Observable<DashboardTab[]> {\n    const url = `${this.apiUrl}${this.apiEndpoint}/dashboard-types`;\n    return this.http.get<DashboardTab[]>(url, { headers: this.getHeaders() });\n  }\n\n  getBaseDateOptions(): Observable<BaseDate[]> {\n    const url = `${this.apiUrl}${this.apiEndpoint}/base-date-options`;\n    return this.http.get<BaseDate[]>(url, { headers: this.getHeaders() });\n  }\n\n  generateDashboard(request: DashboardRequest): Observable<DashboardResponse> {\n    this.loadingSubject.next(true);\n    const url = `${this.apiUrl}${this.apiEndpoint}/generate-dummy-dashboard`;\n\n    return new Observable(observer => {\n      this.http.post<DashboardResponse>(url, request, { headers: this.getHeaders() })\n        .subscribe({\n          next: (response) => {\n            this.loadingSubject.next(false);\n            this.dashboardDataSubject.next(response.data);\n            observer.next(response);\n            observer.complete();\n          },\n          error: (error) => {\n            this.loadingSubject.next(false);\n            this.dashboardDataSubject.next(null);\n            observer.error(error);\n          }\n        });\n    });\n  }\n\n  getDashboardConfig(): Observable<any> {\n    const url = `${this.apiUrl}${this.apiEndpoint}/config`;\n    return this.http.get<any>(url, { headers: this.getHeaders() });\n  }\n\n  clearDashboardData(): void {\n    this.dashboardDataSubject.next(null);\n  }\n\n  getCurrentDashboardData(): any {\n    return this.dashboardDataSubject.value;\n  }\n\n  isLoading(): boolean {\n    return this.loadingSubject.value;\n  }\n  getSummaryItems(summary: DashboardSummary, dashboardType: string): any[] {\n    if (!summary) {\n      return [];\n    }\n\n    if (dashboardType === 'purchase') {\n      return [\n        {\n          icon: 'attach_money',\n          value: this.formatCurrency(summary['total_purchase_amount'] || 0),\n          label: 'Total Purchase Amount'\n        },\n        {\n          icon: 'shopping_cart',\n          value: summary['total_orders'] || 0,\n          label: 'Total Orders'\n        },\n        {\n          icon: 'trending_up',\n          value: this.formatCurrency(summary['avg_order_value'] || 0),\n          label: 'Average Order Value'\n        },\n        {\n          icon: 'business',\n          value: summary['top_vendor'] || 'N/A',\n          label: 'Top Vendor'\n        }\n      ];\n    } else if (dashboardType === 'sales') {\n      return [\n        {\n          icon: 'attach_money',\n          value: this.formatCurrency(summary['total_sales'] || 0),\n          label: 'Total Sales'\n        },\n        {\n          icon: 'trending_up',\n          value: this.formatCurrency(summary['avg_daily_sales'] || 0),\n          label: 'Average Daily Sales'\n        },\n        {\n          icon: 'restaurant',\n          value: summary['top_selling_item'] || 'N/A',\n          label: 'Top Selling Item'\n        },\n        {\n          icon: 'schedule',\n          value: summary['peak_hour'] || 'N/A',\n          label: 'Peak Hour'\n        }\n      ];\n    }\n\n    return [];\n  }\n\n  private formatCurrency(amount: number, config?: any): string {\n    return SmartDashboardUtility.formatCurrency(amount, config);\n  }\n\n  getChartConfig(chartType: string): any {\n    return SmartDashboardUtility.getChartConfiguration(chartType);\n  }\n\n  getChartColors(): string[] {\n    return SmartDashboardUtility.getDefaultChartColors();\n  }\n\n  getDefaultDashboardTabs(): DashboardTab[] {\n    return SmartDashboardUtility.getDefaultDashboardTabs();\n  }\n\n  getDefaultBaseDateOptions(): BaseDate[] {\n    return SmartDashboardUtility.getDefaultBaseDateOptions();\n  }\n\n  getDefaultDashboardConfig(): any {\n    return SmartDashboardUtility.getDefaultDashboardConfig();\n  }\n\n  validateFilters(filters: any): boolean {\n    return SmartDashboardUtility.validateFilters(filters);\n  }\n\n  getEmptyStateMessage(dashboardType: string): { title: string; description: string } {\n    return SmartDashboardUtility.getEmptyStateMessage(dashboardType);\n  }\n\n  getLoadingMessage(dashboardType: string): { title: string; description: string } {\n    return SmartDashboardUtility.getLoadingMessage(dashboardType);\n  }\n\n  formatDate(date: string | Date, config?: any): string {\n    return SmartDashboardUtility.formatDate(date, config);\n  }\n\n  formatNumber(value: number, config?: any): string {\n    return SmartDashboardUtility.formatNumber(value, config);\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,qBAAqB,QAAQ,sCAAsC;;;;AAiD5E,MAGaC,qBAAqB;EAShCC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAVJ,KAAAC,MAAM,GAAGN,WAAW,CAACM,MAAM;IAC3B,KAAAC,WAAW,GAAG,sBAAsB;IAE7C,KAAAC,oBAAoB,GAAG,IAAIT,eAAe,CAAM,IAAI,CAAC;IACtD,KAAAU,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IACxD,KAAAC,cAAc,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IACrD,KAAAa,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACD,YAAY,EAAE;EAKjD;EAEKG,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAG,IAAI,CAACT,WAAW,CAACU,QAAQ,EAAE;IACzC,OAAO,IAAIlB,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUiB,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEAE,gBAAgBA,CAAA;IACd,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACX,MAAM,GAAG,IAAI,CAACC,WAAW,kBAAkB;IAC/D,OAAO,IAAI,CAACH,IAAI,CAACc,GAAG,CAAiBD,GAAG,EAAE;MAAEE,OAAO,EAAE,IAAI,CAACN,UAAU;IAAE,CAAE,CAAC;EAC3E;EAEAO,kBAAkBA,CAAA;IAChB,MAAMH,GAAG,GAAG,GAAG,IAAI,CAACX,MAAM,GAAG,IAAI,CAACC,WAAW,oBAAoB;IACjE,OAAO,IAAI,CAACH,IAAI,CAACc,GAAG,CAAaD,GAAG,EAAE;MAAEE,OAAO,EAAE,IAAI,CAACN,UAAU;IAAE,CAAE,CAAC;EACvE;EAEAQ,iBAAiBA,CAACC,OAAyB;IACzC,IAAI,CAACX,cAAc,CAACY,IAAI,CAAC,IAAI,CAAC;IAC9B,MAAMN,GAAG,GAAG,GAAG,IAAI,CAACX,MAAM,GAAG,IAAI,CAACC,WAAW,2BAA2B;IAExE,OAAO,IAAIT,UAAU,CAAC0B,QAAQ,IAAG;MAC/B,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAoBR,GAAG,EAAEK,OAAO,EAAE;QAAEH,OAAO,EAAE,IAAI,CAACN,UAAU;MAAE,CAAE,CAAC,CAC5Ea,SAAS,CAAC;QACTH,IAAI,EAAGI,QAAQ,IAAI;UACjB,IAAI,CAAChB,cAAc,CAACY,IAAI,CAAC,KAAK,CAAC;UAC/B,IAAI,CAACf,oBAAoB,CAACe,IAAI,CAACI,QAAQ,CAACC,IAAI,CAAC;UAC7CJ,QAAQ,CAACD,IAAI,CAACI,QAAQ,CAAC;UACvBH,QAAQ,CAACK,QAAQ,EAAE;QACrB,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnB,cAAc,CAACY,IAAI,CAAC,KAAK,CAAC;UAC/B,IAAI,CAACf,oBAAoB,CAACe,IAAI,CAAC,IAAI,CAAC;UACpCC,QAAQ,CAACM,KAAK,CAACA,KAAK,CAAC;QACvB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAAA;IAChB,MAAMd,GAAG,GAAG,GAAG,IAAI,CAACX,MAAM,GAAG,IAAI,CAACC,WAAW,SAAS;IACtD,OAAO,IAAI,CAACH,IAAI,CAACc,GAAG,CAAMD,GAAG,EAAE;MAAEE,OAAO,EAAE,IAAI,CAACN,UAAU;IAAE,CAAE,CAAC;EAChE;EAEAmB,kBAAkBA,CAAA;IAChB,IAAI,CAACxB,oBAAoB,CAACe,IAAI,CAAC,IAAI,CAAC;EACtC;EAEAU,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACzB,oBAAoB,CAAC0B,KAAK;EACxC;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACxB,cAAc,CAACuB,KAAK;EAClC;EACAE,eAAeA,CAACC,OAAyB,EAAEC,aAAqB;IAC9D,IAAI,CAACD,OAAO,EAAE;MACZ,OAAO,EAAE;;IAGX,IAAIC,aAAa,KAAK,UAAU,EAAE;MAChC,OAAO,CACL;QACEC,IAAI,EAAE,cAAc;QACpBL,KAAK,EAAE,IAAI,CAACM,cAAc,CAACH,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACjEI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,eAAe;QACrBL,KAAK,EAAEG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;QACnCI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,aAAa;QACnBL,KAAK,EAAE,IAAI,CAACM,cAAc,CAACH,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC3DI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,UAAU;QAChBL,KAAK,EAAEG,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK;QACrCI,KAAK,EAAE;OACR,CACF;KACF,MAAM,IAAIH,aAAa,KAAK,OAAO,EAAE;MACpC,OAAO,CACL;QACEC,IAAI,EAAE,cAAc;QACpBL,KAAK,EAAE,IAAI,CAACM,cAAc,CAACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACvDI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,aAAa;QACnBL,KAAK,EAAE,IAAI,CAACM,cAAc,CAACH,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC3DI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,YAAY;QAClBL,KAAK,EAAEG,OAAO,CAAC,kBAAkB,CAAC,IAAI,KAAK;QAC3CI,KAAK,EAAE;OACR,EACD;QACEF,IAAI,EAAE,UAAU;QAChBL,KAAK,EAAEG,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK;QACpCI,KAAK,EAAE;OACR,CACF;;IAGH,OAAO,EAAE;EACX;EAEQD,cAAcA,CAACE,MAAc,EAAEC,MAAY;IACjD,OAAO1C,qBAAqB,CAACuC,cAAc,CAACE,MAAM,EAAEC,MAAM,CAAC;EAC7D;EAEAC,cAAcA,CAACC,SAAiB;IAC9B,OAAO5C,qBAAqB,CAAC6C,qBAAqB,CAACD,SAAS,CAAC;EAC/D;EAEAE,cAAcA,CAAA;IACZ,OAAO9C,qBAAqB,CAAC+C,qBAAqB,EAAE;EACtD;EAEAC,uBAAuBA,CAAA;IACrB,OAAOhD,qBAAqB,CAACgD,uBAAuB,EAAE;EACxD;EAEAC,yBAAyBA,CAAA;IACvB,OAAOjD,qBAAqB,CAACiD,yBAAyB,EAAE;EAC1D;EAEAC,yBAAyBA,CAAA;IACvB,OAAOlD,qBAAqB,CAACkD,yBAAyB,EAAE;EAC1D;EAEAC,eAAeA,CAACC,OAAY;IAC1B,OAAOpD,qBAAqB,CAACmD,eAAe,CAACC,OAAO,CAAC;EACvD;EAEAC,oBAAoBA,CAAChB,aAAqB;IACxC,OAAOrC,qBAAqB,CAACqD,oBAAoB,CAAChB,aAAa,CAAC;EAClE;EAEAiB,iBAAiBA,CAACjB,aAAqB;IACrC,OAAOrC,qBAAqB,CAACsD,iBAAiB,CAACjB,aAAa,CAAC;EAC/D;EAEAkB,UAAUA,CAACC,IAAmB,EAAEd,MAAY;IAC1C,OAAO1C,qBAAqB,CAACuD,UAAU,CAACC,IAAI,EAAEd,MAAM,CAAC;EACvD;EAEAe,YAAYA,CAACxB,KAAa,EAAES,MAAY;IACtC,OAAO1C,qBAAqB,CAACyD,YAAY,CAACxB,KAAK,EAAES,MAAM,CAAC;EAC1D;;;uBAxKWzC,qBAAqB,EAAAyD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAArB9D,qBAAqB;MAAA+D,OAAA,EAArB/D,qBAAqB,CAAAgE,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA;;SAEPjE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}