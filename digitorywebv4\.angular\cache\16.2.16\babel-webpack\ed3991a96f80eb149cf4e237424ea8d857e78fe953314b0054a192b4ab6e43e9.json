{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/tooltip\";\nimport * as i16 from \"@angular/material/radio\";\nimport * as i17 from \"@angular/material/button\";\nimport * as i18 from \"@angular/material/card\";\nimport * as i19 from \"@angular/material/progress-bar\";\nfunction AccountSetupComponent_mat_error_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"img\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"apps\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\", 53);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_error_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 54);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"div\", 64)(3, \"h3\")(4, \"mat-icon\", 65);\n    i0.ɵɵtext(5, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 66)(14, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Get Started \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_4_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 77);\n    i0.ɵɵtext(1, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_4_app_chat_bot_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat-bot\", 78);\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"tenantId\", ctx_r18.registrationForm.value.tenantId)(\"tenantName\", ctx_r18.registrationForm.value.tenantName);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"chat-bot-minimized\": a0\n  };\n};\nfunction AccountSetupComponent_mat_card_119_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"h3\")(3, \"mat-icon\", 70);\n    i0.ɵɵtext(4, \"smart_toy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Information Assistant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.toggleChatBot());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.startAIProcessing());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"skip_next\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 74);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_119_div_4_p_14_Template, 2, 0, \"p\", 75);\n    i0.ɵɵtemplate(15, AccountSetupComponent_mat_card_119_div_4_app_chat_bot_15_Template, 1, 2, \"app-chat-bot\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r11.chatBotMinimized ? \"Expand\" : \"Minimize\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.chatBotMinimized ? \"expand_more\" : \"expand_less\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r11.chatBotMinimized));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.chatBotMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.chatBotMinimized);\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r22.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 93);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"span\", 101);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_119_div_5_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_119_div_5_div_16_div_3_Template, 3, 0, \"div\", 96);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 97)(6, \"div\", 98);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 99);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r26 = ctx.$implicit;\n    const i_r27 = ctx.index;\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c1, step_r26.completed, ctx_r25.activeStep === i_r27, !step_r26.completed && ctx_r25.activeStep !== i_r27));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r26.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r26.completed && ctx_r25.activeStep === i_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r26.completed && ctx_r25.activeStep !== i_r27);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r26.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r26.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"h3\", 80)(2, \"mat-icon\", 81);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 83);\n    i0.ɵɵelementStart(7, \"div\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 85)(10, \"mat-icon\", 86);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_119_div_5_span_12_Template, 3, 4, \"span\", 19);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_119_div_5_span_13_Template, 2, 0, \"span\", 19);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_119_div_5_span_14_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 88);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_119_div_5_div_16_Template, 10, 10, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 90)(18, \"div\", 91)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 92);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r12.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining > 0 && ctx_r12.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"mat-icon\", 104);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 105);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 106)(9, \"div\", 107)(10, \"mat-card\", 108)(11, \"mat-card-header\")(12, \"div\", 109)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-card-title\");\n    i0.ɵɵtext(16, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-card-subtitle\");\n    i0.ɵɵtext(18, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_6_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.downloadInventory());\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Download \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 107)(25, \"mat-card\", 108)(26, \"mat-card-header\")(27, \"div\", 111)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-card-title\");\n    i0.ɵɵtext(31, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-card-subtitle\");\n    i0.ɵɵtext(33, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card-actions\")(35, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_6_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.downloadPackaging());\n    });\n    i0.ɵɵelementStart(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Download \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"mat-icon\", 114);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 115);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 116)(9, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_119_div_7_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 55)(1, \"div\", 56);\n    i0.ɵɵtext(2, \"AI-Powered Data Generation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_119_div_3_Template, 18, 0, \"div\", 57);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_119_div_4_Template, 16, 7, \"div\", 58);\n    i0.ɵɵtemplate(5, AccountSetupComponent_mat_card_119_div_5_Template, 25, 6, \"div\", 59);\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_119_div_6_Template, 39, 0, \"div\", 60);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_119_div_7_Template, 13, 0, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed && !ctx_r9.showChatBot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showChatBot && !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadFailed);\n  }\n}\nconst _c2 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c3 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // Chat bot related properties\n    this.showChatBot = false;\n    this.chatBotMinimized = false;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  toggleChatBot() {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 120,\n      vars: 19,\n      consts: [[1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\"], [\"value\", \"no\", \"color\", \"primary\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-container\"], [1, \"logo-preview-container\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-placeholder\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"text-center\", \"p-2\", \"my-2\", \"bottomTitles\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"ai-intro-panel\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-8\"], [1, \"ai-icon\"], [1, \"col-md-4\", \"text-center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-ai-btn\", 3, \"click\"], [1, \"chat-bot-section\"], [1, \"chat-bot-header\"], [1, \"chat-bot-icon\"], [1, \"chat-bot-actions\"], [\"mat-icon-button\", \"\", 3, \"matTooltip\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Skip to AI Processing\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"chat-bot-description\", 4, \"ngIf\"], [3, \"tenantId\", \"tenantName\", 4, \"ngIf\"], [1, \"chat-bot-description\"], [3, \"tenantId\", \"tenantName\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"row\", \"download-options\"], [1, \"col-md-6\", \"mb-3\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r36 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 6)(16, \"mat-icon\", 4);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_21_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"mat-card\", 10)(27, \"mat-card-content\")(28, \"form\", 11)(29, \"h3\", 12);\n          i0.ɵɵtext(30, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"div\", 14)(33, \"mat-form-field\", 15)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 16);\n          i0.ɵɵelementStart(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 15)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 18);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_42_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-icon\", 17);\n          i0.ɵɵtext(44, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, AccountSetupComponent_mat_error_45_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 15)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 20);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_49_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-icon\", 17);\n          i0.ɵɵtext(51, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, AccountSetupComponent_mat_error_52_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 14)(54, \"mat-form-field\", 15)(55, \"mat-label\");\n          i0.ɵɵtext(56, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_57_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-icon\", 17);\n          i0.ɵɵtext(59, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, AccountSetupComponent_mat_error_60_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 15)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 22);\n          i0.ɵɵelementStart(65, \"mat-icon\", 17);\n          i0.ɵɵtext(66, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"mat-form-field\", 15)(68, \"mat-label\");\n          i0.ɵɵtext(69, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 23);\n          i0.ɵɵelementStart(71, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_71_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(72, \"mat-icon\");\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 25)(75, \"div\", 26)(76, \"div\", 27)(77, \"h4\", 28);\n          i0.ɵɵtext(78, \"Account Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 29)(80, \"div\", 30)(81, \"label\", 31);\n          i0.ɵɵtext(82, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"mat-radio-group\", 32)(84, \"mat-radio-button\", 33);\n          i0.ɵɵtext(85, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"mat-radio-button\", 34);\n          i0.ɵɵtext(87, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(88, \"div\", 30)(89, \"label\", 31);\n          i0.ɵɵtext(90, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"mat-radio-group\", 35)(92, \"mat-radio-button\", 33);\n          i0.ɵɵtext(93, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"mat-radio-button\", 34);\n          i0.ɵɵtext(95, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"div\", 30)(97, \"label\", 31);\n          i0.ɵɵtext(98, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"mat-radio-group\", 36)(100, \"mat-radio-button\", 33);\n          i0.ɵɵtext(101, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"mat-radio-button\", 34);\n          i0.ɵɵtext(103, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(104, \"div\", 37)(105, \"h4\", 28);\n          i0.ɵɵtext(106, \"Company Logo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"div\", 38)(108, \"div\", 39);\n          i0.ɵɵtemplate(109, AccountSetupComponent_div_109_Template, 2, 1, \"div\", 40);\n          i0.ɵɵtemplate(110, AccountSetupComponent_div_110_Template, 3, 0, \"div\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\", 42)(112, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_112_listener() {\n            i0.ɵɵrestoreView(_r36);\n            const _r7 = i0.ɵɵreference(117);\n            return i0.ɵɵresetView(_r7.click());\n          });\n          i0.ɵɵtemplate(113, AccountSetupComponent_mat_icon_113_Template, 2, 0, \"mat-icon\", 19);\n          i0.ɵɵtemplate(114, AccountSetupComponent_div_114_Template, 3, 0, \"div\", 44);\n          i0.ɵɵtext(115, \" Upload Logo \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"input\", 45, 46);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_116_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(118, AccountSetupComponent_mat_error_118_Template, 2, 0, \"mat-error\", 47);\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵtemplate(119, AccountSetupComponent_mat_card_119_Template, 8, 5, \"mat-card\", 48);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c3));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(36);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, i15.MatTooltip, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i16.MatRadioGroup, i16.MatRadioButton, MatButtonModule, i17.MatButton, i17.MatIconButton, MatCardModule, i18.MatCard, i18.MatCardActions, i18.MatCardAvatar, i18.MatCardContent, i18.MatCardHeader, i18.MatCardSubtitle, i18.MatCardTitle, MatSelectModule, MatProgressBarModule, i19.MatProgressBar, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 6px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 20px;\\n  background-color: #e8eaf6;\\n  border-bottom: 1px solid #c5cae9;\\n}\\n\\n.chat-bot-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #3f51b5;\\n}\\n\\n.chat-bot-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-bot-minimized[_ngcontent-%COMP%] {\\n  height: 0;\\n  overflow: hidden;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 500px;\\n  padding: 0 20px 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY3JtLW1hbmFnZW1lbnQvYWNjb3VudC1zZXR1cC9hY2NvdW50LXNldHVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsYUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQUFGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLHlCQUFBO0FBREo7QUFHSTtFQUNFLFdBQUE7RUFDQSxxQkFBQTtFQUNBLDJCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBRE47QUFHTTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGlCQUFBO0FBRFI7QUFJTTtFQUNFLGNBQUE7RUFDQSwwQkFBQTtBQUZSO0FBS007RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUFIUjtBQU9JO0VBQ0UsYUFBQTtFQUNBLFdBQUE7QUFMTjtBQVNJO0VBMUNGO0lBMkNJLGVBQUE7RUFOSjtFQVNNO0lBQ0UsZUFBQTtJQUNBLFlBQUE7SUFDQSxXQUFBO0VBUFI7RUFXSTtJQUNFLGFBQUE7RUFUTjtBQUNGO0FBY0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBWko7QUFjSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBWk47QUFjTTtFQUNFLGdCQUFBO0FBWlI7QUFtQkk7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7QUFqQk47QUFtQk07RUFDRSxhQUFBO0FBakJSO0FBc0JNO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsNkJBQUE7RUFDQSxtQkFBQTtBQXBCUjtBQXlCUTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBdkJWO0FBeUJVO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUF2Qlo7O0FBa0NBLDJCQUFBO0FBQ0E7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtFQU9BLHdCQUFBO0VBZ0JBLHlCQUFBO0VBVUEsNEJBQUE7RUE2SEEsMEJBQUE7RUFxRUEsdUJBQUE7QUE3UEY7QUE0QkU7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0FBMUJKO0FBOEJFOzs7O0VBSUUsZUFBQTtFQUNBLGtCQUFBO0FBNUJKO0FBK0JFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0FBN0JKO0FBaUNFO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0FBL0JKO0FBaUNJO0VBQ0UsaUJBQUE7QUEvQk47QUFvQ0U7RUE2Q0Usa0JBQUE7RUFvREEseUJBQUE7QUFqSUo7QUFpQ0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtFQU9BLDJDQUFBO0FBckNOO0FBZ0NNO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0FBOUJSO0FBa0NNO0VBQ0Usb0NBQUE7QUFoQ1I7QUFtQ007RUFDRTtJQUFPLHVCQUFBO0VBaENiO0VBaUNNO0lBQUsseUJBQUE7RUE5Qlg7QUFDRjtBQWlDSTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7QUEvQk47QUFpQ007RUFDRSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQS9CUjtBQW1DSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtBQWpDTjtBQW1DTTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtBQWpDUjtBQXNDSTtFQUNFLGNBQUE7QUFwQ047QUFzQ007RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQXBDUjtBQXNDUTtFQUNFLHdDQUFBO0FBcENWO0FBc0NVO0VBQ0UsY0FBQTtBQXBDWjtBQXdDUTtFQUNFLHlDQUFBO0VBQ0EsOEJBQUE7QUF0Q1Y7QUF5Q1E7RUFDRSw2QkFBQTtFQUNBLFlBQUE7QUF2Q1Y7QUEwQ1E7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBeENWO0FBMkNRO0VBQ0UsWUFBQTtBQXpDVjtBQTJDVTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7QUF6Q1o7QUE0Q1U7RUFDRSxXQUFBO0VBQ0EsZUFBQTtBQTFDWjtBQWlESTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLDBDQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtBQS9DTjtBQWlETTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBL0NSO0FBaURRO0VBQ0UsaUJBQUE7QUEvQ1Y7QUFtRE07RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUFqRFI7QUF3REk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtBQXRETjtBQXdETTtFQUNFLGNBQUE7RUFDQSxrQkFBQTtBQXREUjtBQXlETTtFQUNFLGNBQUE7RUFDQSxTQUFBO0FBdkRSO0FBMkRJO0VBQ0UsbUJBQUE7RUFDQSxlQUFBO0FBekROO0FBNERJO0VBQ0Usa0JBQUE7QUExRE47QUE0RE07RUFDRSxZQUFBO0VBQ0EsMEJBQUE7QUExRFI7QUE0RFE7RUFDRSwyQkFBQTtFQUNBLHlDQUFBO0FBMURWO0FBNkRRO0VBQ0UsbUJBQUE7QUEzRFY7QUE2RFU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7QUEzRFo7QUE2RFk7RUFDRSxjQUFBO0FBM0RkO0FBaUVVO0VBQ0Usa0JBQUE7QUEvRFo7QUFpRVk7RUFDRSxrQkFBQTtFQUNBLGVBQUE7QUEvRGQ7QUFvRVE7RUFDRSxzQkFBQTtBQWxFVjtBQTBFSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBeEVOO0FBMEVNO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBeEVSO0FBMkVNO0VBQ0UsY0FBQTtFQUNBLFNBQUE7QUF6RVI7QUE2RUk7RUFDRSxtQkFBQTtFQUNBLGVBQUE7QUEzRU47QUE4RUk7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0FBNUVOOztBQWlGQSwyQkFBQTtBQUNBO0VBR007SUFDRSxtQkFBQTtFQWhGTjtFQXFGSTtJQUNFLFdBQUE7SUFDQSxnQkFBQTtFQW5GTjtBQUNGO0FBd0ZBLCtCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQXRGRjs7QUF5RkE7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFVBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQXRGRjs7QUEwRkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUF2RkY7O0FBMEZBO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0NBQUE7QUF2RkY7O0FBMEZBO0VBQ0U7SUFBVyxZQUFBO0VBdEZYO0VBdUZBO0lBQU0sVUFBQTtFQXBGTjtBQUNGO0FBc0ZBLG9CQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHdDQUFBO0FBcEZGOztBQXVGQTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGdDQUFBO0FBcEZGOztBQXVGQTtFQUNFLFNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQXBGRjs7QUF1RkE7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFwRkY7O0FBdUZBO0VBQ0UsYUFBQTtFQUNBLFFBQUE7QUFwRkY7O0FBdUZBO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSxnQ0FBQTtBQXBGRjs7QUF1RkE7RUFDRSxTQUFBO0VBQ0EsZ0JBQUE7QUFwRkY7O0FBdUZBLDhDQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsYUFBQTtFQUNBLG9CQUFBO0FBcEZGIiwic291cmNlc0NvbnRlbnQiOlsiLy8gTWFpbiBjb250YWluZXIgc3R5bGVzXG4uYWNjb3VudC1zZXR1cC1jb250YWluZXIge1xuICBwYWRkaW5nOiAxNnB4O1xuICBtYXgtd2lkdGg6IDE0MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG5cbiAgLy8gQnJlYWRjcnVtYnMgc3R5bGVzXG4gIC5icmVhZGNydW1icyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDEycHg7XG4gICAgZm9udC1zaXplOiAxM3B4O1xuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7XG4gICAgcGFkZGluZzogOHB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgIGJveC1zaGFkb3c6IDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMGU0ZTg7XG5cbiAgICAuYnJlYWRjcnVtYi1pdGVtIHtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgICAgIC5icmVhZGNydW1iLWljb24ge1xuICAgICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICAgIGhlaWdodDogMThweDtcbiAgICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICAgIG1hcmdpbi1yaWdodDogNHB4O1xuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgY29sb3I6ICMzZjUxYjU7XG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgfVxuXG4gICAgICAmLmFjdGl2ZSB7XG4gICAgICAgIGNvbG9yOiAjM2Y1MWI1O1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5icmVhZGNydW1iLXNlcGFyYXRvciB7XG4gICAgICBtYXJnaW46IDAgOHB4O1xuICAgICAgY29sb3I6ICM5OTk7XG4gICAgfVxuXG4gICAgLy8gUmVzcG9uc2l2ZSBzdHlsZXMgZm9yIGJyZWFkY3J1bWJzXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG5cbiAgICAgIC5icmVhZGNydW1iLWl0ZW0ge1xuICAgICAgICAuYnJlYWRjcnVtYi1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5icmVhZGNydW1iLXNlcGFyYXRvciB7XG4gICAgICAgIG1hcmdpbjogMCA0cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gQ29tcGFjdCBoZWFkZXJcbiAgLmNvbXBhY3QtaGVhZGVyIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG5cbiAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogOHB4O1xuXG4gICAgICAuc2F2ZS1idXR0b24sIC5jYW5jZWwtYnV0dG9uIHtcbiAgICAgICAgbWluLXdpZHRoOiAxMDBweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDb250ZW50IHNlY3Rpb25cbiAgLmNvbnRlbnQtc2VjdGlvbiB7XG4gICAgLmZvcm0tY2FyZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgICAgbWF0LWNhcmQtY29udGVudCB7XG4gICAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICB9XG4gICAgfVxuICAgIC8vIEZvcm0gc3R5bGVzXG4gICAgLmFjY291bnQtZm9ybSB7XG4gICAgICAuZm9ybS1zZWN0aW9uLXRpdGxlIHtcbiAgICAgICAgZm9udC1zaXplOiAxNXB4O1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICBtYXJnaW46IDAgMCAxMnB4IDA7XG4gICAgICAgIGNvbG9yOiAjNTU1O1xuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTtcbiAgICAgICAgcGFkZGluZy1ib3R0b206IDZweDtcbiAgICAgIH1cblxuICAgICAgLy8gQ29tcGFjdCBmb3JtIGdyaWRcbiAgICAgIC5jb21wYWN0LWZvcm0tZ3JpZCB7XG4gICAgICAgIC5mb3JtLXJvdyB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgICAgICAgZ2FwOiAxMnB4O1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7XG5cbiAgICAgICAgICAuZm9ybS1maWVsZCB7XG4gICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgbWluLXdpZHRoOiAyMDBweDtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cblxuICAgIH1cbiAgfVxuXG59XG59XG5cbi8qIEFJIERhdGEgU2VjdGlvbiBTdHlsZXMgKi9cbi5haS1kYXRhLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIG1hcmdpbi10b3A6IDJyZW07XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAuYm90dG9tVGl0bGVzIHtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICB9XG5cbiAgLyogQ29tbW9uIHBhbmVsIHN0eWxlcyAqL1xuICAuYWktaW50cm8tcGFuZWwsXG4gIC5haS1wcm9jZXNzaW5nLXBhbmVsLFxuICAuYWktY29tcGxldGUtcGFuZWwsXG4gIC5haS1lcnJvci1wYW5lbCB7XG4gICAgcGFkZGluZzogMS41cmVtO1xuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgfVxuXG4gIC5haS1pY29uIHtcbiAgICBjb2xvcjogIzY3M2FiNztcbiAgICBmb250LXNpemU6IDI0cHg7XG4gICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgfVxuXG4gIC8qIFN0YXJ0IGJ1dHRvbiBzdHlsaW5nICovXG4gIC5zdGFydC1haS1idG4ge1xuICAgIHBhZGRpbmc6IDhweCAyNHB4O1xuICAgIGZvbnQtc2l6ZTogMTZweDtcblxuICAgIG1hdC1pY29uIHtcbiAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICAgIH1cbiAgfVxuXG4gIC8qIFByb2Nlc3NpbmcgcGFuZWwgc3R5bGVzICovXG4gIC5haS1wcm9jZXNzaW5nLXBhbmVsIHtcbiAgICAucHJvY2Vzc2luZy10aXRsZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcblxuICAgICAgLnByb2Nlc3NpbmctaWNvbiB7XG4gICAgICAgIG1hcmdpbi1yaWdodDogMTBweDtcbiAgICAgICAgY29sb3I6ICM2NzNhYjc7XG4gICAgICB9XG5cbiAgICAgIC8qIFJvdGF0aW9uIGFuaW1hdGlvbiBmb3IgcHJvY2Vzc2luZyBpY29uICovXG4gICAgICAucm90YXRpbmcge1xuICAgICAgICBhbmltYXRpb246IHJvdGF0ZSAycyBsaW5lYXIgaW5maW5pdGU7XG4gICAgICB9XG5cbiAgICAgIEBrZXlmcmFtZXMgcm90YXRlIHtcbiAgICAgICAgZnJvbSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XG4gICAgICAgIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5wcm9ncmVzcy1jb250YWluZXIge1xuICAgICAgbWFyZ2luOiAxLjVyZW0gMDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgLnByb2dyZXNzLWxhYmVsIHtcbiAgICAgICAgbWFyZ2luLXRvcDogOHB4O1xuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmVzdGltYXRlZC10aW1lIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvKiBTdGVwcyBzdHlsaW5nICovXG4gICAgLnByb2Nlc3Npbmctc3RlcHMge1xuICAgICAgbWFyZ2luOiAycmVtIDA7XG5cbiAgICAgIC5zdGVwLXJvdyB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAmLmNvbXBsZXRlZC1zdGVwIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDc2LCAxNzUsIDgwLCAwLjEpO1xuXG4gICAgICAgICAgLnN0ZXAtc3RhdHVzIG1hdC1pY29uIHtcbiAgICAgICAgICAgIGNvbG9yOiAjNGNhZjUwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgICYuYWN0aXZlLXN0ZXAge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTAzLCA1OCwgMTgzLCAwLjEpO1xuICAgICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzY3M2FiNztcbiAgICAgICAgfVxuXG4gICAgICAgICYucGVuZGluZy1zdGVwIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcbiAgICAgICAgICBvcGFjaXR5OiAwLjc7XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1zdGF0dXMge1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogMTZweDtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1kZXRhaWxzIHtcbiAgICAgICAgICBmbGV4LWdyb3c6IDE7XG5cbiAgICAgICAgICAuc3RlcC1uYW1lIHtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLnN0ZXAtZGVzY3JpcHRpb24ge1xuICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLyogVGlwcyBzZWN0aW9uIHN0eWxpbmcgKi9cbiAgICAudGlwcy1zZWN0aW9uIHtcbiAgICAgIG1hcmdpbi10b3A6IDJyZW07XG4gICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgzMywgMTUwLCAyNDMsIDAuMDUpO1xuICAgICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMjE5NmYzO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuXG4gICAgICAudGlwLWhlYWRlciB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGNvbG9yOiAjMjE5NmYzO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG5cbiAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC50aXAtY29udGVudCB7XG4gICAgICAgIGNvbG9yOiAjNTU1O1xuICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyogQ29tcGxldGUgcGFuZWwgc3R5bGVzICovXG4gIC5haS1jb21wbGV0ZS1wYW5lbCB7XG4gICAgLnN1Y2Nlc3MtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuXG4gICAgICAuc3VjY2Vzcy1pY29uIHtcbiAgICAgICAgY29sb3I6ICM0Y2FmNTA7XG4gICAgICAgIG1hcmdpbi1yaWdodDogMTJweDtcbiAgICAgIH1cblxuICAgICAgaDMge1xuICAgICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5zdWNjZXNzLW1lc3NhZ2Uge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICB9XG5cbiAgICAuZG93bmxvYWQtb3B0aW9ucyB7XG4gICAgICBtYXJnaW4tdG9wOiAxLjVyZW07XG5cbiAgICAgIC5kb3dubG9hZC1jYXJkIHtcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycztcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA2cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICAgIH1cblxuICAgICAgICBtYXQtY2FyZC1oZWFkZXIge1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG5cbiAgICAgICAgICAuaW52ZW50b3J5LWljb24sIC5wYWNrYWdpbmctaWNvbiB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcblxuICAgICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgICBjb2xvcjogIzY3M2FiNztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBtYXQtY2FyZC1jb250ZW50IHtcbiAgICAgICAgICB1bCB7XG4gICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7XG5cbiAgICAgICAgICAgIGxpIHtcbiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgbWF0LWNhcmQtYWN0aW9ucyB7XG4gICAgICAgICAgcGFkZGluZzogOHB4IDE2cHggMTZweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qIEVycm9yIHBhbmVsIHN0eWxlcyAqL1xuICAuYWktZXJyb3ItcGFuZWwge1xuICAgIC5lcnJvci1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAgIC5lcnJvci1pY29uIHtcbiAgICAgICAgY29sb3I6ICNmNDQzMzY7XG4gICAgICAgIG1hcmdpbi1yaWdodDogMTJweDtcbiAgICAgIH1cblxuICAgICAgaDMge1xuICAgICAgICBjb2xvcjogI2Y0NDMzNjtcbiAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5lcnJvci1tZXNzYWdlIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgfVxuXG4gICAgLmVycm9yLWFjdGlvbnMge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbWFyZ2luLXRvcDogMXJlbTtcbiAgICB9XG4gIH1cbn1cblxuLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5haS1kYXRhLXNlY3Rpb24ge1xuICAgIC5kb3dubG9hZC1vcHRpb25zIHtcbiAgICAgIC5jb2wtbWQtNiB7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmFpLWludHJvLXBhbmVsIHtcbiAgICAgIC5zdGFydC1haS1idG4ge1xuICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgbWFyZ2luLXRvcDogMXJlbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyogQWRkaXRpb25hbCB1dGlsaXR5IGNsYXNzZXMgKi9cbi5zcGlubmVyLWJvcmRlci1zbSB7XG4gIHdpZHRoOiAyMHB4O1xuICBoZWlnaHQ6IDIwcHg7XG59XG5cbi52aXN1YWxseS1oaWRkZW4ge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHdpZHRoOiAxcHg7XG4gIGhlaWdodDogMXB4O1xuICBtYXJnaW46IC0xcHg7XG4gIHBhZGRpbmc6IDA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGNsaXA6IHJlY3QoMCwgMCwgMCwgMCk7XG4gIGJvcmRlcjogMDtcbn1cblxuXG4uZXN0aW1hdGVkLXRpbWUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBjb2xvcjogI2NjYztcbn1cblxuLmNhbGN1bGF0aW5nIHtcbiAgZm9udC1zdHlsZTogaXRhbGljO1xuICBjb2xvcjogI2Y3Y2UyYTtcbiAgYW5pbWF0aW9uOiBmYWRlSW5PdXQgMS41cyBpbmZpbml0ZTtcbn1cblxuQGtleWZyYW1lcyBmYWRlSW5PdXQge1xuICAwJSwgMTAwJSB7IG9wYWNpdHk6IDAuNjsgfVxuICA1MCUgeyBvcGFjaXR5OiAxOyB9XG59XG5cbi8qIENoYXQgQm90IFN0eWxlcyAqL1xuLmNoYXQtYm90LXNlY3Rpb24ge1xuICBtYXJnaW46IDIwcHggMDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5O1xuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uY2hhdC1ib3QtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAxNXB4IDIwcHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNlOGVhZjY7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjYzVjYWU5O1xufVxuXG4uY2hhdC1ib3QtaGVhZGVyIGgzIHtcbiAgbWFyZ2luOiAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGNvbG9yOiAjM2Y1MWI1O1xufVxuXG4uY2hhdC1ib3QtaWNvbiB7XG4gIG1hcmdpbi1yaWdodDogMTBweDtcbiAgY29sb3I6ICMzZjUxYjU7XG59XG5cbi5jaGF0LWJvdC1hY3Rpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiA1cHg7XG59XG5cbi5jaGF0LWJvdC1kZXNjcmlwdGlvbiB7XG4gIHBhZGRpbmc6IDE1cHggMjBweDtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogIzU1NTtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcbn1cblxuLmNoYXQtYm90LW1pbmltaXplZCB7XG4gIGhlaWdodDogMDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLyogTWFrZSBzdXJlIHRoZSBjaGF0IGJvdCBoYXMgYSBmaXhlZCBoZWlnaHQgKi9cbmFwcC1jaGF0LWJvdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBoZWlnaHQ6IDUwMHB4O1xuICBwYWRkaW5nOiAwIDIwcHggMjBweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_119_div_3_Template_button_click_14_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "ctx_r18", "registrationForm", "value", "tenantId", "tenantName", "AccountSetupComponent_mat_card_119_div_4_Template_button_click_7_listener", "_r20", "ctx_r19", "toggleChatBot", "AccountSetupComponent_mat_card_119_div_4_Template_button_click_10_listener", "ctx_r21", "startAIProcessing", "ɵɵtemplate", "AccountSetupComponent_mat_card_119_div_4_p_14_Template", "AccountSetupComponent_mat_card_119_div_4_app_chat_bot_15_Template", "ɵɵpropertyInterpolate", "ctx_r11", "chatBotMinimized", "ɵɵtextInterpolate", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r22", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_119_div_5_div_16_div_3_Template", "AccountSetupComponent_mat_card_119_div_5_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c1", "step_r26", "completed", "ctx_r25", "activeStep", "i_r27", "name", "description", "AccountSetupComponent_mat_card_119_div_5_span_12_Template", "AccountSetupComponent_mat_card_119_div_5_span_13_Template", "AccountSetupComponent_mat_card_119_div_5_span_14_Template", "AccountSetupComponent_mat_card_119_div_5_div_16_Template", "ctx_r12", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_119_div_6_Template_button_click_20_listener", "_r32", "ctx_r31", "downloadInventory", "AccountSetupComponent_mat_card_119_div_6_Template_button_click_35_listener", "ctx_r33", "downloadPackaging", "AccountSetupComponent_mat_card_119_div_7_Template_button_click_9_listener", "_r35", "ctx_r34", "AccountSetupComponent_mat_card_119_div_3_Template", "AccountSetupComponent_mat_card_119_div_4_Template", "AccountSetupComponent_mat_card_119_div_5_Template", "AccountSetupComponent_mat_card_119_div_6_Template", "AccountSetupComponent_mat_card_119_div_7_Template", "ctx_r9", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_21_listener", "AccountSetupComponent_Template_input_keyup_42_listener", "$event", "AccountSetupComponent_mat_error_45_Template", "AccountSetupComponent_Template_input_keyup_49_listener", "AccountSetupComponent_mat_error_52_Template", "AccountSetupComponent_Template_input_keyup_57_listener", "AccountSetupComponent_mat_error_60_Template", "AccountSetupComponent_Template_button_click_71_listener", "AccountSetupComponent_div_109_Template", "AccountSetupComponent_div_110_Template", "AccountSetupComponent_Template_button_click_112_listener", "_r36", "_r7", "ɵɵreference", "click", "AccountSetupComponent_mat_icon_113_Template", "AccountSetupComponent_div_114_Template", "AccountSetupComponent_Template_input_change_116_listener", "AccountSetupComponent_mat_error_118_Template", "AccountSetupComponent_mat_card_119_Template", "ɵɵpureFunction0", "_c2", "_c3", "<PERSON><PERSON><PERSON><PERSON>", "touched", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i15", "MatTooltip", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i16", "MatRadioGroup", "MatRadioButton", "i17", "MatButton", "MatIconButton", "i18", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i19", "MatProgressBar", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport {ProgressBarMode, MatProgressBarModule} from '@angular/material/progress-bar';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // Chat bot related properties\n  showChatBot: boolean = false;\n  chatBotMinimized: boolean = false;\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  toggleChatBot(): void {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n          <h3 class=\"form-section-title\">Account Information</h3>\n          <div class=\"compact-form-grid\">\n            <!-- First row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant Name</mat-label>\n                <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                <mat-icon matSuffix>business</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant ID</mat-label>\n                <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                <mat-icon matSuffix>fingerprint</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                  Tenant ID already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Account Number</mat-label>\n                <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                <mat-icon matSuffix>account_balance</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                  Account number already exists\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Second row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>G-Sheet</mat-label>\n                <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                  (keyup)=\"checkGSheet($event)\">\n                <mat-icon matSuffix>table_chart</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                  G-Sheet number already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Email</mat-label>\n                <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                <mat-icon matSuffix>email</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Password</mat-label>\n                <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                  [type]=\"hidePassword ? 'password' : 'text'\" />\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n              </mat-form-field>\n            </div>\n\n            <!-- Settings section with two-column layout -->\n            <div class=\"settings-section\">\n              <div class=\"two-column-grid\">\n                <!-- Left column: Status options -->\n                <div class=\"left-column\">\n                  <h4 class=\"section-label\">Account Status</h4>\n                  <div class=\"status-options\">\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Account</label>\n                      <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\">Active</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\">Inactive</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                    \n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Forecast</label>\n                      <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                    \n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Sales</label>\n                      <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Right column: Logo upload -->\n                <div class=\"right-column\">\n                  <h4 class=\"section-label\">Company Logo</h4>\n                  <div class=\"logo-container\">\n                    <div class=\"logo-preview-container\">\n                      <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                        <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                      </div>\n                      <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                        <mat-icon>apps</mat-icon>\n                      </div>\n                    </div>\n                    <div class=\"logo-actions\">\n                      <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                        <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                        <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border\" role=\"status\">\n                          <span class=\"sr-only\">Loading...</span>\n                        </div>\n                        Upload Logo\n                      </button>\n                      <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n                      <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                        Please upload a logo\n                      </mat-error>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- AI Data Download Section - Shows after tenant creation -->\n    <mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n      <div class=\"text-center p-2 my-2 bottomTitles\">AI-Powered Data Generation</div>\n\n      <!-- Initial state - before starting process -->\n      <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed && !showChatBot\" class=\"ai-intro-panel\">\n        <div class=\"row align-items-center\">\n          <div class=\"col-md-8\">\n            <h3><mat-icon class=\"ai-icon\">auto_awesome</mat-icon> Generate AI-Powered Datasets</h3>\n            <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will\n              analyze your business needs and create personalized recommendations.</p>\n            <p><strong>Note:</strong> This process takes approximately 15 minutes to complete. You can continue using\n              the system while processing runs in the background.</p>\n          </div>\n          <div class=\"col-md-4 text-center\">\n            <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"start-ai-btn\">\n              <mat-icon>play_arrow</mat-icon>\n              Get Started\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Chat Bot Section -->\n      <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n        <div class=\"chat-bot-header\">\n          <h3>\n            <mat-icon class=\"chat-bot-icon\">smart_toy</mat-icon>\n            Restaurant Information Assistant\n          </h3>\n          <div class=\"chat-bot-actions\">\n            <button mat-icon-button (click)=\"toggleChatBot()\"\n              matTooltip=\"{{ chatBotMinimized ? 'Expand' : 'Minimize' }}\">\n              <mat-icon>{{ chatBotMinimized ? 'expand_more' : 'expand_less' }}</mat-icon>\n            </button>\n            <button mat-icon-button (click)=\"startAIProcessing()\" matTooltip=\"Skip to AI Processing\">\n              <mat-icon>skip_next</mat-icon>\n            </button>\n          </div>\n        </div>\n\n        <div [ngClass]=\"{'chat-bot-minimized': chatBotMinimized}\">\n          <p class=\"chat-bot-description\" *ngIf=\"!chatBotMinimized\">\n            Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n          </p>\n\n          <app-chat-bot *ngIf=\"!chatBotMinimized\" [tenantId]=\"registrationForm.value.tenantId\"\n            [tenantName]=\"registrationForm.value.tenantName\">\n          </app-chat-bot>\n        </div>\n      </div>\n\n      <!-- Processing state -->\n      <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n        <h3 class=\"processing-title\">\n          <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n          Processing Your Data\n        </h3>\n\n        <!-- Progress indicator -->\n        <div class=\"progress-container\">\n          <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n          <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n        </div>\n\n        <!-- Estimated time -->\n        <div class=\"estimated-time\">\n          <mat-icon class=\"icon\">access_time</mat-icon>\n          <span *ngIf=\"estimatedTimeRemaining > 60\">\n            Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n            Estimated time remaining: less than a minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n            Calculating...\n          </span>\n        </div>\n\n        <!-- Processing steps -->\n        <div class=\"processing-steps\">\n          <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n            [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n            <div class=\"step-status\">\n              <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n              <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                <span class=\"visually-hidden\">Loading...</span>\n              </div>\n              <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n            </div>\n\n            <div class=\"step-details\">\n              <div class=\"step-name\">{{step.name}}</div>\n              <div class=\"step-description\">{{step.description}}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Helpful tips section -->\n        <div class=\"tips-section\">\n          <div class=\"tip-header\">\n            <mat-icon>lightbulb</mat-icon>\n            <span>Did You Know?</span>\n          </div>\n          <div class=\"tip-content\">\n            AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n            insights\n          </div>\n        </div>\n      </div>\n\n      <!-- Download complete state -->\n      <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n        <div class=\"success-header\">\n          <mat-icon class=\"success-icon\">task_alt</mat-icon>\n          <h3>Processing Complete!</h3>\n        </div>\n\n        <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.\n        </p>\n\n        <div class=\"row download-options\">\n          <!-- Inventory Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"inventory-icon\">\n                  <mat-icon>inventory_2</mat-icon>\n                </div>\n                <mat-card-title>Inventory Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n\n          <!-- Packaging Dataset Card -->\n          <div class=\"col-md-6 mb-3\">\n            <mat-card class=\"download-card\">\n              <mat-card-header>\n                <div mat-card-avatar class=\"packaging-icon\">\n                  <mat-icon>category</mat-icon>\n                </div>\n                <mat-card-title>Packaging Dataset</mat-card-title>\n                <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n              </mat-card-header>\n\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\">\n                  <mat-icon>download</mat-icon> Download\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n        </div>\n      </div>\n\n      <!-- Error state -->\n      <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n        <div class=\"error-header\">\n          <mat-icon class=\"error-icon\">error_outline</mat-icon>\n          <h3>Processing Failed</h3>\n        </div>\n\n        <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n          load or connection issues.</p>\n\n        <div class=\"error-actions\">\n          <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\">\n            <mat-icon>refresh</mat-icon> Try Again\n          </button>\n        </div>\n      </div>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAAyBC,oBAAoB,QAAO,gCAAgC;AACpF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICuBZC,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAyDNH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAEtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKzBH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7DH,EAAA,CAAAC,cAAA,cAAqE;IAC7CD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IAgB5BH,EAAA,CAAAC,cAAA,cAA2G;IAGvED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvFH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLACmE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,4IAC2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EAAA,CAAAC,cAAA,eAAkC;IACUD,EAAA,CAAAU,UAAA,mBAAAC,2EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAwBXH,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAEJH,EAAA,CAAAI,SAAA,uBAEe;;;;IAFyBJ,EAAA,CAAAM,UAAA,aAAAY,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;;;;;;;IAtBxFtB,EAAA,CAAAC,cAAA,cAA4G;IAGtED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA8B;IACJD,EAAA,CAAAU,UAAA,mBAAAa,0EAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAS,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/C1B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7EH,EAAA,CAAAC,cAAA,kBAAyF;IAAjED,EAAA,CAAAU,UAAA,mBAAAiB,2EAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAY,IAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAY,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnD7B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAKpCH,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAA8B,UAAA,KAAAC,sDAAA,gBAEI;IAEJ/B,EAAA,CAAA8B,UAAA,KAAAE,iEAAA,2BAEe;IACjBhC,EAAA,CAAAG,YAAA,EAAM;;;;IAjBAH,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAiC,qBAAA,eAAAC,OAAA,CAAAC,gBAAA,yBAA2D;IACjDnC,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAoC,iBAAA,CAAAF,OAAA,CAAAC,gBAAA,iCAAsD;IAQjEnC,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAJ,OAAA,CAAAC,gBAAA,EAAoD;IACtBnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;IAIzCnC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAA4B,OAAA,CAAAC,gBAAA,CAAuB;;;;;IAsBtCnC,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuC,kBAAA,gCAAAvC,EAAA,CAAAwC,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACA1C,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,eAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,cACuI;IAGnID,EAAA,CAAA8B,UAAA,IAAAa,mEAAA,uBAAwD;IACxD3C,EAAA,CAAA8B,UAAA,IAAAc,8DAAA,kBAEM;IACN5C,EAAA,CAAA8B,UAAA,IAAAe,mEAAA,uBAAuF;IACzF7C,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvHpD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAA0C,QAAA,CAAAC,SAAA,CAAoB;IACzBjD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpCpD,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAA0C,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7BpD,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAK,IAAA,CAAa;IACNrD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAoC,iBAAA,CAAAY,QAAA,CAAAM,WAAA,CAAoB;;;;;IAzC1DtD,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAA8B,UAAA,KAAAyB,yDAAA,mBAEO;IACPvD,EAAA,CAAA8B,UAAA,KAAA0B,yDAAA,mBAEO;IACPxD,EAAA,CAAA8B,UAAA,KAAA2B,yDAAA,mBAEO;IACTzD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAA8B,UAAA,KAAA4B,wDAAA,oBAeM;IACR1D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAqD,OAAA,CAAAC,gBAAA,CAA0B;IACnC5D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAuC,kBAAA,KAAAoB,OAAA,CAAAC,gBAAA,eAA8B;IAMnD5D,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,MAAiC;IAGjC1C,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,QAAAiB,OAAA,CAAAjB,sBAAA,OAAgE;IAGhE1C,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAqD,OAAA,CAAAjB,sBAAA,OAAkC;IAOnB1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAqD,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5C7D,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,6FAC3B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,eAAkC;IAMdD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAoD,2EAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEjE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,gBAA2B;IAITD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAGtEH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAwD,2EAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAI,OAAA,GAAAnE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEpE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQnBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAA2D,0EAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuD,OAAA,CAAAtD,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA9KfH,EAAA,CAAAC,cAAA,mBAA2D;IACVD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/EH,EAAA,CAAA8B,UAAA,IAAA0C,iDAAA,mBAgBM;IAGNxE,EAAA,CAAA8B,UAAA,IAAA2C,iDAAA,mBA0BM;IAGNzE,EAAA,CAAA8B,UAAA,IAAA4C,iDAAA,mBAyDM;IAGN1E,EAAA,CAAA8B,UAAA,IAAA6C,iDAAA,mBA+CM;IAGN3E,EAAA,CAAA8B,UAAA,IAAA8C,iDAAA,mBAcM;IACR5E,EAAA,CAAAG,YAAA,EAAW;;;;IA7KHH,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAM,UAAA,UAAAuE,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAI,WAAA,CAA4E;IAmB5EjF,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAI,WAAA,KAAAJ,MAAA,CAAAC,aAAA,KAAAD,MAAA,CAAAE,gBAAA,KAAAF,MAAA,CAAAG,cAAA,CAA2E;IA6B3EhF,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAC,aAAA,CAAmB;IA4DnB9E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAE,gBAAA,CAAsB;IAkDtB/E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAuE,MAAA,CAAAG,cAAA,CAAoB;;;;;;;;;ADrShC,MAuBaE,qBAAqB;EAqChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IA/CpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAA7F,OAAO,GAAkB,IAAI;IAC7B,KAAA8F,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAG3B,KAAA1C,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA2C,gBAAgB,GAAY,IAAI;IAChC,KAAA1B,aAAa,GAAY,KAAK;IAC9B,KAAAlB,gBAAgB,GAAW,CAAC;IAC5B,KAAAmB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA7B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAuC,WAAW,GAAY,KAAK;IAC5B,KAAA9C,gBAAgB,GAAY,KAAK;IAkB/B,IAAI,CAACsE,IAAI,GAAG,IAAI,CAACb,IAAI,CAACc,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACnB,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAEnD;IACA,IAAI,IAAI,CAAC0E,UAAU,EAAE;MACnB,IAAI,CAACe,WAAW,GAAG,IAAI,CAACf,UAAU,CAACgB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC1F,gBAAgB,GAAG,IAAI,CAACoE,EAAE,CAACwB,KAAK,CAAC;MACpC1F,QAAQ,EAAE,IAAI1C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAE;MAC3D1F,UAAU,EAAE,IAAI3C,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAItI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAIvI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAIxI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIzI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAI1I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAI3I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAI5I,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACkI,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAI7I,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACkI,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN;IACA,IAAI,CAAClB,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACmB,WAAW,CAAC,IAAI,CAAC5B,UAAU,CAAC6B,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAACrC,KAAK,CAACsC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACvB,UAAU,GAAG,IAAI,CAAC,CAAC;UAExB;UACA,IAAI,CAACZ,GAAG,CAACoC,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAAC5B,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAAC2C,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAAChC,UAAU,GAAG,KAAK,CAAC,CAAC;cACzBiC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAAC9C,MAAM,CAAC2C,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAAChH,gBAAgB,CAACsH,UAAU,CAAC;MAC/BnH,UAAU,EAAE6G,IAAI,CAAC7G,UAAU;MAC3BD,QAAQ,EAAE8G,IAAI,CAAC9G,QAAQ;MACvB4F,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAChH,OAAO,GAAG2H,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAACnB,aAAa,GAAG,CAAC;QACpBuC,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAAC3B,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACzD,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAAC5H,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAAC7H,gBAAgB,CAAC8H,gBAAgB,EAAE;MACxC,IAAI,CAACxD,MAAM,CAAC2C,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAAC/H,gBAAgB,CAACC,KAAK;MACxC,IAAI+H,GAAG,GAAQ;QACT9H,QAAQ,EAAE6H,MAAM,CAAC7H,QAAQ;QACzBC,UAAU,EAAE4H,MAAM,CAAC5H,UAAU;QAC7B6F,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAACnB,aAAa,CAAC+C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC/C,aAAa,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACjD,GAAG,CAAC0D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAACzC,MAAM,CAAC6D,mBAAmB,CAAC,IAAI,CAAC/C,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAAClB,MAAM,CAACgD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAC3C,iBAAiB,CAAC6D,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAAC9D,MAAM,CAAC2C,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACzI,QAAQ,KAAMqI,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3C,SAAS,KAAMuC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACvI,KAAK;IACtC,IAAI+G,IAAI,GAAG,IAAI,CAAC3C,UAAU,CAACoB,WAAW,EAAE,CAACxF,KAAK;IAC9C,MAAMwI,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAMwC,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAACzI,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAAC7I,gBAAgB,CAAC4I,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACjE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9BoE,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAACpL,OAAO,GAAGmL,MAAM;YACrB,IAAI,CAACxK,gBAAgB,CAACsH,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAChH;YAAO,CAAE,CAAC;YACxD,IAAI,CAAC6F,aAAa,CAACwF,IAAI,CAAC;cAAEjD,GAAG,EAAE+C;YAAM,CAAE,CAAC;YACxC,IAAI,CAACxF,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACgD,aAAa,EAAE;UACzB,CAAC;UACDoC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACpI,aAAa,CAAC4G,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACjJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEClC,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACE,gBAAgB,CAAC6H,OAAO,EAAE;MACjC,IAAI,CAACvD,MAAM,CAAC2C,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACnD,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC9C,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;IAEvB;IACAsD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA1K,iBAAiBA,CAAA;IACf,IAAI,CAACiD,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAAClB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACqC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACiH,UAAU,EAAE;IACjB,MAAM5K,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACsE,GAAG,CAAC6G,eAAe,CAACnL,QAAQ,CAAC,CAACoL,IAAI,CACrC/M,UAAU,CAACgN,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAO9M,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAACgI,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC1E,OAAO,EAAE;QAChC,IAAI,CAAC2E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACzL,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACsL,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAjL,aAAaA,CAAA;IACX,IAAI,CAACS,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC0D,EAAE,CAACgD,aAAa,EAAE;EACzB;EAEAiE,kBAAkBA,CAACzL,QAAgB;IACjC,IAAI,CAAC0L,aAAa,GAAGjN,QAAQ,CAAC,KAAK,CAAC,CAAC2M,IAAI,CACvC9M,SAAS,CAAC,MAAM,IAAI,CAACgG,GAAG,CAACqH,SAAS,CAAC3L,QAAQ,CAAC,CAAC,EAC7CzB,SAAS,CAAEgN,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAAClE,MAAM,KAAK,UAAU,IAAIkE,QAAQ,CAAClE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAE+E,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAAClE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACiE,mBAAmB,CAACC,QAAQ,CAACK,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACrJ,gBAAgB,GAAGgJ,QAAQ,CAACM,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAACxK,sBAAsB,GAAGkK,QAAQ,CAACO,wBAAwB,IAAI,CAAC;MAEpE,IAAIP,QAAQ,CAACQ,WAAW,KAAKC,SAAS,IAAIT,QAAQ,CAACQ,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACjK,UAAU,GAAGyJ,QAAQ,CAACQ,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,QAAQ,CAACQ,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAACzJ,aAAa,CAACuF,MAAM,EAAE;YACjC,IAAI,CAACvF,aAAa,CAACyJ,CAAC,CAAC,CAACrK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAAC4C,EAAE,CAACgD,aAAa,EAAE;MAEvB,IAAI+D,QAAQ,CAAClE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC6E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEb,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAY,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACc,EAAE,CAACgD,aAAa,EAAE;IACvB,IAAI,CAAC9C,QAAQ,CAAC0H,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAjB,mBAAmBA,CAACM,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACe,QAAQ,CAAC0H,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAChG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC5G,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAgD,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,GAAG,CAACmI,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC/EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGAtJ,iBAAiBA,CAAA;IACf,IAAI,CAACuB,GAAG,CAACmI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwG,SAAS,CAC7EkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGrO,IAAI,CAACsO,IAAI,CAACH,YAAY,EAAE;UAAEvD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM2D,QAAQ,GAAG,GAAG,IAAI,CAACnN,gBAAgB,CAACC,KAAK,CAACC,QAAQ,sBAAsB;QAC9EtB,IAAI,CAACwO,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOhG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACApF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvC,QAAQ,CAAC0H,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAhbWtI,qBAAqB,EAAAlF,EAAA,CAAAyO,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA3O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7O,EAAA,CAAAyO,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA9O,EAAA,CAAAyO,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAhP,EAAA,CAAAyO,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAAyO,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAApP,EAAA,CAAAyO,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAtP,EAAA,CAAAyO,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAxP,EAAA,CAAAyO,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA1P,EAAA,CAAAyO,iBAAA,CAAAzO,EAAA,CAAA2P,iBAAA,GAAA3P,EAAA,CAAAyO,iBAAA,CAgDVvP,eAAe,MAAAc,EAAA,CAAAyO,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAAyO,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAhD1B7K,qBAAqB;MAAA8K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlQ,EAAA,CAAAmQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAnF,GAAA;QAAA,IAAAmF,EAAA;;UClDlCzQ,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAgQ,wDAAA;YAAA,OAASpF,GAAA,CAAAvC,IAAA,EAAM;UAAA,EAAC;UACtB/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA6B;UAIUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAiQ,uDAAAC,MAAA;YAAA,OAAStF,GAAA,CAAA7B,aAAA,CAAAmH,MAAA,CAAqB;UAAA,EAAC;UADjF5Q,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAA+O,2CAAA,wBAEY;UACd7Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAoQ,uDAAAF,MAAA;YAAA,OAAStF,GAAA,CAAArB,cAAA,CAAA2G,MAAA,CAAsB;UAAA,EAAC;UADlF5Q,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAA8B,UAAA,KAAAiP,2CAAA,wBAEY;UACd/Q,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAsQ,uDAAAJ,MAAA;YAAA,OAAStF,GAAA,CAAApB,WAAA,CAAA0G,MAAA,CAAmB;UAAA,EAAC;UAD/B5Q,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA8B,UAAA,KAAAmP,2CAAA,wBAEY;UACdjR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAwQ,wDAAA;YAAA,OAAA5F,GAAA,CAAAhF,YAAA,IAAAgF,GAAA,CAAAhF,YAAA;UAAA,EAAsC;UACtEtG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UAIED,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UAC7DD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACvEH,EAAA,CAAAC,cAAA,4BAA6C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UAC/DD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,4BAA6C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAI5EH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,2BAAyG;UACzDD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UACxEH,EAAA,CAAAC,cAAA,6BAA6C;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOhFH,EAAA,CAAAC,cAAA,gBAA0B;UACED,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,gBAA4B;UAExBD,EAAA,CAAA8B,UAAA,MAAAqP,sCAAA,kBAEM;UACNnR,EAAA,CAAA8B,UAAA,MAAAsP,sCAAA,kBAEM;UACRpR,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA2Q,yDAAA;YAAArR,EAAA,CAAAY,aAAA,CAAA0Q,IAAA;YAAA,MAAAC,GAAA,GAAAvR,EAAA,CAAAwR,WAAA;YAAA,OAASxR,EAAA,CAAAgB,WAAA,CAAAuQ,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnEzR,EAAA,CAAA8B,UAAA,MAAA4P,2CAAA,uBAA6D;UAC7D1R,EAAA,CAAA8B,UAAA,MAAA6P,sCAAA,kBAEM;UACN3R,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAAkR,yDAAAhB,MAAA;YAAA,OAAUtF,GAAA,CAAAnB,cAAA,CAAAyG,MAAA,CAAsB;UAAA,EAAC;UAA/D5Q,EAAA,CAAAG,YAAA,EAAwG;UACxGH,EAAA,CAAA8B,UAAA,MAAA+P,4CAAA,wBAEY;UACd7R,EAAA,CAAAG,YAAA,EAAM;UAWtBH,EAAA,CAAA8B,UAAA,MAAAgQ,2CAAA,uBAiLW;UACb9R,EAAA,CAAAG,YAAA,EAAM;;;UA5UCH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA+R,eAAA,KAAAC,GAAA,EAAkC;UAIlChS,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA+R,eAAA,KAAAE,GAAA,EAAqC;UAMJjS,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,yBAAsC;UAClEvG,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAA/E,UAAA,kCAA+C;UAOrDvG,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAuC,kBAAA,MAAA+I,GAAA,CAAA/E,UAAA,4BACF;UAO6BvG,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAgL,GAAA,CAAAnK,gBAAA,CAA8B;UAgBrCnB,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,aAAAmI,QAAA,mBAAiE;UAUjElS,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,cAAAmI,QAAA,oBAAmE;UAanElS,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,WAAAmI,QAAA,iBAA6D;UAcvElS,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAhF,YAAA,uBAA2C;UAEjCtG,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAoC,iBAAA,CAAAkJ,GAAA,CAAAhF,YAAA,mCAAkD;UA2C7BtG,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9K,OAAA,CAAa;UAGTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAAgL,GAAA,CAAA9K,OAAA,CAAc;UAMhCR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAAgL,GAAA,CAAAnF,kBAAA,CAAyB;UAC9BnG,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnF,kBAAA,CAAwB;UAMpBnG,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAf,OAAA,IAAAsC,GAAA,CAAAnK,gBAAA,CAAA4I,GAAA,SAAAoI,OAAA,CAAkF;UAcrGnS,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAgL,GAAA,CAAA9E,gBAAA,CAAsB;;;qBD5HjC9H,YAAY,EAAA0T,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZzT,aAAa,EAAA0T,GAAA,CAAAC,OAAA,EACb1T,cAAc,EAAA2T,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACdhU,gBAAgB,EAAAiU,GAAA,CAAAC,UAAA,EAChBvU,WAAW,EAAAmQ,EAAA,CAAAqE,aAAA,EAAArE,EAAA,CAAAsE,oBAAA,EAAAtE,EAAA,CAAAuE,eAAA,EAAAvE,EAAA,CAAAwE,oBAAA,EACX1U,mBAAmB,EAAAkQ,EAAA,CAAAyE,kBAAA,EAAAzE,EAAA,CAAA0E,eAAA,EACnBpU,cAAc,EAAAqU,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdtU,eAAe,EAAAuU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfxU,aAAa,EAAAyU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACb/U,eAAe,EACfL,oBAAoB,EAAAqV,GAAA,CAAAC,cAAA,EACpBhV,gBAAgB,EAChBL,YAAY,EAAAwP,EAAA,CAAA8F,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIH1P,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}