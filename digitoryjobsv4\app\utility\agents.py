from agno.agent import Agent
from agno.models.openai import OpenAIChat
from pydantic import BaseModel, Field
from typing import List

class RecipeInfo(BaseModel):
    name: str = Field(..., description="Recipe name")
    predicted_ingredients: List[str] = Field(..., description="Predicted ingredients for the recipe")

class Ingredient(BaseModel):
    # isModifier: bool
    # modifierName: Optional[str]
    # ConsumptionUOM: str = Field(..., description="Must be GM, ML, or NOS")
    # InitialWeight: float = Field(..., description="Initial weight in units")
    # Yield: float = Field(..., description="Yield percentage")
    # weightInUse: float = Field(..., description="Weight in use")
    ingredientName: str
    category: str
    subCategory: str
    ledgerAccount: str = Field(..., description="Ledger account name for tally reconciliation")
    issuedTo: str = Field(..., description="Comma-separated list of workareas this ingredient should be issued to")

class IngredientPrediction(BaseModel):
    recipe_name: str
    ingredients: List[Ingredient]

class BatchIngredientPrediction(BaseModel):
    predictions: List[IngredientPrediction]


class IngredientClassification(BaseModel):
    ingredientName: str
    category: str
    subCategory: str
    ledgerAccount: str
    issuedTo: str = Field(..., description="Comma-separated list of workareas this ingredient should be issued to")

class BatchIngredientClassification(BaseModel):
    classifications: List[IngredientClassification]


# class MenuItem(BaseModel):
#     original_name: str
#     cleaned_name: str

# class CleanMenuItems(BaseModel):
#     clean: List[MenuItem]

class CleanMenuItems(BaseModel):
    clean: List[str]

ingredient_predictor = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    description="Predict key ingredients for multiple recipes based on dish name and cuisine.",
    instructions = (
        "You are an elite Indian culinary expert specializing in precise ingredient analysis and inventory management. "
        "Your task is to convert restaurant menu items into comprehensive, standardized ingredient lists.\n\n"

        "### CRITICAL REQUIREMENTS:\n"
        "- Maintain ABSOLUTE ACCURACY for procurement and inventory alignment.\n"
        "- Use AUTHENTIC Indian culinary terminology when applicable.\n"
        "- ALL ingredient names MUST be CONSISTENT, SINGULAR, and GENERIC (e.g., 'TOMATO' not 'TOMATOES').\n"
        "- Standardize formatting: UPPERCASE, fix spelling errors, eliminate plurals.\n"
        "- Quantities must reflect REALISTIC restaurant usage volumes.\n"
        "- ALWAYS decompose composite ingredients into their base components.\n\n"

        "### MANDATORY CLASSIFICATION:\n"
        "- **Category** - EXACTLY ONE of:\n"
        "    - GENERAL, BEVERAGES, FOOD, LIQUOR, NON FOOD, TOBACCO\n"
        "- **Subcategory** - EXACTLY ONE of the following based on category:\n"
        "    - **FOOD** - GROCERY/PROVISION, IMPORTED GROCERY, POULTRY, MEAT, SEAFOOD, FROZEN, INDIAN VEGETABLE, IMPORTED VEGETABLE, DAIRY, FRUITS\n"
        "    - **BEVERAGES** - SYRUP, AERATED DRINKS, JUICES, CRUSHES\n"
        "    - **NON FOOD** - CCG, HOUSEKEEPING, CONSUMABLES, FUEL/GAS, OFFICE SUPPLIES, PACKAGING MATERIAL, PACKAGING, UNIFORM, MAINTENANCE\n"
        "    - **LIQUOR** - WHISKEY, RUM, VODKA, TEQUILA, CRAFT BEER, BOTTLED BEER, COGNAC, BRANDY, GIN, LIQUEUR, WINE, DRAUGHT BEER\n"
        "- **Ledger Account** - EXACTLY ONE of:\n"
        "    - FOOD INVENTORY, BEVERAGE INVENTORY, LIQUOR INVENTORY, NON FOOD INVENTORY, GENERAL INVENTORY\n"
        "- **IssuedTo** - Comma-separated list of ALL workareas where this ingredient should be issued to:\n"
        "    - You will be provided with a list of available workareas in the restaurant.\n"
        "    - CRITICAL: Prioritize completeness over precision - include ALL potentially relevant workareas.\n"
        "    - When in doubt, include the workarea - false positives are acceptable, missing mappings are not.\n"
        "    - Always use workareas from the provided list.\n"
        "    - Examples of common mappings:\n"
        "        - Lemon → \"KITCHEN,BAR,SERVICE AREA,PREP AREA\"\n"
        "        - Meat → \"KITCHEN,PREP AREA,STORE\"\n"
        "        - Vodka → \"BAR,STORE,SERVICE AREA\"\n"
        "        - Cleaning supplies → \"HOUSEKEEPING,STORE,KITCHEN,BAR\"\n\n"

        "### STRICT STANDARDIZATION RULES:\n"
        "- USE ONLY UPPERCASE for ALL ingredient names and classifications.\n"
        "- NEVER use plurals or inconsistent spelling variations.\n"
        "- BE SPECIFIC with ingredient types:\n"
        "    - 'TOMATO' → 'FRESH TOMATO' if listed as such in inventory\n"
        "    - 'BUTTER' → 'AMUL BUTTER' if Amul is a specific SKU\n"
        "- PRESERVE BRAND NAMES when specifically mentioned in menu items:\n"
        "    - 'SMIRNOFF MARTINI' → 'SMIRNOFF VODKA'\n"
        "    - 'JOHNNIE WALKER OLD FASHIONED' → 'JOHNNIE WALKER BLACK LABEL'\n"
        "- DEFAULT to procurement-standard terms when brands aren't specified.\n"
        "- ALWAYS specify varieties for general food items:\n"
        "    - 'PASTA' → 'PENNE PASTA'\n"
        "    - 'OIL' → 'SUNFLOWER OIL'\n"
        "    - 'RICE' → 'BASMATI RICE'\n"
        "- PRIORITIZE ingredients from existing inventory before suggesting new ones.\n\n"

        "### NEW BRAND-SPECIFIC REQUIREMENTS: !IMPORTANT \n"
        "- If an ingredient is predicted, ALWAYS include all top brands available in inventory as separate entries:\n"
        "    - Example: 'VODKA' → 'SMIRNOFF VODKA', 'ABSOLUT VODKA', 'GREY GOOSE VODKA'\n"
        "    - Example: 'BUTTER' → 'AMUL BUTTER', 'GOWARDHAN BUTTER', 'BRITANNIA BUTTER'\n"
        "- When no specific brand is mentioned, provide generic options along with top brands.\n"
        "- Include ALL possible variations that are commercially available and used in restaurant procurement.\n"
    ),
    structured_outputs=True,
    response_model=BatchIngredientPrediction
)


ingredient_classifier = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    description="Identifies category, subcategory, ledger account, and appropriate workareas for ingredients.",
    instructions=(
        """
        You are an AI agent specializing in ingredient classification. Your task is to analyze a list of ingredients and assign the following attributes:

        1. **Category**
            - Choose from the following predefined categories:
                - `GENERAL`, `BEVERAGES`, `FOOD`, `LIQUOR`, `NON FOOD`, `TOBACCO`
            - If no suitable category exists, create a new one that reflects the ingredient type.

        2. **Subcategory**
            - Use the following predefined subcategories:
                - **FOOD** - `GROCERY/PROVISION`, `IMPORTED GROCERY`, `POULTRY`, `MEAT`, `SEAFOOD`, `FROZEN`, `INDIAN VEGETABLE`, `IMPORTED VEGETABLE`, `DAIRY`, `FRUITS`
                - **BEVERAGES** - `SYRUP`, `AERATED DRINKS`, `JUICES`, `CRUSHES`
                - **NON FOOD** - `CCG`, `HOUSEKEEPING`, `CONSUMABLES`, `FUEL/GAS`, `OFFICE SUPPLIES`, `PACKAGING MATERIAL`, `PACKAGING`, `UNIFORM`, `MAINTENANCE`
                - **LIQUOR** - `WHISKEY`, `RUM`, `VODKA`, `TEQUILA`, `CRAFT BEER`, `BOTTLED BEER`, `COGNAC`, `BRANDY`, `GIN`, `LIQUEUR`, `WINE`, `DRAUGHT BEER`
            - If no suitable subcategory exists, create a new one that reflects the ingredient type.

        3. **Ledger Account**
            - Use the following ledger accounts:
                - **FOOD INVENTORY** - For food items
                - **BEVERAGE INVENTORY** - For beverages
                - **LIQUOR INVENTORY** - For liquor items
                - **NON FOOD INVENTORY** - For non-food items
                - **GENERAL INVENTORY** - For uncategorized or new items
            - If no suitable ledger exists, create a new one that reflects the ingredient type.

        4. **IssuedTo**
            - Provide a comma-separated list of ALL workareas where this ingredient should be issued to.
            - You will be provided with a list of available workareas in the restaurant.
            - CRITICAL: Prioritize completeness over precision - include ALL potentially relevant workareas.
            - When in doubt, include the workarea - false positives are acceptable, missing mappings are not.
            - Always use workareas from the provided list.
            - Examples of common mappings:
                - Lemon → "KITCHEN,BAR,SERVICE AREA,PREP AREA"
                - Meat → "KITCHEN,PREP AREA,STORE"
                - Vodka → "BAR,STORE,SERVICE AREA"
                - Cleaning supplies → "HOUSEKEEPING,STORE,KITCHEN,BAR"

        5. **Naming Consistency**
            - Use consistent uppercase formatting for all outputs (Category, subCategory, ledgerAccount, issuedTo).
            - Fix spelling and plural inconsistencies.
            - Avoid abbreviations unless they are commonly used terms.
            - For issuedTo, use standard restaurant workarea terminology.
        """
    ),


    structured_outputs=True,
    response_model=BatchIngredientClassification
)


menu_clean_up = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    description="Cleans and standardizes restaurant menu item names by removing unnecessary terms, correcting spelling, and simplifying for consistency.",
    instructions=(
        """
        ### You are an AI agent specializing in cleaning and simplifying restaurant menu names.
        Your goal is to retain the core identity of each dish while removing unnecessary terms, customizations, and descriptors.
        Cleaned names should be suitable for consistent inventory tracking and customer-facing menus.

        ### ✅ Rules:
        1. **Remove size and quantity indicators**
           - Example: "Half Kg", "750ml", "Glass", "Small", "Large" → Remove
           - Exception: Retain if the size defines a distinct product (e.g., "330ml Coke").

        2. **Retain distinctive brand or product names**
           - Example: "Amul Butter" → Retain
           - Example: "Kingfisher Beer" → Retain

        3. **Remove place names** unless they define the dish’s identity or preparation style.
           - ✅ Retain "Hyderabadi" from "Hyderabadi Biryani"
           - ❌ Remove "Kolkata" from "Kolkata Chicken Roll"

        4. **Remove subjective terms** like "Special", "Chef’s", "House", "Signature".
           - Example: "Chef’s Special Pasta" → "Pasta"

        5. **Remove add-ons and optional ingredients**
           - Example: "Burger with Fries" → "Burger"

        6. **Remove redundant cooking methods** unless they define the dish.
           - ✅ Retain "Tandoori" in "Tandoori Chicken"
           - ❌ Remove "Fried" from "Fried Rice" (since it's implied)

        7. **Remove serving style and combo/meal references**
           - Example: "Family Meal Deal" → "Meal"
           - Example: "Lunch Combo" → "Combo"

        8. **Remove dietary labels** unless essential for customer clarity.
           - Example: "Vegan Burger" → "Burger"
           - Exception: Retain if it’s a key product identifier (e.g., "Gluten-Free Bread").

        9. **Remove numbering and internal codes**
           - Example: "Item 123 - Chicken Tikka" → "Chicken Tikka"

        10. **Remove parenthetical descriptions** unless essential.
            - Example: "Chicken (Grilled)" → "Chicken"

        11. **Fix plural forms and spelling inconsistencies**
            - Example: "Tomatoes" → "Tomato"
            - Example: "Chilli" → "Chili"

        12. **Keep unique regional or specialty terms intact**
            - Example: "Schezwan Sauce" → Retain
            - Example: "Masala Dosa" → Retain

        13. **Preserve specific product or brand names** - Do **NOT** simplify into generic terms.
            - ❌ "Smirnoff Vodka" → Do NOT simplify to "Vodka"

        14. **Ensure consistent case formatting** - Use **Title Case** for final names.
            - Example: "Chicken biryani" → "Chicken Biryani"

        15. **Skip Already Processed Items** - If an item has already been processed, skip it.

        ### 🔥 Best Practices:
        - Consistency is key - Ingredient names should match actual inventory names.
        - Brand names matter - If Amul or Nestlé is critical for procurement, retain it.
        - Keep it natural - Names should feel familiar and clear to customers.
        - Be logical - Remove only what makes the name cleaner without losing clarity.
        """
    ),
    structured_outputs=True,
    response_model=CleanMenuItems
)


class MenuPrediction(BaseModel):
    menu_items: List[str] = Field(..., description="List of predicted menu items based on cuisine and restaurant information")


menu_predictor = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    # tools=[DuckDuckGoTools()],
    description="Generates clean, realistic, and location-adapted restaurant menu item names based on cuisines, alcohol/tobacco policy, and location.",

    instructions=[
        "Search your knowledge base first.",
        "If needed, search the web to fill in gaps or get current information.",

        # Input and Count Enforcement
        "You will receive cuisines, location, alcohol/tobacco policy, and the number of items per cuisine.",
        "You MUST generate EXACTLY the requested number of items per cuisine — not more, not less.",
        "If any cuisine falls short, you MUST continue generating additional items for that cuisine until the exact total is reached.",
        "Do not skip cuisines or move on early. Ensure all item counts are fully satisfied before finishing.",
        "Reach at least 90% per cuisine with authentic, research-based dishes before filling the remainder with creative, fusion, or regional adaptations.",

        # Research and Relevance
        "Search for menus of restaurants near the given location to identify regionally appropriate and trending dishes.",
        "Adapt dish names to reflect local ingredients, cooking styles, terminology, and dining culture where appropriate.",

        # Structure and Variety
        "Distribute items across starters, mains, sides, and desserts with a reasonable mix of veg and non-veg.",
        "If signature dishes are provided, include them in the appropriate cuisine and count.",

        # Alcohol & Tobacco
        "If alcohol is allowed, generate a separate bar list (not included in cuisine item counts).",
        "If tobacco is allowed, generate a separate tobacco list of 20-30 locally relevant items (e.g., hookah flavors, cigars).",
        "Do NOT include alcohol or tobacco items in the cuisine counts unless explicitly told to.",

        # Output Format (IMPORTANT)
        "Your output MUST be a flat, clean list of menu item names only — no categories, no descriptions, no numbering, and no formatting.",

        # Final Verification
        "Before finishing, count the number of items generated per cuisine.",
        "If any cuisine has fewer items than required, you MUST generate the remaining items immediately and append them before returning the final result.",
        "Do NOT return early. Always validate that all cuisine counts are fully met."
    ],
    structured_outputs=True,
    response_model=MenuPrediction
)