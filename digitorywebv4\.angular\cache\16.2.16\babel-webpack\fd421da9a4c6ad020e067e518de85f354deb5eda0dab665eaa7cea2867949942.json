{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nlet InventoryService = /*#__PURE__*/(() => {\n  class InventoryService {\n    constructor(http) {\n      this.http = http;\n      this.baseUrl = environment.baseUrl;\n      this.engineUrl = environment.engineUrl;\n      this.apiUrl = `${this.baseUrl}update-closing-dates`; //\n    }\n\n    postRegistration(registerObj) {\n      return this.http.post(`${this.baseUrl}`, registerObj);\n    }\n    getBaseData(tenantId) {\n      return this.http.get(`${this.baseUrl}/getBaseDataForMD?tenantId=${tenantId}`);\n    }\n    getScreens(tenantId) {\n      return this.http.get(`${this.baseUrl}/getScreensForMD?tenantId=${tenantId}`);\n    }\n    getCategories(obj) {\n      let tenantId = obj['tenantId'];\n      let type = obj['type'];\n      return this.http.get(`${this.baseUrl}/getCategories?tenantId=${tenantId}&type=${type}`);\n    }\n    getSubCategories(obj) {\n      let tenantId = obj['tenantId'];\n      let category = obj['category'];\n      let type = obj['type'];\n      return this.http.get(`${this.baseUrl}/getSubCategories?tenantId=${tenantId}&category=${category}&type=${type}`);\n    }\n    // getPresentData(obj:object) {\n    //   let specific\n    //   let itemCode\n    //   let tenantId = obj['tenantId'];\n    //   let type = obj['type'];\n    //   let userEmail = obj['userEmail'];\n    //   if(obj['specific']){\n    //     specific = obj['specific'];\n    //   }\n    //   if(obj['itemCode'] !== undefined){\n    //     itemCode = obj['itemCode'];\n    //   }\n    //   if(\"specific\" in obj && obj['itemCode'] === undefined){\n    //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}&specific=${specific}`)\n    //   }else if(\"itemCode\" in obj){\n    //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}&specific=${specific}&itemCode=${itemCode}`)\n    //   }else{\n    //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}`)\n    //   }\n    // }\n    uploadExcel(obj) {\n      return this.http.post(`${this.baseUrl}uploadExcel/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    resetSession(obj) {\n      return this.http.post(`${this.baseUrl}resetSession/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updateRegisterUser(registerObj, id) {\n      return this.http.put(`${this.baseUrl}/${id}`, registerObj);\n    }\n    deleteRegistered(id) {\n      return this.http.delete(`${this.baseUrl}/${id}`);\n    }\n    getRegisteredUserId(id) {\n      return this.http.get(`${this.baseUrl}/${id}`);\n    }\n    getCategoryList(tenantId) {\n      return this.http.get(`${this.baseUrl}/getMenuCategoriesForMD/?tenantId=${tenantId}`);\n    }\n    getRecipeCode(tenantId) {\n      return this.http.get(`${this.baseUrl}/getRecipeCode/?tenantId=${tenantId}`);\n    }\n    getPartyCode(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPartyCode/?tenantId=${tenantId}`);\n    }\n    getSubCategoryList(obj) {\n      let tenantId = obj['tenantId'];\n      let category = obj['category'];\n      return this.http.get(`${this.baseUrl}/getMenuSubCategoriesForMD/?tenantId=${tenantId}&category=${category}`);\n    }\n    getServingSizeList(tenantId) {\n      return this.http.get(`${this.baseUrl}/getServingSizeForMD/?tenantId=${tenantId}`);\n    }\n    getSections(obj) {\n      let tenantId = obj['tenantId'];\n      let storeId = obj['storeId'];\n      return this.http.get(`${this.baseUrl}/getPOSFloors/?tenantId=${tenantId}&storeId=${storeId}`);\n    }\n    getInvList(obj) {\n      let tenantId = obj['tenantId'];\n      let restaurantId = obj['restaurantId'];\n      return this.http.get(`${this.baseUrl}/getInventoryListForMD/?tenantId=${tenantId}&restaurantId=${restaurantId}`);\n    }\n    getInventoryListForSubrecipeMD(obj) {\n      let tenantId = obj['tenantId'];\n      let restaurantId = obj['restaurantId'];\n      return this.http.get(`${this.baseUrl}/getInventoryListForSubrecipeMD/?tenantId=${tenantId}&restaurantId=${restaurantId}`);\n    }\n    updateData(obj) {\n      return this.http.post(`${this.engineUrl}master_data/updateData`, obj).pipe(map(res => {\n        if (res.out_of_sync) {\n          alert(res.message);\n          window.location.reload();\n          throw new Error(\"Session is out of sync\");\n        }\n        return res;\n      }), catchError(error => {\n        console.error(\"Error updating data:\", error);\n        throw error;\n      }));\n    }\n    getPresentData(obj) {\n      let specific;\n      let itemCode;\n      let tenantId = obj['tenantId'];\n      let type = obj['type'];\n      let userEmail = obj['userEmail'];\n      if (obj['specific']) {\n        specific = obj['specific'];\n      }\n      if (obj['itemCode'] !== undefined) {\n        itemCode = obj['itemCode'];\n      }\n      if (\"specific\" in obj && obj['itemCode'] === undefined) {\n        return this.http.get(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}&specific=${specific}`);\n      } else if (\"itemCode\" in obj) {\n        return this.http.get(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}&specific=${specific}&itemCode=${itemCode}`);\n      } else {\n        return this.http.get(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}`);\n      }\n    }\n    wacRetrigger(obj) {\n      return this.triggerRequest('wacRetrigger', obj);\n    }\n    salesRetrigger(obj) {\n      return this.triggerRequest('salesRetrigger', obj);\n    }\n    forecastRetrigger(obj) {\n      return this.triggerRequest('forecastRetrigger', obj);\n    }\n    triggerRequest(endpoint, obj) {\n      const tenantId = obj['tenantId'];\n      const event = obj['event'];\n      return this.http.get(`${this.baseUrl}/${endpoint}?tenantId=${tenantId}&event=${event}`);\n    }\n    salesRerun(obj) {\n      return this.http.post(`${this.baseUrl}salesRerun/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    weightedAvg(obj) {\n      return this.http.post(`${this.baseUrl}weightedAvg/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    forecastData(obj) {\n      return this.http.post(`${this.baseUrl}forecastData/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    deleteData(obj) {\n      return this.http.post(`${this.baseUrl}deleteData/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updateRecipe(obj) {\n      return this.http.post(`${this.baseUrl}updateRecipe/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getPages(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPages/?tenantId=${tenantId}`);\n    }\n    getRecipeList(tenantId) {\n      return this.http.get(`${this.baseUrl}/getRecipeList/?tenantId=${tenantId}`);\n    }\n    getRecipeDataById(recipeId) {\n      return this.http.get(`${this.baseUrl}/getRecipeById/?recipeId=${recipeId}`);\n    }\n    getLocations(tenantId) {\n      return this.http.get(`${this.baseUrl}/getBranchesForMD?tenantId=${tenantId}`);\n    }\n    getTenantConfigDetails(tenantId) {\n      return this.http.get(`${this.baseUrl}/getMasterDataConfigByTenantId?tenantId=${tenantId}`);\n    }\n    getClientConfigDetails(tenantId) {\n      return this.http.get(`${this.baseUrl}/getClientConfigDetails?tenantId=${tenantId}`);\n    }\n    getRoles(tenantId) {\n      return this.http.get(`${this.baseUrl}/getRoles?tenantId=${tenantId}`);\n    }\n    getPOSPriceTires(tenantId, branch) {\n      let url = `${this.baseUrl}/getPOSPriceTires?tenantId=${tenantId}`;\n      if (branch) {\n        url += `&restaurant=${branch}`;\n      }\n      return this.http.get(url);\n    }\n    getUIAccess(tenantId) {\n      return this.http.get(`${this.baseUrl}/getUIAccess?tenantId=${tenantId}`);\n    }\n    getModifiers(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPOSModifiers?tenantId=${tenantId}`);\n    }\n    getPOSServingSizes(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPOSServingSize?tenantId=${tenantId}`);\n    }\n    updateClientConfigDetails(obj) {\n      return this.http.post(`${this.baseUrl}updateClientConfigDetails/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    refreshApiCall(obj) {\n      return this.http.post(`${this.baseUrl}refreshApiCall/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updateAccess(obj) {\n      return this.http.post(`${this.baseUrl}updateAccess/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    removeData(obj) {\n      return this.http.post(`${this.baseUrl}removeData/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updatePermission(obj) {\n      return this.http.post(`${this.baseUrl}updatePermission/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    syncToInventory(obj) {\n      return this.http.post(`${this.baseUrl}createUpdateJob/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    retrieveHistory(obj) {\n      return this.http.post(`${this.baseUrl}retrieveUpdates/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getErrorlog(obj) {\n      return this.http.post(`${this.baseUrl}getErrorLog/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getConfig() {\n      let obj = {};\n      return this.http.post(`${this.baseUrl}masterDataUpdateConfig/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getItemCost(obj) {\n      return this.http.post(`${this.baseUrl}getRecipeCost/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getInvCode() {\n      return this.http.get(`${this.baseUrl}/generateInvCode`);\n    }\n    getIPAddress() {\n      // return this.http.get<any[]>('https://api.ipify.org/?format=json');\n      return this.http.get('https://api.bigdatacloud.net/data/client-ip');\n    }\n    readIPConfig(tenantId) {\n      return this.http.get(`${this.baseUrl}/readIPConfig?tenantId=${tenantId}`);\n    }\n    updateIPConfig(obj) {\n      return this.http.post(`${this.baseUrl}updateIPConfig/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    dicontinuedData(obj) {\n      return this.http.post(`${this.baseUrl}dicontinuedData/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getRolesListDiscontinuedLocations(tenantId) {\n      return this.http.get(`${this.baseUrl}/getRolesListDiscontinuedLocations?tenantId=${tenantId}`);\n    }\n    itemNameSearch(obj) {\n      let tenantId = obj['tenantId'];\n      let ingredient_name = obj['ingredient_name'];\n      return this.http.get(`${this.engineUrl}llm/search?tenantId=${tenantId}&ingredient_name=${ingredient_name}`);\n    }\n    getPOS_MenuItems(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPOSMenuItems?tenantId=${tenantId}`);\n    }\n    getPOS_MenuItemById(obj) {\n      let tenantId = obj['tenantId'];\n      let menu_id = obj['menu_id'];\n      return this.http.get(`${this.baseUrl}/getPOSMenuById/?tenantId=${tenantId}&menu_id=${menu_id}`);\n    }\n    getCode(obj) {\n      let tenantId = obj['tenantId'];\n      let code = obj['code'];\n      return this.http.get(`${this.baseUrl}/getCode/?tenantId=${tenantId}&code=${code}`);\n    }\n    getDetailedPriceList(obj) {\n      let tenantId = obj['tenantId'];\n      let priceId = obj['priceId'];\n      let restaurantId = obj['restaurantId'];\n      return this.http.get(`${this.baseUrl}/getPOSMenuPriceById/?tenantId=${tenantId}&priceId=${priceId}&restaurantId=${restaurantId}`);\n    }\n    getDetailedModifierList(obj) {\n      let tenantId = obj['tenantId'];\n      let modifierId = obj['modifierId'];\n      return this.http.get(`${this.baseUrl}/getModifierById/?tenantId=${tenantId}&modifierId=${modifierId}`);\n    }\n    getPOS_MenuCost(obj) {\n      let tenantId = obj['tenantId'];\n      let servingSize = obj['servingSize'];\n      let itemCode = obj['itemCode'];\n      let restaurantId = obj['restaurantId'];\n      return this.http.get(`${this.baseUrl}/getMenuCost/?tenantId=${tenantId}&servingSize=${servingSize}&itemCode=${itemCode}&restaurantId=${restaurantId}`);\n    }\n    getMenuMappingList(obj) {\n      let tenantId = obj['tenantId'];\n      let restaurantId = obj['restaurantId'];\n      let exp = obj.hasOwnProperty('export') ? obj['export'] : false;\n      let itemCode = obj['itemCode'];\n      let page = obj.hasOwnProperty('page') ? obj['page'] : 1;\n      let per_page = obj.hasOwnProperty('per_page') ? obj['per_page'] : 5;\n      return this.http.get(`${this.engineUrl}menu_mapping/List?tenantId=${tenantId}&itemCode=${itemCode}&page=${page}&per_page=${per_page}&export=${exp}&restaurantId=${restaurantId}`);\n    }\n    createPartyOrder(obj) {\n      return this.http.post(`${this.baseUrl}createPartyOrder/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getPartyOrder(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPartyOrder?tenantId=${tenantId}`);\n    }\n    getPartyNames(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPartyNames?tenantId=${tenantId}`);\n    }\n    setPartyDraft(obj) {\n      return this.http.post(`${this.baseUrl}setPartyDraft/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    getPartyDraft(tenantId) {\n      return this.http.get(`${this.baseUrl}/getPartyDraft?tenantId=${tenantId}`);\n    }\n    deletePartyDraft(obj) {\n      return this.http.post(`${this.baseUrl}deletePartyDraft/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    deleteParty(obj) {\n      return this.http.post(`${this.baseUrl}deleteParty/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    deleteAllPartyDraft(obj) {\n      return this.http.post(`${this.baseUrl}deleteAllPartyDraft/`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updateMenuMapping(obj) {\n      return this.http.post(`${this.engineUrl}menu_mapping/${obj['id']}`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    printInvoice(obj) {\n      return this.http.post(`${this.engineUrl}print_data/generate-invoice`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    printPartyInvoice(obj) {\n      return this.http.post(`${this.engineUrl}party_print/generate-party-invoice`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    globalPrintPdf(data) {\n      var pdfData = atob(data);\n      var arrayBuffer = new ArrayBuffer(pdfData.length);\n      var uint8Array = new Uint8Array(arrayBuffer);\n      for (var i = 0; i < pdfData.length; i++) {\n        uint8Array[i] = pdfData.charCodeAt(i);\n      }\n      var blob = new Blob([arrayBuffer], {\n        type: 'application/pdf'\n      });\n      var url = URL.createObjectURL(blob);\n      window.open(url, '_blank');\n    }\n    createMenuMapping(obj) {\n      return this.http.post(`${this.engineUrl}menu_mapping/Create`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    importData(obj) {\n      return this.http.post(`${this.engineUrl}menu_mapping/Import`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    recipeInsight(obj) {\n      return this.http.post(`${this.engineUrl}llm/recipe-insight`, obj).pipe(map(res => {\n        return res;\n      }));\n    }\n    updateConfigAccess(obj) {\n      return this.http.post(`${this.baseUrl}updateConfigAccess/`, obj).pipe(map(res => res));\n    }\n    updateReport(obj) {\n      return this.http.post(`${this.baseUrl}updateReport/`, obj).pipe(map(res => res));\n    }\n    updateRole(obj) {\n      return this.http.post(`${this.baseUrl}updateRole/`, obj).pipe(map(res => res));\n    }\n    getReportData(tenantId) {\n      return this.http.get(`${this.baseUrl}/getReportData?tenantId=${tenantId}`);\n    }\n    getRoloposConfig(obj) {\n      return this.http.get(`${this.baseUrl}getRoloposConfig/`, obj).pipe(map(res => res));\n    }\n    saveAccount(obj) {\n      return this.http.post(`${this.baseUrl}accountSetUp`, obj).pipe(map(res => res));\n    }\n    getAccountById(tenantId) {\n      return this.getRoloposConfig({\n        tenantId: this.getCurrentUser().tenantId\n      }).pipe(map(res => {\n        if (res.success && res.data && Array.isArray(res.data)) {\n          const account = res.data.find(acc => acc.tenantId === tenantId);\n          return {\n            success: !!account,\n            data: account\n          };\n        }\n        return {\n          success: false,\n          data: null\n        };\n      }));\n    }\n    getCurrentUser() {\n      const userStr = sessionStorage.getItem('user');\n      return userStr ? JSON.parse(userStr) : null;\n    }\n    updateClosingDates(data) {\n      return this.http.post(this.apiUrl, data);\n    }\n    startProcessing(tenantId) {\n      return this.http.post(`${this.engineUrl}llm/start_processing`, {\n        tenantId\n      });\n    }\n    getStatus(tenantId) {\n      return this.http.get(`${this.engineUrl}llm/get_status`, {\n        params: {\n          tenantId\n        }\n      });\n    }\n    downloadData(type, tenantId) {\n      return this.http.get(`${this.engineUrl}llm/download`, {\n        params: {\n          type,\n          tenantId\n        },\n        responseType: 'text'\n      });\n    }\n    static {\n      this.ɵfac = function InventoryService_Factory(t) {\n        return new (t || InventoryService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: InventoryService,\n        factory: InventoryService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InventoryService;\n})();\nexport { InventoryService };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}