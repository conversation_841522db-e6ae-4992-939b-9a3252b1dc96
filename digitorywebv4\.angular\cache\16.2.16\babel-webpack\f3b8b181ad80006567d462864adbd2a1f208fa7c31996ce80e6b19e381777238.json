{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/inventory.service\";\nimport * as i3 from \"../../services/smart-dashboard.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/material/tooltip\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"ngx-mat-select-search\";\nconst _c0 = [\"chartsContainer\"];\nfunction SmartDashboardComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tab_r10.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getDashboardDescription(i_r11));\n  }\n}\nfunction SmartDashboardComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.selectedLocations.length, \" selected) \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r13.displayName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"div\", 68)(3, \"mat-icon\", 69);\n    i0.ɵɵtext(4, \"refresh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r6.getLoadingMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getLoadingMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"analytics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h4\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r7.getEmptyStateMessage().title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getEmptyStateMessage().description, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_120_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 82)(5, \"div\", 83);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r15.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.label);\n  }\n}\nfunction SmartDashboardComponent_div_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_120_div_2_Template, 9, 3, \"div\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getSummaryItems());\n  }\n}\nfunction SmartDashboardComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85, 86);\n  }\n}\nChart.register(...registerables);\nclass SmartDashboardComponent {\n  constructor(authService, inventoryService, smartDashboardService, cdr) {\n    this.authService = authService;\n    this.inventoryService = inventoryService;\n    this.smartDashboardService = smartDashboardService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.tabs = [];\n    this.selectedTab = 0;\n    this.locations = [];\n    this.baseDates = [];\n    this.selectedLocations = [];\n    this.selectedBaseDate = '';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    this.dashboardData = null;\n    this.charts = [];\n    this.isLoading = false;\n    this.dashboardConfig = null;\n    this.useDefaultCharts = false;\n    this.locationFilterCtrl = new FormControl();\n    this.allLocationsSelected = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n  initializeComponent() {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n    this.smartDashboardService.dashboardData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.dashboardData = data;\n      if (data) {\n        this.renderCharts();\n      }\n      this.cdr.detectChanges();\n    });\n    this.smartDashboardService.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.detectChanges();\n    });\n  }\n  loadDashboardConfig() {\n    this.smartDashboardService.getDashboardConfig().pipe(first()).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.dashboardConfig = response.data;\n        }\n      },\n      error: () => {}\n    });\n  }\n  setDefaultTabs() {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n  setDefaultBaseDates() {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n  setDefaultDates() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n  onDefaultChartsToggle() {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n      // Note: Dashboard generation is handled by setMode() method\n    }\n  }\n\n  setMode(useDefault) {\n    if (this.useDefaultCharts !== useDefault) {\n      this.useDefaultCharts = useDefault;\n      this.onDefaultChartsToggle();\n      if (useDefault) {\n        // Auto-generate dashboard when switching to default charts mode\n        if (this.areAllFiltersValid()) {\n          setTimeout(() => {\n            this.generateDashboard();\n          }, 100);\n        }\n      } else {\n        // Auto-focus input when switching to custom query mode\n        setTimeout(() => {\n          const inputElement = document.querySelector('.ai-input-field input');\n          if (inputElement && this.areAllFiltersValid()) {\n            inputElement.focus();\n          }\n        }, 100);\n      }\n    }\n  }\n  sendMessage() {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  loadLocations() {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId).pipe(first()).subscribe({\n        next: res => {\n          if (res && res.result === 'success' && res.branches) {\n            this.locations = res.branches.map(branch => ({\n              value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n              label: branch.branchName || branch.name,\n              checked: true\n            }));\n            this.selectedLocations = this.locations.map(location => location.value);\n            this.setupLocationFilter();\n          } else {\n            this.locations = [];\n            this.selectedLocations = [];\n          }\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.locations = [];\n        }\n      });\n    }\n  }\n  resetFilters() {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n  getDashboardDescription(index) {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n  areAllFiltersValid() {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n  setupLocationFilter() {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(startWith(''), map(value => this.filterLocations(value || '')));\n  }\n  filterLocations(value) {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location => location.label.toLowerCase().includes(filterValue));\n  }\n  toggleAllLocations() {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n  onLocationSelectionChange(selectedValues) {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n    this.onFilterChange();\n  }\n  updateLocationStates() {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n  isAllSelected() {\n    return this.selectedLocations.length === this.locations.length;\n  }\n  onFilterChange() {\n    // Auto-generate dashboard if in default charts mode and filters are valid\n    if (this.useDefaultCharts && this.areAllFiltersValid()) {\n      setTimeout(() => {\n        this.generateDashboard();\n      }, 300); // Small delay to allow for multiple filter changes\n    }\n  }\n\n  getLoadingMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n  getEmptyStateMessage() {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n  generateDashboard() {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n    // Helper function to convert Date to string format\n    const formatDateValue = dateValue => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n    this.clearCharts();\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n    this.smartDashboardService.generateDashboard(request).pipe(first()).subscribe({\n      next: () => {},\n      error: () => {}\n    });\n  }\n  clearCharts() {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n  renderCharts() {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n    this.clearCharts();\n    setTimeout(() => {\n      this.dashboardData.charts.forEach(chartConfig => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n  getSummaryItems() {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n    return this.dashboardData.summary_items;\n  }\n  createChart(chartConfig) {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n      const chart = new Chart(ctx, {\n        type: chartConfig.type,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n      this.charts.push(chart);\n    }\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i3.SmartDashboardService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      viewQuery: function SmartDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartsContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 122,\n      vars: 39,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"left-sidebar\"], [1, \"sidebar-section\", \"dashboard-selector-section\"], [\"appearance\", \"outline\", 1, \"dashboard-selector\"], [\"placeholder\", \"Choose Dashboard Type\", 3, \"value\", \"valueChange\", \"selectionChange\"], [1, \"selected-dashboard\"], [1, \"selected-info\"], [1, \"selected-name\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-section\", \"filters-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-icon\"], [1, \"section-title\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [1, \"label-icon\"], [1, \"label-text\"], [\"class\", \"selection-count\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", \"placeholder\", \"Select restaurants\", 3, \"ngModel\", \"ngModelChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search restaurants...\", \"noEntriesFoundLabel\", \"No restaurants found\", 3, \"formControl\"], [1, \"select-all-controls\"], [\"mat-button\", \"\", 1, \"select-toggle-btn\", 3, \"click\"], [\"placeholder\", \"Select base date\", 3, \"value\", \"valueChange\", \"selectionChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\", \"dateChange\"], [\"endPicker\", \"\"], [1, \"sidebar-section\", \"mode-section\"], [1, \"mode-selection-container\"], [1, \"mode-options\"], [1, \"mode-option\", 3, \"click\"], [1, \"mode-radio\"], [1, \"mode-content\"], [1, \"mode-title\"], [1, \"mode-desc\"], [1, \"sidebar-section\", \"reset-section\"], [1, \"reset-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"gradient-highlight-bar\"], [1, \"highlight-content\"], [1, \"highlight-left\"], [1, \"highlight-icon\"], [1, \"highlight-title\"], [1, \"highlight-status\"], [1, \"highlight-right\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", 3, \"placeholder\", \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"matTooltip\", \"click\"], [1, \"dashboard-section\"], [1, \"dashboard-content\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"dashboard-summary\", 4, \"ngIf\"], [\"class\", \"charts-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"dashboard-option\"], [1, \"dashboard-info\"], [1, \"dashboard-name\"], [1, \"dashboard-desc\"], [1, \"selection-count\"], [1, \"loading-state\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"spin\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"empty-state\"], [1, \"empty-state-content\"], [1, \"empty-state-icon\"], [1, \"empty-state-title\"], [1, \"empty-state-description\"], [1, \"dashboard-summary\"], [1, \"summary-cards\"], [\"class\", \"summary-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-value\"], [1, \"summary-label\"], [1, \"charts-container\"], [\"chartsContainer\", \"\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_4_listener($event) {\n            return ctx.selectedTab = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_4_listener($event) {\n            return ctx.onTabChange($event.value);\n          });\n          i0.ɵɵelementStart(5, \"mat-select-trigger\")(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, SmartDashboardComponent_mat_option_10_Template, 7, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n          i0.ɵɵtext(15, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h3\", 13);\n          i0.ɵɵtext(17, \"Smart Filters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"label\", 16)(21, \"mat-icon\", 17);\n          i0.ɵɵtext(22, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 18);\n          i0.ɵɵtext(24, \"Restaurants *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SmartDashboardComponent_span_25_Template, 2, 1, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 20)(27, \"mat-select\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener($event) {\n            return ctx.selectedLocations = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_27_listener($event) {\n            return ctx.onLocationSelectionChange($event.value);\n          });\n          i0.ɵɵelementStart(28, \"mat-option\");\n          i0.ɵɵelement(29, \"ngx-mat-select-search\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_31_listener() {\n            return ctx.toggleAllLocations();\n          });\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_option_33_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵpipe(34, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"label\", 16)(37, \"mat-icon\", 17);\n          i0.ɵɵtext(38, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 18);\n          i0.ɵɵtext(40, \"Base Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 20)(42, \"mat-select\", 25);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_42_listener($event) {\n            return ctx.selectedBaseDate = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_42_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵtemplate(43, SmartDashboardComponent_mat_option_43_Template, 2, 2, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"label\", 16)(46, \"mat-icon\", 17);\n          i0.ɵɵtext(47, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\", 18);\n          i0.ɵɵtext(49, \"Start Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"mat-form-field\", 20)(51, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_51_listener($event) {\n            return ctx.startDate = $event;\n          })(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_51_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"mat-datepicker-toggle\", 27)(53, \"mat-datepicker\", null, 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 15)(56, \"label\", 16)(57, \"mat-icon\", 17);\n          i0.ɵɵtext(58, \"calendar_today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 18);\n          i0.ɵɵtext(60, \"End Date *\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 20)(62, \"input\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_62_listener($event) {\n            return ctx.endDate = $event;\n          })(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_62_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"mat-datepicker-toggle\", 27)(64, \"mat-datepicker\", null, 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(66, \"div\", 31)(67, \"div\", 10)(68, \"div\", 11)(69, \"mat-icon\", 12);\n          i0.ɵɵtext(70, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"h3\", 13);\n          i0.ɵɵtext(72, \"Dashboard Mode\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 32)(74, \"div\", 33)(75, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_div_click_75_listener() {\n            return ctx.setMode(false);\n          });\n          i0.ɵɵelementStart(76, \"div\", 35)(77, \"mat-icon\");\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 36)(80, \"span\", 37);\n          i0.ɵɵtext(81, \"Custom Query\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"span\", 38);\n          i0.ɵɵtext(83, \"Ask specific questions about your data\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(84, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_div_click_84_listener() {\n            return ctx.setMode(true);\n          });\n          i0.ɵɵelementStart(85, \"div\", 35)(86, \"mat-icon\");\n          i0.ɵɵtext(87);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 36)(89, \"span\", 37);\n          i0.ɵɵtext(90, \"Default Charts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"span\", 38);\n          i0.ɵɵtext(92, \"Standard business dashboard view\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(93, \"div\", 39)(94, \"div\", 40)(95, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_95_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(96, \"mat-icon\");\n          i0.ɵɵtext(97, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(98, \" Reset \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(99, \"div\", 42)(100, \"div\", 43)(101, \"div\", 44)(102, \"div\", 45)(103, \"mat-icon\", 46);\n          i0.ɵɵtext(104, \"auto_awesome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"span\", 47);\n          i0.ɵɵtext(106, \"Digi AI Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"span\", 48);\n          i0.ɵɵtext(108);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 49)(110, \"div\", 50)(111, \"mat-form-field\", 51)(112, \"input\", 52);\n          i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_Template_input_ngModelChange_112_listener($event) {\n            return ctx.chatMessage = $event;\n          })(\"keydown\", function SmartDashboardComponent_Template_input_keydown_112_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_113_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(114, \"mat-icon\");\n          i0.ɵɵtext(115);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(116, \"div\", 54)(117, \"div\", 55);\n          i0.ɵɵtemplate(118, SmartDashboardComponent_div_118_Template, 9, 2, \"div\", 56);\n          i0.ɵɵtemplate(119, SmartDashboardComponent_div_119_Template, 9, 2, \"div\", 57);\n          i0.ɵɵtemplate(120, SmartDashboardComponent_div_120_Template, 3, 1, \"div\", 58);\n          i0.ɵɵtemplate(121, SmartDashboardComponent_div_121_Template, 2, 0, \"div\", 59);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(54);\n          const _r5 = i0.ɵɵreference(65);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedTab);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tabs[ctx.selectedTab] == null ? null : ctx.tabs[ctx.selectedTab].label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLocations.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isAllSelected() ? \"Deselect All\" : \"Select All\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(34, 37, ctx.filteredLocations));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", ctx.selectedBaseDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDates);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"ngModel\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"ngModel\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(12);\n          i0.ɵɵclassProp(\"active\", !ctx.useDefaultCharts);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(!ctx.useDefaultCharts ? \"radio_button_checked\" : \"radio_button_unchecked\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"active\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.useDefaultCharts ? \"radio_button_checked\" : \"radio_button_unchecked\");\n          i0.ɵɵadvance(20);\n          i0.ɵɵclassProp(\"ready\", ctx.areAllFiltersValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.areAllFiltersValid() ? \"Ready to analyze\" : \"Please fill all required filters\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled-mode\", ctx.useDefaultCharts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.useDefaultCharts ? \"Default charts mode - input disabled\" : \"Ask me about your business data...\")(\"ngModel\", ctx.chatMessage)(\"disabled\", !ctx.areAllFiltersValid() || ctx.useDefaultCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.useDefaultCharts ? !ctx.areAllFiltersValid() : !ctx.chatMessage.trim() || !ctx.areAllFiltersValid())(\"matTooltip\", ctx.useDefaultCharts ? \"Generate Default Dashboard\" : \"Send Custom Query\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.useDefaultCharts ? \"auto_awesome\" : \"send\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.dashboardData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.dashboardData == null ? null : ctx.dashboardData.summary_items));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dashboardData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.AsyncPipe, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatSuffix, MatSelectModule, i8.MatSelect, i8.MatSelectTrigger, i9.MatOption, MatInputModule, i10.MatInput, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, MatSlideToggleModule, MatTooltipModule, i12.MatTooltip, FormsModule, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel, ReactiveFormsModule, i13.FormControlDirective, NgxMatSelectSearchModule, i14.MatSelectSearchComponent],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 86px);\\n  min-height: calc(100vh - 86px);\\n  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n  color: #1f2937;\\n  position: relative;\\n  overflow: hidden;\\n  align-items: stretch;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.left-sidebar[_ngcontent-%COMP%] {\\n  width: 285px;\\n  background: #ffffff;\\n  border-right: 1px solid #e5e7eb;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  z-index: 10;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n  height: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.dashboard-selector-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.filters-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  padding: 0.5rem 0.75rem 0.125rem 0.75rem;\\n  min-height: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.mode-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: 0.125rem 0.75rem 0.25rem 0.75rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section.reset-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: 0.5rem 0.75rem 0.75rem 0.75rem;\\n  background: #f9fafb;\\n  border-top: 1px solid #f3f4f6;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-top: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 1rem;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .filter-badge[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: #ffffff;\\n  border-radius: 50%;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.65rem;\\n  font-weight: 600;\\n  margin-left: auto;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.04));\\n  border-color: rgba(255, 107, 53, 0.2);\\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.12);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%]   .dashboard-selector[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.125rem;\\n  padding-top: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.25rem;\\n  padding: 0.5rem;\\n  border-radius: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease-out;\\n  border: 2px solid transparent;\\n  background: #f9fafb;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 53, 0.08);\\n  border-color: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-radio[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-title[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 600;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option.active[_ngcontent-%COMP%]   .mode-desc[_ngcontent-%COMP%] {\\n  color: #ff581c;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]:not(.active):hover {\\n  background: rgba(243, 244, 246, 0.8);\\n  border-color: #d1d5db;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-radio[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-top: 1px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-radio[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 18px;\\n  height: 18px;\\n  color: #9ca3af;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-content[_ngcontent-%COMP%]   .mode-title[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  color: #374151;\\n  line-height: 1.3;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .mode-section[_ngcontent-%COMP%]   .mode-selection-container[_ngcontent-%COMP%]   .mode-options[_ngcontent-%COMP%]   .mode-option[_ngcontent-%COMP%]   .mode-content[_ngcontent-%COMP%]   .mode-desc[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  transition: all 0.3s ease-out;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding-right: 0.125rem;\\n  padding-bottom: 0.25rem;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 3px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-bottom: 0.125rem;\\n  font-weight: 500;\\n  color: #374151;\\n  font-size: 0.8rem;\\n  letter-spacing: 0.2px;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  line-height: 1.2;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   .selection-count[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  color: #ff6b35;\\n  font-weight: 600;\\n  background: rgba(255, 107, 53, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 0.375rem;\\n  flex-shrink: 0;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reset-section[_ngcontent-%COMP%]   .reset-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reset-section[_ngcontent-%COMP%]   .reset-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 36px;\\n  border-radius: 0.5rem;\\n  color: #374151;\\n  border-color: #d1d5db;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease-out;\\n  background: #ffffff;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reset-section[_ngcontent-%COMP%]   .reset-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #ff6b35;\\n  color: #ff6b35;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  transform: translateY(-1px);\\n}\\n.left-sidebar[_ngcontent-%COMP%]   .reset-section[_ngcontent-%COMP%]   .reset-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-right: 0.125rem;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0;\\n  gap: 0;\\n  position: relative;\\n  z-index: 1;\\n  align-items: stretch;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 0;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.06), rgba(255, 107, 53, 0.02));\\n  border: 2px solid rgba(255, 107, 53, 0.15);\\n  border-radius: 0.75rem;\\n  margin: 0.5rem;\\n  position: relative;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.08);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  min-height: 60px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  flex-shrink: 0;\\n  min-width: 290px;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  color: #ff6b35;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  margin-right: 0.125rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4b5563;\\n  padding: 3px 8px;\\n  border-radius: 0.375rem;\\n  background: rgba(255, 107, 53, 0.1);\\n  white-space: nowrap;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%]   .highlight-status.ready[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex: 1;\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #f9fafb !important;\\n  border-color: #d1d5db !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #d1d5db !important;\\n  box-shadow: none !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 53, 0.6) !important;\\n  border-color: rgba(255, 107, 53, 0.6) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container.disabled-mode[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 53, 0.7) !important;\\n  border-color: rgba(255, 107, 53, 0.7) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]:not(.disabled-mode)   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_subtle-pulse 2s ease-in-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border: 2px solid #ff6b35 !important;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  height: 44px !important;\\n  min-height: 44px !important;\\n  border-radius: 0.5rem !important;\\n  transition: all 0.3s ease-out !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-color: #e55a2b !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(255, 107, 53, 0.2) !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%] {\\n  padding: 12px 16px !important;\\n  min-height: 20px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .ai-input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  font-weight: 500;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  background: #ff6b35;\\n  color: #ffffff;\\n  transition: all 0.3s ease-out;\\n  flex-shrink: 0;\\n  border-radius: 0.5rem;\\n  border: 2px solid #ff6b35;\\n  box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  background: #e55a2b;\\n  border-color: #e55a2b;\\n  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.15);\\n  transform: translateY(-1px);\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not([disabled])::before {\\n  left: 100%;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[disabled][_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  background: #d1d5db;\\n  border-color: #d1d5db;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  position: relative;\\n  z-index: 1;\\n}\\n@media (max-width: 768px) {\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n    width: 100%;\\n  }\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #ffffff;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border: 1px solid rgba(229, 231, 235, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  margin: 0.5rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f9fafb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 107, 53, 0.2);\\n  border-radius: 3px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 107, 53, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 450px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #d1d5db;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-state-content[_ngcontent-%COMP%]   .empty-state-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 1.25rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #ff6b35;\\n  animation: _ngcontent-%COMP%_spin 1.5s linear infinite;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-title[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.35rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4b5563;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%] {\\n  padding: 1rem 1rem 0.75rem 1rem;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.3s ease-out;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 0.5rem;\\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ff6b35;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .dashboard-summary[_ngcontent-%COMP%]   .summary-cards[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #4b5563;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  align-content: start;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease-out;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  border-color: rgba(255, 107, 53, 0.2);\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header {\\n  margin-bottom: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container .chart-header h3 {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .charts-container[_ngcontent-%COMP%]     .chart-container canvas {\\n  max-width: 100%;\\n  height: 300px !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_subtle-pulse {\\n  0% {\\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  }\\n  50% {\\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.15), 0 0 0 2px rgba(255, 107, 53, 0.1) !important;\\n  }\\n  100% {\\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.08) !important;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 44px);\\n    min-height: calc(100vh - 44px);\\n  }\\n  .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e5e7eb;\\n    flex-shrink: 0;\\n    max-height: 45vh;\\n    overflow-y: auto;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .dashboard-selector-section[_ngcontent-%COMP%] {\\n    margin: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    max-height: 200px;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    flex: 1;\\n    min-height: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .left-sidebar[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%], .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 32px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    gap: 0.25rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    padding: 0.5rem;\\n    align-items: stretch;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-left[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    align-self: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .gradient-highlight-bar[_ngcontent-%COMP%]   .highlight-content[_ngcontent-%COMP%]   .highlight-right[_ngcontent-%COMP%]   .ai-input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .dashboard-section[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n}\\n  .mat-mdc-form-field {\\n  width: 100%;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background: #f9fafb !important;\\n  border-radius: 0.375rem !important;\\n  border: 1px solid #e5e7eb !important;\\n  box-shadow: none !important;\\n  transition: all 0.3s ease-out !important;\\n  height: 38px !important;\\n  min-height: 38px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-text-field-wrapper:hover {\\n  background: #ffffff !important;\\n  border-color: #d1d5db !important;\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {\\n  background: #ffffff !important;\\n  border-color: #ff6b35 !important;\\n  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1) !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-infix {\\n  padding: 6px 8px !important;\\n  min-height: 18px !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mdc-notched-outline {\\n  display: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  color: #1f2937 !important;\\n  font-weight: 500 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element::placeholder {\\n  color: #9ca3af !important;\\n  font-weight: 400 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-input-element:disabled {\\n  color: #9ca3af !important;\\n  background: transparent !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select {\\n  font-size: 0.85rem !important;\\n  line-height: 20px !important;\\n  font-weight: 500 !important;\\n  color: #1f2937 !important;\\n}\\n  .mat-mdc-form-field.dashboard-selector .mat-mdc-select-trigger {\\n  display: flex !important;\\n  align-items: center !important;\\n  min-height: 20px !important;\\n  padding: 0 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow-wrapper {\\n  transform: none !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-select-arrow {\\n  color: #6b7280 !important;\\n  font-size: 20px !important;\\n  transition: color 0.3s ease-out !important;\\n}\\n  .mat-mdc-form-field.mat-focused .mat-mdc-select-arrow {\\n  color: #ff6b35 !important;\\n}\\n  .mat-mdc-form-field:hover .mat-mdc-select-arrow {\\n  color: #4b5563 !important;\\n}\\n  .mat-mdc-form-field .mat-mdc-form-field-icon-prefix .input-prefix-icon {\\n  color: #6b7280 !important;\\n  font-size: 1.125rem !important;\\n  margin-right: 0.25rem !important;\\n}\\n  .mat-mdc-option {\\n  border-radius: 0.375rem !important;\\n  margin: 0 2px !important;\\n  font-size: 0.8rem !important;\\n  min-height: 36px !important;\\n  line-height: 1.2 !important;\\n  padding: 6px 8px !important;\\n  font-weight: 500 !important;\\n  transition: all 0.15s ease-out !important;\\n}\\n  .mat-mdc-option:first-child {\\n  margin-top: 0 !important;\\n  padding-top: 4px !important;\\n}\\n  .mat-mdc-option:hover {\\n  background: rgba(255, 107, 53, 0.08) !important;\\n  transform: translateX(2px) !important;\\n}\\n  .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 107, 53, 0.12) !important;\\n  color: #ff6b35 !important;\\n  font-weight: 600 !important;\\n}\\n  .mat-mdc-option .option-icon {\\n  margin-right: 0.25rem !important;\\n  font-size: 0.9rem !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-mdc-option .dashboard-option {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 4px 0;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 3px;\\n  flex: 1;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-name {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.3;\\n}\\n  .mat-mdc-option .dashboard-option .dashboard-info .dashboard-desc {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n  font-weight: 400;\\n}\\n  .mat-mdc-option .selected-dashboard {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  height: 100%;\\n}\\n  .mat-mdc-option .selected-dashboard .selected-info .selected-name {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1.4;\\n}\\n  .mat-mdc-select-panel {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n  .mat-mdc-button,   .mat-mdc-raised-button,   .mat-mdc-stroked-button {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  text-transform: none !important;\\n  min-width: auto !important;\\n  font-size: 0.85rem !important;\\n}\\n  .mat-mdc-raised-button {\\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;\\n}\\n  .mat-mdc-raised-button:hover {\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;\\n}\\n  .mat-mdc-stroked-button {\\n  border-width: 1px !important;\\n}\\n  .mat-datepicker-input {\\n  font-size: 0.875rem !important;\\n}\\n  .mat-datepicker-toggle {\\n  width: 20px !important;\\n  height: 20px !important;\\n  color: #6b7280 !important;\\n}\\n  .mat-datepicker-toggle:hover {\\n  color: #ff6b35 !important;\\n}\\n  .mat-datepicker-toggle-default-icon {\\n  width: 16px !important;\\n  height: 16px !important;\\n}\\n  .select-all-controls {\\n  padding: 4px 8px 6px 8px !important;\\n  border-bottom: 1px solid #e0e0e0 !important;\\n  background: #f8f9fa !important;\\n  margin: 0 !important;\\n  border-radius: 0 !important;\\n}\\n  .select-all-controls .select-toggle-btn {\\n  width: 100% !important;\\n  height: 26px !important;\\n  font-size: 0.75rem !important;\\n  padding: 0 !important;\\n  border-radius: 3px !important;\\n  font-weight: 600 !important;\\n  background: rgba(255, 107, 53, 0.1) !important;\\n  color: #ff6b35 !important;\\n  border: 1px solid rgba(255, 107, 53, 0.2) !important;\\n  transition: all 0.2s ease !important;\\n  min-height: 26px !important;\\n  line-height: 26px !important;\\n}\\n  .select-all-controls .select-toggle-btn:hover {\\n  background: rgba(255, 107, 53, 0.15) !important;\\n  border-color: rgba(255, 107, 53, 0.3) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYWktZGFzaGJvYXJkL3NtYXJ0LWRhc2hib2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrREE7RUFDRSxhQUFBO0VBQ0EsMEJBQUE7RUFDQSw4QkFBQTtFQUNBLDZEQUFBO0VBQ0EsK0VBQUE7RUFDQSxjQXhDUztFQXlDVCxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7QUFqREY7QUFtREU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsNEZBQUE7RUFDQSxvQkFBQTtFQUNBLFVBQUE7QUFqREo7O0FBc0RBO0VBQ0UsWUFBQTtFQUNBLG1CQXRFTTtFQXVFTiwrQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLDJDQWxEVTtFQW1EVixnQkFBQTtFQUNBLFlBQUE7QUFuREY7QUFzREk7RUFDRSxjQUFBO0VBQ0EsZ0JBcEVPO0VBcUVQLGdDQUFBO0FBcEROO0FBdURJO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0VBQ0Esd0NBQUE7RUFDQSxhQUFBO0FBckROO0FBd0RJO0VBQ0UsY0FBQTtFQUNBLHlDQUFBO0VBQ0EsZ0NBQUE7QUF0RE47QUF5REk7RUFDRSxjQUFBO0VBQ0EsdUNBQUE7RUFDQSxtQkF4R0k7RUF5R0osNkJBQUE7QUF2RE47QUEwREk7RUFDRSxxQkFoR087RUFpR1Asb0JBbEdPO0FBMENiO0FBMERNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUF2R0s7QUErQ2I7QUEwRFE7RUFDRSxjQTdIQTtFQThIQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGNBQUE7QUF4RFY7QUEyRFE7RUFDRSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBNUhDO0VBNkhELHFCQUFBO0FBekRWO0FBNERRO0VBQ0UsbUJBaEpBO0VBaUpBLGNBM0lGO0VBNElFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSwyQ0EzSEU7QUFpRVo7QUFpRUU7RUFDRSx1RkFBQTtFQUNBLDBDQUFBO0VBQ0Esc0JBeElRO0VBeUlSLGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSw4Q0FBQTtBQS9ESjtBQWlFSTtFQUNFLHVGQUFBO0VBQ0EscUNBQUE7RUFDQSwrQ0FBQTtBQS9ETjtBQWtFSTtFQUNFLFdBQUE7QUFoRU47QUFtRU07RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EseURBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx3Q0FBQTtBQWpFUjtBQW1FUTtFQUNFLGdDQUFBO0VBQ0EsMERBQUE7QUFqRVY7QUFxRU07RUFDRSxnQ0FBQTtFQUNBLDZGQUFBO0FBbkVSO0FBc0VNO0VBQ0UsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLHdCQUFBO0VBQ0EsOEJBQUE7QUFwRVI7QUEyRUk7RUFDRSxhQUFBO0VBQ0EsdUJBbk1PO0VBb01QLGNBQUE7QUF6RU47QUE2RU07RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxZQTFNSztBQStIYjtBQTZFUTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFlBL01HO0VBZ05ILGVBL01HO0VBZ05ILHFCQXhNRTtFQXlNRixlQUFBO0VBQ0EsNkJBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQWpPQTtBQXNKVjtBQTZFVTtFQUNFLG9DQUFBO0VBQ0EscUNBQUE7QUEzRVo7QUE2RVk7RUFDRSxjQS9PSjtBQW9LVjtBQThFWTtFQUNFLGNBblBKO0VBb1BJLGdCQUFBO0FBNUVkO0FBK0VZO0VBQ0UsY0FBQTtBQTdFZDtBQWtGWTtFQUNFLG9DQUFBO0VBQ0EscUJBclBIO0FBcUtYO0FBb0ZVO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFsRlo7QUFvRlk7RUFDRSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsY0FoUUg7RUFpUUcsNkJBQUE7QUFsRmQ7QUFzRlU7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtBQXBGWjtBQXNGWTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQTNRSDtFQTRRRyxnQkFBQTtFQUNBLDZCQUFBO0FBcEZkO0FBdUZZO0VBQ0UsaUJBQUE7RUFDQSxjQXBSSDtFQXFSRyxnQkFBQTtFQUNBLDZCQUFBO0FBckZkO0FBK0ZJO0VBQ0UsYUFBQTtFQUNBLHNCQTNSTztBQThMYjtBQWdHSTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxXQWpTTztFQWtTUCxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsdUJBdFNPO0VBdVNQLHVCQXRTTztBQXdNYjtBQWlHTTtFQUNFLFVBQUE7QUEvRlI7QUFrR007RUFDRSx1QkFBQTtBQWhHUjtBQW1HTTtFQUNFLG1DQUFBO0VBQ0Esa0JBQUE7QUFqR1I7QUFtR1E7RUFDRSxtQ0FBQTtBQWpHVjtBQXFHTTtFQUNFLGNBQUE7QUFuR1I7QUFxR1E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQWhVRztFQWlVSCx1QkFsVUc7RUFtVUgsZ0JBQUE7RUFDQSxjQXhVQztFQXlVRCxpQkFBQTtFQUNBLHFCQUFBO0FBbkdWO0FBcUdVO0VBQ0UsaUJBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBOVZGO0VBK1ZFLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQW5HWjtBQXNHVTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtBQXBHWjtBQXVHVTtFQUNFLGtCQUFBO0VBQ0EsY0E1V0Y7RUE2V0UsZ0JBQUE7RUFDQSxtQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBclZBO0VBc1ZBLGNBQUE7QUFyR1o7QUF5R1E7RUFDRSxXQUFBO0FBdkdWO0FBK0dJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0FBN0dOO0FBK0dNO0VBQ0UsWUFBQTtFQUNBLFlBQUE7RUFDQSxxQkF6V0k7RUEwV0osY0F4WEc7RUF5WEgscUJBN1hHO0VBOFhILGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQXJZQTtFQXNZQSwyQ0E1V0k7QUErUFo7QUErR1E7RUFDRSxtQkF4WUE7RUF5WUEscUJBaFpBO0VBaVpBLGNBalpBO0VBa1pBLGlGQWpYRTtFQWtYRiwyQkFBQTtBQTdHVjtBQWdIUTtFQUNFLGlCQUFBO0VBQ0Esc0JBdFlHO0FBd1JiOztBQXNIQTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxVQUFBO0VBQ0EsTUFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0JBQUE7QUFuSEY7QUFzSEU7RUFDRSxjQUFBO0VBQ0EsU0FBQTtFQUNBLHVGQUFBO0VBQ0EsMENBQUE7RUFDQSxzQkFwWlE7RUFxWlIsY0E5WlM7RUErWlQsa0JBQUE7RUFDQSw2QkFBQTtFQUNBLDhDQUFBO0FBcEhKO0FBc0hJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0F0YU87RUF1YVAsZUF2YU87RUF3YVAsZ0JBQUE7QUFwSE47QUFzSE07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQTlhSztFQSthTCxjQUFBO0VBQ0EsZ0JBQUE7QUFwSFI7QUFzSFE7RUFDRSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0VBQ0EsY0F6Y0E7RUEwY0Esc0JBeGJHO0FBb1ViO0FBdUhRO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBamNDO0VBa2NELG1CQUFBO0VBQ0Esc0JBaGNHO0FBMlViO0FBd0hRO0VBQ0Usa0JBQUE7RUFDQSxjQTFjQztFQTJjRCxnQkFBQTtFQUNBLHVCQTliRTtFQStiRixtQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsNkJBQUE7QUF0SFY7QUF3SFU7RUFDRSxtQ0FBQTtFQUNBLGNBN2RGO0FBdVdWO0FBMkhNO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FBekhSO0FBMkhRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0ExZEc7RUEyZEgsT0FBQTtFQUNBLDZCQUFBO0FBekhWO0FBMkhVO0VBQ0UsWUFBQTtBQXpIWjtBQTJIWTtFQUNFLDhCQUFBO0VBQ0EsZ0NBQUE7QUF6SGQ7QUEySGM7RUFDRSxnQ0FBQTtFQUNBLDJCQUFBO0FBekhoQjtBQTZIWTtFQUNFLDhDQUFBO0VBQ0EsZ0RBQUE7QUEzSGQ7QUE2SGM7RUFDRSw4Q0FBQTtFQUNBLGdEQUFBO0FBM0hoQjtBQWlJWTtFQUNFLHNDQUFBO0FBL0hkO0FBbUlVO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUFqSVo7QUFvSVk7RUFDRSw4QkFBQTtFQUNBLG9DQUFBO0VBQ0EseURBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx3Q0FBQTtBQWxJZDtBQW9JYztFQUNFLGdDQUFBO0VBQ0EsMERBQUE7QUFsSWhCO0FBc0lZO0VBQ0UsZ0NBQUE7RUFDQSw2RkFBQTtBQXBJZDtBQXVJWTtFQUNFLDZCQUFBO0VBQ0EsMkJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0FBcklkO0FBd0lZO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBdElkO0FBMElVO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkExakJGO0VBMmpCRSxjQXJqQko7RUFzakJJLDZCQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQWxpQkE7RUFtaUJBLHlCQUFBO0VBQ0EsOENBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBeElaO0FBMElZO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHNGQUFBO0VBQ0EscUJBQUE7QUF4SWQ7QUEySVk7RUFDRSxtQkE5a0JDO0VBK2tCRCxxQkEva0JDO0VBZ2xCRCwrQ0FBQTtFQUNBLDJCQUFBO0FBeklkO0FBMkljO0VBQ0UsVUFBQTtBQXpJaEI7QUE2SVk7RUFDRSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFubEJIO0VBb2xCRyxxQkFwbEJIO0VBcWxCRyxlQUFBO0VBQ0EsZ0JBQUE7QUEzSWQ7QUE4SVk7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtBQTVJZDtBQW9KSTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSxXQTdsQks7SUE4bEJMLG9CQUFBO0VBbEpOO0VBb0pNO0lBQ0UsZUFBQTtJQUNBLHVCQUFBO0VBbEpSO0VBcUpNO0lBQ0UsdUJBQUE7RUFuSlI7RUFxSlE7SUFDRSxlQUFBO0lBQ0EsV0FBQTtFQW5KVjtBQUNGO0FBMEpFO0VBQ0UsT0FBQTtFQTdsQkYsbUJBcENNO0VBcUNOLHNCQWRVO0VBZVYsaUZBWFU7RUFZViwwQ0FBQTtFQTRsQkUsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXhuQlM7QUFtZWI7QUF1Skk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUFySk47QUF3Sk07RUFDRSxVQUFBO0FBdEpSO0FBeUpNO0VBQ0UsbUJBbnBCRTtBQTRmVjtBQTBKTTtFQUNFLG1DQUFBO0VBQ0Esa0JBQUE7QUF4SlI7QUEwSlE7RUFDRSxtQ0FBQTtBQXhKVjtBQTZKTTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQXJwQk07QUEwZmQ7QUE2SlE7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0FBM0pWO0FBNkpVO0VBQ0Usc0JBOXBCQztBQW1nQmI7QUE2Slk7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQS9xQkg7QUFvaEJYO0FBK0pVO0VBQ0UscUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FsckJEO0FBcWhCWDtBQWdLVTtFQUNFLFNBQUE7RUFDQSxjQXpyQkQ7RUEwckJDLGVBQUE7RUFDQSxnQkFBQTtBQTlKWjtBQW9LTTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQTVyQk07QUEwaEJkO0FBb0tRO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQWxLVjtBQW9LVTtFQUNFLHNCQXJzQkM7QUFtaUJiO0FBb0tZO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsY0FodUJKO0VBaXVCSSxvQ0FBQTtBQWxLZDtBQXNLVTtFQUNFLHFCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBMXRCRDtBQXNqQlg7QUF1S1U7RUFDRSxTQUFBO0VBQ0EsY0FqdUJEO0VBa3VCQyxlQUFBO0VBQ0EsZ0JBQUE7QUFyS1o7QUEyS007RUFDRSwrQkFBQTtFQUNBLGdDQUFBO0FBektSO0FBMktRO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsWUF4dUJHO0FBK2pCYjtBQTJLVTtFQUNFLG1CQTF2Qko7RUEydkJJLHlCQUFBO0VBQ0Esc0JBcnVCQTtFQXN1QkEsZ0JBOXVCQztFQSt1QkQsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FsdkJDO0VBbXZCRCw2QkFBQTtFQUNBLDJDQXh1QkE7QUErakJaO0FBMktZO0VBQ0UsaUZBMXVCRjtFQTJ1QkUscUNBQUE7QUF6S2Q7QUE0S1k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQXR2QkY7RUF1dkJFLHNGQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0FBMUtkO0FBNEtjO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtFQUNBLGNBN3hCTjtBQW1uQlY7QUE4S1k7RUFDRSxPQUFBO0VBQ0EsWUFBQTtBQTVLZDtBQThLYztFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXp4Qkw7RUEweEJLLGtCQUFBO0VBQ0EsZ0JBQUE7QUE1S2hCO0FBK0tjO0VBQ0Usa0JBQUE7RUFDQSxjQWx5Qkw7RUFteUJLLGdCQUFBO0VBQ0EsZ0JBQUE7QUE3S2hCO0FBcUxNO0VBQ0UsT0FBQTtFQUNBLGFBcnlCSztFQXN5QkwsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0F4eUJLO0VBeXlCTCxvQkFBQTtBQW5MUjtBQXFMUTtFQUNFLG1CQTV6QkY7RUE2ekJFLHlCQUFBO0VBQ0Esc0JBdnlCRTtFQXd5QkYsZ0JBaHpCRztFQWl6QkgsMkNBdHlCRTtFQXV5QkYsNkJBQUE7QUFuTFY7QUFxTFU7RUFDRSxpRkF6eUJBO0VBMHlCQSxxQ0FBQTtBQW5MWjtBQXNMVTtFQUNFLHNCQTF6QkM7RUEyekJELHNCQTV6QkM7RUE2ekJELGdDQUFBO0FBcExaO0FBc0xZO0VBQ0UsU0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXgwQkg7QUFvcEJYO0FBd0xVO0VBQ0UsZUFBQTtFQUNBLHdCQUFBO0FBdExaOztBQStMQTtFQUNFO0lBQUssdUJBQUE7RUEzTEw7RUE0TEE7SUFBTyx5QkFBQTtFQXpMUDtBQUNGO0FBMkxBO0VBQ0U7SUFDRSx5REFBQTtFQXpMRjtFQTJMQTtJQUNFLDRGQUFBO0VBekxGO0VBMkxBO0lBQ0UseURBQUE7RUF6TEY7QUFDRjtBQTZMQTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSwwQkFBQTtJQUNBLDhCQUFBO0VBM0xGO0VBOExBO0lBQ0UsV0FBQTtJQUNBLGtCQUFBO0lBQ0EsZ0NBQUE7SUFDQSxjQUFBO0lBQ0EsZ0JBQUE7SUFDQSxnQkFBQTtFQTVMRjtFQThMRTtJQUNFLHVCQUFBO0VBNUxKO0VBK0xFO0lBQ0UsZUF6M0JPO0VBNHJCWDtFQWlNSTtJQUNFLGlCQUFBO0lBQ0EsWUEvM0JLO0VBZ3NCWDtFQW9NQTtJQUNFLGdCQXI0QlM7SUFzNEJULE9BQUE7SUFDQSxhQUFBO0VBbE1GO0FBQ0Y7QUFxTUE7RUFFSTtJQUNFLGdCQTk0Qk87RUEwc0JYO0VBd01JO0lBQ0UsV0FsNUJLO0VBNHNCWDtFQXlNSTtJQUNFLHNCQUFBO0lBQ0EsWUF4NUJLO0VBaXRCWDtFQXlNTTs7SUFFRSxVQUFBO0lBQ0EsWUFBQTtFQXZNUjtFQTZNQTtJQUNFLGdCQXA2QlM7SUFxNkJULFlBcjZCUztFQTB0Qlg7RUE4TUk7SUFDRSxzQkFBQTtJQUNBLFdBejZCSztJQTA2QkwsZUExNkJLO0lBMjZCTCxvQkFBQTtFQTVNTjtFQThNTTtJQUNFLHVCQUFBO0lBQ0Esa0JBQUE7RUE1TVI7RUErTU07SUFDRSxXQUFBO0lBQ0EsdUJBQUE7RUE3TVI7RUErTVE7SUFDRSxlQUFBO0VBN01WO0VBK01VO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUE3TVo7RUFzTk07SUFDRSxnQkFwOEJHO0VBZ3ZCWDtBQUNGO0FBNk5FO0VBQ0UsV0FBQTtBQTNOSjtBQTZOSTtFQUNFLDhCQUFBO0VBQ0Esa0NBQUE7RUFDQSxvQ0FBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0FBM05OO0FBNk5NO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHNEQUFBO0FBM05SO0FBK05JO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHdEQUFBO0FBN05OO0FBZ09JO0VBQ0UsMkJBQUE7RUFDQSwyQkFBQTtBQTlOTjtBQWlPSTtFQUNFLHdCQUFBO0FBL05OO0FBa09JO0VBQ0Usd0JBQUE7QUFoT047QUFvT0k7RUFDRSw2QkFBQTtFQUNBLDRCQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtBQWxPTjtBQW9PTTtFQUNFLHlCQUFBO0VBQ0EsMkJBQUE7QUFsT1I7QUFxT007RUFDRSx5QkFBQTtFQUNBLGtDQUFBO0FBbk9SO0FBd09JO0VBQ0UsNkJBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EseUJBQUE7QUF0T047QUEwT0k7RUFDRSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0EsMkJBQUE7RUFDQSxxQkFBQTtBQXhPTjtBQTJPSTtFQUNFLDBCQUFBO0FBek9OO0FBNE9JO0VBQ0UseUJBQUE7RUFDQSwwQkFBQTtFQUNBLDBDQUFBO0FBMU9OO0FBNk9JO0VBQ0UseUJBQUE7QUEzT047QUE4T0k7RUFDRSx5QkFBQTtBQTVPTjtBQWlQTTtFQUNFLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSxnQ0FBQTtBQS9PUjtBQXFQRTtFQUNFLGtDQUFBO0VBQ0Esd0JBQUE7RUFDQSw0QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7RUFDQSwyQkFBQTtFQUNBLDJCQUFBO0VBQ0EseUNBQUE7QUFuUEo7QUFzUEk7RUFDRSx3QkFBQTtFQUNBLDJCQUFBO0FBcFBOO0FBdVBJO0VBQ0UsK0NBQUE7RUFDQSxxQ0FBQTtBQXJQTjtBQXdQSTtFQUNFLCtDQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtBQXRQTjtBQXlQSTtFQUNFLGdDQUFBO0VBQ0EsNEJBQUE7RUFDQSx5QkFBQTtBQXZQTjtBQTJQSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0FBelBOO0FBMlBNO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtFQUNBLE9BQUE7QUF6UFI7QUEyUFE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0ExbUNDO0VBMm1DRCxnQkFBQTtBQXpQVjtBQTRQUTtFQUNFLGtCQUFBO0VBQ0EsY0FubkNDO0VBb25DRCxnQkFBQTtFQUNBLGdCQUFBO0FBMVBWO0FBZ1FJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLFVBQUE7QUE5UE47QUFnUU07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxPQUFBO0VBQ0EsWUFBQTtBQTlQUjtBQWdRUTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQXZvQ0M7RUF3b0NELGdCQUFBO0FBOVBWO0FBcVFFO0VBQ0UseUJBQUE7RUFDQSx3QkFBQTtBQW5RSjtBQXVRRTs7O0VBR0Usa0NBQUE7RUFDQSwyQkFBQTtFQUNBLCtCQUFBO0VBQ0EsMEJBQUE7RUFDQSw2QkFBQTtBQXJRSjtBQXdRRTtFQUNFLHNEQUFBO0FBdFFKO0FBd1FJO0VBQ0UsNEZBQUE7QUF0UU47QUEwUUU7RUFDRSw0QkFBQTtBQXhRSjtBQTRRRTtFQUNFLDhCQUFBO0FBMVFKO0FBNlFFO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0FBM1FKO0FBNlFJO0VBQ0UseUJBQUE7QUEzUU47QUErUUU7RUFDRSxzQkFBQTtFQUNBLHVCQUFBO0FBN1FKO0FBaVJFO0VBQ0UsbUNBQUE7RUFDQSwyQ0FBQTtFQUNBLDhCQUFBO0VBQ0Esb0JBQUE7RUFDQSwyQkFBQTtBQS9RSjtBQWlSSTtFQUNFLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2QkFBQTtFQUNBLHFCQUFBO0VBQ0EsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLDhDQUFBO0VBQ0EseUJBQUE7RUFDQSxvREFBQTtFQUNBLG9DQUFBO0VBQ0EsMkJBQUE7RUFDQSw0QkFBQTtBQS9RTjtBQWlSTTtFQUNFLCtDQUFBO0VBQ0EsZ0RBQUE7QUEvUVIiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb2xvciBQYWxldHRlIC0gV2hpdGUgJiBNaWxkIE9yYW5nZSBUaGVtZVxuJHByaW1hcnk6ICNmZjZiMzU7XG4kcHJpbWFyeS1saWdodDogI2ZmOGM1YTtcbiRwcmltYXJ5LWRhcms6ICNlNTVhMmI7XG4kc3VjY2VzczogIzEwYjk4MTtcblxuLy8gTmV1dHJhbCBDb2xvcnNcbiR3aGl0ZTogI2ZmZmZmZjtcbiRncmF5LTUwOiAjZjlmYWZiO1xuJGdyYXktMTAwOiAjZjNmNGY2O1xuJGdyYXktMjAwOiAjZTVlN2ViO1xuJGdyYXktMzAwOiAjZDFkNWRiO1xuJGdyYXktNDAwOiAjOWNhM2FmO1xuJGdyYXktNTAwOiAjNmI3MjgwO1xuJGdyYXktNjAwOiAjNGI1NTYzO1xuJGdyYXktNzAwOiAjMzc0MTUxO1xuJGdyYXktODAwOiAjMWYyOTM3O1xuXG4vLyBTcGFjaW5nIC0gUmVkdWNlZCBmb3IgcHJvZmVzc2lvbmFsIGNvbXBhY3QgbG9va1xuJHNwYWNpbmcteHM6IDAuMTI1cmVtO1xuJHNwYWNpbmctc206IDAuMjVyZW07XG4kc3BhY2luZy1tZDogMC41cmVtO1xuJHNwYWNpbmctbGc6IDAuNzVyZW07XG4kc3BhY2luZy14bDogMXJlbTtcbiRzcGFjaW5nLTJ4bDogMS4yNXJlbTtcbiRzcGFjaW5nLTN4bDogMS41cmVtO1xuXG4vLyBCb3JkZXIgUmFkaXVzXG4kcmFkaXVzLXNtOiAwLjM3NXJlbTtcbiRyYWRpdXMtbWQ6IDAuNXJlbTtcbiRyYWRpdXMtbGc6IDAuNzVyZW07XG5cbi8vIFNoYWRvd3NcbiRzaGFkb3ctc206IDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4kc2hhZG93LW1kOiAwIDRweCA2cHggLTFweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMnB4IDRweCAtMXB4IHJnYmEoMCwgMCwgMCwgMC4wNik7XG4kc2hhZG93LWxnOiAwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA0cHggNnB4IC0ycHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcblxuLy8gVHJhbnNpdGlvbnNcbiR0cmFuc2l0aW9uLWZhc3Q6IDAuMTVzIGVhc2Utb3V0O1xuJHRyYW5zaXRpb24tbm9ybWFsOiAwLjNzIGVhc2Utb3V0O1xuXG4vLyBNaXhpbnNcbkBtaXhpbiBjYXJkLXN0eWxlIHtcbiAgYmFja2dyb3VuZDogJHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLWxnO1xuICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKCRncmF5LTIwMCwgMC44KTtcbn1cblxuLy8gTWFpbiBDb250YWluZXJcbi5zbWFydC1kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODZweCk7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NnB4KTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJGdyYXktNTAgMCUsICR3aGl0ZSAxMDAlKTtcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgc2Fucy1zZXJpZjtcbiAgY29sb3I6ICRncmF5LTgwMDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhbGlnbi1pdGVtczogc3RyZXRjaDtcblxuICAmOjpiZWZvcmUge1xuICAgIGNvbnRlbnQ6ICcnO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgMjAlLCByZ2JhKCRwcmltYXJ5LCAwLjA4KSAwJSwgdHJhbnNwYXJlbnQgNTAlKTtcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgICB6LWluZGV4OiAwO1xuICB9XG59XG5cbi8vIExlZnQgU2lkZWJhclxuLmxlZnQtc2lkZWJhciB7XG4gIHdpZHRoOiAyODVweDtcbiAgYmFja2dyb3VuZDogJHdoaXRlO1xuICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAkZ3JheS0yMDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTA7XG4gIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGhlaWdodDogMTAwJTtcblxuICAuc2lkZWJhci1zZWN0aW9uIHtcbiAgICAmLmRhc2hib2FyZC1zZWxlY3Rvci1zZWN0aW9uIHtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgcGFkZGluZzogJHNwYWNpbmctbGc7XG4gICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGdyYXktMTAwO1xuICAgIH1cblxuICAgICYuZmlsdGVycy1zZWN0aW9uIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZCAkc3BhY2luZy1sZyAkc3BhY2luZy14cyAkc3BhY2luZy1sZztcbiAgICAgIG1pbi1oZWlnaHQ6IDA7XG4gICAgfVxuXG4gICAgJi5tb2RlLXNlY3Rpb24ge1xuICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy14cyAkc3BhY2luZy1sZyAkc3BhY2luZy1zbSAkc3BhY2luZy1sZztcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0xMDA7XG4gICAgfVxuXG4gICAgJi5yZXNldC1zZWN0aW9uIHtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgcGFkZGluZzogJHNwYWNpbmctbWQgJHNwYWNpbmctbGcgJHNwYWNpbmctbGcgJHNwYWNpbmctbGc7XG4gICAgICBiYWNrZ3JvdW5kOiAkZ3JheS01MDtcbiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAkZ3JheS0xMDA7XG4gICAgfVxuXG4gICAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLW1kO1xuICAgICAgcGFkZGluZy10b3A6ICRzcGFjaW5nLXNtO1xuXG4gICAgICAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuXG4gICAgICAgIC5zZWN0aW9uLWljb24ge1xuICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICAgICAgaGVpZ2h0OiAxOHB4O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5zZWN0aW9uLXRpdGxlIHtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjJweDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5maWx0ZXItYmFkZ2Uge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICRwcmltYXJ5O1xuICAgICAgICAgIGNvbG9yOiAkd2hpdGU7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICAgIHdpZHRoOiAxLjEyNXJlbTtcbiAgICAgICAgICBoZWlnaHQ6IDEuMTI1cmVtO1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBmb250LXNpemU6IDAuNjVyZW07XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBtYXJnaW4tbGVmdDogYXV0bztcbiAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gRGFzaGJvYXJkIFNlbGVjdG9yIFNlY3Rpb25cbiAgLmRhc2hib2FyZC1zZWxlY3Rvci1zZWN0aW9uIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKCRwcmltYXJ5LCAwLjA2KSwgcmdiYSgkcHJpbWFyeSwgMC4wMikpO1xuICAgIGJvcmRlcjogMnB4IHNvbGlkIHJnYmEoJHByaW1hcnksIDAuMTUpO1xuICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbGc7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoJHByaW1hcnksIDAuMDgpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKCRwcmltYXJ5LCAwLjA4KSwgcmdiYSgkcHJpbWFyeSwgMC4wNCkpO1xuICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjIpO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKCRwcmltYXJ5LCAwLjEyKTtcbiAgICB9XG5cbiAgICAuZGFzaGJvYXJkLXNlbGVjdG9yIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuXG4gICAgICAvLyBFbmhhbmNlZCBzdHlsaW5nIGZvciB0aGUgZGFzaGJvYXJkIHNlbGVjdG9yIGZpZWxkXG4gICAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGUgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgJHByaW1hcnkgIWltcG9ydGFudDtcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoJHByaW1hcnksIDAuMDgpICFpbXBvcnRhbnQ7XG4gICAgICAgIGhlaWdodDogNDRweCAhaW1wb3J0YW50O1xuICAgICAgICBtaW4taGVpZ2h0OiA0NHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbWQgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyayAhaW1wb3J0YW50O1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEwcHggcmdiYSgkcHJpbWFyeSwgMC4xNSkgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmLm1hdC1mb2N1c2VkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZGFyayAhaW1wb3J0YW50O1xuICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgkcHJpbWFyeSwgMC4xNSksIDAgNHB4IDEycHggcmdiYSgkcHJpbWFyeSwgMC4yKSAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAubWF0LW1kYy1mb3JtLWZpZWxkLWluZml4IHtcbiAgICAgICAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIG1pbi1oZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gTW9kZSBTZWxlY3Rpb24gU2VjdGlvblxuICAubW9kZS1zZWN0aW9uIHtcbiAgICAuc2VjdGlvbi1oZWFkZXIge1xuICAgICAgbWFyZ2luLXRvcDogMDtcbiAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLXhzO1xuICAgICAgcGFkZGluZy10b3A6IDA7XG4gICAgfVxuXG4gICAgLm1vZGUtc2VsZWN0aW9uLWNvbnRhaW5lciB7XG4gICAgICAubW9kZS1vcHRpb25zIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcblxuICAgICAgICAubW9kZS1vcHRpb24ge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcbiAgICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kO1xuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwO1xuXG4gICAgICAgICAgJi5hY3RpdmUge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4wOCk7XG4gICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuMyk7XG5cbiAgICAgICAgICAgIC5tb2RlLXJhZGlvIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAubW9kZS10aXRsZSB7XG4gICAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeTtcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLm1vZGUtZGVzYyB7XG4gICAgICAgICAgICAgIGNvbG9yOiBkYXJrZW4oJHByaW1hcnksIDUlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmOm5vdCguYWN0aXZlKSB7XG4gICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkZ3JheS0xMDAsIDAuOCk7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5tb2RlLXJhZGlvIHtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgICAgbWFyZ2luLXRvcDogMXB4O1xuXG4gICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgICAgICAgICB3aWR0aDogMThweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAxOHB4O1xuICAgICAgICAgICAgICBjb2xvcjogJGdyYXktNDAwO1xuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5tb2RlLWNvbnRlbnQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgZ2FwOiAycHg7XG5cbiAgICAgICAgICAgIC5tb2RlLXRpdGxlIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICAgIGNvbG9yOiAkZ3JheS03MDA7XG4gICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjM7XG4gICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWw7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5tb2RlLWRlc2Mge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuN3JlbTtcbiAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTUwMDtcbiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMztcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBGaWx0ZXJzIFNlY3Rpb25cbiAgLmZpbHRlcnMtc2VjdGlvbiB7XG4gICAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICAgIG1hcmdpbi10b3A6IDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAkc3BhY2luZy1zbTtcbiAgICB9XG5cbiAgICAuZmlsdGVycy1jb250ZW50IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICBvdmVyZmxvdy15OiBhdXRvO1xuICAgICAgb3ZlcmZsb3cteDogaGlkZGVuO1xuICAgICAgcGFkZGluZy1yaWdodDogJHNwYWNpbmcteHM7XG4gICAgICBwYWRkaW5nLWJvdHRvbTogJHNwYWNpbmctc207XG5cbiAgICAgIC8vIEN1c3RvbSBzY3JvbGxiYXIgLSBvbmx5IHZlcnRpY2FsXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgIHdpZHRoOiAzcHg7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4yKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMyk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmZpbHRlci1ncm91cCB7XG4gICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuXG4gICAgICAgIC5maWx0ZXItbGFiZWwge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLXhzO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgY29sb3I6ICRncmF5LTcwMDtcbiAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4ycHg7XG5cbiAgICAgICAgICAubGFiZWwtaWNvbiB7XG4gICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgICAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5sYWJlbC10ZXh0IHtcbiAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5zZWxlY3Rpb24tY291bnQge1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjY1cmVtO1xuICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMSk7XG4gICAgICAgICAgICBwYWRkaW5nOiAycHggNnB4O1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbTtcbiAgICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5maWx0ZXItZmllbGQge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gUmVzZXQgU2VjdGlvblxuICAucmVzZXQtc2VjdGlvbiB7XG4gICAgLnJlc2V0LWFjdGlvbnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuXG4gICAgICAucmVzZXQtYnRuIHtcbiAgICAgICAgd2lkdGg6IDE0MHB4O1xuICAgICAgICBoZWlnaHQ6IDM2cHg7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbWQ7XG4gICAgICAgIGNvbG9yOiAkZ3JheS03MDA7XG4gICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwO1xuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlO1xuICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgICB9XG5cbiAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogJHNwYWNpbmcteHM7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gTWFpbiBDb250ZW50IEFyZWFcbi5tYWluLWNvbnRlbnQge1xuICBmbGV4OiAxO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBwYWRkaW5nOiAwO1xuICBnYXA6IDA7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbiAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgLy8gR3JhZGllbnQgSGlnaGxpZ2h0IEJhclxuICAuZ3JhZGllbnQtaGlnaGxpZ2h0LWJhciB7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gICAgbWFyZ2luOiAwO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoJHByaW1hcnksIDAuMDYpLCByZ2JhKCRwcmltYXJ5LCAwLjAyKSk7XG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgkcHJpbWFyeSwgMC4xNSk7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1sZztcbiAgICBtYXJnaW46ICRzcGFjaW5nLW1kO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKCRwcmltYXJ5LCAwLjA4KTtcblxuICAgIC5oaWdobGlnaHQtY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcbiAgICAgIG1pbi1oZWlnaHQ6IDYwcHg7XG5cbiAgICAgIC5oaWdobGlnaHQtbGVmdCB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGdhcDogJHNwYWNpbmctc207XG4gICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICBtaW4td2lkdGg6IDI5MHB4O1xuXG4gICAgICAgIC5oaWdobGlnaHQtaWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgd2lkdGg6IDEuMXJlbTtcbiAgICAgICAgICBoZWlnaHQ6IDEuMXJlbTtcbiAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiAkc3BhY2luZy14cztcbiAgICAgICAgfVxuXG4gICAgICAgIC5oaWdobGlnaHQtdGl0bGUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6ICRzcGFjaW5nLXhzO1xuICAgICAgICB9XG5cbiAgICAgICAgLmhpZ2hsaWdodC1zdGF0dXMge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICAgICAgICBjb2xvcjogJGdyYXktNjAwO1xuICAgICAgICAgIHBhZGRpbmc6IDNweCA4cHg7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbTtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjEpO1xuICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcblxuICAgICAgICAgICYucmVhZHkge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkc3VjY2VzcywgMC4xKTtcbiAgICAgICAgICAgIGNvbG9yOiAkc3VjY2VzcztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmhpZ2hsaWdodC1yaWdodCB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XG5cbiAgICAgICAgLmFpLWlucHV0LWNvbnRhaW5lciB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogJHNwYWNpbmctbWQ7XG4gICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuXG4gICAgICAgICAgJi5kaXNhYmxlZC1tb2RlIHtcbiAgICAgICAgICAgIG9wYWNpdHk6IDAuNjtcblxuICAgICAgICAgICAgLmFpLWlucHV0LWZpZWxkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRncmF5LTUwICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnNlbmQtYnRuIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC42KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuNikgIWltcG9ydGFudDtcblxuICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKCRwcmltYXJ5LCAwLjcpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjcpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmOm5vdCguZGlzYWJsZWQtbW9kZSkge1xuICAgICAgICAgICAgLmFpLWlucHV0LWZpZWxkIC5tYXQtbWRjLXRleHQtZmllbGQtd3JhcHBlciB7XG4gICAgICAgICAgICAgIGFuaW1hdGlvbjogc3VidGxlLXB1bHNlIDJzIGVhc2UtaW4tb3V0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5haS1pbnB1dC1maWVsZCB7XG4gICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgbWluLXdpZHRoOiAwO1xuXG4gICAgICAgICAgICAvLyBFbmhhbmNlZCBzdHlsaW5nIHRvIG1hdGNoIGRhc2hib2FyZCBzZWxlY3RvclxuICAgICAgICAgICAgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICRwcmltYXJ5ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDZweCByZ2JhKCRwcmltYXJ5LCAwLjA4KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBoZWlnaHQ6IDQ0cHggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgbWluLWhlaWdodDogNDRweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1ub3JtYWwgIWltcG9ydGFudDtcblxuICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICRwcmltYXJ5LWRhcmsgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMHB4IHJnYmEoJHByaW1hcnksIDAuMTUpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJi5tYXQtZm9jdXNlZCAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICRwcmltYXJ5LWRhcmsgIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoJHByaW1hcnksIDAuMTUpLCAwIDRweCAxMnB4IHJnYmEoJHByaW1hcnksIDAuMikgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLm1hdC1tZGMtZm9ybS1maWVsZC1pbmZpeCB7XG4gICAgICAgICAgICAgIHBhZGRpbmc6IDEycHggMTZweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgICBtaW4taGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpbnB1dCB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuc2VuZC1idG4ge1xuICAgICAgICAgICAgd2lkdGg6IDQ0cHg7XG4gICAgICAgICAgICBoZWlnaHQ6IDQ0cHg7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkcHJpbWFyeTtcbiAgICAgICAgICAgIGNvbG9yOiAkd2hpdGU7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kO1xuICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgJHByaW1hcnk7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCk7XG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gICAgICAgICAgICAmOjpiZWZvcmUge1xuICAgICAgICAgICAgICBjb250ZW50OiAnJztcbiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICB0b3A6IDA7XG4gICAgICAgICAgICAgIGxlZnQ6IC0xMDAlO1xuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCByZ2JhKCR3aGl0ZSwgMC4yKSwgdHJhbnNwYXJlbnQpO1xuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNXM7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICY6aG92ZXI6bm90KFtkaXNhYmxlZF0pIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJHByaW1hcnktZGFyaztcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1kYXJrO1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMHB4IHJnYmEoJHByaW1hcnksIDAuMTUpO1xuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG5cbiAgICAgICAgICAgICAgJjo6YmVmb3JlIHtcbiAgICAgICAgICAgICAgICBsZWZ0OiAxMDAlO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICZbZGlzYWJsZWRdIHtcbiAgICAgICAgICAgICAgb3BhY2l0eTogMC40O1xuICAgICAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAkZ3JheS0zMDA7XG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGdyYXktMzAwO1xuICAgICAgICAgICAgICB0cmFuc2Zvcm06IG5vbmU7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IG5vbmU7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgICAgICAgICAgei1pbmRleDogMTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBSZXNwb25zaXZlIERlc2lnblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLmhpZ2hsaWdodC1jb250ZW50IHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1tZDtcbiAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG5cbiAgICAgICAgLmhpZ2hsaWdodC1sZWZ0IHtcbiAgICAgICAgICBtaW4td2lkdGg6IGF1dG87XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIH1cblxuICAgICAgICAuaGlnaGxpZ2h0LXJpZ2h0IHtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgIC5haS1pbnB1dC1jb250YWluZXIge1xuICAgICAgICAgICAgbWF4LXdpZHRoOiBub25lO1xuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gRGFzaGJvYXJkIFNlY3Rpb25cbiAgLmRhc2hib2FyZC1zZWN0aW9uIHtcbiAgICBmbGV4OiAxO1xuICAgIEBpbmNsdWRlIGNhcmQtc3R5bGU7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgbWFyZ2luOiAkc3BhY2luZy1tZDtcblxuICAgIC5kYXNoYm9hcmQtY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBvdmVyZmxvdy15OiBhdXRvO1xuXG4gICAgICAvLyBDdXN0b20gc2Nyb2xsYmFyXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgIHdpZHRoOiA2cHg7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICAgICAgYmFja2dyb3VuZDogJGdyYXktNTA7XG4gICAgICB9XG5cbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4yKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMyk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRW1wdHkgU3RhdGVcbiAgICAgIC5lbXB0eS1zdGF0ZSB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy0yeGw7XG5cbiAgICAgICAgLmVtcHR5LXN0YXRlLWNvbnRlbnQge1xuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICBtYXgtd2lkdGg6IDQ1MHB4O1xuXG4gICAgICAgICAgLmVtcHR5LXN0YXRlLWljb24ge1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbGc7XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAzcmVtO1xuICAgICAgICAgICAgICB3aWR0aDogM3JlbTtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAzcmVtO1xuICAgICAgICAgICAgICBjb2xvcjogJGdyYXktMzAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5lbXB0eS1zdGF0ZS10aXRsZSB7XG4gICAgICAgICAgICBtYXJnaW46IDAgMCAkc3BhY2luZy1zbSAwO1xuICAgICAgICAgICAgZm9udC1zaXplOiAxLjM1cmVtO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmVtcHR5LXN0YXRlLWRlc2NyaXB0aW9uIHtcbiAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBMb2FkaW5nIFN0YXRlXG4gICAgICAubG9hZGluZy1zdGF0ZSB7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy0yeGw7XG5cbiAgICAgICAgLmxvYWRpbmctY29udGVudCB7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgIG1heC13aWR0aDogNDAwcHg7XG5cbiAgICAgICAgICAubG9hZGluZy1zcGlubmVyIHtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nLWxnO1xuXG4gICAgICAgICAgICAuc3BpbiB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogM3JlbTtcbiAgICAgICAgICAgICAgd2lkdGg6IDNyZW07XG4gICAgICAgICAgICAgIGhlaWdodDogM3JlbTtcbiAgICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5O1xuICAgICAgICAgICAgICBhbmltYXRpb246IHNwaW4gMS41cyBsaW5lYXIgaW5maW5pdGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmxvYWRpbmctdGl0bGUge1xuICAgICAgICAgICAgbWFyZ2luOiAwIDAgJHNwYWNpbmctc20gMDtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4zNXJlbTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBjb2xvcjogJGdyYXktODAwO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5sb2FkaW5nLWRlc2NyaXB0aW9uIHtcbiAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgIGNvbG9yOiAkZ3JheS02MDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBEYXNoYm9hcmQgU3VtbWFyeVxuICAgICAgLmRhc2hib2FyZC1zdW1tYXJ5IHtcbiAgICAgICAgcGFkZGluZzogJHNwYWNpbmcteGwgJHNwYWNpbmcteGwgJHNwYWNpbmctbGcgJHNwYWNpbmcteGw7XG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkZ3JheS0yMDA7XG5cbiAgICAgICAgLnN1bW1hcnktY2FyZHMge1xuICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7XG4gICAgICAgICAgZ2FwOiAkc3BhY2luZy1sZztcblxuICAgICAgICAgIC5zdW1tYXJ5LWNhcmQge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJHdoaXRlO1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGdyYXktMjAwO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1sZztcbiAgICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLWxnO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbDtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XG5cbiAgICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICAgICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoJHByaW1hcnksIDAuMik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5zdW1tYXJ5LWljb24ge1xuICAgICAgICAgICAgICB3aWR0aDogNDhweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0OHB4O1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLW1kO1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKCRwcmltYXJ5LCAwLjEpLCByZ2JhKCRwcmltYXJ5LCAwLjA1KSk7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgICBmbGV4LXNocmluazogMDtcblxuICAgICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgICAgICAgICAgICAgd2lkdGg6IDEuNXJlbTtcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDEuNXJlbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnN1bW1hcnktY29udGVudCB7XG4gICAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgICAgICAgICAuc3VtbWFyeS12YWx1ZSB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG4gICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC5zdW1tYXJ5LWxhYmVsIHtcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICAgICAgY29sb3I6ICRncmF5LTYwMDtcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQ2hhcnRzIENvbnRhaW5lclxuICAgICAgLmNoYXJ0cy1jb250YWluZXIge1xuICAgICAgICBmbGV4OiAxO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy14bDtcbiAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCg0MDBweCwgMWZyKSk7XG4gICAgICAgIGdhcDogJHNwYWNpbmcteGw7XG4gICAgICAgIGFsaWduLWNvbnRlbnQ6IHN0YXJ0O1xuXG4gICAgICAgIDo6bmctZGVlcCAuY2hhcnQtY29udGFpbmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGU7XG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGdyYXktMjAwO1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtbGc7XG4gICAgICAgICAgcGFkZGluZzogJHNwYWNpbmctbGc7XG4gICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbTtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgJHRyYW5zaXRpb24tbm9ybWFsO1xuXG4gICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKCRwcmltYXJ5LCAwLjIpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5jaGFydC1oZWFkZXIge1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogJHNwYWNpbmctbGc7XG4gICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogJHNwYWNpbmctbWQ7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGdyYXktMTAwO1xuXG4gICAgICAgICAgICBoMyB7XG4gICAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY2FudmFzIHtcbiAgICAgICAgICAgIG1heC13aWR0aDogMTAwJTtcbiAgICAgICAgICAgIGhlaWdodDogMzAwcHggIWltcG9ydGFudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gQW5pbWF0aW9uc1xuQGtleWZyYW1lcyBzcGluIHtcbiAgMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxufVxuXG5Aa2V5ZnJhbWVzIHN1YnRsZS1wdWxzZSB7XG4gIDAlIHtcbiAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgfVxuICA1MCUge1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDZweCByZ2JhKCRwcmltYXJ5LCAwLjE1KSwgMCAwIDAgMnB4IHJnYmEoJHByaW1hcnksIDAuMSkgIWltcG9ydGFudDtcbiAgfVxuICAxMDAlIHtcbiAgICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgfVxufVxuXG4vLyBSZXNwb25zaXZlIERlc2lnblxuQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAuc21hcnQtZGFzaGJvYXJkLWNvbnRhaW5lciB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0NHB4KTtcbiAgICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDRweCk7XG4gIH1cblxuICAubGVmdC1zaWRlYmFyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRncmF5LTIwMDtcbiAgICBmbGV4LXNocmluazogMDtcbiAgICBtYXgtaGVpZ2h0OiA0NXZoO1xuICAgIG92ZXJmbG93LXk6IGF1dG87XG5cbiAgICAuc2lkZWJhci1zZWN0aW9uIHtcbiAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtICRzcGFjaW5nLW1kO1xuICAgIH1cblxuICAgIC5kYXNoYm9hcmQtc2VsZWN0b3Itc2VjdGlvbiB7XG4gICAgICBtYXJnaW46ICRzcGFjaW5nLXNtO1xuICAgIH1cblxuICAgIC5maWx0ZXJzLXNlY3Rpb24ge1xuICAgICAgLmZpbHRlcnMtY29udGVudCB7XG4gICAgICAgIG1heC1oZWlnaHQ6IDIwMHB4O1xuICAgICAgICBnYXA6ICRzcGFjaW5nLXNtO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtO1xuICAgIGZsZXg6IDE7XG4gICAgbWluLWhlaWdodDogMDtcbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmxlZnQtc2lkZWJhciB7XG4gICAgLnNpZGViYXItc2VjdGlvbiB7XG4gICAgICBwYWRkaW5nOiAkc3BhY2luZy1zbTtcbiAgICB9XG5cbiAgICAuZmlsdGVycy1zZWN0aW9uIHtcbiAgICAgIC5maWx0ZXJzLWNvbnRlbnQge1xuICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgfVxuXG4gICAgICAuZmlsdGVycy1hY3Rpb25zIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAkc3BhY2luZy1zbTtcblxuICAgICAgICAucmVzZXQtYnRuLFxuICAgICAgICAuYXBwbHktYnRuIHtcbiAgICAgICAgICBmbGV4OiBub25lO1xuICAgICAgICAgIGhlaWdodDogMzJweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6ICRzcGFjaW5nLXNtO1xuICAgIGdhcDogJHNwYWNpbmctc207XG5cbiAgICAuZ3JhZGllbnQtaGlnaGxpZ2h0LWJhciB7XG4gICAgICAuaGlnaGxpZ2h0LWNvbnRlbnQge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBnYXA6ICRzcGFjaW5nLW1kO1xuICAgICAgICBwYWRkaW5nOiAkc3BhY2luZy1tZDtcbiAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG5cbiAgICAgICAgLmhpZ2hsaWdodC1sZWZ0IHtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBhbGlnbi1zZWxmOiBjZW50ZXI7XG4gICAgICAgIH1cblxuICAgICAgICAuaGlnaGxpZ2h0LXJpZ2h0IHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgIC5haS1pbnB1dC1jb250YWluZXIge1xuICAgICAgICAgICAgbWF4LXdpZHRoOiBub25lO1xuXG4gICAgICAgICAgICAuc2VuZC1idG4ge1xuICAgICAgICAgICAgICB3aWR0aDogNDBweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5kYXNoYm9hcmQtc2VjdGlvbiB7XG4gICAgICAuZGFzaGJvYXJkLWNvbnRlbnQge1xuICAgICAgICAuZW1wdHktc3RhdGUge1xuICAgICAgICAgIHBhZGRpbmc6ICRzcGFjaW5nLWxnO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIEFuZ3VsYXIgTWF0ZXJpYWwgT3ZlcnJpZGVzXG46Om5nLWRlZXAge1xuICAvLyBGb3JtIEZpZWxkIFN0eWxpbmdcbiAgLm1hdC1tZGMtZm9ybS1maWVsZCB7XG4gICAgd2lkdGg6IDEwMCU7XG5cbiAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgYmFja2dyb3VuZDogJGdyYXktNTAgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1yYWRpdXM6ICRyYWRpdXMtc20gIWltcG9ydGFudDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRncmF5LTIwMCAhaW1wb3J0YW50O1xuICAgICAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogYWxsICR0cmFuc2l0aW9uLW5vcm1hbCAhaW1wb3J0YW50O1xuICAgICAgaGVpZ2h0OiAzOHB4ICFpbXBvcnRhbnQ7XG4gICAgICBtaW4taGVpZ2h0OiAzOHB4ICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiAkd2hpdGUgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgIWltcG9ydGFudDtcbiAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cblxuICAgICYubWF0LWZvY3VzZWQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICR3aGl0ZSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoJHByaW1hcnksIDAuMSkgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAubWF0LW1kYy1mb3JtLWZpZWxkLWluZml4IHtcbiAgICAgIHBhZGRpbmc6IDZweCA4cHggIWltcG9ydGFudDtcbiAgICAgIG1pbi1oZWlnaHQ6IDE4cHggIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAubWF0LW1kYy1mb3JtLWZpZWxkLXN1YnNjcmlwdC13cmFwcGVyIHtcbiAgICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAubWRjLW5vdGNoZWQtb3V0bGluZSB7XG4gICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLy8gSW5wdXQgc3R5bGluZ1xuICAgIC5tYXQtbWRjLWlucHV0LWVsZW1lbnQge1xuICAgICAgZm9udC1zaXplOiAwLjg1cmVtICFpbXBvcnRhbnQ7XG4gICAgICBsaW5lLWhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgY29sb3I6ICRncmF5LTgwMCAhaW1wb3J0YW50O1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50O1xuXG4gICAgICAmOjpwbGFjZWhvbGRlciB7XG4gICAgICAgIGNvbG9yOiAkZ3JheS00MDAgIWltcG9ydGFudDtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMCAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAmOmRpc2FibGVkIHtcbiAgICAgICAgY29sb3I6ICRncmF5LTQwMCAhaW1wb3J0YW50O1xuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFNlbGVjdCBzdHlsaW5nXG4gICAgLm1hdC1tZGMtc2VsZWN0IHtcbiAgICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkZ3JheS04MDAgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAvLyBTZWxlY3QgdHJpZ2dlciBzdHlsaW5nIGZvciBkYXNoYm9hcmQgc2VsZWN0b3JcbiAgICAmLmRhc2hib2FyZC1zZWxlY3RvciAubWF0LW1kYy1zZWxlY3QtdHJpZ2dlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICBtaW4taGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XG4gICAgICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtc2VsZWN0LWFycm93LXdyYXBwZXIge1xuICAgICAgdHJhbnNmb3JtOiBub25lICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtc2VsZWN0LWFycm93IHtcbiAgICAgIGNvbG9yOiAkZ3JheS01MDAgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtc2l6ZTogMjBweCAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogY29sb3IgJHRyYW5zaXRpb24tbm9ybWFsICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgJi5tYXQtZm9jdXNlZCAubWF0LW1kYy1zZWxlY3QtYXJyb3cge1xuICAgICAgY29sb3I6ICRwcmltYXJ5ICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgJjpob3ZlciAubWF0LW1kYy1zZWxlY3QtYXJyb3cge1xuICAgICAgY29sb3I6ICRncmF5LTYwMCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC8vIFByZWZpeCBpY29uIHN0eWxpbmdcbiAgICAubWF0LW1kYy1mb3JtLWZpZWxkLWljb24tcHJlZml4IHtcbiAgICAgIC5pbnB1dC1wcmVmaXgtaWNvbiB7XG4gICAgICAgIGNvbG9yOiAkZ3JheS01MDAgIWltcG9ydGFudDtcbiAgICAgICAgZm9udC1zaXplOiAxLjEyNXJlbSAhaW1wb3J0YW50O1xuICAgICAgICBtYXJnaW4tcmlnaHQ6ICRzcGFjaW5nLXNtICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gU2VsZWN0IE9wdGlvbnNcbiAgLm1hdC1tZGMtb3B0aW9uIHtcbiAgICBib3JkZXItcmFkaXVzOiAkcmFkaXVzLXNtICFpbXBvcnRhbnQ7XG4gICAgbWFyZ2luOiAwIDJweCAhaW1wb3J0YW50O1xuICAgIGZvbnQtc2l6ZTogMC44cmVtICFpbXBvcnRhbnQ7XG4gICAgbWluLWhlaWdodDogMzZweCAhaW1wb3J0YW50O1xuICAgIGxpbmUtaGVpZ2h0OiAxLjIgIWltcG9ydGFudDtcbiAgICBwYWRkaW5nOiA2cHggOHB4ICFpbXBvcnRhbnQ7XG4gICAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50O1xuICAgIHRyYW5zaXRpb246IGFsbCAkdHJhbnNpdGlvbi1mYXN0ICFpbXBvcnRhbnQ7XG5cbiAgICAvLyBSZW1vdmUgc3BhY2luZyBmb3Igc2VhcmNoIG9wdGlvblxuICAgICY6Zmlyc3QtY2hpbGQge1xuICAgICAgbWFyZ2luLXRvcDogMCAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZy10b3A6IDRweCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgycHgpICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgJi5tYXQtbWRjLW9wdGlvbi1hY3RpdmUge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgkcHJpbWFyeSwgMC4xMikgIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC5vcHRpb24taWNvbiB7XG4gICAgICBtYXJnaW4tcmlnaHQ6ICRzcGFjaW5nLXNtICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXNpemU6IDAuOXJlbSAhaW1wb3J0YW50O1xuICAgICAgY29sb3I6ICRncmF5LTUwMCAhaW1wb3J0YW50O1xuICAgIH1cblxuICAgIC8vIERhc2hib2FyZCBvcHRpb24gc3R5bGluZ1xuICAgIC5kYXNoYm9hcmQtb3B0aW9uIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBwYWRkaW5nOiA0cHggMDtcblxuICAgICAgLmRhc2hib2FyZC1pbmZvIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAzcHg7XG4gICAgICAgIGZsZXg6IDE7XG5cbiAgICAgICAgLmRhc2hib2FyZC1uYW1lIHtcbiAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS04MDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMztcbiAgICAgICAgfVxuXG4gICAgICAgIC5kYXNoYm9hcmQtZGVzYyB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgICAgICAgIGNvbG9yOiAkZ3JheS01MDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMztcbiAgICAgICAgICBmb250LXdlaWdodDogNDAwO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gU2VsZWN0ZWQgZGFzaGJvYXJkIHN0eWxpbmcgKGNsb3NlZCBzdGF0ZSlcbiAgICAuc2VsZWN0ZWQtZGFzaGJvYXJkIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBwYWRkaW5nOiAwO1xuXG4gICAgICAuc2VsZWN0ZWQtaW5mbyB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGZsZXg6IDE7XG4gICAgICAgIGhlaWdodDogMTAwJTtcblxuICAgICAgICAuc2VsZWN0ZWQtbmFtZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgY29sb3I6ICRncmF5LTgwMDtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gUmVtb3ZlIHRvcCBzcGFjaW5nIGZyb20gbWF0LXNlbGVjdCBwYW5lbFxuICAubWF0LW1kYy1zZWxlY3QtcGFuZWwge1xuICAgIHBhZGRpbmctdG9wOiAwICFpbXBvcnRhbnQ7XG4gICAgbWFyZ2luLXRvcDogMCAhaW1wb3J0YW50O1xuICB9XG5cbiAgLy8gQnV0dG9uIFN0eWxpbmdcbiAgLm1hdC1tZGMtYnV0dG9uLFxuICAubWF0LW1kYy1yYWlzZWQtYnV0dG9uLFxuICAubWF0LW1kYy1zdHJva2VkLWJ1dHRvbiB7XG4gICAgYm9yZGVyLXJhZGl1czogJHJhZGl1cy1zbSAhaW1wb3J0YW50O1xuICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDtcbiAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZSAhaW1wb3J0YW50O1xuICAgIG1pbi13aWR0aDogYXV0byAhaW1wb3J0YW50O1xuICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xuICB9XG5cbiAgLm1hdC1tZGMtcmFpc2VkLWJ1dHRvbiB7XG4gICAgYm94LXNoYWRvdzogJHNoYWRvdy1zbSAhaW1wb3J0YW50O1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG5cbiAgLm1hdC1tZGMtc3Ryb2tlZC1idXR0b24ge1xuICAgIGJvcmRlci13aWR0aDogMXB4ICFpbXBvcnRhbnQ7XG4gIH1cblxuICAvLyBEYXRlcGlja2VyXG4gIC5tYXQtZGF0ZXBpY2tlci1pbnB1dCB7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbSAhaW1wb3J0YW50O1xuICB9XG5cbiAgLm1hdC1kYXRlcGlja2VyLXRvZ2dsZSB7XG4gICAgd2lkdGg6IDIwcHggIWltcG9ydGFudDtcbiAgICBoZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcbiAgICBjb2xvcjogJGdyYXktNTAwICFpbXBvcnRhbnQ7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgIH1cbiAgfVxuXG4gIC5tYXQtZGF0ZXBpY2tlci10b2dnbGUtZGVmYXVsdC1pY29uIHtcbiAgICB3aWR0aDogMTZweCAhaW1wb3J0YW50O1xuICAgIGhlaWdodDogMTZweCAhaW1wb3J0YW50O1xuICB9XG5cbiAgLy8gU2VsZWN0IGFsbCBjb250cm9scyBzdHlsaW5nXG4gIC5zZWxlY3QtYWxsLWNvbnRyb2xzIHtcbiAgICBwYWRkaW5nOiA0cHggOHB4IDZweCA4cHggIWltcG9ydGFudDtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMCAhaW1wb3J0YW50O1xuICAgIGJhY2tncm91bmQ6ICNmOGY5ZmEgIWltcG9ydGFudDtcbiAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcbiAgICBib3JkZXItcmFkaXVzOiAwICFpbXBvcnRhbnQ7XG5cbiAgICAuc2VsZWN0LXRvZ2dsZS1idG4ge1xuICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcbiAgICAgIGhlaWdodDogMjZweCAhaW1wb3J0YW50O1xuICAgICAgZm9udC1zaXplOiAwLjc1cmVtICFpbXBvcnRhbnQ7XG4gICAgICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXItcmFkaXVzOiAzcHggIWltcG9ydGFudDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDAgIWltcG9ydGFudDtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMSkgIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiAkcHJpbWFyeSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgkcHJpbWFyeSwgMC4yKSAhaW1wb3J0YW50O1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZSAhaW1wb3J0YW50O1xuICAgICAgbWluLWhlaWdodDogMjZweCAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDI2cHggIWltcG9ydGFudDtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoJHByaW1hcnksIDAuMTUpICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgkcHJpbWFyeSwgMC4zKSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatSlideToggleModule", "MatTooltipModule", "FormsModule", "ReactiveFormsModule", "FormControl", "first", "takeUntil", "startWith", "map", "Subject", "Chart", "registerables", "NgxMatSelectSearchModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r11", "ɵɵadvance", "ɵɵtextInterpolate", "tab_r10", "label", "ctx_r0", "getDashboardDescription", "ɵɵtextInterpolate1", "ctx_r1", "selectedLocations", "length", "location_r12", "value", "baseDate_r13", "displayName", "ctx_r6", "getLoadingMessage", "title", "description", "ctx_r7", "getEmptyStateMessage", "item_r15", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_120_div_2_Template", "ctx_r8", "getSummaryItems", "ɵɵelement", "register", "SmartDashboardComponent", "constructor", "authService", "inventoryService", "smartDashboardService", "cdr", "destroy$", "tabs", "selectedTab", "locations", "baseDates", "selectedBaseDate", "startDate", "endDate", "chatMessage", "dashboardData", "charts", "isLoading", "dashboardConfig", "useDefaultCharts", "locationFilterCtrl", "allLocationsSelected", "ngOnInit", "user", "getCurrentUser", "setDefaultDates", "initializeComponent", "setupLocationFilter", "detectChanges", "ngAfterViewInit", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "setDefaultTabs", "setDefaultBaseDates", "loadDashboardConfig", "loadLocations", "dashboardData$", "pipe", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON>", "loading$", "loading", "getDashboardConfig", "response", "status", "error", "getDefaultDashboardTabs", "active", "getDefaultBaseDateOptions", "today", "Date", "year", "getFullYear", "month", "getMonth", "firstDayOfMonth", "formatDateForInput", "date", "String", "padStart", "day", "getDate", "onTabChange", "index", "for<PERSON>ach", "tab", "i", "clearDashboardData", "onDefaultChartsToggle", "setMode", "useDefault", "areAllFiltersValid", "setTimeout", "generateDashboard", "inputElement", "document", "querySelector", "focus", "sendMessage", "trim", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "tenantId", "getLocations", "res", "result", "branches", "branch", "restaurantIdOld", "restaurantId", "id", "branchName", "name", "checked", "location", "resetFilters", "updateLocationStates", "setValue", "filters", "baseDate", "validateFilters", "filteredLocations", "valueChanges", "filterLocations", "filterValue", "toLowerCase", "filter", "includes", "toggleAllLocations", "isAllSelected", "onLocationSelectionChange", "<PERSON><PERSON><PERSON><PERSON>", "onFilterChange", "currentTab", "dashboardType", "formatDateValue", "dateValue", "request", "user_query", "dashboard_type", "tenant_id", "use_default_charts", "chart", "destroy", "chartsContainer", "nativeElement", "innerHTML", "chartConfig", "createChart", "summary_items", "canvas", "createElement", "width", "height", "chartContainer", "className", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "chartOptions", "getChartConfig", "type", "mergedOptions", "options", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "InventoryService", "i3", "SmartDashboardService", "ChangeDetectorRef", "selectors", "viewQuery", "SmartDashboardComponent_Query", "rf", "ɵɵlistener", "SmartDashboardComponent_Template_mat_select_valueChange_4_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_4_listener", "SmartDashboardComponent_mat_option_10_Template", "SmartDashboardComponent_span_25_Template", "SmartDashboardComponent_Template_mat_select_ngModelChange_27_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_27_listener", "SmartDashboardComponent_Template_button_click_31_listener", "SmartDashboardComponent_mat_option_33_Template", "SmartDashboardComponent_Template_mat_select_valueChange_42_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_42_listener", "SmartDashboardComponent_mat_option_43_Template", "SmartDashboardComponent_Template_input_ngModelChange_51_listener", "SmartDashboardComponent_Template_input_dateChange_51_listener", "SmartDashboardComponent_Template_input_ngModelChange_62_listener", "SmartDashboardComponent_Template_input_dateChange_62_listener", "SmartDashboardComponent_Template_div_click_75_listener", "SmartDashboardComponent_Template_div_click_84_listener", "SmartDashboardComponent_Template_button_click_95_listener", "SmartDashboardComponent_Template_input_ngModelChange_112_listener", "SmartDashboardComponent_Template_input_keydown_112_listener", "SmartDashboardComponent_Template_button_click_113_listener", "SmartDashboardComponent_div_118_Template", "SmartDashboardComponent_div_119_Template", "SmartDashboardComponent_div_120_Template", "SmartDashboardComponent_div_121_Template", "ɵɵpipeBind1", "_r4", "_r5", "ɵɵclassProp", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "AsyncPipe", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "MatSuffix", "i8", "MatSelect", "MatSelectTrigger", "i9", "MatOption", "i10", "MatInput", "i11", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i12", "MatTooltip", "i13", "DefaultValueAccessor", "NgControlStatus", "NgModel", "FormControlDirective", "i14", "MatSelectSearchComponent", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, OnDestroy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { InventoryService } from '../../services/inventory.service';\nimport { SmartDashboardService, DashboardTab, BaseDate, DashboardFilters, DashboardData, SummaryItem } from '../../services/smart-dashboard.service';\nimport { first, takeUntil, startWith, map } from 'rxjs/operators';\nimport { Subject, Observable } from 'rxjs';\nimport { Chart, ChartType, registerables } from 'chart.js';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\n\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatSlideToggleModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NgxMatSelectSearchModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('chartsContainer', { static: false }) chartsContainer!: ElementRef;\n\n  private destroy$ = new Subject<void>();\n\n  tabs: DashboardTab[] = [];\n  selectedTab = 0;\n  user: any;\n  locations: any[] = [];\n  baseDates: BaseDate[] = [];\n  selectedLocations: string[] = [];\n  selectedBaseDate = '';\n  startDate: string | Date = '';\n  endDate: string | Date = '';\n  chatMessage = '';\n  dashboardData: DashboardData | null = null;\n  charts: Chart[] = [];\n  isLoading = false;\n  dashboardConfig: any = null;\n  useDefaultCharts = false;\n\n  locationFilterCtrl = new FormControl();\n  filteredLocations: Observable<any[]>;\n  allLocationsSelected = true;\n\n  constructor(\n    private authService: AuthService,\n    private inventoryService: InventoryService,\n    private smartDashboardService: SmartDashboardService,\n    private cdr: ChangeDetectorRef\n  ) { }\n\n  ngOnInit(): void {\n    this.user = this.authService.getCurrentUser();\n    this.setDefaultDates();\n    this.initializeComponent();\n    this.setupLocationFilter();\n    this.cdr.detectChanges();\n  }\n\n  ngAfterViewInit(): void {}\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.clearCharts();\n  }\n\n  private initializeComponent(): void {\n    this.setDefaultTabs();\n    this.setDefaultBaseDates();\n    this.loadDashboardConfig();\n    this.loadLocations();\n\n    this.smartDashboardService.dashboardData$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.dashboardData = data;\n        if (data) {\n          this.renderCharts();\n        }\n        this.cdr.detectChanges();\n      });\n\n    this.smartDashboardService.loading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n        this.cdr.detectChanges();\n      });\n  }\n\n\n\n  private loadDashboardConfig(): void {\n    this.smartDashboardService.getDashboardConfig()\n      .pipe(first())\n      .subscribe({\n        next: (response: any) => {\n          if (response.status === 'success') {\n            this.dashboardConfig = response.data;\n          }\n        },\n        error: () => {}\n      });\n  }\n\n  private setDefaultTabs(): void {\n    this.tabs = this.smartDashboardService.getDefaultDashboardTabs();\n    if (this.tabs.length > 0) {\n      this.tabs[0].active = true;\n    }\n  }\n\n  private setDefaultBaseDates(): void {\n    this.baseDates = this.smartDashboardService.getDefaultBaseDateOptions();\n    if (this.baseDates.length > 0) {\n      this.selectedBaseDate = this.baseDates[0].value;\n    }\n  }\n\n  private setDefaultDates(): void {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const firstDayOfMonth = new Date(year, month, 1);\n    this.startDate = this.formatDateForInput(firstDayOfMonth);\n    this.endDate = this.formatDateForInput(today);\n  }\n\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n  }\n\n  onDefaultChartsToggle(): void {\n    // Clear existing data when toggling\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n\n    // Clear chat message when switching to default charts\n    if (this.useDefaultCharts) {\n      this.chatMessage = '';\n      // Note: Dashboard generation is handled by setMode() method\n    }\n  }\n\n  setMode(useDefault: boolean): void {\n    if (this.useDefaultCharts !== useDefault) {\n      this.useDefaultCharts = useDefault;\n      this.onDefaultChartsToggle();\n\n      if (useDefault) {\n        // Auto-generate dashboard when switching to default charts mode\n        if (this.areAllFiltersValid()) {\n          setTimeout(() => {\n            this.generateDashboard();\n          }, 100);\n        }\n      } else {\n        // Auto-focus input when switching to custom query mode\n        setTimeout(() => {\n          const inputElement = document.querySelector('.ai-input-field input') as HTMLInputElement;\n          if (inputElement && this.areAllFiltersValid()) {\n            inputElement.focus();\n          }\n        }, 100);\n      }\n    }\n  }\n\n  sendMessage(): void {\n    if (this.useDefaultCharts) {\n      // For default charts, just need valid filters\n      if (this.areAllFiltersValid()) {\n        this.generateDashboard();\n      }\n    } else {\n      // For custom queries, need both message and valid filters\n      if (this.chatMessage.trim() && this.areAllFiltersValid()) {\n        this.generateDashboard();\n        this.chatMessage = '';\n      }\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n\n\n  loadLocations(): void {\n    if (this.user && this.user.tenantId) {\n      this.inventoryService.getLocations(this.user.tenantId)\n        .pipe(first())\n        .subscribe({\n          next: (res: any) => {\n            if (res && res.result === 'success' && res.branches) {\n              this.locations = res.branches.map((branch: any) => ({\n                value: branch.restaurantIdOld || branch.restaurantId || branch.id,\n                label: branch.branchName || branch.name,\n                checked: true\n              }));\n              this.selectedLocations = this.locations.map(location => location.value);\n              this.setupLocationFilter();\n            } else {\n              this.locations = [];\n              this.selectedLocations = [];\n            }\n            this.cdr.detectChanges();\n          },\n          error: () => {\n            this.locations = [];\n          }\n        });\n    }\n  }\n\n  resetFilters(): void {\n    this.selectedLocations = this.locations.map(location => location.value);\n    this.selectedBaseDate = this.baseDates.length > 0 ? this.baseDates[0].value : '';\n    this.setDefaultDates();\n    this.updateLocationStates();\n    this.locationFilterCtrl.setValue('');\n    this.smartDashboardService.clearDashboardData();\n    this.clearCharts();\n    this.cdr.detectChanges();\n  }\n\n\n\n  getDashboardDescription(index: number): string {\n    if (this.tabs && this.tabs[index]) {\n      return this.tabs[index].description;\n    }\n    return 'Business analytics dashboard';\n  }\n\n  areAllFiltersValid(): boolean {\n    const filters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: this.startDate,\n      endDate: this.endDate\n    };\n    return this.smartDashboardService.validateFilters(filters);\n  }\n\n  setupLocationFilter(): void {\n    this.filteredLocations = this.locationFilterCtrl.valueChanges.pipe(\n      startWith(''),\n      map(value => this.filterLocations(value || ''))\n    );\n  }\n\n  private filterLocations(value: string): any[] {\n    if (!this.locations || this.locations.length === 0) {\n      return [];\n    }\n    const filterValue = value.toLowerCase();\n    return this.locations.filter(location =>\n      location.label.toLowerCase().includes(filterValue)\n    );\n  }\n\n  toggleAllLocations(): void {\n    if (this.isAllSelected()) {\n      this.selectedLocations = [];\n    } else {\n      this.selectedLocations = [...this.locations.map(location => location.value)];\n    }\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n  }\n\n  onLocationSelectionChange(selectedValues: any[]): void {\n    this.selectedLocations = selectedValues;\n    this.updateLocationStates();\n    this.cdr.detectChanges();\n    this.onFilterChange();\n  }\n\n  private updateLocationStates(): void {\n    this.locations.forEach(location => {\n      location.checked = this.selectedLocations.includes(location.value);\n    });\n    this.allLocationsSelected = this.selectedLocations.length === this.locations.length;\n  }\n\n  isAllSelected(): boolean {\n    return this.selectedLocations.length === this.locations.length;\n  }\n\n  onFilterChange(): void {\n    // Auto-generate dashboard if in default charts mode and filters are valid\n    if (this.useDefaultCharts && this.areAllFiltersValid()) {\n      setTimeout(() => {\n        this.generateDashboard();\n      }, 300); // Small delay to allow for multiple filter changes\n    }\n  }\n\n  getLoadingMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getLoadingMessage(dashboardType);\n  }\n\n  getEmptyStateMessage(): { title: string; description: string } {\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n    return this.smartDashboardService.getEmptyStateMessage(dashboardType);\n  }\n\n  generateDashboard(): void {\n    if (!this.areAllFiltersValid()) {\n      return;\n    }\n\n    // Helper function to convert Date to string format\n    const formatDateValue = (dateValue: any): string => {\n      if (dateValue instanceof Date) {\n        return this.formatDateForInput(dateValue);\n      }\n      return dateValue || '';\n    };\n\n    const filters: DashboardFilters = {\n      locations: this.selectedLocations,\n      baseDate: this.selectedBaseDate,\n      startDate: formatDateValue(this.startDate),\n      endDate: formatDateValue(this.endDate)\n    };\n\n    this.clearCharts();\n\n    const currentTab = this.tabs[this.selectedTab];\n    const dashboardType = currentTab ? currentTab.value : 'purchase';\n\n    const request = {\n      filters: filters,\n      user_query: this.chatMessage,\n      dashboard_type: dashboardType,\n      tenant_id: this.user?.tenantId || '',\n      use_default_charts: this.useDefaultCharts\n    };\n\n    this.smartDashboardService.generateDashboard(request)\n      .pipe(first())\n      .subscribe({\n        next: () => {},\n        error: () => {}\n      });\n  }\n\n  clearCharts(): void {\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.destroy();\n      }\n    });\n    this.charts = [];\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.innerHTML = '';\n    }\n  }\n\n  renderCharts(): void {\n    if (!this.dashboardData || !this.dashboardData.charts) {\n      return;\n    }\n\n    this.clearCharts();\n\n    setTimeout(() => {\n      this.dashboardData.charts.forEach((chartConfig: any) => {\n        this.createChart(chartConfig);\n      });\n    }, 100);\n  }\n\n  getSummaryItems(): SummaryItem[] {\n    if (!this.dashboardData?.summary_items) {\n      return [];\n    }\n\n    return this.dashboardData.summary_items;\n  }\n\n  createChart(chartConfig: any): void {\n    const canvas = document.createElement('canvas');\n    canvas.id = `chart-${chartConfig.id}`;\n    canvas.width = 400;\n    canvas.height = 300;\n\n    const chartContainer = document.createElement('div');\n    chartContainer.className = 'chart-container';\n    chartContainer.innerHTML = `\n      <div class=\"chart-header\">\n        <h3>${chartConfig.title}</h3>\n      </div>\n    `;\n    chartContainer.appendChild(canvas);\n\n    if (this.chartsContainer) {\n      this.chartsContainer.nativeElement.appendChild(chartContainer);\n    }\n\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const chartOptions = this.smartDashboardService.getChartConfig(chartConfig.type);\n\n      const mergedOptions = {\n        ...chartOptions,\n        ...(chartConfig.options || {})\n      };\n\n      const chart = new Chart(ctx, {\n        type: chartConfig.type as ChartType,\n        data: chartConfig.data,\n        options: mergedOptions\n      });\n\n      this.charts.push(chart);\n    }\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Left Sidebar: Reports + Filters -->\n  <div class=\"left-sidebar\">\n    <!-- Dashboard Type Selection -->\n    <div class=\"sidebar-section dashboard-selector-section\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-selector\">\n        <mat-select [(value)]=\"selectedTab\" (selectionChange)=\"onTabChange($event.value)\" placeholder=\"Choose Dashboard Type\">\n          <mat-select-trigger>\n            <div class=\"selected-dashboard\">\n              <div class=\"selected-info\">\n                <span class=\"selected-name\">{{ tabs[selectedTab]?.label }}</span>\n              </div>\n            </div>\n          </mat-select-trigger>\n          <mat-option *ngFor=\"let tab of tabs; let i = index\" [value]=\"i\">\n            <div class=\"dashboard-option\">\n              <div class=\"dashboard-info\">\n                <span class=\"dashboard-name\">{{ tab.label }}</span>\n                <span class=\"dashboard-desc\">{{ getDashboardDescription(i) }}</span>\n              </div>\n            </div>\n          </mat-option>\n        </mat-select>\n\n      </mat-form-field>\n    </div>\n\n    <!-- Smart Filters Section -->\n    <div class=\"sidebar-section filters-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">tune</mat-icon>\n          <h3 class=\"section-title\">Smart Filters</h3>\n        </div>\n      </div>\n\n      <div class=\"filters-content\">\n        <!-- Location Multi-Select -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">location_on</mat-icon>\n            <span class=\"label-text\">Restaurants *</span>\n            <span class=\"selection-count\" *ngIf=\"selectedLocations.length > 0\">\n              ({{ selectedLocations.length }} selected)\n            </span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select\n              [(ngModel)]=\"selectedLocations\"\n              multiple\n              placeholder=\"Select restaurants\"\n              (selectionChange)=\"onLocationSelectionChange($event.value)\">\n\n              <!-- Search -->\n              <mat-option>\n                <ngx-mat-select-search\n                  placeholderLabel=\"Search restaurants...\"\n                  noEntriesFoundLabel=\"No restaurants found\"\n                  [formControl]=\"locationFilterCtrl\">\n                </ngx-mat-select-search>\n              </mat-option>\n\n              <!-- Select All Control -->\n              <div class=\"select-all-controls\">\n                <button\n                  mat-button\n                  class=\"select-toggle-btn\"\n                  (click)=\"toggleAllLocations()\">\n                  {{ isAllSelected() ? 'Deselect All' : 'Select All' }}\n                </button>\n              </div>\n\n              <!-- Location Options -->\n              <mat-option\n                *ngFor=\"let location of filteredLocations | async\"\n                [value]=\"location.value\">\n                {{ location.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Selection -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">event</mat-icon>\n            <span class=\"label-text\">Base Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-select [(value)]=\"selectedBaseDate\" placeholder=\"Select base date\" (selectionChange)=\"onFilterChange()\">\n              <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n                {{ baseDate.displayName }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">Start Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"startPicker\"\n              [(ngModel)]=\"startDate\"\n              placeholder=\"Select start date\"\n              readonly\n              (dateChange)=\"onFilterChange()\"\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date -->\n        <div class=\"filter-group\">\n          <label class=\"filter-label\">\n            <mat-icon class=\"label-icon\">calendar_today</mat-icon>\n            <span class=\"label-text\">End Date *</span>\n          </label>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <input\n              matInput\n              [matDatepicker]=\"endPicker\"\n              [(ngModel)]=\"endDate\"\n              placeholder=\"Select end date\"\n              readonly\n              (dateChange)=\"onFilterChange()\"\n            >\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Mode Section -->\n    <div class=\"sidebar-section mode-section\">\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <mat-icon class=\"section-icon\">auto_awesome</mat-icon>\n          <h3 class=\"section-title\">Dashboard Mode</h3>\n        </div>\n      </div>\n\n      <div class=\"mode-selection-container\">\n        <div class=\"mode-options\">\n          <div class=\"mode-option\" [class.active]=\"!useDefaultCharts\" (click)=\"setMode(false)\">\n            <div class=\"mode-radio\">\n              <mat-icon>{{ !useDefaultCharts ? 'radio_button_checked' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <div class=\"mode-content\">\n              <span class=\"mode-title\">Custom Query</span>\n              <span class=\"mode-desc\">Ask specific questions about your data</span>\n            </div>\n          </div>\n\n          <div class=\"mode-option\" [class.active]=\"useDefaultCharts\" (click)=\"setMode(true)\">\n            <div class=\"mode-radio\">\n              <mat-icon>{{ useDefaultCharts ? 'radio_button_checked' : 'radio_button_unchecked' }}</mat-icon>\n            </div>\n            <div class=\"mode-content\">\n              <span class=\"mode-title\">Default Charts</span>\n              <span class=\"mode-desc\">Standard business dashboard view</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Reset Button at Bottom -->\n    <div class=\"sidebar-section reset-section\">\n      <div class=\"reset-actions\">\n        <button mat-stroked-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n          Reset\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-content\">\n    <!-- Gradient Highlight Bar -->\n    <div class=\"gradient-highlight-bar\">\n      <div class=\"highlight-content\">\n        <div class=\"highlight-left\">\n          <mat-icon class=\"highlight-icon\">auto_awesome</mat-icon>\n          <span class=\"highlight-title\">Digi AI Assistant</span>\n          <span class=\"highlight-status\" [class.ready]=\"areAllFiltersValid()\">\n            {{ areAllFiltersValid() ? 'Ready to analyze' : 'Please fill all required filters' }}\n          </span>\n        </div>\n        <div class=\"highlight-right\">\n          <!-- AI Input Container -->\n          <div class=\"ai-input-container\" [class.disabled-mode]=\"useDefaultCharts\">\n            <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n              <input\n                matInput\n                type=\"text\"\n                [placeholder]=\"useDefaultCharts ? 'Default charts mode - input disabled' : 'Ask me about your business data...'\"\n                [(ngModel)]=\"chatMessage\"\n                (keydown)=\"onKeyPress($event)\"\n                [disabled]=\"!areAllFiltersValid() || useDefaultCharts\"\n              >\n            </mat-form-field>\n            <button\n              mat-icon-button\n              color=\"primary\"\n              class=\"send-btn\"\n              (click)=\"sendMessage()\"\n              [disabled]=\"useDefaultCharts ? !areAllFiltersValid() : (!chatMessage.trim() || !areAllFiltersValid())\"\n              [matTooltip]=\"useDefaultCharts ? 'Generate Default Dashboard' : 'Send Custom Query'\"\n            >\n              <mat-icon>{{ useDefaultCharts ? 'auto_awesome' : 'send' }}</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dashboard Charts Area -->\n    <div class=\"dashboard-section\">\n      <div class=\"dashboard-content\">\n        <!-- Loading State -->\n        <div class=\"loading-state\" *ngIf=\"isLoading\">\n          <div class=\"loading-content\">\n            <div class=\"loading-spinner\">\n              <mat-icon class=\"spin\">refresh</mat-icon>\n            </div>\n            <h4 class=\"loading-title\">{{ getLoadingMessage().title }}</h4>\n            <p class=\"loading-description\">\n              {{ getLoadingMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Empty State -->\n        <div class=\"empty-state\" *ngIf=\"!isLoading && !dashboardData\">\n          <div class=\"empty-state-content\">\n            <div class=\"empty-state-icon\">\n              <mat-icon>analytics</mat-icon>\n            </div>\n            <h4 class=\"empty-state-title\">{{ getEmptyStateMessage().title }}</h4>\n            <p class=\"empty-state-description\">\n              {{ getEmptyStateMessage().description }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Dashboard Summary -->\n        <div class=\"dashboard-summary\" *ngIf=\"!isLoading && dashboardData?.summary_items\">\n          <div class=\"summary-cards\">\n            <div class=\"summary-card\" *ngFor=\"let item of getSummaryItems()\">\n              <div class=\"summary-icon\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n              </div>\n              <div class=\"summary-content\">\n                <div class=\"summary-value\">{{ item.value }}</div>\n                <div class=\"summary-label\">{{ item.label }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Generated Charts -->\n        <div class=\"charts-container\" *ngIf=\"!isLoading && dashboardData\" #chartsContainer>\n          <!-- Charts will be dynamically generated here -->\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAI9E,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjE,SAASC,OAAO,QAAoB,MAAM;AAC1C,SAASC,KAAK,EAAaC,aAAa,QAAQ,UAAU;AAC1D,SAASC,wBAAwB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;;ICLtDC,EAAA,CAAAC,cAAA,qBAAgE;IAG7BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAJtBH,EAAA,CAAAI,UAAA,UAAAC,KAAA,CAAW;IAG5BL,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAe;IACfT,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAC,uBAAA,CAAAN,KAAA,EAAgC;;;;;IAwBjEL,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,OAAAC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,gBACF;;;;;IA6BEf,EAAA,CAAAC,cAAA,qBAE2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFXH,EAAA,CAAAI,UAAA,UAAAY,YAAA,CAAAC,KAAA,CAAwB;IACxBjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAI,YAAA,CAAAP,KAAA,MACF;;;;;IAaAT,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAc,YAAA,CAAAD,KAAA,CAAwB;IACrEjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAM,YAAA,CAAAC,WAAA,MACF;;;;;IAwINnB,EAAA,CAAAC,cAAA,cAA6C;IAGhBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAA+B;IAC7BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHsBH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,iBAAA,CAAAa,MAAA,CAAAC,iBAAA,GAAAC,KAAA,CAA+B;IAEvDtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,MAAA,CAAAC,iBAAA,GAAAE,WAAA,MACF;;;;;IAKJvB,EAAA,CAAAC,cAAA,cAA8D;IAG9CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAH0BH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,iBAAA,CAAAiB,MAAA,CAAAC,oBAAA,GAAAH,KAAA,CAAkC;IAE9DtB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAY,MAAA,CAAAC,oBAAA,GAAAF,WAAA,MACF;;;;;IAOAvB,EAAA,CAAAC,cAAA,cAAiE;IAEnDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEtCH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJvCH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAC,IAAA,CAAe;IAGE3B,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAT,KAAA,CAAgB;IAChBjB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAmB,QAAA,CAAAjB,KAAA,CAAgB;;;;;IARnDT,EAAA,CAAAC,cAAA,cAAkF;IAE9ED,EAAA,CAAA4B,UAAA,IAAAC,8CAAA,kBAQM;IACR7B,EAAA,CAAAG,YAAA,EAAM;;;;IATuCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAC,eAAA,GAAoB;;;;;IAanE/B,EAAA,CAAAgC,SAAA,kBAEM;;;AD1PdnC,KAAK,CAACoC,QAAQ,CAAC,GAAGnC,aAAa,CAAC;AAEhC,MAsBaoC,uBAAuB;EAyBlCC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,qBAA4C,EAC5CC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,GAAG,GAAHA,GAAG;IA1BL,KAAAC,QAAQ,GAAG,IAAI5C,OAAO,EAAQ;IAEtC,KAAA6C,IAAI,GAAmB,EAAE;IACzB,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA9B,iBAAiB,GAAa,EAAE;IAChC,KAAA+B,gBAAgB,GAAG,EAAE;IACrB,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,OAAO,GAAkB,EAAE;IAC3B,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IAExB,KAAAC,kBAAkB,GAAG,IAAI/D,WAAW,EAAE;IAEtC,KAAAgE,oBAAoB,GAAG,IAAI;EAOvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IAC7C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACtB,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA,GAAU;EAEzBC,WAAWA,CAAA;IACT,IAAI,CAACxB,QAAQ,CAACyB,IAAI,EAAE;IACpB,IAAI,CAACzB,QAAQ,CAAC0B,QAAQ,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,cAAc,EAAE;IACrB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAACjC,qBAAqB,CAACkC,cAAc,CACtCC,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAC1B,aAAa,GAAG0B,IAAI;MACzB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,YAAY,EAAE;;MAErB,IAAI,CAACrC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACxB,qBAAqB,CAACuC,QAAQ,CAChCJ,IAAI,CAAChF,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAACI,OAAO,IAAG;MACnB,IAAI,CAAC3B,SAAS,GAAG2B,OAAO;MACxB,IAAI,CAACvC,GAAG,CAACuB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACN;EAIQQ,mBAAmBA,CAAA;IACzB,IAAI,CAAChC,qBAAqB,CAACyC,kBAAkB,EAAE,CAC5CN,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAGe,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC7B,eAAe,GAAG4B,QAAQ,CAACL,IAAI;;MAExC,CAAC;MACDO,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEQd,cAAcA,CAAA;IACpB,IAAI,CAAC3B,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAAC6C,uBAAuB,EAAE;IAChE,IAAI,IAAI,CAAC1C,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC2C,MAAM,GAAG,IAAI;;EAE9B;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,CAACzB,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAAC+C,yBAAyB,EAAE;IACvE,IAAI,IAAI,CAACzC,SAAS,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK;;EAEnD;EAEQ0C,eAAeA,CAAA;IACrB,MAAM2B,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,EAAE;IAChC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,QAAQ,EAAE;IAC9B,MAAMC,eAAe,GAAG,IAAIL,IAAI,CAACC,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC5C,SAAS,GAAG,IAAI,CAAC+C,kBAAkB,CAACD,eAAe,CAAC;IACzD,IAAI,CAAC7C,OAAO,GAAG,IAAI,CAAC8C,kBAAkB,CAACP,KAAK,CAAC;EAC/C;EAEQO,kBAAkBA,CAACC,IAAU;IACnC,MAAMN,IAAI,GAAGM,IAAI,CAACL,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGK,MAAM,CAACD,IAAI,CAACH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGR,IAAI,IAAIE,KAAK,IAAIO,GAAG,EAAE;EAClC;EAEAE,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC1D,WAAW,GAAG0D,KAAK;IACxB,IAAI,CAAC3D,IAAI,CAAC4D,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAAClB,MAAM,GAAGmB,CAAC,KAAKH,KAAK;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC9D,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;EACpB;EAEAsC,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACnE,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAElB;IACA,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACzB,IAAI,CAACL,WAAW,GAAG,EAAE;MACrB;;EAEJ;;EAEA0D,OAAOA,CAACC,UAAmB;IACzB,IAAI,IAAI,CAACtD,gBAAgB,KAAKsD,UAAU,EAAE;MACxC,IAAI,CAACtD,gBAAgB,GAAGsD,UAAU;MAClC,IAAI,CAACF,qBAAqB,EAAE;MAE5B,IAAIE,UAAU,EAAE;QACd;QACA,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;UAC7BC,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,iBAAiB,EAAE;UAC1B,CAAC,EAAE,GAAG,CAAC;;OAEV,MAAM;QACL;QACAD,UAAU,CAAC,MAAK;UACd,MAAME,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAqB;UACxF,IAAIF,YAAY,IAAI,IAAI,CAACH,kBAAkB,EAAE,EAAE;YAC7CG,YAAY,CAACG,KAAK,EAAE;;QAExB,CAAC,EAAE,GAAG,CAAC;;;EAGb;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC9D,gBAAgB,EAAE;MACzB;MACA,IAAI,IAAI,CAACuD,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACE,iBAAiB,EAAE;;KAE3B,MAAM;MACL;MACA,IAAI,IAAI,CAAC9D,WAAW,CAACoE,IAAI,EAAE,IAAI,IAAI,CAACR,kBAAkB,EAAE,EAAE;QACxD,IAAI,CAACE,iBAAiB,EAAE;QACxB,IAAI,CAAC9D,WAAW,GAAG,EAAE;;;EAG3B;EAEAqE,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACN,WAAW,EAAE;;EAEtB;EAIA5C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACd,IAAI,IAAI,IAAI,CAACA,IAAI,CAACiE,QAAQ,EAAE;MACnC,IAAI,CAACrF,gBAAgB,CAACsF,YAAY,CAAC,IAAI,CAAClE,IAAI,CAACiE,QAAQ,CAAC,CACnDjD,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;QACTT,IAAI,EAAG2D,GAAQ,IAAI;UACjB,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,SAAS,IAAID,GAAG,CAACE,QAAQ,EAAE;YACnD,IAAI,CAACnF,SAAS,GAAGiF,GAAG,CAACE,QAAQ,CAACnI,GAAG,CAAEoI,MAAW,KAAM;cAClD9G,KAAK,EAAE8G,MAAM,CAACC,eAAe,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACG,EAAE;cACjEzH,KAAK,EAAEsH,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,IAAI;cACvCC,OAAO,EAAE;aACV,CAAC,CAAC;YACH,IAAI,CAACvH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAAC2I,QAAQ,IAAIA,QAAQ,CAACrH,KAAK,CAAC;YACvE,IAAI,CAAC4C,mBAAmB,EAAE;WAC3B,MAAM;YACL,IAAI,CAAClB,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC7B,iBAAiB,GAAG,EAAE;;UAE7B,IAAI,CAACyB,GAAG,CAACuB,aAAa,EAAE;QAC1B,CAAC;QACDoB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvC,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;;EAER;EAEA4F,YAAYA,CAAA;IACV,IAAI,CAACzH,iBAAiB,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAAC2I,QAAQ,IAAIA,QAAQ,CAACrH,KAAK,CAAC;IACvE,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC7B,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC3B,KAAK,GAAG,EAAE;IAChF,IAAI,CAAC0C,eAAe,EAAE;IACtB,IAAI,CAAC6E,oBAAoB,EAAE;IAC3B,IAAI,CAAClF,kBAAkB,CAACmF,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAACnG,qBAAqB,CAACkE,kBAAkB,EAAE;IAC/C,IAAI,CAACrC,WAAW,EAAE;IAClB,IAAI,CAAC5B,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAIAnD,uBAAuBA,CAACyF,KAAa;IACnC,IAAI,IAAI,CAAC3D,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC2D,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI,CAAC3D,IAAI,CAAC2D,KAAK,CAAC,CAAC7E,WAAW;;IAErC,OAAO,8BAA8B;EACvC;EAEAqF,kBAAkBA,CAAA;IAChB,MAAM8B,OAAO,GAAG;MACd/F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjC6H,QAAQ,EAAE,IAAI,CAAC9F,gBAAgB;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA;KACf;IACD,OAAO,IAAI,CAACT,qBAAqB,CAACsG,eAAe,CAACF,OAAO,CAAC;EAC5D;EAEA7E,mBAAmBA,CAAA;IACjB,IAAI,CAACgF,iBAAiB,GAAG,IAAI,CAACvF,kBAAkB,CAACwF,YAAY,CAACrE,IAAI,CAChE/E,SAAS,CAAC,EAAE,CAAC,EACbC,GAAG,CAACsB,KAAK,IAAI,IAAI,CAAC8H,eAAe,CAAC9H,KAAK,IAAI,EAAE,CAAC,CAAC,CAChD;EACH;EAEQ8H,eAAeA,CAAC9H,KAAa;IACnC,IAAI,CAAC,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClD,OAAO,EAAE;;IAEX,MAAMiI,WAAW,GAAG/H,KAAK,CAACgI,WAAW,EAAE;IACvC,OAAO,IAAI,CAACtG,SAAS,CAACuG,MAAM,CAACZ,QAAQ,IACnCA,QAAQ,CAAC7H,KAAK,CAACwI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CACnD;EACH;EAEAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,aAAa,EAAE,EAAE;MACxB,IAAI,CAACvI,iBAAiB,GAAG,EAAE;KAC5B,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC6B,SAAS,CAAChD,GAAG,CAAC2I,QAAQ,IAAIA,QAAQ,CAACrH,KAAK,CAAC,CAAC;;IAE9E,IAAI,CAACuH,oBAAoB,EAAE;IAC3B,IAAI,CAACjG,GAAG,CAACuB,aAAa,EAAE;EAC1B;EAEAwF,yBAAyBA,CAACC,cAAqB;IAC7C,IAAI,CAACzI,iBAAiB,GAAGyI,cAAc;IACvC,IAAI,CAACf,oBAAoB,EAAE;IAC3B,IAAI,CAACjG,GAAG,CAACuB,aAAa,EAAE;IACxB,IAAI,CAAC0F,cAAc,EAAE;EACvB;EAEQhB,oBAAoBA,CAAA;IAC1B,IAAI,CAAC7F,SAAS,CAAC0D,OAAO,CAACiC,QAAQ,IAAG;MAChCA,QAAQ,CAACD,OAAO,GAAG,IAAI,CAACvH,iBAAiB,CAACqI,QAAQ,CAACb,QAAQ,CAACrH,KAAK,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACsC,oBAAoB,GAAG,IAAI,CAACzC,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EACrF;EAEAsI,aAAaA,CAAA;IACX,OAAO,IAAI,CAACvI,iBAAiB,CAACC,MAAM,KAAK,IAAI,CAAC4B,SAAS,CAAC5B,MAAM;EAChE;EAEAyI,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACnG,gBAAgB,IAAI,IAAI,CAACuD,kBAAkB,EAAE,EAAE;MACtDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEb;;EAEAzF,iBAAiBA,CAAA;IACf,MAAMoI,UAAU,GAAG,IAAI,CAAChH,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMgH,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACxI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACjB,iBAAiB,CAACqI,aAAa,CAAC;EACpE;EAEAjI,oBAAoBA,CAAA;IAClB,MAAMgI,UAAU,GAAG,IAAI,CAAChH,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMgH,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACxI,KAAK,GAAG,UAAU;IAChE,OAAO,IAAI,CAACqB,qBAAqB,CAACb,oBAAoB,CAACiI,aAAa,CAAC;EACvE;EAEA5C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACF,kBAAkB,EAAE,EAAE;MAC9B;;IAGF;IACA,MAAM+C,eAAe,GAAIC,SAAc,IAAY;MACjD,IAAIA,SAAS,YAAYrE,IAAI,EAAE;QAC7B,OAAO,IAAI,CAACM,kBAAkB,CAAC+D,SAAS,CAAC;;MAE3C,OAAOA,SAAS,IAAI,EAAE;IACxB,CAAC;IAED,MAAMlB,OAAO,GAAqB;MAChC/F,SAAS,EAAE,IAAI,CAAC7B,iBAAiB;MACjC6H,QAAQ,EAAE,IAAI,CAAC9F,gBAAgB;MAC/BC,SAAS,EAAE6G,eAAe,CAAC,IAAI,CAAC7G,SAAS,CAAC;MAC1CC,OAAO,EAAE4G,eAAe,CAAC,IAAI,CAAC5G,OAAO;KACtC;IAED,IAAI,CAACoB,WAAW,EAAE;IAElB,MAAMsF,UAAU,GAAG,IAAI,CAAChH,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9C,MAAMgH,aAAa,GAAGD,UAAU,GAAGA,UAAU,CAACxI,KAAK,GAAG,UAAU;IAEhE,MAAM4I,OAAO,GAAG;MACdnB,OAAO,EAAEA,OAAO;MAChBoB,UAAU,EAAE,IAAI,CAAC9G,WAAW;MAC5B+G,cAAc,EAAEL,aAAa;MAC7BM,SAAS,EAAE,IAAI,CAACvG,IAAI,EAAEiE,QAAQ,IAAI,EAAE;MACpCuC,kBAAkB,EAAE,IAAI,CAAC5G;KAC1B;IAED,IAAI,CAACf,qBAAqB,CAACwE,iBAAiB,CAAC+C,OAAO,CAAC,CAClDpF,IAAI,CAACjF,KAAK,EAAE,CAAC,CACbkF,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdiB,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACN;EAEAf,WAAWA,CAAA;IACT,IAAI,CAACjB,MAAM,CAACmD,OAAO,CAAC6D,KAAK,IAAG;MAC1B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,EAAE;;IAEnB,CAAC,CAAC;IACF,IAAI,CAACjH,MAAM,GAAG,EAAE;IAEhB,IAAI,IAAI,CAACkH,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;;EAErD;EAEA1F,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC3B,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACC,MAAM,EAAE;MACrD;;IAGF,IAAI,CAACiB,WAAW,EAAE;IAElB0C,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5D,aAAa,CAACC,MAAM,CAACmD,OAAO,CAAEkE,WAAgB,IAAI;QACrD,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAxI,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAEwH,aAAa,EAAE;MACtC,OAAO,EAAE;;IAGX,OAAO,IAAI,CAACxH,aAAa,CAACwH,aAAa;EACzC;EAEAD,WAAWA,CAACD,WAAgB;IAC1B,MAAMG,MAAM,GAAG1D,QAAQ,CAAC2D,aAAa,CAAC,QAAQ,CAAC;IAC/CD,MAAM,CAACxC,EAAE,GAAG,SAASqC,WAAW,CAACrC,EAAE,EAAE;IACrCwC,MAAM,CAACE,KAAK,GAAG,GAAG;IAClBF,MAAM,CAACG,MAAM,GAAG,GAAG;IAEnB,MAAMC,cAAc,GAAG9D,QAAQ,CAAC2D,aAAa,CAAC,KAAK,CAAC;IACpDG,cAAc,CAACC,SAAS,GAAG,iBAAiB;IAC5CD,cAAc,CAACR,SAAS,GAAG;;cAEjBC,WAAW,CAACjJ,KAAK;;KAE1B;IACDwJ,cAAc,CAACE,WAAW,CAACN,MAAM,CAAC;IAElC,IAAI,IAAI,CAACN,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,aAAa,CAACW,WAAW,CAACF,cAAc,CAAC;;IAGhE,MAAMG,GAAG,GAAGP,MAAM,CAACQ,UAAU,CAAC,IAAI,CAAC;IACnC,IAAID,GAAG,EAAE;MACP,MAAME,YAAY,GAAG,IAAI,CAAC7I,qBAAqB,CAAC8I,cAAc,CAACb,WAAW,CAACc,IAAI,CAAC;MAEhF,MAAMC,aAAa,GAAG;QACpB,GAAGH,YAAY;QACf,IAAIZ,WAAW,CAACgB,OAAO,IAAI,EAAE;OAC9B;MAED,MAAMrB,KAAK,GAAG,IAAIrK,KAAK,CAACoL,GAAG,EAAE;QAC3BI,IAAI,EAAEd,WAAW,CAACc,IAAiB;QACnC1G,IAAI,EAAE4F,WAAW,CAAC5F,IAAI;QACtB4G,OAAO,EAAED;OACV,CAAC;MAEF,IAAI,CAACpI,MAAM,CAACsI,IAAI,CAACtB,KAAK,CAAC;;EAE3B;;;uBAraWhI,uBAAuB,EAAAlC,EAAA,CAAAyL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3L,EAAA,CAAAyL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7L,EAAA,CAAAyL,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAA/L,EAAA,CAAAyL,iBAAA,CAAAzL,EAAA,CAAAgM,iBAAA;IAAA;EAAA;;;YAAvB9J,uBAAuB;MAAA+J,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAnB,GAAA;QAAA,IAAAmB,EAAA;;;;;;;;;;;;;;;UC7CpCpM,EAAA,CAAAC,cAAA,aAAuC;UAMnBD,EAAA,CAAAqM,UAAA,yBAAAC,mEAAAC,MAAA;YAAA,OAAAtB,GAAA,CAAAvI,WAAA,GAAA6J,MAAA;UAAA,EAAuB,6BAAAC,uEAAAD,MAAA;YAAA,OAAoBtB,GAAA,CAAA9E,WAAA,CAAAoG,MAAA,CAAAtL,KAAA,CAAyB;UAAA,EAA7C;UACjCjB,EAAA,CAAAC,cAAA,yBAAoB;UAGcD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIvEH,EAAA,CAAA4B,UAAA,KAAA6K,8CAAA,wBAOa;UACfzM,EAAA,CAAAG,YAAA,EAAa;UAMjBH,EAAA,CAAAC,cAAA,cAA6C;UAGRD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIhDH,EAAA,CAAAC,cAAA,eAA6B;UAIMD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7CH,EAAA,CAAA4B,UAAA,KAAA8K,wCAAA,mBAEO;UACT1M,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,0BAA0D;UAEtDD,EAAA,CAAAqM,UAAA,2BAAAM,sEAAAJ,MAAA;YAAA,OAAAtB,GAAA,CAAAnK,iBAAA,GAAAyL,MAAA;UAAA,EAA+B,6BAAAK,wEAAAL,MAAA;YAAA,OAGZtB,GAAA,CAAA3B,yBAAA,CAAAiD,MAAA,CAAAtL,KAAA,CAAuC;UAAA,EAH3B;UAM/BjB,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAgC,SAAA,iCAIwB;UAC1BhC,EAAA,CAAAG,YAAA,EAAa;UAGbH,EAAA,CAAAC,cAAA,eAAiC;UAI7BD,EAAA,CAAAqM,UAAA,mBAAAQ,0DAAA;YAAA,OAAS5B,GAAA,CAAA7B,kBAAA,EAAoB;UAAA,EAAC;UAC9BpJ,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAA4B,UAAA,KAAAkL,8CAAA,wBAIa;;UACf9M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7CH,EAAA,CAAAC,cAAA,0BAA0D;UAC5CD,EAAA,CAAAqM,UAAA,yBAAAU,oEAAAR,MAAA;YAAA,OAAAtB,GAAA,CAAApI,gBAAA,GAAA0J,MAAA;UAAA,EAA4B,6BAAAS,wEAAA;YAAA,OAAmD/B,GAAA,CAAAzB,cAAA,EAAgB;UAAA,EAAnE;UACtCxJ,EAAA,CAAA4B,UAAA,KAAAqL,8CAAA,wBAEa;UACfjN,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAqM,UAAA,2BAAAa,iEAAAX,MAAA;YAAA,OAAAtB,GAAA,CAAAnI,SAAA,GAAAyJ,MAAA;UAAA,EAAuB,wBAAAY,8DAAA;YAAA,OAGTlC,GAAA,CAAAzB,cAAA,EAAgB;UAAA,EAHP;UAHzBxJ,EAAA,CAAAG,YAAA,EAOC;UACDH,EAAA,CAAAgC,SAAA,iCAA6E;UAE/EhC,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEOD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5CH,EAAA,CAAAC,cAAA,0BAA0D;UAItDD,EAAA,CAAAqM,UAAA,2BAAAe,iEAAAb,MAAA;YAAA,OAAAtB,GAAA,CAAAlI,OAAA,GAAAwJ,MAAA;UAAA,EAAqB,wBAAAc,8DAAA;YAAA,OAGPpC,GAAA,CAAAzB,cAAA,EAAgB;UAAA,EAHT;UAHvBxJ,EAAA,CAAAG,YAAA,EAOC;UACDH,EAAA,CAAAgC,SAAA,iCAA2E;UAE7EhC,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAA0C;UAGLD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtDH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjDH,EAAA,CAAAC,cAAA,eAAsC;UAE0BD,EAAA,CAAAqM,UAAA,mBAAAiB,uDAAA;YAAA,OAASrC,GAAA,CAAAvE,OAAA,CAAQ,KAAK,CAAC;UAAA,EAAC;UAClF1G,EAAA,CAAAC,cAAA,eAAwB;UACZD,EAAA,CAAAE,MAAA,IAA2E;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAElGH,EAAA,CAAAC,cAAA,eAA0B;UACCD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,8CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzEH,EAAA,CAAAC,cAAA,eAAmF;UAAxBD,EAAA,CAAAqM,UAAA,mBAAAkB,uDAAA;YAAA,OAAStC,GAAA,CAAAvE,OAAA,CAAQ,IAAI,CAAC;UAAA,EAAC;UAChF1G,EAAA,CAAAC,cAAA,eAAwB;UACZD,EAAA,CAAAE,MAAA,IAA0E;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEjGH,EAAA,CAAAC,cAAA,eAA0B;UACCD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,wCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAQzEH,EAAA,CAAAC,cAAA,eAA2C;UAEMD,EAAA,CAAAqM,UAAA,mBAAAmB,0DAAA;YAAA,OAASvC,GAAA,CAAA1C,YAAA,EAAc;UAAA,EAAC;UACnEvI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAKeD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxDH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtDH,EAAA,CAAAC,cAAA,iBAAoE;UAClED,EAAA,CAAAE,MAAA,KACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,gBAA6B;UAQrBD,EAAA,CAAAqM,UAAA,2BAAAoB,kEAAAlB,MAAA;YAAA,OAAAtB,GAAA,CAAAjI,WAAA,GAAAuJ,MAAA;UAAA,EAAyB,qBAAAmB,4DAAAnB,MAAA;YAAA,OACdtB,GAAA,CAAA5D,UAAA,CAAAkF,MAAA,CAAkB;UAAA,EADJ;UAJ3BvM,EAAA,CAAAG,YAAA,EAOC;UAEHH,EAAA,CAAAC,cAAA,mBAOC;UAHCD,EAAA,CAAAqM,UAAA,mBAAAsB,2DAAA;YAAA,OAAS1C,GAAA,CAAA9D,WAAA,EAAa;UAAA,EAAC;UAIvBnH,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,KAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQ/EH,EAAA,CAAAC,cAAA,gBAA+B;UAG3BD,EAAA,CAAA4B,UAAA,MAAAgM,wCAAA,kBAUM;UAGN5N,EAAA,CAAA4B,UAAA,MAAAiM,wCAAA,kBAUM;UAGN7N,EAAA,CAAA4B,UAAA,MAAAkM,wCAAA,kBAYM;UAGN9N,EAAA,CAAA4B,UAAA,MAAAmM,wCAAA,kBAEM;UACR/N,EAAA,CAAAG,YAAA,EAAM;;;;;UA1QQH,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,UAAA6K,GAAA,CAAAvI,WAAA,CAAuB;UAIC1C,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,iBAAA,CAAA0K,GAAA,CAAAxI,IAAA,CAAAwI,GAAA,CAAAvI,WAAA,mBAAAuI,GAAA,CAAAxI,IAAA,CAAAwI,GAAA,CAAAvI,WAAA,EAAAjC,KAAA,CAA8B;UAIpCT,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAA6K,GAAA,CAAAxI,IAAA,CAAS;UA4BJzC,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAI,UAAA,SAAA6K,GAAA,CAAAnK,iBAAA,CAAAC,MAAA,KAAkC;UAM/Df,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,YAAA6K,GAAA,CAAAnK,iBAAA,CAA+B;UAU3Bd,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAA6K,GAAA,CAAA3H,kBAAA,CAAkC;UAUlCtD,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAAqK,GAAA,CAAA5B,aAAA,wCACF;UAKqBrJ,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgO,WAAA,SAAA/C,GAAA,CAAApC,iBAAA,EAA4B;UAezC7I,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,UAAA6K,GAAA,CAAApI,gBAAA,CAA4B;UACL7C,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAA6K,GAAA,CAAArI,SAAA,CAAY;UAgB7C5C,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,kBAAA6N,GAAA,CAA6B,YAAAhD,GAAA,CAAAnI,SAAA;UAME9C,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAI,UAAA,QAAA6N,GAAA,CAAmB;UAclDjO,EAAA,CAAAM,SAAA,IAA2B;UAA3BN,EAAA,CAAAI,UAAA,kBAAA8N,GAAA,CAA2B,YAAAjD,GAAA,CAAAlI,OAAA;UAMI/C,EAAA,CAAAM,SAAA,GAAiB;UAAjBN,EAAA,CAAAI,UAAA,QAAA8N,GAAA,CAAiB;UAkB3BlO,EAAA,CAAAM,SAAA,IAAkC;UAAlCN,EAAA,CAAAmO,WAAA,YAAAlD,GAAA,CAAA5H,gBAAA,CAAkC;UAE7CrD,EAAA,CAAAM,SAAA,GAA2E;UAA3EN,EAAA,CAAAO,iBAAA,EAAA0K,GAAA,CAAA5H,gBAAA,qDAA2E;UAQhErD,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAmO,WAAA,WAAAlD,GAAA,CAAA5H,gBAAA,CAAiC;UAE5CrD,EAAA,CAAAM,SAAA,GAA0E;UAA1EN,EAAA,CAAAO,iBAAA,CAAA0K,GAAA,CAAA5H,gBAAA,qDAA0E;UA8BzDrD,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAAmO,WAAA,UAAAlD,GAAA,CAAArE,kBAAA,GAAoC;UACjE5G,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAY,kBAAA,MAAAqK,GAAA,CAAArE,kBAAA,kEACF;UAIgC5G,EAAA,CAAAM,SAAA,GAAwC;UAAxCN,EAAA,CAAAmO,WAAA,kBAAAlD,GAAA,CAAA5H,gBAAA,CAAwC;UAKlErD,EAAA,CAAAM,SAAA,GAAgH;UAAhHN,EAAA,CAAAI,UAAA,gBAAA6K,GAAA,CAAA5H,gBAAA,iFAAgH,YAAA4H,GAAA,CAAAjI,WAAA,eAAAiI,GAAA,CAAArE,kBAAA,MAAAqE,GAAA,CAAA5H,gBAAA;UAWlHrD,EAAA,CAAAM,SAAA,GAAsG;UAAtGN,EAAA,CAAAI,UAAA,aAAA6K,GAAA,CAAA5H,gBAAA,IAAA4H,GAAA,CAAArE,kBAAA,MAAAqE,GAAA,CAAAjI,WAAA,CAAAoE,IAAA,OAAA6D,GAAA,CAAArE,kBAAA,GAAsG,eAAAqE,GAAA,CAAA5H,gBAAA;UAG5FrD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAO,iBAAA,CAAA0K,GAAA,CAAA5H,gBAAA,2BAAgD;UAWpCrD,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAA6K,GAAA,CAAA9H,SAAA,CAAe;UAajBnD,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,UAAA6K,GAAA,CAAA9H,SAAA,KAAA8H,GAAA,CAAAhI,aAAA,CAAkC;UAa5BjD,EAAA,CAAAM,SAAA,GAAgD;UAAhDN,EAAA,CAAAI,UAAA,UAAA6K,GAAA,CAAA9H,SAAA,KAAA8H,GAAA,CAAAhI,aAAA,kBAAAgI,GAAA,CAAAhI,aAAA,CAAAwH,aAAA,EAAgD;UAejDzK,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,UAAA6K,GAAA,CAAA9H,SAAA,IAAA8H,GAAA,CAAAhI,aAAA,CAAiC;;;qBDlPpEvE,YAAY,EAAA0P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,SAAA,EACZ5P,aAAa,EACbC,eAAe,EAAA4P,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf7P,aAAa,EAAA8P,EAAA,CAAAC,OAAA,EACb9P,kBAAkB,EAAA+P,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBhQ,eAAe,EAAAiQ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAC,EAAA,CAAAC,SAAA,EACfpQ,cAAc,EAAAqQ,GAAA,CAAAC,QAAA,EACdrQ,mBAAmB,EAAAsQ,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnBxQ,mBAAmB,EACnBC,oBAAoB,EACpBC,gBAAgB,EAAAuQ,GAAA,CAAAC,UAAA,EAChBvQ,WAAW,EAAAwQ,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACX1Q,mBAAmB,EAAAuQ,GAAA,CAAAI,oBAAA,EACnBlQ,wBAAwB,EAAAmQ,GAAA,CAAAC,wBAAA;MAAAC,MAAA;IAAA;EAAA;;SAKflO,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}