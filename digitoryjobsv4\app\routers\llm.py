from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, Body
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
from dotenv import load_dotenv
import json
from langchain_core.messages import SystemMessage
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from app.models.llm import ProcessingResponse, RecipeRequest, StartProcessingRequest, StatusResponse, ChatMessage, ConversationHistoryResponse, RestaurantMenuPredictionData, DatabaseDocumentationRequest
from app.utility.chat_db import save_message, get_conversation_history, clear_conversation_history, initialize_chat_tables
from app.utility.restaurant_db import save_restaurant_data, get_restaurant_data
from app.utility.db_documentation import get_database_documentation
from app.prompt.recipe import system_prompt, user_prompt
from app.database import Servingsizerecipes
from app.utility.agents import ingredient_predictor, ingredient_classifier, menu_clean_up, menu_predictor
from app.utility.llm_utlity import POSflow
import openai
from langchain_openai import ChatOpenAI
from langchain.memory import ConversationBufferMemory
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from jinja2 import Environment, FileSystemLoader
import time
import base64
from sqlalchemy import text, create_engine
import uuid
import io
from pathlib import Path


load_dotenv()
OPENAI_MODEL_NAME = os.getenv("OPENAI_MODEL_NAME")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
BEARER_TOKEN = os.getenv("BEARER_TOKEN")
engine = create_engine(os.getenv("POSTGRES_URL"))

template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'prompt')
jinja_env = Environment(loader=FileSystemLoader(template_dir), variable_start_string='${', variable_end_string='}')

def load_template(template_name):
    template = jinja_env.get_template(template_name)
    return template.render()
restaurant_onboarding_prompt = load_template('restaurant_onboarding_tool.j2')
db_documentation_prompt_template = jinja_env.get_template('db_documentation.j2')

chat_memories = {}
current_responses = {}

from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(_):
    await initialize_chat_tables()
    yield
    pass

router = APIRouter(lifespan=lifespan)
security = HTTPBearer()
processing_data = {}

INVENTORY_BATCH_SIZE = 50
RECIPE_BATCH_SIZE = 50
MAX_WORKERS = 5
BATCH_DELAY = 3
AVERAGE_RATE_PER_MENU= 2
POPULAR_ITEM_THRESHOLD = 15

async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Authenticate requests using bearer token."""
    if not credentials or credentials.credentials != BEARER_TOKEN:
        raise HTTPException(status_code=401, detail="Invalid or missing token")
    return credentials.credentials


class RecipeWorkflowService:
    """
    Service class for handling recipe workflow processing
    """
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.inventory_data = []
        self.package_data = []
        self.missing = []
        self.processed_ingredients = set()
        self.inventory_file = Path("output") / f"{self.tenant_id}_inventory.xlsx"
        self.package_file = Path("output") / f"{self.tenant_id}_package.xlsx"
        self.vendor_file = Path("output") / f"{self.tenant_id}_vendor.xlsx"
        self.pos_flow = POSflow(
            self.tenant_id,
            self.inventory_file,
            self.package_file,
            self.inventory_data,
            self.package_data,
            self.vendor_file
        )
        self.total_menus = 0
        self.processed_menus = 0
        self.processing_id = None
        self.popular_items_count = 0
        self.start_time = None
        self.popular_items_processed = False
        self.popular1 = 0
        self.recipe1 = 0
        self.alcohol_enabled = None
        self.tobacco_enabled = None

    def run_workflow(self, processing_id: str):
        """Run the workflow"""
        self.start_time = datetime.now()
        print(f"Workflow start time: {self.start_time}")
        self.processing_id = processing_id

        # Initialize alcohol and tobacco settings
        self.alcohol_enabled, self.tobacco_enabled = asyncio.run(self._check_alcohol_tobacco_enabled())
        print(f"Initialized settings - Alcohol enabled: {self.alcohol_enabled}, Tobacco enabled: {self.tobacco_enabled}")

        # Load restaurant data to get workareas
        asyncio.run(self.pos_flow._load_restaurant_data())

        # Get count of recipes and popular inventory items
        start = time.perf_counter()
        recipe_count = self.pos_flow._fetch_recipes_from_api(counter=True)
        if not recipe_count:
            recipe_count = 400
        end = time.perf_counter()
        print(f"_fetch_recipes_from_api(counter=True) took {end - start:.2f} seconds")

        start = time.perf_counter()
        self.popular_items_count = self._get_popular_items_count()
        end = time.perf_counter()
        print(f"_get_popular_items_count took {end - start:.2f} seconds")

        self.total_menus = recipe_count + self.popular_items_count

        # Initialize processing data
        processing_data[self.tenant_id]["status"] = "processing"
        processing_data[self.tenant_id]["steps"][0]["completed"] = True
        processing_data[self.tenant_id]["currentStep"] = 1
        processing_data[self.tenant_id]["total_menus"] = self.total_menus
        processing_data[self.tenant_id]["processed_menus"] = 0
        processing_data[self.tenant_id]["last_estimated_time"] = self.total_menus * AVERAGE_RATE_PER_MENU
        processing_data[self.tenant_id]["progress"] = 5

        # Process popular inventory items - if you want to include this
        start = time.perf_counter()
        self._popular_inventory()
        end = time.perf_counter()
        print(f"_popular_inventory took {end - start:.2f} seconds")

        # Process recipes
        start = time.perf_counter()
        recipes = self.pos_flow._fetch_recipes_from_api()
        end = time.perf_counter()
        print(f"_fetch_recipes_from_api() took {end - start:.2f} seconds")

        # Check if recipes list is empty
        if not recipes:
            print("No recipes found from POS API. Predicting menu items based on restaurant data...")

            # Get restaurant data
            restaurant_data = asyncio.run(get_restaurant_data(self.tenant_id))

            if restaurant_data:
                cuisines = restaurant_data.get("commonCuisinesAcrossOutlets", [])

                def extract_field(field_name, default_value, sub_field=None):
                    if field_name not in restaurant_data:
                        return default_value

                    field_data = restaurant_data[field_name]
                    if isinstance(field_data, dict) and sub_field:
                        return field_data.get(sub_field, default_value)
                    elif isinstance(field_data, list) and field_name == "signatureElements":
                        return field_data
                    elif isinstance(field_data, str):
                        return field_data
                    return default_value

                signature_dishes = extract_field("signatureElements", [], "signatureDishes")
                beverage_info = extract_field("beverageInfo", "No", "alcoholService")
                tobacco_info = extract_field("tobaccoInfo", "No", "tobaccoService")

                location = ""
                outlet_details = restaurant_data.get("outletDetails", [])
                if isinstance(outlet_details, list) and outlet_details:
                    first_outlet = outlet_details[0]
                    if isinstance(first_outlet, dict):
                        location = first_outlet.get("outletAddress", "")
                    elif isinstance(first_outlet, str):
                        location = first_outlet

                # Get cuisine-specific menu item counts
                cuisine_menu_counts = restaurant_data.get("cuisineMenuCounts", [])
                cuisine_count_info = ""

                if cuisine_menu_counts:
                    cuisine_count_info = "Cuisine-specific menu item counts:\n"
                    for count_info in cuisine_menu_counts:
                        if isinstance(count_info, dict) and "cuisine" in count_info and "menuItemCount" in count_info:
                            cuisine_count_info += f"- {count_info['cuisine']}: {count_info['menuItemCount']} items\n"

                # Prepare prompt for menu prediction
                prompt = (
                    f"Predict menu items for a restaurant with the following details:\n"
                    f"Cuisines: {', '.join(cuisines)}\n"
                    f"Signature Dishes: {', '.join(signature_dishes)}\n"
                    f"Beverage Service: {beverage_info}\n"
                    f"Tobacco Service: {tobacco_info}\n"
                    f"Location: {location}\n"
                )

                prompt += f"\n{cuisine_count_info}"

                print("Calling menu predictor agent...")
                response = menu_predictor.run(prompt)
                predicted_menu = response.content.model_dump()
                recipes = predicted_menu.get("menu_items", []) if predicted_menu else []

                if recipes:
                    print(f"Predicted {len(recipes)} menu items based on restaurant data")
                else:
                    print("Menu prediction failed, no menu items returned")
                    recipes = []

                if recipes:
                    self.total_menus += len(recipes)
                    processing_data[self.tenant_id]["total_menus"] = self.total_menus
            else:
                print("No restaurant data found for this tenant")
                recipes = []

        if recipes:
            start = time.perf_counter()
            print(f"Processing {len(recipes)} menu items")
            prompt = f"Clean the following menu_item: {', '.join(recipes)}"
            response = menu_clean_up.run(prompt)
            prediction = response.content.model_dump()
            end = time.perf_counter()
            print(f"menu_clean_up.run() took {end - start:.2f} seconds")

            start = time.perf_counter()
            self._process_recipes(prediction.get("clean", []))
            end = time.perf_counter()
            print(f"_process_recipes took {end - start:.2f} seconds")
        else:
            print("No menu items to process after all attempts")

        processing_data[self.tenant_id]["steps"][1]["completed"] = True
        processing_data[self.tenant_id]["currentStep"] = 2

        end_time = datetime.now()
        print(f"Workflow end time: {end_time}")
        print(f"Total time taken: {(end_time - self.start_time).total_seconds():.2f} seconds")

        start = time.perf_counter()
        self.pos_flow._save_results_to_excel()
        end = time.perf_counter()
        print(f"_save_results_to_excel took {end - start:.2f} seconds")

        start = time.perf_counter()
        with open(f'output/{self.tenant_id}_missing.json', 'w') as f:
            json.dump(self.missing, f, indent=4)
        end = time.perf_counter()
        print(f"Saving missing JSON took {end - start:.2f} seconds")

        # Complete processing
        processing_data[self.tenant_id]["status"] = "complete"
        processing_data[self.tenant_id]["progress"] = 100
        processing_data[self.tenant_id]["steps"][2]["completed"] = True
        processing_data[self.tenant_id]["last_estimated_time"] = 0

        print("popular llm interaction ", self.popular1)
        print("recipe llm interaction ", self.recipe1)


    def _update_progress_and_time(self):
        """Update progress percentage and estimated time remaining."""
        if self.processed_menus == 0:
            return
        processing_data[self.tenant_id]["processed_menus"] = self.processed_menus
        progress = min(int((self.processed_menus / self.total_menus) * 100), 99)
        processing_data[self.tenant_id]["progress"] = progress
        current_time = datetime.now()
        elapsed_seconds = (current_time - self.start_time).total_seconds()
        seconds_per_menu = elapsed_seconds / self.processed_menus
        remaining_menus = self.total_menus - self.processed_menus
        estimated_seconds_remaining = remaining_menus * seconds_per_menu
        processing_data[self.tenant_id]["last_estimated_time"] = estimated_seconds_remaining


    def _get_popular_items_count(self) -> int:
        """Get count of popular inventory items."""
        popular_items_query = text(f"""
            SELECT COUNT(*)
            FROM inventory_master
            WHERE array_length(source, 1) > {POPULAR_ITEM_THRESHOLD};
        """)
        with engine.connect() as conn:
            result = conn.execute(popular_items_query).scalar()
            return result or 0

    def _lookup_ingredient(self, ingredient_name: str) -> Dict[str, Any]:
        """
        Find the best matching inventory item for the ingredient and up to 3 related packaging items.
        Includes fallback to simpler terms if specific match not found.
        """
        if ingredient_name in self.processed_ingredients:
            return None

        self.processed_ingredients.add(ingredient_name)
        query = text("SELECT search_ingredient(:ingredient_name)")
        with engine.connect() as conn:
            result = conn.execute(query, {"ingredient_name": ingredient_name}).scalar()

        if result:
            if result.get('packaging_matches') is None:
                result['packaging_matches'] = []
            return result
        else:
            return {
                "inventory_match": None,
                "packaging_matches": []
            }

    async def _check_alcohol_tobacco_enabled(self) -> tuple:
        """Check if alcohol and tobacco are enabled in restaurant data."""
        if self.alcohol_enabled is not None and self.tobacco_enabled is not None:
            return self.alcohol_enabled, self.tobacco_enabled

        restaurant_data = await get_restaurant_data(self.tenant_id)
        self.alcohol_enabled = False
        self.tobacco_enabled = False

        if restaurant_data:
            if 'beverageInfo' in restaurant_data and isinstance(restaurant_data['beverageInfo'], dict):
                if 'alcoholService' in restaurant_data['beverageInfo']:
                    self.alcohol_enabled = restaurant_data['beverageInfo']['alcoholService'].upper() == 'YES'

            if 'tobaccoInfo' in restaurant_data and isinstance(restaurant_data['tobaccoInfo'], dict):
                if 'tobaccoService' in restaurant_data['tobaccoInfo']:
                    self.tobacco_enabled = restaurant_data['tobaccoInfo']['tobaccoService'].upper() == 'YES'

        return self.alcohol_enabled, self.tobacco_enabled

    def _process_ingredient(self, ingredient: Dict[str, Any], recipe_name: str = None, source: str = 'Digi') -> None:
        """Process a single ingredient."""
        ingredient_name = ingredient['ingredientName']
        category = ingredient.get('category', '').upper()
        if (category == 'LIQUOR' and not self.alcohol_enabled) or (category == 'TOBACCO' and not self.tobacco_enabled):
            return

        result = self._lookup_ingredient(ingredient_name)
        if result:
            if result.get('inventory_match'):
                inventory_item = result['inventory_match']
                inventory_item['recipe_ingredient'] = ingredient_name
                inventory_item['recipe_name'] = recipe_name
                inventory_item['ledger'] = ingredient.get('ledgerAccount', '').upper()
                inventory_item['category'] = category
                inventory_item['sub_category'] = ingredient.get('subCategory', '').upper()
                inventory_item['source'] = source.upper()
                inventory_item['issuedTo'] = ingredient.get('issuedTo', '').upper()
                self.inventory_data.append(inventory_item)
            else:
                self.missing.append(ingredient_name)
            for package in result.get('packaging_matches', []):
                package['recipe_ingredient'] = ingredient_name
                package['recipe_name'] = recipe_name
                self.package_data.append(package)


    def _popular_inventory(self, popular_items: Optional[List[str]] = None) -> None:
        """Process popular inventory items in batches."""
        if not self.popular_items_processed:
            initial_processed_count = self.processed_menus

            if popular_items is None:
                popular_items_query = text(f"""
                    SELECT
                        item_name,
                        array_length(source, 1) AS source_count
                    FROM
                        inventory_master
                    WHERE
                        array_length(source, 1) > {POPULAR_ITEM_THRESHOLD}
                    ORDER BY
                        array_length(source, 1) DESC;
                """)
                with engine.connect() as conn:
                    result = conn.execute(popular_items_query)
                    popular_items = [row[0] for row in result.fetchall()]

            workareas_info = self.pos_flow.get_workareas_for_prompt()
            successfully_processed = 0
            for i in range(0, len(popular_items), INVENTORY_BATCH_SIZE):
                batch = popular_items[i:i + INVENTORY_BATCH_SIZE]
                try:
                    prompt = (
                        f"Provide the classification details for the following ingredients: {', '.join(batch)}\n\n"
                        f"{workareas_info}\n\n"
                        f"For the issuedTo field, map each ingredient to ALL potentially relevant workareas from the list above. "
                        f"When in doubt, include the workarea - false positives are acceptable, missing mappings are not. "
                        f"Always use workareas from the provided list."
                    )
                    response = ingredient_classifier.run(prompt)
                    self.popular1 +=1
                    formatted_ingredients = response.content.model_dump()
                    batch_success_count = 0
                    for ingredient in formatted_ingredients.get("classifications", []):
                        self._process_ingredient(ingredient)
                        batch_success_count += 1

                    successfully_processed += batch_success_count
                    self.processed_menus = initial_processed_count + successfully_processed
                    self._update_progress_and_time()

                except Exception as e:
                    print(f"Error processing batch {i // INVENTORY_BATCH_SIZE + 1}: {str(e)}")
            if popular_items is None:
                self.popular_items_processed = True

    def _process_recipes(self, recipes: List[str]) -> None:
        """Process recipes in batches."""
        total_successful = 0
        source = "LLM"

        for i in range(0, len(recipes), RECIPE_BATCH_SIZE):
            batch = recipes[i:i + RECIPE_BATCH_SIZE]
            regex_patterns = [{"menuItemName": {"$regex": f"^{recipe}$", "$options": "i"}} for recipe in batch]
            pipeline = [
                {"$match": {"$or": regex_patterns}},
                {"$sort": {"_id": -1}},
                {
                    "$group": {
                        "_id": "$menuItemName",
                        "latest_record": {"$first": "$$ROOT"}
                    }
                }
            ]
            latest_recipes = list(Servingsizerecipes.aggregate(pipeline))

            found_recipe_names = set()
            all_batch_ingredients = []

            for record in latest_recipes:
                recipe = record.get("latest_record")
                recipe_name = recipe.get("menuItemName")

                ingredients = [
                    ing["IngredientName"]
                    for ing in recipe.get("Ingredients", [])
                    if ing["IngredientName"] not in self.processed_ingredients
                ]
                if ingredients:
                    found_recipe_names.add(recipe_name)
                    all_batch_ingredients.extend(ingredients)
            if all_batch_ingredients:
                self._popular_inventory(all_batch_ingredients)

            if found_recipe_names:
                total_successful += len(found_recipe_names)
                self.processed_menus += len(found_recipe_names)
                self._update_progress_and_time()

            missing_recipes = [recipe for recipe in batch if recipe not in found_recipe_names]
            if missing_recipes:
                print(f"{len(missing_recipes)} recipes not found in DB, falling back to LLM...")
                try:
                    workareas_info = self.pos_flow.get_workareas_for_prompt()
                    prompt = (
                        f"Predict accurate ingredient lists for these menu items: {', '.join(missing_recipes)}.\n\n"
                        f"When possible, prioritize using these existing ingredients: {', '.join(self.processed_ingredients)}.\n\n"
                        f"{workareas_info}\n\n"
                        f"For the issuedTo field, map each ingredient to ALL potentially relevant workareas from the list above. "
                        f"When in doubt, include the workarea - false positives are acceptable, missing mappings are not. "
                        f"Always use workareas from the provided list."
                    )
                    self.recipe1 +=1
                    response = ingredient_predictor.run(prompt)
                    prediction = response.content.model_dump()

                    successfully_processed_recipes = set()

                    for recipe_data in prediction.get('predictions', []):
                        recipe_name = recipe_data.get('recipe_name')
                        ingredients = recipe_data.get('ingredients', [])
                        if ingredients:
                            successfully_processed_recipes.add(recipe_name)
                            for ingredient in ingredients:
                                self._process_ingredient(ingredient, recipe_name, source)

                    if successfully_processed_recipes:
                        total_successful += len(successfully_processed_recipes)
                        self.processed_menus += len(successfully_processed_recipes)
                        self._update_progress_and_time()

                except Exception as e:
                    print(f"Error processing batch {i // RECIPE_BATCH_SIZE + 1}: {str(e)}")


@router.post("/recipe-insight/")
async def recipe_insight(request_data: RecipeRequest):
    try:
        system_message_prompt = SystemMessagePromptTemplate.from_template(system_prompt)
        human_message_prompt = HumanMessagePromptTemplate.from_template(user_prompt)
        chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])
        formatted_prompt = chat_prompt.format_prompt(
            menu_master=request_data.menu_master,
            menu_recipes=request_data.menu_recipes
        ).to_messages()

        chat = ChatOpenAI(
            model_name=OPENAI_MODEL_NAME,
            temperature=0.1,
            openai_api_key=OPENAI_API_KEY
        )
        result = chat.invoke(formatted_prompt)

        return {"data": result.content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing recipe insight: {str(e)}")


@router.post("/start_processing", response_model=ProcessingResponse)
async def start_processing(request: StartProcessingRequest, background_tasks: BackgroundTasks):
    tenant_id = request.tenantId
    inventory_file = Path("output") / f"{tenant_id}_inventory.xlsx"
    package_file = Path("output") / f"{tenant_id}_package.xlsx"

    if inventory_file.exists() and package_file.exists():
        return {
            "success": True,
            "message": "Processing already completed. Output files are available."
        }

    if tenant_id in processing_data:
        if processing_data[tenant_id]["status"] in ["complete", "failed"]:
            return {
                "success": True,
                "processingId": processing_data[tenant_id]["processingId"],
                "message": f"Processing already {processing_data[tenant_id]['status']} for this tenant"
            }

        elif processing_data[tenant_id]["status"] in ["initializing", "processing"]:
            return {
                "success": True,
                "processingId": processing_data[tenant_id]["processingId"],
                "message": f"Processing already in progress ({processing_data[tenant_id]['progress']}% complete)"
            }

    processing_id = str(uuid.uuid4())
    workflow_service = RecipeWorkflowService(tenant_id)

    processing_data[tenant_id] = {
        "processingId": processing_id,
        "startTime": time.time(),
        "status": "initializing",
        "progress": 0,
        "currentStep": 0,
        "steps": [
            {"name": "Initializing tenant data models", "completed": False},
            {"name": "Fetching recipes from POS", "completed": False},
            {"name": "Processing ingredients and packaging", "completed": False},
            {"name": "Finalizing and saving results", "completed": False},
        ],
        "total_menus": 0,
        "processed_menus": 0
    }

    background_tasks.add_task(workflow_service.run_workflow, processing_id)

    return {"success": True, "processingId": processing_id, "message": "Processing started"}


@router.get("/get_status", response_model=StatusResponse)
async def get_status(tenantId: str = Query(...)):
    inventory_file = Path("output") / f"{tenantId}_inventory.xlsx"
    package_file = Path("output") / f"{tenantId}_package.xlsx"

    if inventory_file.exists() and package_file.exists():
        return {
                "status": "complete",
                "progress": 100,
                "currentStep": 2,
                "message": "Processing complete",
                "total_menus": 0,
                "processed_menus": 0,
                "estimated_time_remaining": 0
            }
    if tenantId not in processing_data:
        data = processing_data[tenantId]
        estimated_time_remaining = data.get("last_estimated_time")

        if data["status"] == "processing" and data["total_menus"] > 0:
            remaining_menus = data["total_menus"] - data["processed_menus"]

            if data["processed_menus"] > data.get("last_processed_menus", 0):
                estimated_time_remaining = max(0, int(AVERAGE_RATE_PER_MENU * remaining_menus))
                data["last_processed_menus"] = data["processed_menus"]
                data["last_estimated_time"] = estimated_time_remaining

        return {
            "status": data["status"],
            "progress": data["progress"],
            "currentStep": data["currentStep"],
            "message": data.get("message"),
            "total_menus": data["total_menus"],
            "processed_menus": data["processed_menus"],
            "estimated_time_remaining": estimated_time_remaining
        }
    else:
        return {
                "status": "Pending",
                "progress": 5,
                "currentStep": 1,
                "message": "Processing complete",
                "total_menus": 0,
                "processed_menus": 0,
                "estimated_time_remaining": 0
            }



@router.get("/search", response_model=Dict[str, Any])
async def search(ingredient_name: str = Query(...)):
    ingredient_name = ingredient_name.strip()
    if not ingredient_name:
        raise HTTPException(status_code=400, detail="Ingredient name cannot be empty")

    query = text("SELECT search_ingredient(:ingredient_name)")
    with engine.connect() as conn:
        result = conn.execute(query, {"ingredient_name": ingredient_name}).scalar()
    if result:
        if result.get('packaging_matches') is None:
            result['packaging_matches'] = []
        return result
    else:
        return {
            "inventory_match": None,
            "packaging_matches": []
        }


@router.get("/download")
async def download(tenantId: str = Query(...), type: str = Query(...)):
    """
    Return file data as base64 encoded string
    type can be 'inventory', 'package', 'vendor', or 'all'
    """

    inventory_file = Path("output") / f"{tenantId}_inventory.xlsx"
    package_file = Path("output") / f"{tenantId}_package.xlsx"
    vendor_file = Path("output") / f"{tenantId}_vendor.xlsx"

    if type == "inventory":
        if not inventory_file.exists():
            raise HTTPException(status_code=400, detail="Inventory file not found")

        with open(inventory_file, "rb") as f:
            file_data = f.read()

        base64_encoded = base64.b64encode(file_data).decode('utf-8')
        return base64_encoded

    elif type == "package":
        if not package_file.exists():
            raise HTTPException(status_code=400, detail="Package file not found")

        with open(package_file, "rb") as f:
            file_data = f.read()

        base64_encoded = base64.b64encode(file_data).decode('utf-8')
        return base64_encoded

    elif type == "vendor":
        if not vendor_file.exists():
            raise HTTPException(status_code=400, detail="Vendor file not found")

        with open(vendor_file, "rb") as f:
            file_data = f.read()

        base64_encoded = base64.b64encode(file_data).decode('utf-8')
        return base64_encoded

    elif type == "all":
        import zipfile
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            if inventory_file.exists():
                zip_file.write(inventory_file, arcname=inventory_file.name)
            if package_file.exists():
                zip_file.write(package_file, arcname=package_file.name)
            if vendor_file.exists():
                zip_file.write(vendor_file, arcname=vendor_file.name)
        zip_buffer.seek(0)
        zip_data = zip_buffer.getvalue()
        base64_encoded = base64.b64encode(zip_data).decode('utf-8')
        return base64_encoded

    else:
        raise HTTPException(status_code=400, detail="Invalid type. Must be 'inventory', 'package', 'vendor', or 'all'")

@router.delete("/reset_processing")
async def reset_processing(tenantId: str = Query(...)):
    """
    Reset processing for a tenant - mostly for testing purposes
    """
    if tenantId in processing_data:
        del processing_data[tenantId]
        return {"success": True, "message": "Processing data reset"}
    else:
        return {"success": False, "message": "No processing data found for this tenant"}


async def get_or_create_memory(tenant_id: str) -> ConversationBufferMemory:
    if tenant_id not in chat_memories:
        chat_memories[tenant_id] = ConversationBufferMemory(
            return_messages=True,
            memory_key="chat_history"
        )
        if tenant_id not in current_responses:
            current_responses[tenant_id] = ""
    return chat_memories[tenant_id]


@router.post("/clear_history")
async def clear_history(
    background_tasks: BackgroundTasks,
    tenant_id: str = Query(...)
):
    try:
        if tenant_id in chat_memories:
            chat_memories[tenant_id] = ConversationBufferMemory(
                return_messages=True,
                memory_key="chat_history"
            )
        if tenant_id in current_responses:
            current_responses[tenant_id] = ""

        background_tasks.add_task(clear_conversation_history, tenant_id)

        return {"status": "ok", "message": f"Chat history cleared for tenant {tenant_id}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error clearing conversation history: {str(e)}")



@router.get("/restaurant_data")
async def get_restaurant_data_endpoint(
    tenant_id: str = Query(...)
):
    """Retrieve the latest restaurant data for the specified tenant"""
    try:
        data = await get_restaurant_data(tenant_id)
        if data:
            return {"status": "ok", "data": data}
        else:
            print(f"No restaurant data found for tenant: {tenant_id}")
            return {"status": "not_found", "message": "No restaurant data found for this tenant"}
    except Exception as e:
        print(f"Error retrieving restaurant data for tenant {tenant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving restaurant data: {str(e)}")

@router.get("/conversation_history", response_model=ConversationHistoryResponse)
async def get_conversation_history_endpoint(
    background_tasks: BackgroundTasks,
    tenant_id: str = Query(...)
):
    """Retrieve conversation history for the specified tenant"""
    try:
        messages = await get_conversation_history(tenant_id)

        if not messages and tenant_id in chat_memories:
            memory = chat_memories[tenant_id]

            for message in memory.chat_memory.messages:
                message_type = "human" if message.type == "human" else "ai"
                background_tasks.add_task(save_message, tenant_id, message.content, message_type)

            for message in memory.chat_memory.messages:
                message_type = "human" if message.type == "human" else "ai"
                messages.append(ChatMessage(
                    content=message.content,
                    type=message_type,
                    created_at=datetime.now()
                ))

        return ConversationHistoryResponse(
            tenant_id=tenant_id,
            messages=messages
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation history: {str(e)}")


CHAT_MODEL = os.getenv("OPENAI_MODEL_NAME", "gpt-4o-mini")
CHAT_TEMPERATURE = float(os.getenv("CHAT_TEMPERATURE", "0.7"))
CHAT_MAX_TOKENS = int(os.getenv("CHAT_MAX_TOKENS", "1000"))
FOLLOW_UP_MAX_TOKENS = int(os.getenv("FOLLOW_UP_MAX_TOKENS", "500"))
RESTAURANT_DATA_FUNCTION_NAME = "collect_restaurant_data"
SUCCESS_MESSAGE = "Your restaurant information has been saved. You can view all your restaurant details in the panel on the right side of the screen."

DEFAULT_RESTAURANT_DATA = {
    "totalOutlets": 1,
    "outletDetails": [],
    "commonCuisinesAcrossOutlets": [],
    "cuisineMenuCounts": [],
    "signatureElements": {"signatureDishes": []},
    "beverageInfo": {"alcoholService": "No"},
    "tobaccoInfo": {"tobaccoService": "No"}
}

REQUIRED_RESTAURANT_FIELDS = [
    'totalOutlets',
    'outletDetails',
    'commonCuisinesAcrossOutlets',
    'cuisineMenuCounts',
    'signatureElements',
    'beverageInfo',
    'tobaccoInfo'
]

async def convert_text_to_uppercase(data_dict):
    """Convert all string values in the restaurant data to uppercase"""
    if 'outletDetails' in data_dict:
        for outlet in data_dict['outletDetails']:
            if 'outletName' in outlet:
                outlet['outletName'] = outlet['outletName'].upper()

            if 'outletAddress' in outlet:
                outlet['outletAddress'] = outlet['outletAddress'].upper()

            if 'outletWorkAreas' in outlet and isinstance(outlet['outletWorkAreas'], list):
                outlet['outletWorkAreas'] = [area.upper() for area in outlet['outletWorkAreas']]

            if 'outletAbbreviation' not in outlet or not outlet['outletAbbreviation']:
                if 'outletName' in outlet:
                    generate_abbreviation(outlet)

    if 'commonCuisinesAcrossOutlets' in data_dict and isinstance(data_dict['commonCuisinesAcrossOutlets'], list):
        data_dict['commonCuisinesAcrossOutlets'] = [cuisine.upper() for cuisine in data_dict['commonCuisinesAcrossOutlets']]

    if 'signatureElements' in data_dict and isinstance(data_dict['signatureElements'], dict):
        if 'signatureDishes' in data_dict['signatureElements'] and isinstance(data_dict['signatureElements']['signatureDishes'], list):
            data_dict['signatureElements']['signatureDishes'] = [dish.upper() for dish in data_dict['signatureElements']['signatureDishes']]

    if 'beverageInfo' in data_dict and isinstance(data_dict['beverageInfo'], dict):
        if 'alcoholService' in data_dict['beverageInfo']:
            data_dict['beverageInfo']['alcoholService'] = data_dict['beverageInfo']['alcoholService'].upper()

    if 'tobaccoInfo' in data_dict and isinstance(data_dict['tobaccoInfo'], dict):
        if 'tobaccoService' in data_dict['tobaccoInfo']:
            data_dict['tobaccoInfo']['tobaccoService'] = data_dict['tobaccoInfo']['tobaccoService'].upper()

    return data_dict

def generate_abbreviation(outlet):
    """Generate an abbreviation for an outlet name"""
    words = outlet['outletName'].split()
    if len(words) > 1:
        abbr = ''.join(word[0] for word in words if word)
    else:
        abbr = outlet['outletName'][:min(4, len(outlet['outletName']))]
    outlet['outletAbbreviation'] = abbr.upper()

def ensure_required_fields(data_dict):
    """Ensure all required fields exist in the restaurant data"""
    for field in REQUIRED_RESTAURANT_FIELDS:
        if field not in data_dict:
            if field == 'totalOutlets':
                data_dict[field] = 1
            elif field == 'outletDetails':
                data_dict[field] = []
            elif field == 'commonCuisinesAcrossOutlets':
                data_dict[field] = []
            elif field == 'cuisineMenuCounts':
                data_dict[field] = []
            elif field == 'signatureElements':
                data_dict[field] = {'signatureDishes': []}
            elif field == 'beverageInfo':
                data_dict[field] = {'alcoholService': 'No'}
            elif field == 'tobaccoInfo':
                data_dict[field] = {'tobaccoService': 'No'}

    if 'signatureElements' in data_dict and not isinstance(data_dict['signatureElements'], dict):
        data_dict['signatureElements'] = {'signatureDishes': data_dict.get('signatureElements', [])}

    if 'beverageInfo' in data_dict and not isinstance(data_dict['beverageInfo'], dict):
        data_dict['beverageInfo'] = {'alcoholService': data_dict.get('beverageInfo', 'No')}

    if 'tobaccoInfo' in data_dict and not isinstance(data_dict['tobaccoInfo'], dict):
        data_dict['tobaccoInfo'] = {'tobaccoService': data_dict.get('tobaccoInfo', 'No')}

    return data_dict

@router.get("/ask")
async def ask(
    query: str,
    background_tasks: BackgroundTasks,
    tenant_id: str = Query(...)
):
    """Process a user query and generate a response."""
    memory = await get_or_create_memory(tenant_id)
    memory.chat_memory.add_user_message(query)
    background_tasks.add_task(save_message, tenant_id, query, "human")

    if tenant_id not in current_responses:
        current_responses[tenant_id] = ""

    existing_data = await get_restaurant_data(tenant_id)

    # Reload the template for each request to ensure we get the latest version
    template = jinja_env.get_template('restaurant_onboarding_tool.j2')
    system_message = template.render()
    if existing_data:
        if "_metadata" in existing_data:
            del existing_data["_metadata"]
        system_message += f"\n\nEXISTING_RESTAURANT_DATA: {json.dumps(existing_data)}\n\nIMPORTANT: Start with the existing restaurant data above. Only ask questions about missing or incomplete information. Do not ask about information that is already provided."

    messages = [{"role": "system", "content": system_message}]
    for msg in memory.chat_memory.messages:
        role = "user" if msg.type == "human" else "assistant"
        messages.append({"role": role, "content": msg.content})

    tools = [{
        "type": "function",
        "function": {
            "name": RESTAURANT_DATA_FUNCTION_NAME,
            "description": "Collect restaurant data including outlets, cuisines, and other details",
            "parameters": RestaurantMenuPredictionData.model_json_schema()
        }
    }]

    response_text = ""
    client = openai.OpenAI(api_key=OPENAI_API_KEY)

    try:
        response = client.chat.completions.create(
            model=CHAT_MODEL,
            messages=messages,
            tools=tools,
            temperature=CHAT_TEMPERATURE,
            max_tokens=CHAT_MAX_TOKENS,
            stream=False
        )

        if response.choices and response.choices[0].message:
            message = response.choices[0].message

            if message.content:
                response_text = message.content

            if message.tool_calls:
                tool_call = message.tool_calls[0]
                function_name = tool_call.function.name
                function_args = tool_call.function.arguments
                tool_call_id = tool_call.id

                if function_name == RESTAURANT_DATA_FUNCTION_NAME and function_args:
                    args_data = DEFAULT_RESTAURANT_DATA.copy()

                    parsed = json.loads(function_args)
                    if isinstance(parsed, dict):
                        args_data.update(parsed)

                    existing_data = await get_restaurant_data(tenant_id)
                    if existing_data:
                        if "_metadata" in existing_data:
                            del existing_data["_metadata"]

                        for field in existing_data:
                            if field not in args_data or not args_data[field]:
                                args_data[field] = existing_data[field]

                    args_data = ensure_required_fields(args_data)

                    restaurant_data = RestaurantMenuPredictionData(**args_data)
                    restaurant_data_dict = restaurant_data.model_dump()

                    restaurant_data_dict = await convert_text_to_uppercase(restaurant_data_dict)

                    await save_restaurant_data(tenant_id, restaurant_data_dict)

                    response_text += f"\n\n{SUCCESS_MESSAGE}"

                    follow_up_messages = messages.copy()
                    formatted_args = json.dumps(args_data)

                    follow_up_messages.append({
                        "role": "assistant",
                        "content": None,
                        "tool_calls": [
                            {
                                "id": tool_call_id,
                                "type": "function",
                                "function": {
                                    "name": RESTAURANT_DATA_FUNCTION_NAME,
                                    "arguments": formatted_args
                                }
                            }
                        ]
                    })

                    model_json = json.dumps(restaurant_data.model_dump())
                    follow_up_messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call_id,
                        "name": RESTAURANT_DATA_FUNCTION_NAME,
                        "content": model_json
                    })

                    follow_up_response = client.chat.completions.create(
                        model=CHAT_MODEL,
                        messages=follow_up_messages,
                        temperature=CHAT_TEMPERATURE,
                        max_tokens=FOLLOW_UP_MAX_TOKENS,
                        stream=False
                    )

                    if follow_up_response.choices and follow_up_response.choices[0].message.content:
                        follow_up_message = follow_up_response.choices[0].message.content
                        response_text += f"\n\n{follow_up_message}"

    except Exception as e:
        response_text = f"Error: {str(e)}"
        return {"error": str(e), "message": response_text}

    memory.chat_memory.add_ai_message(response_text)
    background_tasks.add_task(save_message, tenant_id, response_text, "ai")

    return {
        "content": response_text,
        "restaurant_data": await get_restaurant_data(tenant_id)
    }



@router.post("/documentation")
async def download_database_documentation(request: DatabaseDocumentationRequest = Body(DatabaseDocumentationRequest())):
    collections, relationships, groups = get_database_documentation(request.include_sample_data)
    collections_json = json.dumps([c.model_dump() for c in collections], indent=2)
    relationships_json = json.dumps([r.model_dump() for r in relationships], indent=2)
    groups_json = json.dumps([g.model_dump() for g in groups], indent=2)
    prompt = db_documentation_prompt_template.render(
        collections=collections,
        collections_json=collections_json,
        relationships_json=relationships_json,
        groups_json=groups_json
    )

    chat = ChatOpenAI(
        model_name="gpt-4o-mini",
        temperature=0.2,
        openai_api_key=OPENAI_API_KEY
    )
    response = chat.invoke([SystemMessage(content=prompt)])
    summary = response.content
    os.makedirs('output', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tenant_id = request.tenant_id or "default"
    filename = f"{tenant_id}_db_documentation_{timestamp}.md"
    file_path = os.path.join("output", filename)

    with open(file_path, 'w') as f:
        f.write(summary)

    return {"file_path": file_path}
