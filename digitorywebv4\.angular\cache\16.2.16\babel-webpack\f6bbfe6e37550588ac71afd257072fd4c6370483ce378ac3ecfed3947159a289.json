{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"src/app/services/restaurant-summary.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r12.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"div\", 28);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_13_div_5_Template, 3, 4, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r12.sender === \"user\", message_r12.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r12.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r12.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"span\")(3, \"span\")(4, \"span\");\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵtext(6, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.restaurantSummary.location);\n  }\n}\nfunction ChatBotComponent_p_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_37_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cuisine_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cuisine_r16);\n  }\n}\nfunction ChatBotComponent_ul_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_37_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantSummary.cuisineTypes);\n  }\n}\nfunction ChatBotComponent_p_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ul_42_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const specialty_r18 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(specialty_r18);\n  }\n}\nfunction ChatBotComponent_ul_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35);\n    i0.ɵɵtemplate(1, ChatBotComponent_ul_42_li_1_Template, 2, 1, \"li\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.restaurantSummary.specialties);\n  }\n}\nfunction ChatBotComponent_p_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_table_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 37)(1, \"tr\")(2, \"td\");\n    i0.ɵɵtext(3, \"Menu Count:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tr\")(7, \"td\");\n    i0.ɵɵtext(8, \"Categories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.restaurantSummary.menuCategories.length);\n  }\n}\nfunction ChatBotComponent_p_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.restaurantSummary.operatingHours);\n  }\n}\nfunction ChatBotComponent_p_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1, \"Pending\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, summaryService, cd, snackBar) {\n    this.sseService = sseService;\n    this.summaryService = summaryService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    // Restaurant summary information\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    // Add properties for summary refresh\n    this.summarySubscription = null;\n    this.refreshInterval = 5000; // 5 seconds\n    this.isLoadingSummary = false;\n    this.summaryError = false;\n    // Flag to track if conversation history has been loaded\n    this.conversationHistoryLoaded = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n      this.loadRestaurantSummary();\n      // Set up periodic refresh of restaurant summary\n      this.summarySubscription = interval(this.refreshInterval).subscribe(() => {\n        this.loadRestaurantSummary();\n      });\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Update existing message\n          this.messages[existingMessageIndex] = message;\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          if (!duplicateMessage) {\n            // Add new bot message\n            this.messages.push(message);\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // No need to track connection status\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.summarySubscription) {\n      this.summarySubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  updateRestaurantSummary(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') || lowerMessage.includes('located') || lowerMessage.includes('area') || lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') || lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') || lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') || lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') || lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n    this.cd.detectChanges();\n  }\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractInformation(message) {\n    // Remove common question phrases\n    const cleanedMessage = message.replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '').replace(/\\?/g, '');\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  extractListItems(message) {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message.split(/,|\\sand\\s/).map(item => item.trim()).filter(item => item.length > 0).map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m => m.sender === message.sender && m.text === message.text);\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to just the welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Conversation has been cleared. How can I help you today?',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  // ngOnDestroy is already defined above\n  /**\n   * Load restaurant summary data from the API\n   */\n  loadRestaurantSummary() {\n    if (!this.tenantId) return;\n    this.isLoadingSummary = true;\n    this.summaryError = false;\n    this.cd.detectChanges();\n    this.summaryService.getRestaurantSummary(this.tenantId).subscribe({\n      next: response => {\n        if (response.success && response.summary) {\n          // Map the backend summary to our frontend model\n          const summary = response.summary;\n          // Update the restaurant summary with data from the API\n          this.restaurantSummary = {\n            location: this.getLocationFromOutlets(summary.outlets),\n            cuisineTypes: [...summary.common_cuisines, ...this.getCuisinesFromOutlets(summary.outlets)],\n            specialties: this.getWorkAreasFromOutlets(summary.outlets),\n            menuCount: summary.total_outlets,\n            menuCategories: summary.outlets.map(o => o.name),\n            operatingHours: `Last updated: ${new Date(summary.last_updated).toLocaleTimeString()}`\n          };\n          this.isLoadingSummary = false;\n          this.cd.detectChanges();\n        }\n      },\n      error: error => {\n        console.error('Error loading restaurant summary:', error);\n        this.isLoadingSummary = false;\n        this.summaryError = true;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Extract location information from outlets\n   */\n  getLocationFromOutlets(outlets) {\n    if (!outlets || outlets.length === 0) return '';\n    return outlets.map(o => o.address).join(', ');\n  }\n  /**\n   * Extract cuisine types from outlets\n   */\n  getCuisinesFromOutlets(outlets) {\n    if (!outlets || outlets.length === 0) return [];\n    const cuisines = new Set();\n    outlets.forEach(outlet => {\n      if (outlet.cuisines && Array.isArray(outlet.cuisines)) {\n        outlet.cuisines.forEach(cuisine => cuisines.add(cuisine));\n      }\n    });\n    return Array.from(cuisines);\n  }\n  /**\n   * Extract work areas from outlets\n   */\n  getWorkAreasFromOutlets(outlets) {\n    if (!outlets || outlets.length === 0) return [];\n    const workAreas = new Set();\n    outlets.forEach(outlet => {\n      if (outlet.work_areas && Array.isArray(outlet.work_areas)) {\n        outlet.work_areas.forEach(area => workAreas.add(area));\n      }\n    });\n    return Array.from(workAreas);\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i2.RestaurantSummaryService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 16,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-summary\"], [1, \"summary-header\"], [1, \"summary-title\"], [1, \"summary-icon\"], [1, \"summary-content\"], [1, \"summary-section\"], [4, \"ngIf\"], [\"class\", \"placeholder-text\", 4, \"ngIf\"], [\"class\", \"compact-list\", 4, \"ngIf\"], [\"class\", \"summary-table\", 4, \"ngIf\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-text\"], [1, \"placeholder-text\"], [1, \"compact-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"summary-table\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_9_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"clear\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵtemplate(13, ChatBotComponent_div_13_Template, 6, 8, \"div\", 9);\n          i0.ɵɵtemplate(14, ChatBotComponent_div_14_Template, 7, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_17_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_18_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17)(24, \"mat-icon\", 18);\n          i0.ɵɵtext(25, \"summarize\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Summary\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"h4\");\n          i0.ɵɵtext(31, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ChatBotComponent_p_32_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(33, ChatBotComponent_p_33_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 20)(35, \"h4\");\n          i0.ɵɵtext(36, \"Cuisine Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, ChatBotComponent_ul_37_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(38, ChatBotComponent_p_38_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 20)(40, \"h4\");\n          i0.ɵɵtext(41, \"Specialties\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, ChatBotComponent_ul_42_Template, 2, 1, \"ul\", 23);\n          i0.ɵɵtemplate(43, ChatBotComponent_p_43_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 20)(45, \"h4\");\n          i0.ɵɵtext(46, \"Menu Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ChatBotComponent_table_47_Template, 11, 2, \"table\", 24);\n          i0.ɵɵtemplate(48, ChatBotComponent_p_48_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 20)(50, \"h4\");\n          i0.ɵɵtext(51, \"Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, ChatBotComponent_p_52_Template, 2, 1, \"p\", 21);\n          i0.ɵɵtemplate(53, ChatBotComponent_p_53_Template, 2, 0, \"p\", 22);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.location);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.location);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.cuisineTypes.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.specialties.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.menuCount === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantSummary.operatingHours);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantSummary.operatingHours);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, ReactiveFormsModule, MatButtonModule, i6.MatIconButton, i6.MatMiniFabButton, MatCardModule, MatFormFieldModule, i7.MatFormField, MatIconModule, i8.MatIcon, MatInputModule, i9.MatInput, MatProgressSpinnerModule, MatTooltipModule, i10.MatTooltip, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  min-height: 450px;\\n  gap: 15px;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  width: 32px;\\n  line-height: 32px;\\n  color: #333;\\n  min-width: 32px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 8px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  line-height: 18px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n  background-color: #e0e0e0;\\n  border-color: #ccc;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  margin-right: 16px;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  width: 100%;\\n  padding: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  max-width: 80%;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #57705d;\\n  margin-bottom: 16px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.bot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  max-width: 80%;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 10px 16px;\\n  border-radius: 18px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  margin-left: 8px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  float: left;\\n  margin: 0 2px;\\n  background-color: #999;\\n  display: block;\\n  border-radius: 50%;\\n  opacity: 0.4;\\n  transform: translateY(0);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(1) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.3333s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(2) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.6666s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-of-type(3) {\\n  animation: 1s _ngcontent-%COMP%_blink-bounce infinite 0.9999s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 12px;\\n  color: #666;\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink-bounce {\\n  0%, 100% {\\n    opacity: 0.4;\\n    transform: translateY(0);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: translateY(-4px);\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px 16px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.summary-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.summary-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.summary-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 15px;\\n  color: #555;\\n  font-weight: 500;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  color: #616161;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.restaurant-summary[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 12px;\\n  background-color: #57705d;\\n  color: white;\\n  min-height: 36px;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.summary-content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  overflow-y: auto;\\n  flex: 1;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 3px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 16px;\\n}\\n\\n.compact-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 2px;\\n  font-size: 13px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n  color: #555;\\n}\\n\\n.summary-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.placeholder-text[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n  font-style: italic;\\n  font-size: 12px;\\n  margin: 0;\\n}\\n\\n.summary-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n  color: #555;\\n}\\n\\n.summary-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  font-weight: 500;\\n  width: 50%;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.summary-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #e0e0e0;\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  width: 40px;\\n  justify-content: center;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 6px;\\n  width: 6px;\\n  margin: 0 2px;\\n  background-color: #555;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.2s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n  50% {\\n    transform: translateY(-2px) scale(1.05);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.7;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MarkdownPipe", "interval", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r12", "timestamp", "ɵɵelement", "ɵɵtemplate", "ChatBotComponent_div_13_div_5_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ctx_r2", "restaurantSummary", "location", "cuisine_r16", "ChatBotComponent_ul_37_li_1_Template", "ctx_r4", "cuisineTypes", "specialty_r18", "ChatBotComponent_ul_42_li_1_Template", "ctx_r6", "specialties", "ctx_r8", "menuCount", "menuCategories", "length", "ctx_r10", "operatingHours", "ChatBotComponent", "constructor", "sseService", "summaryService", "cd", "snackBar", "tenantId", "tenantName", "messages", "currentMessage", "isConnecting", "isWaitingForResponse", "messageSubscription", "connectionSubscription", "summarySubscription", "refreshInterval", "isLoadingSummary", "summaryError", "conversationHistoryLoaded", "ngOnChanges", "changes", "currentValue", "previousValue", "loadConversationHistory", "ngOnInit", "loadRestaurantSummary", "subscribe", "id", "generateId", "Date", "messages$", "message", "startsWith", "detectChanges", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "push", "sort", "a", "b", "getTime", "isDuplicate", "some", "Math", "abs", "console", "log", "scrollToBottom", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "trim", "open", "duration", "messageToSend", "updateRestaurantSummary", "userMessage", "next", "error", "lowerMessage", "toLowerCase", "extractInformation", "cuisines", "extractListItems", "Set", "menuCountMatch", "match", "parseInt", "categories", "cleanedMessage", "replace", "char<PERSON>t", "toUpperCase", "slice", "split", "map", "item", "filter", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "substring", "trackById", "_index", "for<PERSON>ach", "existingMessage", "clearConversationHistory", "getRestaurantSummary", "response", "success", "summary", "getLocationFromOutlets", "outlets", "common_cuisines", "getCuisinesFromOutlets", "getWorkAreasFromOutlets", "total_outlets", "o", "name", "last_updated", "toLocaleTimeString", "address", "join", "outlet", "Array", "isArray", "cuisine", "add", "from", "work<PERSON><PERSON><PERSON>", "work_areas", "area", "ɵɵdirectiveInject", "i1", "SseService", "i2", "RestaurantSummaryService", "ChangeDetectorRef", "i3", "MatSnackBar", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ɵɵlistener", "ChatBotComponent_Template_button_click_9_listener", "ChatBotComponent_div_13_Template", "ChatBotComponent_div_14_Template", "ChatBotComponent_Template_input_ngModelChange_17_listener", "$event", "ChatBotComponent_Template_input_keydown_17_listener", "ChatBotComponent_Template_button_click_18_listener", "ChatBotComponent_p_32_Template", "ChatBotComponent_p_33_Template", "ChatBotComponent_ul_37_Template", "ChatBotComponent_p_38_Template", "ChatBotComponent_ul_42_Template", "ChatBotComponent_p_43_Template", "ChatBotComponent_table_47_Template", "ChatBotComponent_p_48_Template", "ChatBotComponent_p_52_Template", "ChatBotComponent_p_53_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i6", "MatIconButton", "MatMiniFabButton", "i7", "MatFormField", "i8", "MatIcon", "i9", "MatInput", "i10", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { RestaurantSummaryService } from 'src/app/services/restaurant-summary.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { RestaurantSummary } from 'src/app/models/restaurant-summary.model';\nimport { Subscription, interval } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    SafeHtmlPipe,\n    MarkdownPipe\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n\n  // Restaurant summary information\n  restaurantSummary: RestaurantSummary = {\n    location: '',\n    cuisineTypes: [],\n    specialties: [],\n    menuCount: 0,\n    menuCategories: [],\n    operatingHours: ''\n  };\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n\n  // Add properties for summary refresh\n  private summarySubscription: Subscription | null = null;\n  private refreshInterval = 5000; // 5 seconds\n  isLoadingSummary = false;\n  summaryError = false;\n\n  constructor(\n    private sseService: SseService,\n    private summaryService: RestaurantSummaryService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId'] && changes['tenantId'].currentValue) {\n      // Only reload if the tenant ID actually changed\n      if (changes['tenantId'].previousValue !== changes['tenantId'].currentValue) {\n        this.conversationHistoryLoaded = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if conversation history has been loaded\n  private conversationHistoryLoaded = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    // Load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      this.loadConversationHistory();\n      this.loadRestaurantSummary();\n\n      // Set up periodic refresh of restaurant summary\n      this.summarySubscription = interval(this.refreshInterval).subscribe(() => {\n        this.loadRestaurantSummary();\n      });\n    } else {\n      // If no tenant ID, just show the welcome message\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            if (!duplicateMessage) {\n              // Add new bot message\n              this.messages.push(message);\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // No need to track connection status\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.summarySubscription) {\n      this.summarySubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // Update restaurant summary based on user message\n    this.updateRestaurantSummary(messageToSend);\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Update restaurant summary based on user message\n   * @param message The user message\n   */\n  private updateRestaurantSummary(message: string): void {\n    const lowerMessage = message.toLowerCase();\n\n    // Extract location information\n    if (lowerMessage.includes('location') || lowerMessage.includes('address') ||\n        lowerMessage.includes('located') || lowerMessage.includes('area') ||\n        lowerMessage.includes('city') || lowerMessage.includes('town')) {\n      // Simple extraction - in a real app, you'd use NLP\n      this.restaurantSummary.location = this.extractInformation(lowerMessage);\n    }\n\n    // Extract cuisine types\n    if (lowerMessage.includes('cuisine') || lowerMessage.includes('food type') ||\n        lowerMessage.includes('serve') || lowerMessage.includes('dishes')) {\n      const cuisines = this.extractListItems(lowerMessage);\n      if (cuisines.length > 0) {\n        this.restaurantSummary.cuisineTypes = [...new Set([...this.restaurantSummary.cuisineTypes, ...cuisines])];\n      }\n    }\n\n    // Extract specialties\n    if (lowerMessage.includes('special') || lowerMessage.includes('signature') ||\n        lowerMessage.includes('famous for') || lowerMessage.includes('known for')) {\n      const specialties = this.extractListItems(lowerMessage);\n      if (specialties.length > 0) {\n        this.restaurantSummary.specialties = [...new Set([...this.restaurantSummary.specialties, ...specialties])];\n      }\n    }\n\n    // Extract menu information\n    if (lowerMessage.includes('menu') || lowerMessage.includes('dish') ||\n        lowerMessage.includes('item') || lowerMessage.includes('food')) {\n      // Try to extract a number for menu count\n      const menuCountMatch = lowerMessage.match(/(\\d+)\\s+(menu|dish|item|food)/i);\n      if (menuCountMatch && menuCountMatch[1]) {\n        this.restaurantSummary.menuCount = parseInt(menuCountMatch[1], 10);\n      }\n\n      // Extract menu categories\n      if (lowerMessage.includes('categor')) {\n        const categories = this.extractListItems(lowerMessage);\n        if (categories.length > 0) {\n          this.restaurantSummary.menuCategories = [...new Set([...this.restaurantSummary.menuCategories, ...categories])];\n        }\n      }\n    }\n\n    // Extract operating hours\n    if (lowerMessage.includes('hour') || lowerMessage.includes('open') ||\n        lowerMessage.includes('close') || lowerMessage.includes('time')) {\n      this.restaurantSummary.operatingHours = this.extractInformation(lowerMessage);\n    }\n\n    this.cd.detectChanges();\n  }\n\n  /**\n   * Extract information from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractInformation(message: string): string {\n    // Remove common question phrases\n    const cleanedMessage = message\n      .replace(/^(what|where|when|how|is|are|do|does|can|could|would|our|my|we|i|the|a|an)\\s+/gi, '')\n      .replace(/\\?/g, '');\n\n    // Capitalize first letter\n    return cleanedMessage.charAt(0).toUpperCase() + cleanedMessage.slice(1);\n  }\n\n  /**\n   * Extract list items from a message\n   * Simple implementation - in a real app, you'd use NLP\n   */\n  private extractListItems(message: string): string[] {\n    // Look for comma-separated or 'and'-separated items\n    if (message.includes(',') || message.includes(' and ')) {\n      return message\n        .split(/,|\\sand\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0)\n        .map(item => item.charAt(0).toUpperCase() + item.slice(1));\n    }\n\n    // If no separators found, just return the whole message as one item\n    return [this.extractInformation(message)];\n  }\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.conversationHistoryLoaded) {\n      return;\n    }\n\n    // Set the flag to prevent duplicate API calls\n    this.conversationHistoryLoaded = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Load conversation history from the server\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Clear existing messages first to avoid duplicates\n          this.messages = [];\n\n          // Add messages to the local array (they'll also come through the subscription)\n          messages.forEach(message => {\n            // Check if this message already exists in the conversation\n            const existingMessage = this.messages.find(m =>\n              m.sender === message.sender && m.text === message.text\n            );\n\n            if (!existingMessage) {\n              this.messages.push(message);\n            }\n\n            // Update restaurant summary based on user messages\n            if (message.sender === 'user') {\n              this.updateRestaurantSummary(message.text);\n            }\n          });\n\n          // If no messages after deduplication, add welcome message\n          if (this.messages.length === 0) {\n            this.messages.push({\n              id: this.generateId(),\n              text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n              sender: 'bot',\n              timestamp: new Date()\n            });\n          }\n        } else {\n          // If no messages, add welcome message\n          this.messages = [{\n            id: this.generateId(),\n            text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n            sender: 'bot',\n            timestamp: new Date()\n          }];\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, add welcome message\n        this.messages = [{\n          id: this.generateId(),\n          text: 'Welcome to the Restaurant Information Assistant! I\\'ll help you gather information about your restaurant. Please tell me about your restaurant location and cuisine type.',\n          sender: 'bot',\n          timestamp: new Date()\n        }];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    // Reset restaurant summary\n    this.restaurantSummary = {\n      location: '',\n      cuisineTypes: [],\n      specialties: [],\n      menuCount: 0,\n      menuCategories: [],\n      operatingHours: ''\n    };\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to just the welcome message\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          // Reset the flag to allow loading conversation history again\n          this.conversationHistoryLoaded = false;\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [\n            {\n              id: this.generateId(),\n              text: 'Conversation has been cleared. How can I help you today?',\n              sender: 'bot',\n              timestamp: new Date()\n            }\n          ];\n\n          this.conversationHistoryLoaded = false;\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.conversationHistoryLoaded = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n  // ngOnDestroy is already defined above\n\n  /**\n   * Load restaurant summary data from the API\n   */\n  loadRestaurantSummary(): void {\n    if (!this.tenantId) return;\n\n    this.isLoadingSummary = true;\n    this.summaryError = false;\n    this.cd.detectChanges();\n\n    this.summaryService.getRestaurantSummary(this.tenantId).subscribe({\n      next: (response) => {\n        if (response.success && response.summary) {\n          // Map the backend summary to our frontend model\n          const summary = response.summary;\n\n          // Update the restaurant summary with data from the API\n          this.restaurantSummary = {\n            location: this.getLocationFromOutlets(summary.outlets),\n            cuisineTypes: [...summary.common_cuisines, ...this.getCuisinesFromOutlets(summary.outlets)],\n            specialties: this.getWorkAreasFromOutlets(summary.outlets),\n            menuCount: summary.total_outlets,\n            menuCategories: summary.outlets.map(o => o.name),\n            operatingHours: `Last updated: ${new Date(summary.last_updated).toLocaleTimeString()}`\n          };\n\n          this.isLoadingSummary = false;\n          this.cd.detectChanges();\n        }\n      },\n      error: (error) => {\n        console.error('Error loading restaurant summary:', error);\n        this.isLoadingSummary = false;\n        this.summaryError = true;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Extract location information from outlets\n   */\n  private getLocationFromOutlets(outlets: any[]): string {\n    if (!outlets || outlets.length === 0) return '';\n    return outlets.map(o => o.address).join(', ');\n  }\n\n  /**\n   * Extract cuisine types from outlets\n   */\n  private getCuisinesFromOutlets(outlets: any[]): string[] {\n    if (!outlets || outlets.length === 0) return [];\n\n    const cuisines = new Set<string>();\n    outlets.forEach(outlet => {\n      if (outlet.cuisines && Array.isArray(outlet.cuisines)) {\n        outlet.cuisines.forEach(cuisine => cuisines.add(cuisine));\n      }\n    });\n\n    return Array.from(cuisines);\n  }\n\n  /**\n   * Extract work areas from outlets\n   */\n  private getWorkAreasFromOutlets(outlets: any[]): string[] {\n    if (!outlets || outlets.length === 0) return [];\n\n    const workAreas = new Set<string>();\n    outlets.forEach(outlet => {\n      if (outlet.work_areas && Array.isArray(outlet.work_areas)) {\n        outlet.work_areas.forEach(area => workAreas.add(area));\n      }\n    });\n\n    return Array.from(workAreas);\n  }\n}\n", "<div class=\"chat-layout\">\n  <!-- Left side: Chat interface -->\n  <div class=\"chat-container\">\n    <div class=\"chat-header\">\n      <div class=\"chat-title\">\n        <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n        <span class=\"assistant-title\">Restaurant details</span>\n      </div>\n      <div class=\"chat-actions\">\n        <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n          <mat-icon>clear</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"chat-messages\">\n      <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n        <div class=\"message-content\">\n          <div class=\"message-wrapper\">\n            <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n            <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Improved loading indicator when waiting for a response -->\n      <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n          <div class=\"typing-text\">AI is thinking...</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"chat-input\">\n      <mat-form-field appearance=\"outline\" class=\"message-field\">\n        <input matInput\n               [(ngModel)]=\"currentMessage\"\n               placeholder=\"Type your message...\"\n               (keydown)=\"onKeyPress($event)\"\n               [disabled]=\"isConnecting\">\n      </mat-form-field>\n      <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n        <mat-icon>send</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Right side: Restaurant summary -->\n  <div class=\"restaurant-summary\">\n    <div class=\"summary-header\">\n      <div class=\"summary-title\">\n        <mat-icon class=\"summary-icon\">summarize</mat-icon>\n        <span>Summary</span>\n      </div>\n    </div>\n\n    <div class=\"summary-content\">\n      <div class=\"summary-section\">\n        <h4>Location</h4>\n        <p *ngIf=\"restaurantSummary.location\">{{ restaurantSummary.location }}</p>\n        <p *ngIf=\"!restaurantSummary.location\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Cuisine Types</h4>\n        <ul *ngIf=\"restaurantSummary.cuisineTypes.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let cuisine of restaurantSummary.cuisineTypes\">{{ cuisine }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.cuisineTypes.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Specialties</h4>\n        <ul *ngIf=\"restaurantSummary.specialties.length > 0\" class=\"compact-list\">\n          <li *ngFor=\"let specialty of restaurantSummary.specialties\">{{ specialty }}</li>\n        </ul>\n        <p *ngIf=\"restaurantSummary.specialties.length === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Menu Info</h4>\n        <table class=\"summary-table\" *ngIf=\"restaurantSummary.menuCount > 0\">\n          <tr>\n            <td>Menu Count:</td>\n            <td>{{ restaurantSummary.menuCount }}</td>\n          </tr>\n          <tr>\n            <td>Categories:</td>\n            <td>{{ restaurantSummary.menuCategories.length }}</td>\n          </tr>\n        </table>\n        <p *ngIf=\"restaurantSummary.menuCount === 0\" class=\"placeholder-text\">Pending</p>\n      </div>\n\n      <div class=\"summary-section\">\n        <h4>Hours</h4>\n        <p *ngIf=\"restaurantSummary.operatingHours\">{{ restaurantSummary.operatingHours }}</p>\n        <p *ngIf=\"!restaurantSummary.operatingHours\" class=\"placeholder-text\">Pending</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,YAAY,QAAQ,2BAA2B;AAKxD,SAAuBC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;ICGjCC,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnHR,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAAS,SAAA,cAAsE;;IACtET,EAAA,CAAAU,UAAA,IAAAC,sCAAA,kBAAmH;IACrHX,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAP,WAAA,CAAAQ,MAAA,aAAAR,WAAA,CAAAQ,MAAA,YAAgG;IAGpJf,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,UAAA,cAAAZ,EAAA,CAAAgB,WAAA,OAAAT,WAAA,CAAAU,IAAA,GAAAjB,EAAA,CAAAkB,cAAA,CAAqC;IAC/BlB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,SAAAL,WAAA,CAAAQ,MAAA,cAAiC;;;;;IAMvEf,EAAA,CAAAC,cAAA,cAAwE;IAEpED,EAAA,CAAAS,SAAA,WAAa;IAGbT,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA+BlDH,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAc,MAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAgC;;;;;IACtErB,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzEH,EAAA,CAAAC,cAAA,SAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAiB,WAAA,CAAa;;;;;IAD1EtB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,UAAA,IAAAa,oCAAA,iBAA6E;IAC/EvB,EAAA,CAAAG,YAAA,EAAK;;;;IADqBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAY,UAAA,YAAAY,MAAA,CAAAJ,iBAAA,CAAAK,YAAA,CAAiC;;;;;IAE3DzB,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAMzFH,EAAA,CAAAC,cAAA,SAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAqB,aAAA,CAAe;;;;;IAD7E1B,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAU,UAAA,IAAAiB,oCAAA,iBAAgF;IAClF3B,EAAA,CAAAG,YAAA,EAAK;;;;IADuBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAR,iBAAA,CAAAS,WAAA,CAAgC;;;;;IAE5D7B,EAAA,CAAAC,cAAA,YAA+E;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAK1FH,EAAA,CAAAC,cAAA,gBAAqE;IAE7DD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJlDH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAW,SAAA,CAAiC;IAIjC/B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAyB,MAAA,CAAAV,iBAAA,CAAAY,cAAA,CAAAC,MAAA,CAA6C;;;;;IAGrDjC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAKjFH,EAAA,CAAAC,cAAA,QAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAA6B,OAAA,CAAAd,iBAAA,CAAAe,cAAA,CAAsC;;;;;IAClFnC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADjFzF,MAqBaiC,gBAAgB;EA4B3BC,YACUC,UAAsB,EACtBC,cAAwC,EACxCC,EAAqB,EACrBC,QAAqB;IAHrB,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IA/BT,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA3B,iBAAiB,GAAsB;MACrCC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAEO,KAAAa,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAE1D;IACQ,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IAChC,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,YAAY,GAAG,KAAK;IAoBpB;IACQ,KAAAC,yBAAyB,GAAG,KAAK;EAdtC;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;MAC3D;MACA,IAAID,OAAO,CAAC,UAAU,CAAC,CAACE,aAAa,KAAKF,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;QAC1E,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC,IAAI,CAACK,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAChB,QAAQ,GAAG,EAAE;IAElB;IACA,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACiB,uBAAuB,EAAE;MAC9B,IAAI,CAACE,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAACX,mBAAmB,GAAGnD,QAAQ,CAAC,IAAI,CAACoD,eAAe,CAAC,CAACW,SAAS,CAAC,MAAK;QACvE,IAAI,CAACD,qBAAqB,EAAE;MAC9B,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACjB,QAAQ,GAAG,CACd;QACEmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrB/C,IAAI,EAAE,2KAA2K;QACjLF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIyD,IAAI;OACpB,CACF;;IAGH;IACA,IAAI,CAACjB,mBAAmB,GAAG,IAAI,CAACV,UAAU,CAAC4B,SAAS,CAACJ,SAAS,CAC3DK,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAACpD,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAIoD,OAAO,CAACJ,EAAE,CAACK,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAACrB,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAIF,OAAO,CAACpD,MAAM,KAAK,KAAK,EAAE;QAC5B;QACA,MAAMuD,oBAAoB,GAAG,IAAI,CAAC1B,QAAQ,CAAC2B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKI,OAAO,CAACJ,EAAE,CAAC;QAE9E,IAAIO,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAAC1B,QAAQ,CAAC0B,oBAAoB,CAAC,GAAGH,OAAO;SAC9C,MAAM;UACL;UACA,MAAMM,gBAAgB,GAAG,IAAI,CAAC7B,QAAQ,CAAC8B,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACzD,MAAM,KAAK,KAAK,IAAIyD,CAAC,CAACvD,IAAI,KAAKkD,OAAO,CAAClD,IAAI,IAAI,CAACuD,CAAC,CAACvD,IAAI,CAAC0D,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED,IAAI,CAACF,gBAAgB,EAAE;YACrB;YACA,IAAI,CAAC7B,QAAQ,CAACgC,IAAI,CAACT,OAAO,CAAC;YAE3B;YACA,IAAI,CAACvB,QAAQ,CAACiC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtE,SAAS,CAACwE,OAAO,EAAE,GAAGD,CAAC,CAACvE,SAAS,CAACwE,OAAO,EAAE,CAAC;;;OAGhF,MAAM,IAAIb,OAAO,CAACpD,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAMkE,WAAW,GAAG,IAAI,CAACrC,QAAQ,CAACsC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACzD,MAAM,KAAK,MAAM,IACnByD,CAAC,CAACvD,IAAI,KAAKkD,OAAO,CAAClD,IAAI,IACvBkE,IAAI,CAACC,GAAG,CAACZ,CAAC,CAAChE,SAAS,CAACwE,OAAO,EAAE,GAAGb,OAAO,CAAC3D,SAAS,CAACwE,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACC,WAAW,EAAE;UAChBI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,OAAO,CAAClD,IAAI,CAAC;UACjD;UACA,IAAI,CAAC2B,QAAQ,CAACgC,IAAI,CAACT,OAAO,CAAC;UAE3B;UACA,IAAI,CAACvB,QAAQ,CAACiC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtE,SAAS,CAACwE,OAAO,EAAE,GAAGD,CAAC,CAACvE,SAAS,CAACwE,OAAO,EAAE,CAAC;SAC5E,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEnB,OAAO,CAAClD,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAACuB,EAAE,CAAC6B,aAAa,EAAE;MAEvB;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB,CAAC,CACF;IAED;EACF;;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACxC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACyC,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACxC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACwC,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAACvC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACuC,WAAW,EAAE;;IAGxC;IACA,IAAI,CAACnD,UAAU,CAACoD,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC9C,cAAc,CAAC+C,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAClD,QAAQ,EAAE;MAClB,IAAI,CAACD,QAAQ,CAACoD,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAChD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAMgD,aAAa,GAAG,IAAI,CAAClD,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA;IAEA;IACA,IAAI,CAAC2B,uBAAuB,CAACD,aAAa,CAAC;IAE3C;IACA,MAAME,WAAW,GAAgB;MAC/BlC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrB/C,IAAI,EAAE8E,aAAa;MACnBhF,MAAM,EAAE,MAAM;MACdP,SAAS,EAAE,IAAIyD,IAAI;KACpB;IAED;IACA,MAAMgB,WAAW,GAAG,IAAI,CAACrC,QAAQ,CAACsC,IAAI,CAACV,CAAC,IACtCA,CAAC,CAACzD,MAAM,KAAK,MAAM,IACnByD,CAAC,CAACvD,IAAI,KAAK8E,aAAa,CACzB;IAED,IAAI,CAACd,WAAW,EAAE;MAChB,IAAI,CAACrC,QAAQ,CAACgC,IAAI,CAACqB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAClD,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;IACvB,IAAI,CAACkB,cAAc,EAAE;IAErB;IACA,IAAI,CAACjD,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACjD,QAAQ,EAAEqD,aAAa,CAAC,CAACjC,SAAS,CAAC;MAClEoC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAACpD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACrD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAACoD,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACtD,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIQ2B,uBAAuBA,CAAC7B,OAAe;IAC7C,MAAMiC,YAAY,GAAGjC,OAAO,CAACkC,WAAW,EAAE;IAE1C;IACA,IAAID,YAAY,CAACzB,QAAQ,CAAC,UAAU,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IACrEyB,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IACjEyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,IAAI,CAACvD,iBAAiB,CAACC,QAAQ,GAAG,IAAI,CAACiF,kBAAkB,CAACF,YAAY,CAAC;;IAGzE;IACA,IAAIA,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACrE,MAAM4B,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACJ,YAAY,CAAC;MACpD,IAAIG,QAAQ,CAACtE,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACb,iBAAiB,CAACK,YAAY,GAAG,CAAC,GAAG,IAAIgF,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrF,iBAAiB,CAACK,YAAY,EAAE,GAAG8E,QAAQ,CAAC,CAAC,CAAC;;;IAI7G;IACA,IAAIH,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,IACtEyB,YAAY,CAACzB,QAAQ,CAAC,YAAY,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC7E,MAAM9C,WAAW,GAAG,IAAI,CAAC2E,gBAAgB,CAACJ,YAAY,CAAC;MACvD,IAAIvE,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACb,iBAAiB,CAACS,WAAW,GAAG,CAAC,GAAG,IAAI4E,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrF,iBAAiB,CAACS,WAAW,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAC;;;IAI9G;IACA,IAAIuE,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MAClE;MACA,MAAM+B,cAAc,GAAGN,YAAY,CAACO,KAAK,CAAC,gCAAgC,CAAC;MAC3E,IAAID,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAE;QACvC,IAAI,CAACtF,iBAAiB,CAACW,SAAS,GAAG6E,QAAQ,CAACF,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAGpE;MACA,IAAIN,YAAY,CAACzB,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpC,MAAMkC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACJ,YAAY,CAAC;QACtD,IAAIS,UAAU,CAAC5E,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACb,iBAAiB,CAACY,cAAc,GAAG,CAAC,GAAG,IAAIyE,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrF,iBAAiB,CAACY,cAAc,EAAE,GAAG6E,UAAU,CAAC,CAAC,CAAC;;;;IAKrH;IACA,IAAIT,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAC9DyB,YAAY,CAACzB,QAAQ,CAAC,OAAO,CAAC,IAAIyB,YAAY,CAACzB,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnE,IAAI,CAACvD,iBAAiB,CAACe,cAAc,GAAG,IAAI,CAACmE,kBAAkB,CAACF,YAAY,CAAC;;IAG/E,IAAI,CAAC5D,EAAE,CAAC6B,aAAa,EAAE;EACzB;EAEA;;;;EAIQiC,kBAAkBA,CAACnC,OAAe;IACxC;IACA,MAAM2C,cAAc,GAAG3C,OAAO,CAC3B4C,OAAO,CAAC,iFAAiF,EAAE,EAAE,CAAC,CAC9FA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAErB;IACA,OAAOD,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC;EACzE;EAEA;;;;EAIQV,gBAAgBA,CAACrC,OAAe;IACtC;IACA,IAAIA,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,IAAIR,OAAO,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,OAAOR,OAAO,CACXgD,KAAK,CAAC,WAAW,CAAC,CAClBC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzB,IAAI,EAAE,CAAC,CACxB0B,MAAM,CAACD,IAAI,IAAIA,IAAI,CAACpF,MAAM,GAAG,CAAC,CAAC,CAC/BmF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG9D;IACA,OAAO,CAAC,IAAI,CAACZ,kBAAkB,CAACnC,OAAO,CAAC,CAAC;EAC3C;EAEA;;;;EAIAoD,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAAChC,WAAW,EAAE;;EAEtB;EAEA;;;EAGQJ,cAAcA,CAAA;IACpB;IACAqC,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQjE,UAAUA,CAAA;IAChB,OAAOmB,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGjD,IAAI,CAAC+C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAC,SAASA,CAACC,MAAc,EAAEnE,OAAoB;IAC5C,OAAOA,OAAO,CAACJ,EAAE;EACnB;EAEA;;;EAGAJ,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACjB,QAAQ,IAAI,IAAI,CAACY,yBAAyB,EAAE;MACpD;;IAGF;IACA,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAACR,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACR,UAAU,CAACqB,uBAAuB,CAAC,IAAI,CAACjB,QAAQ,EAAE,KAAK,CAAC,CAACoB,SAAS,CAAC;MACtEoC,IAAI,EAAGtD,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,IAAI,CAACW,QAAQ,GAAG,EAAE;UAElB;UACAA,QAAQ,CAAC2F,OAAO,CAACpE,OAAO,IAAG;YACzB;YACA,MAAMqE,eAAe,GAAG,IAAI,CAAC5F,QAAQ,CAAC8B,IAAI,CAACF,CAAC,IAC1CA,CAAC,CAACzD,MAAM,KAAKoD,OAAO,CAACpD,MAAM,IAAIyD,CAAC,CAACvD,IAAI,KAAKkD,OAAO,CAAClD,IAAI,CACvD;YAED,IAAI,CAACuH,eAAe,EAAE;cACpB,IAAI,CAAC5F,QAAQ,CAACgC,IAAI,CAACT,OAAO,CAAC;;YAG7B;YACA,IAAIA,OAAO,CAACpD,MAAM,KAAK,MAAM,EAAE;cAC7B,IAAI,CAACiF,uBAAuB,CAAC7B,OAAO,CAAClD,IAAI,CAAC;;UAE9C,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAAC2B,QAAQ,CAACX,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACW,QAAQ,CAACgC,IAAI,CAAC;cACjBb,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;cACrB/C,IAAI,EAAE,2KAA2K;cACjLF,MAAM,EAAE,KAAK;cACbP,SAAS,EAAE,IAAIyD,IAAI;aACpB,CAAC;;SAEL,MAAM;UACL;UACA,IAAI,CAACrB,QAAQ,GAAG,CAAC;YACfmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrB/C,IAAI,EAAE,2KAA2K;YACjLF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIyD,IAAI;WACpB,CAAC;;QAGJ,IAAI,CAACnB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACkB,cAAc,EAAE;MACvB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACrD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACH,QAAQ,GAAG,CAAC;UACfmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;UACrB/C,IAAI,EAAE,2KAA2K;UACjLF,MAAM,EAAE,KAAK;UACbP,SAAS,EAAE,IAAIyD,IAAI;SACpB,CAAC;QAEF,IAAI,CAACzB,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAoE,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAAC3F,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA,IAAI,CAACjD,iBAAiB,GAAG;MACvBC,QAAQ,EAAE,EAAE;MACZI,YAAY,EAAE,EAAE;MAChBI,WAAW,EAAE,EAAE;MACfE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBG,cAAc,EAAE;KACjB;IAED;IACA,IAAI,IAAI,CAACO,QAAQ,EAAE;MACjB,IAAI,CAACJ,UAAU,CAACmG,wBAAwB,CAAC,IAAI,CAAC/F,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAACoB,SAAS,CAAC;QAC5EoC,IAAI,EAAEA,CAAA,KAAK;UACTb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAAC1C,QAAQ,GAAG,CACd;YACEmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrB/C,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIyD,IAAI;WACpB,CACF;UAED;UACA,IAAI,CAACX,yBAAyB,GAAG,KAAK;UAEtC,IAAI,CAACR,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAACvD,QAAQ,GAAG,CACd;YACEmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;YACrB/C,IAAI,EAAE,0DAA0D;YAChEF,MAAM,EAAE,KAAK;YACbP,SAAS,EAAE,IAAIyD,IAAI;WACpB,CACF;UAED,IAAI,CAACX,yBAAyB,GAAG,KAAK;UACtC,IAAI,CAACR,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACkB,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC3C,QAAQ,GAAG,CACd;QACEmB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrB/C,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbP,SAAS,EAAE,IAAIyD,IAAI;OACpB,CACF;MAED,IAAI,CAACX,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACR,YAAY,GAAG,KAAK;MACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;MACvB,IAAI,CAACkB,cAAc,EAAE;;EAEzB;EAEA;EAEA;;;EAGA1B,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACnB,QAAQ,EAAE;IAEpB,IAAI,CAACU,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACb,EAAE,CAAC6B,aAAa,EAAE;IAEvB,IAAI,CAAC9B,cAAc,CAACmG,oBAAoB,CAAC,IAAI,CAAChG,QAAQ,CAAC,CAACoB,SAAS,CAAC;MAChEoC,IAAI,EAAGyC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,OAAO,EAAE;UACxC;UACA,MAAMA,OAAO,GAAGF,QAAQ,CAACE,OAAO;UAEhC;UACA,IAAI,CAACzH,iBAAiB,GAAG;YACvBC,QAAQ,EAAE,IAAI,CAACyH,sBAAsB,CAACD,OAAO,CAACE,OAAO,CAAC;YACtDtH,YAAY,EAAE,CAAC,GAAGoH,OAAO,CAACG,eAAe,EAAE,GAAG,IAAI,CAACC,sBAAsB,CAACJ,OAAO,CAACE,OAAO,CAAC,CAAC;YAC3FlH,WAAW,EAAE,IAAI,CAACqH,uBAAuB,CAACL,OAAO,CAACE,OAAO,CAAC;YAC1DhH,SAAS,EAAE8G,OAAO,CAACM,aAAa;YAChCnH,cAAc,EAAE6G,OAAO,CAACE,OAAO,CAAC3B,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC;YAChDlH,cAAc,EAAE,iBAAiB,IAAI8B,IAAI,CAAC4E,OAAO,CAACS,YAAY,CAAC,CAACC,kBAAkB,EAAE;WACrF;UAED,IAAI,CAACnG,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACZ,EAAE,CAAC6B,aAAa,EAAE;;MAE3B,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,YAAY,GAAG,IAAI;QACxB,IAAI,CAACb,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGQyE,sBAAsBA,CAACC,OAAc;IAC3C,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC9G,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAC/C,OAAO8G,OAAO,CAAC3B,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACI,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAC/C;EAEA;;;EAGQR,sBAAsBA,CAACF,OAAc;IAC3C,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC9G,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE/C,MAAMsE,QAAQ,GAAG,IAAIE,GAAG,EAAU;IAClCsC,OAAO,CAACR,OAAO,CAACmB,MAAM,IAAG;MACvB,IAAIA,MAAM,CAACnD,QAAQ,IAAIoD,KAAK,CAACC,OAAO,CAACF,MAAM,CAACnD,QAAQ,CAAC,EAAE;QACrDmD,MAAM,CAACnD,QAAQ,CAACgC,OAAO,CAACsB,OAAO,IAAItD,QAAQ,CAACuD,GAAG,CAACD,OAAO,CAAC,CAAC;;IAE7D,CAAC,CAAC;IAEF,OAAOF,KAAK,CAACI,IAAI,CAACxD,QAAQ,CAAC;EAC7B;EAEA;;;EAGQ2C,uBAAuBA,CAACH,OAAc;IAC5C,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC9G,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE/C,MAAM+H,SAAS,GAAG,IAAIvD,GAAG,EAAU;IACnCsC,OAAO,CAACR,OAAO,CAACmB,MAAM,IAAG;MACvB,IAAIA,MAAM,CAACO,UAAU,IAAIN,KAAK,CAACC,OAAO,CAACF,MAAM,CAACO,UAAU,CAAC,EAAE;QACzDP,MAAM,CAACO,UAAU,CAAC1B,OAAO,CAAC2B,IAAI,IAAIF,SAAS,CAACF,GAAG,CAACI,IAAI,CAAC,CAAC;;IAE1D,CAAC,CAAC;IAEF,OAAOP,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC;EAC9B;;;uBA5lBW5H,gBAAgB,EAAApC,EAAA,CAAAmK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAArK,EAAA,CAAAmK,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAAvK,EAAA,CAAAmK,iBAAA,CAAAnK,EAAA,CAAAwK,iBAAA,GAAAxK,EAAA,CAAAmK,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBtI,gBAAgB;MAAAuI,SAAA;MAAAC,MAAA;QAAAlI,QAAA;QAAAC,UAAA;MAAA;MAAAkI,UAAA;MAAAC,QAAA,GAAA9K,EAAA,CAAA+K,oBAAA,EAAA/K,EAAA,CAAAgL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxC7BtL,EAAA,CAAAC,cAAA,aAAyB;UAKWD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UACwBD,EAAA,CAAAwL,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAA9C,wBAAA,EAA0B;UAAA,EAAC;UAClFzI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKhCH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAU,UAAA,KAAAgL,gCAAA,iBAOM;UAGN1L,EAAA,CAAAU,UAAA,KAAAiL,gCAAA,kBAOM;UACR3L,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAwL,UAAA,2BAAAI,0DAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA1I,cAAA,GAAAgJ,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBN,GAAA,CAAAhE,UAAA,CAAAsE,MAAA,CAAkB;UAAA,EAFD;UADnC7L,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAwL,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAA5F,WAAA,EAAa;UAAA,EAAC;UAC1D3F,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM/BH,EAAA,CAAAC,cAAA,eAAgC;UAGKD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIxBH,EAAA,CAAAC,cAAA,eAA6B;UAErBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAU,UAAA,KAAAsL,8BAAA,gBAA0E;UAC1EhM,EAAA,CAAAU,UAAA,KAAAuL,8BAAA,gBAA2E;UAC7EjM,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAU,UAAA,KAAAwL,+BAAA,iBAEK;UACLlM,EAAA,CAAAU,UAAA,KAAAyL,8BAAA,gBAA2F;UAC7FnM,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAU,UAAA,KAAA0L,+BAAA,iBAEK;UACLpM,EAAA,CAAAU,UAAA,KAAA2L,8BAAA,gBAA0F;UAC5FrM,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAU,UAAA,KAAA4L,kCAAA,qBASQ;UACRtM,EAAA,CAAAU,UAAA,KAAA6L,8BAAA,gBAAiF;UACnFvM,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA6B;UACvBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAU,UAAA,KAAA8L,8BAAA,gBAAsF;UACtFxM,EAAA,CAAAU,UAAA,KAAA+L,8BAAA,gBAAiF;UACnFzM,EAAA,CAAAG,YAAA,EAAM;;;UArFmBH,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAY,UAAA,YAAA2K,GAAA,CAAA3I,QAAA,CAAa,iBAAA2I,GAAA,CAAAlD,SAAA;UAUhCrI,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAxI,oBAAA,CAA0B;UAavB/C,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAY,UAAA,YAAA2K,GAAA,CAAA1I,cAAA,CAA4B,aAAA0I,GAAA,CAAAzI,YAAA;UAKwB9C,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAY,UAAA,cAAA2K,GAAA,CAAA1I,cAAA,CAAA+C,IAAA,MAAA2F,GAAA,CAAAzI,YAAA,CAAmD;UAkB1G9C,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAC,QAAA,CAAgC;UAChCrB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAY,UAAA,UAAA2K,GAAA,CAAAnK,iBAAA,CAAAC,QAAA,CAAiC;UAKhCrB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,KAA+C;UAGhDjC,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAK,YAAA,CAAAQ,MAAA,OAAiD;UAKhDjC,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAS,WAAA,CAAAI,MAAA,KAA8C;UAG/CjC,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAS,WAAA,CAAAI,MAAA,OAAgD;UAKtBjC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAW,SAAA,KAAqC;UAU/D/B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAW,SAAA,OAAuC;UAKvC/B,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAY,UAAA,SAAA2K,GAAA,CAAAnK,iBAAA,CAAAe,cAAA,CAAsC;UACtCnC,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAY,UAAA,UAAA2K,GAAA,CAAAnK,iBAAA,CAAAe,cAAA,CAAuC;;;qBD7E/C/C,YAAY,EAAAsN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZzN,WAAW,EAAA0N,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX5N,mBAAmB,EACnBC,eAAe,EAAA4N,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACf7N,aAAa,EACbC,kBAAkB,EAAA6N,EAAA,CAAAC,YAAA,EAClB7N,aAAa,EAAA8N,EAAA,CAAAC,OAAA,EACb9N,cAAc,EAAA+N,EAAA,CAAAC,QAAA,EACd/N,wBAAwB,EACxBC,gBAAgB,EAAA+N,GAAA,CAAAC,UAAA,EAEhB/N,YAAY;MAAAgO,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAMH3L,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}