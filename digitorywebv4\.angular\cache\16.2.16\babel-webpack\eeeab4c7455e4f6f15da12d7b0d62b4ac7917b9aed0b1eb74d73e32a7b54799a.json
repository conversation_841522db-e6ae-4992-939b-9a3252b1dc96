{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/sse.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/tooltip\";\nfunction ChatBotComponent_div_2_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Start\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Click to Resume\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startConversation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"mat-icon\", 29);\n    i0.ɵɵtext(3, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Restaurant Onboarding Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"I'll help you collect information about your restaurant outlets, cuisines, and more.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 30)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ChatBotComponent_div_2_span_11_Template, 2, 0, \"span\", 26);\n    i0.ɵɵtemplate(12, ChatBotComponent_div_2_span_12_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n  }\n}\nfunction ChatBotComponent_div_17_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, message_r9.timestamp, \"shortTime\"));\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatBotComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33);\n    i0.ɵɵelement(3, \"div\", 34);\n    i0.ɵɵpipe(4, \"markdown\");\n    i0.ɵɵtemplate(5, ChatBotComponent_div_17_div_5_Template, 3, 4, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, message_r9.sender === \"user\", message_r9.sender === \"bot\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 3, message_r9.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r9.sender !== \"system\");\n  }\n}\nfunction ChatBotComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38)(2, \"div\", 39);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 40);\n    i0.ɵɵtext(7, \"AI is thinking...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatBotComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Restaurant Data Yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"As you provide information about your restaurant through the chat, it will appear here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ChatBotComponent_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.refreshRestaurantData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Force Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.isRefreshing);\n  }\n}\nfunction ChatBotComponent_ng_container_44_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.restaurantData.commonCuisinesAcrossOutlets.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_44_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_44_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.restaurantData.signatureElements.signatureDishes.join(\", \"), \" \");\n  }\n}\nfunction ChatBotComponent_ng_container_44_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_44_div_33_span_14_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_44_div_33_span_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ChatBotComponent_ng_container_44_div_33_span_14_span_1_span_2_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r24 = ctx.$implicit;\n    const last_r25 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", area_r24, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r25);\n  }\n}\nfunction ChatBotComponent_ng_container_44_div_33_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, ChatBotComponent_ng_container_44_div_33_span_14_span_1_Template, 3, 2, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const outlet_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", outlet_r19.outletWorkAreas);\n  }\n}\nfunction ChatBotComponent_ng_container_44_div_33_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" None specified \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatBotComponent_ng_container_44_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"h3\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"span\", 45);\n    i0.ɵɵtext(7, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 46);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"span\", 45);\n    i0.ɵɵtext(12, \"Work Areas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 46);\n    i0.ɵɵtemplate(14, ChatBotComponent_ng_container_44_div_33_span_14_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_44_div_33_span_15_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const outlet_r19 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" Outlet \", i_r20 + 1, \": \", outlet_r19.outletName, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(outlet_r19.outletAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", outlet_r19.outletWorkAreas && outlet_r19.outletWorkAreas.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !outlet_r19.outletWorkAreas || outlet_r19.outletWorkAreas.length === 0);\n  }\n}\nfunction ChatBotComponent_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"h3\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurant Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 44)(7, \"span\", 45);\n    i0.ɵɵtext(8, \"Total Outlets:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 46);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"span\", 45);\n    i0.ɵɵtext(13, \"Cuisines:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 46);\n    i0.ɵɵtemplate(15, ChatBotComponent_ng_container_44_span_15_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(16, ChatBotComponent_ng_container_44_span_16_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"span\", 45);\n    i0.ɵɵtext(19, \"Signature Dishes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 46);\n    i0.ɵɵtemplate(21, ChatBotComponent_ng_container_44_span_21_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(22, ChatBotComponent_ng_container_44_span_22_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 44)(24, \"span\", 45);\n    i0.ɵɵtext(25, \"Alcohol Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 46);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 44)(29, \"span\", 45);\n    i0.ɵɵtext(30, \"Tobacco Service:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 46);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ChatBotComponent_ng_container_44_div_33_Template, 16, 5, \"div\", 47);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.restaurantData.totalOutlets);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.commonCuisinesAcrossOutlets && ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.commonCuisinesAcrossOutlets || ctx_r4.restaurantData.commonCuisinesAcrossOutlets.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.restaurantData.signatureElements && ctx_r4.restaurantData.signatureElements.signatureDishes && ctx_r4.restaurantData.signatureElements.signatureDishes.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.restaurantData.signatureElements || !ctx_r4.restaurantData.signatureElements.signatureDishes || ctx_r4.restaurantData.signatureElements.signatureDishes.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.beverageInfo == null ? null : ctx_r4.restaurantData.beverageInfo.alcoholService) || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.restaurantData.tobaccoInfo == null ? null : ctx_r4.restaurantData.tobaccoInfo.tobaccoService) || \"Not specified\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.restaurantData.outletDetails);\n  }\n}\nclass ChatBotComponent {\n  constructor(sseService, cd, snackBar, dialog) {\n    this.sseService = sseService;\n    this.cd = cd;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.tenantId = '';\n    this.tenantName = '';\n    this.messages = [];\n    this.currentMessage = '';\n    this.isConnecting = false;\n    this.isWaitingForResponse = false;\n    this.restaurantData = null;\n    this.conversationStarted = false;\n    this.isRefreshing = false;\n    this.messageSubscription = null;\n    this.connectionSubscription = null;\n    this.dataUpdateSubscription = null;\n    // Flag to track if we're in the process of loading conversation history\n    this.loadingHistory = false;\n  }\n  ngOnChanges(changes) {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n  ngOnInit() {\n    // Initialize with an empty messages array\n    this.messages = [];\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n      // Reset the flag to ensure history is loaded\n      this.loadingHistory = false;\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n      // Load conversation history\n      this.loadConversationHistory();\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        console.log('Checking messages after timeout:', this.messages.length);\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n          console.log('Conversation marked as started due to existing messages');\n        } else if (this.conversationStarted) {\n          // If conversation was started but no messages, initiate conversation\n          console.log('Conversation was started but no messages found, initiating conversation');\n          this.initiateConversation();\n        }\n        // Force UI update\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000); // Longer delay to ensure history is fully loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(message => {\n      // Handle special system messages\n      if (message.sender === 'system') {\n        // This is a completion message, hide the loading indicator\n        if (message.id.startsWith('completion-')) {\n          this.isWaitingForResponse = false;\n          this.cd.detectChanges();\n          return;\n        }\n        // Ignore other system messages\n        return;\n      }\n      // For streaming responses, we need to handle bot messages differently\n      if (message.sender === 'bot') {\n        console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n        // Skip empty messages\n        if (!message.text.trim()) {\n          console.log('Skipping empty bot message');\n          return;\n        }\n        // Check if this is a streaming update to an existing message\n        const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n        if (existingMessageIndex !== -1) {\n          // Only update if the new message is not \"AI is thinking...\"\n          if (message.text !== 'AI is thinking...') {\n            // Update existing message\n            this.messages[existingMessageIndex] = message;\n            console.log('Updated existing message');\n          }\n        } else {\n          // Check if this message already exists in the conversation\n          const duplicateMessage = this.messages.find(m => m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor'));\n          // Replace \"AI is thinking...\" with actual content\n          const thinkingIndex = this.messages.findIndex(m => m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id);\n          if (thinkingIndex !== -1) {\n            // Replace the thinking message with the actual content\n            this.messages[thinkingIndex] = message;\n            console.log('Replaced thinking message with actual content');\n          } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n            // Add new bot message (but not \"AI is thinking...\" messages)\n            this.messages.push(message);\n            console.log('Added new bot message');\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          }\n        }\n        // If this is a complete message (no blinking cursor), mark response as complete\n        if (!message.text.includes('blinking-cursor')) {\n          this.isWaitingForResponse = false;\n        }\n        // Don't show the \"AI is thinking...\" message if there's no user message before it\n        if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n          const lastMessage = this.messages[this.messages.length - 1];\n          if (lastMessage.sender !== 'user') {\n            // Skip this message\n            console.log('Skipping \"AI is thinking\" message because there is no user message before it');\n            return;\n          }\n        }\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      } else if (message.sender === 'user') {\n        // For user messages, add them if they don't exist already\n        // Use a more reliable way to check for duplicates\n        const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === message.text && Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n        );\n\n        if (!isDuplicate) {\n          console.log('Adding user message:', message.text);\n          // Add user message to the messages array\n          this.messages.push(message);\n          // Sort messages by timestamp to ensure correct order\n          this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n        } else {\n          console.log('Duplicate user message detected, not adding:', message.text);\n        }\n      }\n      // Force change detection to update the UI immediately\n      this.cd.detectChanges();\n      // Scroll to bottom to show latest message\n      this.scrollToBottom();\n    });\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(data => {\n      console.log('Received data update:', data);\n      if (data && data.type === 'restaurant_data') {\n        if (data.data) {\n          console.log('Setting restaurant data:', data.data);\n          this.restaurantData = data.data;\n          console.log('Restaurant data received, restaurantData:', this.restaurantData);\n          console.log('Restaurant data keys:', Object.keys(this.restaurantData));\n          // Check if the data has the expected structure\n          if (this.restaurantData.outletDetails) {\n            console.log('Outlet details found:', this.restaurantData.outletDetails);\n          } else {\n            console.warn('No outlet details found in restaurant data');\n          }\n        } else {\n          console.warn('Received null restaurant data, clearing restaurantData');\n          this.restaurantData = null;\n        }\n        // Force change detection\n        setTimeout(() => {\n          this.cd.detectChanges();\n          console.log('Change detection triggered after restaurant data update');\n          console.log('Restaurant data after change detection:', this.restaurantData);\n        }, 0);\n        // The backend will automatically send the next question\n        // No need to request it from the frontend\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n  // We don't need a separate connect method anymore as we'll connect on demand\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage() {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n    // No need to update restaurant summary\n    // Add the user message directly to the messages array\n    const userMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m => m.sender === 'user' && m.text === messageToSend);\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n        // The backend will automatically handle the conversation flow\n        // No need to do anything special after sending a message\n      },\n\n      error: error => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n  // Restaurant summary methods removed\n  // Removed unused methods for extracting information from messages\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  /**\n   * Scroll the chat container to the bottom\n   */\n  scrollToBottom() {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n  /**\n   * Generate a random ID for messages\n   */\n  generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index, message) {\n    return message.id;\n  }\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory() {\n    if (!this.tenantId || this.loadingHistory) {\n      console.log('Not loading conversation history: no tenantId or already loading');\n      return;\n    }\n    console.log('Loading conversation history for tenant:', this.tenantId);\n    // Set the flag to prevent duplicate API calls\n    this.loadingHistory = true;\n    // Show loading state\n    this.isConnecting = true;\n    // Clear existing messages to avoid duplicates\n    this.messages = [];\n    // Load conversation history from the server\n    // Set addToStream to false so we can handle the messages ourselves\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: messages => {\n        console.log('Received history messages:', messages.length);\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Sort messages by timestamp to ensure correct order\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n          console.log('Sorted messages by timestamp');\n          // Add all messages to our local array without filtering\n          this.messages = messages;\n          console.log('Added all messages to local array, count:', this.messages.length);\n          // Mark conversation as started since we have history\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          // If no messages, initialize with empty array\n          this.messages = [];\n          console.log('No history found, initialized with empty array');\n        }\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        // If error, initialize with empty array\n        this.messages = [];\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory() {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n    // No need to reset restaurant summary\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n          // Reset to empty array\n          this.messages = [];\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          // Reset restaurant data\n          this.restaurantData = null;\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error clearing conversation history:', error);\n          // Still reset the UI even if the server call fails\n          this.messages = [];\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n          // Reset restaurant data\n          this.restaurantData = null;\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [{\n        id: this.generateId(),\n        text: 'Conversation has been cleared. How can I help you today?',\n        sender: 'bot',\n        timestamp: new Date()\n      }];\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n  // ngOnDestroy is already defined above\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation() {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    // Mark conversation as started\n    this.conversationStarted = true;\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n    // The backend will send the welcome message and start the conversation\n    // Just initiate the conversation with the backend\n    this.initiateConversation();\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n  /**\n   * Initiate or continue the conversation with the backend\n   * This simply sends a signal to the backend to continue the conversation flow\n   */\n  initiateConversation() {\n    if (!this.tenantId) {\n      return;\n    }\n    console.log('Initiating conversation with backend for tenant:', this.tenantId);\n    // Send a special message to the backend to continue the conversation\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n        console.log('Continue conversation signal sent successfully');\n        // The response will come through the normal message subscription\n      },\n\n      error: error => {\n        console.error('Error initiating conversation:', error);\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Removed unused methods for checking data completeness and updating questions\n  /**\n   * Debug function to log the current messages\n   */\n  debugMessages() {\n    console.log('Current messages count:', this.messages.length);\n    this.messages.forEach((msg, index) => {\n      console.log(`Message ${index}:`, msg.id, msg.sender, msg.text.substring(0, 30) + '...');\n    });\n    // Force reload of conversation history\n    this.loadingHistory = false;\n    this.loadConversationHistory();\n    // Check restaurant data\n    console.log('Current restaurant data:', this.restaurantData);\n    // Create test restaurant data if none exists\n    if (!this.restaurantData && this.tenantId) {\n      this.createTestRestaurantData();\n    }\n  }\n  /**\n   * Create test restaurant data for debugging\n   */\n  createTestRestaurantData() {\n    if (!this.tenantId) {\n      console.warn('Cannot create test data: No tenant ID');\n      return;\n    }\n    console.log('Creating test restaurant data for tenant:', this.tenantId);\n    const testData = {\n      totalOutlets: 1,\n      outletDetails: [{\n        outletName: 'Test Restaurant',\n        outletAddress: '123 Test Street, Test City',\n        outletWorkAreas: ['Kitchen', 'Bar', 'Dining area']\n      }],\n      commonCuisinesAcrossOutlets: ['Italian', 'Chinese'],\n      signatureElements: {\n        signatureDishes: ['Test Dish 1', 'Test Dish 2']\n      },\n      beverageInfo: {\n        alcoholService: 'Full bar'\n      },\n      tobaccoInfo: {\n        tobaccoService: 'Yes'\n      }\n    };\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    this.sseService.saveRestaurantData(this.tenantId, testData).subscribe({\n      next: response => {\n        console.log('Test restaurant data saved:', response);\n        if (response) {\n          this.restaurantData = testData;\n          this.snackBar.open('Test restaurant data created successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('Failed to create test restaurant data', 'Close', {\n            duration: 3000,\n            panelClass: 'error-snackbar'\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error creating test restaurant data:', error);\n        this.snackBar.open('Failed to create test restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.createTestRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Refresh restaurant data from the backend\n   */\n  refreshRestaurantData() {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: data => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: error => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Save restaurant data to the backend\n   */\n  saveRestaurantData() {\n    if (!this.tenantId || !this.restaurantData || this.isRefreshing) {\n      return;\n    }\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n    console.log('Saving restaurant data for tenant:', this.tenantId);\n    this.sseService.saveRestaurantData(this.tenantId, this.restaurantData).subscribe({\n      next: response => {\n        console.log('Restaurant data saved:', response);\n        if (response) {\n          this.snackBar.open('Restaurant data saved successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('Failed to save restaurant data', 'Close', {\n            duration: 3000,\n            panelClass: 'error-snackbar'\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error saving restaurant data:', error);\n        this.snackBar.open('Failed to save restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.saveRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  /**\n   * Delete restaurant data from the backend\n   */\n  deleteRestaurantData() {\n    if (!this.tenantId || this.isRefreshing || !this.restaurantData) {\n      return;\n    }\n    // Show confirmation dialog\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      width: '350px',\n      data: {\n        title: 'Delete Restaurant Data',\n        message: 'Are you sure you want to delete all restaurant data? This action cannot be undone.',\n        confirmButtonText: 'Delete',\n        cancelButtonText: 'Cancel'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // User confirmed deletion\n        this.isRefreshing = true;\n        this.cd.detectChanges();\n        console.log('Deleting restaurant data for tenant:', this.tenantId);\n        this.sseService.deleteRestaurantData(this.tenantId).subscribe({\n          next: response => {\n            console.log('Restaurant data deleted:', response);\n            if (response) {\n              this.restaurantData = null;\n              this.snackBar.open('Restaurant data deleted successfully', 'Close', {\n                duration: 3000,\n                panelClass: 'success-snackbar'\n              });\n            } else {\n              this.snackBar.open('Failed to delete restaurant data', 'Close', {\n                duration: 3000,\n                panelClass: 'error-snackbar'\n              });\n            }\n            this.isRefreshing = false;\n            this.cd.detectChanges();\n          },\n          error: error => {\n            console.error('Error deleting restaurant data:', error);\n            this.snackBar.open('Failed to delete restaurant data', 'Retry', {\n              duration: 3000,\n              panelClass: 'error-snackbar'\n            }).onAction().subscribe(() => {\n              this.deleteRestaurantData();\n            });\n            this.isRefreshing = false;\n            this.cd.detectChanges();\n          }\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ChatBotComponent_Factory(t) {\n      return new (t || ChatBotComponent)(i0.ɵɵdirectiveInject(i1.SseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatBotComponent,\n      selectors: [[\"app-chat-bot\"]],\n      inputs: {\n        tenantId: \"tenantId\",\n        tenantName: \"tenantName\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 45,\n      vars: 14,\n      consts: [[1, \"chat-layout\"], [1, \"chat-container\"], [\"class\", \"chat-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"assistant-title\"], [1, \"chat-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Clear Chat\", 1, \"clear-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Debug Messages\", 1, \"debug-btn\", 3, \"click\"], [1, \"chat-messages\"], [\"class\", \"message-container\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-container bot-message\", 4, \"ngIf\"], [1, \"chat-input\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"placeholder\", \"Type your message...\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"restaurant-data-panel\"], [1, \"panel-header\"], [1, \"header-left\"], [1, \"header-right\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Save restaurant data\", 1, \"action-button\", \"save-button\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Refresh restaurant data\", 1, \"action-button\", \"refresh-button\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete restaurant data\", 1, \"action-button\", \"delete-button\", 3, \"disabled\", \"click\"], [1, \"panel-content\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"chat-overlay\", 3, \"click\"], [1, \"overlay-content\"], [1, \"overlay-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"start-button\"], [1, \"message-container\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-wrapper\"], [1, \"message-text\", 3, \"innerHTML\"], [\"class\", \"message-timestamp\", 4, \"ngIf\"], [1, \"message-timestamp\"], [1, \"message-container\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-text\"], [1, \"no-data-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"data-section\", \"summary-section\"], [1, \"data-item\"], [1, \"label\"], [1, \"value\"], [\"class\", \"data-section outlet-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"data-section\", \"outlet-section\"], [\"class\", \"work-area-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"work-area-tag\"]],\n      template: function ChatBotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ChatBotComponent_div_2_Template, 13, 2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Restaurant details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_10_listener() {\n            return ctx.clearConversationHistory();\n          });\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"clear\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_13_listener() {\n            return ctx.debugMessages();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"bug_report\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 10);\n          i0.ɵɵtemplate(17, ChatBotComponent_div_17_Template, 6, 8, \"div\", 11);\n          i0.ɵɵtemplate(18, ChatBotComponent_div_18_Template, 8, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"mat-form-field\", 14)(21, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatBotComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.currentMessage = $event;\n          })(\"keydown\", function ChatBotComponent_Template_input_keydown_21_listener($event) {\n            return ctx.onKeyPress($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_22_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"send\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18)(27, \"div\", 19)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"restaurant_menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"h2\");\n          i0.ɵɵtext(31, \"Restaurant Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 20)(33, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_33_listener() {\n            return ctx.saveRestaurantData();\n          });\n          i0.ɵɵelementStart(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"save\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_36_listener() {\n            return ctx.refreshRestaurantData();\n          });\n          i0.ɵɵelementStart(37, \"mat-icon\");\n          i0.ɵɵtext(38, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ChatBotComponent_Template_button_click_39_listener() {\n            return ctx.deleteRestaurantData();\n          });\n          i0.ɵɵelementStart(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"delete\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 24);\n          i0.ɵɵtemplate(43, ChatBotComponent_div_43_Template, 11, 1, \"div\", 25);\n          i0.ɵɵtemplate(44, ChatBotComponent_ng_container_44_Template, 34, 8, \"ng-container\", 26);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.conversationStarted);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWaitingForResponse);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.currentMessage)(\"disabled\", ctx.isConnecting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.currentMessage.trim() || ctx.isConnecting);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing || !ctx.restaurantData);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"rotating\", ctx.isRefreshing);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isRefreshing || !ctx.restaurantData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.restaurantData);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.restaurantData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, ReactiveFormsModule, MatButtonModule, i6.MatButton, i6.MatIconButton, i6.MatMiniFabButton, MatCardModule, MatFormFieldModule, i7.MatFormField, MatIconModule, i8.MatIcon, MatInputModule, i9.MatInput, MatProgressSpinnerModule, MatTooltipModule, i10.MatTooltip, MatDialogModule, MarkdownPipe],\n      styles: [\"\\n\\n.chat-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  height: 100%;\\n  gap: 20px;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 0.6; \\n\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  border: 1px solid #e0e0e0;\\n  position: relative; \\n\\n}\\n\\n.restaurant-data-panel[_ngcontent-%COMP%] {\\n  flex: 0.4; \\n\\n  width: auto; \\n\\n  height: 100%;\\n  min-height: 400px;\\n  max-height: 600px;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\\n  background-color: #fff;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.chat-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  max-width: 80%;\\n}\\n\\n.overlay-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f57c00; \\n\\n  margin-bottom: 1rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-size: 1rem;\\n  background-color: #f57c00; \\n\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  transition: background-color 0.3s;\\n}\\n\\n.start-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ff9800; \\n\\n}\\n\\n.start-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  overflow: hidden;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n}\\n\\n.assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n  background-color: white;\\n  background-image: none;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  gap: 8px;\\n}\\n\\n.message-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 6px;\\n  max-width: 85%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n  will-change: transform, opacity;\\n  transform: translateZ(0);\\n  align-self: flex-start;\\n  margin-left: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-right: 8px;\\n  align-self: flex-end; \\n\\n}\\n\\n.bot-message[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  border-radius: 14px;\\n  position: relative;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  max-width: 100%;\\n  word-wrap: break-word;\\n  line-height: 1.3;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(0, 0, 0, 0.5);\\n  padding: 0 2px;\\n  font-style: italic;\\n  white-space: nowrap;\\n  align-self: flex-end;\\n  margin-left: auto;\\n}\\n.message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  flex: 1;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-top: 0.5em;\\n  margin-bottom: 0.5em;\\n  font-weight: 600;\\n}\\n.message-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-left: 1.5em;\\n  margin-bottom: 0.5em; \\n\\n  padding-left: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.2em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 6px 8px; \\n\\n  border-radius: 4px;\\n  overflow-x: auto;\\n  margin-top: 0.3em;\\n  margin-bottom: 0.5em; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left: 3px solid #ccc;\\n  padding: 2px 0 2px 10px; \\n\\n  margin: 0.3em 0 0.5em 0; \\n\\n  color: #666;\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.2em 0; \\n\\n}\\n.message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0077cc;\\n  text-decoration: underline;\\n}\\n.message-content[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  width: 100%;\\n  margin-bottom: 0.8em;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 6px;\\n}\\n.message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.message-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  border-top-right-radius: 4px;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: rgba(255, 255, 255, 0.5);\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #90caf9;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-timestamp[_ngcontent-%COMP%] {\\n  color: rgba(0, 0, 0, 0.5);\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-top-left-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.3;\\n  word-break: break-word;\\n  white-space: normal;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: white;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);\\n  min-height: 50px;\\n}\\n\\n.message-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 12px;\\n  width: 100%; \\n\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  border-radius: 24px;\\n  padding: 0 8px;\\n}\\n\\n.message-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  margin-top: -4px;\\n}\\n\\n.submit-spinner[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n  .message-field .mat-mdc-form-field-infix {\\n  width: 100% !important;\\n}\\n\\n\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #bdbdbd;\\n  border-radius: 3px;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #9e9e9e;\\n}\\n\\n\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #757575;\\n  height: 100%;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 20px;\\n  transition: all 0.3s ease;\\n}\\n.no-data-message[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #f57c00; \\n\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 0 0 12px 0;\\n  color: #555;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n  max-width: 240px;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 10px 16px;\\n  background-color: white;\\n  color: #333;\\n  height: 48px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.panel-header[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  transition: all 0.3s ease;\\n  margin-left: 4px;\\n}\\n.panel-header[_ngcontent-%COMP%]   .refresh-button[_ngcontent-%COMP%]:hover {\\n  color: #2196f3;\\n}\\n.panel-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\\n  color: #4caf50;\\n}\\n.panel-header[_ngcontent-%COMP%]   .delete-button[_ngcontent-%COMP%]:hover {\\n  color: #f44336;\\n}\\n.panel-header[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1.5s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  height: 20px;\\n  width: 20px;\\n  color: #f57c00; \\n\\n}\\n\\n.panel-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  overflow-y: auto;\\n  flex: 1;\\n  background-color: #f5f5f5;\\n  gap: 16px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 16px;\\n  border-bottom: 1px solid #f0f0f0;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.data-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.data-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n}\\n\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n  border-bottom: 1px solid #f0f0f0;\\n  padding-bottom: 10px;\\n}\\n.data-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #f57c00;\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n}\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  background-color: #fff9f0;\\n}\\n\\n.outlet-section[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n}\\n\\n.work-area-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  padding: 4px 0;\\n  border-bottom: 1px dashed #f0f0f0;\\n}\\n.data-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #555;\\n  min-width: 130px;\\n  padding-right: 10px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #333;\\n  flex: 1;\\n  word-break: break-word;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: #f5f5f5;\\n  padding: 8px 16px;\\n  border-radius: 18px;\\n  width: auto;\\n  justify-content: flex-start;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\\n  margin: 8px 0 8px 16px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  margin: 0 3px;\\n  background-color: #57705d;\\n  border-radius: 50%;\\n  display: inline-block;\\n  opacity: 0.7;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n  animation-delay: 0.4s;\\n}\\n\\n.typing-text[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  font-size: 13px;\\n  color: #555;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-4px) scale(1.2);\\n    opacity: 0.9;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n[_nghost-%COMP%]     .blinking-cursor {\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_cursor-blink 0.5s infinite;\\n  font-weight: bold;\\n  color: #1976d2;\\n  will-change: opacity;\\n  transform: translateZ(0); \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { ChatBotComponent };", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatProgressSpinnerModule", "MatTooltipModule", "MatDialogModule", "ConfirmDialogComponent", "MarkdownPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatBotComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "startConversation", "ɵɵtemplate", "ChatBotComponent_div_2_span_11_Template", "ChatBotComponent_div_2_span_12_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "messages", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "message_r9", "timestamp", "ɵɵelement", "ChatBotComponent_div_17_div_5_Template", "ɵɵpureFunction2", "_c0", "sender", "ɵɵpipeBind1", "text", "ɵɵsanitizeHtml", "ChatBotComponent_div_43_Template_button_click_7_listener", "_r13", "ctx_r12", "refreshRestaurantData", "ctx_r3", "isRefreshing", "ɵɵtextInterpolate1", "ctx_r14", "restaurantData", "commonCuisinesAcrossOutlets", "join", "ctx_r16", "signatureElements", "signatureDishes", "ChatBotComponent_ng_container_44_div_33_span_14_span_1_span_2_Template", "area_r24", "last_r25", "ChatBotComponent_ng_container_44_div_33_span_14_span_1_Template", "outlet_r19", "outletWorkAreas", "ChatBotComponent_ng_container_44_div_33_span_14_Template", "ChatBotComponent_ng_container_44_div_33_span_15_Template", "ɵɵtextInterpolate2", "i_r20", "outletName", "outletAddress", "ɵɵelementContainerStart", "ChatBotComponent_ng_container_44_span_15_Template", "ChatBotComponent_ng_container_44_span_16_Template", "ChatBotComponent_ng_container_44_span_21_Template", "ChatBotComponent_ng_container_44_span_22_Template", "ChatBotComponent_ng_container_44_div_33_Template", "ɵɵelementContainerEnd", "ctx_r4", "totalOutlets", "beverageInfo", "alcoholService", "tobaccoInfo", "tobaccoService", "outletDetails", "ChatBotComponent", "constructor", "sseService", "cd", "snackBar", "dialog", "tenantId", "tenantName", "currentMessage", "isConnecting", "isWaitingForResponse", "conversationStarted", "messageSubscription", "connectionSubscription", "dataUpdateSubscription", "loadingHistory", "ngOnChanges", "changes", "newTenantId", "currentValue", "prevTenantId", "previousValue", "console", "log", "loadConversationHistory", "ngOnInit", "conversationStarted<PERSON><PERSON>", "localStorage", "getItem", "setTimeout", "setItem", "initiateConversation", "detectChanges", "scrollToBottom", "messages$", "subscribe", "message", "id", "startsWith", "substring", "trim", "existingMessageIndex", "findIndex", "m", "duplicateMessage", "find", "includes", "thinkingIndex", "push", "sort", "a", "b", "getTime", "lastMessage", "isDuplicate", "some", "Math", "abs", "dataUpdates$", "data", "type", "Object", "keys", "warn", "ngOnDestroy", "unsubscribe", "disconnect", "sendMessage", "open", "duration", "messageToSend", "userMessage", "generateId", "Date", "next", "error", "onKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "requestAnimationFrame", "chatContainer", "document", "querySelector", "scrollTop", "scrollHeight", "random", "toString", "trackById", "_index", "clearConversationHistory", "removeItem", "debugMessages", "for<PERSON>ach", "msg", "index", "createTestRestaurantData", "testData", "saveRestaurantData", "response", "panelClass", "onAction", "fetchRestaurantData", "deleteRestaurantData", "dialogRef", "width", "title", "confirmButtonText", "cancelButtonText", "afterClosed", "result", "ɵɵdirectiveInject", "i1", "SseService", "ChangeDetectorRef", "i2", "MatSnackBar", "i3", "MatDialog", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatBotComponent_Template", "rf", "ctx", "ChatBotComponent_div_2_Template", "ChatBotComponent_Template_button_click_10_listener", "ChatBotComponent_Template_button_click_13_listener", "ChatBotComponent_div_17_Template", "ChatBotComponent_div_18_Template", "ChatBotComponent_Template_input_ngModelChange_21_listener", "$event", "ChatBotComponent_Template_input_keydown_21_listener", "ChatBotComponent_Template_button_click_22_listener", "ChatBotComponent_Template_button_click_33_listener", "ChatBotComponent_Template_button_click_36_listener", "ChatBotComponent_Template_button_click_39_listener", "ChatBotComponent_div_43_Template", "ChatBotComponent_ng_container_44_Template", "ɵɵclassProp", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i6", "MatButton", "MatIconButton", "MatMiniFabButton", "i7", "MatFormField", "i8", "MatIcon", "i9", "MatInput", "i10", "MatTooltip", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/components/chat-bot/chat-bot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';\nimport { SafeHtmlPipe } from '../../pipes/safe-html.pipe';\nimport { MarkdownPipe } from '../../pipes/markdown.pipe';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatMessage } from 'src/app/models/chat-message.model';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-chat-bot',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatIconModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule,\n    MatDialogModule,\n    SafeHtmlPipe,\n    MarkdownPipe,\n    ConfirmDialogComponent\n  ],\n  templateUrl: './chat-bot.component.html',\n  styleUrls: ['./chat-bot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ChatBotComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() tenantId: string = '';\n  @Input() tenantName: string = '';\n\n  messages: ChatMessage[] = [];\n  currentMessage: string = '';\n  isConnecting: boolean = false;\n  isWaitingForResponse: boolean = false;\n  restaurantData: any = null;\n  conversationStarted: boolean = false;\n  isRefreshing: boolean = false;\n\n  private messageSubscription: Subscription | null = null;\n  private connectionSubscription: Subscription | null = null;\n  private dataUpdateSubscription: Subscription | null = null;\n\n  constructor(\n    private sseService: SseService,\n    private cd: ChangeDetectorRef,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // If tenantId changes, reset the flag and load conversation history\n    if (changes['tenantId']) {\n      const newTenantId = changes['tenantId'].currentValue;\n      const prevTenantId = changes['tenantId'].previousValue;\n\n      console.log('tenantId changed from', prevTenantId, 'to', newTenantId);\n\n      // Only reload if the tenant ID actually changed and is not empty\n      if (newTenantId && prevTenantId !== newTenantId) {\n        console.log('Resetting conversation history flag and loading new history for tenant:', newTenantId);\n        this.loadingHistory = false;\n        this.loadConversationHistory();\n      }\n    }\n  }\n\n  // Flag to track if we're in the process of loading conversation history\n  private loadingHistory = false;\n\n  ngOnInit(): void {\n    // Initialize with an empty messages array\n    this.messages = [];\n\n    console.log('ChatBotComponent initialized with tenantId:', this.tenantId);\n\n    // Always load conversation history if we have a tenant ID\n    if (this.tenantId) {\n      console.log('Loading conversation history by default');\n\n      // Reset the flag to ensure history is loaded\n      this.loadingHistory = false;\n\n      // Check if conversation was previously started\n      const conversationStartedKey = `conversation_started_${this.tenantId}`;\n      this.conversationStarted = localStorage.getItem(conversationStartedKey) === 'true';\n\n      // Load conversation history\n      this.loadConversationHistory();\n\n      // If we have messages after loading history, consider the conversation started\n      setTimeout(() => {\n        console.log('Checking messages after timeout:', this.messages.length);\n        if (this.messages.length > 0) {\n          this.conversationStarted = true;\n          localStorage.setItem(conversationStartedKey, 'true');\n          console.log('Conversation marked as started due to existing messages');\n        } else if (this.conversationStarted) {\n          // If conversation was started but no messages, initiate conversation\n          console.log('Conversation was started but no messages found, initiating conversation');\n          this.initiateConversation();\n        }\n\n        // Force UI update\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      }, 2000); // Longer delay to ensure history is fully loaded\n    } else {\n      console.log('No tenant ID, showing overlay');\n      this.messages = [];\n    }\n\n    // Subscribe to incoming messages\n    this.messageSubscription = this.sseService.messages$.subscribe(\n      (message: ChatMessage) => {\n        // Handle special system messages\n        if (message.sender === 'system') {\n          // This is a completion message, hide the loading indicator\n          if (message.id.startsWith('completion-')) {\n            this.isWaitingForResponse = false;\n            this.cd.detectChanges();\n            return;\n          }\n          // Ignore other system messages\n          return;\n        }\n\n        // For streaming responses, we need to handle bot messages differently\n        if (message.sender === 'bot') {\n          console.log('Bot message received:', message.id, message.text.substring(0, 20) + '...');\n\n          // Skip empty messages\n          if (!message.text.trim()) {\n            console.log('Skipping empty bot message');\n            return;\n          }\n\n          // Check if this is a streaming update to an existing message\n          const existingMessageIndex = this.messages.findIndex(m => m.id === message.id);\n\n          if (existingMessageIndex !== -1) {\n            // Only update if the new message is not \"AI is thinking...\"\n            if (message.text !== 'AI is thinking...') {\n              // Update existing message\n              this.messages[existingMessageIndex] = message;\n              console.log('Updated existing message');\n            }\n          } else {\n            // Check if this message already exists in the conversation\n            const duplicateMessage = this.messages.find(m =>\n              m.sender === 'bot' && m.text === message.text && !m.text.includes('blinking-cursor')\n            );\n\n            // Replace \"AI is thinking...\" with actual content\n            const thinkingIndex = this.messages.findIndex(m =>\n              m.sender === 'bot' && m.text === 'AI is thinking...' && m.id === message.id\n            );\n\n            if (thinkingIndex !== -1) {\n              // Replace the thinking message with the actual content\n              this.messages[thinkingIndex] = message;\n              console.log('Replaced thinking message with actual content');\n            } else if (!duplicateMessage && message.text !== 'AI is thinking...') {\n              // Add new bot message (but not \"AI is thinking...\" messages)\n              this.messages.push(message);\n              console.log('Added new bot message');\n\n              // Sort messages by timestamp to ensure correct order\n              this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n            }\n          }\n\n          // If this is a complete message (no blinking cursor), mark response as complete\n          if (!message.text.includes('blinking-cursor')) {\n            this.isWaitingForResponse = false;\n          }\n\n          // Don't show the \"AI is thinking...\" message if there's no user message before it\n          if (message.text.includes('AI is thinking') && this.messages.length > 0) {\n            const lastMessage = this.messages[this.messages.length - 1];\n            if (lastMessage.sender !== 'user') {\n              // Skip this message\n              console.log('Skipping \"AI is thinking\" message because there is no user message before it');\n              return;\n            }\n          }\n\n          // Force change detection to update the UI immediately\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        } else if (message.sender === 'user') {\n          // For user messages, add them if they don't exist already\n          // Use a more reliable way to check for duplicates\n          const isDuplicate = this.messages.some(m =>\n            m.sender === 'user' &&\n            m.text === message.text &&\n            Math.abs(m.timestamp.getTime() - message.timestamp.getTime()) < 1000 // Within 1 second\n          );\n\n          if (!isDuplicate) {\n            console.log('Adding user message:', message.text);\n            // Add user message to the messages array\n            this.messages.push(message);\n\n            // Sort messages by timestamp to ensure correct order\n            this.messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n          } else {\n            console.log('Duplicate user message detected, not adding:', message.text);\n          }\n        }\n\n        // Force change detection to update the UI immediately\n        this.cd.detectChanges();\n\n        // Scroll to bottom to show latest message\n        this.scrollToBottom();\n      }\n    );\n\n    // Subscribe to data updates (for restaurant data)\n    this.dataUpdateSubscription = this.sseService.dataUpdates$.subscribe(\n      (data: any) => {\n        console.log('Received data update:', data);\n        if (data && data.type === 'restaurant_data') {\n          if (data.data) {\n            console.log('Setting restaurant data:', data.data);\n            this.restaurantData = data.data;\n            console.log('Restaurant data received, restaurantData:', this.restaurantData);\n            console.log('Restaurant data keys:', Object.keys(this.restaurantData));\n\n            // Check if the data has the expected structure\n            if (this.restaurantData.outletDetails) {\n              console.log('Outlet details found:', this.restaurantData.outletDetails);\n            } else {\n              console.warn('No outlet details found in restaurant data');\n            }\n          } else {\n            console.warn('Received null restaurant data, clearing restaurantData');\n            this.restaurantData = null;\n          }\n\n          // Force change detection\n          setTimeout(() => {\n            this.cd.detectChanges();\n            console.log('Change detection triggered after restaurant data update');\n            console.log('Restaurant data after change detection:', this.restaurantData);\n          }, 0);\n\n          // The backend will automatically send the next question\n          // No need to request it from the frontend\n        }\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n\n    if (this.connectionSubscription) {\n      this.connectionSubscription.unsubscribe();\n    }\n\n    if (this.dataUpdateSubscription) {\n      this.dataUpdateSubscription.unsubscribe();\n    }\n\n    // Disconnect from SSE\n    this.sseService.disconnect();\n  }\n\n  // We don't need a separate connect method anymore as we'll connect on demand\n\n  /**\n   * Send a message to the chat service\n   */\n  sendMessage(): void {\n    if (!this.currentMessage.trim()) {\n      return;\n    }\n\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to send messages', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Show loading state\n    this.isConnecting = true;\n    this.isWaitingForResponse = true;\n\n    // Clear input field and save the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    this.cd.detectChanges();\n\n    // We'll let the message subscription handle adding the user message to the array\n    // The service will emit the user message through the messages$ observable\n\n    // No need to update restaurant summary\n\n    // Add the user message directly to the messages array\n    const userMessage: ChatMessage = {\n      id: this.generateId(),\n      text: messageToSend,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    // Check if this message already exists\n    const isDuplicate = this.messages.some(m =>\n      m.sender === 'user' &&\n      m.text === messageToSend\n    );\n\n    if (!isDuplicate) {\n      this.messages.push(userMessage);\n    }\n\n    // Make sure the loading indicator is visible\n    this.isWaitingForResponse = true;\n    this.cd.detectChanges();\n    this.scrollToBottom();\n\n    // Send message to server - the service will handle adding messages to the stream\n    this.sseService.sendMessage(this.tenantId, messageToSend).subscribe({\n      next: () => {\n        // Message sent successfully, but keep waiting for response\n        // The loading indicator will be hidden when the bot response is complete\n        this.isConnecting = false;\n        this.cd.detectChanges();\n\n        // The backend will automatically handle the conversation flow\n        // No need to do anything special after sending a message\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.snackBar.open('Failed to send message', 'Retry', {\n          duration: 3000\n        });\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  // Restaurant summary methods removed\n\n  // Removed unused methods for extracting information from messages\n\n  /**\n   * Handle key press events in the message input\n   * @param event The keyboard event\n   */\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  /**\n   * Scroll the chat container to the bottom\n   */\n  private scrollToBottom(): void {\n    // Use requestAnimationFrame for smoother scrolling that's synchronized with browser rendering\n    requestAnimationFrame(() => {\n      const chatContainer = document.querySelector('.chat-messages');\n      if (chatContainer) {\n        chatContainer.scrollTop = chatContainer.scrollHeight;\n      }\n    });\n  }\n\n  /**\n   * Generate a random ID for messages\n   */\n  private generateId(): string {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Track messages by ID for better performance with ngFor\n   */\n  trackById(_index: number, message: ChatMessage): string {\n    return message.id;\n  }\n\n  /**\n   * Load conversation history from the server\n   */\n  loadConversationHistory(): void {\n    if (!this.tenantId || this.loadingHistory) {\n      console.log('Not loading conversation history: no tenantId or already loading');\n      return;\n    }\n\n    console.log('Loading conversation history for tenant:', this.tenantId);\n\n    // Set the flag to prevent duplicate API calls\n    this.loadingHistory = true;\n\n    // Show loading state\n    this.isConnecting = true;\n\n    // Clear existing messages to avoid duplicates\n    this.messages = [];\n\n    // Load conversation history from the server\n    // Set addToStream to false so we can handle the messages ourselves\n    this.sseService.loadConversationHistory(this.tenantId, false).subscribe({\n      next: (messages) => {\n        console.log('Received history messages:', messages.length);\n\n        // If we got messages from the server, use them\n        if (messages && messages.length > 0) {\n          // Sort messages by timestamp to ensure correct order\n          messages.sort((a, b) => {\n            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();\n          });\n\n          console.log('Sorted messages by timestamp');\n\n          // Add all messages to our local array without filtering\n          this.messages = messages;\n          console.log('Added all messages to local array, count:', this.messages.length);\n\n          // Mark conversation as started since we have history\n          this.conversationStarted = true;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.setItem(conversationStartedKey, 'true');\n        } else {\n          // If no messages, initialize with empty array\n          this.messages = [];\n          console.log('No history found, initialized with empty array');\n        }\n\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n        this.cd.detectChanges();\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Error loading conversation history:', error);\n        this.isConnecting = false;\n        this.isWaitingForResponse = false;\n\n        // If error, initialize with empty array\n        this.messages = [];\n\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Clear conversation history\n   */\n  clearConversationHistory(): void {\n    // Show loading state\n    this.isConnecting = true;\n    this.cd.detectChanges();\n\n    console.log('Clearing conversation history for tenant:', this.tenantId);\n\n    // No need to reset restaurant summary\n\n    // Clear both the UI, cache, AND the server data\n    if (this.tenantId) {\n      console.log('Calling SSE service to clear conversation history for tenant:', this.tenantId);\n      this.sseService.clearConversationHistory(this.tenantId, true, true).subscribe({\n        next: () => {\n          console.log('Conversation history cleared on server');\n\n          // Reset to empty array\n          this.messages = [];\n\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n\n          // Reset restaurant data\n          this.restaurantData = null;\n\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n\n          // No pending questions to clear\n\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error('Error clearing conversation history:', error);\n\n          // Still reset the UI even if the server call fails\n          this.messages = [];\n\n          // Reset the conversation started flag\n          this.conversationStarted = false;\n          const conversationStartedKey = `conversation_started_${this.tenantId}`;\n          localStorage.removeItem(conversationStartedKey);\n\n          // Reset restaurant data\n          this.restaurantData = null;\n\n          // Reset the flag to allow loading conversation history again\n          this.loadingHistory = false;\n\n          // No pending questions to clear\n          this.isConnecting = false;\n          this.cd.detectChanges();\n          this.scrollToBottom();\n        }\n      });\n    } else {\n      // If no tenant ID, just reset the UI\n      this.messages = [\n        {\n          id: this.generateId(),\n          text: 'Conversation has been cleared. How can I help you today?',\n          sender: 'bot',\n          timestamp: new Date()\n        }\n      ];\n\n      this.loadingHistory = false;\n      this.isConnecting = false;\n      this.cd.detectChanges();\n      this.scrollToBottom();\n    }\n  }\n\n  // ngOnDestroy is already defined above\n\n  /**\n   * Start the conversation when the user clicks the overlay\n   */\n  startConversation(): void {\n    if (!this.tenantId) {\n      this.snackBar.open('Tenant ID is required to start conversation', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    // Mark conversation as started\n    this.conversationStarted = true;\n\n    // Save to localStorage to persist across page refreshes\n    const conversationStartedKey = `conversation_started_${this.tenantId}`;\n    localStorage.setItem(conversationStartedKey, 'true');\n\n    // The backend will send the welcome message and start the conversation\n    // Just initiate the conversation with the backend\n    this.initiateConversation();\n\n    // Force UI update\n    this.cd.detectChanges();\n    this.scrollToBottom();\n  }\n\n  /**\n   * Initiate or continue the conversation with the backend\n   * This simply sends a signal to the backend to continue the conversation flow\n   */\n  private initiateConversation(): void {\n    if (!this.tenantId) {\n      return;\n    }\n\n    console.log('Initiating conversation with backend for tenant:', this.tenantId);\n\n    // Send a special message to the backend to continue the conversation\n    this.sseService.sendMessage(this.tenantId, '__continue_conversation__').subscribe({\n      next: () => {\n        console.log('Continue conversation signal sent successfully');\n        // The response will come through the normal message subscription\n      },\n      error: (error) => {\n        console.error('Error initiating conversation:', error);\n        this.snackBar.open('Failed to continue conversation', 'Retry', {\n          duration: 3000\n        });\n      }\n    });\n  }\n\n  // Removed unused methods for checking data completeness and updating questions\n\n  /**\n   * Debug function to log the current messages\n   */\n  debugMessages(): void {\n    console.log('Current messages count:', this.messages.length);\n    this.messages.forEach((msg, index) => {\n      console.log(`Message ${index}:`, msg.id, msg.sender, msg.text.substring(0, 30) + '...');\n    });\n\n    // Force reload of conversation history\n    this.loadingHistory = false;\n    this.loadConversationHistory();\n\n    // Check restaurant data\n    console.log('Current restaurant data:', this.restaurantData);\n\n    // Create test restaurant data if none exists\n    if (!this.restaurantData && this.tenantId) {\n      this.createTestRestaurantData();\n    }\n  }\n\n  /**\n   * Create test restaurant data for debugging\n   */\n  createTestRestaurantData(): void {\n    if (!this.tenantId) {\n      console.warn('Cannot create test data: No tenant ID');\n      return;\n    }\n\n    console.log('Creating test restaurant data for tenant:', this.tenantId);\n\n    const testData = {\n      totalOutlets: 1,\n      outletDetails: [\n        {\n          outletName: 'Test Restaurant',\n          outletAddress: '123 Test Street, Test City',\n          outletWorkAreas: ['Kitchen', 'Bar', 'Dining area']\n        }\n      ],\n      commonCuisinesAcrossOutlets: ['Italian', 'Chinese'],\n      signatureElements: {\n        signatureDishes: ['Test Dish 1', 'Test Dish 2']\n      },\n      beverageInfo: {\n        alcoholService: 'Full bar'\n      },\n      tobaccoInfo: {\n        tobaccoService: 'Yes'\n      }\n    };\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    this.sseService.saveRestaurantData(this.tenantId, testData).subscribe({\n      next: (response) => {\n        console.log('Test restaurant data saved:', response);\n        if (response) {\n          this.restaurantData = testData;\n          this.snackBar.open('Test restaurant data created successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('Failed to create test restaurant data', 'Close', {\n            duration: 3000,\n            panelClass: 'error-snackbar'\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error creating test restaurant data:', error);\n        this.snackBar.open('Failed to create test restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.createTestRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Refresh restaurant data from the backend\n   */\n  refreshRestaurantData(): void {\n    if (!this.tenantId) {\n      console.warn('Cannot refresh restaurant data: No tenant ID');\n      this.snackBar.open('Cannot refresh: No tenant ID', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n\n    if (this.isRefreshing) {\n      console.warn('Already refreshing restaurant data');\n      return;\n    }\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    console.log('Refreshing restaurant data for tenant:', this.tenantId);\n\n    this.sseService.fetchRestaurantData(this.tenantId).subscribe({\n      next: (data) => {\n        console.log('Restaurant data refreshed:', data);\n        if (data) {\n          console.log('Restaurant data structure:', Object.keys(data));\n          this.restaurantData = data;\n          this.snackBar.open('Restaurant data refreshed successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          console.warn('No restaurant data returned from API');\n          this.snackBar.open('No restaurant data available', 'Close', {\n            duration: 3000\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n        console.log('After refresh - restaurantData:', this.restaurantData);\n      },\n      error: (error) => {\n        console.error('Error refreshing restaurant data:', error);\n        this.snackBar.open('Failed to refresh restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.refreshRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Save restaurant data to the backend\n   */\n  saveRestaurantData(): void {\n    if (!this.tenantId || !this.restaurantData || this.isRefreshing) {\n      return;\n    }\n\n    this.isRefreshing = true;\n    this.cd.detectChanges();\n\n    console.log('Saving restaurant data for tenant:', this.tenantId);\n\n    this.sseService.saveRestaurantData(this.tenantId, this.restaurantData).subscribe({\n      next: (response) => {\n        console.log('Restaurant data saved:', response);\n        if (response) {\n          this.snackBar.open('Restaurant data saved successfully', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar'\n          });\n        } else {\n          this.snackBar.open('Failed to save restaurant data', 'Close', {\n            duration: 3000,\n            panelClass: 'error-snackbar'\n          });\n        }\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      },\n      error: (error) => {\n        console.error('Error saving restaurant data:', error);\n        this.snackBar.open('Failed to save restaurant data', 'Retry', {\n          duration: 3000,\n          panelClass: 'error-snackbar'\n        }).onAction().subscribe(() => {\n          this.saveRestaurantData();\n        });\n        this.isRefreshing = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  /**\n   * Delete restaurant data from the backend\n   */\n  deleteRestaurantData(): void {\n    if (!this.tenantId || this.isRefreshing || !this.restaurantData) {\n      return;\n    }\n\n    // Show confirmation dialog\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      width: '350px',\n      data: {\n        title: 'Delete Restaurant Data',\n        message: 'Are you sure you want to delete all restaurant data? This action cannot be undone.',\n        confirmButtonText: 'Delete',\n        cancelButtonText: 'Cancel'\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        // User confirmed deletion\n        this.isRefreshing = true;\n        this.cd.detectChanges();\n\n        console.log('Deleting restaurant data for tenant:', this.tenantId);\n\n        this.sseService.deleteRestaurantData(this.tenantId).subscribe({\n          next: (response) => {\n            console.log('Restaurant data deleted:', response);\n            if (response) {\n              this.restaurantData = null;\n              this.snackBar.open('Restaurant data deleted successfully', 'Close', {\n                duration: 3000,\n                panelClass: 'success-snackbar'\n              });\n            } else {\n              this.snackBar.open('Failed to delete restaurant data', 'Close', {\n                duration: 3000,\n                panelClass: 'error-snackbar'\n              });\n            }\n            this.isRefreshing = false;\n            this.cd.detectChanges();\n          },\n          error: (error) => {\n            console.error('Error deleting restaurant data:', error);\n            this.snackBar.open('Failed to delete restaurant data', 'Retry', {\n              duration: 3000,\n              panelClass: 'error-snackbar'\n            }).onAction().subscribe(() => {\n              this.deleteRestaurantData();\n            });\n            this.isRefreshing = false;\n            this.cd.detectChanges();\n          }\n        });\n      }\n    });\n  }\n}\n", "<div class=\"chat-layout\">\n  <div class=\"chat-container\">\n  <!-- Start/Resume Overlay -->\n  <div class=\"chat-overlay\" *ngIf=\"!conversationStarted\" (click)=\"startConversation()\">\n    <div class=\"overlay-content\">\n      <mat-icon class=\"overlay-icon\">restaurant</mat-icon>\n      <h2>Restaurant Onboarding Assistant</h2>\n      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>\n      <button mat-raised-button color=\"primary\" class=\"start-button\">\n        <mat-icon>play_arrow</mat-icon>\n        <span *ngIf=\"messages.length === 0\">Click to Start</span>\n        <span *ngIf=\"messages.length > 0\">Click to Resume</span>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-header\">\n    <div class=\"chat-title\">\n      <mat-icon class=\"chat-icon\">restaurant</mat-icon>\n      <span class=\"assistant-title\">Restaurant details</span>\n    </div>\n    <div class=\"chat-actions\">\n      <!-- Preview button removed -->\n      <button mat-icon-button matTooltip=\"Clear Chat\" (click)=\"clearConversationHistory()\" class=\"clear-btn\">\n        <mat-icon>clear</mat-icon>\n      </button>\n      <button mat-icon-button matTooltip=\"Debug Messages\" (click)=\"debugMessages()\" class=\"debug-btn\">\n        <mat-icon>bug_report</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <div class=\"chat-messages\">\n    <div *ngFor=\"let message of messages; trackBy: trackById\" class=\"message-container\" [ngClass]=\"{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}\">\n      <div class=\"message-content\">\n        <div class=\"message-wrapper\">\n          <div class=\"message-text\" [innerHTML]=\"message.text | markdown\"></div>\n          <div class=\"message-timestamp\" *ngIf=\"message.sender !== 'system'\">{{ message.timestamp | date:'shortTime' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Improved loading indicator when waiting for a response -->\n    <div *ngIf=\"isWaitingForResponse\" class=\"message-container bot-message\">\n      <div class=\"typing-indicator\">\n        <div class=\"typing-dots\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n        <div class=\"typing-text\">AI is thinking...</div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"chat-input\">\n    <mat-form-field appearance=\"outline\" class=\"message-field\">\n      <input matInput\n             [(ngModel)]=\"currentMessage\"\n             placeholder=\"Type your message...\"\n             (keydown)=\"onKeyPress($event)\"\n             [disabled]=\"isConnecting\">\n    </mat-form-field>\n    <button mat-mini-fab color=\"primary\" (click)=\"sendMessage()\" [disabled]=\"!currentMessage.trim() || isConnecting\">\n      <mat-icon>send</mat-icon>\n    </button>\n  </div>\n</div>\n\n  <!-- Restaurant Data Panel -->\n  <div class=\"restaurant-data-panel\">\n    <div class=\"panel-header\">\n      <div class=\"header-left\">\n        <mat-icon>restaurant_menu</mat-icon>\n        <h2>Restaurant Information</h2>\n      </div>\n      <div class=\"header-right\">\n        <button mat-icon-button class=\"action-button save-button\" (click)=\"saveRestaurantData()\" matTooltip=\"Save restaurant data\" [disabled]=\"isRefreshing || !restaurantData\">\n          <mat-icon>save</mat-icon>\n        </button>\n        <button mat-icon-button class=\"action-button refresh-button\" (click)=\"refreshRestaurantData()\" matTooltip=\"Refresh restaurant data\" [disabled]=\"isRefreshing\">\n          <mat-icon [class.rotating]=\"isRefreshing\">refresh</mat-icon>\n        </button>\n        <button mat-icon-button class=\"action-button delete-button\" (click)=\"deleteRestaurantData()\" matTooltip=\"Delete restaurant data\" [disabled]=\"isRefreshing || !restaurantData\">\n          <mat-icon>delete</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <div class=\"panel-content\">\n      <!-- No Data Message -->\n      <div class=\"no-data-message\" *ngIf=\"!restaurantData\">\n        <mat-icon>restaurant</mat-icon>\n        <h3>No Restaurant Data Yet</h3>\n        <p>As you provide information about your restaurant through the chat, it will appear here.</p>\n        <button mat-raised-button color=\"primary\" (click)=\"refreshRestaurantData()\" [disabled]=\"isRefreshing\">\n          <mat-icon>refresh</mat-icon> Force Refresh Data\n        </button>\n      </div>\n\n      <!-- Restaurant Data (shown when available) -->\n      <ng-container *ngIf=\"restaurantData\">\n        <!-- Summary Section -->\n        <div class=\"data-section summary-section\">\n          <h3><mat-icon>info</mat-icon> Restaurant Summary</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Total Outlets:</span>\n            <span class=\"value\">{{restaurantData.totalOutlets}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Cuisines:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.commonCuisinesAcrossOutlets && restaurantData.commonCuisinesAcrossOutlets.length > 0\">\n                {{restaurantData.commonCuisinesAcrossOutlets.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.commonCuisinesAcrossOutlets || restaurantData.commonCuisinesAcrossOutlets.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Signature Dishes:</span>\n            <span class=\"value\">\n              <span *ngIf=\"restaurantData.signatureElements && restaurantData.signatureElements.signatureDishes && restaurantData.signatureElements.signatureDishes.length > 0\">\n                {{restaurantData.signatureElements.signatureDishes.join(', ')}}\n              </span>\n              <span *ngIf=\"!restaurantData.signatureElements || !restaurantData.signatureElements.signatureDishes || restaurantData.signatureElements.signatureDishes.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Alcohol Service:</span>\n            <span class=\"value\">{{restaurantData.beverageInfo?.alcoholService || 'Not specified'}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Tobacco Service:</span>\n            <span class=\"value\">{{restaurantData.tobaccoInfo?.tobaccoService || 'Not specified'}}</span>\n          </div>\n        </div>\n\n        <!-- Outlets Section -->\n        <div class=\"data-section outlet-section\" *ngFor=\"let outlet of restaurantData.outletDetails; let i = index\">\n          <h3><mat-icon>store</mat-icon> Outlet {{i+1}}: {{outlet.outletName}}</h3>\n          <div class=\"data-item\">\n            <span class=\"label\">Address:</span>\n            <span class=\"value\">{{outlet.outletAddress}}</span>\n          </div>\n          <div class=\"data-item\">\n            <span class=\"label\">Work Areas:</span>\n            <span class=\"value\">\n              <span *ngIf=\"outlet.outletWorkAreas && outlet.outletWorkAreas.length > 0\">\n                <span *ngFor=\"let area of outlet.outletWorkAreas; let last = last\" class=\"work-area-tag\">\n                  {{area}}<span *ngIf=\"!last\">, </span>\n                </span>\n              </span>\n              <span *ngIf=\"!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0\">\n                None specified\n              </span>\n            </span>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAAoBC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;;ICJhDC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAR9DH,EAAA,CAAAC,cAAA,cAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,qDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAClFX,EAAA,CAAAC,cAAA,cAA6B;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2FAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3FH,EAAA,CAAAC,cAAA,iBAA+D;IACnDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAY,UAAA,KAAAC,uCAAA,mBAAyD;IACzDb,EAAA,CAAAY,UAAA,KAAAE,uCAAA,mBAAwD;IAC1Dd,EAAA,CAAAG,YAAA,EAAS;;;;IAFAH,EAAA,CAAAe,SAAA,IAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,OAA2B;IAC3BnB,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAAyB;;;;;IA0B9BnB,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhDH,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAC,UAAA,CAAAC,SAAA,eAA0C;;;;;;;;;;;IAJnHvB,EAAA,CAAAC,cAAA,cAAqL;IAG/KD,EAAA,CAAAwB,SAAA,cAAsE;;IACtExB,EAAA,CAAAY,UAAA,IAAAa,sCAAA,kBAAmH;IACrHzB,EAAA,CAAAG,YAAA,EAAM;;;;IAL0EH,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAM,MAAA,aAAAN,UAAA,CAAAM,MAAA,YAAgG;IAGpJ5B,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,cAAAhB,EAAA,CAAA6B,WAAA,OAAAP,UAAA,CAAAQ,IAAA,GAAA9B,EAAA,CAAA+B,cAAA,CAAqC;IAC/B/B,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,SAAAM,UAAA,CAAAM,MAAA,cAAiC;;;;;IAMvE5B,EAAA,CAAAC,cAAA,cAAwE;IAGlED,EAAA,CAAAwB,SAAA,WAAa;IAGfxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAyClDH,EAAA,CAAAC,cAAA,cAAqD;IACzCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8FAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9FH,EAAA,CAAAC,cAAA,iBAAsG;IAA5DD,EAAA,CAAAI,UAAA,mBAAA4B,yDAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAwB,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IACzEnC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,4BAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFmEH,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,aAAAoB,MAAA,CAAAC,YAAA,CAAyB;;;;;IAiB/FrC,EAAA,CAAAC,cAAA,WAAkH;IAChHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAsC,kBAAA,MAAAC,OAAA,CAAAC,cAAA,CAAAC,2BAAA,CAAAC,IAAA,YACF;;;;;IACA1C,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMPH,EAAA,CAAAC,cAAA,WAAkK;IAChKD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAsC,kBAAA,MAAAK,OAAA,CAAAH,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAAH,IAAA,YACF;;;;;IACA1C,EAAA,CAAAC,cAAA,WAAsK;IACpKD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAyBKH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADvCH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAY,UAAA,IAAAkC,sEAAA,mBAA6B;IACvC9C,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAe,SAAA,GAAQ;IAARf,EAAA,CAAAsC,kBAAA,MAAAS,QAAA,KAAQ;IAAO/C,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAAgC,QAAA,CAAW;;;;;IAF9BhD,EAAA,CAAAC,cAAA,WAA0E;IACxED,EAAA,CAAAY,UAAA,IAAAqC,+DAAA,mBAEO;IACTjD,EAAA,CAAAG,YAAA,EAAO;;;;IAHkBH,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,YAAAkC,UAAA,CAAAC,eAAA,CAA2B;;;;;IAIpDnD,EAAA,CAAAC,cAAA,WAA6E;IAC3ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBbH,EAAA,CAAAC,cAAA,cAA4G;IAC5FD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAwC,wDAAA,mBAIO;IACPpD,EAAA,CAAAY,UAAA,KAAAyC,wDAAA,mBAEO;IACTrD,EAAA,CAAAG,YAAA,EAAO;;;;;IAhBsBH,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAsD,kBAAA,aAAAC,KAAA,YAAAL,UAAA,CAAAM,UAAA,KAAqC;IAG9CxD,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAoB,iBAAA,CAAA8B,UAAA,CAAAO,aAAA,CAAwB;IAKnCzD,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAgB,UAAA,SAAAkC,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAAhC,MAAA,KAAiE;IAKjEnB,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,UAAA,UAAAkC,UAAA,CAAAC,eAAA,IAAAD,UAAA,CAAAC,eAAA,CAAAhC,MAAA,OAAoE;;;;;IAvDnFnB,EAAA,CAAA0D,uBAAA,GAAqC;IAEnC1D,EAAA,CAAAC,cAAA,cAA0C;IAC1BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,cAAuB;IACDD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAA+C,iDAAA,mBAEO;IACP3D,EAAA,CAAAY,UAAA,KAAAgD,iDAAA,mBAEO;IACT5D,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAY,UAAA,KAAAiD,iDAAA,mBAEO;IACP7D,EAAA,CAAAY,UAAA,KAAAkD,iDAAA,mBAEO;IACT9D,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE/FH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKhGH,EAAA,CAAAY,UAAA,KAAAmD,gDAAA,mBAmBM;IACR/D,EAAA,CAAAgE,qBAAA,EAAe;;;;IAvDWhE,EAAA,CAAAe,SAAA,IAA+B;IAA/Bf,EAAA,CAAAoB,iBAAA,CAAA6C,MAAA,CAAAzB,cAAA,CAAA0B,YAAA,CAA+B;IAK1ClE,EAAA,CAAAe,SAAA,GAAyG;IAAzGf,EAAA,CAAAgB,UAAA,SAAAiD,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAtB,MAAA,KAAyG;IAGzGnB,EAAA,CAAAe,SAAA,GAA4G;IAA5Gf,EAAA,CAAAgB,UAAA,UAAAiD,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,IAAAwB,MAAA,CAAAzB,cAAA,CAAAC,2BAAA,CAAAtB,MAAA,OAA4G;IAQ5GnB,EAAA,CAAAe,SAAA,GAAyJ;IAAzJf,EAAA,CAAAgB,UAAA,SAAAiD,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,IAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAA1B,MAAA,KAAyJ;IAGzJnB,EAAA,CAAAe,SAAA,GAA6J;IAA7Jf,EAAA,CAAAgB,UAAA,UAAAiD,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,KAAAqB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,IAAAoB,MAAA,CAAAzB,cAAA,CAAAI,iBAAA,CAAAC,eAAA,CAAA1B,MAAA,OAA6J;IAOlJnB,EAAA,CAAAe,SAAA,GAAkE;IAAlEf,EAAA,CAAAoB,iBAAA,EAAA6C,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,kBAAAF,MAAA,CAAAzB,cAAA,CAAA2B,YAAA,CAAAC,cAAA,qBAAkE;IAIlEpE,EAAA,CAAAe,SAAA,GAAiE;IAAjEf,EAAA,CAAAoB,iBAAA,EAAA6C,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,kBAAAJ,MAAA,CAAAzB,cAAA,CAAA6B,WAAA,CAAAC,cAAA,qBAAiE;IAK7BtE,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,UAAA,YAAAiD,MAAA,CAAAzB,cAAA,CAAA+B,aAAA,CAAiC;;;AD3HrG,MAuBaC,gBAAgB;EAgB3BC,YACUC,UAAsB,EACtBC,EAAqB,EACrBC,QAAqB,EACrBC,MAAiB;IAHjB,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAnBP,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,EAAE;IAEhC,KAAA7D,QAAQ,GAAkB,EAAE;IAC5B,KAAA8D,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAA1C,cAAc,GAAQ,IAAI;IAC1B,KAAA2C,mBAAmB,GAAY,KAAK;IACpC,KAAA9C,YAAY,GAAY,KAAK;IAErB,KAAA+C,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,sBAAsB,GAAwB,IAAI;IA0B1D;IACQ,KAAAC,cAAc,GAAG,KAAK;EApB3B;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,MAAMC,WAAW,GAAGD,OAAO,CAAC,UAAU,CAAC,CAACE,YAAY;MACpD,MAAMC,YAAY,GAAGH,OAAO,CAAC,UAAU,CAAC,CAACI,aAAa;MAEtDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,YAAY,EAAE,IAAI,EAAEF,WAAW,CAAC;MAErE;MACA,IAAIA,WAAW,IAAIE,YAAY,KAAKF,WAAW,EAAE;QAC/CI,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAEL,WAAW,CAAC;QACnG,IAAI,CAACH,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACS,uBAAuB,EAAE;;;EAGpC;EAKAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC/E,QAAQ,GAAG,EAAE;IAElB4E,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEzE;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBgB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD;MACA,IAAI,CAACR,cAAc,GAAG,KAAK;MAE3B;MACA,MAAMW,sBAAsB,GAAG,wBAAwB,IAAI,CAACpB,QAAQ,EAAE;MACtE,IAAI,CAACK,mBAAmB,GAAGgB,YAAY,CAACC,OAAO,CAACF,sBAAsB,CAAC,KAAK,MAAM;MAElF;MACA,IAAI,CAACF,uBAAuB,EAAE;MAE9B;MACAK,UAAU,CAAC,MAAK;QACdP,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC7E,QAAQ,CAACC,MAAM,CAAC;QACrE,IAAI,IAAI,CAACD,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACgE,mBAAmB,GAAG,IAAI;UAC/BgB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;UACpDJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;SACvE,MAAM,IAAI,IAAI,CAACZ,mBAAmB,EAAE;UACnC;UACAW,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;UACtF,IAAI,CAACQ,oBAAoB,EAAE;;QAG7B;QACA,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACX,MAAM;MACLX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAAC7E,QAAQ,GAAG,EAAE;;IAGpB;IACA,IAAI,CAACkE,mBAAmB,GAAG,IAAI,CAACV,UAAU,CAACgC,SAAS,CAACC,SAAS,CAC3DC,OAAoB,IAAI;MACvB;MACA,IAAIA,OAAO,CAAChF,MAAM,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAIgF,OAAO,CAACC,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACxC,IAAI,CAAC5B,oBAAoB,GAAG,KAAK;UACjC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;UACvB;;QAEF;QACA;;MAGF;MACA,IAAII,OAAO,CAAChF,MAAM,KAAK,KAAK,EAAE;QAC5BkE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEa,OAAO,CAACC,EAAE,EAAED,OAAO,CAAC9E,IAAI,CAACiF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAEvF;QACA,IAAI,CAACH,OAAO,CAAC9E,IAAI,CAACkF,IAAI,EAAE,EAAE;UACxBlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC;;QAGF;QACA,MAAMkB,oBAAoB,GAAG,IAAI,CAAC/F,QAAQ,CAACgG,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKD,OAAO,CAACC,EAAE,CAAC;QAE9E,IAAII,oBAAoB,KAAK,CAAC,CAAC,EAAE;UAC/B;UACA,IAAIL,OAAO,CAAC9E,IAAI,KAAK,mBAAmB,EAAE;YACxC;YACA,IAAI,CAACZ,QAAQ,CAAC+F,oBAAoB,CAAC,GAAGL,OAAO;YAC7Cd,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;;SAE1C,MAAM;UACL;UACA,MAAMqB,gBAAgB,GAAG,IAAI,CAAClG,QAAQ,CAACmG,IAAI,CAACF,CAAC,IAC3CA,CAAC,CAACvF,MAAM,KAAK,KAAK,IAAIuF,CAAC,CAACrF,IAAI,KAAK8E,OAAO,CAAC9E,IAAI,IAAI,CAACqF,CAAC,CAACrF,IAAI,CAACwF,QAAQ,CAAC,iBAAiB,CAAC,CACrF;UAED;UACA,MAAMC,aAAa,GAAG,IAAI,CAACrG,QAAQ,CAACgG,SAAS,CAACC,CAAC,IAC7CA,CAAC,CAACvF,MAAM,KAAK,KAAK,IAAIuF,CAAC,CAACrF,IAAI,KAAK,mBAAmB,IAAIqF,CAAC,CAACN,EAAE,KAAKD,OAAO,CAACC,EAAE,CAC5E;UAED,IAAIU,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB;YACA,IAAI,CAACrG,QAAQ,CAACqG,aAAa,CAAC,GAAGX,OAAO;YACtCd,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;WAC7D,MAAM,IAAI,CAACqB,gBAAgB,IAAIR,OAAO,CAAC9E,IAAI,KAAK,mBAAmB,EAAE;YACpE;YACA,IAAI,CAACZ,QAAQ,CAACsG,IAAI,CAACZ,OAAO,CAAC;YAC3Bd,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;YAEpC;YACA,IAAI,CAAC7E,QAAQ,CAACuG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnG,SAAS,CAACqG,OAAO,EAAE,GAAGD,CAAC,CAACpG,SAAS,CAACqG,OAAO,EAAE,CAAC;;;QAI/E;QACA,IAAI,CAAChB,OAAO,CAAC9E,IAAI,CAACwF,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C,IAAI,CAACpC,oBAAoB,GAAG,KAAK;;QAGnC;QACA,IAAI0B,OAAO,CAAC9E,IAAI,CAACwF,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAACpG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACvE,MAAM0G,WAAW,GAAG,IAAI,CAAC3G,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;UAC3D,IAAI0G,WAAW,CAACjG,MAAM,KAAK,MAAM,EAAE;YACjC;YACAkE,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;YAC3F;;;QAIJ;QACA,IAAI,CAACpB,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM,IAAIG,OAAO,CAAChF,MAAM,KAAK,MAAM,EAAE;QACpC;QACA;QACA,MAAMkG,WAAW,GAAG,IAAI,CAAC5G,QAAQ,CAAC6G,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACvF,MAAM,KAAK,MAAM,IACnBuF,CAAC,CAACrF,IAAI,KAAK8E,OAAO,CAAC9E,IAAI,IACvBkG,IAAI,CAACC,GAAG,CAACd,CAAC,CAAC5F,SAAS,CAACqG,OAAO,EAAE,GAAGhB,OAAO,CAACrF,SAAS,CAACqG,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;SACtE;;QAED,IAAI,CAACE,WAAW,EAAE;UAChBhC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEa,OAAO,CAAC9E,IAAI,CAAC;UACjD;UACA,IAAI,CAACZ,QAAQ,CAACsG,IAAI,CAACZ,OAAO,CAAC;UAE3B;UACA,IAAI,CAAC1F,QAAQ,CAACuG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnG,SAAS,CAACqG,OAAO,EAAE,GAAGD,CAAC,CAACpG,SAAS,CAACqG,OAAO,EAAE,CAAC;SAC5E,MAAM;UACL9B,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEa,OAAO,CAAC9E,IAAI,CAAC;;;MAI7E;MACA,IAAI,CAAC6C,EAAE,CAAC6B,aAAa,EAAE;MAEvB;MACA,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CACF;IAED;IACA,IAAI,CAACnB,sBAAsB,GAAG,IAAI,CAACZ,UAAU,CAACwD,YAAY,CAACvB,SAAS,CACjEwB,IAAS,IAAI;MACZrC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoC,IAAI,CAAC;MAC1C,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;QAC3C,IAAID,IAAI,CAACA,IAAI,EAAE;UACbrC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoC,IAAI,CAACA,IAAI,CAAC;UAClD,IAAI,CAAC3F,cAAc,GAAG2F,IAAI,CAACA,IAAI;UAC/BrC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACvD,cAAc,CAAC;UAC7EsD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9F,cAAc,CAAC,CAAC;UAEtE;UACA,IAAI,IAAI,CAACA,cAAc,CAAC+B,aAAa,EAAE;YACrCuB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACvD,cAAc,CAAC+B,aAAa,CAAC;WACxE,MAAM;YACLuB,OAAO,CAACyC,IAAI,CAAC,4CAA4C,CAAC;;SAE7D,MAAM;UACLzC,OAAO,CAACyC,IAAI,CAAC,wDAAwD,CAAC;UACtE,IAAI,CAAC/F,cAAc,GAAG,IAAI;;QAG5B;QACA6D,UAAU,CAAC,MAAK;UACd,IAAI,CAAC1B,EAAE,CAAC6B,aAAa,EAAE;UACvBV,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtED,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAACvD,cAAc,CAAC;QAC7E,CAAC,EAAE,CAAC,CAAC;QAEL;QACA;;IAEJ,CAAC,CACF;EACH;;EAEAgG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACpD,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACqD,WAAW,EAAE;;IAGxC,IAAI,IAAI,CAACpD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACoD,WAAW,EAAE;;IAG3C,IAAI,IAAI,CAACnD,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACmD,WAAW,EAAE;;IAG3C;IACA,IAAI,CAAC/D,UAAU,CAACgE,UAAU,EAAE;EAC9B;EAEA;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAACgC,IAAI,EAAE,EAAE;MAC/B;;IAGF,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;MAClB,IAAI,CAACF,QAAQ,CAACgE,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;QACpEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC5D,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAM4D,aAAa,GAAG,IAAI,CAAC9D,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IACxB,IAAI,CAACL,EAAE,CAAC6B,aAAa,EAAE;IAEvB;IACA;IAEA;IAEA;IACA,MAAMuC,WAAW,GAAgB;MAC/BlC,EAAE,EAAE,IAAI,CAACmC,UAAU,EAAE;MACrBlH,IAAI,EAAEgH,aAAa;MACnBlH,MAAM,EAAE,MAAM;MACdL,SAAS,EAAE,IAAI0H,IAAI;KACpB;IAED;IACA,MAAMnB,WAAW,GAAG,IAAI,CAAC5G,QAAQ,CAAC6G,IAAI,CAACZ,CAAC,IACtCA,CAAC,CAACvF,MAAM,KAAK,MAAM,IACnBuF,CAAC,CAACrF,IAAI,KAAKgH,aAAa,CACzB;IAED,IAAI,CAAChB,WAAW,EAAE;MAChB,IAAI,CAAC5G,QAAQ,CAACsG,IAAI,CAACuB,WAAW,CAAC;;IAGjC;IACA,IAAI,CAAC7D,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAAC/B,UAAU,CAACiE,WAAW,CAAC,IAAI,CAAC7D,QAAQ,EAAEgE,aAAa,CAAC,CAACnC,SAAS,CAAC;MAClEuC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA;QACA,IAAI,CAACjE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;QAEvB;QACA;MACF,CAAC;;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAClE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACN,QAAQ,CAACgE,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UACpDC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAClE,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EAEA;EAEA;;;;EAIA4C,UAAUA,CAACC,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACb,WAAW,EAAE;;EAEtB;EAEA;;;EAGQlC,cAAcA,CAAA;IACpB;IACAgD,qBAAqB,CAAC,MAAK;MACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC9D,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACI,YAAY;;IAExD,CAAC,CAAC;EACJ;EAEA;;;EAGQd,UAAUA,CAAA;IAChB,OAAOhB,IAAI,CAAC+B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACjD,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGiB,IAAI,CAAC+B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACjD,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClG;EAEA;;;EAGAkD,SAASA,CAACC,MAAc,EAAEtD,OAAoB;IAC5C,OAAOA,OAAO,CAACC,EAAE;EACnB;EAEA;;;EAGAb,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAClB,QAAQ,IAAI,IAAI,CAACS,cAAc,EAAE;MACzCO,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E;;IAGFD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACS,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACN,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAAC/D,QAAQ,GAAG,EAAE;IAElB;IACA;IACA,IAAI,CAACwD,UAAU,CAACsB,uBAAuB,CAAC,IAAI,CAAClB,QAAQ,EAAE,KAAK,CAAC,CAAC6B,SAAS,CAAC;MACtEuC,IAAI,EAAGhI,QAAQ,IAAI;QACjB4E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE7E,QAAQ,CAACC,MAAM,CAAC;QAE1D;QACA,IAAID,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACnC;UACAD,QAAQ,CAACuG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACrB,OAAO,IAAIsB,IAAI,CAACvB,CAAC,CAACnG,SAAS,CAAC,CAACqG,OAAO,EAAE,GAAG,IAAIqB,IAAI,CAACtB,CAAC,CAACpG,SAAS,CAAC,CAACqG,OAAO,EAAE;UAC1E,CAAC,CAAC;UAEF9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAE3C;UACA,IAAI,CAAC7E,QAAQ,GAAGA,QAAQ;UACxB4E,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC7E,QAAQ,CAACC,MAAM,CAAC;UAE9E;UACA,IAAI,CAACgE,mBAAmB,GAAG,IAAI;UAC/B,MAAMe,sBAAsB,GAAG,wBAAwB,IAAI,CAACpB,QAAQ,EAAE;UACtEqB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;SACrD,MAAM;UACL;UACA,IAAI,CAAChF,QAAQ,GAAG,EAAE;UAClB4E,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;QAG/D,IAAI,CAACd,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAACP,EAAE,CAAC6B,aAAa,EAAE;QACvB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAAClE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAAChE,QAAQ,GAAG,EAAE;QAElB,IAAI,CAACyD,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGA2D,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAAClF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;IAEvBV,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEvE;IAEA;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjBgB,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAACjB,QAAQ,CAAC;MAC3F,IAAI,CAACJ,UAAU,CAACyF,wBAAwB,CAAC,IAAI,CAACrF,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC6B,SAAS,CAAC;QAC5EuC,IAAI,EAAEA,CAAA,KAAK;UACTpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UAErD;UACA,IAAI,CAAC7E,QAAQ,GAAG,EAAE;UAElB;UACA,IAAI,CAACiE,mBAAmB,GAAG,KAAK;UAChC,MAAMe,sBAAsB,GAAG,wBAAwB,IAAI,CAACpB,QAAQ,EAAE;UACtEqB,YAAY,CAACiE,UAAU,CAAClE,sBAAsB,CAAC;UAE/C;UACA,IAAI,CAAC1D,cAAc,GAAG,IAAI;UAE1B;UACA,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAE3B;UAEA,IAAI,CAACN,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC;QACD0C,KAAK,EAAGA,KAAK,IAAI;UACfrD,OAAO,CAACqD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAE5D;UACA,IAAI,CAACjI,QAAQ,GAAG,EAAE;UAElB;UACA,IAAI,CAACiE,mBAAmB,GAAG,KAAK;UAChC,MAAMe,sBAAsB,GAAG,wBAAwB,IAAI,CAACpB,QAAQ,EAAE;UACtEqB,YAAY,CAACiE,UAAU,CAAClE,sBAAsB,CAAC;UAE/C;UACA,IAAI,CAAC1D,cAAc,GAAG,IAAI;UAE1B;UACA,IAAI,CAAC+C,cAAc,GAAG,KAAK;UAE3B;UACA,IAAI,CAACN,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;UACvB,IAAI,CAACC,cAAc,EAAE;QACvB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACvF,QAAQ,GAAG,CACd;QACE2F,EAAE,EAAE,IAAI,CAACmC,UAAU,EAAE;QACrBlH,IAAI,EAAE,0DAA0D;QAChEF,MAAM,EAAE,KAAK;QACbL,SAAS,EAAE,IAAI0H,IAAI;OACpB,CACF;MAED,IAAI,CAAC1D,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACN,YAAY,GAAG,KAAK;MACzB,IAAI,CAACN,EAAE,CAAC6B,aAAa,EAAE;MACvB,IAAI,CAACC,cAAc,EAAE;;EAEzB;EAEA;EAEA;;;EAGA9F,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACmE,QAAQ,EAAE;MAClB,IAAI,CAACF,QAAQ,CAACgE,IAAI,CAAC,6CAA6C,EAAE,OAAO,EAAE;QACzEC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA,IAAI,CAAC1D,mBAAmB,GAAG,IAAI;IAE/B;IACA,MAAMe,sBAAsB,GAAG,wBAAwB,IAAI,CAACpB,QAAQ,EAAE;IACtEqB,YAAY,CAACG,OAAO,CAACJ,sBAAsB,EAAE,MAAM,CAAC;IAEpD;IACA;IACA,IAAI,CAACK,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC5B,EAAE,CAAC6B,aAAa,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;;EAIQF,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACzB,QAAQ,EAAE;MAClB;;IAGFgB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAE9E;IACA,IAAI,CAACJ,UAAU,CAACiE,WAAW,CAAC,IAAI,CAAC7D,QAAQ,EAAE,2BAA2B,CAAC,CAAC6B,SAAS,CAAC;MAChFuC,IAAI,EAAEA,CAAA,KAAK;QACTpD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D;MACF,CAAC;;MACDoD,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACvE,QAAQ,CAACgE,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EAEA;;;EAGAwB,aAAaA,CAAA;IACXvE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC7E,QAAQ,CAACC,MAAM,CAAC;IAC5D,IAAI,CAACD,QAAQ,CAACoJ,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAI;MACnC1E,OAAO,CAACC,GAAG,CAAC,WAAWyE,KAAK,GAAG,EAAED,GAAG,CAAC1D,EAAE,EAAE0D,GAAG,CAAC3I,MAAM,EAAE2I,GAAG,CAACzI,IAAI,CAACiF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;IACzF,CAAC,CAAC;IAEF;IACA,IAAI,CAACxB,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACS,uBAAuB,EAAE;IAE9B;IACAF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACvD,cAAc,CAAC;IAE5D;IACA,IAAI,CAAC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACsC,QAAQ,EAAE;MACzC,IAAI,CAAC2F,wBAAwB,EAAE;;EAEnC;EAEA;;;EAGAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC3F,QAAQ,EAAE;MAClBgB,OAAO,CAACyC,IAAI,CAAC,uCAAuC,CAAC;MACrD;;IAGFzC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEvE,MAAM4F,QAAQ,GAAG;MACfxG,YAAY,EAAE,CAAC;MACfK,aAAa,EAAE,CACb;QACEf,UAAU,EAAE,iBAAiB;QAC7BC,aAAa,EAAE,4BAA4B;QAC3CN,eAAe,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,aAAa;OAClD,CACF;MACDV,2BAA2B,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MACnDG,iBAAiB,EAAE;QACjBC,eAAe,EAAE,CAAC,aAAa,EAAE,aAAa;OAC/C;MACDsB,YAAY,EAAE;QACZC,cAAc,EAAE;OACjB;MACDC,WAAW,EAAE;QACXC,cAAc,EAAE;;KAEnB;IAED,IAAI,CAACjC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;IAEvB,IAAI,CAAC9B,UAAU,CAACiG,kBAAkB,CAAC,IAAI,CAAC7F,QAAQ,EAAE4F,QAAQ,CAAC,CAAC/D,SAAS,CAAC;MACpEuC,IAAI,EAAG0B,QAAQ,IAAI;QACjB9E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6E,QAAQ,CAAC;QACpD,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACpI,cAAc,GAAGkI,QAAQ;UAC9B,IAAI,CAAC9F,QAAQ,CAACgE,IAAI,CAAC,2CAA2C,EAAE,OAAO,EAAE;YACvEC,QAAQ,EAAE,IAAI;YACdgC,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACL,IAAI,CAACjG,QAAQ,CAACgE,IAAI,CAAC,uCAAuC,EAAE,OAAO,EAAE;YACnEC,QAAQ,EAAE,IAAI;YACdgC,UAAU,EAAE;WACb,CAAC;;QAEJ,IAAI,CAACxI,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACvE,QAAQ,CAACgE,IAAI,CAAC,uCAAuC,EAAE,OAAO,EAAE;UACnEC,QAAQ,EAAE,IAAI;UACdgC,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAACnE,SAAS,CAAC,MAAK;UAC3B,IAAI,CAAC8D,wBAAwB,EAAE;QACjC,CAAC,CAAC;QACF,IAAI,CAACpI,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGArE,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC2C,QAAQ,EAAE;MAClBgB,OAAO,CAACyC,IAAI,CAAC,8CAA8C,CAAC;MAC5D,IAAI,CAAC3D,QAAQ,CAACgE,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;QAC1DC,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF,IAAI,IAAI,CAACxG,YAAY,EAAE;MACrByD,OAAO,CAACyC,IAAI,CAAC,oCAAoC,CAAC;MAClD;;IAGF,IAAI,CAAClG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;IAEvBV,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEpE,IAAI,CAACJ,UAAU,CAACqG,mBAAmB,CAAC,IAAI,CAACjG,QAAQ,CAAC,CAAC6B,SAAS,CAAC;MAC3DuC,IAAI,EAAGf,IAAI,IAAI;QACbrC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoC,IAAI,CAAC;QAC/C,IAAIA,IAAI,EAAE;UACRrC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC;UAC5D,IAAI,CAAC3F,cAAc,GAAG2F,IAAI;UAC1B,IAAI,CAACvD,QAAQ,CAACgE,IAAI,CAAC,wCAAwC,EAAE,OAAO,EAAE;YACpEC,QAAQ,EAAE,IAAI;YACdgC,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACL/E,OAAO,CAACyC,IAAI,CAAC,sCAAsC,CAAC;UACpD,IAAI,CAAC3D,QAAQ,CAACgE,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;YAC1DC,QAAQ,EAAE;WACX,CAAC;;QAEJ,IAAI,CAACxG,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;QACvBV,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACvD,cAAc,CAAC;MACrE,CAAC;MACD2G,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACvE,QAAQ,CAACgE,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;UAC/DC,QAAQ,EAAE,IAAI;UACdgC,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAACnE,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACxE,qBAAqB,EAAE;QAC9B,CAAC,CAAC;QACF,IAAI,CAACE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAmE,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC7F,QAAQ,IAAI,CAAC,IAAI,CAACtC,cAAc,IAAI,IAAI,CAACH,YAAY,EAAE;MAC/D;;IAGF,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;IAEvBV,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACjB,QAAQ,CAAC;IAEhE,IAAI,CAACJ,UAAU,CAACiG,kBAAkB,CAAC,IAAI,CAAC7F,QAAQ,EAAE,IAAI,CAACtC,cAAc,CAAC,CAACmE,SAAS,CAAC;MAC/EuC,IAAI,EAAG0B,QAAQ,IAAI;QACjB9E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6E,QAAQ,CAAC;QAC/C,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAChG,QAAQ,CAACgE,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;YAChEC,QAAQ,EAAE,IAAI;YACdgC,UAAU,EAAE;WACb,CAAC;SACH,MAAM;UACL,IAAI,CAACjG,QAAQ,CAACgE,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;YAC5DC,QAAQ,EAAE,IAAI;YACdgC,UAAU,EAAE;WACb,CAAC;;QAEJ,IAAI,CAACxI,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;MACzB,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfrD,OAAO,CAACqD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACvE,QAAQ,CAACgE,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAC5DC,QAAQ,EAAE,IAAI;UACdgC,UAAU,EAAE;SACb,CAAC,CAACC,QAAQ,EAAE,CAACnE,SAAS,CAAC,MAAK;UAC3B,IAAI,CAACgE,kBAAkB,EAAE;QAC3B,CAAC,CAAC;QACF,IAAI,CAACtI,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAwE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAClG,QAAQ,IAAI,IAAI,CAACzC,YAAY,IAAI,CAAC,IAAI,CAACG,cAAc,EAAE;MAC/D;;IAGF;IACA,MAAMyI,SAAS,GAAG,IAAI,CAACpG,MAAM,CAAC+D,IAAI,CAAC9I,sBAAsB,EAAE;MACzDoL,KAAK,EAAE,OAAO;MACd/C,IAAI,EAAE;QACJgD,KAAK,EAAE,wBAAwB;QAC/BvE,OAAO,EAAE,oFAAoF;QAC7FwE,iBAAiB,EAAE,QAAQ;QAC3BC,gBAAgB,EAAE;;KAErB,CAAC;IAEFJ,SAAS,CAACK,WAAW,EAAE,CAAC3E,SAAS,CAAC4E,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV;QACA,IAAI,CAAClJ,YAAY,GAAG,IAAI;QACxB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;QAEvBV,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACjB,QAAQ,CAAC;QAElE,IAAI,CAACJ,UAAU,CAACsG,oBAAoB,CAAC,IAAI,CAAClG,QAAQ,CAAC,CAAC6B,SAAS,CAAC;UAC5DuC,IAAI,EAAG0B,QAAQ,IAAI;YACjB9E,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6E,QAAQ,CAAC;YACjD,IAAIA,QAAQ,EAAE;cACZ,IAAI,CAACpI,cAAc,GAAG,IAAI;cAC1B,IAAI,CAACoC,QAAQ,CAACgE,IAAI,CAAC,sCAAsC,EAAE,OAAO,EAAE;gBAClEC,QAAQ,EAAE,IAAI;gBACdgC,UAAU,EAAE;eACb,CAAC;aACH,MAAM;cACL,IAAI,CAACjG,QAAQ,CAACgE,IAAI,CAAC,kCAAkC,EAAE,OAAO,EAAE;gBAC9DC,QAAQ,EAAE,IAAI;gBACdgC,UAAU,EAAE;eACb,CAAC;;YAEJ,IAAI,CAACxI,YAAY,GAAG,KAAK;YACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;UACzB,CAAC;UACD2C,KAAK,EAAGA,KAAK,IAAI;YACfrD,OAAO,CAACqD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAACvE,QAAQ,CAACgE,IAAI,CAAC,kCAAkC,EAAE,OAAO,EAAE;cAC9DC,QAAQ,EAAE,IAAI;cACdgC,UAAU,EAAE;aACb,CAAC,CAACC,QAAQ,EAAE,CAACnE,SAAS,CAAC,MAAK;cAC3B,IAAI,CAACqE,oBAAoB,EAAE;YAC7B,CAAC,CAAC;YACF,IAAI,CAAC3I,YAAY,GAAG,KAAK;YACzB,IAAI,CAACsC,EAAE,CAAC6B,aAAa,EAAE;UACzB;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;;;uBAjzBWhC,gBAAgB,EAAAxE,EAAA,CAAAwL,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA1L,EAAA,CAAAwL,iBAAA,CAAAxL,EAAA,CAAA2L,iBAAA,GAAA3L,EAAA,CAAAwL,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA7L,EAAA,CAAAwL,iBAAA,CAAAM,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAhBvH,gBAAgB;MAAAwH,SAAA;MAAAC,MAAA;QAAAnH,QAAA;QAAAC,UAAA;MAAA;MAAAmH,UAAA;MAAAC,QAAA,GAAAnM,EAAA,CAAAoM,oBAAA,EAAApM,EAAA,CAAAqM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1C7B3M,EAAA,CAAAC,cAAA,aAAyB;UAGvBD,EAAA,CAAAY,UAAA,IAAAiM,+BAAA,kBAWM;UAEN7M,EAAA,CAAAC,cAAA,aAAyB;UAEOD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,aAA0B;UAEwBD,EAAA,CAAAI,UAAA,mBAAA0M,mDAAA;YAAA,OAASF,GAAA,CAAAzC,wBAAA,EAA0B;UAAA,EAAC;UAClFnK,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE5BH,EAAA,CAAAC,cAAA,iBAAgG;UAA5CD,EAAA,CAAAI,UAAA,mBAAA2M,mDAAA;YAAA,OAASH,GAAA,CAAAvC,aAAA,EAAe;UAAA,EAAC;UAC3ErK,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKrCH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAY,UAAA,KAAAoM,gCAAA,kBAOM;UAGNhN,EAAA,CAAAY,UAAA,KAAAqM,gCAAA,kBASM;UACRjN,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UAGbD,EAAA,CAAAI,UAAA,2BAAA8M,0DAAAC,MAAA;YAAA,OAAAP,GAAA,CAAA5H,cAAA,GAAAmI,MAAA;UAAA,EAA4B,qBAAAC,oDAAAD,MAAA;YAAA,OAEjBP,GAAA,CAAAxD,UAAA,CAAA+D,MAAA,CAAkB;UAAA,EAFD;UADnCnN,EAAA,CAAAG,YAAA,EAIiC;UAEnCH,EAAA,CAAAC,cAAA,kBAAiH;UAA5ED,EAAA,CAAAI,UAAA,mBAAAiN,mDAAA;YAAA,OAAST,GAAA,CAAAjE,WAAA,EAAa;UAAA,EAAC;UAC1D3I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7BH,EAAA,CAAAC,cAAA,eAAmC;UAGnBD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjCH,EAAA,CAAAC,cAAA,eAA0B;UACkCD,EAAA,CAAAI,UAAA,mBAAAkN,mDAAA;YAAA,OAASV,GAAA,CAAAjC,kBAAA,EAAoB;UAAA,EAAC;UACtF3K,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE3BH,EAAA,CAAAC,cAAA,kBAA8J;UAAjGD,EAAA,CAAAI,UAAA,mBAAAmN,mDAAA;YAAA,OAASX,GAAA,CAAAzK,qBAAA,EAAuB;UAAA,EAAC;UAC5FnC,EAAA,CAAAC,cAAA,gBAA0C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE9DH,EAAA,CAAAC,cAAA,kBAA8K;UAAlHD,EAAA,CAAAI,UAAA,mBAAAoN,mDAAA;YAAA,OAASZ,GAAA,CAAA5B,oBAAA,EAAsB;UAAA,EAAC;UAC1FhL,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAKjCH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAY,UAAA,KAAA6M,gCAAA,mBAOM;UAGNzN,EAAA,CAAAY,UAAA,KAAA8M,yCAAA,4BA6De;UACjB1N,EAAA,CAAAG,YAAA,EAAM;;;UAhKmBH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,UAAA4L,GAAA,CAAAzH,mBAAA,CAA0B;UA8B1BnF,EAAA,CAAAe,SAAA,IAAa;UAAbf,EAAA,CAAAgB,UAAA,YAAA4L,GAAA,CAAA1L,QAAA,CAAa,iBAAA0L,GAAA,CAAA3C,SAAA;UAUhCjK,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAA4L,GAAA,CAAA1H,oBAAA,CAA0B;UAevBlF,EAAA,CAAAe,SAAA,GAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,YAAA4L,GAAA,CAAA5H,cAAA,CAA4B,aAAA4H,GAAA,CAAA3H,YAAA;UAKwBjF,EAAA,CAAAe,SAAA,GAAmD;UAAnDf,EAAA,CAAAgB,UAAA,cAAA4L,GAAA,CAAA5H,cAAA,CAAAgC,IAAA,MAAA4F,GAAA,CAAA3H,YAAA,CAAmD;UAcejF,EAAA,CAAAe,SAAA,IAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,aAAA4L,GAAA,CAAAvK,YAAA,KAAAuK,GAAA,CAAApK,cAAA,CAA4C;UAGnCxC,EAAA,CAAAe,SAAA,GAAyB;UAAzBf,EAAA,CAAAgB,UAAA,aAAA4L,GAAA,CAAAvK,YAAA,CAAyB;UACjJrC,EAAA,CAAAe,SAAA,GAA+B;UAA/Bf,EAAA,CAAA2N,WAAA,aAAAf,GAAA,CAAAvK,YAAA,CAA+B;UAEsFrC,EAAA,CAAAe,SAAA,GAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,aAAA4L,GAAA,CAAAvK,YAAA,KAAAuK,GAAA,CAAApK,cAAA,CAA4C;UAQjJxC,EAAA,CAAAe,SAAA,GAAqB;UAArBf,EAAA,CAAAgB,UAAA,UAAA4L,GAAA,CAAApK,cAAA,CAAqB;UAUpCxC,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAAgB,UAAA,SAAA4L,GAAA,CAAApK,cAAA,CAAoB;;;qBD9ErCrD,YAAY,EAAAyO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ5O,WAAW,EAAA6O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX/O,mBAAmB,EACnBC,eAAe,EAAA+O,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,gBAAA,EACfjP,aAAa,EACbC,kBAAkB,EAAAiP,EAAA,CAAAC,YAAA,EAClBjP,aAAa,EAAAkP,EAAA,CAAAC,OAAA,EACblP,cAAc,EAAAmP,EAAA,CAAAC,QAAA,EACdnP,wBAAwB,EACxBC,gBAAgB,EAAAmP,GAAA,CAAAC,UAAA,EAChBnP,eAAe,EAEfE,YAAY;MAAAkP,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAOH1K,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}