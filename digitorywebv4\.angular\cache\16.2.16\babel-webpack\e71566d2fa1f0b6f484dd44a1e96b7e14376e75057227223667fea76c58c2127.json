{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/forms\";\nfunction SmartDashboardComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const i_r7 = restoredCtx.index;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onTabChange(i_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tab_r6.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r6.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.getActiveFiltersCount());\n  }\n}\nfunction SmartDashboardComponent_div_28_mat_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r14.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_28_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDate_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDate_r15.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDate_r15.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"label\", 29)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Restaurants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 30)(7, \"mat-select\", 31);\n    i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_div_28_Template_mat_select_valueChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.selectedLocations = $event);\n    });\n    i0.ɵɵtemplate(8, SmartDashboardComponent_div_28_mat_option_8_Template, 2, 2, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"label\", 29)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Base Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 30)(15, \"mat-select\", 33);\n    i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_div_28_Template_mat_select_valueChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.selectedBaseDate = $event);\n    });\n    i0.ɵɵtemplate(16, SmartDashboardComponent_div_28_mat_option_16_Template, 2, 2, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 28)(18, \"label\", 29)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"date_range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Start Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 30)(23, \"input\", 34);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_28_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.startDate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"mat-datepicker-toggle\", 35)(25, \"mat-datepicker\", null, 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"label\", 29)(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"date_range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" End Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 30)(33, \"input\", 37);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_28_Template_input_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.endDate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"mat-datepicker-toggle\", 35)(35, \"mat-datepicker\", null, 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(26);\n    const _r13 = i0.ɵɵreference(36);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedLocations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.locations);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedBaseDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.baseDates);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"matDatepicker\", _r12)(\"ngModel\", ctx_r2.startDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r12);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"matDatepicker\", _r13)(\"ngModel\", ctx_r2.endDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r13);\n  }\n}\nfunction SmartDashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"mat-form-field\", 42)(3, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function SmartDashboardComponent_div_30_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.chatMessage = $event);\n    })(\"keydown\", function SmartDashboardComponent_div_30_Template_input_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onKeyPress($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_30_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.sendMessage());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"send\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Generate \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.chatMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.chatMessage.trim());\n  }\n}\nfunction SmartDashboardComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"mat-icon\", 47);\n    i0.ɵɵtext(3, \"insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Ready to Generate Insights\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Configure your filters and ask the AI assistant to generate visualizations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48)(9, \"div\", 49)(10, \"mat-icon\", 50);\n    i0.ɵɵtext(11, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Interactive Charts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"mat-icon\", 50);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Smart Insights\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 49)(20, \"mat-icon\", 50);\n    i0.ɵɵtext(21, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Natural Language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 49)(25, \"mat-icon\", 50);\n    i0.ɵɵtext(26, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Real-time Analysis\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SmartDashboardComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵtext(4, \"Chart 1\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 53)(6, \"div\", 54);\n    i0.ɵɵtext(7, \"Chart 2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 53)(9, \"div\", 54);\n    i0.ɵɵtext(10, \"Chart 3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 53)(12, \"div\", 54);\n    i0.ɵɵtext(13, \"Chart 4\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor() {\n    this.isFiltersOpen = false;\n    this.selectedTab = 0;\n    this.hasGeneratedCharts = false;\n    // GRN Filter options\n    this.locations = [{\n      value: 'restaurant1',\n      label: 'Main Branch Restaurant',\n      checked: false\n    }, {\n      value: 'restaurant2',\n      label: 'Downtown Branch',\n      checked: false\n    }, {\n      value: 'restaurant3',\n      label: 'Mall Branch',\n      checked: false\n    }, {\n      value: 'restaurant4',\n      label: 'Airport Branch',\n      checked: false\n    }, {\n      value: 'restaurant5',\n      label: 'City Center Branch',\n      checked: false\n    }];\n    this.baseDates = [{\n      value: 'today',\n      label: 'Today'\n    }, {\n      value: 'yesterday',\n      label: 'Yesterday'\n    }, {\n      value: 'last_week',\n      label: 'Last Week'\n    }, {\n      value: 'last_month',\n      label: 'Last Month'\n    }, {\n      value: 'custom',\n      label: 'Custom Date'\n    }];\n    // Selected values for GRN filters\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.chatMessage = '';\n    // Tab data\n    this.tabs = [{\n      label: 'GRN Report',\n      active: true\n    }, {\n      label: 'Purchase Report',\n      active: false\n    }, {\n      label: 'Sales Report',\n      active: false\n    }, {\n      label: 'Inventory Report',\n      active: false\n    }];\n  }\n  ngOnInit() {}\n  toggleFilters() {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n  onTabChange(index) {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n  sendMessage() {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n      this.chatMessage = '';\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  applyFilters() {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n  resetFilters() {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 16,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"dashboard-header\"], [1, \"tabs-section\"], [1, \"tabs-container\"], [\"class\", \"tab-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"controls-section\"], [\"mat-button\", \"\", 1, \"toggle-btn\", 3, \"click\"], [\"class\", \"filter-count\", 4, \"ngIf\"], [1, \"expand-icon\"], [1, \"quick-actions\"], [\"mat-button\", \"\", 1, \"reset-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"apply-btn\", 3, \"click\"], [1, \"filters-section\"], [\"class\", \"filters-content\", 4, \"ngIf\"], [1, \"ai-section\"], [\"class\", \"ai-content\", 4, \"ngIf\"], [1, \"dashboard-charts\"], [1, \"charts-header\"], [1, \"charts-icon\"], [1, \"charts-actions\"], [\"mat-icon-button\", \"\", 1, \"fullscreen-btn\", 3, \"click\"], [\"mat-icon-button\", \"\", 1, \"refresh-btn\", 3, \"click\"], [1, \"charts-content\"], [\"class\", \"charts-placeholder\", 4, \"ngIf\"], [\"class\", \"generated-charts\", 4, \"ngIf\"], [1, \"tab-button\", 3, \"click\"], [1, \"filter-count\"], [1, \"filters-content\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", 3, \"value\", \"valueChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"valueChange\"], [\"matInput\", \"\", \"placeholder\", \"Select start date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Select end date\", \"readonly\", \"\", 3, \"matDatepicker\", \"ngModel\", \"ngModelChange\"], [\"endPicker\", \"\"], [3, \"value\"], [1, \"ai-content\"], [1, \"ai-input-container\"], [\"appearance\", \"outline\", 1, \"ai-input-field\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"e.g., Show me sales trends for the last quarter by region\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"charts-placeholder\"], [1, \"placeholder-content\"], [1, \"placeholder-icon\"], [1, \"feature-cards\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"generated-charts\"], [1, \"chart-grid\"], [1, \"chart-item\"], [1, \"chart-placeholder\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, SmartDashboardComponent_button_4_Template, 2, 3, \"button\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_6_listener() {\n            return ctx.toggleFilters();\n          });\n          i0.ɵɵelementStart(7, \"mat-icon\");\n          i0.ɵɵtext(8, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \" Filters \");\n          i0.ɵɵtemplate(10, SmartDashboardComponent_span_10_Template, 2, 1, \"span\", 7);\n          i0.ɵɵelementStart(11, \"mat-icon\", 8);\n          i0.ɵɵtext(12, \"expand_more\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_13_listener() {\n            return ctx.toggleAI();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"psychology\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" AI Assistant \");\n          i0.ɵɵelementStart(17, \"mat-icon\", 8);\n          i0.ɵɵtext(18, \"expand_more\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_20_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"refresh\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_23_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"check\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Apply \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 12);\n          i0.ɵɵtemplate(28, SmartDashboardComponent_div_28_Template, 37, 10, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 14);\n          i0.ɵɵtemplate(30, SmartDashboardComponent_div_30_Template, 8, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 16)(32, \"div\", 17)(33, \"mat-icon\", 18);\n          i0.ɵɵtext(34, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"h3\");\n          i0.ɵɵtext(36, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 19)(38, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_38_listener() {\n            return ctx.toggleFullscreen();\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"fullscreen\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_41_listener() {\n            return ctx.refreshCharts();\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 22);\n          i0.ɵɵtemplate(45, SmartDashboardComponent_div_45_Template, 29, 0, \"div\", 23);\n          i0.ɵɵtemplate(46, SmartDashboardComponent_div_46_Template, 14, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.getActiveFiltersCount() > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"expanded\", ctx.isFiltersExpanded);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"expanded\", ctx.isAIExpanded);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"collapsed\", !ctx.isFiltersExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFiltersExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"collapsed\", !ctx.isAIExpanded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAIExpanded);\n          i0.ɵɵadvance(14);\n          i0.ɵɵclassProp(\"fullscreen\", ctx.isFullscreen);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasGeneratedCharts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasGeneratedCharts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatCardModule, MatButtonModule, i2.MatButton, i2.MatIconButton, MatIconModule, i3.MatIcon, MatFormFieldModule, i4.MatFormField, i4.MatSuffix, MatSelectModule, i5.MatSelect, i6.MatOption, MatInputModule, i7.MatInput, MatCheckboxModule, MatTabsModule, MatSidenavModule, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  background-color: #f8f9fa;\\n  \\n\\n}\\n[_nghost-%COMP%]   *[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n.smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  min-height: 100vh;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.report-tabs[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 0 20px;\\n  flex-shrink: 0;\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  font-size: 13px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  border-bottom: 2px solid transparent;\\n  transition: all 0.2s ease;\\n  height: 36px;\\n  border-radius: 6px 6px 0 0;\\n  margin-right: 4px;\\n}\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: #f8f9fa;\\n}\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  border-bottom-color: #2196f3;\\n  background-color: #f8f9fa;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px 20px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.filters-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n  border-radius: 10px;\\n  padding: 2px 6px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  min-width: 18px;\\n  text-align: center;\\n  margin-left: 4px;\\n  line-height: 1.2;\\n}\\n\\n.filters-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.reset-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  height: 32px;\\n  padding: 0 12px;\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n  font-size: 13px;\\n  border-radius: 6px;\\n}\\n.reset-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #d0d0d0;\\n}\\n.reset-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.apply-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  height: 32px;\\n  padding: 0 16px;\\n  background-color: #2196f3;\\n  color: white;\\n  font-size: 13px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n}\\n.apply-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.apply-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.filters-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\\n  gap: 16px;\\n  max-width: 1200px;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #444;\\n  margin-bottom: 2px;\\n}\\n.filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  color: #666;\\n}\\n\\n.filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  max-height: 32px;\\n  overflow: hidden;\\n  font-size: 13px;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-datepicker-toggle {\\n  color: #666;\\n}\\n.filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-icon-suffix {\\n  padding-left: 6px;\\n}\\n.filter-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.checkbox-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 8px;\\n}\\n\\n.filter-checkbox[_ngcontent-%COMP%]     .mat-mdc-checkbox {\\n  --mdc-checkbox-state-layer-size: 40px;\\n}\\n.filter-checkbox[_ngcontent-%COMP%]     .mdc-form-field {\\n  font-size: 14px;\\n  color: #333;\\n}\\n\\n\\n\\n.ai-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 1px solid #e0e0e0;\\n  padding: 16px 20px;\\n}\\n\\n.ai-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.ai-header[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 8px;\\n}\\n.ai-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.ai-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 13px;\\n  color: #666;\\n  line-height: 1.4;\\n}\\n\\n.ai-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  max-width: 700px;\\n  margin: 0 auto;\\n  align-items: flex-start;\\n}\\n\\n.ai-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  height: 40px;\\n  border-radius: 6px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  font-size: 13px;\\n}\\n.ai-input-field[_ngcontent-%COMP%]     input {\\n  font-size: 13px;\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  height: 40px;\\n  padding: 0 16px;\\n  background-color: #2196f3;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n}\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #e0e0e0;\\n  color: #999;\\n}\\n.send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1976d2;\\n}\\n.send-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.dashboard-charts[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  padding: 16px 20px;\\n}\\n\\n.charts-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.charts-header[_ngcontent-%COMP%]   .charts-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.charts-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.charts-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n  min-height: 350px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.charts-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 350px;\\n  padding: 32px 20px;\\n}\\n\\n.placeholder-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #ddd;\\n  margin-bottom: 16px;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.feature-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-top: 16px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e8e8e8;\\n  border-radius: 6px;\\n  padding: 12px 8px;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n  border-color: #ddd;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: #ff9800;\\n  margin-bottom: 6px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.generated-charts[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  min-height: 350px;\\n  \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 12px;\\n  }\\n  .ai-input-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 8px;\\n  }\\n  .send-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n  .tabs-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    -webkit-overflow-scrolling: touch;\\n    padding-bottom: 2px;\\n  }\\n  .tab-button[_ngcontent-%COMP%] {\\n    white-space: nowrap;\\n    min-width: 100px;\\n    font-size: 12px;\\n    padding: 6px 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .filters-section[_ngcontent-%COMP%], .ai-section[_ngcontent-%COMP%], .dashboard-charts[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .filters-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n  .feature-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .filters-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .reset-btn[_ngcontent-%COMP%], .apply-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 36px;\\n  }\\n  .charts-placeholder[_ngcontent-%COMP%] {\\n    min-height: 280px;\\n    padding: 20px 16px;\\n  }\\n}\\n\\n\\n.tab-button[_ngcontent-%COMP%]:focus, .send-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n.ai-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n\\n\\n.reset-btn[_ngcontent-%COMP%]:focus, .apply-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2196f3;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.loading[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  pointer-events: none;\\n}\\n\\n\\n\\n.feature-card[_ngcontent-%COMP%], .charts-content[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.charts-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatSelectModule", "MatInputModule", "MatCheckboxModule", "MatTabsModule", "MatSidenavModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SmartDashboardComponent_button_4_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "i_r7", "index", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onTabChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "tab_r6", "active", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtextInterpolate", "ctx_r1", "getActiveFiltersCount", "ɵɵproperty", "location_r14", "value", "baseDate_r15", "SmartDashboardComponent_div_28_Template_mat_select_valueChange_7_listener", "$event", "_r17", "ctx_r16", "selectedLocations", "ɵɵtemplate", "SmartDashboardComponent_div_28_mat_option_8_Template", "SmartDashboardComponent_div_28_Template_mat_select_valueChange_15_listener", "ctx_r18", "selectedBaseDate", "SmartDashboardComponent_div_28_mat_option_16_Template", "SmartDashboardComponent_div_28_Template_input_ngModelChange_23_listener", "ctx_r19", "startDate", "ɵɵelement", "SmartDashboardComponent_div_28_Template_input_ngModelChange_33_listener", "ctx_r20", "endDate", "ctx_r2", "locations", "baseDates", "_r12", "_r13", "SmartDashboardComponent_div_30_Template_input_ngModelChange_3_listener", "_r22", "ctx_r21", "chatMessage", "SmartDashboardComponent_div_30_Template_input_keydown_3_listener", "ctx_r23", "onKeyPress", "SmartDashboardComponent_div_30_Template_button_click_4_listener", "ctx_r24", "sendMessage", "ctx_r3", "trim", "SmartDashboardComponent", "constructor", "isFiltersOpen", "selectedTab", "hasGeneratedCharts", "checked", "tabs", "ngOnInit", "toggleFilters", "for<PERSON>ach", "tab", "i", "console", "log", "event", "key", "shift<PERSON>ey", "preventDefault", "applyFilters", "resetFilters", "location", "count", "length", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_button_4_Template", "SmartDashboardComponent_Template_button_click_6_listener", "SmartDashboardComponent_span_10_Template", "SmartDashboardComponent_Template_button_click_13_listener", "toggleAI", "SmartDashboardComponent_Template_button_click_20_listener", "SmartDashboardComponent_Template_button_click_23_listener", "SmartDashboardComponent_div_28_Template", "SmartDashboardComponent_div_30_Template", "SmartDashboardComponent_Template_button_click_38_listener", "toggleFullscreen", "SmartDashboardComponent_Template_button_click_41_listener", "refresh<PERSON><PERSON><PERSON>", "SmartDashboardComponent_div_45_Template", "SmartDashboardComponent_div_46_Template", "isFiltersExpanded", "isAIExpanded", "isFullscreen", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "MatButton", "MatIconButton", "i3", "MatIcon", "i4", "MatFormField", "MatSuffix", "i5", "MatSelect", "i6", "MatOption", "i7", "MatInput", "i8", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/ai-dashboard/smart-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatInputModule,\n    MatCheckboxModule,\n    MatTabsModule,\n    MatSidenavModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit {\n  isFiltersOpen = false;\n  selectedTab = 0;\n  hasGeneratedCharts = false;\n\n  // GRN Filter options\n  locations = [\n    { value: 'restaurant1', label: 'Main Branch Restaurant', checked: false },\n    { value: 'restaurant2', label: 'Downtown Branch', checked: false },\n    { value: 'restaurant3', label: 'Mall Branch', checked: false },\n    { value: 'restaurant4', label: 'Airport Branch', checked: false },\n    { value: 'restaurant5', label: 'City Center Branch', checked: false }\n  ];\n\n  baseDates = [\n    { value: 'today', label: 'Today' },\n    { value: 'yesterday', label: 'Yesterday' },\n    { value: 'last_week', label: 'Last Week' },\n    { value: 'last_month', label: 'Last Month' },\n    { value: 'custom', label: 'Custom Date' }\n  ];\n\n  // Selected values for GRN filters\n  selectedLocations: string[] = [];\n  selectedBaseDate = 'today';\n  startDate: string = '';\n  endDate: string = '';\n  chatMessage = '';\n\n  // Tab data\n  tabs = [\n    { label: 'GRN Report', active: true },\n    { label: 'Purchase Report', active: false },\n    { label: 'Sales Report', active: false },\n    { label: 'Inventory Report', active: false }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  toggleFilters(): void {\n    this.isFiltersOpen = !this.isFiltersOpen;\n  }\n\n  onTabChange(index: number): void {\n    this.selectedTab = index;\n    this.tabs.forEach((tab, i) => {\n      tab.active = i === index;\n    });\n  }\n\n  sendMessage(): void {\n    if (this.chatMessage.trim()) {\n      // Handle chat message and generate charts\n      console.log('Chat message:', this.chatMessage);\n\n      // Simulate chart generation\n      this.hasGeneratedCharts = true;\n\n      this.chatMessage = '';\n    }\n  }\n\n  onKeyPress(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  applyFilters(): void {\n    // Apply selected filters\n    console.log('Applying filters...');\n    this.isFiltersOpen = false;\n  }\n\n  resetFilters(): void {\n    // Reset GRN filters to default\n    this.selectedLocations = [];\n    this.selectedBaseDate = 'today';\n    this.startDate = '';\n    this.endDate = '';\n    this.locations.forEach(location => location.checked = false);\n  }\n\n  getActiveFiltersCount(): number {\n    let count = 0;\n    if (this.selectedLocations.length > 0) count++;\n    if (this.selectedBaseDate !== 'today') count++;\n    if (this.startDate) count++;\n    if (this.endDate) count++;\n    return count;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Top Header with Tabs and Controls -->\n  <div class=\"dashboard-header\">\n    <!-- Report Type Tabs -->\n    <div class=\"tabs-section\">\n      <div class=\"tabs-container\">\n        <button\n          *ngFor=\"let tab of tabs; let i = index\"\n          class=\"tab-button\"\n          [class.active]=\"tab.active\"\n          (click)=\"onTabChange(i)\"\n        >\n          {{ tab.label }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Collapsible Controls -->\n    <div class=\"controls-section\">\n      <!-- Filters Toggle -->\n      <button mat-button class=\"toggle-btn\" (click)=\"toggleFilters()\">\n        <mat-icon>tune</mat-icon>\n        Filters\n        <span class=\"filter-count\" *ngIf=\"getActiveFiltersCount() > 0\">{{getActiveFiltersCount()}}</span>\n        <mat-icon class=\"expand-icon\" [class.expanded]=\"isFiltersExpanded\">expand_more</mat-icon>\n      </button>\n\n      <!-- AI Assistant Toggle -->\n      <button mat-button class=\"toggle-btn\" (click)=\"toggleAI()\">\n        <mat-icon>psychology</mat-icon>\n        AI Assistant\n        <mat-icon class=\"expand-icon\" [class.expanded]=\"isAIExpanded\">expand_more</mat-icon>\n      </button>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions\">\n        <button mat-button class=\"reset-btn\" (click)=\"resetFilters()\">\n          <mat-icon>refresh</mat-icon>\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"apply-btn\" (click)=\"applyFilters()\">\n          <mat-icon>check</mat-icon>\n          Apply\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Collapsible Filters Section -->\n  <div class=\"filters-section\" [class.collapsed]=\"!isFiltersExpanded\">\n    <div class=\"filters-content\" *ngIf=\"isFiltersExpanded\">\n      <!-- 1. Location Multi-Select -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>location_on</mat-icon>\n          Restaurants\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select [(value)]=\"selectedLocations\" multiple>\n            <mat-option *ngFor=\"let location of locations\" [value]=\"location.value\">\n              {{ location.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- 2. Select Base Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>event</mat-icon>\n          Base Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <mat-select [(value)]=\"selectedBaseDate\">\n            <mat-option *ngFor=\"let baseDate of baseDates\" [value]=\"baseDate.value\">\n              {{ baseDate.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <!-- 3. Start Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>date_range</mat-icon>\n          Start Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <input\n            matInput\n            [matDatepicker]=\"startPicker\"\n            [(ngModel)]=\"startDate\"\n            placeholder=\"Select start date\"\n            readonly\n          >\n          <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #startPicker></mat-datepicker>\n        </mat-form-field>\n      </div>\n\n      <!-- 4. End Date -->\n      <div class=\"filter-group\">\n        <label class=\"filter-label\">\n          <mat-icon>date_range</mat-icon>\n          End Date\n        </label>\n        <mat-form-field appearance=\"outline\" class=\"filter-field\">\n          <input\n            matInput\n            [matDatepicker]=\"endPicker\"\n            [(ngModel)]=\"endDate\"\n            placeholder=\"Select end date\"\n            readonly\n          >\n          <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n          <mat-datepicker #endPicker></mat-datepicker>\n        </mat-form-field>\n      </div>\n    </div>\n  </div>\n\n  <!-- Collapsible AI Assistant Section -->\n  <div class=\"ai-section\" [class.collapsed]=\"!isAIExpanded\">\n    <div class=\"ai-content\" *ngIf=\"isAIExpanded\">\n      <div class=\"ai-input-container\">\n        <mat-form-field appearance=\"outline\" class=\"ai-input-field\">\n          <input\n            matInput\n            type=\"text\"\n            placeholder=\"e.g., Show me sales trends for the last quarter by region\"\n            [(ngModel)]=\"chatMessage\"\n            (keydown)=\"onKeyPress($event)\"\n          >\n        </mat-form-field>\n        <button mat-raised-button color=\"primary\" class=\"send-btn\" (click)=\"sendMessage()\" [disabled]=\"!chatMessage.trim()\">\n          <mat-icon>send</mat-icon>\n          Generate\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Dashboard Charts Area (Primary Focus) -->\n  <div class=\"dashboard-charts\">\n    <div class=\"charts-header\">\n      <mat-icon class=\"charts-icon\">dashboard</mat-icon>\n      <h3>Dashboard</h3>\n      <div class=\"charts-actions\">\n        <button mat-icon-button class=\"fullscreen-btn\" (click)=\"toggleFullscreen()\">\n          <mat-icon>fullscreen</mat-icon>\n        </button>\n        <button mat-icon-button class=\"refresh-btn\" (click)=\"refreshCharts()\">\n          <mat-icon>refresh</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <!-- Charts Content - Main Focus Area -->\n    <div class=\"charts-content\" [class.fullscreen]=\"isFullscreen\">\n      <!-- Placeholder content when no charts are generated -->\n      <div class=\"charts-placeholder\" *ngIf=\"!hasGeneratedCharts\">\n        <div class=\"placeholder-content\">\n          <mat-icon class=\"placeholder-icon\">insights</mat-icon>\n          <h4>Ready to Generate Insights</h4>\n          <p>Configure your filters and ask the AI assistant to generate visualizations.</p>\n\n          <!-- Compact Feature Cards -->\n          <div class=\"feature-cards\">\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">bar_chart</mat-icon>\n              <span>Interactive Charts</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">trending_up</mat-icon>\n              <span>Smart Insights</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">chat</mat-icon>\n              <span>Natural Language</span>\n            </div>\n            <div class=\"feature-card\">\n              <mat-icon class=\"feature-icon\">schedule</mat-icon>\n              <span>Real-time Analysis</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Generated charts will appear here -->\n      <div class=\"generated-charts\" *ngIf=\"hasGeneratedCharts\">\n        <!-- Charts will be dynamically generated here -->\n        <div class=\"chart-grid\">\n          <!-- Sample chart placeholders -->\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 1</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 2</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 3</div>\n          </div>\n          <div class=\"chart-item\">\n            <div class=\"chart-placeholder\">Chart 4</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICPpCC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAExBP,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAJPd,EAAA,CAAAe,WAAA,WAAAC,MAAA,CAAAC,MAAA,CAA2B;IAG3BjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACF;;;;;IAUApB,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAAlCd,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAqB,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA2B;;;;;IAmCtFvB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAC,YAAA,CAAAC,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAM,YAAA,CAAAL,KAAA,MACF;;;;;IAaApB,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAAwB,UAAA,UAAAG,YAAA,CAAAD,KAAA,CAAwB;IACrE1B,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAQ,YAAA,CAAAP,KAAA,MACF;;;;;;IA1BRpB,EAAA,CAAAC,cAAA,cAAuD;IAIvCD,EAAA,CAAAa,MAAA,kBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAChCd,EAAA,CAAAa,MAAA,oBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,yBAA0D;IAC5CD,EAAA,CAAAE,UAAA,yBAAA0B,0EAAAC,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAoB,OAAA,CAAAC,iBAAA,GAAAH,MAAA;IAAA,EAA6B;IACvC7B,EAAA,CAAAiC,UAAA,IAAAC,oDAAA,yBAEa;IACflC,EAAA,CAAAc,YAAA,EAAa;IAKjBd,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAa,MAAA,aAAK;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC1Bd,EAAA,CAAAa,MAAA,mBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAC5CD,EAAA,CAAAE,UAAA,yBAAAiC,2EAAAN,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAM,OAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAyB,OAAA,CAAAC,gBAAA,GAAAR,MAAA;IAAA,EAA4B;IACtC7B,EAAA,CAAAiC,UAAA,KAAAK,qDAAA,yBAEa;IACftC,EAAA,CAAAc,YAAA,EAAa;IAKjBd,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC/Bd,EAAA,CAAAa,MAAA,oBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAItDD,EAAA,CAAAE,UAAA,2BAAAqC,wEAAAV,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAU,OAAA,GAAAxC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA6B,OAAA,CAAAC,SAAA,GAAAZ,MAAA;IAAA,EAAuB;IAHzB7B,EAAA,CAAAc,YAAA,EAMC;IACDd,EAAA,CAAA0C,SAAA,iCAA6E;IAE/E1C,EAAA,CAAAc,YAAA,EAAiB;IAInBd,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC/Bd,EAAA,CAAAa,MAAA,kBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,0BAA0D;IAItDD,EAAA,CAAAE,UAAA,2BAAAyC,wEAAAd,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAyB,IAAA;MAAA,MAAAc,OAAA,GAAA5C,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAiC,OAAA,CAAAC,OAAA,GAAAhB,MAAA;IAAA,EAAqB;IAHvB7B,EAAA,CAAAc,YAAA,EAMC;IACDd,EAAA,CAAA0C,SAAA,iCAA2E;IAE7E1C,EAAA,CAAAc,YAAA,EAAiB;;;;;;IA1DHd,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAwB,UAAA,UAAAsB,MAAA,CAAAd,iBAAA,CAA6B;IACNhC,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAwB,UAAA,YAAAsB,MAAA,CAAAC,SAAA,CAAY;IAcnC/C,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAwB,UAAA,UAAAsB,MAAA,CAAAT,gBAAA,CAA4B;IACLrC,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAwB,UAAA,YAAAsB,MAAA,CAAAE,SAAA,CAAY;IAgB7ChD,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAwB,UAAA,kBAAAyB,IAAA,CAA6B,YAAAH,MAAA,CAAAL,SAAA;IAKEzC,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAwB,UAAA,QAAAyB,IAAA,CAAmB;IAclDjD,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAwB,UAAA,kBAAA0B,IAAA,CAA2B,YAAAJ,MAAA,CAAAD,OAAA;IAKI7C,EAAA,CAAAkB,SAAA,GAAiB;IAAjBlB,EAAA,CAAAwB,UAAA,QAAA0B,IAAA,CAAiB;;;;;;IASxDlD,EAAA,CAAAC,cAAA,cAA6C;IAOrCD,EAAA,CAAAE,UAAA,2BAAAiD,uEAAAtB,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA0C,OAAA,CAAAC,WAAA,GAAAzB,MAAA;IAAA,EAAyB,qBAAA0B,iEAAA1B,MAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAI,OAAA,GAAAxD,EAAA,CAAAU,aAAA;MAAA,OACdV,EAAA,CAAAW,WAAA,CAAA6C,OAAA,CAAAC,UAAA,CAAA5B,MAAA,CAAkB;IAAA,EADJ;IAJ3B7B,EAAA,CAAAc,YAAA,EAMC;IAEHd,EAAA,CAAAC,cAAA,iBAAoH;IAAzDD,EAAA,CAAAE,UAAA,mBAAAwD,gEAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAO,OAAA,GAAA3D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAChF5D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACzBd,EAAA,CAAAa,MAAA,iBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAPLd,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAwB,UAAA,YAAAqC,MAAA,CAAAP,WAAA,CAAyB;IAIsDtD,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAwB,UAAA,cAAAqC,MAAA,CAAAP,WAAA,CAAAQ,IAAA,GAAgC;;;;;IA0BrH9D,EAAA,CAAAC,cAAA,cAA4D;IAErBD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACtDd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,iCAA0B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,kFAA2E;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAGlFd,EAAA,CAAAC,cAAA,cAA2B;IAEQD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACnDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAEjCd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACrDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,sBAAc;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAE7Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,YAAI;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC9Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAE/Bd,EAAA,CAAAC,cAAA,eAA0B;IACOD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClDd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IAOvCd,EAAA,CAAAC,cAAA,cAAyD;IAKpBD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,cAAwB;IACSD,EAAA,CAAAa,MAAA,cAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,cAAwB;IACSD,EAAA,CAAAa,MAAA,eAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EAAA,CAAAC,cAAA,eAAwB;IACSD,EAAA,CAAAa,MAAA,eAAO;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;AD3LxD,MAqBaiD,uBAAuB;EAqClCC,YAAA;IApCA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAApB,SAAS,GAAG,CACV;MAAErB,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,wBAAwB;MAAEgD,OAAO,EAAE;IAAK,CAAE,EACzE;MAAE1C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,iBAAiB;MAAEgD,OAAO,EAAE;IAAK,CAAE,EAClE;MAAE1C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,aAAa;MAAEgD,OAAO,EAAE;IAAK,CAAE,EAC9D;MAAE1C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,gBAAgB;MAAEgD,OAAO,EAAE;IAAK,CAAE,EACjE;MAAE1C,KAAK,EAAE,aAAa;MAAEN,KAAK,EAAE,oBAAoB;MAAEgD,OAAO,EAAE;IAAK,CAAE,CACtE;IAED,KAAApB,SAAS,GAAG,CACV;MAAEtB,KAAK,EAAE,OAAO;MAAEN,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEM,KAAK,EAAE,YAAY;MAAEN,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEM,KAAK,EAAE,QAAQ;MAAEN,KAAK,EAAE;IAAa,CAAE,CAC1C;IAED;IACA,KAAAY,iBAAiB,GAAa,EAAE;IAChC,KAAAK,gBAAgB,GAAG,OAAO;IAC1B,KAAAI,SAAS,GAAW,EAAE;IACtB,KAAAI,OAAO,GAAW,EAAE;IACpB,KAAAS,WAAW,GAAG,EAAE;IAEhB;IACA,KAAAe,IAAI,GAAG,CACL;MAAEjD,KAAK,EAAE,YAAY;MAAEH,MAAM,EAAE;IAAI,CAAE,EACrC;MAAEG,KAAK,EAAE,iBAAiB;MAAEH,MAAM,EAAE;IAAK,CAAE,EAC3C;MAAEG,KAAK,EAAE,cAAc;MAAEH,MAAM,EAAE;IAAK,CAAE,EACxC;MAAEG,KAAK,EAAE,kBAAkB;MAAEH,MAAM,EAAE;IAAK,CAAE,CAC7C;EAEe;EAEhBqD,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACN,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEArD,WAAWA,CAACJ,KAAa;IACvB,IAAI,CAAC0D,WAAW,GAAG1D,KAAK;IACxB,IAAI,CAAC6D,IAAI,CAACG,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC3BD,GAAG,CAACxD,MAAM,GAAGyD,CAAC,KAAKlE,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAoD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACN,WAAW,CAACQ,IAAI,EAAE,EAAE;MAC3B;MACAa,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACtB,WAAW,CAAC;MAE9C;MACA,IAAI,CAACa,kBAAkB,GAAG,IAAI;MAE9B,IAAI,CAACb,WAAW,GAAG,EAAE;;EAEzB;EAEAG,UAAUA,CAACoB,KAAoB;IAC7B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;MACtB,IAAI,CAACpB,WAAW,EAAE;;EAEtB;EAEAqB,YAAYA,CAAA;IACV;IACAN,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACX,aAAa,GAAG,KAAK;EAC5B;EAEAiB,YAAYA,CAAA;IACV;IACA,IAAI,CAAClD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACK,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACI,SAAS,GAAG,EAAE;IACnB,IAAI,CAACI,OAAO,GAAG,EAAE;IACjB,IAAI,CAACE,SAAS,CAACyB,OAAO,CAACW,QAAQ,IAAIA,QAAQ,CAACf,OAAO,GAAG,KAAK,CAAC;EAC9D;EAEA7C,qBAAqBA,CAAA;IACnB,IAAI6D,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACpD,iBAAiB,CAACqD,MAAM,GAAG,CAAC,EAAED,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC/C,gBAAgB,KAAK,OAAO,EAAE+C,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAC3C,SAAS,EAAE2C,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACvC,OAAO,EAAEuC,KAAK,EAAE;IACzB,OAAOA,KAAK;EACd;;;uBA9FWrB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAuB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxF,EAAA,CAAAyF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCpC/F,EAAA,CAAAC,cAAA,aAAuC;UAM/BD,EAAA,CAAAiC,UAAA,IAAAgE,yCAAA,oBAOS;UACXjG,EAAA,CAAAc,YAAA,EAAM;UAIRd,EAAA,CAAAC,cAAA,aAA8B;UAEUD,EAAA,CAAAE,UAAA,mBAAAgG,yDAAA;YAAA,OAASF,GAAA,CAAAzB,aAAA,EAAe;UAAA,EAAC;UAC7DvE,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,gBACA;UAAAb,EAAA,CAAAiC,UAAA,KAAAkE,wCAAA,kBAAiG;UACjGnG,EAAA,CAAAC,cAAA,mBAAmE;UAAAD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAI3Fd,EAAA,CAAAC,cAAA,iBAA2D;UAArBD,EAAA,CAAAE,UAAA,mBAAAkG,0DAAA;YAAA,OAASJ,GAAA,CAAAK,QAAA,EAAU;UAAA,EAAC;UACxDrG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAa,MAAA,sBACA;UAAAb,EAAA,CAAAC,cAAA,mBAA8D;UAAAD,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAItFd,EAAA,CAAAC,cAAA,cAA2B;UACYD,EAAA,CAAAE,UAAA,mBAAAoG,0DAAA;YAAA,OAASN,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAC3DlF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAE9Bd,EAAA,CAAAC,cAAA,kBAAqF;UAAzBD,EAAA,CAAAE,UAAA,mBAAAqG,0DAAA;YAAA,OAASP,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAClFjF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC1Bd,EAAA,CAAAa,MAAA,eACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAMfd,EAAA,CAAAC,cAAA,eAAoE;UAClED,EAAA,CAAAiC,UAAA,KAAAuE,uCAAA,oBAoEM;UACRxG,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,eAA0D;UACxDD,EAAA,CAAAiC,UAAA,KAAAwE,uCAAA,kBAgBM;UACRzG,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,eAA8B;UAEID,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAClDd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAClBd,EAAA,CAAAC,cAAA,eAA4B;UACqBD,EAAA,CAAAE,UAAA,mBAAAwG,0DAAA;YAAA,OAASV,GAAA,CAAAW,gBAAA,EAAkB;UAAA,EAAC;UACzE3G,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAEjCd,EAAA,CAAAC,cAAA,kBAAsE;UAA1BD,EAAA,CAAAE,UAAA,mBAAA0G,0DAAA;YAAA,OAASZ,GAAA,CAAAa,aAAA,EAAe;UAAA,EAAC;UACnE7G,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAMlCd,EAAA,CAAAC,cAAA,eAA8D;UAE5DD,EAAA,CAAAiC,UAAA,KAAA6E,uCAAA,mBA0BM;UAGN9G,EAAA,CAAAiC,UAAA,KAAA8E,uCAAA,mBAiBM;UACR/G,EAAA,CAAAc,YAAA,EAAM;;;UAvMgBd,EAAA,CAAAkB,SAAA,GAAS;UAATlB,EAAA,CAAAwB,UAAA,YAAAwE,GAAA,CAAA3B,IAAA,CAAS;UAgBCrE,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAwB,UAAA,SAAAwE,GAAA,CAAAzE,qBAAA,OAAiC;UAC/BvB,EAAA,CAAAkB,SAAA,GAAoC;UAApClB,EAAA,CAAAe,WAAA,aAAAiF,GAAA,CAAAgB,iBAAA,CAAoC;UAOpChH,EAAA,CAAAkB,SAAA,GAA+B;UAA/BlB,EAAA,CAAAe,WAAA,aAAAiF,GAAA,CAAAiB,YAAA,CAA+B;UAiBtCjH,EAAA,CAAAkB,SAAA,IAAsC;UAAtClB,EAAA,CAAAe,WAAA,eAAAiF,GAAA,CAAAgB,iBAAA,CAAsC;UACnChH,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAwB,UAAA,SAAAwE,GAAA,CAAAgB,iBAAA,CAAuB;UAwE/BhH,EAAA,CAAAkB,SAAA,GAAiC;UAAjClB,EAAA,CAAAe,WAAA,eAAAiF,GAAA,CAAAiB,YAAA,CAAiC;UAC9BjH,EAAA,CAAAkB,SAAA,GAAkB;UAAlBlB,EAAA,CAAAwB,UAAA,SAAAwE,GAAA,CAAAiB,YAAA,CAAkB;UAmCfjH,EAAA,CAAAkB,SAAA,IAAiC;UAAjClB,EAAA,CAAAe,WAAA,eAAAiF,GAAA,CAAAkB,YAAA,CAAiC;UAE1BlH,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAAwB,UAAA,UAAAwE,GAAA,CAAA7B,kBAAA,CAAyB;UA6B3BnE,EAAA,CAAAkB,SAAA,GAAwB;UAAxBlB,EAAA,CAAAwB,UAAA,SAAAwE,GAAA,CAAA7B,kBAAA,CAAwB;;;qBDzKzDhF,YAAY,EAAAgI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjI,aAAa,EACbC,eAAe,EAAAiI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflI,aAAa,EAAAmI,EAAA,CAAAC,OAAA,EACbnI,kBAAkB,EAAAoI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBrI,eAAe,EAAAsI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfxI,cAAc,EAAAyI,EAAA,CAAAC,QAAA,EACdzI,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EAAAuI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBzI,mBAAmB,EACnBC,WAAW,EAAAyI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA;;SAKF7E,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}