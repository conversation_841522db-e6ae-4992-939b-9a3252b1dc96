{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/radio\";\nimport * as i15 from \"@angular/material/button\";\nimport * as i16 from \"@angular/material/card\";\nimport * as i17 from \"@angular/material/progress-bar\";\nimport * as i18 from \"@angular/material/tabs\";\nfunction AccountSetupComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"div\", 52)(3, \"span\", 53);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 54);\n    i0.ɵɵtext(6, \"Loading account...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"img\", 56);\n    i0.ɵɵelementStart(2, \"div\", 57)(3, \"div\", 58)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59);\n    i0.ɵɵtext(7, \"Click to change\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61);\n    i0.ɵɵtext(4, \"Click to upload logo\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 52)(2, \"span\", 63);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_error_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 64);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_6_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 82);\n    i0.ɵɵtext(1, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"Chat Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_6_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 82);\n    i0.ɵɵtext(1, \"dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"Generate Datasets\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74)(2, \"mat-tab-group\", 75);\n    i0.ɵɵlistener(\"selectedIndexChange\", function AccountSetupComponent_mat_card_118_div_6_Template_mat_tab_group_selectedIndexChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.selectedAITab = $event);\n    });\n    i0.ɵɵelementStart(3, \"mat-tab\");\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_118_div_6_ng_template_4_Template, 4, 0, \"ng-template\", 76);\n    i0.ɵɵelementStart(5, \"div\", 77);\n    i0.ɵɵelement(6, \"app-chat-bot\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-tab\");\n    i0.ɵɵtemplate(8, AccountSetupComponent_mat_card_118_div_6_ng_template_8_Template, 4, 0, \"ng-template\", 76);\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"div\", 80)(11, \"p\");\n    i0.ɵɵtext(12, \"Our AI will analyze your restaurant information to create optimized inventory, packaging and vendor datasets.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" This process takes approximately 30-40 minutes to complete.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_118_div_6_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.startAIProcessing());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"play_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Generate Datasets \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selectedIndex\", ctx_r10.selectedAITab);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"tenantId\", ctx_r10.registrationForm.value.tenantId)(\"tenantName\", ctx_r10.registrationForm.value.tenantName);\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r19.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"span\", 53);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_mat_card_118_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100);\n    i0.ɵɵtemplate(2, AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_2_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵtemplate(3, AccountSetupComponent_mat_card_118_div_7_div_16_div_3_Template, 3, 0, \"div\", 101);\n    i0.ɵɵtemplate(4, AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_4_Template, 2, 0, \"mat-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102)(6, \"div\", 103);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 104);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, step_r23.completed, ctx_r22.activeStep === i_r24, !step_r23.completed && ctx_r22.activeStep !== i_r24));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r23.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep === i_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep !== i_r24);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r23.description);\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h3\", 85)(2, \"mat-icon\", 86);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Processing Your Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 87);\n    i0.ɵɵelement(6, \"mat-progress-bar\", 88);\n    i0.ɵɵelementStart(7, \"div\", 89);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 90)(10, \"mat-icon\", 91);\n    i0.ɵɵtext(11, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AccountSetupComponent_mat_card_118_div_7_span_12_Template, 3, 4, \"span\", 20);\n    i0.ɵɵtemplate(13, AccountSetupComponent_mat_card_118_div_7_span_13_Template, 2, 0, \"span\", 20);\n    i0.ɵɵtemplate(14, AccountSetupComponent_mat_card_118_div_7_span_14_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 93);\n    i0.ɵɵtemplate(16, AccountSetupComponent_mat_card_118_div_7_div_16_Template, 10, 10, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 95)(18, \"div\", 96)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 97);\n    i0.ɵɵtext(24, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r11.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining > 0 && ctx_r11.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 107)(2, \"mat-icon\", 108);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 109);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 110)(9, \"mat-card\", 111)(10, \"mat-card-header\")(11, \"div\", 112)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"cloud_download\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-card-title\");\n    i0.ɵɵtext(15, \"Download All Datasets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-card-subtitle\");\n    i0.ɵɵtext(17, \"Get all files in a single ZIP archive\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-actions\")(19, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_118_div_8_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.downloadAll());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"archive\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Download All Files \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 115)(2, \"mat-icon\", 116);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 117);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 118)(9, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_mat_card_118_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.startAIProcessing());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_mat_card_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 65)(1, \"div\", 66)(2, \"mat-icon\", 67);\n    i0.ɵɵtext(3, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 68);\n    i0.ɵɵtext(5, \"Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AccountSetupComponent_mat_card_118_div_6_Template, 21, 3, \"div\", 69);\n    i0.ɵɵtemplate(7, AccountSetupComponent_mat_card_118_div_7_Template, 25, 6, \"div\", 70);\n    i0.ɵɵtemplate(8, AccountSetupComponent_mat_card_118_div_8_Template, 23, 0, \"div\", 71);\n    i0.ɵɵtemplate(9, AccountSetupComponent_mat_card_118_div_9_Template, 13, 0, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.showChatBot && !ctx_r9.isDownloading && !ctx_r9.downloadComplete && !ctx_r9.downloadFailed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isDownloading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.downloadFailed);\n  }\n}\nconst _c1 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c2 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.selectedTabIndex = 0;\n    this.tenantCreated = false;\n    this.aiDataAvailable = false;\n    this.isLoading = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    this.showChatBot = true;\n    this.selectedAITab = 0;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    this.isEditMode = false;\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true;\n      this.prefillData(this.dialogData.elements);\n    } else {\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true;\n          this.isLoading = true;\n          this.cd.detectChanges();\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false;\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n              this.isLoading = false;\n              this.cd.detectChanges();\n            },\n            error: err => {\n              this.isEditMode = false;\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n              this.isLoading = false;\n              this.cd.detectChanges();\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true;\n            this.aiDataAvailable = true;\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  onTabChange(index) {\n    this.selectedTabIndex = index;\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  switchToChatAgent() {\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before using the chat agent');\n      return;\n    }\n    this.selectedAITab = 0;\n    this.cd.detectChanges();\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  switchAITab(tabIndex) {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startAIProcessing();\n    });\n  }\n  downloadAll() {\n    this.api.downloadData('all', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const bytes = new Uint8Array(binaryString.length);\n        for (let i = 0; i < binaryString.length; i++) {\n          bytes[i] = binaryString.charCodeAt(i);\n        }\n        const blob = new Blob([bytes], {\n          type: 'application/zip'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${this.registrationForm.value.tenantId}_inventory.zip`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n        this.snackBar.open(\"All files downloaded successfully!\", \"Close\", {\n          duration: 3000\n        });\n      } catch (error) {\n        console.error(\"Base64 Decoding or ZIP Error:\", error);\n        this.snackBar.open(\"Failed to download files. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download files. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 119,\n      vars: 21,\n      consts: [[\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"form-card\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"status-header\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\", 1, \"compact-radio\"], [\"value\", \"no\", \"color\", \"primary\", 1, \"compact-radio\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-header\"], [1, \"logo-container\"], [1, \"logo-dropzone\", 3, \"click\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [\"class\", \"logo-loading\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [\"class\", \"ai-data-section\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"spinner-container\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"visually-hidden\"], [1, \"loading-text\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-overlay\"], [1, \"overlay-content\"], [1, \"change-text\"], [1, \"logo-placeholder\"], [1, \"upload-text\"], [1, \"logo-loading\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-data-section\"], [1, \"section-header\"], [1, \"section-icon\"], [1, \"section-title\"], [\"class\", \"chat-bot-section\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"chat-bot-section\"], [1, \"ai-data-tabs\"], [\"animationDuration\", \"300ms\", 1, \"compact-tabs\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"mat-tab-label\", \"\"], [1, \"tab-content\"], [3, \"tenantId\", \"tenantName\"], [1, \"tab-content\", \"dataset-tab-content\"], [1, \"dataset-info\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-btn\", 3, \"click\"], [1, \"tab-icon\"], [1, \"tab-label\"], [1, \"ai-processing-panel\"], [1, \"processing-title\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [1, \"icon\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"download-all-container\", \"mb-3\"], [1, \"download-all-card\"], [\"mat-card-avatar\", \"\", 1, \"download-all-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r32 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, AccountSetupComponent_div_0_Template, 7, 0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 4)(10, \"mat-icon\", 5);\n          i0.ɵɵtext(11, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"span\", 6);\n          i0.ɵɵtext(15, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\", 7)(17, \"mat-icon\", 5);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 8)(22, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_22_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"mat-card\", 11)(28, \"mat-card-content\")(29, \"form\", 12)(30, \"h3\", 13);\n          i0.ɵɵtext(31, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 14)(33, \"div\", 15)(34, \"mat-form-field\", 16)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 17);\n          i0.ɵɵelementStart(38, \"mat-icon\", 18);\n          i0.ɵɵtext(39, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"mat-form-field\", 16)(41, \"mat-label\");\n          i0.ɵɵtext(42, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"input\", 19);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_43_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-icon\", 18);\n          i0.ɵɵtext(45, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, AccountSetupComponent_mat_error_46_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 16)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_50_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-icon\", 18);\n          i0.ɵɵtext(52, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(53, AccountSetupComponent_mat_error_53_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 15)(55, \"mat-form-field\", 16)(56, \"mat-label\");\n          i0.ɵɵtext(57, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 22);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_58_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"mat-icon\", 18);\n          i0.ɵɵtext(60, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(61, AccountSetupComponent_mat_error_61_Template, 2, 0, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-form-field\", 16)(63, \"mat-label\");\n          i0.ɵɵtext(64, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 23);\n          i0.ɵɵelementStart(66, \"mat-icon\", 18);\n          i0.ɵɵtext(67, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"mat-form-field\", 16)(69, \"mat-label\");\n          i0.ɵɵtext(70, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(71, \"input\", 24);\n          i0.ɵɵelementStart(72, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_72_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(73, \"mat-icon\");\n          i0.ɵɵtext(74);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 26)(76, \"div\", 27)(77, \"div\", 28)(78, \"div\", 29)(79, \"h4\", 30);\n          i0.ɵɵtext(80, \"Account Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 31)(82, \"div\", 32)(83, \"label\", 33);\n          i0.ɵɵtext(84, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-radio-group\", 34)(86, \"mat-radio-button\", 35);\n          i0.ɵɵtext(87, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-radio-button\", 36);\n          i0.ɵɵtext(89, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 32)(91, \"label\", 33);\n          i0.ɵɵtext(92, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"mat-radio-group\", 37)(94, \"mat-radio-button\", 35);\n          i0.ɵɵtext(95, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"mat-radio-button\", 36);\n          i0.ɵɵtext(97, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(98, \"div\", 32)(99, \"label\", 33);\n          i0.ɵɵtext(100, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"mat-radio-group\", 38)(102, \"mat-radio-button\", 35);\n          i0.ɵɵtext(103, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"mat-radio-button\", 36);\n          i0.ɵɵtext(105, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(106, \"div\", 39)(107, \"div\", 40)(108, \"h4\", 30);\n          i0.ɵɵtext(109, \"Company Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(110, \"div\", 41)(111, \"div\", 42);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_div_click_111_listener() {\n            i0.ɵɵrestoreView(_r32);\n            const _r7 = i0.ɵɵreference(116);\n            return i0.ɵɵresetView(_r7.click());\n          });\n          i0.ɵɵtemplate(112, AccountSetupComponent_div_112_Template, 8, 1, \"div\", 43);\n          i0.ɵɵtemplate(113, AccountSetupComponent_div_113_Template, 5, 0, \"div\", 44);\n          i0.ɵɵtemplate(114, AccountSetupComponent_div_114_Template, 4, 0, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"input\", 46, 47);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_115_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(117, AccountSetupComponent_mat_error_117_Template, 2, 0, \"mat-error\", 48);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(118, AccountSetupComponent_mat_card_118_Template, 10, 4, \"mat-card\", 49);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c2));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(37);\n          i0.ɵɵclassProp(\"has-logo\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataDownload);\n        }\n      },\n      dependencies: [CommonModule, i10.NgClass, i10.NgForOf, i10.NgIf, i10.DecimalPipe, MatIconModule, i11.MatIcon, MatInputModule, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatError, i13.MatSuffix, MatTooltipModule, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i14.MatRadioGroup, i14.MatRadioButton, MatButtonModule, i15.MatButton, i15.MatIconButton, MatCardModule, i16.MatCard, i16.MatCardActions, i16.MatCardAvatar, i16.MatCardContent, i16.MatCardHeader, i16.MatCardSubtitle, i16.MatCardTitle, MatSelectModule, MatProgressBarModule, i17.MatProgressBar, MatTabsModule, i18.MatTabLabel, i18.MatTab, i18.MatTabGroup, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 8px;\\n  text-align: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  padding: 0;\\n  width: 100%;\\n  margin: 20px 0 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 250px;\\n  padding-right: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  padding-left: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .logo-header[_ngcontent-%COMP%] {\\n  align-self: flex-end;\\n  text-align: right;\\n  width: 100%;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  align-self: flex-end;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  border-bottom: 1px solid #ddd;\\n  justify-content: flex-start;\\n  width: 100%;\\n  text-align: left;\\n  padding-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  border-bottom: 1px solid #ddd;\\n  justify-content: flex-end;\\n  width: 100%;\\n  text-align: right;\\n  padding-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin-bottom: 0;\\n  color: #444;\\n  padding-bottom: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n  width: 100%;\\n  align-self: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  text-align: left;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #666;\\n  font-weight: 500;\\n  text-align: left;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label {\\n  margin: 0;\\n  padding: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-container {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-outer-circle, .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-inner-circle {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label-content {\\n  padding-left: 4px;\\n  font-size: 13px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 180px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 140px;\\n  border-radius: 8px;\\n  background-color: #f9f9f9;\\n  position: relative;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n  border: 2px dashed #ddd;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]:hover {\\n  border-color: #f8a055;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%] {\\n  border-style: solid;\\n  border-color: #f8a055;\\n  position: relative;\\n  \\n\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"Edit\\\";\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  background-color: #f8a055;\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 500;\\n  padding: 3px 8px;\\n  border-top-left-radius: 6px;\\n  z-index: 2;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]:hover {\\n  border-color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone.has-logo[_ngcontent-%COMP%]:hover   .logo-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  background-color: rgba(0, 0, 0, 0.6);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n  padding: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0; \\n\\n  transition: all 0.2s ease;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  padding: 10px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   .logo-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   .change-text[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  color: #aaa;\\n  padding: 16px;\\n  text-align: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  width: 36px;\\n  height: 36px;\\n  margin-bottom: 8px;\\n  color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #666;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-loading[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-dropzone[_ngcontent-%COMP%]   .logo-loading[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  color: #f8a055;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  text-align: center;\\n  margin-top: 4px;\\n}\\n\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-top: 2rem;\\n  border-radius: 8px;\\n  background-color: #fafafa;\\n  transition: all 0.3s ease;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .bottomTitles[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1.2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n  font-size: 24px;\\n  vertical-align: middle;\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .processing-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-title[_ngcontent-%COMP%]   .rotating[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n  position: relative;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .estimated-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  padding: 12px;\\n  border-radius: 4px;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.completed-step[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.active-step[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 58, 183, 0.1);\\n  border-left: 4px solid #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row.pending-step[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  opacity: 0.7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-details[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: rgba(33, 150, 243, 0.05);\\n  border-left: 4px solid #2196f3;\\n  border-radius: 4px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-processing-panel[_ngcontent-%COMP%]   .tips-section[_ngcontent-%COMP%]   .tip-content[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-style: italic;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%] {\\n  \\n\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 2px solid #f8a055;\\n  transition: all 0.3s ease;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .download-all-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f8a055;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .download-all-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .inventory-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .packaging-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #673ab7;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding-left: 20px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-complete-panel[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .download-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 16px 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin-right: 12px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  margin: 0;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  font-size: 16px;\\n}\\n.ai-data-section[_ngcontent-%COMP%]   .ai-error-panel[_ngcontent-%COMP%]   .error-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 0;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding-left: 0;\\n    padding-right: 0;\\n    align-items: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%] {\\n    align-items: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%] {\\n    margin-bottom: 1.5rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .download-all-container[_ngcontent-%COMP%]   .download-all-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .download-options[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .ai-data-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .start-ai-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.visually-hidden[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  margin: -1px;\\n  padding: 0;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  border: 0;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  z-index: 9999;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n}\\n@media (min-width: 992px) {\\n  .loading-overlay[_ngcontent-%COMP%] {\\n    padding-left: 120px; \\n\\n  }\\n}\\n@media (max-width: 991px) {\\n  .loading-overlay[_ngcontent-%COMP%] {\\n    padding-left: 0; \\n\\n  }\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  padding: 15px 25px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  margin-right: 10%;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n  border-width: 0.2rem;\\n  color: #f8a055; \\n\\n}\\n\\n.loading-text[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  font-size: 0.9rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.estimated-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: #ccc;\\n}\\n\\n.calculating[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #f7ce2a;\\n  animation: _ngcontent-%COMP%_fadeInOut 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.ai-data-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.section-icon[_ngcontent-%COMP%] {\\n  color: #555;\\n  margin-right: 10px;\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.chat-bot-section[_ngcontent-%COMP%] {\\n  margin: 0;\\n  overflow: hidden;\\n  padding: 0 20px 20px;\\n  background-color: white;\\n}\\n\\n.chat-bot-description[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n\\n\\napp-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 550px;\\n}\\n\\n\\n\\n.dataset-tab-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  min-height: 550px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  text-align: center;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n  color: #f8a055; \\n\\n  font-size: 24px;\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  color: #f8a055; \\n\\n}\\n\\n.dataset-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  color: #555;\\n}\\n\\n.generate-btn[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding: 8px 24px;\\n  font-size: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY3JtLW1hbmFnZW1lbnQvYWNjb3VudC1zZXR1cC9hY2NvdW50LXNldHVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsYUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQUFGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLHlCQUFBO0FBREo7QUFHSTtFQUNFLFdBQUE7RUFDQSxxQkFBQTtFQUNBLDJCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBRE47QUFHTTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGlCQUFBO0FBRFI7QUFJTTtFQUNFLGNBQUE7RUFDQSwwQkFBQTtBQUZSO0FBS007RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUFIUjtBQU9JO0VBQ0UsYUFBQTtFQUNBLFdBQUE7QUFMTjtBQVNJO0VBMUNGO0lBMkNJLGVBQUE7RUFOSjtFQVNNO0lBQ0UsZUFBQTtJQUNBLFlBQUE7SUFDQSxXQUFBO0VBUFI7RUFXSTtJQUNFLGFBQUE7RUFUTjtBQUNGO0FBY0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBWko7QUFjSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBWk47QUFjTTs7RUFFRSxnQkFBQTtBQVpSO0FBbUJJO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLHdDQUFBO0FBakJOO0FBbUJNO0VBQ0UsYUFBQTtBQWpCUjtBQXVCTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQXJCUjtBQTBCUTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBeEJWO0FBMEJVO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUF4Qlo7QUE2QlE7RUFDRSxnQkFBQTtBQTNCVjtBQTZCVTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsTUFBQTtFQUNBLHVCQUFBO0VBQ0EsOEJBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBM0JaO0FBNkJZO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtBQTNCZDtBQTZCYztFQUNFLHNCQUFBO0FBM0JoQjtBQThCYztFQUNFLHNCQUFBO0FBNUJoQjtBQWdDWTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtBQTlCZDtBQWdDYztFQUNFLG9CQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0FBOUJoQjtBQWlDYztFQUNFLG9CQUFBO0FBL0JoQjtBQW1DWTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBakNkO0FBb0NZO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSw2QkFBQTtFQUNBLHlCQUFBO0VBQ0EsV0FBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7QUFsQ2Q7QUFxQ1k7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQW5DZDtBQXVDWTtFQUNFLFdBQUE7RUFDQSxzQkFBQTtBQXJDZDtBQXVDYztFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7QUFyQ2hCO0FBdUNnQjtFQUNFLGNBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBckNsQjtBQXdDZ0I7RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQXRDbEI7QUF5Q29CO0VBQ0UsU0FBQTtFQUNBLFVBQUE7QUF2Q3RCO0FBMENvQjtFQUNFLFlBQUE7RUFDQSxXQUFBO0FBeEN0QjtBQTJDb0I7O0VBRUUsWUFBQTtFQUNBLFdBQUE7QUF6Q3RCO0FBNENvQjtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtBQTFDdEI7QUFrRFk7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFoRGQ7QUFtRGM7RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5Q0FBQTtFQUNBLHVCQUFBO0FBakRoQjtBQW1EZ0I7RUFDRSxxQkFBQTtFQUNBLHlDQUFBO0VBQ0EsMkJBQUE7QUFqRGxCO0FBb0RnQjtFQUNFLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSxrQkFBQTtFQUVBLGdFQUFBO0FBbkRsQjtBQW9Ea0I7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsUUFBQTtFQUNBLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsMkJBQUE7RUFDQSxVQUFBO0FBbERwQjtBQXFEa0I7RUFDRSxxQkFBQTtBQW5EcEI7QUFzRGtCO0VBQ0UsVUFBQTtFQUNBLG9DQUFBO0FBcERwQjtBQXlEZ0I7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7QUF2RGxCO0FBeURrQjtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtBQXZEcEI7QUEyRGtCO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esb0NBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFVBQUEsRUFBQSwwQ0FBQTtFQUNBLHlCQUFBO0FBekRwQjtBQTJEb0I7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0FBekR0QjtBQTJEc0I7RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7QUF6RHhCO0FBNERzQjtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQTFEeEI7QUFpRWdCO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7QUEvRGxCO0FBaUVrQjtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQS9EcEI7QUFrRWtCO0VBQ0UsZUFBQTtFQUNBLFdBQUE7QUFoRXBCO0FBcUVnQjtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDBDQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFuRWxCO0FBcUVrQjtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtBQW5FcEI7QUF5RWM7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0FBdkVoQjs7QUFpRkEsMkJBQUE7QUFDQTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx3Q0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBT0Esd0JBQUE7RUFnQkEseUJBQUE7RUFVQSw0QkFBQTtFQWtJQSwwQkFBQTtFQXlIQSx1QkFBQTtBQXJXRjtBQTJFRTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7QUF6RUo7QUE2RUU7Ozs7RUFJRSxlQUFBO0VBQ0Esa0JBQUE7QUEzRUo7QUE4RUU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7QUE1RUo7QUFnRkU7RUFDRSxpQkFBQTtFQUNBLGVBQUE7QUE5RUo7QUFnRkk7RUFDRSxpQkFBQTtBQTlFTjtBQW1GRTtFQWtERSxrQkFBQTtFQW9EQSx5QkFBQTtBQXJMSjtBQWdGSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0VBT0EsMkNBQUE7QUFwRk47QUErRU07RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUE3RVI7QUFpRk07RUFDRSxvQ0FBQTtBQS9FUjtBQWtGTTtFQUNFO0lBQ0UsdUJBQUE7RUFoRlI7RUFtRk07SUFDRSx5QkFBQTtFQWpGUjtBQUNGO0FBcUZJO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQW5GTjtBQXFGTTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBbkZSO0FBdUZJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLHFCQUFBO0FBckZOO0FBdUZNO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0FBckZSO0FBMEZJO0VBQ0UsY0FBQTtBQXhGTjtBQTBGTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBeEZSO0FBMEZRO0VBQ0Usd0NBQUE7QUF4RlY7QUEwRlU7RUFDRSxjQUFBO0FBeEZaO0FBNEZRO0VBQ0UseUNBQUE7RUFDQSw4QkFBQTtBQTFGVjtBQTZGUTtFQUNFLDZCQUFBO0VBQ0EsWUFBQTtBQTNGVjtBQThGUTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUE1RlY7QUErRlE7RUFDRSxZQUFBO0FBN0ZWO0FBK0ZVO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQTdGWjtBQWdHVTtFQUNFLFdBQUE7RUFDQSxlQUFBO0FBOUZaO0FBcUdJO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsMENBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0FBbkdOO0FBcUdNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFuR1I7QUFxR1E7RUFDRSxpQkFBQTtBQW5HVjtBQXVHTTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtBQXJHUjtBQTJHRTtFQXNCRSwyQkFBQTtBQTlISjtBQXlHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBdkdOO0FBeUdNO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBdkdSO0FBMEdNO0VBQ0UsY0FBQTtFQUNBLFNBQUE7QUF4R1I7QUE0R0k7RUFDRSxtQkFBQTtFQUNBLGVBQUE7QUExR047QUE4R0k7RUFDRSxtQkFBQTtBQTVHTjtBQThHTTtFQUNFLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtBQTVHUjtBQThHUTtFQUNFLDJCQUFBO0VBQ0EseUNBQUE7QUE1R1Y7QUErR1E7RUFDRSxtQkFBQTtBQTdHVjtBQStHVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQTdHWjtBQStHWTtFQUNFLFlBQUE7QUE3R2Q7QUFpSFU7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUEvR1o7QUFrSFU7RUFDRSxXQUFBO0FBaEhaO0FBb0hRO0VBQ0Usc0JBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7QUFsSFY7QUFvSFU7RUFDRSxnQkFBQTtBQWxIWjtBQXdISTtFQUNFLGtCQUFBO0FBdEhOO0FBd0hNO0VBQ0UsWUFBQTtFQUNBLDBCQUFBO0FBdEhSO0FBd0hRO0VBQ0UsMkJBQUE7RUFDQSx5Q0FBQTtBQXRIVjtBQXlIUTtFQUNFLG1CQUFBO0FBdkhWO0FBeUhVOztFQUVFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQXZIWjtBQXlIWTs7RUFDRSxjQUFBO0FBdEhkO0FBNEhVO0VBQ0Usa0JBQUE7QUExSFo7QUE0SFk7RUFDRSxrQkFBQTtFQUNBLGVBQUE7QUExSGQ7QUErSFE7RUFDRSxzQkFBQTtBQTdIVjtBQXFJSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBbklOO0FBcUlNO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0FBbklSO0FBc0lNO0VBQ0UsY0FBQTtFQUNBLFNBQUE7QUFwSVI7QUF3SUk7RUFDRSxtQkFBQTtFQUNBLGVBQUE7QUF0SU47QUF5SUk7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0FBdklOOztBQTRJQSwyQkFBQTtBQUNBO0VBTVk7SUFDRSxzQkFBQTtJQUNBLFVBQUE7RUE5SVo7RUFnSlk7O0lBRUUsV0FBQTtJQUNBLGVBQUE7SUFDQSxnQkFBQTtJQUNBLG1CQUFBO0VBOUlkO0VBaUpZO0lBQ0UsbUJBQUE7RUEvSWQ7RUFpSmM7SUFDRSxrQkFBQTtFQS9JaEI7RUFpSmdCO0lBQ0Usa0JBQUE7RUEvSWxCO0VBb0pZO0lBQ0UsZ0JBQUE7RUFsSmQ7RUE0SkU7SUFDRSxxQkFBQTtFQTFKSjtFQThKUTtJQUNFLFdBQUE7RUE1SlY7RUFtS0k7SUFDRSxtQkFBQTtFQWpLTjtFQXNLSTtJQUNFLFdBQUE7SUFDQSxnQkFBQTtFQXBLTjtBQUNGO0FBeUtBLCtCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQXZLRjs7QUEwS0E7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFVBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQXZLRjs7QUEwS0Esc0NBQUE7QUFDQTtFQUNFLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsMENBQUE7RUFDQSxhQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFFQSxtRUFBQTtBQXhLRjtBQXlLRTtFQWJGO0lBY0ksbUJBQUEsRUFBQSwyREFBQTtFQXRLRjtBQUNGO0FBd0tFO0VBakJGO0lBa0JJLGVBQUEsRUFBQSx3REFBQTtFQXJLRjtBQUNGOztBQXdLQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLGlCQUFBO0FBcktGOztBQXdLQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7RUFDQSxjQUFBLEVBQUEscUNBQUE7QUFyS0Y7O0FBd0tBO0VBQ0UsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQXJLRjs7QUF5S0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUF0S0Y7O0FBeUtBO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0NBQUE7QUF0S0Y7O0FBeUtBO0VBRUU7SUFFRSxZQUFBO0VBeEtGO0VBMktBO0lBQ0UsVUFBQTtFQXpLRjtBQUNGO0FBNEtBLHdDQUFBO0FBQ0E7RUFDRSxnQkFBQTtBQTFLRjs7QUE2S0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdDQUFBO0FBMUtGOztBQTZLQTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQTFLRjs7QUE2S0E7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtBQTFLRjs7QUE2S0E7RUFDRSxTQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtFQUNBLHVCQUFBO0FBMUtGOztBQThLQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0NBQUE7QUEzS0Y7O0FBOEtBLDhDQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsYUFBQTtBQTNLRjs7QUE4S0Esd0JBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUEzS0Y7O0FBOEtBO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQTNLRjs7QUE4S0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQSxFQUFBLGdCQUFBO0VBQ0EsZUFBQTtBQTNLRjs7QUE4S0E7RUFDRSxrQkFBQTtFQUNBLGNBQUEsRUFBQSxnQkFBQTtBQTNLRjs7QUE4S0E7RUFDRSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUEzS0Y7O0FBOEtBO0VBQ0UsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7QUEzS0YiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBNYWluIGNvbnRhaW5lciBzdHlsZXNcbi5hY2NvdW50LXNldHVwLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIG1heC13aWR0aDogMTQwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcblxuICAvLyBCcmVhZGNydW1icyBzdHlsZXNcbiAgLmJyZWFkY3J1bWJzIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMTJweDtcbiAgICBmb250LXNpemU6IDEzcHg7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTtcbiAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgYm94LXNoYWRvdzogMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTRlODtcblxuICAgIC5icmVhZGNydW1iLWl0ZW0ge1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjJzIGVhc2U7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAgICAgLmJyZWFkY3J1bWItaWNvbiB7XG4gICAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgICAgaGVpZ2h0OiAxOHB4O1xuICAgICAgICB3aWR0aDogMThweDtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7XG4gICAgICB9XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBjb2xvcjogIzNmNTFiNTtcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgICB9XG5cbiAgICAgICYuYWN0aXZlIHtcbiAgICAgICAgY29sb3I6ICMzZjUxYjU7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmJyZWFkY3J1bWItc2VwYXJhdG9yIHtcbiAgICAgIG1hcmdpbjogMCA4cHg7XG4gICAgICBjb2xvcjogIzk5OTtcbiAgICB9XG5cbiAgICAvLyBSZXNwb25zaXZlIHN0eWxlcyBmb3IgYnJlYWRjcnVtYnNcbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcblxuICAgICAgLmJyZWFkY3J1bWItaXRlbSB7XG4gICAgICAgIC5icmVhZGNydW1iLWljb24ge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmJyZWFkY3J1bWItc2VwYXJhdG9yIHtcbiAgICAgICAgbWFyZ2luOiAwIDRweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDb21wYWN0IGhlYWRlclxuICAuY29tcGFjdC1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcblxuICAgIC5oZWFkZXItYWN0aW9ucyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiA4cHg7XG5cbiAgICAgIC5zYXZlLWJ1dHRvbixcbiAgICAgIC5jYW5jZWwtYnV0dG9uIHtcbiAgICAgICAgbWluLXdpZHRoOiAxMDBweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDb250ZW50IHNlY3Rpb25cbiAgLmNvbnRlbnQtc2VjdGlvbiB7XG4gICAgLmZvcm0tY2FyZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgICAgbWF0LWNhcmQtY29udGVudCB7XG4gICAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRm9ybSBzdHlsZXNcbiAgICAuYWNjb3VudC1mb3JtIHtcbiAgICAgIC5mb3JtLXNlY3Rpb24tdGl0bGUge1xuICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIG1hcmdpbjogMCAwIDE2cHggMDtcbiAgICAgICAgY29sb3I6ICM1NTU7XG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlO1xuICAgICAgICBwYWRkaW5nLWJvdHRvbTogOHB4O1xuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICB9XG5cbiAgICAgIC8vIENvbXBhY3QgZm9ybSBncmlkXG4gICAgICAuY29tcGFjdC1mb3JtLWdyaWQge1xuICAgICAgICAuZm9ybS1yb3cge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuXG4gICAgICAgICAgLmZvcm0tZmllbGQge1xuICAgICAgICAgICAgZmxleDogMTtcbiAgICAgICAgICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFNldHRpbmdzIHNlY3Rpb24gc3R5bGVzXG4gICAgICAgIC5zZXR0aW5ncy1zZWN0aW9uIHtcbiAgICAgICAgICBtYXJnaW4tdG9wOiAxMHB4O1xuXG4gICAgICAgICAgLnR3by1jb2x1bW4tZ3JpZCB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgICAgICAgZ2FwOiAwO1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICBtYXJnaW46IDIwcHggMCAwO1xuXG4gICAgICAgICAgICAubGVmdC1jb2x1bW4ge1xuICAgICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgICBtaW4td2lkdGg6IDI1MHB4O1xuICAgICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiAwO1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuXG4gICAgICAgICAgICAgIC5zdGF0dXMtaGVhZGVyIHtcbiAgICAgICAgICAgICAgICBhbGlnbi1zZWxmOiBmbGV4LXN0YXJ0O1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLnN0YXR1cy1vcHRpb25zIHtcbiAgICAgICAgICAgICAgICBhbGlnbi1zZWxmOiBmbGV4LXN0YXJ0O1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5yaWdodC1jb2x1bW4ge1xuICAgICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDA7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgICAgICAgICAgICAgLmxvZ28taGVhZGVyIHtcbiAgICAgICAgICAgICAgICBhbGlnbi1zZWxmOiBmbGV4LWVuZDtcbiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDtcbiAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC5sb2dvLWNvbnRhaW5lciB7XG4gICAgICAgICAgICAgICAgYWxpZ24tc2VsZjogZmxleC1lbmQ7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnN0YXR1cy1oZWFkZXIge1xuICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2RkZDtcbiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdDtcbiAgICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDhweDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLmxvZ28taGVhZGVyIHtcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNkZGQ7XG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDtcbiAgICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDhweDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLnNlY3Rpb24tbGFiZWwge1xuICAgICAgICAgICAgICBmb250LXNpemU6IDE1cHg7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgICAgICAgICAgIGNvbG9yOiAjNDQ0O1xuICAgICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogNHB4O1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBTdGF0dXMgb3B0aW9ucyBzdHlsZXNcbiAgICAgICAgICAgIC5zdGF0dXMtb3B0aW9ucyB7XG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICBhbGlnbi1zZWxmOiBmbGV4LXN0YXJ0O1xuXG4gICAgICAgICAgICAgIC5zdGF0dXMtb3B0aW9uIHtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XG5cbiAgICAgICAgICAgICAgICAuc3RhdHVzLWxhYmVsIHtcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdDtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAuc3RhdHVzLXJhZGlvLWdyb3VwIHtcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgICBnYXA6IDIwcHg7XG5cbiAgICAgICAgICAgICAgICAgIC5jb21wYWN0LXJhZGlvIHtcbiAgICAgICAgICAgICAgICAgICAgOjpuZy1kZWVwIC5tYXQtcmFkaW8tbGFiZWwge1xuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgOjpuZy1kZWVwIC5tYXQtcmFkaW8tY29udGFpbmVyIHtcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDE2cHg7XG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDE2cHg7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICA6Om5nLWRlZXAgLm1hdC1yYWRpby1vdXRlci1jaXJjbGUsXG4gICAgICAgICAgICAgICAgICAgIDo6bmctZGVlcCAubWF0LXJhZGlvLWlubmVyLWNpcmNsZSB7XG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxNnB4O1xuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxNnB4O1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgOjpuZy1kZWVwIC5tYXQtcmFkaW8tbGFiZWwtY29udGVudCB7XG4gICAgICAgICAgICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiA0cHg7XG4gICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIExvZ28gY29udGFpbmVyIHN0eWxlc1xuICAgICAgICAgICAgLmxvZ28tY29udGFpbmVyIHtcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgICAgICAgZ2FwOiAxMHB4O1xuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxODBweDtcblxuICAgICAgICAgICAgICAvLyBJbnRlcmFjdGl2ZSBsb2dvIGRyb3B6b25lXG4gICAgICAgICAgICAgIC5sb2dvLWRyb3B6b25lIHtcbiAgICAgICAgICAgICAgICB3aWR0aDogMTQwcHg7XG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAxNDBweDtcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4gICAgICAgICAgICAgICAgYm9yZGVyOiAycHggZGFzaGVkICNkZGQ7XG5cbiAgICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2Y4YTA1NTtcbiAgICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICYuaGFzLWxvZ28ge1xuICAgICAgICAgICAgICAgICAgYm9yZGVyLXN0eWxlOiBzb2xpZDtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2Y4YTA1NTtcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAgICAgICAgICAgLyogQWRkIGEgc21hbGwgZWRpdCBidXR0b24gaW4gdGhlIGNvcm5lciB0aGF0J3MgYWx3YXlzIHZpc2libGUgKi9cbiAgICAgICAgICAgICAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgICAgICAgICAgICAgY29udGVudDogJ0VkaXQnO1xuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgICAgICAgICAgICAgIGJvdHRvbTogMDtcbiAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IDA7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGEwNTU7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMXB4O1xuICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAzcHggOHB4O1xuICAgICAgICAgICAgICAgICAgICBib3JkZXItdG9wLWxlZnQtcmFkaXVzOiA2cHg7XG4gICAgICAgICAgICAgICAgICAgIHotaW5kZXg6IDI7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNmOGEwNTU7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICY6aG92ZXIgLmxvZ28tb3ZlcmxheSB7XG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDE7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42KTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyBMb2dvIHByZXZpZXdcbiAgICAgICAgICAgICAgICAubG9nby1wcmV2aWV3IHtcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAgICAgICAgICAgaW1nIHtcbiAgICAgICAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgICAgICAgICAgICBvYmplY3QtZml0OiBjb250YWluO1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiA4cHg7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgIC8vIEVkaXQgb3ZlcmxheVxuICAgICAgICAgICAgICAgICAgLmxvZ28tb3ZlcmxheSB7XG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgICAgICAgICAgdG9wOiAwO1xuICAgICAgICAgICAgICAgICAgICBsZWZ0OiAwO1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwOyAvKiBIaWRkZW4gYnkgZGVmYXVsdCwgYnV0IHNob3duIG9uIGhvdmVyICovXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG5cbiAgICAgICAgICAgICAgICAgICAgLm92ZXJsYXktY29udGVudCB7XG4gICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEwcHg7XG5cbiAgICAgICAgICAgICAgICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMjRweDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjRweDtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAuY2hhbmdlLXRleHQge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gUGxhY2Vob2xkZXJcbiAgICAgICAgICAgICAgICAubG9nby1wbGFjZWhvbGRlciB7XG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjYWFhO1xuICAgICAgICAgICAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcblxuICAgICAgICAgICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDM2cHg7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAzNnB4O1xuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDM2cHg7XG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGEwNTU7XG4gICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgIC51cGxvYWQtdGV4dCB7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gTG9hZGluZyBzdGF0ZVxuICAgICAgICAgICAgICAgIC5sb2dvLWxvYWRpbmcge1xuICAgICAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICAgICAgICAgICAgdG9wOiAwO1xuICAgICAgICAgICAgICAgICAgbGVmdDogMDtcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgICAgICAgICAgLnNwaW5uZXItYm9yZGVyIHtcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDJyZW07XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogMnJlbTtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGEwNTU7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLy8gRXJyb3IgbWVzc2FnZVxuICAgICAgICAgICAgICAubG9nby1lcnJvciB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiA0cHg7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyogQUkgRGF0YSBTZWN0aW9uIFN0eWxlcyAqL1xuLmFpLWRhdGEtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgbWFyZ2luLXRvcDogMnJlbTtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gIC5ib3R0b21UaXRsZXMge1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgZm9udC1zaXplOiAxLjJyZW07XG4gIH1cblxuICAvKiBDb21tb24gcGFuZWwgc3R5bGVzICovXG4gIC5haS1pbnRyby1wYW5lbCxcbiAgLmFpLXByb2Nlc3NpbmctcGFuZWwsXG4gIC5haS1jb21wbGV0ZS1wYW5lbCxcbiAgLmFpLWVycm9yLXBhbmVsIHtcbiAgICBwYWRkaW5nOiAxLjVyZW07XG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICB9XG5cbiAgLmFpLWljb24ge1xuICAgIGNvbG9yOiAjNjczYWI3O1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xuICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICB9XG5cbiAgLyogU3RhcnQgYnV0dG9uIHN0eWxpbmcgKi9cbiAgLnN0YXJ0LWFpLWJ0biB7XG4gICAgcGFkZGluZzogOHB4IDI0cHg7XG4gICAgZm9udC1zaXplOiAxNnB4O1xuXG4gICAgbWF0LWljb24ge1xuICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgfVxuICB9XG5cbiAgLyogUHJvY2Vzc2luZyBwYW5lbCBzdHlsZXMgKi9cbiAgLmFpLXByb2Nlc3NpbmctcGFuZWwge1xuICAgIC5wcm9jZXNzaW5nLXRpdGxlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuXG4gICAgICAucHJvY2Vzc2luZy1pY29uIHtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4O1xuICAgICAgICBjb2xvcjogIzY3M2FiNztcbiAgICAgIH1cblxuICAgICAgLyogUm90YXRpb24gYW5pbWF0aW9uIGZvciBwcm9jZXNzaW5nIGljb24gKi9cbiAgICAgIC5yb3RhdGluZyB7XG4gICAgICAgIGFuaW1hdGlvbjogcm90YXRlIDJzIGxpbmVhciBpbmZpbml0ZTtcbiAgICAgIH1cblxuICAgICAgQGtleWZyYW1lcyByb3RhdGUge1xuICAgICAgICBmcm9tIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHRvIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnByb2dyZXNzLWNvbnRhaW5lciB7XG4gICAgICBtYXJnaW46IDEuNXJlbSAwO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAucHJvZ3Jlc3MtbGFiZWwge1xuICAgICAgICBtYXJnaW4tdG9wOiA4cHg7XG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZXN0aW1hdGVkLXRpbWUge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcblxuICAgICAgbWF0LWljb24ge1xuICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgfVxuICAgIH1cblxuICAgIC8qIFN0ZXBzIHN0eWxpbmcgKi9cbiAgICAucHJvY2Vzc2luZy1zdGVwcyB7XG4gICAgICBtYXJnaW46IDJyZW0gMDtcblxuICAgICAgLnN0ZXAtcm93IHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgICAgICAgcGFkZGluZzogMTJweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgICAgICYuY29tcGxldGVkLXN0ZXAge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzYsIDE3NSwgODAsIDAuMSk7XG5cbiAgICAgICAgICAuc3RlcC1zdGF0dXMgbWF0LWljb24ge1xuICAgICAgICAgICAgY29sb3I6ICM0Y2FmNTA7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgJi5hY3RpdmUtc3RlcCB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMDMsIDU4LCAxODMsIDAuMSk7XG4gICAgICAgICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjNjczYWI3O1xuICAgICAgICB9XG5cbiAgICAgICAgJi5wZW5kaW5nLXN0ZXAge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgICAgICAgIG9wYWNpdHk6IDAuNztcbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLXN0YXR1cyB7XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNnB4O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLWRldGFpbHMge1xuICAgICAgICAgIGZsZXgtZ3JvdzogMTtcblxuICAgICAgICAgIC5zdGVwLW5hbWUge1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuc3RlcC1kZXNjcmlwdGlvbiB7XG4gICAgICAgICAgICBjb2xvcjogIzY2NjtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvKiBUaXBzIHNlY3Rpb24gc3R5bGluZyAqL1xuICAgIC50aXBzLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luLXRvcDogMnJlbTtcbiAgICAgIHBhZGRpbmc6IDFyZW07XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDMzLCAxNTAsIDI0MywgMC4wNSk7XG4gICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMyMTk2ZjM7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG5cbiAgICAgIC50aXAtaGVhZGVyIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgY29sb3I6ICMyMTk2ZjM7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcblxuICAgICAgICBtYXQtaWNvbiB7XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLnRpcC1jb250ZW50IHtcbiAgICAgICAgY29sb3I6ICM1NTU7XG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKiBDb21wbGV0ZSBwYW5lbCBzdHlsZXMgKi9cbiAgLmFpLWNvbXBsZXRlLXBhbmVsIHtcbiAgICAuc3VjY2Vzcy1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAgIC5zdWNjZXNzLWljb24ge1xuICAgICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4O1xuICAgICAgfVxuXG4gICAgICBoMyB7XG4gICAgICAgIGNvbG9yOiAjNGNhZjUwO1xuICAgICAgICBtYXJnaW46IDA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnN1Y2Nlc3MtbWVzc2FnZSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgIH1cblxuICAgIC8qIERvd25sb2FkIEFsbCBDb250YWluZXIgKi9cbiAgICAuZG93bmxvYWQtYWxsLWNvbnRhaW5lciB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuXG4gICAgICAuZG93bmxvYWQtYWxsLWNhcmQge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZjhhMDU1O1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1jYXJkLWhlYWRlciB7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgICAgICAgIC5kb3dubG9hZC1hbGwtaWNvbiB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4YTA1NTtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcblxuICAgICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgbWF0LWNhcmQtdGl0bGUge1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgIGNvbG9yOiAjMzMzO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIG1hdC1jYXJkLXN1YnRpdGxlIHtcbiAgICAgICAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1jYXJkLWFjdGlvbnMge1xuICAgICAgICAgIHBhZGRpbmc6IDhweCAxNnB4IDE2cHg7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgICAgIGJ1dHRvbiB7XG4gICAgICAgICAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5kb3dubG9hZC1vcHRpb25zIHtcbiAgICAgIG1hcmdpbi10b3A6IDEuNXJlbTtcblxuICAgICAgLmRvd25sb2FkLWNhcmQge1xuICAgICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDZweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1jYXJkLWhlYWRlciB7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgICAgICAgIC5pbnZlbnRvcnktaWNvbixcbiAgICAgICAgICAucGFja2FnaW5nLWljb24ge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG5cbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgY29sb3I6ICM2NzNhYjc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgbWF0LWNhcmQtY29udGVudCB7XG4gICAgICAgICAgdWwge1xuICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAyMHB4O1xuXG4gICAgICAgICAgICBsaSB7XG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIG1hdC1jYXJkLWFjdGlvbnMge1xuICAgICAgICAgIHBhZGRpbmc6IDhweCAxNnB4IDE2cHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKiBFcnJvciBwYW5lbCBzdHlsZXMgKi9cbiAgLmFpLWVycm9yLXBhbmVsIHtcbiAgICAuZXJyb3ItaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuXG4gICAgICAuZXJyb3ItaWNvbiB7XG4gICAgICAgIGNvbG9yOiAjZjQ0MzM2O1xuICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gICAgICB9XG5cbiAgICAgIGgzIHtcbiAgICAgICAgY29sb3I6ICNmNDQzMzY7XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZXJyb3ItbWVzc2FnZSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgIH1cblxuICAgIC5lcnJvci1hY3Rpb25zIHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIG1hcmdpbi10b3A6IDFyZW07XG4gICAgfVxuICB9XG59XG5cbi8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuYWNjb3VudC1zZXR1cC1jb250YWluZXIge1xuICAgIC5jb250ZW50LXNlY3Rpb24ge1xuICAgICAgLmFjY291bnQtZm9ybSB7XG4gICAgICAgIC5jb21wYWN0LWZvcm0tZ3JpZCB7XG4gICAgICAgICAgLnNldHRpbmdzLXNlY3Rpb24ge1xuICAgICAgICAgICAgLnR3by1jb2x1bW4tZ3JpZCB7XG4gICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgICAgIHBhZGRpbmc6IDA7XG5cbiAgICAgICAgICAgICAgLmxlZnQtY29sdW1uLFxuICAgICAgICAgICAgICAucmlnaHQtY29sdW1uIHtcbiAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDA7XG4gICAgICAgICAgICAgICAgcGFkZGluZy1yaWdodDogMDtcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLnN0YXR1cy1vcHRpb25zIHtcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuXG4gICAgICAgICAgICAgICAgLnN0YXR1cy1vcHRpb24ge1xuICAgICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuXG4gICAgICAgICAgICAgICAgICAuc3RhdHVzLWxhYmVsIHtcbiAgICAgICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC5yaWdodC1jb2x1bW4ge1xuICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDIwcHg7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuYWktZGF0YS1zZWN0aW9uIHtcbiAgICAuZG93bmxvYWQtYWxsLWNvbnRhaW5lciB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAgIC5kb3dubG9hZC1hbGwtY2FyZCB7XG4gICAgICAgIG1hdC1jYXJkLWFjdGlvbnMge1xuICAgICAgICAgIGJ1dHRvbiB7XG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZG93bmxvYWQtb3B0aW9ucyB7XG4gICAgICAuY29sLW1kLTYge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5haS1pbnRyby1wYW5lbCB7XG4gICAgICAuc3RhcnQtYWktYnRuIHtcbiAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgIG1hcmdpbi10b3A6IDFyZW07XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8qIEFkZGl0aW9uYWwgdXRpbGl0eSBjbGFzc2VzICovXG4uc3Bpbm5lci1ib3JkZXItc20ge1xuICB3aWR0aDogMjBweDtcbiAgaGVpZ2h0OiAyMHB4O1xufVxuXG4udmlzdWFsbHktaGlkZGVuIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB3aWR0aDogMXB4O1xuICBoZWlnaHQ6IDFweDtcbiAgbWFyZ2luOiAtMXB4O1xuICBwYWRkaW5nOiAwO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBjbGlwOiByZWN0KDAsIDAsIDAsIDApO1xuICBib3JkZXI6IDA7XG59XG5cbi8qIE1haW4gY29udGVudCBhcmVhIGxvYWRpbmcgb3ZlcmxheSAqL1xuLmxvYWRpbmctb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7XG4gIHotaW5kZXg6IDk5OTk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuXG4gIC8qIENlbnRlciBpbiB0aGUgbWFpbiBjb250ZW50IGFyZWEgYnkgb2Zmc2V0dGluZyBmcm9tIHRoZSBzaWRlYmFyICovXG4gIEBtZWRpYSAobWluLXdpZHRoOiA5OTJweCkge1xuICAgIHBhZGRpbmctbGVmdDogMTIwcHg7IC8qIEhhbGYgb2YgdGhlIHNpZGViYXIgd2lkdGggdG8gY2VudGVyIGluIHJlbWFpbmluZyBzcGFjZSAqL1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDk5MXB4KSB7XG4gICAgcGFkZGluZy1sZWZ0OiAwOyAvKiBObyBvZmZzZXQgb24gbW9iaWxlIHdoZW4gc2lkZWJhciBtaWdodCBiZSBjb2xsYXBzZWQgKi9cbiAgfVxufVxuXG4uc3Bpbm5lci1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDE1cHggMjVweDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgbWFyZ2luLXJpZ2h0OiAxMCU7XG59XG5cbi5zcGlubmVyLWJvcmRlciB7XG4gIHdpZHRoOiAycmVtO1xuICBoZWlnaHQ6IDJyZW07XG4gIGJvcmRlci13aWR0aDogMC4ycmVtO1xuICBjb2xvcjogI2Y4YTA1NTsgLyogTWlsZCBvcmFuZ2UgY29sb3IgdG8gbWF0Y2ggdGhlbWUgKi9cbn1cblxuLmxvYWRpbmctdGV4dCB7XG4gIG1hcmdpbi10b3A6IDAuNzVyZW07XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBjb2xvcjogIzY2NjtcbiAgZm9udC13ZWlnaHQ6IDQwMDtcbn1cblxuXG4uZXN0aW1hdGVkLXRpbWUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBjb2xvcjogI2NjYztcbn1cblxuLmNhbGN1bGF0aW5nIHtcbiAgZm9udC1zdHlsZTogaXRhbGljO1xuICBjb2xvcjogI2Y3Y2UyYTtcbiAgYW5pbWF0aW9uOiBmYWRlSW5PdXQgMS41cyBpbmZpbml0ZTtcbn1cblxuQGtleWZyYW1lcyBmYWRlSW5PdXQge1xuXG4gIDAlLFxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAwLjY7XG4gIH1cblxuICA1MCUge1xuICAgIG9wYWNpdHk6IDE7XG4gIH1cbn1cblxuLyogQUkgRGF0YSBHZW5lcmF0aW9uIHdpdGggVGFicyBTdHlsZXMgKi9cbi5haS1kYXRhLXNlY3Rpb24ge1xuICBtYXJnaW4tdG9wOiAyMHB4O1xufVxuXG4uc2VjdGlvbi1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAxNnB4IDIwcHg7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTBlMGUwO1xufVxuXG4uc2VjdGlvbi1pY29uIHtcbiAgY29sb3I6ICM1NTU7XG4gIG1hcmdpbi1yaWdodDogMTBweDtcbiAgZm9udC1zaXplOiAyNHB4O1xuICBoZWlnaHQ6IDI0cHg7XG4gIHdpZHRoOiAyNHB4O1xufVxuXG4uc2VjdGlvbi10aXRsZSB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBmb250LXdlaWdodDogNTAwO1xuICBjb2xvcjogIzMzMztcbn1cblxuLmNoYXQtYm90LXNlY3Rpb24ge1xuICBtYXJnaW46IDA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHBhZGRpbmc6IDAgMjBweCAyMHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcbn1cblxuXG4uY2hhdC1ib3QtZGVzY3JpcHRpb24ge1xuICBwYWRkaW5nOiAxNXB4IDIwcHg7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM1NTU7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XG59XG5cbi8qIE1ha2Ugc3VyZSB0aGUgY2hhdCBib3QgaGFzIGEgZml4ZWQgaGVpZ2h0ICovXG5hcHAtY2hhdC1ib3Qge1xuICBkaXNwbGF5OiBibG9jaztcbiAgaGVpZ2h0OiA1NTBweDtcbn1cblxuLyogRGF0YXNldCB0YWIgc3R5bGluZyAqL1xuLmRhdGFzZXQtdGFiLWNvbnRlbnQge1xuICBwYWRkaW5nOiAyNHB4O1xuICBtaW4taGVpZ2h0OiA1NTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbi5kYXRhc2V0LWluZm8ge1xuICBtYXgtd2lkdGg6IDYwMHB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5kYXRhc2V0LWluZm8gaDMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgY29sb3I6ICNmOGEwNTU7IC8qIE1pbGQgb3JhbmdlICovXG4gIGZvbnQtc2l6ZTogMjRweDtcbn1cblxuLmRhdGFzZXQtaW5mbyBtYXQtaWNvbiB7XG4gIG1hcmdpbi1yaWdodDogMTBweDtcbiAgY29sb3I6ICNmOGEwNTU7IC8qIE1pbGQgb3JhbmdlICovXG59XG5cbi5kYXRhc2V0LWluZm8gcCB7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgY29sb3I6ICM1NTU7XG59XG5cbi5nZW5lcmF0ZS1idG4ge1xuICBtYXJnaW4tdG9wOiAyNHB4O1xuICBwYWRkaW5nOiA4cHggMjRweDtcbiAgZm9udC1zaXplOiAxNnB4O1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "MatTabsModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_mat_card_118_div_6_Template_mat_tab_group_selectedIndexChange_2_listener", "$event", "ɵɵrestoreView", "_r17", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "selectedAITab", "ɵɵtemplate", "AccountSetupComponent_mat_card_118_div_6_ng_template_4_Template", "AccountSetupComponent_mat_card_118_div_6_ng_template_8_Template", "AccountSetupComponent_mat_card_118_div_6_Template_button_click_17_listener", "ctx_r18", "startAIProcessing", "ctx_r10", "registrationForm", "value", "tenantId", "tenantName", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r19", "estimatedTimeRemaining", "AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_2_Template", "AccountSetupComponent_mat_card_118_div_7_div_16_div_3_Template", "AccountSetupComponent_mat_card_118_div_7_div_16_mat_icon_4_Template", "ɵɵpureFunction3", "_c0", "step_r23", "completed", "ctx_r22", "activeStep", "i_r24", "ɵɵtextInterpolate", "name", "description", "AccountSetupComponent_mat_card_118_div_7_span_12_Template", "AccountSetupComponent_mat_card_118_div_7_span_13_Template", "AccountSetupComponent_mat_card_118_div_7_span_14_Template", "AccountSetupComponent_mat_card_118_div_7_div_16_Template", "ctx_r11", "downloadProgress", "downloadSteps", "AccountSetupComponent_mat_card_118_div_8_Template_button_click_19_listener", "_r29", "ctx_r28", "downloadAll", "AccountSetupComponent_mat_card_118_div_9_Template_button_click_9_listener", "_r31", "ctx_r30", "AccountSetupComponent_mat_card_118_div_6_Template", "AccountSetupComponent_mat_card_118_div_7_Template", "AccountSetupComponent_mat_card_118_div_8_Template", "AccountSetupComponent_mat_card_118_div_9_Template", "ctx_r9", "showChatBot", "isDownloading", "downloadComplete", "downloadFailed", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "selectedTabIndex", "tenantCreated", "aiDataAvailable", "isLoading", "showDataDownload", "user", "getCurrentUser", "baseData", "getBaseData", "isDuplicate", "key", "group", "required", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "detectChanges", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "onTabChange", "index", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "switchToChatAgent", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "switchAITab", "tabIndex", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "bytes", "Uint8Array", "charCodeAt", "blob", "Blob", "window", "URL", "createObjectURL", "a", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_div_0_Template", "AccountSetupComponent_Template_button_click_22_listener", "AccountSetupComponent_Template_input_keyup_43_listener", "AccountSetupComponent_mat_error_46_Template", "AccountSetupComponent_Template_input_keyup_50_listener", "AccountSetupComponent_mat_error_53_Template", "AccountSetupComponent_Template_input_keyup_58_listener", "AccountSetupComponent_mat_error_61_Template", "AccountSetupComponent_Template_button_click_72_listener", "AccountSetupComponent_Template_div_click_111_listener", "_r32", "_r7", "ɵɵreference", "AccountSetupComponent_div_112_Template", "AccountSetupComponent_div_113_Template", "AccountSetupComponent_div_114_Template", "AccountSetupComponent_Template_input_change_115_listener", "AccountSetupComponent_mat_error_117_Template", "AccountSetupComponent_mat_card_118_Template", "ɵɵpureFunction0", "_c1", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵclassProp", "touched", "i10", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i11", "MatIcon", "i12", "MatInput", "i13", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i14", "MatRadioGroup", "MatRadioButton", "i15", "MatButton", "MatIconButton", "i16", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i17", "MatProgressBar", "i18", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    MatTabsModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n  selectedTabIndex: number = 0;\n  tenantCreated: boolean = false;\n  aiDataAvailable: boolean = false;\n  isLoading: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n  showChatBot: boolean = true; \n  selectedAITab: number = 0; \n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    this.isEditMode = false;\n\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true;\n      this.prefillData(this.dialogData.elements);\n    } else {\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true;\n          this.isLoading = true;\n          this.cd.detectChanges();\n\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false;\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n              this.isLoading = false;\n              this.cd.detectChanges();\n            },\n            error: (err) => {\n              this.isEditMode = false;\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n              this.isLoading = false;\n              this.cd.detectChanges();\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.tenantCreated = true;\n            this.aiDataAvailable = true;\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  onTabChange(index: number) {\n    this.selectedTabIndex = index;\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n\n  onFileSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = (e: any) => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({ logo: this.logoUrl });\n            this.selectedFiles.push({ url: pngUrl });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   switchToChatAgent(): void {\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before using the chat agent');\n      return;\n    }\n    this.selectedAITab = 0;\n    this.cd.detectChanges();\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  switchAITab(tabIndex: number): void {\n    this.selectedAITab = tabIndex;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startAIProcessing();\n    });\n  }\n\n\n  downloadAll(): void {\n    this.api.downloadData('all', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n\n          const bytes = new Uint8Array(binaryString.length);\n          for (let i = 0; i < binaryString.length; i++) {\n            bytes[i] = binaryString.charCodeAt(i);\n          }\n\n          const blob = new Blob([bytes], { type: 'application/zip' });\n          const url = window.URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `${this.registrationForm.value.tenantId}_inventory.zip`;\n          document.body.appendChild(a);\n          a.click();\n          document.body.removeChild(a);\n          window.URL.revokeObjectURL(url);\n\n          this.snackBar.open(\n            \"All files downloaded successfully!\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        } catch (error) {\n          console.error(\"Base64 Decoding or ZIP Error:\", error);\n          this.snackBar.open(\n            \"Failed to download files. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download files. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<!-- Full-screen loading spinner overlay -->\n<div *ngIf=\"isLoading\" class=\"loading-overlay\">\n  <div class=\"spinner-container\">\n    <div class=\"spinner-border\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"loading-text\">Loading account...</div>\n  </div>\n</div>\n\n<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"form-card\">\n      <mat-card-content>\n        <form class=\"account-form\" [formGroup]=\"registrationForm\">\n          <h3 class=\"form-section-title\">Account Information</h3>\n          <div class=\"compact-form-grid\">\n            <!-- First row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant Name</mat-label>\n                <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                <mat-icon matSuffix>business</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Tenant ID</mat-label>\n                <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                <mat-icon matSuffix>fingerprint</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                  Tenant ID already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Account Number</mat-label>\n                <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                  oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                <mat-icon matSuffix>account_balance</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                  Account number already exists\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Second row -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>G-Sheet</mat-label>\n                <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                  (keyup)=\"checkGSheet($event)\">\n                <mat-icon matSuffix>table_chart</mat-icon>\n                <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                  G-Sheet number already exists\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Email</mat-label>\n                <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                <mat-icon matSuffix>email</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Password</mat-label>\n                <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                  [type]=\"hidePassword ? 'password' : 'text'\" />\n                <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                </button>\n              </mat-form-field>\n            </div>\n\n            <!-- Settings section with two-column layout -->\n            <div class=\"settings-section\">\n              <div class=\"two-column-grid\">\n                <!-- Left column: Status options -->\n                <div class=\"left-column\">\n                  <div class=\"status-header\">\n                    <h4 class=\"section-label\">Account Status</h4>\n                  </div>\n                  <div class=\"status-options\">\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Account</label>\n                      <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Active</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Inactive</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Forecast</label>\n                      <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n\n                    <div class=\"status-option\">\n                      <label class=\"status-label\">Sales</label>\n                      <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                        <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                        <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                      </mat-radio-group>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Right column: Logo upload -->\n                <div class=\"right-column\">\n                  <div class=\"logo-header\">\n                    <h4 class=\"section-label\">Company Logo</h4>\n                  </div>\n                  <div class=\"logo-container\">\n                    <!-- Interactive logo upload area -->\n                    <div class=\"logo-dropzone\" (click)=\"fileInput.click()\" [class.has-logo]=\"logoUrl\">\n                      <!-- Logo preview with change instruction overlay -->\n                      <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                        <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                        <div class=\"logo-overlay\">\n                          <div class=\"overlay-content\">\n                            <mat-icon>edit</mat-icon>\n                            <div class=\"change-text\">Click to change</div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Placeholder with upload instruction -->\n                      <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                        <mat-icon>cloud_upload</mat-icon>\n                        <div class=\"upload-text\">Click to upload logo</div>\n                      </div>\n\n                      <!-- Loading spinner -->\n                      <div class=\"logo-loading\" *ngIf=\"loadSpinnerForLogo\">\n                        <div class=\"spinner-border\" role=\"status\">\n                          <span class=\"sr-only\">Loading...</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Hidden file input -->\n                    <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n\n                    <!-- Error message -->\n                    <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                      Please upload a logo\n                    </mat-error>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- AI Data Download Section - Shows after tenant creation -->\n    <mat-card *ngIf=\"showDataDownload\" class=\"ai-data-section\">\n      <div class=\"section-header\">\n        <mat-icon class=\"section-icon\">auto_awesome</mat-icon>\n        <h3 class=\"section-title\">Generate AI-Powered Datasets</h3>\n      </div>\n\n      <!-- AI Data Generation Section with Tabs -->\n      <div *ngIf=\"showChatBot && !isDownloading && !downloadComplete && !downloadFailed\" class=\"chat-bot-section\">\n        <div class=\"ai-data-tabs\">\n          <mat-tab-group [(selectedIndex)]=\"selectedAITab\" animationDuration=\"300ms\" class=\"compact-tabs\">\n            <!-- Chat Agent Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">chat</mat-icon>\n                <span class=\"tab-label\">Chat Agent</span>\n              </ng-template>\n\n              <div class=\"tab-content\">\n                <app-chat-bot [tenantId]=\"registrationForm.value.tenantId\"\n                  [tenantName]=\"registrationForm.value.tenantName\">\n                </app-chat-bot>\n              </div>\n            </mat-tab>\n\n            <!-- Dataset Tab -->\n            <mat-tab>\n              <ng-template mat-tab-label>\n                <mat-icon class=\"tab-icon\">dataset</mat-icon>\n                <span class=\"tab-label\">Generate Datasets</span>\n              </ng-template>\n\n              <div class=\"tab-content dataset-tab-content\">\n                <div class=\"dataset-info\">\n                  <p>Our AI will analyze your restaurant information to create optimized inventory, packaging and vendor datasets.</p>\n                  <p><strong>Note:</strong> This process takes approximately 30-40 minutes to complete.</p>\n\n                  <button mat-raised-button color=\"primary\" (click)=\"startAIProcessing()\" class=\"generate-btn\">\n                    <mat-icon>play_circle</mat-icon>\n                    Generate Datasets\n                  </button>\n                </div>\n              </div>\n            </mat-tab>\n          </mat-tab-group>\n        </div>\n      </div>\n\n      <!-- Processing state -->\n      <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n        <h3 class=\"processing-title\">\n          <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n          Processing Your Data\n        </h3>\n\n        <!-- Progress indicator -->\n        <div class=\"progress-container\">\n          <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n          <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n        </div>\n\n        <!-- Estimated time -->\n        <div class=\"estimated-time\">\n          <mat-icon class=\"icon\">access_time</mat-icon>\n          <span *ngIf=\"estimatedTimeRemaining > 60\">\n            Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n            Estimated time remaining: less than a minute\n          </span>\n          <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n            Calculating...\n          </span>\n        </div>\n\n        <!-- Processing steps -->\n        <div class=\"processing-steps\">\n          <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n            [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n            <div class=\"step-status\">\n              <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n              <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                <span class=\"visually-hidden\">Loading...</span>\n              </div>\n              <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n            </div>\n\n            <div class=\"step-details\">\n              <div class=\"step-name\">{{step.name}}</div>\n              <div class=\"step-description\">{{step.description}}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Helpful tips section -->\n        <div class=\"tips-section\">\n          <div class=\"tip-header\">\n            <mat-icon>lightbulb</mat-icon>\n            <span>Did You Know?</span>\n          </div>\n          <div class=\"tip-content\">\n            AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n            insights\n          </div>\n        </div>\n      </div>\n\n      <!-- Download complete state -->\n      <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n        <div class=\"success-header\">\n          <mat-icon class=\"success-icon\">task_alt</mat-icon>\n          <h3>Processing Complete!</h3>\n        </div>\n\n        <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.\n        </p>\n\n        <!-- Download All Button -->\n        <div class=\"download-all-container mb-3\">\n          <mat-card class=\"download-all-card\">\n            <mat-card-header>\n              <div mat-card-avatar class=\"download-all-icon\">\n                <mat-icon>cloud_download</mat-icon>\n              </div>\n              <mat-card-title>Download All Datasets</mat-card-title>\n              <mat-card-subtitle>Get all files in a single ZIP archive</mat-card-subtitle>\n            </mat-card-header>\n            <mat-card-actions>\n              <button mat-raised-button color=\"primary\" (click)=\"downloadAll()\">\n                <mat-icon>archive</mat-icon> Download All Files\n              </button>\n            </mat-card-actions>\n          </mat-card>\n        </div>\n      </div>\n\n      <!-- Error state -->\n      <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n        <div class=\"error-header\">\n          <mat-icon class=\"error-icon\">error_outline</mat-icon>\n          <h3>Processing Failed</h3>\n        </div>\n\n        <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n          load or connection issues.</p>\n\n        <div class=\"error-actions\">\n          <button mat-raised-button color=\"warn\" (click)=\"startAIProcessing()\">\n            <mat-icon>refresh</mat-icon> Try Again\n          </button>\n        </div>\n      </div>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAM1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;ICpBnCC,EAAA,CAAAC,cAAA,cAA+C;IAGXD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAkDtCH,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA+DNH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IACxCJ,EAAA,CAAAC,cAAA,cAA0B;IAEZD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ7CH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAUtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIrDH,EAAA,CAAAC,cAAA,cAAqD;IAE3BD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS7CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAwBhBH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAazCH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IArB1DH,EAAA,CAAAC,cAAA,cAA4G;IAEzFD,EAAA,CAAAU,UAAA,iCAAAC,+FAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAAF,OAAA,CAAAG,aAAA,GAAAN,MAAA;IAAA,EAAiC;IAE9CZ,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAmB,UAAA,IAAAC,+DAAA,0BAGc;IAEdpB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,uBAEe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAS;IACPD,EAAA,CAAAmB,UAAA,IAAAE,+DAAA,0BAGc;IAEdrB,EAAA,CAAAC,cAAA,cAA6C;IAEtCD,EAAA,CAAAE,MAAA,qHAA6G;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpHH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,oEAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzFH,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAU,UAAA,mBAAAY,2EAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAAvB,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAM,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrExB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA9BFH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,kBAAAmB,OAAA,CAAAP,aAAA,CAAiC;IAS5BlB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAM,UAAA,aAAAmB,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,QAAA,CAA4C,eAAAH,OAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAE,UAAA;;;;;IA6ChE7B,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA8B,kBAAA,gCAAA9B,EAAA,CAAA+B,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACAjC,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,eAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,cACuI;IAGnID,EAAA,CAAAmB,UAAA,IAAAe,mEAAA,uBAAwD;IACxDlC,EAAA,CAAAmB,UAAA,IAAAgB,8DAAA,mBAEM;IACNnC,EAAA,CAAAmB,UAAA,IAAAiB,mEAAA,uBAAuF;IACzFpC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvH3C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAiC,QAAA,CAAAC,SAAA,CAAoB;IACzBxC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAiC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpC3C,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAiC,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7B3C,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA4C,iBAAA,CAAAL,QAAA,CAAAM,IAAA,CAAa;IACN7C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA4C,iBAAA,CAAAL,QAAA,CAAAO,WAAA,CAAoB;;;;;IAzC1D9C,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,cAA4B;IACHD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAmB,UAAA,KAAA4B,yDAAA,mBAEO;IACP/C,EAAA,CAAAmB,UAAA,KAAA6B,yDAAA,mBAEO;IACPhD,EAAA,CAAAmB,UAAA,KAAA8B,yDAAA,mBAEO;IACTjD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAmB,UAAA,KAAA+B,wDAAA,oBAeM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAA6C,OAAA,CAAAC,gBAAA,CAA0B;IACnCpD,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA8B,kBAAA,KAAAqB,OAAA,CAAAC,gBAAA,eAA8B;IAMnDpD,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,MAAiC;IAGjCjC,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,QAAAkB,OAAA,CAAAlB,sBAAA,OAAgE;IAGhEjC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAA6C,OAAA,CAAAlB,sBAAA,OAAkC;IAOnBjC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAA6C,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5CrD,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,6FAC3B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,eAAyC;IAIvBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAErCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACtDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAE9EH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAA4C,2EAAA;MAAAtD,EAAA,CAAAa,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAuC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC/DzD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,4BAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAOjBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAAgD,0EAAA;MAAA1D,EAAA,CAAAa,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAA2C,OAAA,CAAApC,iBAAA,EAAmB;IAAA,EAAC;IAClExB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IArJfH,EAAA,CAAAC,cAAA,mBAA2D;IAExBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI7DH,EAAA,CAAAmB,UAAA,IAAA0C,iDAAA,mBAsCM;IAGN7D,EAAA,CAAAmB,UAAA,IAAA2C,iDAAA,mBAyDM;IAGN9D,EAAA,CAAAmB,UAAA,IAAA4C,iDAAA,mBA0BM;IAGN/D,EAAA,CAAAmB,UAAA,IAAA6C,iDAAA,mBAcM;IACRhE,EAAA,CAAAG,YAAA,EAAW;;;;IAjJHH,EAAA,CAAAK,SAAA,GAA2E;IAA3EL,EAAA,CAAAM,UAAA,SAAA2D,MAAA,CAAAC,WAAA,KAAAD,MAAA,CAAAE,aAAA,KAAAF,MAAA,CAAAG,gBAAA,KAAAH,MAAA,CAAAI,cAAA,CAA2E;IAyC3ErE,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,SAAA2D,MAAA,CAAAE,aAAA,CAAmB;IA4DnBnE,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAA2D,MAAA,CAAAG,gBAAA,CAAsB;IA6BtBpE,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAA2D,MAAA,CAAAI,cAAA,CAAoB;;;;;;;;;ADvShC,MAwBaC,qBAAqB;EAuChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB;IAXtB,KAAAX,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IAhDlB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAhF,OAAO,GAAkB,IAAI;IAC7B,KAAAiF,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,SAAS,GAAY,KAAK;IAG1B,KAAAzC,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAA0C,gBAAgB,GAAY,IAAI;IAChC,KAAA5B,aAAa,GAAY,KAAK;IAC9B,KAAAf,gBAAgB,GAAW,CAAC;IAC5B,KAAAgB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA3B,UAAU,GAAW,CAAC;IACtB,KAAAT,sBAAsB,GAAG,CAAC;IAC1B,KAAAiC,WAAW,GAAY,IAAI;IAC3B,KAAAhD,aAAa,GAAW,CAAC;IAiBvB,IAAI,CAAC8E,IAAI,GAAG,IAAI,CAAChB,IAAI,CAACiB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACtB,UAAU,CAACuB,WAAW,EAAE,CAACxE,KAAK;IAEnD,IAAI,IAAI,CAACuD,UAAU,EAAE;MACnB,IAAI,CAACkB,WAAW,GAAG,IAAI,CAAClB,UAAU,CAACmB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAAC1E,gBAAgB,GAAG,IAAI,CAACiD,EAAE,CAAC2B,KAAK,CAAC;MACpC1E,QAAQ,EAAE,IAAIjD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAE;MAC3D1E,UAAU,EAAE,IAAIlD,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MAC5DC,OAAO,EAAE,IAAI7H,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MACzDE,MAAM,EAAE,IAAI9H,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MACxDG,SAAS,EAAE,IAAI/H,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MAC3DI,QAAQ,EAAE,IAAIhI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MAC1DK,OAAO,EAAE,IAAIjI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MAC3DM,QAAQ,EAAE,IAAIlI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MAC5DO,KAAK,EAAE,IAAInI,WAAW,CAAS,IAAI,EAAEG,UAAU,CAACyH,QAAQ,CAAC;MACzDQ,IAAI,EAAE,IAAIpI,WAAW,CAAS,EAAE,EAAEG,UAAU,CAACyH,QAAQ;KACtD,CAAc;EAEjB;EAEAS,QAAQA,CAAA;IACN,IAAI,CAACtB,UAAU,GAAG,KAAK;IAEvB,IAAI,IAAI,CAACR,UAAU,IAAI,IAAI,CAACA,UAAU,CAACmB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACX,UAAU,GAAG,IAAI;MACtB,IAAI,CAACuB,WAAW,CAAC,IAAI,CAAC/B,UAAU,CAACgC,QAAQ,CAAC;KAC3C,MAAM;MACL,IAAI,CAACxC,KAAK,CAACyC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAAC3B,UAAU,GAAG,IAAI;UACtB,IAAI,CAACI,SAAS,GAAG,IAAI;UACrB,IAAI,CAACb,EAAE,CAACqC,aAAa,EAAE;UAEvB,IAAI,CAACvC,GAAG,CAACwC,cAAc,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACV,WAAW,CAACQ,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAACjC,UAAU,GAAG,KAAK;gBACvB,IAAI,CAACb,MAAM,CAAC+C,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;cAE9C,IAAI,CAAC/B,SAAS,GAAG,KAAK;cACtB,IAAI,CAACb,EAAE,CAACqC,aAAa,EAAE;YACzB,CAAC;YACDQ,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAACrC,UAAU,GAAG,KAAK;cACvBsC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAAClD,MAAM,CAAC+C,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;cAC5C,IAAI,CAAC/B,SAAS,GAAG,KAAK;cACtB,IAAI,CAACb,EAAE,CAACqC,aAAa,EAAE;YACzB;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAL,WAAWA,CAACU,IAAI;IACd,IAAI,CAACjG,gBAAgB,CAACuG,UAAU,CAAC;MAC/BpG,UAAU,EAAE8F,IAAI,CAAC9F,UAAU;MAC3BD,QAAQ,EAAE+F,IAAI,CAAC/F,QAAQ;MACvB4E,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,MAAM,EAAEkB,IAAI,CAAClB,MAAM;MACnBC,SAAS,EAAEiB,IAAI,CAACjB,SAAS;MACzBC,QAAQ,EAAEgB,IAAI,CAAChB,QAAQ;MACvBC,OAAO,EAAEe,IAAI,CAACO,MAAM,CAACtB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEc,IAAI,CAACO,MAAM,CAACrB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEa,IAAI,CAACO,MAAM,CAACpB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEY,IAAI,CAACQ,aAAa,EAAEpB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIY,IAAI,CAACQ,aAAa,EAAEpB,IAAI,EAAE;MAC5B,IAAI,CAACvG,OAAO,GAAGmH,IAAI,CAACQ,aAAa,CAACpB,IAAI;MACtC,IAAI,CAACvB,aAAa,GAAG,CAAC;QACpB4C,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACpB;OACzB,CAAC;;IAEJ,IAAI,CAAC9B,EAAE,CAACqC,aAAa,EAAE;EACzB;EAEAe,KAAKA,CAAA;IACH,IAAI,CAAC5D,MAAM,CAACoD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAS,IAAIA,CAAA;IACF,IAAI,IAAI,CAAC5G,gBAAgB,CAAC6G,OAAO,EAAE;MACjC,IAAI,CAAC7G,gBAAgB,CAAC8G,gBAAgB,EAAE;MACxC,IAAI,CAAC3D,MAAM,CAAC+C,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIa,MAAM,GAAG,IAAI,CAAC/G,gBAAgB,CAACC,KAAK;MACxC,IAAI+G,GAAG,GAAQ;QACT9G,QAAQ,EAAE6G,MAAM,CAAC7G,QAAQ;QACzBC,UAAU,EAAE4G,MAAM,CAAC5G,UAAU;QAC7B6E,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrByB,MAAM,EAAE;UACNtB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDqB,aAAa,EAAE;UACfpB,IAAI,EAAE,IAAI,CAACvB,aAAa,CAACmD,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnD,aAAa,CAAC,CAAC,CAAC,CAAC4C,GAAG,GAAG;;OAEvE;MACD,IAAI,CAACrD,GAAG,CAAC6D,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAAC7C,MAAM,CAACgE,mBAAmB,CAAC,IAAI,CAACnD,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAACE,aAAa,GAAG,IAAI;YACzB,IAAI,CAACC,eAAe,GAAG,IAAI;YAC3B,IAAI,CAACpB,MAAM,CAACoD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAAC/C,iBAAiB,CAACgE,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAACjE,MAAM,CAAC+C,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACe,GAAG,CAAChB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAiB,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACtD,gBAAgB,GAAGsD,KAAK;EAC/B;EAEAC,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACzH,KAAK;IACtC,IAAIgG,IAAI,GAAG,IAAI,CAAC/C,UAAU,CAACuB,WAAW,EAAE,CAACxE,KAAK;IAC9C,MAAM0H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3H,QAAQ,KAAMuH,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC3H,gBAAgB,CAAC8H,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAAC/H,gBAAgB,CAAC8H,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACzH,KAAK;IACtC,IAAIgG,IAAI,GAAG,IAAI,CAAC/C,UAAU,CAACuB,WAAW,EAAE,CAACxE,KAAK;IAC9C,MAAM0H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7C,SAAS,KAAMyC,WAAW,CAAC;IAC1E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC3H,gBAAgB,CAAC8H,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAAC/H,gBAAgB,CAAC8H,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAE1D;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAACzH,KAAK;IACtC,IAAIgG,IAAI,GAAG,IAAI,CAAC/C,UAAU,CAACuB,WAAW,EAAE,CAACxE,KAAK;IAC9C,MAAM0H,eAAe,GAAG1B,IAAI,CAAC2B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9C,MAAM,KAAM0C,WAAW,CAAC;IACvE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAC3H,gBAAgB,CAAC8H,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAAC/H,gBAAgB,CAAC8H,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAG,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAACvE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9B0E,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAAC7K,OAAO,GAAG4K,MAAM;YACrB,IAAI,CAAC1J,gBAAgB,CAACuG,UAAU,CAAC;cAAElB,IAAI,EAAE,IAAI,CAACvG;YAAO,CAAE,CAAC;YACxD,IAAI,CAACgF,aAAa,CAAC8F,IAAI,CAAC;cAAElD,GAAG,EAAEgD;YAAM,CAAE,CAAC;YACxC,IAAI,CAAC9F,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACL,EAAE,CAACqC,aAAa,EAAE;UACzB,CAAC;UACDoD,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEN;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACrI,aAAa,CAAC6G,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACnJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAECkJ,iBAAiBA,CAAA;IAChB,IAAI,IAAI,CAAClK,gBAAgB,CAAC6G,OAAO,EAAE;MACjC,IAAI,CAAC1D,MAAM,CAAC+C,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAEF,IAAI,CAAC1G,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC+D,EAAE,CAACqC,aAAa,EAAE;EACzB;EAEA9F,iBAAiBA,CAAA;IACf,IAAI,CAAC2C,aAAa,GAAG,IAAI;IACzB,IAAI,CAACf,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACnB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACmC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACqH,UAAU,EAAE;IACjB,MAAM9J,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAACC,QAAQ;IAErD,IAAI,CAACmD,GAAG,CAAC8G,eAAe,CAACjK,QAAQ,CAAC,CAACkK,IAAI,CACrCnM,UAAU,CAACoM,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAOlM,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAACsH,SAAS,CAAE6E,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAACvE,OAAO,EAAE;QAChC,IAAI,CAACwE,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACvK,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAACoK,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAACC,QAAgB;IAC1B,IAAI,CAACnL,aAAa,GAAGmL,QAAQ;IAC7B,IAAI,CAACpH,EAAE,CAACqC,aAAa,EAAE;EACzB;EAEA6E,kBAAkBA,CAACvK,QAAgB;IACjC,IAAI,CAAC0K,aAAa,GAAGvM,QAAQ,CAAC,KAAK,CAAC,CAAC+L,IAAI,CACvClM,SAAS,CAAC,MAAM,IAAI,CAACmF,GAAG,CAACwH,SAAS,CAAC3K,QAAQ,CAAC,CAAC,EAC7C/B,SAAS,CAAEoM,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAAC/D,MAAM,KAAK,UAAU,IAAI+D,QAAQ,CAAC/D,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACd,SAAS,CAAE6E,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAAC/D,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAAC8D,mBAAmB,CAACC,QAAQ,CAACO,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACpJ,gBAAgB,GAAG6I,QAAQ,CAACQ,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAACxK,sBAAsB,GAAGgK,QAAQ,CAACS,wBAAwB,IAAI,CAAC;MAEpE,IAAIT,QAAQ,CAACU,WAAW,KAAKC,SAAS,IAAIX,QAAQ,CAACU,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACjK,UAAU,GAAGuJ,QAAQ,CAACU,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIZ,QAAQ,CAACU,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAACxJ,aAAa,CAACsF,MAAM,EAAE;YACjC,IAAI,CAACtF,aAAa,CAACwJ,CAAC,CAAC,CAACrK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAACyC,EAAE,CAACqC,aAAa,EAAE;MAEvB,IAAI2E,QAAQ,CAAC/D,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC4E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEf,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAc,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC5I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,EAAE,CAACqC,aAAa,EAAE;IACvB,IAAI,CAACnC,QAAQ,CAAC6H,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAnB,mBAAmBA,CAACQ,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC5I,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACc,QAAQ,CAAC6H,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAChG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC5F,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAiC,WAAWA,CAAA;IACT,IAAI,CAACsB,GAAG,CAACsI,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC3L,gBAAgB,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACwF,SAAS,CACzEkG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAE1C,MAAMI,KAAK,GAAG,IAAIC,UAAU,CAACH,YAAY,CAAC9E,MAAM,CAAC;QACjD,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,YAAY,CAAC9E,MAAM,EAAEkE,CAAC,EAAE,EAAE;UAC5Cc,KAAK,CAACd,CAAC,CAAC,GAAGY,YAAY,CAACI,UAAU,CAAChB,CAAC,CAAC;;QAGvC,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,KAAK,CAAC,EAAE;UAAEvD,IAAI,EAAE;QAAiB,CAAE,CAAC;QAC3D,MAAMhC,GAAG,GAAG4F,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,CAAC,GAAGtD,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCqD,CAAC,CAACC,IAAI,GAAGhG,GAAG;QACZ+F,CAAC,CAACE,QAAQ,GAAG,GAAG,IAAI,CAAC3M,gBAAgB,CAACC,KAAK,CAACC,QAAQ,gBAAgB;QACpEiJ,QAAQ,CAACyD,IAAI,CAACC,WAAW,CAACJ,CAAC,CAAC;QAC5BA,CAAC,CAACK,KAAK,EAAE;QACT3D,QAAQ,CAACyD,IAAI,CAACG,WAAW,CAACN,CAAC,CAAC;QAC5BH,MAAM,CAACC,GAAG,CAACS,eAAe,CAACtG,GAAG,CAAC;QAE/B,IAAI,CAACjD,QAAQ,CAAC6H,IAAI,CAChB,oCAAoC,EACpC,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,CAAC,OAAOnF,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC3C,QAAQ,CAAC6H,IAAI,CAChB,6CAA6C,EAC7C,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAnF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC3C,QAAQ,CAAC6H,IAAI,CAChB,6CAA6C,EAC7C,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEA0B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAhaWzI,qBAAqB,EAAAtE,EAAA,CAAA4O,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA9O,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhP,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAjP,EAAA,CAAA4O,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAnP,EAAA,CAAA4O,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAArP,EAAA,CAAA4O,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAvP,EAAA,CAAA4O,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAzP,EAAA,CAAA4O,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAA3P,EAAA,CAAA4O,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAA4O,iBAAA,CAAA5O,EAAA,CAAA8P,iBAAA,GAAA9P,EAAA,CAAA4O,iBAAA,CAkDV1P,eAAe,MAAAc,EAAA,CAAA4O,iBAAA,CAAAmB,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAlD1B1L,qBAAqB;MAAA2L,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnQ,EAAA,CAAAoQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAA3F,GAAA;QAAA,IAAA2F,EAAA;;UClDlC1Q,EAAA,CAAAmB,UAAA,IAAAwP,oCAAA,iBAOM;UAEN3Q,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAAkQ,wDAAA;YAAA,OAAS7F,GAAA,CAAAzC,IAAA,EAAM;UAAA,EAAC;UACtBtI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6B;UAIUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAmQ,uDAAAjQ,MAAA;YAAA,OAASmK,GAAA,CAAA7B,aAAA,CAAAtI,MAAA,CAAqB;UAAA,EAAC;UADjFZ,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAmB,UAAA,KAAA2P,2CAAA,wBAEY;UACd9Q,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAqQ,uDAAAnQ,MAAA;YAAA,OAASmK,GAAA,CAAArB,cAAA,CAAA9I,MAAA,CAAsB;UAAA,EAAC;UADlFZ,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAmB,UAAA,KAAA6P,2CAAA,wBAEY;UACdhR,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAuQ,uDAAArQ,MAAA;YAAA,OAASmK,GAAA,CAAApB,WAAA,CAAA/I,MAAA,CAAmB;UAAA,EAAC;UAD/BZ,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAmB,UAAA,KAAA+P,2CAAA,wBAEY;UACdlR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAyQ,wDAAA;YAAA,OAAApG,GAAA,CAAAtF,YAAA,IAAAsF,GAAA,CAAAtF,YAAA;UAAA,EAAsC;UACtEzF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UAKID,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UACvCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC7FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UACzCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACnCD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,6BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOtGH,EAAA,CAAAC,cAAA,gBAA0B;UAEID,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,gBAA4B;UAECD,EAAA,CAAAU,UAAA,mBAAA0Q,sDAAA;YAAApR,EAAA,CAAAa,aAAA,CAAAwQ,IAAA;YAAA,MAAAC,GAAA,GAAAtR,EAAA,CAAAuR,WAAA;YAAA,OAASvR,EAAA,CAAAiB,WAAA,CAAAqQ,GAAA,CAAA9C,KAAA,EAAiB;UAAA,EAAC;UAEpDxO,EAAA,CAAAmB,UAAA,MAAAqQ,sCAAA,kBAQM;UAGNxR,EAAA,CAAAmB,UAAA,MAAAsQ,sCAAA,kBAGM;UAGNzR,EAAA,CAAAmB,UAAA,MAAAuQ,sCAAA,kBAIM;UACR1R,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAAiR,yDAAA/Q,MAAA;YAAA,OAAUmK,GAAA,CAAAnB,cAAA,CAAAhJ,MAAA,CAAsB;UAAA,EAAC;UAA/DZ,EAAA,CAAAG,YAAA,EAAwG;UAGxGH,EAAA,CAAAmB,UAAA,MAAAyQ,4CAAA,wBAEY;UACd5R,EAAA,CAAAG,YAAA,EAAM;UAUpBH,EAAA,CAAAmB,UAAA,MAAA0Q,2CAAA,wBAwJW;UACb7R,EAAA,CAAAG,YAAA,EAAM;;;UAjVFH,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAAjF,SAAA,CAAe;UAaZ9F,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA8R,eAAA,KAAAC,GAAA,EAAkC;UAIlC/R,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA8R,eAAA,KAAAE,GAAA,EAAqC;UAMJhS,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAA4C,iBAAA,CAAAmI,GAAA,CAAArF,UAAA,yBAAsC;UAClE1F,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA4C,iBAAA,CAAAmI,GAAA,CAAArF,UAAA,kCAA+C;UAOrD1F,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAA8B,kBAAA,MAAAiJ,GAAA,CAAArF,UAAA,4BACF;UAO6B1F,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAAyK,GAAA,CAAArJ,gBAAA,CAA8B;UAgBrC1B,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAArJ,gBAAA,CAAA8H,GAAA,aAAAyI,QAAA,mBAAiE;UAUjEjS,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAArJ,gBAAA,CAAA8H,GAAA,cAAAyI,QAAA,oBAAmE;UAanEjS,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAArJ,gBAAA,CAAA8H,GAAA,WAAAyI,QAAA,iBAA6D;UAcvEjS,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAAtF,YAAA,uBAA2C;UAEjCzF,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAA4C,iBAAA,CAAAmI,GAAA,CAAAtF,YAAA,mCAAkD;UA+CHzF,EAAA,CAAAK,SAAA,IAA0B;UAA1BL,EAAA,CAAAkS,WAAA,aAAAnH,GAAA,CAAAvK,OAAA,CAA0B;UAEpDR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAAvK,OAAA,CAAa;UAWTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAAyK,GAAA,CAAAvK,OAAA,CAAc;UAMlBR,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAAzF,kBAAA,CAAwB;UAWzCtF,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAArJ,gBAAA,CAAA8H,GAAA,SAAAjB,OAAA,IAAAwC,GAAA,CAAArJ,gBAAA,CAAA8H,GAAA,SAAA2I,OAAA,CAAkF;UAanGnS,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAAyK,GAAA,CAAAhF,gBAAA,CAAsB;;;qBDvJjCrH,YAAY,EAAA0T,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZzT,aAAa,EAAA0T,GAAA,CAAAC,OAAA,EACb1T,cAAc,EAAA2T,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACdhU,gBAAgB,EAChBL,WAAW,EAAAsQ,EAAA,CAAAgE,aAAA,EAAAhE,EAAA,CAAAiE,oBAAA,EAAAjE,EAAA,CAAAkE,eAAA,EAAAlE,EAAA,CAAAmE,oBAAA,EACXxU,mBAAmB,EAAAqQ,EAAA,CAAAoE,kBAAA,EAAApE,EAAA,CAAAqE,eAAA,EACnBjU,cAAc,EAAAkU,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACdnU,eAAe,EAAAoU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfrU,aAAa,EAAAsU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACb5U,eAAe,EACfN,oBAAoB,EAAAmV,GAAA,CAAAC,cAAA,EACpBnV,aAAa,EAAAoV,GAAA,CAAAC,WAAA,EAAAD,GAAA,CAAAE,MAAA,EAAAF,GAAA,CAAAG,WAAA,EACbjV,gBAAgB,EAChBL,YAAY,EAAA0P,EAAA,CAAA6F,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHxQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}