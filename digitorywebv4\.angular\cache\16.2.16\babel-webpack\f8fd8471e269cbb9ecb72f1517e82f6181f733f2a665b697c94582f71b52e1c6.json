{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/services/share-data.service\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"src/app/services/master-data.service\";\nimport * as i7 from \"src/app/services/inventory.service\";\nimport * as i8 from \"src/app/services/auth.service\";\nimport * as i9 from \"@angular/material/snack-bar\";\nimport * as i10 from \"src/app/services/sse.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/radio\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/card\";\nimport * as i18 from \"@angular/material/progress-bar\";\nfunction AccountSetupComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Account Information\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Tenant ID already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Account number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_mat_error_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" G-Sheet number already exists \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"img\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.logoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AccountSetupComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"apps\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_icon_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_mat_error_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 77);\n    i0.ɵɵtext(1, \" Please upload a logo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"AI Data Generation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Agent Chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_ng_template_150_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 70);\n    i0.ɵɵtext(1, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3, \"Dataset Download\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"div\", 80)(3, \"mat-icon\", 81);\n    i0.ɵɵtext(4, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Generate AI-Powered Datasets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will analyze your business needs and create personalized recommendations.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 82)(10, \"mat-icon\", 83);\n    i0.ɵɵtext(11, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" This process takes approximately 15 minutes to complete. You can continue using the system while processing runs in the background.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 84)(14, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_152_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.startDataDownload());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"play_arrow\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Start Generation \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSetupComponent_div_153_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Estimated time remaining: \", i0.ɵɵpipeBind2(2, 1, ctx_r19.estimatedTimeRemaining * 0.0166667, \"1.0-0\"), \" minute \");\n  }\n}\nfunction AccountSetupComponent_div_153_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Estimated time remaining: less than a minute \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1, \" Calculating... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 105);\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSetupComponent_div_153_div_17_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"radio_button_unchecked\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"completed-step\": a0,\n    \"active-step\": a1,\n    \"pending-step\": a2\n  };\n};\nfunction AccountSetupComponent_div_153_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101);\n    i0.ɵɵtemplate(2, AccountSetupComponent_div_153_div_17_mat_icon_2_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵtemplate(3, AccountSetupComponent_div_153_div_17_div_3_Template, 3, 0, \"div\", 49);\n    i0.ɵɵtemplate(4, AccountSetupComponent_div_153_div_17_mat_icon_4_Template, 2, 0, \"mat-icon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102)(6, \"div\", 103);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 104);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, step_r23.completed, ctx_r22.activeStep === i_r24, !step_r23.completed && ctx_r22.activeStep !== i_r24));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r23.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep === i_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !step_r23.completed && ctx_r22.activeStep !== i_r24);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r23.description);\n  }\n}\nfunction AccountSetupComponent_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"mat-icon\", 88);\n    i0.ɵɵtext(3, \"autorenew\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Your Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 89);\n    i0.ɵɵelement(7, \"mat-progress-bar\", 90);\n    i0.ɵɵelementStart(8, \"div\", 91);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 92)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AccountSetupComponent_div_153_span_13_Template, 3, 4, \"span\", 22);\n    i0.ɵɵtemplate(14, AccountSetupComponent_div_153_span_14_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(15, AccountSetupComponent_div_153_span_15_Template, 2, 0, \"span\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 94);\n    i0.ɵɵtemplate(17, AccountSetupComponent_div_153_div_17_Template, 10, 10, \"div\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 96)(19, \"div\", 97)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Did You Know?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 98);\n    i0.ɵɵtext(25, \" AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand insights \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r14.downloadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.downloadProgress, \"% Complete\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining > 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining > 0 && ctx_r14.estimatedTimeRemaining <= 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.estimatedTimeRemaining === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.downloadSteps);\n  }\n}\nfunction AccountSetupComponent_div_154_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 107)(2, \"mat-icon\", 108);\n    i0.ɵɵtext(3, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Complete!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 109);\n    i0.ɵɵtext(7, \"Your AI-powered datasets have been generated successfully and are ready for download.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 110)(9, \"mat-card\", 111)(10, \"mat-card-header\")(11, \"div\", 112)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"inventory_2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-card-title\");\n    i0.ɵɵtext(15, \"Inventory Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-card-subtitle\");\n    i0.ɵɵtext(17, \"AI-Optimized Inventory Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"ul\", 113)(20, \"li\");\n    i0.ɵɵtext(21, \"Optimized stock levels based on demand forecasting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵtext(23, \"Intelligent categorization and tagging\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"li\");\n    i0.ɵɵtext(25, \"Ready for import into your inventory system\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"mat-card-actions\")(27, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_154_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.downloadInventory());\n    });\n    i0.ɵɵelementStart(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Download \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"mat-card\", 111)(32, \"mat-card-header\")(33, \"div\", 115)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-card-title\");\n    i0.ɵɵtext(37, \"Packaging Dataset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"mat-card-subtitle\");\n    i0.ɵɵtext(39, \"AI-Optimized Packaging Master\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"mat-card-content\")(41, \"ul\", 113)(42, \"li\");\n    i0.ɵɵtext(43, \"Cost-effective packaging recommendations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"li\");\n    i0.ɵɵtext(45, \"Sustainable options highlighted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"li\");\n    i0.ɵɵtext(47, \"Compatibility with your inventory items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"mat-card-actions\")(49, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_154_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.downloadPackaging());\n    });\n    i0.ɵɵelementStart(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52, \" Download \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction AccountSetupComponent_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117)(2, \"mat-icon\", 118);\n    i0.ɵɵtext(3, \"error_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Processing Failed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 119);\n    i0.ɵɵtext(7, \"We encountered an issue while generating your AI datasets. This could be due to server load or connection issues.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 120)(9, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function AccountSetupComponent_div_155_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.startDataDownload());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function () {\n  return [\"/dashboard/home\"];\n};\nconst _c2 = function () {\n  return [\"/dashboard/account\"];\n};\nclass AccountSetupComponent {\n  constructor(dialog, router, route, fb, sharedData, notify, masterDataService, api, auth, cd, dialogData, snackBar, sseService) {\n    this.dialog = dialog;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.sharedData = sharedData;\n    this.notify = notify;\n    this.masterDataService = masterDataService;\n    this.api = api;\n    this.auth = auth;\n    this.cd = cd;\n    this.dialogData = dialogData;\n    this.snackBar = snackBar;\n    this.sseService = sseService;\n    this.isUpdateButtonDisabled = false;\n    this.isUpdateActive = false;\n    this.loadSpinnerForLogo = false;\n    this.loadSpinnerForApi = false;\n    this.selectedFiles = [];\n    this.logoUrl = null;\n    this.hidePassword = true;\n    this.isEditMode = false;\n    this.downloadSteps = [{\n      \"name\": \"Starting your menu analysis\",\n      \"completed\": false,\n      \"description\": \"Reviewing all items on your restaurant menu\"\n    }, {\n      \"name\": \"Identifying and matching ingredients\",\n      \"completed\": false,\n      \"description\": \"Intelligently categorizing ingredients from your recipes\"\n    }, {\n      \"name\": \"Creating your final data\",\n      \"completed\": false,\n      \"description\": \"Generating detailed inventory and packaging recommendations\"\n    }];\n    this.showDataDownload = true;\n    this.isDownloading = false;\n    this.downloadProgress = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.activeStep = 0;\n    this.estimatedTimeRemaining = 0;\n    // Chat bot related properties\n    this.showChatBot = false;\n    this.chatBotMinimized = false;\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl('', Validators.required),\n      tenantName: new FormControl('', Validators.required),\n      emailId: new FormControl('', Validators.required),\n      gSheet: new FormControl('', Validators.required),\n      accountNo: new FormControl('', Validators.required),\n      password: new FormControl('', Validators.required),\n      account: new FormControl('no', Validators.required),\n      forecast: new FormControl('no', Validators.required),\n      sales: new FormControl('no', Validators.required),\n      logo: new FormControl('', Validators.required)\n    });\n  }\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: res => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: err => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    });\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj = {\n        tenantId: detail.tenantId,\n        tenantName: detail.tenantName,\n        accountNo: detail.accountNo,\n        emailId: detail.emailId,\n        password: detail.password,\n        gSheet: detail.gSheet,\n        status: {\n          account: detail.account == 'yes',\n          forecast: detail.forecast == 'yes',\n          sales: detail.sales == 'yes'\n        },\n        tenantDetails: {\n          logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n        }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: res => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: err => {\n          console.log(err);\n        }\n      });\n    }\n  }\n  checkTenantId(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('tenantId').setErrors({\n        'tenantIdExists': true\n      });\n    } else {\n      this.registrationForm.get('tenantId').setErrors(null);\n    }\n  }\n  checkAccountNo(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('accountNo').setErrors({\n        'accountNoExists': true\n      });\n    } else {\n      this.registrationForm.get('accountNo').setErrors(null);\n    }\n  }\n  checkGSheet(filterValue) {\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet === filterValue);\n    if (isItemAvailable) {\n      this.registrationForm.get('gSheet').setErrors({\n        'gSheetExists': true\n      });\n    } else {\n      this.registrationForm.get('gSheet').setErrors(null);\n    }\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = [];\n      this.loadSpinnerForLogo = true;\n      Array.from(input.files).forEach(file => {\n        if (!file.type.startsWith('image/')) return;\n        const reader = new FileReader();\n        reader.onload = e => {\n          const img = new Image();\n          img.onload = () => {\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = 140;\n            canvas.height = 140;\n            ctx.drawImage(img, 0, 0, 140, 140);\n            const pngUrl = canvas.toDataURL('image/png');\n            this.logoUrl = pngUrl;\n            this.registrationForm.patchValue({\n              logo: this.logoUrl\n            });\n            this.selectedFiles.push({\n              url: pngUrl\n            });\n            this.loadSpinnerForLogo = false;\n            this.cd.detectChanges();\n          };\n          img.src = e.target.result;\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  }\n  resetSteps() {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n  startDataDownload() {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }, 100);\n  }\n  startAIProcessing() {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n    this.api.startProcessing(tenantId).pipe(catchError(_error => {\n      this.handleDownloadError('Failed to start processing. Please try again.');\n      return of(null);\n    })).subscribe(response => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n  toggleChatBot() {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n  startStatusPolling(tenantId) {\n    this.statusPolling = interval(10000).pipe(switchMap(() => this.api.getStatus(tenantId)), takeWhile(response => {\n      return response && response.status !== 'complete' && response.status !== 'failed';\n    }, true)).subscribe(response => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n      this.cd.detectChanges();\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n  completeDownload() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n  handleDownloadError(message) {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n  downloadInventory() {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download inventory data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  downloadPackaging() {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(base64Data => {\n      try {\n        const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n        const binaryString = atob(cleanBase64Data);\n        const workbook = XLSX.read(binaryString, {\n          type: 'binary'\n        });\n        const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n      } catch (error) {\n        console.error(\"Base64 Decoding or XLSX Error:\", error);\n        this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n          duration: 3000\n        });\n      }\n    }, error => {\n      console.error(\"API Error:\", error);\n      this.snackBar.open(\"Failed to download packaging data. Please try again.\", \"Close\", {\n        duration: 3000\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function AccountSetupComponent_Factory(t) {\n      return new (t || AccountSetupComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.ShareDataService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.MasterDataService), i0.ɵɵdirectiveInject(i7.InventoryService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA, 8), i0.ɵɵdirectiveInject(i9.MatSnackBar), i0.ɵɵdirectiveInject(i10.SseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSetupComponent,\n      selectors: [[\"app-account-setup\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 156,\n      vars: 27,\n      consts: [[1, \"account-setup-container\"], [1, \"compact-header\"], [1, \"breadcrumbs\"], [1, \"breadcrumb-item\", 3, \"routerLink\"], [1, \"breadcrumb-icon\"], [1, \"breadcrumb-separator\"], [1, \"breadcrumb-item\", \"active\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"click\"], [1, \"content-section\"], [1, \"main-card\"], [\"animationDuration\", \"200ms\", 3, \"selectedIndex\", \"selectedIndexChange\"], [\"mat-tab-label\", \"\"], [1, \"tab-content\"], [1, \"account-form\", 3, \"formGroup\"], [1, \"form-section-title\"], [1, \"compact-form-grid\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"formControlName\", \"tenantName\", \"matInput\", \"\", \"placeholder\", \"Enter tenant name\"], [\"matSuffix\", \"\"], [\"formControlName\", \"tenantId\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter tenant ID\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [4, \"ngIf\"], [\"formControlName\", \"accountNo\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter account number\", \"oninput\", \"this.value = this.value.toUpperCase()\", 3, \"keyup\"], [\"formControlName\", \"gSheet\", \"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Enter G-Sheet ID\", 3, \"keyup\"], [\"formControlName\", \"emailId\", \"matInput\", \"\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"formControlName\", \"password\", \"matInput\", \"\", \"placeholder\", \"Enter password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"settings-section\"], [1, \"two-column-grid\"], [1, \"left-column\"], [1, \"status-header\"], [1, \"section-label\"], [1, \"status-options\"], [1, \"status-option\"], [1, \"status-label\"], [\"formControlName\", \"account\", \"aria-labelledby\", \"account-status-label\", 1, \"status-radio-group\"], [\"value\", \"yes\", \"color\", \"primary\", 1, \"compact-radio\"], [\"value\", \"no\", \"color\", \"primary\", 1, \"compact-radio\"], [\"formControlName\", \"forecast\", \"aria-labelledby\", \"forecast-status-label\", 1, \"status-radio-group\"], [\"formControlName\", \"sales\", \"aria-labelledby\", \"sales-status-label\", 1, \"status-radio-group\"], [1, \"right-column\"], [1, \"logo-header\"], [1, \"logo-container\"], [1, \"logo-preview-container\"], [\"class\", \"logo-preview\", 4, \"ngIf\"], [\"class\", \"logo-placeholder\", 4, \"ngIf\"], [1, \"logo-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"logo-error\", 4, \"ngIf\"], [3, \"disabled\"], [1, \"tab-content\", \"ai-tab-content\"], [\"animationDuration\", \"200ms\", 1, \"nested-tabs\"], [1, \"chat-interface\"], [1, \"chat-header\"], [1, \"chat-title\"], [1, \"chat-icon\"], [1, \"chat-description\"], [1, \"chat-content\"], [3, \"tenantId\", \"tenantName\"], [1, \"chat-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"next-step-button\", 3, \"click\"], [1, \"dataset-download-section\"], [\"class\", \"ai-intro-panel\", 4, \"ngIf\"], [\"class\", \"ai-processing-panel\", 4, \"ngIf\"], [\"class\", \"ai-complete-panel\", 4, \"ngIf\"], [\"class\", \"ai-error-panel\", 4, \"ngIf\"], [1, \"tab-icon\"], [1, \"tab-label\"], [1, \"logo-preview\"], [\"alt\", \"Company Logo\", 3, \"src\"], [1, \"logo-placeholder\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [1, \"sr-only\"], [1, \"logo-error\"], [1, \"ai-intro-panel\"], [1, \"intro-content\"], [1, \"intro-header\"], [1, \"intro-icon\"], [1, \"info-note\"], [1, \"info-icon\"], [1, \"intro-action\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"generate-button\", 3, \"click\"], [1, \"ai-processing-panel\"], [1, \"processing-header\"], [1, \"processing-icon\", \"rotating\"], [1, \"progress-container\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"], [1, \"progress-label\"], [1, \"estimated-time\"], [\"class\", \"calculating\", 4, \"ngIf\"], [1, \"processing-steps\"], [\"class\", \"step-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"tips-section\"], [1, \"tip-header\"], [1, \"tip-content\"], [1, \"calculating\"], [1, \"step-row\", 3, \"ngClass\"], [1, \"step-status\"], [1, \"step-details\"], [1, \"step-name\"], [1, \"step-description\"], [1, \"visually-hidden\"], [1, \"ai-complete-panel\"], [1, \"success-header\"], [1, \"success-icon\"], [1, \"success-message\"], [1, \"download-options\"], [1, \"download-card\"], [\"mat-card-avatar\", \"\", 1, \"inventory-icon\"], [1, \"dataset-features\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"download-button\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"packaging-icon\"], [1, \"ai-error-panel\"], [1, \"error-header\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"retry-button\", 3, \"click\"]],\n      template: function AccountSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r33 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\");\n          i0.ɵɵtext(12, \"Accounts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"\\u203A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 6)(16, \"mat-icon\", 4);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_21_listener() {\n            return ctx.save();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"mat-card\", 10)(27, \"mat-card-content\")(28, \"mat-tab-group\", 11);\n          i0.ɵɵlistener(\"selectedIndexChange\", function AccountSetupComponent_Template_mat_tab_group_selectedIndexChange_28_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementStart(29, \"mat-tab\");\n          i0.ɵɵtemplate(30, AccountSetupComponent_ng_template_30_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"form\", 14)(33, \"h3\", 15);\n          i0.ɵɵtext(34, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 16)(36, \"div\", 17)(37, \"mat-form-field\", 18)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"Tenant Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 19);\n          i0.ɵɵelementStart(41, \"mat-icon\", 20);\n          i0.ɵɵtext(42, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 18)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Tenant ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 21);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_46_listener($event) {\n            return ctx.checkTenantId($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-icon\", 20);\n          i0.ɵɵtext(48, \"fingerprint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, AccountSetupComponent_mat_error_49_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-form-field\", 18)(51, \"mat-label\");\n          i0.ɵɵtext(52, \"Account Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"input\", 23);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_53_listener($event) {\n            return ctx.checkAccountNo($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-icon\", 20);\n          i0.ɵɵtext(55, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, AccountSetupComponent_mat_error_56_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 17)(58, \"mat-form-field\", 18)(59, \"mat-label\");\n          i0.ɵɵtext(60, \"G-Sheet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"input\", 24);\n          i0.ɵɵlistener(\"keyup\", function AccountSetupComponent_Template_input_keyup_61_listener($event) {\n            return ctx.checkGSheet($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"mat-icon\", 20);\n          i0.ɵɵtext(63, \"table_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(64, AccountSetupComponent_mat_error_64_Template, 2, 0, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"mat-form-field\", 18)(66, \"mat-label\");\n          i0.ɵɵtext(67, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"input\", 25);\n          i0.ɵɵelementStart(69, \"mat-icon\", 20);\n          i0.ɵɵtext(70, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"mat-form-field\", 18)(72, \"mat-label\");\n          i0.ɵɵtext(73, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"input\", 26);\n          i0.ɵɵelementStart(75, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_75_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(76, \"mat-icon\");\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(78, \"div\", 28)(79, \"div\", 29)(80, \"div\", 30)(81, \"div\", 31)(82, \"h4\", 32);\n          i0.ɵɵtext(83, \"Account Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 33)(85, \"div\", 34)(86, \"label\", 35);\n          i0.ɵɵtext(87, \"Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-radio-group\", 36)(89, \"mat-radio-button\", 37);\n          i0.ɵɵtext(90, \"Active\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"mat-radio-button\", 38);\n          i0.ɵɵtext(92, \"Inactive\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(93, \"div\", 34)(94, \"label\", 35);\n          i0.ɵɵtext(95, \"Forecast\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"mat-radio-group\", 39)(97, \"mat-radio-button\", 37);\n          i0.ɵɵtext(98, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"mat-radio-button\", 38);\n          i0.ɵɵtext(100, \"Disabled\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 34)(102, \"label\", 35);\n          i0.ɵɵtext(103, \"Sales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"mat-radio-group\", 40)(105, \"mat-radio-button\", 37);\n          i0.ɵɵtext(106, \"Enabled\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"mat-radio-button\", 38);\n          i0.ɵɵtext(108, \"Disabled\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(109, \"div\", 41)(110, \"div\", 42)(111, \"h4\", 32);\n          i0.ɵɵtext(112, \"Company Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 43)(114, \"div\", 44);\n          i0.ɵɵtemplate(115, AccountSetupComponent_div_115_Template, 2, 1, \"div\", 45);\n          i0.ɵɵtemplate(116, AccountSetupComponent_div_116_Template, 3, 0, \"div\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 47)(118, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_118_listener() {\n            i0.ɵɵrestoreView(_r33);\n            const _r8 = i0.ɵɵreference(124);\n            return i0.ɵɵresetView(_r8.click());\n          });\n          i0.ɵɵtemplate(119, AccountSetupComponent_mat_icon_119_Template, 2, 0, \"mat-icon\", 22);\n          i0.ɵɵtemplate(120, AccountSetupComponent_div_120_Template, 3, 0, \"div\", 49);\n          i0.ɵɵelementStart(121, \"span\");\n          i0.ɵɵtext(122, \"Upload Logo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(123, \"input\", 50, 51);\n          i0.ɵɵlistener(\"change\", function AccountSetupComponent_Template_input_change_123_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(125, AccountSetupComponent_mat_error_125_Template, 2, 0, \"mat-error\", 52);\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵelementStart(126, \"mat-tab\", 53);\n          i0.ɵɵtemplate(127, AccountSetupComponent_ng_template_127_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(128, \"div\", 54)(129, \"mat-tab-group\", 55)(130, \"mat-tab\");\n          i0.ɵɵtemplate(131, AccountSetupComponent_ng_template_131_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(132, \"div\", 56)(133, \"div\", 57)(134, \"div\", 58)(135, \"mat-icon\", 59);\n          i0.ɵɵtext(136, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"h3\");\n          i0.ɵɵtext(138, \"Restaurant Information Assistant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(139, \"p\", 60);\n          i0.ɵɵtext(140, \" Please provide information about your restaurant to help us generate more accurate AI-powered datasets. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 61);\n          i0.ɵɵelement(142, \"app-chat-bot\", 62);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 63)(144, \"button\", 64);\n          i0.ɵɵlistener(\"click\", function AccountSetupComponent_Template_button_click_144_listener() {\n            return ctx.startAIProcessing();\n          });\n          i0.ɵɵelementStart(145, \"span\");\n          i0.ɵɵtext(146, \"Continue to Data Processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"mat-icon\");\n          i0.ɵɵtext(148, \"arrow_forward\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(149, \"mat-tab\", 53);\n          i0.ɵɵtemplate(150, AccountSetupComponent_ng_template_150_Template, 4, 0, \"ng-template\", 12);\n          i0.ɵɵelementStart(151, \"div\", 65);\n          i0.ɵɵtemplate(152, AccountSetupComponent_div_152_Template, 18, 0, \"div\", 66);\n          i0.ɵɵtemplate(153, AccountSetupComponent_div_153_Template, 26, 6, \"div\", 67);\n          i0.ɵɵtemplate(154, AccountSetupComponent_div_154_Template, 53, 0, \"div\", 68);\n          i0.ɵɵtemplate(155, AccountSetupComponent_div_155_Template, 13, 0, \"div\", 69);\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(25, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(26, _c2));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"edit\" : \"add_circle\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Account\" : \"New Account\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedTabIndex);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"tenantId\").hasError(\"tenantIdExists\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"accountNo\").hasError(\"accountNoExists\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"gSheet\").hasError(\"gSheetExists\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(38);\n          i0.ɵɵproperty(\"ngIf\", ctx.logoUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.logoUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadSpinnerForLogo);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationForm.get(\"logo\").invalid && ctx.registrationForm.get(\"logo\").touched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.tenantCreated);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"tenantId\", ctx.registrationForm.value.tenantId)(\"tenantName\", ctx.registrationForm.value.tenantName);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", !ctx.aiDataAvailable);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDownloading && !ctx.downloadComplete && !ctx.downloadFailed);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDownloading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.downloadComplete);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.downloadFailed);\n        }\n      },\n      dependencies: [CommonModule, i11.NgClass, i11.NgForOf, i11.NgIf, i11.DecimalPipe, MatIconModule, i12.MatIcon, MatInputModule, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatError, i14.MatSuffix, MatTooltipModule, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatRadioModule, i15.MatRadioGroup, i15.MatRadioButton, MatButtonModule, i16.MatButton, i16.MatIconButton, MatCardModule, i17.MatCard, i17.MatCardActions, i17.MatCardAvatar, i17.MatCardContent, i17.MatCardHeader, i17.MatCardSubtitle, i17.MatCardTitle, MatSelectModule, MatProgressBarModule, i18.MatProgressBar, ChatBotComponent, RouterModule, i2.RouterLink],\n      styles: [\".account-setup-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  font-size: 13px;\\n  background-color: #f5f7fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e0e4e8;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover {\\n  color: #3f51b5;\\n  text-decoration: underline;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  color: #999;\\n}\\n@media (max-width: 768px) {\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    height: 16px;\\n    width: 16px;\\n  }\\n  .account-setup-container[_ngcontent-%COMP%]   .breadcrumbs[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n  }\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .compact-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 20px;\\n  overflow: hidden;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]     .mat-tab-header {\\n  border-bottom: 1px solid #e0e4e8;\\n  background-color: #f5f7fa;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]     .mat-tab-label {\\n  min-width: 160px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .nested-tabs[_ngcontent-%COMP%]     .mat-tab-header {\\n  background-color: #fff;\\n  border-bottom: 1px solid #eee;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .nested-tabs[_ngcontent-%COMP%]     .mat-tab-label {\\n  min-width: 140px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #555;\\n  border-bottom: 1px solid #eee;\\n  padding-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 24px;\\n  align-items: flex-start;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 2;\\n  min-width: 250px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%], .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  border-bottom: 1px solid #eee;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 0;\\n  color: #555;\\n  padding-bottom: 6px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 6px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label {\\n  margin: 0;\\n  padding: 0;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-container {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-outer-circle, .account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-inner-circle {\\n  height: 16px;\\n  width: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .status-options[_ngcontent-%COMP%]   .status-option[_ngcontent-%COMP%]   .status-radio-group[_ngcontent-%COMP%]   .compact-radio[_ngcontent-%COMP%]     .mat-radio-label-content {\\n  padding-left: 4px;\\n  font-size: 13px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  align-items: center;\\n  width: 100%;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border: 1px dashed #ccc;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  background-color: #f9f9f9;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #aaa;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-preview-container[_ngcontent-%COMP%]   .logo-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  width: 100%;\\n  max-width: 150px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  width: 100%;\\n  padding: 6px 12px;\\n  font-size: 13px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .account-form[_ngcontent-%COMP%]   .compact-form-grid[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .two-column-grid[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo-actions[_ngcontent-%COMP%]   .logo-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 4px;\\n  text-align: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 600px;\\n  padding: 0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  background-color: #f9f9f9;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background-color: #e8eaf6;\\n  border-bottom: 1px solid #c5cae9;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  color: #3f51b5;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%]   .chat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #3f51b5;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #555;\\n  font-size: 14px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   app-chat-bot[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 100%;\\n  width: 100%;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background-color: #f5f5f5;\\n  border-top: 1px solid #e0e0e0;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]   .next-step-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .chat-interface[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]   .next-step-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 24px;\\n  align-items: center;\\n  background-color: #f5f7fa;\\n  padding: 24px;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%] {\\n  flex: 3;\\n  min-width: 300px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   .intro-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   .intro-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  color: #3f51b5;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   .intro-header[_ngcontent-%COMP%]   .intro-icon[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  font-size: 24px;\\n  margin-right: 12px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 12px;\\n  color: #555;\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   p.info-note[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  background-color: rgba(33, 150, 243, 0.1);\\n  border-radius: 4px;\\n  margin-top: 16px;\\n  color: #0277bd;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%]   p.info-note[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-right: 8px;\\n  color: #0277bd;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-action[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 150px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-action[_ngcontent-%COMP%]   .generate-button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.account-setup-container[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .dataset-download-section[_ngcontent-%COMP%]   .ai-intro-panel[_ngcontent-%COMP%]   .intro-action[_ngcontent-%COMP%]   .generate-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { AccountSetupComponent };", "map": {"version": 3, "names": ["CommonModule", "FormControl", "FormsModule", "ReactiveFormsModule", "Validators", "MatIconModule", "MatInputModule", "MatTooltipModule", "MAT_DIALOG_DATA", "MatProgressBarModule", "RouterModule", "MatRadioModule", "MatButtonModule", "MatCardModule", "MatSelectModule", "ChatBotComponent", "catchError", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "of", "interval", "XLSX", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "logoUrl", "ɵɵsanitizeUrl", "ɵɵlistener", "AccountSetupComponent_div_152_Template_button_click_14_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "startDataDownload", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r19", "estimatedTimeRemaining", "ɵɵtemplate", "AccountSetupComponent_div_153_div_17_mat_icon_2_Template", "AccountSetupComponent_div_153_div_17_div_3_Template", "AccountSetupComponent_div_153_div_17_mat_icon_4_Template", "ɵɵpureFunction3", "_c0", "step_r23", "completed", "ctx_r22", "activeStep", "i_r24", "ɵɵtextInterpolate", "name", "description", "AccountSetupComponent_div_153_span_13_Template", "AccountSetupComponent_div_153_span_14_Template", "AccountSetupComponent_div_153_span_15_Template", "AccountSetupComponent_div_153_div_17_Template", "ctx_r14", "downloadProgress", "downloadSteps", "AccountSetupComponent_div_154_Template_button_click_27_listener", "_r29", "ctx_r28", "downloadInventory", "AccountSetupComponent_div_154_Template_button_click_49_listener", "ctx_r30", "downloadPackaging", "AccountSetupComponent_div_155_Template_button_click_9_listener", "_r32", "ctx_r31", "AccountSetupComponent", "constructor", "dialog", "router", "route", "fb", "sharedData", "notify", "masterDataService", "api", "auth", "cd", "dialogData", "snackBar", "sseService", "isUpdateButtonDisabled", "isUpdateActive", "loadSpinnerForLogo", "loadSpinnerForApi", "selectedFiles", "hidePassword", "isEditMode", "showDataDownload", "isDownloading", "downloadComplete", "downloadFailed", "showChatBot", "chatBotMinimized", "user", "getCurrentUser", "baseData", "getBaseData", "value", "isDuplicate", "key", "registrationForm", "group", "tenantId", "required", "tenantName", "emailId", "gSheet", "accountNo", "password", "account", "forecast", "sales", "logo", "ngOnInit", "prefillData", "elements", "queryParams", "subscribe", "params", "getAccountById", "next", "res", "success", "data", "snackBarShowError", "navigate", "error", "err", "console", "patchValue", "status", "tenantDetails", "url", "detectChanges", "close", "save", "invalid", "mark<PERSON>llAsTouched", "detail", "obj", "length", "saveAccount", "snackBarShowSuccess", "triggerRefreshTable", "log", "checkTenantId", "filterValue", "target", "isItemAvailable", "some", "item", "get", "setErrors", "checkAccountNo", "checkGSheet", "onFileSelected", "event", "input", "files", "Array", "from", "for<PERSON>ach", "file", "type", "startsWith", "reader", "FileReader", "onload", "e", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "pngUrl", "toDataURL", "push", "src", "result", "readAsDataURL", "resetSteps", "step", "setTimeout", "chatBotElement", "querySelector", "scrollIntoView", "behavior", "startAIProcessing", "startProcessing", "pipe", "_error", "handleDownloadError", "response", "processingId", "startStatusPolling", "toggleChatBot", "statusPolling", "getStatus", "message", "progress", "estimated_time_remaining", "currentStep", "undefined", "i", "completeDownload", "unsubscribe", "open", "duration", "horizontalPosition", "verticalPosition", "onAction", "downloadData", "base64Data", "cleanBase64Data", "replace", "binaryString", "atob", "workbook", "read", "fileName", "writeFile", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "ShareDataService", "i5", "NotificationService", "i6", "MasterDataService", "i7", "InventoryService", "i8", "AuthService", "ChangeDetectorRef", "i9", "MatSnackBar", "i10", "SseService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AccountSetupComponent_Template", "rf", "AccountSetupComponent_Template_button_click_21_listener", "AccountSetupComponent_Template_mat_tab_group_selectedIndexChange_28_listener", "$event", "onTabChange", "AccountSetupComponent_ng_template_30_Template", "AccountSetupComponent_Template_input_keyup_46_listener", "AccountSetupComponent_mat_error_49_Template", "AccountSetupComponent_Template_input_keyup_53_listener", "AccountSetupComponent_mat_error_56_Template", "AccountSetupComponent_Template_input_keyup_61_listener", "AccountSetupComponent_mat_error_64_Template", "AccountSetupComponent_Template_button_click_75_listener", "AccountSetupComponent_div_115_Template", "AccountSetupComponent_div_116_Template", "AccountSetupComponent_Template_button_click_118_listener", "_r33", "_r8", "ɵɵreference", "click", "AccountSetupComponent_mat_icon_119_Template", "AccountSetupComponent_div_120_Template", "AccountSetupComponent_Template_input_change_123_listener", "AccountSetupComponent_mat_error_125_Template", "AccountSetupComponent_ng_template_127_Template", "AccountSetupComponent_ng_template_131_Template", "AccountSetupComponent_Template_button_click_144_listener", "AccountSetupComponent_ng_template_150_Template", "AccountSetupComponent_div_152_Template", "AccountSetupComponent_div_153_Template", "AccountSetupComponent_div_154_Template", "AccountSetupComponent_div_155_Template", "ɵɵpureFunction0", "_c1", "_c2", "selectedTabIndex", "<PERSON><PERSON><PERSON><PERSON>", "touched", "tenantCreated", "aiDataAvailable", "i11", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i12", "MatIcon", "i13", "MatInput", "i14", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i15", "MatRadioGroup", "MatRadioButton", "i16", "MatButton", "MatIconButton", "i17", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i18", "MatProgressBar", "RouterLink", "styles", "changeDetection"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.ts", "/home/<USER>/other/digi/digitorywebv4/src/app/pages/crm-management/account-setup/account-setup.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport {ProgressBarMode, MatProgressBarModule} from '@angular/material/progress-bar';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ShareDataService } from 'src/app/services/share-data.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { MasterDataService } from 'src/app/services/master-data.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { SseService } from 'src/app/services/sse.service';\nimport { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';\nimport { catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { of, interval, Subscription } from 'rxjs';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport * as XLSX from 'xlsx';\n\n\n\n@Component({\n  selector: 'app-account-setup',\n  standalone: true,\n  templateUrl: './account-setup.component.html',\n  styleUrls: ['./account-setup.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatInputModule,\n    MatTooltipModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatRadioModule,\n    MatButtonModule,\n    MatCardModule,\n    MatSelectModule,\n    MatProgressBarModule,\n    ChatBotComponent,\n    RouterModule\n  ]\n})\n\nexport class AccountSetupComponent {\n\n  registrationForm!: FormGroup;\n  isUpdateButtonDisabled = false;\n  isDuplicate: any;\n  baseData: any;\n  user: any;\n  isUpdateActive: boolean = false;\n  loadSpinnerForLogo: boolean = false;\n  loadSpinnerForApi: boolean = false;\n  selectedFiles: { url: string }[] = [];\n  logoUrl: string | null = null;\n  hidePassword: boolean = true;\n  isEditMode: boolean = false;\n\n\n  downloadSteps = [\n    {\"name\": \"Starting your menu analysis\", \"completed\": false, \"description\": \"Reviewing all items on your restaurant menu\"},\n    {\"name\": \"Identifying and matching ingredients\", \"completed\": false, \"description\": \"Intelligently categorizing ingredients from your recipes\"},\n    {\"name\": \"Creating your final data\", \"completed\": false, \"description\": \"Generating detailed inventory and packaging recommendations\"}\n]\n\n  statusPolling: any;\n  processingId: string;\n  showDataDownload: boolean = true;\n  isDownloading: boolean = false;\n  downloadProgress: number = 0;\n  downloadComplete: boolean = false;\n  downloadFailed: boolean = false;\n  activeStep: number = 0;\n  estimatedTimeRemaining = 0;\n\n  // Chat bot related properties\n  showChatBot: boolean = false;\n  chatBotMinimized: boolean = false;\n\n\n  constructor(\n    public dialog: MatDialog,\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private sharedData: ShareDataService,\n    private notify : NotificationService,\n    private masterDataService: MasterDataService,\n    private api: InventoryService,\n    private auth: AuthService,\n    private cd: ChangeDetectorRef,\n    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,\n    private snackBar: MatSnackBar,\n    private sseService: SseService\n  ){\n    this.user = this.auth.getCurrentUser();\n    this.baseData = this.sharedData.getBaseData().value;\n\n    // Handle the case when dialogData is null (when loaded as a standalone page)\n    if (this.dialogData) {\n      this.isDuplicate = this.dialogData.key;\n    } else {\n      this.isDuplicate = false;\n    }\n    this.registrationForm = this.fb.group({\n      tenantId: new FormControl<string>('', Validators.required,),\n      tenantName: new FormControl<string>('', Validators.required),\n      emailId: new FormControl<string>('', Validators.required),\n      gSheet: new FormControl<string>('', Validators.required),\n      accountNo: new FormControl<string>('', Validators.required),\n      password: new FormControl<string>('', Validators.required),\n      account: new FormControl<string>('no', Validators.required),\n      forecast: new FormControl<string>('no', Validators.required),\n      sales: new FormControl<string>('no', Validators.required),\n      logo: new FormControl<string>('', Validators.required),\n    }) as FormGroup;\n\n  }\n\n  ngOnInit() {\n    // Set default mode to new account\n    this.isEditMode = false;\n\n    // Check if we have dialog data (for backward compatibility)\n    if (this.dialogData && this.dialogData.key == false) {\n      this.isEditMode = true; // Set edit mode before rendering\n      this.prefillData(this.dialogData.elements);\n    } else {\n      // Check for query parameters for direct navigation\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true; // Set edit mode before rendering\n\n          // Fetch account data by ID\n          this.api.getAccountById(params['id']).subscribe({\n            next: (res) => {\n              if (res.success && res.data) {\n                this.prefillData(res.data);\n              } else {\n                this.isEditMode = false; // Reset if account not found\n                this.notify.snackBarShowError('Account not found');\n                this.router.navigate(['/dashboard/account']);\n              }\n            },\n            error: (err) => {\n              this.isEditMode = false; // Reset on error\n              console.error('Error fetching account data:', err);\n              this.notify.snackBarShowError('Failed to load account data');\n              this.router.navigate(['/dashboard/account']);\n            }\n          });\n        }\n      });\n    }\n  }\n\n  prefillData(data) {\n    this.registrationForm.patchValue({\n      tenantName: data.tenantName,\n      tenantId: data.tenantId,\n      emailId: data.emailId,\n      gSheet: data.gSheet,\n      accountNo: data.accountNo,\n      password: data.password,\n      account: data.status.account ? 'yes' : 'no',\n      forecast: data.status.forecast ? 'yes' : 'no',\n      sales: data.status.sales ? 'yes' : 'no',\n      logo: data.tenantDetails?.logo || ''\n    })\n    if (data.tenantDetails?.logo) {\n      this.logoUrl = data.tenantDetails.logo;\n      this.selectedFiles = [{\n        url: data.tenantDetails.logo\n      }];\n    }\n    this.cd.detectChanges();\n  }\n\n  close() {\n    this.router.navigate(['/dashboard/account']);\n  }\n\n  save() {\n    if (this.registrationForm.invalid) {\n      this.registrationForm.markAllAsTouched();\n      this.notify.snackBarShowError('Please fill out all required fields');\n    } else {\n      let detail = this.registrationForm.value;\n      let obj: any = {\n            tenantId: detail.tenantId,\n            tenantName: detail.tenantName,\n            accountNo: detail.accountNo,\n            emailId: detail.emailId,\n            password: detail.password,\n            gSheet: detail.gSheet,\n            status: {\n              account: detail.account == 'yes',\n              forecast: detail.forecast == 'yes',\n              sales: detail.sales == 'yes'\n            },\n            tenantDetails: {\n            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null\n            }\n      };\n      this.api.saveAccount(obj).subscribe({\n        next: (res) => {\n          if (res.success) {\n            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');\n            this.router.navigate(['/dashboard/account']);\n            this.masterDataService.triggerRefreshTable();\n          } else {\n            this.notify.snackBarShowError('Something went wrong!');\n          }\n        },\n        error: (err) => {\n          console.log(err);\n        }\n      });\n    }\n  }\n\n  checkTenantId(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.tenantId  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });\n  } else {\n    this.registrationForm.get('tenantId').setErrors(null);\n  }\n  }\n\n  checkAccountNo(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.accountNo  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });\n  } else {\n    this.registrationForm.get('accountNo').setErrors(null);\n  }\n  }\n\n  checkGSheet(filterValue){\n    filterValue = filterValue.target.value;\n    let data = this.sharedData.getBaseData().value;\n    const isItemAvailable = data.some(item => item.gSheet  === filterValue);\n  if (isItemAvailable) {\n    this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });\n  } else {\n    this.registrationForm.get('gSheet').setErrors(null);\n  }\n  }\n\n  onFileSelected(event: Event): void {\n  const input = event.target as HTMLInputElement;\n  if (input.files) {\n    this.selectedFiles = [];\n    this.loadSpinnerForLogo = true;\n    Array.from(input.files).forEach(file => {\n      if (!file.type.startsWith('image/')) return;\n      const reader = new FileReader();\n      reader.onload = (e: any) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n          canvas.width = 140;\n          canvas.height = 140;\n          ctx.drawImage(img, 0, 0, 140, 140);\n          const pngUrl = canvas.toDataURL('image/png');\n          this.logoUrl = pngUrl;\n          this.registrationForm.patchValue({ logo: this.logoUrl });\n          this.selectedFiles.push({ url: pngUrl });\n          this.loadSpinnerForLogo = false;\n          this.cd.detectChanges();\n        };\n        img.src = e.target.result;\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  }\n\n\n  resetSteps(): void {\n    this.downloadSteps.forEach(step => step.completed = false);\n    this.activeStep = -1;\n  }\n\n   startDataDownload(): void {\n    // If the form is not valid, show an error\n    if (this.registrationForm.invalid) {\n      this.notify.snackBarShowError('Please fill out all required fields before starting the process');\n      return;\n    }\n\n    // Show the chat bot instead of starting the download process\n    this.showChatBot = true;\n    this.chatBotMinimized = false;\n    this.cd.detectChanges();\n\n    // Scroll to the chat bot section\n    setTimeout(() => {\n      const chatBotElement = document.querySelector('.chat-bot-section');\n      if (chatBotElement) {\n        chatBotElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  }\n\n  startAIProcessing(): void {\n    this.isDownloading = true;\n    this.downloadProgress = 0;\n    this.estimatedTimeRemaining = 0;\n    this.downloadComplete = false;\n    this.downloadFailed = false;\n    this.resetSteps();\n    const tenantId = this.registrationForm.value.tenantId;\n\n    this.api.startProcessing(tenantId).pipe(\n      catchError(_error => {\n        this.handleDownloadError('Failed to start processing. Please try again.');\n        return of(null);\n      })\n    ).subscribe((response: any) => {\n      if (response && response.success) {\n        this.processingId = response.processingId;\n        this.startStatusPolling(tenantId);\n      } else {\n        this.handleDownloadError('Failed to initialize processing. Please try again.');\n      }\n    });\n  }\n\n  toggleChatBot(): void {\n    this.chatBotMinimized = !this.chatBotMinimized;\n    this.cd.detectChanges();\n  }\n\n  startStatusPolling(tenantId: string): void {\n    this.statusPolling = interval(10000).pipe(\n      switchMap(() => this.api.getStatus(tenantId)),\n      takeWhile((response: any) => {\n        return response && response.status !== 'complete' && response.status !== 'failed';\n      }, true)\n    ).subscribe((response: any) => {\n      if (!response) {\n        this.handleDownloadError('Lost connection to server. Please try again.');\n        return;\n      }\n\n      if (response.status === 'failed') {\n        this.handleDownloadError(response.message || 'Processing failed. Please try again.');\n        return;\n      }\n\n      this.downloadProgress = response.progress || 0;\n      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;\n\n      if (response.currentStep !== undefined && response.currentStep >= 0) {\n        this.activeStep = response.currentStep;\n\n        for (let i = 0; i <= response.currentStep; i++) {\n          if (i < this.downloadSteps.length) {\n            this.downloadSteps[i].completed = true;\n          }\n        }\n      }\n\n      this.cd.detectChanges();\n\n      if (response.status === 'complete') {\n        this.completeDownload();\n      }\n    }, _error => {\n      this.handleDownloadError('Error communicating with server. Please try again.');\n    });\n  }\n\n  completeDownload(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadComplete = true;\n    this.cd.detectChanges();\n    this.snackBar.open('Tenant data is ready for download!', 'Close', {\n      duration: 5000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  handleDownloadError(message: string): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n\n    this.isDownloading = false;\n    this.downloadFailed = true;\n    this.snackBar.open(message, 'Retry', {\n      duration: 10000,\n      horizontalPosition: 'center',\n      verticalPosition: 'bottom'\n    }).onAction().subscribe(() => {\n      this.startDataDownload();\n    });\n  }\n\n\n  downloadInventory(): void {\n    this.api.downloadData('inventory', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-inventory-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download inventory data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download inventory data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n\n  downloadPackaging(): void {\n    this.api.downloadData('package', this.registrationForm.value.tenantId).subscribe(\n      (base64Data: string) => {\n        try {\n          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');\n          const binaryString = atob(cleanBase64Data);\n          const workbook = XLSX.read(binaryString, { type: 'binary' });\n          const fileName = `${this.registrationForm.value.tenantId}-packaging-data.xlsx`;\n          XLSX.writeFile(workbook, fileName);\n        } catch (error) {\n          console.error(\"Base64 Decoding or XLSX Error:\", error);\n          this.snackBar.open(\n            \"Failed to download packaging data. Please try again.\",\n            \"Close\",\n            { duration: 3000 }\n          );\n        }\n      },\n      (error) => {\n        console.error(\"API Error:\", error);\n        this.snackBar.open(\n          \"Failed to download packaging data. Please try again.\",\n          \"Close\",\n          { duration: 3000 }\n        );\n      }\n    );\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusPolling) {\n      this.statusPolling.unsubscribe();\n    }\n  }\n\n  }\n\n", "<div class=\"account-setup-container\">\n  <!-- Compact header with breadcrumbs and actions -->\n  <div class=\"compact-header\">\n    <div class=\"breadcrumbs\">\n      <a [routerLink]=\"['/dashboard/home']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">home</mat-icon>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <a [routerLink]=\"['/dashboard/account']\" class=\"breadcrumb-item\">\n        <mat-icon class=\"breadcrumb-icon\">business</mat-icon>\n        <span>Accounts</span>\n      </a>\n      <span class=\"breadcrumb-separator\">›</span>\n      <span class=\"breadcrumb-item active\">\n        <mat-icon class=\"breadcrumb-icon\">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>\n        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>\n      </span>\n    </div>\n\n    <div class=\"header-actions\">\n      <button (click)=\"save()\" mat-raised-button color=\"primary\" class=\"save-button\">\n        <mat-icon>save</mat-icon>\n        {{isEditMode ? 'Update' : 'Create'}}\n      </button>\n    </div>\n  </div>\n\n  <div class=\"content-section\">\n    <mat-card class=\"main-card\">\n      <mat-card-content>\n        <!-- Tabbed interface -->\n        <mat-tab-group animationDuration=\"200ms\" [selectedIndex]=\"selectedTabIndex\" (selectedIndexChange)=\"onTabChange($event)\">\n          <!-- Account Information Tab -->\n          <mat-tab>\n            <ng-template mat-tab-label>\n              <mat-icon class=\"tab-icon\">business</mat-icon>\n              <span class=\"tab-label\">Account Information</span>\n            </ng-template>\n            \n            <div class=\"tab-content\">\n              <form class=\"account-form\" [formGroup]=\"registrationForm\">\n                <h3 class=\"form-section-title\">Account Information</h3>\n                <div class=\"compact-form-grid\">\n                  <!-- First row -->\n                  <div class=\"form-row\">\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Tenant Name</mat-label>\n                      <input formControlName=\"tenantName\" matInput placeholder=\"Enter tenant name\" />\n                      <mat-icon matSuffix>business</mat-icon>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Tenant ID</mat-label>\n                      <input formControlName=\"tenantId\" type=\"text\" matInput placeholder=\"Enter tenant ID\"\n                        oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkTenantId($event)\">\n                      <mat-icon matSuffix>fingerprint</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('tenantId').hasError('tenantIdExists')\">\n                        Tenant ID already exists\n                      </mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Account Number</mat-label>\n                      <input formControlName=\"accountNo\" type=\"text\" matInput placeholder=\"Enter account number\"\n                        oninput=\"this.value = this.value.toUpperCase()\" (keyup)=\"checkAccountNo($event)\">\n                      <mat-icon matSuffix>account_balance</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('accountNo').hasError('accountNoExists')\">\n                        Account number already exists\n                      </mat-error>\n                    </mat-form-field>\n                  </div>\n\n                  <!-- Second row -->\n                  <div class=\"form-row\">\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>G-Sheet</mat-label>\n                      <input formControlName=\"gSheet\" type=\"text\" matInput placeholder=\"Enter G-Sheet ID\"\n                        (keyup)=\"checkGSheet($event)\">\n                      <mat-icon matSuffix>table_chart</mat-icon>\n                      <mat-error *ngIf=\"registrationForm.get('gSheet').hasError('gSheetExists')\">\n                        G-Sheet number already exists\n                      </mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Email</mat-label>\n                      <input formControlName=\"emailId\" matInput placeholder=\"Enter email address\" type=\"email\" />\n                      <mat-icon matSuffix>email</mat-icon>\n                    </mat-form-field>\n\n                    <mat-form-field appearance=\"outline\" class=\"form-field\">\n                      <mat-label>Password</mat-label>\n                      <input formControlName=\"password\" matInput placeholder=\"Enter password\"\n                        [type]=\"hidePassword ? 'password' : 'text'\" />\n                      <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n                        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n                      </button>\n                    </mat-form-field>\n                  </div>\n\n                  <!-- Settings section with two-column layout -->\n                  <div class=\"settings-section\">\n                    <div class=\"two-column-grid\">\n                      <!-- Left column: Status options -->\n                      <div class=\"left-column\">\n                        <div class=\"status-header\">\n                          <h4 class=\"section-label\">Account Status</h4>\n                        </div>\n                        <div class=\"status-options\">\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Account</label>\n                            <mat-radio-group formControlName=\"account\" aria-labelledby=\"account-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Active</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Inactive</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Forecast</label>\n                            <mat-radio-group formControlName=\"forecast\" aria-labelledby=\"forecast-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n\n                          <div class=\"status-option\">\n                            <label class=\"status-label\">Sales</label>\n                            <mat-radio-group formControlName=\"sales\" aria-labelledby=\"sales-status-label\" class=\"status-radio-group\">\n                              <mat-radio-button value=\"yes\" color=\"primary\" class=\"compact-radio\">Enabled</mat-radio-button>\n                              <mat-radio-button value=\"no\" color=\"primary\" class=\"compact-radio\">Disabled</mat-radio-button>\n                            </mat-radio-group>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Right column: Logo upload -->\n                      <div class=\"right-column\">\n                        <div class=\"logo-header\">\n                          <h4 class=\"section-label\">Company Logo</h4>\n                        </div>\n                        <div class=\"logo-container\">\n                          <div class=\"logo-preview-container\">\n                            <div class=\"logo-preview\" *ngIf=\"logoUrl\">\n                              <img [src]=\"logoUrl\" alt=\"Company Logo\">\n                            </div>\n                            <div class=\"logo-placeholder\" *ngIf=\"!logoUrl\">\n                              <mat-icon>apps</mat-icon>\n                            </div>\n                          </div>\n                          <div class=\"logo-actions\">\n                            <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                              <mat-icon *ngIf=\"!loadSpinnerForLogo\">cloud_upload</mat-icon>\n                              <div *ngIf=\"loadSpinnerForLogo\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                                <span class=\"sr-only\">Loading...</span>\n                              </div>\n                              <span>Upload Logo</span>\n                            </button>\n                            <input #fileInput type=\"file\" (change)=\"onFileSelected($event)\" accept=\"image/*\" style=\"display: none;\">\n                            <mat-error *ngIf=\"registrationForm.get('logo').invalid && registrationForm.get('logo').touched\" class=\"logo-error\">\n                              Please upload a logo\n                            </mat-error>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </mat-tab>\n\n          <!-- AI Data Generation Tab -->\n          <mat-tab [disabled]=\"!tenantCreated\">\n            <ng-template mat-tab-label>\n              <mat-icon class=\"tab-icon\">auto_awesome</mat-icon>\n              <span class=\"tab-label\">AI Data Generation</span>\n            </ng-template>\n\n            <div class=\"tab-content ai-tab-content\">\n              <!-- Nested tabs for AI section -->\n              <mat-tab-group animationDuration=\"200ms\" class=\"nested-tabs\">\n                <!-- Agent Chat Tab -->\n                <mat-tab>\n                  <ng-template mat-tab-label>\n                    <mat-icon class=\"tab-icon\">chat</mat-icon>\n                    <span class=\"tab-label\">Agent Chat</span>\n                  </ng-template>\n\n                  <div class=\"chat-interface\">\n                    <div class=\"chat-header\">\n                      <div class=\"chat-title\">\n                        <mat-icon class=\"chat-icon\">smart_toy</mat-icon>\n                        <h3>Restaurant Information Assistant</h3>\n                      </div>\n                      <p class=\"chat-description\">\n                        Please provide information about your restaurant to help us generate more accurate AI-powered datasets.\n                      </p>\n                    </div>\n\n                    <div class=\"chat-content\">\n                      <app-chat-bot [tenantId]=\"registrationForm.value.tenantId\"\n                        [tenantName]=\"registrationForm.value.tenantName\">\n                      </app-chat-bot>\n                    </div>\n\n                    <div class=\"chat-actions\">\n                      <button mat-raised-button color=\"primary\" (click)=\"startAIProcessing()\" class=\"next-step-button\">\n                        <span>Continue to Data Processing</span>\n                        <mat-icon>arrow_forward</mat-icon>\n                      </button>\n                    </div>\n                  </div>\n                </mat-tab>\n\n                <!-- Dataset Download Tab -->\n                <mat-tab [disabled]=\"!aiDataAvailable\">\n                  <ng-template mat-tab-label>\n                    <mat-icon class=\"tab-icon\">download</mat-icon>\n                    <span class=\"tab-label\">Dataset Download</span>\n                  </ng-template>\n\n                  <div class=\"dataset-download-section\">\n                    <!-- Initial state - before starting process -->\n                    <div *ngIf=\"!isDownloading && !downloadComplete && !downloadFailed\" class=\"ai-intro-panel\">\n                      <div class=\"intro-content\">\n                        <div class=\"intro-header\">\n                          <mat-icon class=\"intro-icon\">auto_awesome</mat-icon>\n                          <h3>Generate AI-Powered Datasets</h3>\n                        </div>\n                        <p>Enhance your tenant with AI-optimized inventory and packaging solutions. Our advanced algorithms will\n                          analyze your business needs and create personalized recommendations.</p>\n                        <p class=\"info-note\"><mat-icon class=\"info-icon\">info</mat-icon> This process takes approximately 15 minutes to complete. You can continue using\n                          the system while processing runs in the background.</p>\n                      </div>\n                      <div class=\"intro-action\">\n                        <button mat-raised-button color=\"primary\" (click)=\"startDataDownload()\" class=\"generate-button\">\n                          <mat-icon>play_arrow</mat-icon>\n                          Start Generation\n                        </button>\n                      </div>\n                    </div>\n\n                    <!-- Processing state -->\n                    <div *ngIf=\"isDownloading\" class=\"ai-processing-panel\">\n                      <div class=\"processing-header\">\n                        <mat-icon class=\"processing-icon rotating\">autorenew</mat-icon>\n                        <h3>Processing Your Data</h3>\n                      </div>\n\n                      <!-- Progress indicator -->\n                      <div class=\"progress-container\">\n                        <mat-progress-bar mode=\"determinate\" [value]=\"downloadProgress\" color=\"accent\"></mat-progress-bar>\n                        <div class=\"progress-label\">{{downloadProgress}}% Complete</div>\n                      </div>\n\n                      <!-- Estimated time -->\n                      <div class=\"estimated-time\">\n                        <mat-icon>access_time</mat-icon>\n                        <span *ngIf=\"estimatedTimeRemaining > 60\">\n                          Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute\n                        </span>\n                        <span *ngIf=\"estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60\">\n                          Estimated time remaining: less than a minute\n                        </span>\n                        <span *ngIf=\"estimatedTimeRemaining === 0\" class=\"calculating\">\n                          Calculating...\n                        </span>\n                      </div>\n\n                      <!-- Processing steps -->\n                      <div class=\"processing-steps\">\n                        <div *ngFor=\"let step of downloadSteps; let i = index\" class=\"step-row\"\n                          [ngClass]=\"{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}\">\n\n                          <div class=\"step-status\">\n                            <mat-icon *ngIf=\"step.completed\">check_circle</mat-icon>\n                            <div *ngIf=\"!step.completed && activeStep === i\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                              <span class=\"visually-hidden\">Loading...</span>\n                            </div>\n                            <mat-icon *ngIf=\"!step.completed && activeStep !== i\">radio_button_unchecked</mat-icon>\n                          </div>\n\n                          <div class=\"step-details\">\n                            <div class=\"step-name\">{{step.name}}</div>\n                            <div class=\"step-description\">{{step.description}}</div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Helpful tips section -->\n                      <div class=\"tips-section\">\n                        <div class=\"tip-header\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>Did You Know?</span>\n                        </div>\n                        <div class=\"tip-content\">\n                          AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand\n                          insights\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Download complete state -->\n                    <div *ngIf=\"downloadComplete\" class=\"ai-complete-panel\">\n                      <div class=\"success-header\">\n                        <mat-icon class=\"success-icon\">task_alt</mat-icon>\n                        <h3>Processing Complete!</h3>\n                      </div>\n\n                      <p class=\"success-message\">Your AI-powered datasets have been generated successfully and are ready for download.</p>\n\n                      <div class=\"download-options\">\n                        <!-- Inventory Dataset Card -->\n                        <mat-card class=\"download-card\">\n                          <mat-card-header>\n                            <div mat-card-avatar class=\"inventory-icon\">\n                              <mat-icon>inventory_2</mat-icon>\n                            </div>\n                            <mat-card-title>Inventory Dataset</mat-card-title>\n                            <mat-card-subtitle>AI-Optimized Inventory Master</mat-card-subtitle>\n                          </mat-card-header>\n                          <mat-card-content>\n                            <ul class=\"dataset-features\">\n                              <li>Optimized stock levels based on demand forecasting</li>\n                              <li>Intelligent categorization and tagging</li>\n                              <li>Ready for import into your inventory system</li>\n                            </ul>\n                          </mat-card-content>\n                          <mat-card-actions>\n                            <button mat-raised-button color=\"primary\" (click)=\"downloadInventory()\" class=\"download-button\">\n                              <mat-icon>download</mat-icon> Download\n                            </button>\n                          </mat-card-actions>\n                        </mat-card>\n\n                        <!-- Packaging Dataset Card -->\n                        <mat-card class=\"download-card\">\n                          <mat-card-header>\n                            <div mat-card-avatar class=\"packaging-icon\">\n                              <mat-icon>category</mat-icon>\n                            </div>\n                            <mat-card-title>Packaging Dataset</mat-card-title>\n                            <mat-card-subtitle>AI-Optimized Packaging Master</mat-card-subtitle>\n                          </mat-card-header>\n                          <mat-card-content>\n                            <ul class=\"dataset-features\">\n                              <li>Cost-effective packaging recommendations</li>\n                              <li>Sustainable options highlighted</li>\n                              <li>Compatibility with your inventory items</li>\n                            </ul>\n                          </mat-card-content>\n                          <mat-card-actions>\n                            <button mat-raised-button color=\"primary\" (click)=\"downloadPackaging()\" class=\"download-button\">\n                              <mat-icon>download</mat-icon> Download\n                            </button>\n                          </mat-card-actions>\n                        </mat-card>\n                      </div>\n                    </div>\n\n                    <!-- Error state -->\n                    <div *ngIf=\"downloadFailed\" class=\"ai-error-panel\">\n                      <div class=\"error-header\">\n                        <mat-icon class=\"error-icon\">error_outline</mat-icon>\n                        <h3>Processing Failed</h3>\n                      </div>\n\n                      <p class=\"error-message\">We encountered an issue while generating your AI datasets. This could be due to server\n                        load or connection issues.</p>\n\n                      <div class=\"error-actions\">\n                        <button mat-raised-button color=\"warn\" (click)=\"startDataDownload()\" class=\"retry-button\">\n                          <mat-icon>refresh</mat-icon> Try Again\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </mat-tab>\n              </mat-tab-group>\n            </div>\n          </mat-tab>\n        </mat-tab-group>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAsBC,WAAW,EAAaC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAClH,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAAyBC,oBAAoB,QAAO,gCAAgC;AACpF,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAO1D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACjE,SAASC,EAAE,EAAEC,QAAQ,QAAsB,MAAM;AAEjD,OAAO,KAAKC,IAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;ICYdC,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAoB1CH,EAAA,CAAAC,cAAA,gBAA+E;IAC7ED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA6DNH,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAI,SAAA,cAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,OAAA,EAAAR,EAAA,CAAAS,aAAA,CAAe;;;;;IAEtBT,EAAA,CAAAC,cAAA,cAA+C;IACnCD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKzBH,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC7DH,EAAA,CAAAC,cAAA,cAAuF;IAC/DD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK3CH,EAAA,CAAAC,cAAA,oBAAmH;IACjHD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAc1BH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS3CH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAgCzCH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAK/CH,EAAA,CAAAC,cAAA,cAA2F;IAGxDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iLACmE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,YAAqB;IAA4BD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,4IACZ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EAAA,CAAAC,cAAA,eAA0B;IACkBD,EAAA,CAAAU,UAAA,mBAAAC,gEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAoBTH,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkB,kBAAA,gCAAAlB,EAAA,CAAAmB,WAAA,OAAAC,OAAA,CAAAC,sBAAA,mCACF;;;;;IACArB,EAAA,CAAAC,cAAA,WAAyE;IACvED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASHH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxDH,EAAA,CAAAC,cAAA,cAAwG;IACxED,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEjDH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;;;;;;;IAR3FH,EAAA,CAAAC,cAAA,eACuI;IAGnID,EAAA,CAAAsB,UAAA,IAAAC,wDAAA,uBAAwD;IACxDvB,EAAA,CAAAsB,UAAA,IAAAE,mDAAA,kBAEM;IACNxB,EAAA,CAAAsB,UAAA,IAAAG,wDAAA,uBAAuF;IACzFzB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACDD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZ1DH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,SAAA,EAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,GAAAJ,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,EAAoI;IAGvHhC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,SAAAsB,QAAA,CAAAC,SAAA,CAAoB;IACzB7B,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAsB,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAGpChC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,UAAA,UAAAsB,QAAA,CAAAC,SAAA,IAAAC,OAAA,CAAAC,UAAA,KAAAC,KAAA,CAAyC;IAI7BhC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAiC,iBAAA,CAAAL,QAAA,CAAAM,IAAA,CAAa;IACNlC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAiC,iBAAA,CAAAL,QAAA,CAAAO,WAAA,CAAoB;;;;;IAzC1DnC,EAAA,CAAAC,cAAA,cAAuD;IAERD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI/BH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAI,SAAA,2BAAkG;IAClGJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlEH,EAAA,CAAAC,cAAA,eAA4B;IAChBD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAsB,UAAA,KAAAc,8CAAA,mBAEO;IACPpC,EAAA,CAAAsB,UAAA,KAAAe,8CAAA,mBAEO;IACPrC,EAAA,CAAAsB,UAAA,KAAAgB,8CAAA,mBAEO;IACTtC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAsB,UAAA,KAAAiB,6CAAA,oBAeM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEZD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAE,MAAA,uHAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA/C+BH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,UAAAkC,OAAA,CAAAC,gBAAA,CAA0B;IACnCzC,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAkB,kBAAA,KAAAsB,OAAA,CAAAC,gBAAA,eAA8B;IAMnDzC,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,MAAiC;IAGjCrB,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,QAAAmB,OAAA,CAAAnB,sBAAA,OAAgE;IAGhErB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,SAAAkC,OAAA,CAAAnB,sBAAA,OAAkC;IAOnBrB,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,YAAAkC,OAAA,CAAAE,aAAA,CAAkB;;;;;;IAgC5C1C,EAAA,CAAAC,cAAA,eAAwD;IAErBD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,4FAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEpHH,EAAA,CAAAC,cAAA,eAA8B;IAKZD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAEVD,EAAA,CAAAE,MAAA,0DAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,8CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGxDH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAiC,gEAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA6B,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrE9C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAC,cAAA,qBAAgC;IAGhBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE/BH,EAAA,CAAAC,cAAA,sBAAgB;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAClDH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEtEH,EAAA,CAAAC,cAAA,wBAAkB;IAEVD,EAAA,CAAAE,MAAA,gDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGpDH,EAAA,CAAAC,cAAA,wBAAkB;IAC0BD,EAAA,CAAAU,UAAA,mBAAAqC,gEAAA;MAAA/C,EAAA,CAAAY,aAAA,CAAAgC,IAAA;MAAA,MAAAI,OAAA,GAAAhD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAgC,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEjD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAOjBH,EAAA,CAAAC,cAAA,eAAmD;IAElBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,wHACG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA2B;IACcD,EAAA,CAAAU,UAAA,mBAAAwC,+DAAA;MAAAlD,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAoC,OAAA,CAAAnC,iBAAA,EAAmB;IAAA,EAAC;IAClEjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,mBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;AD1VjC,MAuBakD,qBAAqB;EAqChCC,YACSC,MAAiB,EAChBC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,UAA4B,EAC5BC,MAA4B,EAC5BC,iBAAoC,EACpCC,GAAqB,EACrBC,IAAiB,EACjBC,EAAqB,EACeC,UAAe,EACnDC,QAAqB,EACrBC,UAAsB;IAZvB,KAAAZ,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACkC,KAAAC,UAAU,GAAVA,UAAU;IAC9C,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IA/CpB,KAAAC,sBAAsB,GAAG,KAAK;IAI9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAhE,OAAO,GAAkB,IAAI;IAC7B,KAAAiE,YAAY,GAAY,IAAI;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAG3B,KAAAhC,aAAa,GAAG,CACd;MAAC,MAAM,EAAE,6BAA6B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6C,CAAC,EACzH;MAAC,MAAM,EAAE,sCAAsC;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA0D,CAAC,EAC/I;MAAC,MAAM,EAAE,0BAA0B;MAAE,WAAW,EAAE,KAAK;MAAE,aAAa,EAAE;IAA6D,CAAC,CACzI;IAIC,KAAAiC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAnC,gBAAgB,GAAW,CAAC;IAC5B,KAAAoC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA/C,UAAU,GAAW,CAAC;IACtB,KAAAV,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAA0D,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAY,KAAK;IAkB/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAAClB,IAAI,CAACmB,cAAc,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACxB,UAAU,CAACyB,WAAW,EAAE,CAACC,KAAK;IAEnD;IACA,IAAI,IAAI,CAACpB,UAAU,EAAE;MACnB,IAAI,CAACqB,WAAW,GAAG,IAAI,CAACrB,UAAU,CAACsB,GAAG;KACvC,MAAM;MACL,IAAI,CAACD,WAAW,GAAG,KAAK;;IAE1B,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAAC9B,EAAE,CAAC+B,KAAK,CAAC;MACpCC,QAAQ,EAAE,IAAI/G,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAE;MAC3DC,UAAU,EAAE,IAAIjH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MAC5DE,OAAO,EAAE,IAAIlH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MACzDG,MAAM,EAAE,IAAInH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MACxDI,SAAS,EAAE,IAAIpH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MAC3DK,QAAQ,EAAE,IAAIrH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MAC1DM,OAAO,EAAE,IAAItH,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MAC3DO,QAAQ,EAAE,IAAIvH,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MAC5DQ,KAAK,EAAE,IAAIxH,WAAW,CAAS,IAAI,EAAEG,UAAU,CAAC6G,QAAQ,CAAC;MACzDS,IAAI,EAAE,IAAIzH,WAAW,CAAS,EAAE,EAAEG,UAAU,CAAC6G,QAAQ;KACtD,CAAc;EAEjB;EAEAU,QAAQA,CAAA;IACN;IACA,IAAI,CAAC3B,UAAU,GAAG,KAAK;IAEvB;IACA,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACsB,GAAG,IAAI,KAAK,EAAE;MACnD,IAAI,CAACb,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAAC4B,WAAW,CAAC,IAAI,CAACrC,UAAU,CAACsC,QAAQ,CAAC;KAC3C,MAAM;MACL;MACA,IAAI,CAAC9C,KAAK,CAAC+C,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAAChC,UAAU,GAAG,IAAI,CAAC,CAAC;UAExB;UACA,IAAI,CAACZ,GAAG,CAAC6C,cAAc,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAACD,SAAS,CAAC;YAC9CG,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,IAAI,EAAE;gBAC3B,IAAI,CAACT,WAAW,CAACO,GAAG,CAACE,IAAI,CAAC;eAC3B,MAAM;gBACL,IAAI,CAACrC,UAAU,GAAG,KAAK,CAAC,CAAC;gBACzB,IAAI,CAACd,MAAM,CAACoD,iBAAiB,CAAC,mBAAmB,CAAC;gBAClD,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;;YAEhD,CAAC;YACDC,KAAK,EAAGC,GAAG,IAAI;cACb,IAAI,CAACzC,UAAU,GAAG,KAAK,CAAC,CAAC;cACzB0C,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;cAClD,IAAI,CAACvD,MAAM,CAACoD,iBAAiB,CAAC,6BAA6B,CAAC;cAC5D,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC9C;WACD,CAAC;;MAEN,CAAC,CAAC;;EAEN;EAEAX,WAAWA,CAACS,IAAI;IACd,IAAI,CAACvB,gBAAgB,CAAC6B,UAAU,CAAC;MAC/BzB,UAAU,EAAEmB,IAAI,CAACnB,UAAU;MAC3BF,QAAQ,EAAEqB,IAAI,CAACrB,QAAQ;MACvBG,OAAO,EAAEkB,IAAI,CAAClB,OAAO;MACrBC,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBC,SAAS,EAAEgB,IAAI,CAAChB,SAAS;MACzBC,QAAQ,EAAEe,IAAI,CAACf,QAAQ;MACvBC,OAAO,EAAEc,IAAI,CAACO,MAAM,CAACrB,OAAO,GAAG,KAAK,GAAG,IAAI;MAC3CC,QAAQ,EAAEa,IAAI,CAACO,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7CC,KAAK,EAAEY,IAAI,CAACO,MAAM,CAACnB,KAAK,GAAG,KAAK,GAAG,IAAI;MACvCC,IAAI,EAAEW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,IAAI;KACnC,CAAC;IACF,IAAIW,IAAI,CAACQ,aAAa,EAAEnB,IAAI,EAAE;MAC5B,IAAI,CAAC5F,OAAO,GAAGuG,IAAI,CAACQ,aAAa,CAACnB,IAAI;MACtC,IAAI,CAAC5B,aAAa,GAAG,CAAC;QACpBgD,GAAG,EAAET,IAAI,CAACQ,aAAa,CAACnB;OACzB,CAAC;;IAEJ,IAAI,CAACpC,EAAE,CAACyD,aAAa,EAAE;EACzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAClE,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAU,IAAIA,CAAA;IACF,IAAI,IAAI,CAACnC,gBAAgB,CAACoC,OAAO,EAAE;MACjC,IAAI,CAACpC,gBAAgB,CAACqC,gBAAgB,EAAE;MACxC,IAAI,CAACjE,MAAM,CAACoD,iBAAiB,CAAC,qCAAqC,CAAC;KACrE,MAAM;MACL,IAAIc,MAAM,GAAG,IAAI,CAACtC,gBAAgB,CAACH,KAAK;MACxC,IAAI0C,GAAG,GAAQ;QACTrC,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ;QACzBE,UAAU,EAAEkC,MAAM,CAAClC,UAAU;QAC7BG,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BF,OAAO,EAAEiC,MAAM,CAACjC,OAAO;QACvBG,QAAQ,EAAE8B,MAAM,CAAC9B,QAAQ;QACzBF,MAAM,EAAEgC,MAAM,CAAChC,MAAM;QACrBwB,MAAM,EAAE;UACNrB,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,IAAI,KAAK;UAChCC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,KAAK;UAClCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI;SACxB;QACDoB,aAAa,EAAE;UACfnB,IAAI,EAAE,IAAI,CAAC5B,aAAa,CAACwD,MAAM,GAAG,CAAC,GAAG,IAAI,CAACxD,aAAa,CAAC,CAAC,CAAC,CAACgD,GAAG,GAAG;;OAEvE;MACD,IAAI,CAAC1D,GAAG,CAACmE,WAAW,CAACF,GAAG,CAAC,CAACtB,SAAS,CAAC;QAClCG,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAAClD,MAAM,CAACsE,mBAAmB,CAAC,IAAI,CAACxD,UAAU,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;YAClH,IAAI,CAAClB,MAAM,CAACyD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;YAC5C,IAAI,CAACpD,iBAAiB,CAACsE,mBAAmB,EAAE;WAC7C,MAAM;YACL,IAAI,CAACvE,MAAM,CAACoD,iBAAiB,CAAC,uBAAuB,CAAC;;QAE1D,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACgB,GAAG,CAACjB,GAAG,CAAC;QAClB;OACD,CAAC;;EAEN;EAEAkB,aAAaA,CAACC,WAAW;IACvBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAClD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACpD,UAAU,CAACyB,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMmD,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAChD,QAAQ,KAAM4C,WAAW,CAAC;IAC3E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAChD,gBAAgB,CAACmD,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC;QAAE,gBAAgB,EAAE;MAAI,CAAE,CAAC;KAC5E,MAAM;MACL,IAAI,CAACpD,gBAAgB,CAACmD,GAAG,CAAC,UAAU,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAEvD;EAEAC,cAAcA,CAACP,WAAW;IACxBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAClD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACpD,UAAU,CAACyB,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMmD,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3C,SAAS,KAAMuC,WAAW,CAAC;IAC5E,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAChD,gBAAgB,CAACmD,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC;QAAE,iBAAiB,EAAE;MAAI,CAAE,CAAC;KAC9E,MAAM;MACL,IAAI,CAACpD,gBAAgB,CAACmD,GAAG,CAAC,WAAW,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAExD;EAEAE,WAAWA,CAACR,WAAW;IACrBA,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAClD,KAAK;IACtC,IAAI0B,IAAI,GAAG,IAAI,CAACpD,UAAU,CAACyB,WAAW,EAAE,CAACC,KAAK;IAC9C,MAAMmD,eAAe,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAMwC,WAAW,CAAC;IACzE,IAAIE,eAAe,EAAE;MACnB,IAAI,CAAChD,gBAAgB,CAACmD,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAAE,cAAc,EAAE;MAAI,CAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAACpD,gBAAgB,CAACmD,GAAG,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;;EAErD;EAEAG,cAAcA,CAACC,KAAY;IAC3B,MAAMC,KAAK,GAAGD,KAAK,CAACT,MAA0B;IAC9C,IAAIU,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAAC1E,aAAa,GAAG,EAAE;MACvB,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9B6E,KAAK,CAACC,IAAI,CAACH,KAAK,CAACC,KAAK,CAAC,CAACG,OAAO,CAACC,IAAI,IAAG;QACrC,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACF,MAAM,GAAG,MAAK;YAChB,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAA6B;YAC/DJ,MAAM,CAACK,KAAK,GAAG,GAAG;YAClBL,MAAM,CAACM,MAAM,GAAG,GAAG;YACnBH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;YAClC,MAAMU,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;YAC5C,IAAI,CAAChK,OAAO,GAAG+J,MAAM;YACrB,IAAI,CAAC/E,gBAAgB,CAAC6B,UAAU,CAAC;cAAEjB,IAAI,EAAE,IAAI,CAAC5F;YAAO,CAAE,CAAC;YACxD,IAAI,CAACgE,aAAa,CAACiG,IAAI,CAAC;cAAEjD,GAAG,EAAE+C;YAAM,CAAE,CAAC;YACxC,IAAI,CAACjG,kBAAkB,GAAG,KAAK;YAC/B,IAAI,CAACN,EAAE,CAACyD,aAAa,EAAE;UACzB,CAAC;UACDoC,GAAG,CAACa,GAAG,GAAGd,CAAC,CAACrB,MAAM,CAACoC,MAAM;QAC3B,CAAC;QACDlB,MAAM,CAACmB,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;;EAEJ;EAGAuB,UAAUA,CAAA;IACR,IAAI,CAACnI,aAAa,CAAC2G,OAAO,CAACyB,IAAI,IAAIA,IAAI,CAACjJ,SAAS,GAAG,KAAK,CAAC;IAC1D,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAECd,iBAAiBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACuE,gBAAgB,CAACoC,OAAO,EAAE;MACjC,IAAI,CAAChE,MAAM,CAACoD,iBAAiB,CAAC,iEAAiE,CAAC;MAChG;;IAGF;IACA,IAAI,CAACjC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAChB,EAAE,CAACyD,aAAa,EAAE;IAEvB;IACAsD,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,mBAAmB,CAAC;MAClE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACxG,aAAa,GAAG,IAAI;IACzB,IAAI,CAACnC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACpB,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACwD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC+F,UAAU,EAAE;IACjB,MAAMnF,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAACH,KAAK,CAACK,QAAQ;IAErD,IAAI,CAAC5B,GAAG,CAACuH,eAAe,CAAC3F,QAAQ,CAAC,CAAC4F,IAAI,CACrC5L,UAAU,CAAC6L,MAAM,IAAG;MAClB,IAAI,CAACC,mBAAmB,CAAC,+CAA+C,CAAC;MACzE,OAAO3L,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAAC4G,SAAS,CAAEgF,QAAa,IAAI;MAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC3E,OAAO,EAAE;QAChC,IAAI,CAAC4E,YAAY,GAAGD,QAAQ,CAACC,YAAY;QACzC,IAAI,CAACC,kBAAkB,CAACjG,QAAQ,CAAC;OAClC,MAAM;QACL,IAAI,CAAC8F,mBAAmB,CAAC,oDAAoD,CAAC;;IAElF,CAAC,CAAC;EACJ;EAEAI,aAAaA,CAAA;IACX,IAAI,CAAC5G,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAChB,EAAE,CAACyD,aAAa,EAAE;EACzB;EAEAkE,kBAAkBA,CAACjG,QAAgB;IACjC,IAAI,CAACmG,aAAa,GAAG/L,QAAQ,CAAC,KAAK,CAAC,CAACwL,IAAI,CACvC3L,SAAS,CAAC,MAAM,IAAI,CAACmE,GAAG,CAACgI,SAAS,CAACpG,QAAQ,CAAC,CAAC,EAC7C9F,SAAS,CAAE6L,QAAa,IAAI;MAC1B,OAAOA,QAAQ,IAAIA,QAAQ,CAACnE,MAAM,KAAK,UAAU,IAAImE,QAAQ,CAACnE,MAAM,KAAK,QAAQ;IACnF,CAAC,EAAE,IAAI,CAAC,CACT,CAACb,SAAS,CAAEgF,QAAa,IAAI;MAC5B,IAAI,CAACA,QAAQ,EAAE;QACb,IAAI,CAACD,mBAAmB,CAAC,8CAA8C,CAAC;QACxE;;MAGF,IAAIC,QAAQ,CAACnE,MAAM,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACkE,mBAAmB,CAACC,QAAQ,CAACM,OAAO,IAAI,sCAAsC,CAAC;QACpF;;MAGF,IAAI,CAACtJ,gBAAgB,GAAGgJ,QAAQ,CAACO,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAAC3K,sBAAsB,GAAGoK,QAAQ,CAACQ,wBAAwB,IAAI,CAAC;MAEpE,IAAIR,QAAQ,CAACS,WAAW,KAAKC,SAAS,IAAIV,QAAQ,CAACS,WAAW,IAAI,CAAC,EAAE;QACnE,IAAI,CAACnK,UAAU,GAAG0J,QAAQ,CAACS,WAAW;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIX,QAAQ,CAACS,WAAW,EAAEE,CAAC,EAAE,EAAE;UAC9C,IAAIA,CAAC,GAAG,IAAI,CAAC1J,aAAa,CAACsF,MAAM,EAAE;YACjC,IAAI,CAACtF,aAAa,CAAC0J,CAAC,CAAC,CAACvK,SAAS,GAAG,IAAI;;;;MAK5C,IAAI,CAACmC,EAAE,CAACyD,aAAa,EAAE;MAEvB,IAAIgE,QAAQ,CAACnE,MAAM,KAAK,UAAU,EAAE;QAClC,IAAI,CAAC+E,gBAAgB,EAAE;;IAE3B,CAAC,EAAEd,MAAM,IAAG;MACV,IAAI,CAACC,mBAAmB,CAAC,oDAAoD,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAa,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACb,EAAE,CAACyD,aAAa,EAAE;IACvB,IAAI,CAACvD,QAAQ,CAACqI,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;MAChEC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAlB,mBAAmBA,CAACO,OAAe;IACjC,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;IAGlC,IAAI,CAAC1H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACZ,QAAQ,CAACqI,IAAI,CAACR,OAAO,EAAE,OAAO,EAAE;MACnCS,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE;KACnB,CAAC,CAACC,QAAQ,EAAE,CAAClG,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACxF,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGA6B,iBAAiBA,CAAA;IACf,IAAI,CAACgB,GAAG,CAAC8I,YAAY,CAAC,WAAW,EAAE,IAAI,CAACpH,gBAAgB,CAACH,KAAK,CAACK,QAAQ,CAAC,CAACe,SAAS,CAC/EoG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGnN,IAAI,CAACoN,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC5H,gBAAgB,CAACH,KAAK,CAACK,QAAQ,sBAAsB;QAC9E3F,IAAI,CAACsN,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOlG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAChD,QAAQ,CAACqI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAtF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAChD,QAAQ,CAACqI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAGAvJ,iBAAiBA,CAAA;IACf,IAAI,CAACa,GAAG,CAAC8I,YAAY,CAAC,SAAS,EAAE,IAAI,CAACpH,gBAAgB,CAACH,KAAK,CAACK,QAAQ,CAAC,CAACe,SAAS,CAC7EoG,UAAkB,IAAI;MACrB,IAAI;QACF,MAAMC,eAAe,GAAGD,UAAU,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QAClE,MAAMC,YAAY,GAAGC,IAAI,CAACH,eAAe,CAAC;QAC1C,MAAMI,QAAQ,GAAGnN,IAAI,CAACoN,IAAI,CAACH,YAAY,EAAE;UAAEzD,IAAI,EAAE;QAAQ,CAAE,CAAC;QAC5D,MAAM6D,QAAQ,GAAG,GAAG,IAAI,CAAC5H,gBAAgB,CAACH,KAAK,CAACK,QAAQ,sBAAsB;QAC9E3F,IAAI,CAACsN,SAAS,CAACH,QAAQ,EAAEE,QAAQ,CAAC;OACnC,CAAC,OAAOlG,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAChD,QAAQ,CAACqI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;;IAEL,CAAC,EACAtF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAChD,QAAQ,CAACqI,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;IACH,CAAC,CACF;EACH;EAEAc,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACS,WAAW,EAAE;;EAEpC;;;uBAhbWjJ,qBAAqB,EAAArD,EAAA,CAAAuN,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAzN,EAAA,CAAAuN,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA3N,EAAA,CAAAuN,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA5N,EAAA,CAAAuN,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA9N,EAAA,CAAAuN,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAhO,EAAA,CAAAuN,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAlO,EAAA,CAAAuN,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAApO,EAAA,CAAAuN,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAtO,EAAA,CAAAuN,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAAxO,EAAA,CAAAuN,iBAAA,CAAAvN,EAAA,CAAAyO,iBAAA,GAAAzO,EAAA,CAAAuN,iBAAA,CAgDVrO,eAAe,MAAAc,EAAA,CAAAuN,iBAAA,CAAAmB,EAAA,CAAAC,WAAA,GAAA3O,EAAA,CAAAuN,iBAAA,CAAAqB,GAAA,CAAAC,UAAA;IAAA;EAAA;;;YAhD1BxL,qBAAqB;MAAAyL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhP,EAAA,CAAAiP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAArF,GAAA;QAAA,IAAAqF,EAAA;;UClDlCvP,EAAA,CAAAC,cAAA,aAAqC;UAKKD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEnDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,aAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,WAAiE;UAC7BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,eAAqC;UACDD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnFH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIhEH,EAAA,CAAAC,cAAA,cAA4B;UAClBD,EAAA,CAAAU,UAAA,mBAAA8O,wDAAA;YAAA,OAAStF,GAAA,CAAAvC,IAAA,EAAM;UAAA,EAAC;UACtB3H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA6B;UAIqDD,EAAA,CAAAU,UAAA,iCAAA+O,6EAAAC,MAAA;YAAA,OAAuBxF,GAAA,CAAAyF,WAAA,CAAAD,MAAA,CAAmB;UAAA,EAAC;UAErH1P,EAAA,CAAAC,cAAA,eAAS;UACPD,EAAA,CAAAsB,UAAA,KAAAsO,6CAAA,0BAGc;UAEd5P,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA+B;UAIdD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAI,SAAA,iBAA+E;UAC/EJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGzCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAC,cAAA,iBACkF;UAAhCD,EAAA,CAAAU,UAAA,mBAAAmP,uDAAAH,MAAA;YAAA,OAASxF,GAAA,CAAA7B,aAAA,CAAAqH,MAAA,CAAqB;UAAA,EAAC;UADjF1P,EAAA,CAAAG,YAAA,EACkF;UAClFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAsB,UAAA,KAAAwO,2CAAA,wBAEY;UACd9P,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,iBACmF;UAAjCD,EAAA,CAAAU,UAAA,mBAAAqP,uDAAAL,MAAA;YAAA,OAASxF,GAAA,CAAArB,cAAA,CAAA6G,MAAA,CAAsB;UAAA,EAAC;UADlF1P,EAAA,CAAAG,YAAA,EACmF;UACnFH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAsB,UAAA,KAAA0O,2CAAA,wBAEY;UACdhQ,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAAsB;UAEPD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,iBACgC;UAA9BD,EAAA,CAAAU,UAAA,mBAAAuP,uDAAAP,MAAA;YAAA,OAASxF,GAAA,CAAApB,WAAA,CAAA4G,MAAA,CAAmB;UAAA,EAAC;UAD/B1P,EAAA,CAAAG,YAAA,EACgC;UAChCH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAsB,UAAA,KAAA4O,2CAAA,wBAEY;UACdlQ,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,iBAA2F;UAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGtCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,iBACgD;UAChDJ,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAU,UAAA,mBAAAyP,wDAAA;YAAA,OAAAjG,GAAA,CAAAzF,YAAA,IAAAyF,GAAA,CAAAzF,YAAA;UAAA,EAAsC;UACtEzE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAM7EH,EAAA,CAAAC,cAAA,eAA8B;UAKID,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,2BAA6G;UACvCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC7FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,eAA2B;UACGD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,2BAA+G;UACzCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,4BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAIlGH,EAAA,CAAAC,cAAA,gBAA2B;UACGD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,4BAAyG;UACnCD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAC9FH,EAAA,CAAAC,cAAA,6BAAmE;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAmB;UAOtGH,EAAA,CAAAC,cAAA,gBAA0B;UAEID,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7CH,EAAA,CAAAC,cAAA,gBAA4B;UAExBD,EAAA,CAAAsB,UAAA,MAAA8O,sCAAA,kBAEM;UACNpQ,EAAA,CAAAsB,UAAA,MAAA+O,sCAAA,kBAEM;UACRrQ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAA4P,yDAAA;YAAAtQ,EAAA,CAAAY,aAAA,CAAA2P,IAAA;YAAA,MAAAC,GAAA,GAAAxQ,EAAA,CAAAyQ,WAAA;YAAA,OAASzQ,EAAA,CAAAgB,WAAA,CAAAwP,GAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UACnE1Q,EAAA,CAAAsB,UAAA,MAAAqP,2CAAA,uBAA6D;UAC7D3Q,EAAA,CAAAsB,UAAA,MAAAsP,sCAAA,kBAEM;UACN5Q,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1BH,EAAA,CAAAC,cAAA,sBAAwG;UAA1ED,EAAA,CAAAU,UAAA,oBAAAmQ,yDAAAnB,MAAA;YAAA,OAAUxF,GAAA,CAAAnB,cAAA,CAAA2G,MAAA,CAAsB;UAAA,EAAC;UAA/D1P,EAAA,CAAAG,YAAA,EAAwG;UACxGH,EAAA,CAAAsB,UAAA,MAAAwP,4CAAA,wBAEY;UACd9Q,EAAA,CAAAG,YAAA,EAAM;UAWtBH,EAAA,CAAAC,cAAA,oBAAqC;UACnCD,EAAA,CAAAsB,UAAA,MAAAyP,8CAAA,0BAGc;UAEd/Q,EAAA,CAAAC,cAAA,gBAAwC;UAKlCD,EAAA,CAAAsB,UAAA,MAAA0P,8CAAA,0BAGc;UAEdhR,EAAA,CAAAC,cAAA,gBAA4B;UAGMD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChDH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,yCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE3CH,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAE,MAAA,kHACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,gBAA0B;UACxBD,EAAA,CAAAI,SAAA,yBAEe;UACjBJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA0B;UACkBD,EAAA,CAAAU,UAAA,mBAAAuQ,yDAAA;YAAA,OAAS/G,GAAA,CAAAkB,iBAAA,EAAmB;UAAA,EAAC;UACrEpL,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxCH,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAO1CH,EAAA,CAAAC,cAAA,oBAAuC;UACrCD,EAAA,CAAAsB,UAAA,MAAA4P,8CAAA,0BAGc;UAEdlR,EAAA,CAAAC,cAAA,gBAAsC;UAEpCD,EAAA,CAAAsB,UAAA,MAAA6P,sCAAA,mBAiBM;UAGNnR,EAAA,CAAAsB,UAAA,MAAA8P,sCAAA,mBAyDM;UAGNpR,EAAA,CAAAsB,UAAA,MAAA+P,sCAAA,mBAuDM;UAGNrR,EAAA,CAAAsB,UAAA,MAAAgQ,sCAAA,mBAcM;UACRtR,EAAA,CAAAG,YAAA,EAAM;;;UApXfH,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAuR,eAAA,KAAAC,GAAA,EAAkC;UAIlCxR,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAuR,eAAA,KAAAE,GAAA,EAAqC;UAMJzR,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAiC,iBAAA,CAAAiI,GAAA,CAAAxF,UAAA,yBAAsC;UAClE1E,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAAiC,iBAAA,CAAAiI,GAAA,CAAAxF,UAAA,kCAA+C;UAOrD1E,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAkB,kBAAA,MAAAgJ,GAAA,CAAAxF,UAAA,4BACF;UAQ2C1E,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,kBAAA4J,GAAA,CAAAwH,gBAAA,CAAkC;UAS1C1R,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,cAAA4J,GAAA,CAAA1E,gBAAA,CAA8B;UAgBrCxF,EAAA,CAAAK,SAAA,IAAiE;UAAjEL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA1E,gBAAA,CAAAmD,GAAA,aAAAgJ,QAAA,mBAAiE;UAUjE3R,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA1E,gBAAA,CAAAmD,GAAA,cAAAgJ,QAAA,oBAAmE;UAanE3R,EAAA,CAAAK,SAAA,GAA6D;UAA7DL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA1E,gBAAA,CAAAmD,GAAA,WAAAgJ,QAAA,iBAA6D;UAcvE3R,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAAzF,YAAA,uBAA2C;UAEjCzE,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAiC,iBAAA,CAAAiI,GAAA,CAAAzF,YAAA,mCAAkD;UA+C7BzE,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA1J,OAAA,CAAa;UAGTR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,UAAA4J,GAAA,CAAA1J,OAAA,CAAc;UAMhCR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,UAAA4J,GAAA,CAAA5F,kBAAA,CAAyB;UAC9BtE,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA5F,kBAAA,CAAwB;UAMpBtE,EAAA,CAAAK,SAAA,GAAkF;UAAlFL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAA1E,gBAAA,CAAAmD,GAAA,SAAAf,OAAA,IAAAsC,GAAA,CAAA1E,gBAAA,CAAAmD,GAAA,SAAAiJ,OAAA,CAAkF;UAcvG5R,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,UAAA,cAAA4J,GAAA,CAAA2H,aAAA,CAA2B;UA4BV7R,EAAA,CAAAK,SAAA,IAA4C;UAA5CL,EAAA,CAAAM,UAAA,aAAA4J,GAAA,CAAA1E,gBAAA,CAAAH,KAAA,CAAAK,QAAA,CAA4C,eAAAwE,GAAA,CAAA1E,gBAAA,CAAAH,KAAA,CAAAO,UAAA;UAevD5F,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAM,UAAA,cAAA4J,GAAA,CAAA4H,eAAA,CAA6B;UAQ5B9R,EAAA,CAAAK,SAAA,GAA4D;UAA5DL,EAAA,CAAAM,UAAA,UAAA4J,GAAA,CAAAtF,aAAA,KAAAsF,GAAA,CAAArF,gBAAA,KAAAqF,GAAA,CAAApF,cAAA,CAA4D;UAoB5D9E,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAAtF,aAAA,CAAmB;UA4DnB5E,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAArF,gBAAA,CAAsB;UA0DtB7E,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAM,UAAA,SAAA4J,GAAA,CAAApF,cAAA,CAAoB;;;qBDvU1CpG,YAAY,EAAAqT,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EAAAH,GAAA,CAAAI,WAAA,EACZpT,aAAa,EAAAqT,GAAA,CAAAC,OAAA,EACbrT,cAAc,EAAAsT,GAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EACd3T,gBAAgB,EAChBL,WAAW,EAAAiP,EAAA,CAAAgF,aAAA,EAAAhF,EAAA,CAAAiF,oBAAA,EAAAjF,EAAA,CAAAkF,eAAA,EAAAlF,EAAA,CAAAmF,oBAAA,EACXnU,mBAAmB,EAAAgP,EAAA,CAAAoF,kBAAA,EAAApF,EAAA,CAAAqF,eAAA,EACnB7T,cAAc,EAAA8T,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,cAAA,EACd/T,eAAe,EAAAgU,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,aAAA,EACfjU,aAAa,EAAAkU,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAAH,GAAA,CAAAI,cAAA,EAAAJ,GAAA,CAAAK,aAAA,EAAAL,GAAA,CAAAM,eAAA,EAAAN,GAAA,CAAAO,YAAA,EACbxU,eAAe,EACfL,oBAAoB,EAAA8U,GAAA,CAAAC,cAAA,EACpBzU,gBAAgB,EAChBL,YAAY,EAAAsO,EAAA,CAAAyG,UAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAIHhR,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}