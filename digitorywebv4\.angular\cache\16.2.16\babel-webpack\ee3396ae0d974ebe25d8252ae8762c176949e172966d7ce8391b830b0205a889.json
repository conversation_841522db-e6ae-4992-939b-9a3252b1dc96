{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nclass MarkdownPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.initializeRenderer();\n  }\n  initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n    // Paragraph rendering with proper spacing\n    this.markedRenderer.paragraph = text => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    // List rendering with proper spacing and styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = ordered && start !== 1 ? ` start=\"${start}\"` : '';\n      const listStyle = ordered ? 'decimal' : 'disc';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: ${listStyle};\">${body}</${type}>`;\n    };\n    // List item rendering with proper spacing\n    this.markedRenderer.listitem = text => {\n      return `<li style=\"margin-bottom: 0.5em; display: list-item;\">${text}</li>`;\n    };\n    // Code block rendering\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n    // Heading rendering with proper spacing and sizing\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - level * 0.1}em;\">${text}</h${level}>`;\n    };\n    // Table rendering with proper styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: 1em; border: 1px solid #ddd;\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n    // Table row rendering\n    this.markedRenderer.tablerow = content => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n    // Table cell rendering\n    this.markedRenderer.tablecell = (content, {\n      header,\n      align\n    }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n    // Set global options for marked\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true,\n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false // Don't add IDs to headers\n    });\n  }\n\n  transform(value) {\n    if (!value) {\n      return '';\n    }\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      // Preprocess the markdown to ensure consistent formatting\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n      // Convert markdown to HTML\n      const html = marked(preprocessedMarkdown);\n      // Post-process the HTML for better display\n      let processedHtml = html\n      // Fix spacing issues\n      .replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n      .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n      .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n      .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n      .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n      .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n      // Fix attribute formatting\n      .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (_match, tag, attrs, content) => {\n        if (tag === 'th' || tag === 'td') {\n          return _match;\n        }\n        const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n        return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n      });\n      // Special handling for lists to ensure proper rendering\n      processedHtml = processedHtml\n      // Make sure lists have proper spacing\n      .replace(/<ul style=\"[^\"]*\">/g, '<ul style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: disc;\">').replace(/<ol style=\"[^\"]*\">/g, '<ol style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: decimal;\">')\n      // Ensure list items have proper spacing\n      .replace(/<li style=\"[^\"]*\">/g, '<li style=\"margin-bottom: 0.5em; display: list-item;\">')\n      // Fix nested lists\n      .replace(/<\\/li><ul/g, '</li>\\n<ul').replace(/<\\/li><ol/g, '</li>\\n<ol').replace(/<\\/ul><\\/li>/g, '</ul>\\n</li>').replace(/<\\/ol><\\/li>/g, '</ol>\\n</li>');\n      // Add the cursor back if it was present\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      // Fallback to simple text rendering if markdown processing fails\n      const safeText = value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n  preprocessMarkdown(text) {\n    if (!text) return '';\n    // First, let's normalize line endings\n    let processed = text.replace(/\\r\\n/g, '\\n');\n    // Fix template variables like {{ variable_name }}\n    processed = this.fixTemplateVariables(processed);\n    // Fix ordered lists that might be broken\n    processed = this.fixOrderedLists(processed);\n    // Fix broken words across lines (like \"Kitche\\nn\")\n    processed = this.fixBrokenWords(processed);\n    // Fix list items that don't have proper spacing\n    processed = processed\n    // Ensure headings have a space after the hash\n    .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Ensure list items have a space after the bullet\n    .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n    // Fix code blocks\n    .replace(/```(\\w*)\\s*\\n/g, '```$1\\n').replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```')\n    // Add double line breaks after headings if not already present\n    .replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n')\n    // Add proper spacing for list items\n    .replace(/(\\n|^)(\\s*[-*+]\\s.+?)\\n(?!\\s*[-*+]\\s|\\n)/g, '$1$2\\n')\n    // Ensure paragraphs have proper spacing\n    .replace(/(\\n|^)([^\\n#>*+\\-|0-9].+?)\\n(?!\\n|#|>|\\*|\\+|\\-|\\||[0-9])/g, '$1$2\\n\\n')\n    // Handle triple asterisks for bold+italic\n    .replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>')\n    // Escape hash symbols in text that aren't headings\n    .replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n    // Special handling for lists - ensure each list item is properly formatted\n    processed = processed.replace(/^(\\s*[-*+]\\s.+)$/gm, match => {\n      return match + '\\n';\n    });\n    // Fix lists with multiple items\n    processed = processed.replace(/(^\\s*[-*+]\\s.+\\n)(\\s*[-*+]\\s.+\\n)+/gm, match => {\n      return match.replace(/\\n{2,}/g, '\\n');\n    });\n    // Split list items that are on the same line (separated by commas or hyphens)\n    processed = this.splitListItems(processed);\n    // Close unclosed code blocks\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n    // Remove excessive line breaks but preserve intentional ones\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n    return processed;\n  }\n  /**\n   * Fixes ordered lists that might be broken or incorrectly formatted\n   */\n  fixOrderedLists(text) {\n    let result = text;\n    // Fix numbered lists where numbers and text run together\n    // Example: \"1.Total number\" -> \"1. Total number\"\n    result = result.replace(/^(\\d+)\\.([^\\s])/gm, '$1. $2');\n    // Fix numbered lists where items are on the same line\n    // Example: \"1. Item1 2. Item2\" -> \"1. Item1\\n2. Item2\"\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s+)(\\d+\\.\\s+)/g, '$1\\n$3');\n    // Fix numbered lists where numbers are followed by closing parenthesis\n    // Example: \"1) Item\" -> \"1. Item\"\n    result = result.replace(/^(\\d+)\\)(\\s+)/gm, '$1.$2');\n    // Fix numbered lists where items are separated by periods or other punctuation\n    result = result.replace(/^(\\d+)\\.(\\s+)([^\\n]+?)\\.(\\s*)(\\d+)\\./gm, '$1.$2$3\\n$5.');\n    // Fix numbered lists where items have incorrect spacing\n    result = result.replace(/^(\\d+)\\.(?!\\s)/gm, '$1. ');\n    // Fix specific case with \"First Question:\" appearing after a list\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s*)(First Question:)/g, '$1\\n\\n$3');\n    // Fix specific case with \"Let's get started\" appearing after a list\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s*)(Let's get started)/g, '$1\\n\\n$3');\n    // Fix specific case with \"###\" appearing in a list (markdown formatting)\n    result = result.replace(/(###)(\\s*)(First Question:)/g, '\\n\\n$3');\n    return result;\n  }\n  /**\n   * Fixes template variables like {{ variable_name }} that might be broken\n   */\n  fixTemplateVariables(text) {\n    let result = text;\n    // Fix broken template variables like {{ variable_name }}\n    // First, look for patterns like {{ and }} that might indicate template variables\n    if (result.includes('{{') || result.includes('}}')) {\n      // Fix variables that might be broken across lines\n      result = result.replace(/({{\\s*[\\w_]+)\\s*}}\\s*-/g, '$1}}');\n      // Fix variables that might have extra spaces or underscores\n      result = result.replace(/{{\\s*([\\w_]+)\\s*}}/g, '{{$1}}');\n      // Fix specific case with total_questions_estimate\n      result = result.replace(/{{\\s*total_questions_estimate\\s*}}/, '12');\n      // Fix the \"12-12 short questions\" redundancy\n      result = result.replace(/(\\d+)-(\\1)\\s+short\\s+questions/, '$1 short questions');\n    }\n    return result;\n  }\n  /**\n   * Fixes words that are broken across lines, like \"Kitche\\nn\" or \"Bar\\n1\"\n   */\n  fixBrokenWords(text) {\n    // Fix broken words after list items\n    let result = text;\n    // Pattern to match a list item followed by a partial word at the end of a line\n    // and the continuation of that word at the beginning of the next line\n    const brokenWordPattern = /([-*+]\\s+\\w+)([a-zA-Z0-9]*)\\n([a-zA-Z0-9]+)/g;\n    // Replace with the complete word on the first line\n    result = result.replace(brokenWordPattern, (_, listStart, wordEnd, nextLine) => {\n      return `${listStart}${wordEnd}${nextLine}`;\n    });\n    // Handle the case where a list item is followed by a single character on the next line\n    result = result.replace(/([-*+]\\s+\\w+)\\n([a-zA-Z0-9])/g, '$1$2');\n    // Handle the case where a list item is split across lines\n    result = result.replace(/^(\\s*[-*+]\\s+\\w+)\\n([a-zA-Z0-9]+)/gm, '$1$2');\n    // Handle the case where a number appears on a line by itself after a list item\n    result = result.replace(/([-*+]\\s+\\w+)\\n(\\d+)/g, '$1$2');\n    // Handle the case where \"Is that correct?\" appears after a list item\n    result = result.replace(/([-*+]\\s+\\w+\\d*)\\n(Is that correct\\?)/g, '$1 $2');\n    // Handle numbered lists with broken words\n    result = result.replace(/^(\\d+\\.\\s+\\w+)([a-zA-Z0-9]*)\\n([a-zA-Z0-9]+)/gm, '$1$2$3');\n    return result;\n  }\n  /**\n   * Splits list items that are on the same line into separate list items\n   * This handles cases like \"- Item1, Item2, Item3\" or \"- Item1- Item2- Item3\"\n   */\n  splitListItems(text) {\n    let result = text;\n    // Find list items with commas and split them\n    result = result.replace(/^(\\s*[-*+]\\s+)([^\\n,]+)(,\\s+)([^\\n,]+)/gm, (_, bullet, item1, _separator, item2) => {\n      return `${bullet}${item1}\\n${bullet}${item2}`;\n    });\n    // Handle multiple commas in a list item\n    while (result.match(/^(\\s*[-*+]\\s+[^\\n,]+),\\s+([^\\n,]+)/gm)) {\n      result = result.replace(/^(\\s*[-*+]\\s+[^\\n,]+),\\s+([^\\n,]+)/gm, (_, firstPart, secondPart) => {\n        const bullet = firstPart.match(/^(\\s*[-*+]\\s+)/)[1];\n        const item = firstPart.replace(/^\\s*[-*+]\\s+/, '');\n        return `${bullet}${item}\\n${bullet}${secondPart}`;\n      });\n    }\n    // Find list items with hyphens between them and split them\n    result = result.replace(/([-*+]\\s+[^\\n-]+)-\\s+/g, '$1\\n- ');\n    // Handle specific case like \"Bar- Kitchen- Chinese Kitchen- Staff Kitchen\"\n    result = result.replace(/([-*+]\\s+[^\\n-]+)-(\\s*[A-Za-z])/g, '$1\\n-$2');\n    // Find list items that are directly adjacent and split them\n    result = result.replace(/([-*+]\\s+[^\\n]+)([-*+]\\s+)/g, '$1\\n$2');\n    // Final pass to ensure all list items are properly formatted\n    result = result.replace(/^(\\s*[-*+]\\s+)([^\\n]+)$/gm, (_, bullet, content) => {\n      // Split content by hyphens that are followed by a space and a word\n      if (content.match(/\\s+-\\s+[A-Za-z]/)) {\n        const parts = content.split(/\\s+-\\s+/);\n        return parts.map(part => `${bullet}${part.trim()}`).join('\\n');\n      }\n      return `${bullet}${content}`;\n    });\n    return result;\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport { MarkdownPipe };", "map": {"version": 3, "names": ["marked", "MarkdownPipe", "constructor", "sanitizer", "initialize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "paragraph", "text", "list", "body", "ordered", "start", "type", "startAttr", "listStyle", "listitem", "code", "language", "heading", "level", "table", "header", "tablerow", "content", "tablecell", "align", "tag", "style", "setOptions", "renderer", "gfm", "breaks", "pedantic", "smartLists", "smartypants", "xhtml", "headerIds", "transform", "value", "hasCursor", "includes", "textWithoutCursor", "replace", "preprocessedMarkdown", "preprocessMarkdown", "html", "processedHtml", "_match", "attrs", "cleanAttrs", "finalHtml", "bypassSecurityTrustHtml", "error", "console", "safeText", "processed", "fixTemplateVariables", "fixOrderedLists", "fixBrokenWords", "match", "splitListItems", "codeBlockMatches", "length", "result", "brokenWordPattern", "_", "listStart", "wordEnd", "nextLine", "bullet", "item1", "_separator", "item2", "firstPart", "second<PERSON><PERSON>", "item", "parts", "split", "map", "part", "trim", "join", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["/home/<USER>/other/digi/digitorywebv4/src/app/pipes/markdown.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { marked } from 'marked';\n\n@Pipe({\n  name: 'markdown',\n  standalone: true\n})\nexport class MarkdownPipe implements PipeTransform {\n  private markedRenderer: marked.Renderer;\n\n  constructor(private sanitizer: DomSanitizer) {\n    this.initializeRenderer();\n  }\n\n  private initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n\n    // Paragraph rendering with proper spacing\n    this.markedRenderer.paragraph = (text) => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    \n    // List rendering with proper spacing and styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = (ordered && start !== 1) ? ` start=\"${start}\"` : '';\n      const listStyle = ordered ? 'decimal' : 'disc';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: ${listStyle};\">${body}</${type}>`;\n    };\n\n    // List item rendering with proper spacing\n    this.markedRenderer.listitem = (text) => {\n      return `<li style=\"margin-bottom: 0.5em; display: list-item;\">${text}</li>`;\n    };\n\n    // Code block rendering\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n\n    // Heading rendering with proper spacing and sizing\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - (level * 0.1)}em;\">${text}</h${level}>`;\n    };\n\n    // Table rendering with proper styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: 1em; border: 1px solid #ddd;\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n\n    // Table row rendering\n    this.markedRenderer.tablerow = (content) => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n\n    // Table cell rendering\n    this.markedRenderer.tablecell = (content, { header, align }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n    \n    // Set global options for marked\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,           // GitHub Flavored Markdown\n      breaks: true,        // Convert \\n to <br>\n      pedantic: false,     // Don't be too strict with original markdown spec\n      smartLists: true,    // Use smarter list behavior\n      smartypants: true,   // Use smart punctuation\n      xhtml: true,         // Use self-closing tags\n      headerIds: false     // Don't add IDs to headers\n    });\n  }\n\n  transform(value: string): SafeHtml {\n    if (!value) {\n      return '';\n    }\n\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n\n      // Preprocess the markdown to ensure consistent formatting\n      const preprocessedMarkdown = this.preprocessMarkdown(textWithoutCursor);\n\n      // Convert markdown to HTML\n      const html = marked(preprocessedMarkdown);\n\n      // Post-process the HTML for better display\n      let processedHtml = html\n        // Fix spacing issues\n        .replace(/\\n\\s*\\n/g, '\\n') // Remove double line breaks\n        .replace(/<\\/p>\\s*<p>/g, '</p><p>') // Remove space between paragraphs\n        .replace(/<\\/li>\\s*<li>/g, '</li><li>') // Remove space between list items\n        .replace(/<p><\\/p>/g, '') // Remove empty paragraphs\n        .replace(/<p>\\s+/g, '<p>') // Remove leading whitespace in paragraphs\n        .replace(/\\s+<\\/p>/g, '</p>') // Remove trailing whitespace in paragraphs\n\n        // Fix attribute formatting\n        .replace(/<([a-z]+)([^>]*)>([^<]*)<\\/\\1>/g, (_match, tag, attrs, content) => {\n          if (tag === 'th' || tag === 'td') {\n            return _match;\n          }\n          const cleanAttrs = attrs.replace(/[\\s\\\"]+=/g, '=\"').replace(/=([^\\\"\\s>]+)(\\s|>)/g, '=\"$1\"$2');\n          return `<${tag}${cleanAttrs}>${content}</${tag}>`;\n        });\n\n      // Special handling for lists to ensure proper rendering\n      processedHtml = processedHtml\n        // Make sure lists have proper spacing\n        .replace(/<ul style=\"[^\"]*\">/g, '<ul style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: disc;\">')\n        .replace(/<ol style=\"[^\"]*\">/g, '<ol style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: decimal;\">')\n        // Ensure list items have proper spacing\n        .replace(/<li style=\"[^\"]*\">/g, '<li style=\"margin-bottom: 0.5em; display: list-item;\">')\n        // Fix nested lists\n        .replace(/<\\/li><ul/g, '</li>\\n<ul')\n        .replace(/<\\/li><ol/g, '</li>\\n<ol')\n        .replace(/<\\/ul><\\/li>/g, '</ul>\\n</li>')\n        .replace(/<\\/ol><\\/li>/g, '</ol>\\n</li>');\n\n      // Add the cursor back if it was present\n      const finalHtml = hasCursor ? processedHtml + '<span class=\"blinking-cursor\">|</span>' : processedHtml;\n\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      // Fallback to simple text rendering if markdown processing fails\n      const safeText = value\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n\n  private preprocessMarkdown(text: string): string {\n    if (!text) return '';\n\n    // First, let's normalize line endings\n    let processed = text.replace(/\\r\\n/g, '\\n');\n    \n    // Fix template variables like {{ variable_name }}\n    processed = this.fixTemplateVariables(processed);\n    \n    // Fix ordered lists that might be broken\n    processed = this.fixOrderedLists(processed);\n    \n    // Fix broken words across lines (like \"Kitche\\nn\")\n    processed = this.fixBrokenWords(processed);\n    \n    // Fix list items that don't have proper spacing\n    processed = processed\n      // Ensure headings have a space after the hash\n      .replace(/^(#{1,6})\\s*(.+?)\\s*$/gm, '$1 $2')\n      \n      // Ensure list items have a space after the bullet\n      .replace(/^(\\s*[-*+])\\s*(.+?)\\s*$/gm, '$1 $2')\n      \n      // Fix code blocks\n      .replace(/```(\\w*)\\s*\\n/g, '```$1\\n')\n      .replace(/```(\\w*)\\s*([^`]*)$/g, '```$1\\n$2\\n```')\n      \n      // Add double line breaks after headings if not already present\n      .replace(/(\\n|^)(#{1,6}\\s.+?)\\n(?!\\n)/g, '$1$2\\n\\n')\n      \n      // Add proper spacing for list items\n      .replace(/(\\n|^)(\\s*[-*+]\\s.+?)\\n(?!\\s*[-*+]\\s|\\n)/g, '$1$2\\n')\n      \n      // Ensure paragraphs have proper spacing\n      .replace(/(\\n|^)([^\\n#>*+\\-|0-9].+?)\\n(?!\\n|#|>|\\*|\\+|\\-|\\||[0-9])/g, '$1$2\\n\\n')\n      \n      // Handle triple asterisks for bold+italic\n      .replace(/\\*\\*\\*([^*]+)\\*\\*\\*/g, '<strong><em>$1</em></strong>')\n      \n      // Escape hash symbols in text that aren't headings\n      .replace(/(^|\\n)([^#\\n].*?)(#)([^#\\s])/g, '$1$2\\\\$3$4');\n\n    // Special handling for lists - ensure each list item is properly formatted\n    processed = processed.replace(/^(\\s*[-*+]\\s.+)$/gm, (match) => {\n      return match + '\\n';\n    });\n    \n    // Fix lists with multiple items\n    processed = processed.replace(/(^\\s*[-*+]\\s.+\\n)(\\s*[-*+]\\s.+\\n)+/gm, (match) => {\n      return match.replace(/\\n{2,}/g, '\\n');\n    });\n    \n    // Split list items that are on the same line (separated by commas or hyphens)\n    processed = this.splitListItems(processed);\n\n    // Close unclosed code blocks\n    const codeBlockMatches = processed.match(/```[^`]*$/g);\n    if (codeBlockMatches && codeBlockMatches.length > 0) {\n      processed += '\\n```';\n    }\n\n    // Remove excessive line breaks but preserve intentional ones\n    processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n\n    return processed;\n  }\n\n  /**\n   * Fixes ordered lists that might be broken or incorrectly formatted\n   */\n  private fixOrderedLists(text: string): string {\n    let result = text;\n    \n    // Fix numbered lists where numbers and text run together\n    // Example: \"1.Total number\" -> \"1. Total number\"\n    result = result.replace(/^(\\d+)\\.([^\\s])/gm, '$1. $2');\n    \n    // Fix numbered lists where items are on the same line\n    // Example: \"1. Item1 2. Item2\" -> \"1. Item1\\n2. Item2\"\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s+)(\\d+\\.\\s+)/g, '$1\\n$3');\n    \n    // Fix numbered lists where numbers are followed by closing parenthesis\n    // Example: \"1) Item\" -> \"1. Item\"\n    result = result.replace(/^(\\d+)\\)(\\s+)/gm, '$1.$2');\n    \n    // Fix numbered lists where items are separated by periods or other punctuation\n    result = result.replace(/^(\\d+)\\.(\\s+)([^\\n]+?)\\.(\\s*)(\\d+)\\./gm, '$1.$2$3\\n$5.');\n    \n    // Fix numbered lists where items have incorrect spacing\n    result = result.replace(/^(\\d+)\\.(?!\\s)/gm, '$1. ');\n    \n    // Fix specific case with \"First Question:\" appearing after a list\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s*)(First Question:)/g, '$1\\n\\n$3');\n    \n    // Fix specific case with \"Let's get started\" appearing after a list\n    result = result.replace(/(\\d+\\.\\s+[^\\n]+)(\\s*)(Let's get started)/g, '$1\\n\\n$3');\n    \n    // Fix specific case with \"###\" appearing in a list (markdown formatting)\n    result = result.replace(/(###)(\\s*)(First Question:)/g, '\\n\\n$3');\n    \n    return result;\n  }\n\n  /**\n   * Fixes template variables like {{ variable_name }} that might be broken\n   */\n  private fixTemplateVariables(text: string): string {\n    let result = text;\n    \n    // Fix broken template variables like {{ variable_name }}\n    // First, look for patterns like {{ and }} that might indicate template variables\n    if (result.includes('{{') || result.includes('}}')) {\n      // Fix variables that might be broken across lines\n      result = result.replace(/({{\\s*[\\w_]+)\\s*}}\\s*-/g, '$1}}');\n      \n      // Fix variables that might have extra spaces or underscores\n      result = result.replace(/{{\\s*([\\w_]+)\\s*}}/g, '{{$1}}');\n      \n      // Fix specific case with total_questions_estimate\n      result = result.replace(/{{\\s*total_questions_estimate\\s*}}/, '12');\n      \n      // Fix the \"12-12 short questions\" redundancy\n      result = result.replace(/(\\d+)-(\\1)\\s+short\\s+questions/, '$1 short questions');\n    }\n    \n    return result;\n  }\n  \n  /**\n   * Fixes words that are broken across lines, like \"Kitche\\nn\" or \"Bar\\n1\"\n   */\n  private fixBrokenWords(text: string): string {\n    // Fix broken words after list items\n    let result = text;\n    \n    // Pattern to match a list item followed by a partial word at the end of a line\n    // and the continuation of that word at the beginning of the next line\n    const brokenWordPattern = /([-*+]\\s+\\w+)([a-zA-Z0-9]*)\\n([a-zA-Z0-9]+)/g;\n    \n    // Replace with the complete word on the first line\n    result = result.replace(brokenWordPattern, (_, listStart, wordEnd, nextLine) => {\n      return `${listStart}${wordEnd}${nextLine}`;\n    });\n    \n    // Handle the case where a list item is followed by a single character on the next line\n    result = result.replace(/([-*+]\\s+\\w+)\\n([a-zA-Z0-9])/g, '$1$2');\n    \n    // Handle the case where a list item is split across lines\n    result = result.replace(/^(\\s*[-*+]\\s+\\w+)\\n([a-zA-Z0-9]+)/gm, '$1$2');\n    \n    // Handle the case where a number appears on a line by itself after a list item\n    result = result.replace(/([-*+]\\s+\\w+)\\n(\\d+)/g, '$1$2');\n    \n    // Handle the case where \"Is that correct?\" appears after a list item\n    result = result.replace(/([-*+]\\s+\\w+\\d*)\\n(Is that correct\\?)/g, '$1 $2');\n    \n    // Handle numbered lists with broken words\n    result = result.replace(/^(\\d+\\.\\s+\\w+)([a-zA-Z0-9]*)\\n([a-zA-Z0-9]+)/gm, '$1$2$3');\n    \n    return result;\n  }\n  \n  /**\n   * Splits list items that are on the same line into separate list items\n   * This handles cases like \"- Item1, Item2, Item3\" or \"- Item1- Item2- Item3\"\n   */\n  private splitListItems(text: string): string {\n    let result = text;\n    \n    // Find list items with commas and split them\n    result = result.replace(/^(\\s*[-*+]\\s+)([^\\n,]+)(,\\s+)([^\\n,]+)/gm, (_, bullet, item1, _separator, item2) => {\n      return `${bullet}${item1}\\n${bullet}${item2}`;\n    });\n    \n    // Handle multiple commas in a list item\n    while (result.match(/^(\\s*[-*+]\\s+[^\\n,]+),\\s+([^\\n,]+)/gm)) {\n      result = result.replace(/^(\\s*[-*+]\\s+[^\\n,]+),\\s+([^\\n,]+)/gm, (_, firstPart, secondPart) => {\n        const bullet = firstPart.match(/^(\\s*[-*+]\\s+)/)[1];\n        const item = firstPart.replace(/^\\s*[-*+]\\s+/, '');\n        return `${bullet}${item}\\n${bullet}${secondPart}`;\n      });\n    }\n    \n    // Find list items with hyphens between them and split them\n    result = result.replace(/([-*+]\\s+[^\\n-]+)-\\s+/g, '$1\\n- ');\n    \n    // Handle specific case like \"Bar- Kitchen- Chinese Kitchen- Staff Kitchen\"\n    result = result.replace(/([-*+]\\s+[^\\n-]+)-(\\s*[A-Za-z])/g, '$1\\n-$2');\n    \n    // Find list items that are directly adjacent and split them\n    result = result.replace(/([-*+]\\s+[^\\n]+)([-*+]\\s+)/g, '$1\\n$2');\n    \n    // Final pass to ensure all list items are properly formatted\n    result = result.replace(/^(\\s*[-*+]\\s+)([^\\n]+)$/gm, (_, bullet, content) => {\n      // Split content by hyphens that are followed by a space and a word\n      if (content.match(/\\s+-\\s+[A-Za-z]/)) {\n        const parts = content.split(/\\s+-\\s+/);\n        return parts.map((part: string) => `${bullet}${part.trim()}`).join('\\n');\n      }\n      return `${bullet}${content}`;\n    });\n    \n    return result;\n  }\n}\n"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;AAE/B,MAIaC,YAAY;EAGvBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIL,MAAM,CAACM,QAAQ,EAAE;IAE3C;IACA,IAAI,CAACD,cAAc,CAACE,SAAS,GAAIC,IAAI,IAAK,kCAAkCA,IAAI,MAAM;IAEtF;IACA,IAAI,CAACH,cAAc,CAACI,IAAI,GAAG,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,KAAI;MAClD,MAAMC,IAAI,GAAGF,OAAO,GAAG,IAAI,GAAG,IAAI;MAClC,MAAMG,SAAS,GAAIH,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAI,WAAWA,KAAK,GAAG,GAAG,EAAE;MACrE,MAAMG,SAAS,GAAGJ,OAAO,GAAG,SAAS,GAAG,MAAM;MAC9C,OAAO,IAAIE,IAAI,GAAGC,SAAS,mEAAmEC,SAAS,MAAML,IAAI,KAAKG,IAAI,GAAG;IAC/H,CAAC;IAED;IACA,IAAI,CAACR,cAAc,CAACW,QAAQ,GAAIR,IAAI,IAAI;MACtC,OAAO,yDAAyDA,IAAI,OAAO;IAC7E,CAAC;IAED;IACA,IAAI,CAACH,cAAc,CAACY,IAAI,GAAG,CAACA,IAAI,EAAEC,QAAQ,KAAI;MAC5C,OAAO,+NAA+NA,QAAQ,IAAI,MAAM,6BAA6BD,IAAI,eAAe;IAC1S,CAAC;IAED;IACA,IAAI,CAACZ,cAAc,CAACc,OAAO,GAAG,CAACX,IAAI,EAAEY,KAAK,KAAI;MAC5C,OAAO,KAAKA,KAAK,+EAA+E,GAAG,GAAIA,KAAK,GAAG,GAAI,QAAQZ,IAAI,MAAMY,KAAK,GAAG;IAC/I,CAAC;IAED;IACA,IAAI,CAACf,cAAc,CAACgB,KAAK,GAAG,CAACC,MAAM,EAAEZ,IAAI,KAAI;MAC3C,OAAO;oDACuCY,MAAM;iBACzCZ,IAAI;eACN;IACX,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAACkB,QAAQ,GAAIC,OAAO,IAAI;MACzC,OAAO,8CAA8CA,OAAO,OAAO;IACrE,CAAC;IAED;IACA,IAAI,CAACnB,cAAc,CAACoB,SAAS,GAAG,CAACD,OAAO,EAAE;MAAEF,MAAM;MAAEI;IAAK,CAAE,KAAI;MAC7D,MAAMC,GAAG,GAAGL,MAAM,GAAG,IAAI,GAAG,IAAI;MAChC,MAAMM,KAAK,GAAG,6BAA6BF,KAAK,IAAI,MAAM,iCAAiC;MAC3F,OAAO,IAAIC,GAAG,WAAWC,KAAK,KAAKJ,OAAO,KAAKG,GAAG,GAAG;IACvD,CAAC;IAED;IACA3B,MAAM,CAAC6B,UAAU,CAAC;MAChBC,QAAQ,EAAE,IAAI,CAACzB,cAAc;MAC7B0B,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,KAAK,CAAK;KACtB,CAAC;EACJ;;EAEAC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX,IAAI;MACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,QAAQ,CAAC,wCAAwC,CAAC;MAC1E,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,OAAO,CAAC,2CAA2C,EAAE,EAAE,CAAC;MAExF;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAACC,kBAAkB,CAACH,iBAAiB,CAAC;MAEvE;MACA,MAAMI,IAAI,GAAG9C,MAAM,CAAC4C,oBAAoB,CAAC;MAEzC;MACA,IAAIG,aAAa,GAAGD;MAClB;MAAA,CACCH,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;MAAA,CACnCA,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;MAAA,CACvCA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;MAAA,CACzBA,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;MAAA,CAC1BA,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;MAE9B;MAAA,CACCA,OAAO,CAAC,iCAAiC,EAAE,CAACK,MAAM,EAAErB,GAAG,EAAEsB,KAAK,EAAEzB,OAAO,KAAI;QAC1E,IAAIG,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,IAAI,EAAE;UAChC,OAAOqB,MAAM;;QAEf,MAAME,UAAU,GAAGD,KAAK,CAACN,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC;QAC7F,OAAO,IAAIhB,GAAG,GAAGuB,UAAU,IAAI1B,OAAO,KAAKG,GAAG,GAAG;MACnD,CAAC,CAAC;MAEJ;MACAoB,aAAa,GAAGA;MACd;MAAA,CACCJ,OAAO,CAAC,qBAAqB,EAAE,4EAA4E,CAAC,CAC5GA,OAAO,CAAC,qBAAqB,EAAE,+EAA+E;MAC/G;MAAA,CACCA,OAAO,CAAC,qBAAqB,EAAE,wDAAwD;MACxF;MAAA,CACCA,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,CACnCA,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,CACnCA,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC,CACxCA,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC;MAE3C;MACA,MAAMQ,SAAS,GAAGX,SAAS,GAAGO,aAAa,GAAG,wCAAwC,GAAGA,aAAa;MAEtG,OAAO,IAAI,CAAC5C,SAAS,CAACiD,uBAAuB,CAACD,SAAS,CAAC;KACzD,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA,MAAME,QAAQ,GAAGhB,KAAK,CACnBI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACzB,OAAO,IAAI,CAACxC,SAAS,CAACiD,uBAAuB,CAACG,QAAQ,CAAC;;EAE3D;EAEQV,kBAAkBA,CAACrC,IAAY;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB;IACA,IAAIgD,SAAS,GAAGhD,IAAI,CAACmC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;IAE3C;IACAa,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACD,SAAS,CAAC;IAEhD;IACAA,SAAS,GAAG,IAAI,CAACE,eAAe,CAACF,SAAS,CAAC;IAE3C;IACAA,SAAS,GAAG,IAAI,CAACG,cAAc,CAACH,SAAS,CAAC;IAE1C;IACAA,SAAS,GAAGA;IACV;IAAA,CACCb,OAAO,CAAC,yBAAyB,EAAE,OAAO;IAE3C;IAAA,CACCA,OAAO,CAAC,2BAA2B,EAAE,OAAO;IAE7C;IAAA,CACCA,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CACpCA,OAAO,CAAC,sBAAsB,EAAE,gBAAgB;IAEjD;IAAA,CACCA,OAAO,CAAC,8BAA8B,EAAE,UAAU;IAEnD;IAAA,CACCA,OAAO,CAAC,2CAA2C,EAAE,QAAQ;IAE9D;IAAA,CACCA,OAAO,CAAC,2DAA2D,EAAE,UAAU;IAEhF;IAAA,CACCA,OAAO,CAAC,sBAAsB,EAAE,8BAA8B;IAE/D;IAAA,CACCA,OAAO,CAAC,+BAA+B,EAAE,YAAY,CAAC;IAEzD;IACAa,SAAS,GAAGA,SAAS,CAACb,OAAO,CAAC,oBAAoB,EAAGiB,KAAK,IAAI;MAC5D,OAAOA,KAAK,GAAG,IAAI;IACrB,CAAC,CAAC;IAEF;IACAJ,SAAS,GAAGA,SAAS,CAACb,OAAO,CAAC,sCAAsC,EAAGiB,KAAK,IAAI;MAC9E,OAAOA,KAAK,CAACjB,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC;IAEF;IACAa,SAAS,GAAG,IAAI,CAACK,cAAc,CAACL,SAAS,CAAC;IAE1C;IACA,MAAMM,gBAAgB,GAAGN,SAAS,CAACI,KAAK,CAAC,YAAY,CAAC;IACtD,IAAIE,gBAAgB,IAAIA,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACnDP,SAAS,IAAI,OAAO;;IAGtB;IACAA,SAAS,GAAGA,SAAS,CAACb,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IAEhD,OAAOa,SAAS;EAClB;EAEA;;;EAGQE,eAAeA,CAAClD,IAAY;IAClC,IAAIwD,MAAM,GAAGxD,IAAI;IAEjB;IACA;IACAwD,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,mBAAmB,EAAE,QAAQ,CAAC;IAEtD;IACA;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,kCAAkC,EAAE,QAAQ,CAAC;IAErE;IACA;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC;IAEnD;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,wCAAwC,EAAE,cAAc,CAAC;IAEjF;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;IAEnD;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,yCAAyC,EAAE,UAAU,CAAC;IAE9E;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,2CAA2C,EAAE,UAAU,CAAC;IAEhF;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,8BAA8B,EAAE,QAAQ,CAAC;IAEjE,OAAOqB,MAAM;EACf;EAEA;;;EAGQP,oBAAoBA,CAACjD,IAAY;IACvC,IAAIwD,MAAM,GAAGxD,IAAI;IAEjB;IACA;IACA,IAAIwD,MAAM,CAACvB,QAAQ,CAAC,IAAI,CAAC,IAAIuB,MAAM,CAACvB,QAAQ,CAAC,IAAI,CAAC,EAAE;MAClD;MACAuB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC;MAE1D;MACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,qBAAqB,EAAE,QAAQ,CAAC;MAExD;MACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,oCAAoC,EAAE,IAAI,CAAC;MAEnE;MACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,gCAAgC,EAAE,oBAAoB,CAAC;;IAGjF,OAAOqB,MAAM;EACf;EAEA;;;EAGQL,cAAcA,CAACnD,IAAY;IACjC;IACA,IAAIwD,MAAM,GAAGxD,IAAI;IAEjB;IACA;IACA,MAAMyD,iBAAiB,GAAG,8CAA8C;IAExE;IACAD,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAACsB,iBAAiB,EAAE,CAACC,CAAC,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,KAAI;MAC7E,OAAO,GAAGF,SAAS,GAAGC,OAAO,GAAGC,QAAQ,EAAE;IAC5C,CAAC,CAAC;IAEF;IACAL,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,+BAA+B,EAAE,MAAM,CAAC;IAEhE;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC;IAEtE;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;IAExD;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,wCAAwC,EAAE,OAAO,CAAC;IAE1E;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,gDAAgD,EAAE,QAAQ,CAAC;IAEnF,OAAOqB,MAAM;EACf;EAEA;;;;EAIQH,cAAcA,CAACrD,IAAY;IACjC,IAAIwD,MAAM,GAAGxD,IAAI;IAEjB;IACAwD,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,0CAA0C,EAAE,CAACuB,CAAC,EAAEI,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,KAAI;MAC1G,OAAO,GAAGH,MAAM,GAAGC,KAAK,KAAKD,MAAM,GAAGG,KAAK,EAAE;IAC/C,CAAC,CAAC;IAEF;IACA,OAAOT,MAAM,CAACJ,KAAK,CAAC,sCAAsC,CAAC,EAAE;MAC3DI,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,sCAAsC,EAAE,CAACuB,CAAC,EAAEQ,SAAS,EAAEC,UAAU,KAAI;QAC3F,MAAML,MAAM,GAAGI,SAAS,CAACd,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACnD,MAAMgB,IAAI,GAAGF,SAAS,CAAC/B,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QAClD,OAAO,GAAG2B,MAAM,GAAGM,IAAI,KAAKN,MAAM,GAAGK,UAAU,EAAE;MACnD,CAAC,CAAC;;IAGJ;IACAX,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,wBAAwB,EAAE,QAAQ,CAAC;IAE3D;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,kCAAkC,EAAE,SAAS,CAAC;IAEtE;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,6BAA6B,EAAE,QAAQ,CAAC;IAEhE;IACAqB,MAAM,GAAGA,MAAM,CAACrB,OAAO,CAAC,2BAA2B,EAAE,CAACuB,CAAC,EAAEI,MAAM,EAAE9C,OAAO,KAAI;MAC1E;MACA,IAAIA,OAAO,CAACoC,KAAK,CAAC,iBAAiB,CAAC,EAAE;QACpC,MAAMiB,KAAK,GAAGrD,OAAO,CAACsD,KAAK,CAAC,SAAS,CAAC;QACtC,OAAOD,KAAK,CAACE,GAAG,CAAEC,IAAY,IAAK,GAAGV,MAAM,GAAGU,IAAI,CAACC,IAAI,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;;MAE1E,OAAO,GAAGZ,MAAM,GAAG9C,OAAO,EAAE;IAC9B,CAAC,CAAC;IAEF,OAAOwC,MAAM;EACf;;;uBA9UW/D,YAAY,EAAAkF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAAZrF,YAAY;MAAAsF,IAAA;MAAAC,UAAA;IAAA;EAAA;;SAAZvF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}